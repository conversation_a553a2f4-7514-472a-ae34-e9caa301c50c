; ModuleID = 'test_emoji.9fb72e329dcd38ec-cgu.0'
source_filename = "test_emoji.9fb72e329dcd38ec-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h6d287ee106e0cdddE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf89f17d342b2e472E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf89f17d342b2e472E" }>, align 8
@anon.ceef70cbd4be60d1da0c272b343491b3.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_1e40ffcc23a23d10d73ae0a517360727 = private unnamed_addr constant [23 x i8] c"\F0\9F\8E\89 Hello World! \F0\9F\9A\80\0A", align 1
@alloc_51e73e1dcae28e202f0ed6524f206d14 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1e40ffcc23a23d10d73ae0a517360727, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_959348f436f968ac83f76207d14dc23d = private unnamed_addr constant [48 x i8] c"\E2\9C\85 This has emojis \E2\9D\8C but will work fine \F0\9F\92\AF\0A", align 1
@alloc_cb62211d60c4c34e32cb29c417a2edb2 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_959348f436f968ac83f76207d14dc23d, [8 x i8] c"0\00\00\00\00\00\00\00" }>, align 8
@alloc_3a33aa60fc55fd725b13a1f0fb329cf2 = private unnamed_addr constant [34 x i8] c"Arabic: \D9\85\D8\B1\D8\AD\D8\A8\D8\A7 \D8\A8\D8\A7\D9\84\D8\B9\D8\A7\D9\84\D9\85\0A", align 1
@alloc_4f3f64854e5386b61f09cfaf87383dff = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3a33aa60fc55fd725b13a1f0fb329cf2, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_29ac7c9321059e2e9128f18abd10b3f3 = private unnamed_addr constant [22 x i8] c"Chinese: \E4\BD\A0\E5\A5\BD\E4\B8\96\E7\95\8C\0A", align 1
@alloc_fda168bf352b75c306f6553194b204d0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_29ac7c9321059e2e9128f18abd10b3f3, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_0a8a8152fe34c06e392d2ffcd375c31a = private unnamed_addr constant [24 x i8] c"Regular text works fine\0A", align 1
@alloc_81e6abcc6128d7d94e5f452b6246b979 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0a8a8152fe34c06e392d2ffcd375c31a, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_64ee829c111eb3a6100edbe1756a88f7 = private unnamed_addr constant [11 x i8] c"\F0\9F\94\A2 Math: ", align 1
@alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf = private unnamed_addr constant [3 x i8] c" + ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_b98279e3bc28e3208c221280ebb2a3c8 = private unnamed_addr constant [6 x i8] c" \F0\9F\8E\AF\0A", align 1
@alloc_41a2d4eab79e9d26efd78e26447ff5a7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_64ee829c111eb3a6100edbe1756a88f7, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_b98279e3bc28e3208c221280ebb2a3c8, [8 x i8] c"\06\00\00\00\00\00\00\00" }>, align 8
@alloc_f4ac366901a2548f3724312f73dda6d0 = private unnamed_addr constant [13 x i8] c"test_emoji.rs", align 1
@alloc_03b92670611f333628124c6ac0923042 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f4ac366901a2548f3724312f73dda6d0, [16 x i8] c"\0D\00\00\00\00\00\00\00\0A\00\00\000\00\00\00" }>, align 8
@alloc_7ef5cc56da40dd40641f072707117518 = private unnamed_addr constant [43 x i8] c"\F0\9F\8C\9F Unicode works perfectly in Rust! \F0\9F\8C\9F\0A", align 1
@alloc_715a1da5ee2fbf95d7225f4d9c30da25 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7ef5cc56da40dd40641f072707117518, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h63c33b6af80227abE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf89f17d342b2e472E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17haaf0ee4e604d506fE(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h50a577384c11e8adE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17haaf0ee4e604d506fE(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h19b5a79cad1c4a7eE(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h92b9413b03874ef5E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117ha3e097c13af56770E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.ceef70cbd4be60d1da0c272b343491b3.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.ceef70cbd4be60d1da0c272b343491b3.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.ceef70cbd4be60d1da0c272b343491b3.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.ceef70cbd4be60d1da0c272b343491b3.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h6d287ee106e0cdddE"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h72da66b46395c69eE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h19b5a79cad1c4a7eE(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h72da66b46395c69eE(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf89f17d342b2e472E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hc60daef8b0adbeecE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h50a577384c11e8adE"() unnamed_addr #1 {
start:
  ret i32 0
}

; test_emoji::main
; Function Attrs: uwtable
define internal void @_ZN10test_emoji4main17h64a909985dd03d3aE() unnamed_addr #0 {
start:
  %_32 = alloca [48 x i8], align 8
  %_29 = alloca [4 x i8], align 4
  %_27 = alloca [16 x i8], align 8
  %_25 = alloca [16 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_19 = alloca [48 x i8], align 8
  %y = alloca [4 x i8], align 4
  %x = alloca [4 x i8], align 4
  %_14 = alloca [48 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_51e73e1dcae28e202f0ed6524f206d14)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_cb62211d60c4c34e32cb29c417a2edb2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_4f3f64854e5386b61f09cfaf87383dff)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_fda168bf352b75c306f6553194b204d0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_81e6abcc6128d7d94e5f452b6246b979)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
  store i32 10, ptr %x, align 4
  store i32 20, ptr %y, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h92b9413b03874ef5E(ptr sret([16 x i8]) align 8 %_23, ptr align 4 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h92b9413b03874ef5E(ptr sret([16 x i8]) align 8 %_25, ptr align 4 %y)
  %0 = load i32, ptr %x, align 4
  %1 = load i32, ptr %y, align 4
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %0, i32 %1)
  %_30.0 = extractvalue { i32, i1 } %2, 0
  %_30.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_30.1, label %panic, label %bb13

bb13:                                             ; preds = %start
  store i32 %_30.0, ptr %_29, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h92b9413b03874ef5E(ptr sret([16 x i8]) align 8 %_27, ptr align 4 %_29)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_23, i64 16, i1 false)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_25, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_27, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117ha3e097c13af56770E(ptr sret([48 x i8]) align 8 %_19, ptr align 8 @alloc_41a2d4eab79e9d26efd78e26447ff5a7, ptr align 8 %_22)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_19)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hdfa7846202362bd3E(ptr sret([48 x i8]) align 8 %_32, ptr align 8 @alloc_715a1da5ee2fbf95d7225f4d9c30da25)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_32)
  ret void

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_03b92670611f333628124c6ac0923042) #7
  unreachable
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #3

declare i32 @__CxxFrameHandler3(...) unnamed_addr #4

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #6

define i32 @main(i32 %0, ptr %1) unnamed_addr #4 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h63c33b6af80227abE(ptr @_ZN10test_emoji4main17h64a909985dd03d3aE, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #4 = { "target-cpu"="x86-64" }
attributes #5 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #6 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #7 = { noreturn }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 8071162674064523}
