// 8. String Operations - concatenation, manipulation (Rust version)
// This file demonstrates all string operations in Rust for comparison with Dolet

fn main() {
    println!("=== String Operations Demo (Rust) ===");
    println!();

    // Basic string concatenation
    let first = "Hello";
    let second = "World";
    let combined = format!("{} {}", first, second);
    println!("Basic concatenation:");
    println!("first = '{}'", first);
    println!("second = '{}'", second);
    println!("combined = '{}'", combined);
    println!();

    // String concatenation with variables
    let name = "<PERSON>";
    let age = 25;
    let city = "Cairo";
    let info = format!("Name: {}, Age: {}, City: {}", name, age, city);
    println!("String with variables:");
    println!("{}", info);
    println!();

    // Multiple string concatenation
    let part1 = "The";
    let part2 = "quick";
    let part3 = "brown";
    let part4 = "fox";
    let sentence = format!("{} {} {} {}", part1, part2, part3, part4);
    println!("Multiple concatenation:");
    println!("{}", sentence);
    println!();

    // String with numbers
    let product = "Laptop";
    let price = 1500;
    let quantity = 2;
    let order = format!("Product: {}, Price: ${}, Qty: {}", product, price, quantity);
    println!("String with numbers:");
    println!("{}", order);
    println!();

    // Empty string operations
    let empty = "";
    let text = "Hello";
    let result1 = format!("{}{}", empty, text);
    let result2 = format!("{}{}", text, empty);
    println!("Empty string operations:");
    println!("empty + text = '{}'", result1);
    println!("text + empty = '{}'", result2);
    println!();

    // String building in loop
    let mut builder = String::new();
    for i in 1..=5 {
        builder.push_str(&format!("Step {} ", i));
    }
    println!("String building in loop:");
    println!("Result: '{}'", builder);
    println!();

    // String comparison operations
    let str1 = "apple";
    let str2 = "banana";
    let str3 = "apple";
    println!("String comparisons:");
    println!("str1 = '{}'", str1);
    println!("str2 = '{}'", str2);
    println!("str3 = '{}'", str3);

    if str1 == str3 {
        println!("✓ str1 equals str3");
    } else {
        println!("✗ str1 does not equal str3");
    }

    if str1 != str2 {
        println!("✓ str1 is different from str2");
    } else {
        println!("✗ str1 is same as str2");
    }
    println!();

    // String with special characters
    let special = "Hello, World! @#$%^&*()";
    let quotes = "He said 'Hello' to me";
    let numbers = "Numbers: 123456789";
    println!("Special character strings:");
    println!("{}", special);
    println!("{}", quotes);
    println!("{}", numbers);
    println!();

    // String formatting patterns
    let user1 = format!("User: {}, Score: {}", "Ahmed", 95);
    let user2 = format!("User: {}, Score: {}", "Sara", 87);
    println!("String formatting patterns:");
    println!("{}", user1);
    println!("{}", user2);
    println!();

    // Dynamic string creation
    let base = "Message";
    let mut counter = 1;
    let msg1 = format!("{} {}", base, counter);
    counter += 1;
    let msg2 = format!("{} {}", base, counter);
    counter += 1;
    let msg3 = format!("{} {}", base, counter);
    println!("Dynamic string creation:");
    println!("{}", msg1);
    println!("{}", msg2);
    println!("{}", msg3);
    println!();

    // String arrays and concatenation
    let words = ["Programming", "is", "fun", "and", "creative"];
    let sentence_from_array = words.join(" ");
    println!("String from array:");
    println!("{}", sentence_from_array);
    println!();

    // String with boolean values
    let success = true;
    let error = false;
    let status1 = format!("Operation successful: {}", success);
    let status2 = format!("Error occurred: {}", error);
    println!("String with boolean values:");
    println!("{}", status1);
    println!("{}", status2);
    println!();

    // Complex string operations
    let prefix = "LOG";
    let timestamp = "2024-01-15";
    let level = "INFO";
    let message = "System started";
    let log_entry = format!("[{}] {} [{}] {}", prefix, timestamp, level, message);
    println!("Complex string operations:");
    println!("{}", log_entry);
    println!();

    // String modification
    let original = "Hello World";
    let modified = "Hi Universe";
    println!("String modification:");
    println!("Original: {}", original);
    println!("Modified: {}", modified);
    println!();

    // String with calculations
    let a = 10;
    let b = 5;
    let math_result = format!("{} + {} = {}", a, b, a + b);
    let math_result2 = format!("{} * {} = {}", a, b, a * b);
    println!("String with calculations:");
    println!("{}", math_result);
    println!("{}", math_result2);
    println!();

    // Multi-line string simulation
    let line1 = "This is line 1";
    let line2 = "This is line 2";
    let line3 = "This is line 3";
    let multiline = format!("{} | {} | {}", line1, line2, line3);
    println!("Multi-line string simulation:");
    println!("{}", multiline);
    println!();

    // String length
    println!("String length operations:");
    let test_str = "Hello World";
    println!("Length of '{}': {}", test_str, test_str.len());
    println!();

    // String case conversion
    println!("String case conversion:");
    let lower_str = "hello world";
    let upper_str = "HELLO WORLD";
    println!("Original: '{}'", lower_str);
    println!("Uppercase: '{}'", lower_str.to_uppercase());
    println!("Original: '{}'", upper_str);
    println!("Lowercase: '{}'", upper_str.to_lowercase());
    println!();

    // String contains operations
    println!("String contains operations:");
    let main_text = "Hello World";
    println!("'{}' contains 'World': {}", main_text, main_text.contains("World"));
    println!("'{}' contains 'Hello': {}", main_text, main_text.contains("Hello"));
    println!("'{}' contains 'xyz': {}", main_text, main_text.contains("xyz"));
    println!();

    // String starts with / ends with
    println!("String prefix/suffix operations:");
    let test_string = "Hello World";
    println!("'{}' starts with 'Hello': {}", test_string, test_string.starts_with("Hello"));
    println!("'{}' starts with 'World': {}", test_string, test_string.starts_with("World"));
    println!("'{}' ends with 'World': {}", test_string, test_string.ends_with("World"));
    println!("'{}' ends with 'Hello': {}", test_string, test_string.ends_with("Hello"));
    println!();

    // String trimming
    println!("String trimming:");
    let padded1 = "  Hello  ";
    let padded2 = "  World  ";
    println!("Original: '{}'", padded1);
    println!("Trimmed:  '{}'", padded1.trim());
    println!("Original: '{}'", padded2);
    println!("Trimmed:  '{}'", padded2.trim());
    println!();

    // String replacement
    println!("String replacement:");
    let original_text = "Hello World";
    let replaced1 = original_text.replace("World", "Rust");
    let replaced2 = original_text.replace("Hello", "Hi");
    println!("Original: '{}'", original_text);
    println!("Replace 'World' with 'Rust': '{}'", replaced1);
    println!("Replace 'Hello' with 'Hi': '{}'", replaced2);
    println!();

    // String splitting
    println!("String splitting:");
    let csv_data = "apple,banana,orange";
    let parts: Vec<&str> = csv_data.split(',').collect();
    println!("Split '{}' by ',': {:?}", csv_data, parts);

    let sentence_data = "Hello World Rust";
    let words: Vec<&str> = sentence_data.split(' ').collect();
    println!("Split '{}' by ' ': {:?}", sentence_data, words);
    println!();

    println!("=== End of String Operations Demo ===");
}
