; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 91 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"Cairo\0A\00", align 1
@.str_2 = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_3 = private unnamed_addr constant [20 x i8] c"Hello, World! @#$%\0A\00", align 1
@.str_4 = private unnamed_addr constant [25 x i8] c"This is a long sentence\0A\00", align 1
@.str_5 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_6 = private unnamed_addr constant [11 x i8] c"Beautiful\0A\00", align 1
@.str_7 = private unnamed_addr constant [7 x i8] c"World\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [7 x i8] c"age = \00", align 1
@.str_expr_1 = private unnamed_addr constant [8 x i8] c"year = \00", align 1
@.str_expr_2 = private unnamed_addr constant [12 x i8] c"negative = \00", align 1
@.str_expr_3 = private unnamed_addr constant [8 x i8] c"zero = \00", align 1
@.str_expr_4 = private unnamed_addr constant [9 x i8] c"large = \00", align 1
@.str_expr_5 = private unnamed_addr constant [8 x i8] c"name = \00", align 1
@.str_expr_6 = private unnamed_addr constant [8 x i8] c"city = \00", align 1
@.str_expr_7 = private unnamed_addr constant [10 x i8] c"empty = '\00", align 1
@.str_expr_8 = private unnamed_addr constant [2 x i8] c"'\00", align 1
@.str_expr_9 = private unnamed_addr constant [11 x i8] c"special = \00", align 1
@.str_expr_10 = private unnamed_addr constant [13 x i8] c"multiword = \00", align 1
@.str_expr_11 = private unnamed_addr constant [9 x i8] c"price = \00", align 1
@.str_expr_12 = private unnamed_addr constant [6 x i8] c"pi = \00", align 1
@.str_expr_13 = private unnamed_addr constant [18 x i8] c"negative_float = \00", align 1
@.str_expr_14 = private unnamed_addr constant [14 x i8] c"zero_float = \00", align 1
@.str_expr_15 = private unnamed_addr constant [9 x i8] c"small = \00", align 1
@.str_expr_16 = private unnamed_addr constant [11 x i8] c"precise = \00", align 1
@.str_expr_17 = private unnamed_addr constant [16 x i8] c"large_double = \00", align 1
@.str_expr_18 = private unnamed_addr constant [14 x i8] c"scientific = \00", align 1
@.str_expr_19 = private unnamed_addr constant [8 x i8] c"tiny = \00", align 1
@.str_expr_20 = private unnamed_addr constant [10 x i8] c"letter = \00", align 1
@.str_expr_21 = private unnamed_addr constant [9 x i8] c"digit = \00", align 1
@.str_expr_22 = private unnamed_addr constant [10 x i8] c"symbol = \00", align 1
@.str_expr_23 = private unnamed_addr constant [10 x i8] c"space = '\00", align 1
@.str_expr_24 = private unnamed_addr constant [10 x i8] c"active = \00", align 1
@.str_expr_25 = private unnamed_addr constant [12 x i8] c"finished = \00", align 1
@.str_expr_26 = private unnamed_addr constant [9 x i8] c"ready = \00", align 1
@.str_expr_27 = private unnamed_addr constant [9 x i8] c"error = \00", align 1
@.str_expr_28 = private unnamed_addr constant [11 x i8] c"Integers: \00", align 1
@.str_expr_29 = private unnamed_addr constant [3 x i8] c", \00", align 1
@.str_expr_30 = private unnamed_addr constant [10 x i8] c"Strings: \00", align 1
@.str_expr_31 = private unnamed_addr constant [2 x i8] c" \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.2f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_age = global i32 0, align 4
@global_year = global i32 0, align 4
@global_negative = global i32 0, align 4
@global_zero = global i32 0, align 4
@global_large = global i32 0, align 4
@global_name = global i8* null, align 8
@global_city = global i8* null, align 8
@global_empty = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_special = global i8* null, align 8
@global_multiword = global i8* null, align 8
@global_price = global float 0.0, align 4
@global_pi = global float 0.0, align 4
@global_negative_float = global float 0.0, align 4
@global_zero_float = global float 0.0, align 4
@global_small = global float 0.0, align 4
@global_precise = global double 0.0, align 8
@global_large_double = global double 0.0, align 8
@global_scientific = global double 0.0, align 8
@global_tiny = global double 0.0, align 8
@global_letter = global i8 0, align 1
@global_digit = global i8 0, align 1
@global_symbol = global i8 0, align 1
@global_space = global i8 0, align 1
@global_newline = global i8 0, align 1
@global_active = global i1 0, align 1
@global_finished = global i1 0, align 1
@global_ready = global i1 0, align 1
@global_error = global i1 0, align 1
@global_x = global i32 0, align 4
@global_y = global i32 0, align 4
@global_z = global i32 0, align 4
@global_first = global i8* null, align 8
@global_second = global i8* null, align 8
@global_third = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== Variables and Data Types ===" (inline)
  %tmp_1_str = alloca [33 x i8], align 1
  store [33 x i8] c"=== Variables and Data Types ===\00", [33 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [33 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Integer Variables:" (inline)
  %tmp_3_str = alloca [19 x i8], align 1
  store [19 x i8] c"Integer Variables:\00", [19 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [19 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int age = 25
  store i32 25, i32* @global_age, align 4
  ; int year = 2024
  store i32 2024, i32* @global_year, align 4
  ; int negative = -100
  store i32 -100, i32* @global_negative, align 4
  ; int zero = 0
  store i32 0, i32* @global_zero, align 4
  ; int large = 1000000
  store i32 1000000, i32* @global_large, align 4
  ; print expression: "age = " + age
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_5 = load i32, i32* @global_age, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_5)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "year = " + year
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_6 = load i32, i32* @global_year, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative = " + negative
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_7 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero = " + zero
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_8 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large = " + large
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_9 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "String Variables:" (inline)
  %tmp_10_str = alloca [18 x i8], align 1
  store [18 x i8] c"String Variables:\00", [18 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [18 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string name = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_name, align 8
  ; string city = "Cairo"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_city, align 8
  ; string empty = ""
  store i8* getelementptr inbounds ([1 x i8], [1 x i8]* @.str_2, i64 0, i64 0), i8** @global_empty, align 8
  ; string special = "Hello, World! @#$%"
  store i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_3, i64 0, i64 0), i8** @global_special, align 8
  ; string multiword = "This is a long sentence"
  store i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_4, i64 0, i64 0), i8** @global_multiword, align 8
  ; print expression: "name = " + name
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_12 = load i8*, i8** @global_name, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "city = " + city
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_13 = load i8*, i8** @global_city, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "empty = '" + empty + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_14 = load i8*, i8** @global_empty, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_14)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_8, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "special = " + special
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_15 = load i8*, i8** @global_special, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "multiword = " + multiword
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_16 = load i8*, i8** @global_multiword, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Float Variables:" (inline)
  %tmp_17_str = alloca [17 x i8], align 1
  store [17 x i8] c"Float Variables:\00", [17 x i8]* %tmp_17_str, align 1
  %tmp_18 = bitcast [17 x i8]* %tmp_17_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float price = 19.99
  store float 1.999000e+01, float* @global_price, align 4
  ; float pi = 3.14159
  store float 3.141590e+00, float* @global_pi, align 4
  ; float negative_float = -2.5
  store float -2.500000e+00, float* @global_negative_float, align 4
  ; float zero_float = 0.0
  store float 0.0, float* @global_zero_float, align 4
  ; float small = 0.001
  store float 1.000000e-03, float* @global_small, align 4
  ; print expression: "price = " + price
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_19 = load float, float* @global_price, align 4
  %tmp_20 = fpext float %tmp_19 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "pi = " + pi
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_21 = load float, float* @global_pi, align 4
  %tmp_22 = fpext float %tmp_21 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative_float = " + negative_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_23 = load float, float* @global_negative_float, align 4
  %tmp_24 = fpext float %tmp_23 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero_float = " + zero_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_25 = load float, float* @global_zero_float, align 4
  %tmp_26 = fpext float %tmp_25 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "small = " + small
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_27 = load float, float* @global_small, align 4
  %tmp_28 = fpext float %tmp_27 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_28)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Double Variables:" (inline)
  %tmp_29_str = alloca [18 x i8], align 1
  store [18 x i8] c"Double Variables:\00", [18 x i8]* %tmp_29_str, align 1
  %tmp_30 = bitcast [18 x i8]* %tmp_29_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; double precise = 3.141592653589793
  store double 3.141593e+00, double* @global_precise, align 8
  ; double large_double = 1234567.89012345
  store double 1.234568e+06, double* @global_large_double, align 8
  ; double scientific = 1.23e10
  store double 1.230000e+10, double* @global_scientific, align 8
  ; double tiny = 1.23e-10
  store double 1.230000e-10, double* @global_tiny, align 8
  ; print expression: "precise = " + precise
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_31 = load double, double* @global_precise, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_31)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large_double = " + large_double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_32 = load double, double* @global_large_double, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "scientific = " + scientific
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_33 = load double, double* @global_scientific, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_33)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "tiny = " + tiny
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_34 = load double, double* @global_tiny, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Character Variables:" (inline)
  %tmp_35_str = alloca [21 x i8], align 1
  store [21 x i8] c"Character Variables:\00", [21 x i8]* %tmp_35_str, align 1
  %tmp_36 = bitcast [21 x i8]* %tmp_35_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_36)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; char letter = 'A'
  store i8 65, i8* @global_letter, align 1
  ; char digit = '5'
  store i8 53, i8* @global_digit, align 1
  ; char symbol = '@'
  store i8 64, i8* @global_symbol, align 1
  ; char space = ' '
  store i8 32, i8* @global_space, align 1
  ; char newline = '\n'
  ; char assignment from expression: newline = '\n'
  ; print expression: "letter = " + letter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_37 = load i8, i8* @global_letter, align 1
  %tmp_38 = zext i8 %tmp_37 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_38)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "digit = " + digit
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_39 = load i8, i8* @global_digit, align 1
  %tmp_40 = zext i8 %tmp_39 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "symbol = " + symbol
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_41 = load i8, i8* @global_symbol, align 1
  %tmp_42 = zext i8 %tmp_41 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "space = '" + space + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_23, i32 0, i32 0))
  %tmp_43 = load i8, i8* @global_space, align 1
  %tmp_44 = zext i8 %tmp_43 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_44)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_8, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Boolean Variables:" (inline)
  %tmp_45_str = alloca [19 x i8], align 1
  store [19 x i8] c"Boolean Variables:\00", [19 x i8]* %tmp_45_str, align 1
  %tmp_46 = bitcast [19 x i8]* %tmp_45_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_46)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; bool active = true
  store i1 1, i1* @global_active, align 1
  ; bool finished = false
  store i1 0, i1* @global_finished, align 1
  ; bool ready = true
  store i1 1, i1* @global_ready, align 1
  ; bool error = false
  store i1 0, i1* @global_error, align 1
  ; print expression: "active = " + active
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_47 = load i1, i1* @global_active, align 1
  br i1 %tmp_47, label %tmp_48, label %tmp_49
tmp_48:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_50
tmp_49:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_50
tmp_50:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "finished = " + finished
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_25, i32 0, i32 0))
  %tmp_51 = load i1, i1* @global_finished, align 1
  br i1 %tmp_51, label %tmp_52, label %tmp_53
tmp_52:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_54
tmp_53:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_54
tmp_54:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "ready = " + ready
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_26, i32 0, i32 0))
  %tmp_55 = load i1, i1* @global_ready, align 1
  br i1 %tmp_55, label %tmp_56, label %tmp_57
tmp_56:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_58
tmp_57:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_58
tmp_58:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "error = " + error
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_27, i32 0, i32 0))
  %tmp_59 = load i1, i1* @global_error, align 1
  br i1 %tmp_59, label %tmp_60, label %tmp_61
tmp_60:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_62
tmp_61:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_62
tmp_62:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Variable Modification:" (inline)
  %tmp_63_str = alloca [23 x i8], align 1
  store [23 x i8] c"Variable Modification:\00", [23 x i8]* %tmp_63_str, align 1
  %tmp_64 = bitcast [23 x i8]* %tmp_63_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_64)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set age = 26
  ; set name = "Ali"
  %tmp_65_str = alloca [4 x i8], align 1
  %tmp_66 = getelementptr inbounds [4 x i8], [4 x i8]* %tmp_65_str, i64 0, i64 0
  store i8 65, i8* %tmp_66, align 1
  %tmp_67 = getelementptr inbounds [4 x i8], [4 x i8]* %tmp_65_str, i64 0, i64 1
  store i8 108, i8* %tmp_67, align 1
  %tmp_68 = getelementptr inbounds [4 x i8], [4 x i8]* %tmp_65_str, i64 0, i64 2
  store i8 105, i8* %tmp_68, align 1
  %tmp_69 = getelementptr inbounds [4 x i8], [4 x i8]* %tmp_65_str, i64 0, i64 3
  store i8 0, i8* %tmp_69, align 1
  %tmp_70 = bitcast [4 x i8]* %tmp_65_str to i8*
  store i8* %tmp_70, i8** @global_name, align 8
  ; set price = 29.99
  ; set active = false
  ; print "After modification:" (inline)
  %tmp_71_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_71_str, align 1
  %tmp_72 = bitcast [20 x i8]* %tmp_71_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_72)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "age = " + age
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_73 = load i32, i32* @global_age, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_73)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "name = " + name
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_74 = load i8*, i8** @global_name, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_74)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "price = " + price
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_75 = load float, float* @global_price, align 4
  %tmp_76 = fpext float %tmp_75 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_76)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "active = " + active
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_77 = load i1, i1* @global_active, align 1
  br i1 %tmp_77, label %tmp_78, label %tmp_79
tmp_78:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_80
tmp_79:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_80
tmp_80:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Multiple Variables:" (inline)
  %tmp_81_str = alloca [20 x i8], align 1
  store [20 x i8] c"Multiple Variables:\00", [20 x i8]* %tmp_81_str, align 1
  %tmp_82 = bitcast [20 x i8]* %tmp_81_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_82)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int x = 10
  store i32 10, i32* @global_x, align 4
  ; int y = 20
  store i32 20, i32* @global_y, align 4
  ; int z = 30
  store i32 30, i32* @global_z, align 4
  ; string first = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_5, i64 0, i64 0), i8** @global_first, align 8
  ; string second = "Beautiful"
  store i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_6, i64 0, i64 0), i8** @global_second, align 8
  ; string third = "World"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_7, i64 0, i64 0), i8** @global_third, align 8
  ; print expression: "Integers: " + x + ", " + y + ", " + z
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_83 = load i32, i32* @global_x, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_83)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_84 = load i32, i32* @global_y, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_85 = load i32, i32* @global_z, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_85)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Strings: " + first + " " + second + " " + third
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_86 = load i8*, i8** @global_first, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_87 = load i8*, i8** @global_second, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_87)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_88 = load i8*, i8** @global_third, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_88)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "...................." (inline)
  %tmp_89_str = alloca [21 x i8], align 1
  store [21 x i8] c"....................\00", [21 x i8]* %tmp_89_str, align 1
  %tmp_90 = bitcast [21 x i8]* %tmp_89_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== End of Variables and Types Demo ===" (inline)
  %tmp_91_str = alloca [40 x i8], align 1
  store [40 x i8] c"=== End of Variables and Types Demo ===\00", [40 x i8]* %tmp_91_str, align 1
  %tmp_92 = bitcast [40 x i8]* %tmp_91_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_92)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
