// 23. Algorithms - sorting, searching, optimization (Rust version)
// This file demonstrates algorithms in Rust for comparison with Dolet

fn main() {
    println!("=== Algorithms Demo (Rust) ===");
    println!();

    // Sorting algorithms
    println!("Sorting Algorithms:");
    
    // Bubble sort
    let mut bubble_data = vec![64, 34, 25, 12, 22, 11, 90];
    println!("Bubble Sort:");
    println!("  Original: {:?}", bubble_data);
    bubble_sort(&mut bubble_data);
    println!("  Sorted: {:?}", bubble_data);
    println!();

    // Selection sort
    let mut selection_data = vec![64, 25, 12, 22, 11];
    println!("Selection Sort:");
    println!("  Original: {:?}", selection_data);
    selection_sort(&mut selection_data);
    println!("  Sorted: {:?}", selection_data);
    println!();

    // Insertion sort
    let mut insertion_data = vec![12, 11, 13, 5, 6];
    println!("Insertion Sort:");
    println!("  Original: {:?}", insertion_data);
    insertion_sort(&mut insertion_data);
    println!("  Sorted: {:?}", insertion_data);
    println!();

    // Quick sort
    let mut quick_data = vec![10, 7, 8, 9, 1, 5];
    println!("Quick Sort:");
    println!("  Original: {:?}", quick_data);
    let len = quick_data.len() as i32 - 1;
    quick_sort(&mut quick_data, 0, len);
    println!("  Sorted: {:?}", quick_data);
    println!();

    // Merge sort
    let mut merge_data = vec![38, 27, 43, 3, 9, 82, 10];
    println!("Merge Sort:");
    println!("  Original: {:?}", merge_data);
    merge_sort(&mut merge_data);
    println!("  Sorted: {:?}", merge_data);
    println!();

    // Searching algorithms
    println!("Searching Algorithms:");
    
    let search_array = vec![2, 3, 4, 10, 40, 50, 60, 70, 80, 90];
    let search_targets = vec![10, 25, 70, 100];
    
    println!("Search array: {:?}", search_array);
    
    // Linear search
    println!("Linear Search:");
    for target in &search_targets {
        match linear_search(&search_array, *target) {
            Some(index) => println!("  {} found at index {}", target, index),
            None => println!("  {} not found", target),
        }
    }
    println!();

    // Binary search
    println!("Binary Search:");
    for target in &search_targets {
        match binary_search(&search_array, *target) {
            Some(index) => println!("  {} found at index {}", target, index),
            None => println!("  {} not found", target),
        }
    }
    println!();

    // Graph algorithms
    println!("Graph Algorithms:");
    
    // Breadth-First Search (BFS)
    let graph = create_sample_graph();
    println!("BFS traversal from node 0:");
    let bfs_result = bfs(&graph, 0);
    println!("  Visited order: {:?}", bfs_result);
    
    // Depth-First Search (DFS)
    println!("DFS traversal from node 0:");
    let dfs_result = dfs(&graph, 0);
    println!("  Visited order: {:?}", dfs_result);
    println!();

    // Dynamic programming
    println!("Dynamic Programming:");
    
    // Fibonacci with memoization
    println!("Fibonacci sequence (with memoization):");
    for i in 0..=15 {
        let result = fibonacci_memo(i, &mut std::collections::HashMap::new());
        print!("{} ", result);
    }
    println!();
    
    // Longest Common Subsequence
    let str1 = "ABCDGH";
    let str2 = "AEDFHR";
    let lcs_length = longest_common_subsequence(str1, str2);
    println!("LCS of '{}' and '{}': length = {}", str1, str2, lcs_length);
    println!();

    // Greedy algorithms
    println!("Greedy Algorithms:");
    
    // Coin change problem
    let coins = vec![25, 10, 5, 1]; // quarters, dimes, nickels, pennies
    let amount = 67;
    let coin_result = coin_change_greedy(&coins, amount);
    println!("Coin change for {} cents:", amount);
    for (coin, count) in coin_result {
        if count > 0 {
            println!("  {} cent coins: {}", coin, count);
        }
    }
    println!();

    // Activity selection problem
    let activities = vec![
        Activity { start: 1, finish: 4, name: "A".to_string() },
        Activity { start: 3, finish: 5, name: "B".to_string() },
        Activity { start: 0, finish: 6, name: "C".to_string() },
        Activity { start: 5, finish: 7, name: "D".to_string() },
        Activity { start: 8, finish: 9, name: "E".to_string() },
        Activity { start: 5, finish: 9, name: "F".to_string() },
    ];
    
    let selected = activity_selection(activities);
    println!("Activity Selection:");
    for activity in selected {
        println!("  Activity {}: {} to {}", activity.name, activity.start, activity.finish);
    }
    println!();

    // String algorithms
    println!("String Algorithms:");
    
    // Pattern matching (naive approach)
    let text = "ABABDABACDABABCABCABCABCABC";
    let pattern = "ABABCAB";
    let matches = naive_string_match(text, pattern);
    println!("Pattern '{}' found in text at positions: {:?}", pattern, matches);
    
    // Palindrome checking
    let test_strings = vec!["racecar", "hello", "madam", "level", "world"];
    println!("Palindrome checking:");
    for s in test_strings {
        println!("  '{}' is palindrome: {}", s, is_palindrome_algorithm(s));
    }
    println!();

    // Mathematical algorithms
    println!("Mathematical Algorithms:");
    
    // Greatest Common Divisor (Euclidean algorithm)
    let pairs = vec![(48, 18), (100, 25), (17, 13)];
    println!("GCD calculations:");
    for (a, b) in pairs {
        println!("  GCD({}, {}) = {}", a, b, gcd(a, b));
    }
    
    // Prime number generation (Sieve of Eratosthenes)
    let limit = 30;
    let primes = sieve_of_eratosthenes(limit);
    println!("Prime numbers up to {}: {:?}", limit, primes);
    
    // Fast exponentiation
    let base = 2;
    let exponents = vec![0, 1, 5, 10, 20];
    println!("Fast exponentiation (base = {}):", base);
    for exp in exponents {
        let result = fast_power(base, exp);
        println!("  {}^{} = {}", base, exp, result);
    }
    println!();

    // Optimization algorithms
    println!("Optimization Algorithms:");
    
    // Finding maximum subarray sum (Kadane's algorithm)
    let array = vec![-2, -3, 4, -1, -2, 1, 5, -3];
    let max_sum = max_subarray_sum(&array);
    println!("Maximum subarray sum in {:?}: {}", array, max_sum);
    
    // Two sum problem
    let numbers = vec![2, 7, 11, 15];
    let target = 9;
    match two_sum(&numbers, target) {
        Some((i, j)) => println!("Two sum indices for target {}: ({}, {})", target, i, j),
        None => println!("No two sum solution found for target {}", target),
    }
    println!();

    // Backtracking algorithms
    println!("Backtracking Algorithms:");
    
    // N-Queens problem (for small N)
    let n = 4;
    let solutions = solve_n_queens(n);
    println!("{}-Queens problem solutions: {} found", n, solutions.len());
    if !solutions.is_empty() {
        println!("First solution:");
        print_chess_board(&solutions[0]);
    }
    println!();

    // Performance comparison
    println!("Performance Comparison:");
    let test_data = (1..=1000).collect::<Vec<i32>>();
    
    // Time different sorting algorithms (conceptually)
    println!("Sorting 1000 elements:");
    println!("  Bubble sort: O(n²) - slow for large data");
    println!("  Quick sort: O(n log n) - fast average case");
    println!("  Merge sort: O(n log n) - consistent performance");
    
    println!("Searching in 1000 elements:");
    println!("  Linear search: O(n) - checks each element");
    println!("  Binary search: O(log n) - very fast on sorted data");
    println!();

    println!("=== End of Algorithms Demo ===");
}

// Sorting algorithms
fn bubble_sort(arr: &mut Vec<i32>) {
    let n = arr.len();
    for i in 0..n {
        for j in 0..n - 1 - i {
            if arr[j] > arr[j + 1] {
                arr.swap(j, j + 1);
            }
        }
    }
}

fn selection_sort(arr: &mut Vec<i32>) {
    let n = arr.len();
    for i in 0..n {
        let mut min_idx = i;
        for j in i + 1..n {
            if arr[j] < arr[min_idx] {
                min_idx = j;
            }
        }
        arr.swap(i, min_idx);
    }
}

fn insertion_sort(arr: &mut Vec<i32>) {
    for i in 1..arr.len() {
        let key = arr[i];
        let mut j = i as i32 - 1;
        
        while j >= 0 && arr[j as usize] > key {
            arr[(j + 1) as usize] = arr[j as usize];
            j -= 1;
        }
        arr[(j + 1) as usize] = key;
    }
}

fn quick_sort(arr: &mut Vec<i32>, low: i32, high: i32) {
    if low < high {
        let pi = partition(arr, low, high);
        quick_sort(arr, low, pi - 1);
        quick_sort(arr, pi + 1, high);
    }
}

fn partition(arr: &mut Vec<i32>, low: i32, high: i32) -> i32 {
    let pivot = arr[high as usize];
    let mut i = low - 1;
    
    for j in low..high {
        if arr[j as usize] < pivot {
            i += 1;
            arr.swap(i as usize, j as usize);
        }
    }
    arr.swap((i + 1) as usize, high as usize);
    i + 1
}

fn merge_sort(arr: &mut Vec<i32>) {
    let len = arr.len();
    if len <= 1 {
        return;
    }
    
    let mid = len / 2;
    let mut left = arr[0..mid].to_vec();
    let mut right = arr[mid..].to_vec();
    
    merge_sort(&mut left);
    merge_sort(&mut right);
    
    merge(arr, &left, &right);
}

fn merge(arr: &mut Vec<i32>, left: &[i32], right: &[i32]) {
    let mut i = 0;
    let mut j = 0;
    let mut k = 0;
    
    while i < left.len() && j < right.len() {
        if left[i] <= right[j] {
            arr[k] = left[i];
            i += 1;
        } else {
            arr[k] = right[j];
            j += 1;
        }
        k += 1;
    }
    
    while i < left.len() {
        arr[k] = left[i];
        i += 1;
        k += 1;
    }
    
    while j < right.len() {
        arr[k] = right[j];
        j += 1;
        k += 1;
    }
}

// Searching algorithms
fn linear_search(arr: &[i32], target: i32) -> Option<usize> {
    for (index, &value) in arr.iter().enumerate() {
        if value == target {
            return Some(index);
        }
    }
    None
}

fn binary_search(arr: &[i32], target: i32) -> Option<usize> {
    let mut left = 0;
    let mut right = arr.len();
    
    while left < right {
        let mid = left + (right - left) / 2;
        if arr[mid] == target {
            return Some(mid);
        } else if arr[mid] < target {
            left = mid + 1;
        } else {
            right = mid;
        }
    }
    None
}

// Graph algorithms
type Graph = Vec<Vec<usize>>;

fn create_sample_graph() -> Graph {
    vec![
        vec![1, 2],       // Node 0 connects to 1, 2
        vec![0, 3, 4],    // Node 1 connects to 0, 3, 4
        vec![0, 5],       // Node 2 connects to 0, 5
        vec![1],          // Node 3 connects to 1
        vec![1, 5],       // Node 4 connects to 1, 5
        vec![2, 4],       // Node 5 connects to 2, 4
    ]
}

fn bfs(graph: &Graph, start: usize) -> Vec<usize> {
    let mut visited = vec![false; graph.len()];
    let mut queue = std::collections::VecDeque::new();
    let mut result = Vec::new();
    
    queue.push_back(start);
    visited[start] = true;
    
    while let Some(node) = queue.pop_front() {
        result.push(node);
        
        for &neighbor in &graph[node] {
            if !visited[neighbor] {
                visited[neighbor] = true;
                queue.push_back(neighbor);
            }
        }
    }
    
    result
}

fn dfs(graph: &Graph, start: usize) -> Vec<usize> {
    let mut visited = vec![false; graph.len()];
    let mut result = Vec::new();
    dfs_helper(graph, start, &mut visited, &mut result);
    result
}

fn dfs_helper(graph: &Graph, node: usize, visited: &mut Vec<bool>, result: &mut Vec<usize>) {
    visited[node] = true;
    result.push(node);
    
    for &neighbor in &graph[node] {
        if !visited[neighbor] {
            dfs_helper(graph, neighbor, visited, result);
        }
    }
}

// Dynamic programming
fn fibonacci_memo(n: u32, memo: &mut std::collections::HashMap<u32, u64>) -> u64 {
    if let Some(&result) = memo.get(&n) {
        return result;
    }
    
    let result = match n {
        0 => 0,
        1 => 1,
        _ => fibonacci_memo(n - 1, memo) + fibonacci_memo(n - 2, memo),
    };
    
    memo.insert(n, result);
    result
}

fn longest_common_subsequence(s1: &str, s2: &str) -> usize {
    let chars1: Vec<char> = s1.chars().collect();
    let chars2: Vec<char> = s2.chars().collect();
    let m = chars1.len();
    let n = chars2.len();
    
    let mut dp = vec![vec![0; n + 1]; m + 1];
    
    for i in 1..=m {
        for j in 1..=n {
            if chars1[i - 1] == chars2[j - 1] {
                dp[i][j] = dp[i - 1][j - 1] + 1;
            } else {
                dp[i][j] = dp[i - 1][j].max(dp[i][j - 1]);
            }
        }
    }
    
    dp[m][n]
}

// Greedy algorithms
fn coin_change_greedy(coins: &[i32], amount: i32) -> Vec<(i32, i32)> {
    let mut result = Vec::new();
    let mut remaining = amount;
    
    for &coin in coins {
        let count = remaining / coin;
        result.push((coin, count));
        remaining %= coin;
    }
    
    result
}

#[derive(Clone)]
struct Activity {
    start: i32,
    finish: i32,
    name: String,
}

fn activity_selection(mut activities: Vec<Activity>) -> Vec<Activity> {
    activities.sort_by_key(|a| a.finish);
    
    let mut result = Vec::new();
    let mut last_finish = 0;
    
    for activity in activities {
        if activity.start >= last_finish {
            last_finish = activity.finish;
            result.push(activity);
        }
    }
    
    result
}

// String algorithms
fn naive_string_match(text: &str, pattern: &str) -> Vec<usize> {
    let text_chars: Vec<char> = text.chars().collect();
    let pattern_chars: Vec<char> = pattern.chars().collect();
    let mut matches = Vec::new();
    
    for i in 0..=text_chars.len().saturating_sub(pattern_chars.len()) {
        let mut found = true;
        for j in 0..pattern_chars.len() {
            if text_chars[i + j] != pattern_chars[j] {
                found = false;
                break;
            }
        }
        if found {
            matches.push(i);
        }
    }
    
    matches
}

fn is_palindrome_algorithm(s: &str) -> bool {
    let chars: Vec<char> = s.chars().collect();
    let len = chars.len();
    
    for i in 0..len / 2 {
        if chars[i] != chars[len - 1 - i] {
            return false;
        }
    }
    true
}

// Mathematical algorithms
fn gcd(mut a: u32, mut b: u32) -> u32 {
    while b != 0 {
        let temp = b;
        b = a % b;
        a = temp;
    }
    a
}

fn sieve_of_eratosthenes(limit: usize) -> Vec<usize> {
    let mut is_prime = vec![true; limit + 1];
    is_prime[0] = false;
    if limit > 0 {
        is_prime[1] = false;
    }
    
    for i in 2..=((limit as f64).sqrt() as usize) {
        if is_prime[i] {
            for j in ((i * i)..=limit).step_by(i) {
                is_prime[j] = false;
            }
        }
    }
    
    (2..=limit).filter(|&i| is_prime[i]).collect()
}

fn fast_power(base: u64, exp: u32) -> u64 {
    if exp == 0 {
        return 1;
    }
    
    let half = fast_power(base, exp / 2);
    if exp % 2 == 0 {
        half * half
    } else {
        base * half * half
    }
}

// Optimization algorithms
fn max_subarray_sum(arr: &[i32]) -> i32 {
    let mut max_so_far = arr[0];
    let mut max_ending_here = arr[0];
    
    for &num in &arr[1..] {
        max_ending_here = num.max(max_ending_here + num);
        max_so_far = max_so_far.max(max_ending_here);
    }
    
    max_so_far
}

fn two_sum(nums: &[i32], target: i32) -> Option<(usize, usize)> {
    let mut map = std::collections::HashMap::new();
    
    for (i, &num) in nums.iter().enumerate() {
        let complement = target - num;
        if let Some(&j) = map.get(&complement) {
            return Some((j, i));
        }
        map.insert(num, i);
    }
    
    None
}

// Backtracking algorithms
fn solve_n_queens(n: usize) -> Vec<Vec<usize>> {
    let mut solutions = Vec::new();
    let mut board = vec![0; n];
    solve_n_queens_helper(0, n, &mut board, &mut solutions);
    solutions
}

fn solve_n_queens_helper(row: usize, n: usize, board: &mut Vec<usize>, solutions: &mut Vec<Vec<usize>>) {
    if row == n {
        solutions.push(board.clone());
        return;
    }
    
    for col in 0..n {
        if is_safe(row, col, board) {
            board[row] = col;
            solve_n_queens_helper(row + 1, n, board, solutions);
        }
    }
}

fn is_safe(row: usize, col: usize, board: &[usize]) -> bool {
    for i in 0..row {
        if board[i] == col || 
           board[i] + i == col + row || 
           board[i] as i32 - i as i32 == col as i32 - row as i32 {
            return false;
        }
    }
    true
}

fn print_chess_board(solution: &[usize]) {
    for &col in solution {
        for j in 0..solution.len() {
            if j == col {
                print!("Q ");
            } else {
                print!(". ");
            }
        }
        println!();
    }
}
