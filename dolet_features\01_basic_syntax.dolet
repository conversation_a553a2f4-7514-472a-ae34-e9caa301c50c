# 01. Basic Syntax - Dolet Language Fundamentals
# This file demonstrates the basic syntax elements of Dolet
set a = 10
print "=== Dolet Basic Syntax Demo ==="

print "Integers: " + a
print "hi: " + " ahmed"
print a
print 10

# Comments
# This is a single-line comment
print "Comments work with # symbol"

# Basic print statements
print "Hello, World!"
print "This is Dolet programming language"
print ""

# Empty lines and spacing
print "Empty lines help organize code"


print "Multiple empty lines above"
print ""

# String literals with different quotes
print "Double quotes work"
print 1
print ""

# Special characters in strings
print "Special chars: @#$%^&*()"
print "Numbers in strings: *********"
print "Mixed: Hello123World!"
print ""

# Multiple print statements
print "Line 1"
print "Line 2" 
print "Line 3"
print ""

# Print with empty string
print ""
print "Empty line printed above"
print ""

print "=== End of Basic Syntax Demo ==="
