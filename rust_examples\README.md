# Rust Examples - Dolet Language Feature Comparison

This directory contains Rust implementations of all the features demonstrated in the Dolet language examples. Each file corresponds to a specific Dolet example file and shows how the same concepts are implemented in Rust.

## File Structure

| Rust File | Dolet File | Description |
|-----------|------------|-------------|
| `01_variables_data_types.rs` | `01_variables_data_types.dolet` | Variable declarations and basic data types |
| `02_mathematical_operations.rs` | `02_mathematical_operations.dolet` | Arithmetic operations and expressions |
| `03_comparison_operations.rs` | `03_comparison_operations.dolet` | Comparison operators and boolean logic |
| `04_conditional_statements.rs` | `04_conditional_statements.dolet` | If-else statements and conditional logic |
| `05_loops.rs` | `05_loops.dolet` | For loops, while loops, and iteration |
| `06_functions.rs` | `06_functions.dolet` | Function definitions, parameters, and return values |
| `07_arrays.rs` | `07_arrays.dolet` | Array operations and manipulation |
| `08_string_operations.rs` | `08_string_operations.dolet` | String concatenation and basic operations |
| `09_input_output.rs` | `09_input_output.dolet` | Print statements and basic I/O |
| `10_builtin_functions.rs` | `10_builtin_functions.dolet` | Built-in mathematical and utility functions |
| `11_advanced_features.rs` | `11_advanced_features.dolet` | Advanced language features and control flow |
| `12_comments.rs` | `12_comments.dolet` | Comment styles and documentation |
| `13_constants_special_values.rs` | `13_constants_special_values.dolet` | Constants and special numeric values |
| `14_complex_expressions.rs` | `14_complex_expressions.dolet` | Complex mathematical and logical expressions |
| `15_imports_modules.rs` | `15_imports_modules.dolet` | Module system and imports |
| `16_variable_scope.rs` | `16_variable_scope.dolet` | Variable scope and lifetime |
| `17_recursive_functions.rs` | `17_recursive_functions.dolet` | Recursive function implementations |
| `18_array_operations.rs` | `18_array_operations.dolet` | Advanced array operations and algorithms |
| `19_string_manipulation.rs` | `19_string_manipulation.dolet` | Advanced string processing |
| `20_file_operations.rs` | `20_file_operations.dolet` | File I/O and filesystem operations |
| `21_mathematical_functions.rs` | `21_mathematical_functions.dolet` | Advanced mathematical functions |
| `22_data_structures.rs` | `22_data_structures.dolet` | Data structures and collections |
| `23_algorithms.rs` | `23_algorithms.dolet` | Common algorithms and data processing |
| `24_error_handling.rs` | `24_error_handling.dolet` | Error handling and exception management |
| `25_concurrency_basics.rs` | `25_concurrency_basics.dolet` | Basic concurrency and threading |

## How to Run

### Prerequisites
- Install Rust from [https://rustup.rs/](https://rustup.rs/)
- Ensure you have `rustc` and `cargo` available in your PATH

### Running Individual Examples

To run any specific example:

```bash
# Navigate to the rust_examples directory
cd rust_examples

# Compile and run a specific example
rustc 01_variables_data_types.rs && ./01_variables_data_types

# Or use cargo for better error messages
cargo run --bin 01_variables_data_types
```

### Running All Examples

You can create a simple script to run all examples:

```bash
#!/bin/bash
for file in *.rs; do
    if [ "$file" != "README.md" ]; then
        echo "=== Running $file ==="
        rustc "$file" && ./"${file%.rs}"
        echo
    fi
done
```

### Using Cargo Project (Recommended)

For better dependency management and compilation, you can create a `Cargo.toml` file:

```toml
[package]
name = "rust_examples"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "01_variables_data_types"
path = "01_variables_data_types.rs"

[[bin]]
name = "02_mathematical_operations"
path = "02_mathematical_operations.rs"

# ... add more bins for each example
```

Then run with:
```bash
cargo run --bin 01_variables_data_types
```

## Key Differences Between Dolet and Rust

### 1. Type System
- **Dolet**: Dynamic typing with automatic type inference
- **Rust**: Static typing with explicit type annotations and inference

### 2. Memory Management
- **Dolet**: Garbage collected (assumed)
- **Rust**: Ownership system with compile-time memory safety

### 3. Error Handling
- **Dolet**: Exception-based error handling
- **Rust**: Result and Option types for explicit error handling

### 4. Concurrency
- **Dolet**: Thread-based concurrency (assumed)
- **Rust**: Ownership-based thread safety with channels and mutexes

### 5. Syntax Differences
- **Dolet**: More Python-like syntax
- **Rust**: C-like syntax with additional safety features

## Learning Path

1. **Start with basics**: Begin with files 01-05 to understand fundamental concepts
2. **Functions and data**: Progress through files 06-10 for function and data handling
3. **Advanced features**: Explore files 11-17 for more complex language features
4. **Practical applications**: Study files 18-25 for real-world programming concepts

## Notes

- Each Rust example includes detailed comments explaining the concepts
- The examples are designed to be educational and demonstrate best practices
- Some Rust examples include additional features not present in Dolet to show Rust-specific capabilities
- Error handling in Rust examples uses proper Result/Option types instead of exceptions

## Contributing

When adding new examples:
1. Follow the existing naming convention
2. Include comprehensive comments
3. Demonstrate both basic and advanced usage
4. Update this README with the new file information

## Resources

- [The Rust Programming Language Book](https://doc.rust-lang.org/book/)
- [Rust by Example](https://doc.rust-lang.org/rust-by-example/)
- [Rust Standard Library Documentation](https://doc.rust-lang.org/std/)
