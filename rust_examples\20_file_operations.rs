// 20. File Operations - reading, writing, file management (Rust version)
// This file demonstrates file operations in Rust for comparison with Dolet

use std::fs::{self, File, OpenOptions};
use std::io::{self, Read, Write, <PERSON>uf<PERSON><PERSON>, <PERSON>uf<PERSON>eader, BufWriter};
use std::path::Path;

fn main() {
    println!("=== File Operations Demo (Rust) ===");
    println!();

    // Basic file writing
    println!("Basic file writing:");
    let content = "Hello, World!\nThis is a test file.\nCreated with Rust.";
    
    match fs::write("test_file.txt", content) {
        Ok(_) => println!("✓ File 'test_file.txt' written successfully"),
        Err(e) => println!("✗ Error writing file: {}", e),
    }
    println!();

    // Basic file reading
    println!("Basic file reading:");
    match fs::read_to_string("test_file.txt") {
        Ok(file_content) => {
            println!("✓ File read successfully:");
            println!("Content:\n{}", file_content);
        }
        Err(e) => println!("✗ Error reading file: {}", e),
    }
    println!();

    // File existence checking
    println!("File existence checking:");
    let files_to_check = ["test_file.txt", "nonexistent.txt", "another_file.txt"];
    
    for filename in files_to_check {
        if Path::new(filename).exists() {
            println!("✓ File '{}' exists", filename);
        } else {
            println!("✗ File '{}' does not exist", filename);
        }
    }
    println!();

    // File metadata
    println!("File metadata:");
    match fs::metadata("test_file.txt") {
        Ok(metadata) => {
            println!("File: test_file.txt");
            println!("  Size: {} bytes", metadata.len());
            println!("  Is file: {}", metadata.is_file());
            println!("  Is directory: {}", metadata.is_dir());
            println!("  Read-only: {}", metadata.permissions().readonly());
        }
        Err(e) => println!("✗ Error getting metadata: {}", e),
    }
    println!();

    // Appending to file
    println!("Appending to file:");
    let append_content = "\nThis line was appended.\nAnd this is another appended line.";
    
    match OpenOptions::new()
        .create(true)
        .append(true)
        .open("test_file.txt")
    {
        Ok(mut file) => {
            match file.write_all(append_content.as_bytes()) {
                Ok(_) => println!("✓ Content appended successfully"),
                Err(e) => println!("✗ Error appending: {}", e),
            }
        }
        Err(e) => println!("✗ Error opening file for append: {}", e),
    }
    println!();

    // Reading file after append
    println!("Reading file after append:");
    match fs::read_to_string("test_file.txt") {
        Ok(content) => {
            println!("Updated content:\n{}", content);
        }
        Err(e) => println!("✗ Error reading file: {}", e),
    }
    println!();

    // Line-by-line reading
    println!("Line-by-line reading:");
    match File::open("test_file.txt") {
        Ok(file) => {
            let reader = BufReader::new(file);
            println!("Reading line by line:");
            for (line_num, line) in reader.lines().enumerate() {
                match line {
                    Ok(content) => println!("  Line {}: {}", line_num + 1, content),
                    Err(e) => println!("  Error reading line {}: {}", line_num + 1, e),
                }
            }
        }
        Err(e) => println!("✗ Error opening file: {}", e),
    }
    println!();

    // Writing multiple files
    println!("Writing multiple files:");
    let file_data = [
        ("file1.txt", "This is the content of file 1.\nIt has multiple lines."),
        ("file2.txt", "File 2 content here.\nDifferent from file 1."),
        ("file3.txt", "Third file with its own content.\nUnique data."),
    ];
    
    for (filename, content) in file_data {
        match fs::write(filename, content) {
            Ok(_) => println!("✓ Created file: {}", filename),
            Err(e) => println!("✗ Error creating {}: {}", filename, e),
        }
    }
    println!();

    // Reading multiple files
    println!("Reading multiple files:");
    for (filename, _) in file_data {
        match fs::read_to_string(filename) {
            Ok(content) => {
                println!("Content of {}:", filename);
                println!("{}", content);
                println!("---");
            }
            Err(e) => println!("✗ Error reading {}: {}", filename, e),
        }
    }
    println!();

    // File copying
    println!("File copying:");
    match fs::copy("test_file.txt", "test_file_copy.txt") {
        Ok(bytes_copied) => println!("✓ File copied successfully ({} bytes)", bytes_copied),
        Err(e) => println!("✗ Error copying file: {}", e),
    }
    println!();

    // Directory operations
    println!("Directory operations:");
    
    // Create directory
    match fs::create_dir("test_directory") {
        Ok(_) => println!("✓ Directory 'test_directory' created"),
        Err(e) => {
            if e.kind() == io::ErrorKind::AlreadyExists {
                println!("ℹ Directory 'test_directory' already exists");
            } else {
                println!("✗ Error creating directory: {}", e);
            }
        }
    }
    
    // List directory contents
    match fs::read_dir(".") {
        Ok(entries) => {
            println!("Current directory contents:");
            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let path = entry.path();
                        let file_type = if path.is_dir() { "DIR" } else { "FILE" };
                        println!("  {} - {}", file_type, path.display());
                    }
                    Err(e) => println!("  Error reading entry: {}", e),
                }
            }
        }
        Err(e) => println!("✗ Error reading directory: {}", e),
    }
    println!();

    // File moving/renaming
    println!("File moving/renaming:");
    match fs::rename("file3.txt", "test_directory/moved_file3.txt") {
        Ok(_) => println!("✓ File moved to directory"),
        Err(e) => println!("✗ Error moving file: {}", e),
    }
    println!();

    // Binary file operations
    println!("Binary file operations:");
    let binary_data = vec![0u8, 1, 2, 3, 4, 5, 255, 254, 253];
    
    match fs::write("binary_file.bin", &binary_data) {
        Ok(_) => println!("✓ Binary file written"),
        Err(e) => println!("✗ Error writing binary file: {}", e),
    }
    
    match fs::read("binary_file.bin") {
        Ok(data) => {
            println!("✓ Binary file read successfully");
            println!("  Data: {:?}", data);
            println!("  Size: {} bytes", data.len());
        }
        Err(e) => println!("✗ Error reading binary file: {}", e),
    }
    println!();

    // CSV file operations
    println!("CSV file operations:");
    let csv_content = "Name,Age,City\nAhmed,25,Cairo\nSara,30,Alexandria\nOmar,28,Giza";
    
    match fs::write("data.csv", csv_content) {
        Ok(_) => {
            println!("✓ CSV file created");
            
            // Read and parse CSV
            match fs::read_to_string("data.csv") {
                Ok(content) => {
                    println!("CSV content:");
                    for (i, line) in content.lines().enumerate() {
                        if i == 0 {
                            println!("  Header: {}", line);
                        } else {
                            let fields: Vec<&str> = line.split(',').collect();
                            println!("  Record {}: Name={}, Age={}, City={}", 
                                   i, fields[0], fields[1], fields[2]);
                        }
                    }
                }
                Err(e) => println!("✗ Error reading CSV: {}", e),
            }
        }
        Err(e) => println!("✗ Error creating CSV: {}", e),
    }
    println!();

    // JSON-like file operations
    println!("JSON-like file operations:");
    let json_like_content = r#"{
    "name": "Ahmed Hassan",
    "age": 25,
    "city": "Cairo",
    "skills": ["Rust", "Python", "JavaScript"],
    "active": true
}"#;
    
    match fs::write("data.json", json_like_content) {
        Ok(_) => {
            println!("✓ JSON file created");
            
            match fs::read_to_string("data.json") {
                Ok(content) => {
                    println!("JSON content:");
                    println!("{}", content);
                }
                Err(e) => println!("✗ Error reading JSON: {}", e),
            }
        }
        Err(e) => println!("✗ Error creating JSON: {}", e),
    }
    println!();

    // Log file operations
    println!("Log file operations:");
    let log_entries = [
        "[2024-01-15 10:00:00] INFO: Application started",
        "[2024-01-15 10:01:00] DEBUG: Loading configuration",
        "[2024-01-15 10:02:00] INFO: User logged in: ahmed",
        "[2024-01-15 10:03:00] WARNING: Low disk space",
        "[2024-01-15 10:04:00] ERROR: Database connection failed",
    ];
    
    // Write log file
    match File::create("application.log") {
        Ok(file) => {
            let mut writer = BufWriter::new(file);
            for entry in log_entries {
                if let Err(e) = writeln!(writer, "{}", entry) {
                    println!("✗ Error writing log entry: {}", e);
                }
            }
            println!("✓ Log file created with {} entries", log_entries.len());
        }
        Err(e) => println!("✗ Error creating log file: {}", e),
    }
    
    // Read and filter log file
    match File::open("application.log") {
        Ok(file) => {
            let reader = BufReader::new(file);
            println!("Error entries from log:");
            for line in reader.lines() {
                match line {
                    Ok(content) => {
                        if content.contains("ERROR") {
                            println!("  {}", content);
                        }
                    }
                    Err(e) => println!("  Error reading log line: {}", e),
                }
            }
        }
        Err(e) => println!("✗ Error reading log file: {}", e),
    }
    println!();

    // File size and statistics
    println!("File size and statistics:");
    let files_to_analyze = ["test_file.txt", "data.csv", "data.json", "application.log"];
    let mut total_size = 0u64;
    
    for filename in files_to_analyze {
        match fs::metadata(filename) {
            Ok(metadata) => {
                let size = metadata.len();
                total_size += size;
                println!("  {}: {} bytes", filename, size);
            }
            Err(e) => println!("  {}: Error getting size - {}", filename, e),
        }
    }
    println!("Total size of analyzed files: {} bytes", total_size);
    println!();

    // Cleanup operations
    println!("Cleanup operations:");
    let files_to_remove = [
        "test_file.txt", "test_file_copy.txt", "file1.txt", "file2.txt",
        "binary_file.bin", "data.csv", "data.json", "application.log"
    ];
    
    for filename in files_to_remove {
        match fs::remove_file(filename) {
            Ok(_) => println!("✓ Removed file: {}", filename),
            Err(e) => {
                if e.kind() != io::ErrorKind::NotFound {
                    println!("✗ Error removing {}: {}", filename, e);
                }
            }
        }
    }
    
    // Remove directory and its contents
    match fs::remove_dir_all("test_directory") {
        Ok(_) => println!("✓ Removed directory: test_directory"),
        Err(e) => {
            if e.kind() != io::ErrorKind::NotFound {
                println!("✗ Error removing directory: {}", e);
            }
        }
    }
    println!();

    println!("=== End of File Operations Demo ===");
}
