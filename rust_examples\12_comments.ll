; ModuleID = '12_comments.feddbc16f02c3e07-cgu.0'
source_filename = "12_comments.feddbc16f02c3e07-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
%"core::fmt::rt::Placeholder" = type { %"core::fmt::rt::Count", %"core::fmt::rt::Count", i64, i32, [1 x i32] }
%"core::fmt::rt::Count" = type { i16, [7 x i16] }

@alloc_7e0cd81f9dcb179626435c932d0b5a52 = private unnamed_addr constant [214 x i8] c"unsafe precondition(s) violated: slice::get_unchecked requires that the range is within the slice\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_f81c2c1bd0b99ce84e80a9469274ae18 = private unnamed_addr constant [91 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\adapters\\enumerate.rs", align 1
@alloc_67987f2acd65c96a8c883ff2ecae97af = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f81c2c1bd0b99ce84e80a9469274ae18, [16 x i8] c"[\00\00\00\00\00\00\001\00\00\00\09\00\00\00" }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h91104e2ed3d9b4f7E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf71c0a080a4baa4cE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf71c0a080a4baa4cE" }>, align 8
@anon.e9ee036f1d998da69b02774f266e1b43.0 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@anon.e9ee036f1d998da69b02774f266e1b43.1 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_846c8306b730cf00804b037ffdb61170 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_sub cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_91f2a00ff2cd9cdc4bb205a66832e2bb = private unnamed_addr constant [219 x i8] c"unsafe precondition(s) violated: str::get_unchecked requires that the range is within the string slice\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_8c83f7f409a3fddc842cc79871b63dd1 = private unnamed_addr constant [75 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\str\\mod.rs", align 1
@alloc_7afbf74ed710c75f5dddd72ec933c2e5 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_8c83f7f409a3fddc842cc79871b63dd1, [16 x i8] c"K\00\00\00\00\00\00\00{\01\00\00\0D\00\00\00" }>, align 8
@alloc_2036c7ed9ced1d18087f55fa8b69f75d = private unnamed_addr constant [79 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\str\\pattern.rs", align 1
@alloc_94daefe3bc4de5a6c4a5a8e87168a971 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\FD\06\00\00\17\00\00\00" }>, align 8
@alloc_98442059e4852e99c56931d49fa198af = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\17\07\00\00,\00\00\00" }>, align 8
@alloc_1399bee46b96c7f2f59a90d1c4d86e8d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\1A\07\00\00!\00\00\00" }>, align 8
@alloc_e85b6867b441d45a32f5c9e81ee13411 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00Q\07\00\00\18\00\00\00" }>, align 8
@alloc_162b8c84a21eee0918482f7aa1726e6f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00N\07\00\00\0D\00\00\00" }>, align 8
@alloc_d0b5540d38aa67683bd942cb6db4a43b = private unnamed_addr constant [28 x i8] c"window size must be non-zero", align 1
@alloc_29949ac893d7f5b8b2595a656c15fe2e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\13\07\00\00\1E\00\00\00" }>, align 8
@alloc_8821998f047ca62cad40e6bc4e4d87c4 = private unnamed_addr constant [1 x i8] c"\01", align 1
@alloc_a2b3767d37061bfef0bfcdf089c5053c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\08\07\00\00I\00\00\00" }>, align 8
@alloc_8113544254eb174f4af9b7b42b23e9d1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\E2\05\00\00\14\00\00\00" }>, align 8
@alloc_7131847d62882af9d6c09c2b5050c8ea = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\E2\05\00\00!\00\00\00" }>, align 8
@alloc_85f8ff8172fa0a155a0a9d540f76803c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\D6\05\00\00\14\00\00\00" }>, align 8
@alloc_5e95671221ecfb0d1badc509254cde7b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\D6\05\00\00!\00\00\00" }>, align 8
@alloc_4dc07e69e5d34e9d1484dfbbef0bb9b1 = private unnamed_addr constant [174 x i8] c"unsafe precondition(s) violated: invalid value for `char`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_9d37b389f51913e222e48fbc2fb8b511 = private unnamed_addr constant [80 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\slice\\memchr.rs", align 1
@alloc_c4a687d06f875c3cd6d9b59a57eb65ae = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9d37b389f51913e222e48fbc2fb8b511, [16 x i8] c"P\00\00\00\00\00\00\00(\00\00\00\0C\00\00\00" }>, align 8
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@alloc_3ec2d20c60de7d2fe53bb70f0ca537b8 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00\E4\03\00\00:\00\00\00" }>, align 8
@__rust_no_alloc_shim_is_unstable = external global i8
@alloc_ce48450aae05af7a24a801fc57e6a3b8 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2036c7ed9ced1d18087f55fa8b69f75d, [16 x i8] c"O\00\00\00\00\00\00\00f\04\00\00$\00\00\00" }>, align 8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_c10cf53f0ee0f754900a21ca81d4eda2 = private unnamed_addr constant [78 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\slice\\iter.rs", align 1
@alloc_1ce40ffc6da8a10f7c1ede63115687a0 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_c10cf53f0ee0f754900a21ca81d4eda2, [16 x i8] c"N\00\00\00\00\00\00\00P\05\00\00#\00\00\00" }>, align 8
@alloc_ac94b0f15567a3bff9a57d750dd3b469 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_c10cf53f0ee0f754900a21ca81d4eda2, [16 x i8] c"N\00\00\00\00\00\00\00Q\05\00\00\1D\00\00\00" }>, align 8
@alloc_9c940776993d83a8f12be93d89ec27da = private unnamed_addr constant [29 x i8] c"=== Comments Demo (Rust) ===\0A", align 1
@alloc_e34eb99905e2bb144bb353ec27805dce = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9c940776993d83a8f12be93d89ec27da, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8d4c00e73aaf884197918cdcc0fb0996 = private unnamed_addr constant [40 x i8] c"Single-line comments are marked with //\0A", align 1
@alloc_87cc6b5b2b729ec9d079d553cad80a51 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8d4c00e73aaf884197918cdcc0fb0996, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_ff28c080ba9ff13761e1b18df316cea8 = private unnamed_addr constant [30 x i8] c"Multi-line comments use /* */\0A", align 1
@alloc_dfcbfb69979486380580e63e3b259b40 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ff28c080ba9ff13761e1b18df316cea8, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_b468ef00fa5916b1335583d25c8252ad = private unnamed_addr constant [5 x i8] c"Ahmed", align 1
@alloc_2f1f9ccad4d720ac86e681c40de27f1a = private unnamed_addr constant [18 x i8] c"User information:\0A", align 1
@alloc_2aaa6cfbb39584e321d4f0520cd114ac = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2f1f9ccad4d720ac86e681c40de27f1a, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a = private unnamed_addr constant [6 x i8] c"Name: ", align 1
@alloc_cd99b202573048149e4ac0d7df184409 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aa11b714384947dbd3caef261ef0dc2e = private unnamed_addr constant [5 x i8] c"Age: ", align 1
@alloc_c8d066cadf1ba35bf631428b9f61b62c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aa11b714384947dbd3caef261ef0dc2e, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5fb5ea3f1a2b2f8bfd25c3cbe1f2e205 = private unnamed_addr constant [8 x i8] c"Salary: ", align 1
@alloc_828475e8994c9a2156e0be0dce462ed4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5fb5ea3f1a2b2f8bfd25c3cbe1f2e205, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e1cb722f3e88b54895c365ab133e8b7d = private unnamed_addr constant [14 x i8] c"12_comments.rs", align 1
@alloc_114b365722e71454eef4238a6d3bd7eb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00$\00\00\00\0F\00\00\00" }>, align 8
@alloc_866d7070cf81be649d420795782544b0 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00%\00\00\00\16\00\00\00" }>, align 8
@alloc_0906f7dc9a28da801694102873cb9feb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00&\00\00\00\13\00\00\00" }>, align 8
@alloc_4b26373a2560346ffe2f9e63d6fc6076 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00'\00\00\00\14\00\00\00" }>, align 8
@alloc_934fb1042c8809e05368b3f6fa89dc08 = private unnamed_addr constant [25 x i8] c"Mathematical operations:\0A", align 1
@alloc_c617fe87cce53e57948be4e098b3f9cf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_934fb1042c8809e05368b3f6fa89dc08, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf = private unnamed_addr constant [3 x i8] c" + ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_75b10f0b41c742fe880b0f6947bcc0f9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e2b8ff18cd338c23dd4ea4f16738ab74 = private unnamed_addr constant [3 x i8] c" - ", align 1
@alloc_505609185f08173c2506561fcfd13b85 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_26402fef52f74f1c44c90ec1eef0b4f1 = private unnamed_addr constant [3 x i8] c" * ", align 1
@alloc_0170cf9431e7bc8b96f6d1590e9f77a7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_37d018637e55331d5bf3277fd5414bcf = private unnamed_addr constant [3 x i8] c" / ", align 1
@alloc_fd5119e50c7c3e81e4bb265e08fca1f1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_37d018637e55331d5bf3277fd5414bcf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b75296cdc8822675c0ed686851be1e80 = private unnamed_addr constant [18 x i8] c"Array operations:\0A", align 1
@alloc_5f58f8ffc7ffdf80f12f01c6e973b49f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b75296cdc8822675c0ed686851be1e80, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_3bfe1d77f19cd92ecdfc2aacae78f910 = private unnamed_addr constant [15 x i8] c"First element: ", align 1
@alloc_730290c59e61c25b88204a03dbba8f8e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3bfe1d77f19cd92ecdfc2aacae78f910, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_523985710b7b40b53e296296a4c629d7 = private unnamed_addr constant [14 x i8] c"Last element: ", align 1
@alloc_8fb8157e70b102d0cc050be818399ca1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_523985710b7b40b53e296296a4c629d7, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4d5a280f00d094385ed5e67ed2940c68 = private unnamed_addr constant [14 x i8] c"All elements:\0A", align 1
@alloc_bc32138ead3b39df8991b59e9ca42df3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4d5a280f00d094385ed5e67ed2940c68, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_54ab464787ae0caca5353d93c0b34796 = private unnamed_addr constant [18 x i8] c"Grade: F (Failed)\0A", align 1
@alloc_86c76d745292d3636de6d7979ede66a6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_54ab464787ae0caca5353d93c0b34796, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_aba04d8701b729810f3a4fc70b6d5907 = private unnamed_addr constant [25 x i8] c"Grade: D (Below Average)\0A", align 1
@alloc_07322d9824ec4d35e74eb9033c8d2e87 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_aba04d8701b729810f3a4fc70b6d5907, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_2081804bad2c2286dd0a6e6b4df50e80 = private unnamed_addr constant [19 x i8] c"Grade: C (Average)\0A", align 1
@alloc_3e1dc8319c2e9f7badc1123d1aa42be1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2081804bad2c2286dd0a6e6b4df50e80, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_2726d6c261be86ea796cfd72ab47c014 = private unnamed_addr constant [16 x i8] c"Grade: B (Good)\0A", align 1
@alloc_5700194d0098aef4e3f0ea17d9f81b0f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2726d6c261be86ea796cfd72ab47c014, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_d776c7b9b755466f41a79916424c05af = private unnamed_addr constant [21 x i8] c"Grade: A (Excellent)\0A", align 1
@alloc_56d6c3511debb15c6eed4ca29f926493 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d776c7b9b755466f41a79916424c05af, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_9f02e137ddc81d1e7096ff184733dcad = private unnamed_addr constant [16 x i8] c"Rectangle area: ", align 1
@alloc_2ceb5d572258e5f91d3c3c50f74eeb40 = private unnamed_addr constant [14 x i8] c" square units\0A", align 1
@alloc_2804d0d6d39c2a65bce727b0ddd23f07 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9f02e137ddc81d1e7096ff184733dcad, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_2ceb5d572258e5f91d3c3c50f74eeb40, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_c612765a4a38088d5ebb64bbc5144771 = private unnamed_addr constant [15 x i8] c"Loop examples:\0A", align 1
@alloc_e041f18fa8d6c8ef82d4bcc70d963ca6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c612765a4a38088d5ebb64bbc5144771, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_f5199d266a18c4555b84f279b8698b07 = private unnamed_addr constant [22 x i8] c"Counting from 1 to 5:\0A", align 1
@alloc_81c0284ff39ce0cdb71367c51f0cea33 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f5199d266a18c4555b84f279b8698b07, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_cb8519e75fa2010eb98c6f14eb0575f6 = private unnamed_addr constant [22 x i8] c"While loop countdown:\0A", align 1
@alloc_30662e36b0588b19e27c7bf09c057fa1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cb8519e75fa2010eb98c6f14eb0575f6, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_a10b29fa1aded051447dbb8c90cd1047 = private unnamed_addr constant [6 x i8] c"Hassan", align 1
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_a0434d70b15bcc9ff1a7b717be16ed72 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_cc37ac66d3de0ec310ec5ff51b45c46f = private unnamed_addr constant [19 x i8] c"String operations:\0A", align 1
@alloc_14d01a973725b6c430f6d6d1b8fcf186 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cc37ac66d3de0ec310ec5ff51b45c46f, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_e6a7fc9f329d07fb75a4a6d7b4edcc62 = private unnamed_addr constant [11 x i8] c"Full name: ", align 1
@alloc_e2ba38de3c2433e25c1ef4e475e0ae91 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e6a7fc9f329d07fb75a4a6d7b4edcc62, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7099dfc5bf30ed4602dad14f59e74cfe = private unnamed_addr constant [13 x i8] c"Hello, World!", align 1
@alloc_d438fa9f8cddde3b9ffe300872d155ea = private unnamed_addr constant [7 x i8] c"Text: '", align 1
@alloc_12a9d76f5dbcbafc68e14c1df740ed24 = private unnamed_addr constant [2 x i8] c"'\0A", align 1
@alloc_2079bb73470e11856b0abd402a928650 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d438fa9f8cddde3b9ffe300872d155ea, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_ab2cf61edd6d48f1649f7fd89e529174 = private unnamed_addr constant [8 x i8] c"Length: ", align 1
@alloc_5301d26306a7a7874f2dc8a275fdbe65 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ab2cf61edd6d48f1649f7fd89e529174, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7f02da8fcc61d9d8445a5684c60b4202 = private unnamed_addr constant [18 x i8] c"Contains 'World': ", align 1
@alloc_fddd4bceb3c789185e3334d008fe2223 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7f02da8fcc61d9d8445a5684c60b4202, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7c36acd6e5096800e8610a1984ba6ddd = private unnamed_addr constant [5 x i8] c"World", align 1
@alloc_5135d04ba0ec880755b188a551f56b63 = private unnamed_addr constant [20 x i8] c"Boolean operations:\0A", align 1
@alloc_2ef2a44534689a452eb3d6048a546613 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5135d04ba0ec880755b188a551f56b63, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_f046d6874a85ae50402881e63ee76303 = private unnamed_addr constant [10 x i8] c"Is adult: ", align 1
@alloc_fbe59325d2f56c01006873536ec16a23 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f046d6874a85ae50402881e63ee76303, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_88be2a87dcf07b8d6d8ce67b444666ca = private unnamed_addr constant [12 x i8] c"Has income: ", align 1
@alloc_aa1ad406e09f54625f465fc645fd2dc8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_88be2a87dcf07b8d6d8ce67b444666ca, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_706b3e28c7d7898010873c42be5656e5 = private unnamed_addr constant [13 x i8] c"Is eligible: ", align 1
@alloc_0ed86fe2d083c03bac3f5ae9948cd9e2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_706b3e28c7d7898010873c42be5656e5, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9e012f20ea7c5978c185d7d8978c23c4 = private unnamed_addr constant [31 x i8] c"Compound interest calculation:\0A", align 1
@alloc_2c79ae6afef2713bf7bdf4a91ceb42d4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9e012f20ea7c5978c185d7d8978c23c4, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_40309ba347e1180eabebc4062dce573d = private unnamed_addr constant [12 x i8] c"Principal: $", align 1
@alloc_f3605924ed5959fc81c8e45edc7a92e7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_40309ba347e1180eabebc4062dce573d, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_55c384264ea9014f968616b028a5d8da = private unnamed_addr constant [6 x i8] c"Rate: ", align 1
@alloc_1c66b5fb2c6e357af3588db0097b572a = private unnamed_addr constant [2 x i8] c"%\0A", align 1
@alloc_280cc4bd13691fcf8685661ccf810602 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_55c384264ea9014f968616b028a5d8da, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_1c66b5fb2c6e357af3588db0097b572a, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_d9ebe39abeab7db2c3999bcb5baaf7b1 = private unnamed_addr constant [6 x i8] c"Time: ", align 1
@alloc_f1090a81acfc81b45c6dc1b4df6e1c0d = private unnamed_addr constant [7 x i8] c" years\0A", align 1
@alloc_ab67f7fe1291fe0d5abc97efdf942547 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d9ebe39abeab7db2c3999bcb5baaf7b1, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_f1090a81acfc81b45c6dc1b4df6e1c0d, [8 x i8] c"\07\00\00\00\00\00\00\00" }>, align 8
@alloc_0c3f0dfabac36e3e1688ebb4015acc1f = private unnamed_addr constant [15 x i8] c"Final amount: $", align 1
@alloc_484598a9a2d17a346ada0a4a52f37371 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0c3f0dfabac36e3e1688ebb4015acc1f, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5e4340ef2b23b6a51d08b67f0de8da21 = private unnamed_addr constant [18 x i8] c"Interest earned: $", align 1
@alloc_024462f0deb68215125012e9bae6f974 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5e4340ef2b23b6a51d08b67f0de8da21, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_09278f1ccb334d63f3183c983c88a540 = private unnamed_addr constant [29 x i8] c"=== End of Comments Demo ===\0A", align 1
@alloc_87cbbb52c89333e8d849f3aecffe4859 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09278f1ccb334d63f3183c983c88a540, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_14b4ae4ebe468088e2f8baa3a0abf269 = private unnamed_addr constant [11 x i8] c"  Counter: ", align 1
@alloc_c3ef747d882502a007264a7eed8d1f64 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_14b4ae4ebe468088e2f8baa3a0abf269, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2ff50d90c9886a185b4d1047e9156af3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00o\00\00\00\09\00\00\00" }>, align 8
@alloc_f9b42dd58c395e232d5ea70febfc1690 = private unnamed_addr constant [9 x i8] c"  Count: ", align 1
@alloc_2ba1ec6542c75bf98ebfb61a85a9b057 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9b42dd58c395e232d5ea70febfc1690, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8a4576243b6539938c0417f2d563f046 = private unnamed_addr constant [10 x i8] c"  numbers[", align 1
@alloc_428053e6434889c8e3f16313e3d2a9ba = private unnamed_addr constant [4 x i8] c"] = ", align 1
@alloc_f1f6cfcfa934f782575feca97693e20e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8a4576243b6539938c0417f2d563f046, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_428053e6434889c8e3f16313e3d2a9ba, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1b7075675b896b820b4618344fc982b4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_e1cb722f3e88b54895c365ab133e8b7d, [16 x i8] c"\0E\00\00\00\00\00\00\00\B9\00\00\00\05\00\00\00" }>, align 8

; <core::ops::range::Range<usize> as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17hba97ba0ada474577E"(i64 %start1, i64 %end, i64 %len) unnamed_addr #0 {
start:
  %_4 = icmp uge i64 %end, %start1
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb1, %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_7e0cd81f9dcb179626435c932d0b5a52, i64 214) #18
  unreachable

bb1:                                              ; preds = %start
  %_5 = icmp ule i64 %end, %len
  br i1 %_5, label %bb2, label %bb3

bb2:                                              ; preds = %bb1
  ret void
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17ha95a14a4f1b9db44E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hb553f54c1bc7b320E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hbb728d0a8b28748cE"(ptr align 8 %self) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %self1 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h9217bc7b5ffadb4bE"(ptr align 8 %self)
  store ptr %0, ptr %self1, align 8
  %1 = load ptr, ptr %self1, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_11 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_11 to i1
  br i1 %4, label %bb8, label %bb7

bb8:                                              ; preds = %start
  %v = load ptr, ptr %self1, align 8
  store ptr %v, ptr %_3, align 8
  %val = load ptr, ptr %_3, align 8
  %5 = getelementptr inbounds i8, ptr %self, i64 16
  %i = load i64, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  %7 = load i64, ptr %6, align 8
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %7, i64 1)
  %_8.0 = extractvalue { i64, i1 } %8, 0
  %_8.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_8.1, label %panic, label %bb3

bb7:                                              ; preds = %start
  %9 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr null, ptr %9, align 8
  br label %bb4

bb4:                                              ; preds = %bb3, %bb7
  %10 = load i64, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  %12 = load ptr, ptr %11, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %10, 0
  %14 = insertvalue { i64, ptr } %13, ptr %12, 1
  ret { i64, ptr } %14

bb3:                                              ; preds = %bb8
  %15 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %_8.0, ptr %15, align 8
  store i64 %i, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %val, ptr %16, align 8
  br label %bb4

panic:                                            ; preds = %bb8
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_67987f2acd65c96a8c883ff2ecae97af) #19
          to label %unreachable unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind to caller

funclet_bb5:                                      ; preds = %panic
  %cleanuppad = cleanuppad within none []
  br label %bb5

unreachable:                                      ; preds = %panic
  unreachable

bb2:                                              ; No predecessors!
  unreachable
}

; <core::ops::range::RangeFrom<usize> as core::slice::index::SliceIndex<[T]>>::index
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN110_$LT$core..ops..range..RangeFrom$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hdeeb06dd3690dc0bE"(i64 %self, ptr align 1 %slice.0, i64 %slice.1, ptr align 8 %0) unnamed_addr #1 {
start:
  %_3 = icmp ugt i64 %self, %slice.1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  br label %bb3

bb1:                                              ; preds = %start
; call core::slice::index::slice_start_index_len_fail
  call void @_ZN4core5slice5index26slice_start_index_len_fail17hc7aabe8c812252f8E(i64 %self, i64 %slice.1, ptr align 8 %0) #19
  unreachable

bb3:                                              ; preds = %bb2
; call <core::ops::range::Range<usize> as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17hba97ba0ada474577E"(i64 %self, i64 %slice.1, i64 %slice.1) #20
  br label %bb4

bb4:                                              ; preds = %bb3
  %new_len = sub nuw i64 %slice.1, %self
  %_12 = getelementptr inbounds nuw i8, ptr %slice.0, i64 %self
  %1 = insertvalue { ptr, i64 } poison, ptr %_12, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %new_len, 1
  ret { ptr, i64 } %2
}

; <core::iter::adapters::zip::Zip<A,B> as core::iter::adapters::zip::ZipImpl<A,B>>::new
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$core..iter..adapters..zip..Zip$LT$A$C$B$GT$$u20$as$u20$core..iter..adapters..zip..ZipImpl$LT$A$C$B$GT$$GT$3new17ha26505436e2ca055E"(ptr sret([56 x i8]) align 8 %_0, ptr %0, ptr %1, ptr %2, ptr %3) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %b = alloca [16 x i8], align 8
  %a = alloca [16 x i8], align 8
  store ptr %0, ptr %a, align 8
  %4 = getelementptr inbounds i8, ptr %a, i64 8
  store ptr %1, ptr %4, align 8
  store ptr %2, ptr %b, align 8
  %5 = getelementptr inbounds i8, ptr %b, i64 8
  store ptr %3, ptr %5, align 8
; invoke core::iter::adapters::zip::TrustedRandomAccessNoCoerce::size
  %a_len = invoke i64 @_ZN4core4iter8adapters3zip27TrustedRandomAccessNoCoerce4size17h2511ccbe9624bb78E(ptr align 8 %a)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %bb2, %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
; invoke core::iter::adapters::zip::TrustedRandomAccessNoCoerce::size
  %v2 = invoke i64 @_ZN4core4iter8adapters3zip27TrustedRandomAccessNoCoerce4size17h2511ccbe9624bb78E(ptr align 8 %b)
          to label %bb2 unwind label %funclet_bb3

bb2:                                              ; preds = %bb1
; invoke core::cmp::Ord::min
  %len = invoke i64 @_ZN4core3cmp3Ord3min17h7657b4296391d1f3E(i64 %a_len, i64 %v2)
          to label %bb6 unwind label %funclet_bb3

bb6:                                              ; preds = %bb2
  %_8.0 = load ptr, ptr %a, align 8
  %6 = getelementptr inbounds i8, ptr %a, i64 8
  %_8.1 = load ptr, ptr %6, align 8
  %_9.0 = load ptr, ptr %b, align 8
  %7 = getelementptr inbounds i8, ptr %b, i64 8
  %_9.1 = load ptr, ptr %7, align 8
  store ptr %_8.0, ptr %_0, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %_8.1, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %_9.0, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %9, i64 8
  store ptr %_9.1, ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 0, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 40
  store i64 %len, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_0, i64 48
  store i64 %a_len, ptr %13, align 8
  ret void
}

; <core::iter::adapters::zip::Zip<A,B> as core::iter::adapters::zip::ZipImpl<A,B>>::next
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN111_$LT$core..iter..adapters..zip..Zip$LT$A$C$B$GT$$u20$as$u20$core..iter..adapters..zip..ZipImpl$LT$A$C$B$GT$$GT$4next17h17d502eef7beb895E"(ptr align 8 %self) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  %_3 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 40
  %_4 = load i64, ptr %1, align 8
  %_2 = icmp ult i64 %_3, %_4
  br i1 %_2, label %bb1, label %bb4

bb4:                                              ; preds = %start
  br label %bb10

bb1:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %self, i64 32
  %i = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 32
  %4 = getelementptr inbounds i8, ptr %self, i64 32
  %5 = load i64, ptr %4, align 8
  %6 = add i64 %5, 1
  store i64 %6, ptr %3, align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::__iterator_get_unchecked
  %_7 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$24__iterator_get_unchecked17h91f48be79c413c22E"(ptr align 8 %self, i64 %i)
  %_10 = getelementptr inbounds i8, ptr %self, i64 16
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::__iterator_get_unchecked
  %_9 = invoke align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$24__iterator_get_unchecked17h91f48be79c413c22E"(ptr align 8 %_10, i64 %i)
          to label %bb3 unwind label %funclet_bb13

bb10:                                             ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb11

bb11:                                             ; preds = %bb10
  br label %bb12

bb12:                                             ; preds = %bb3, %bb11
  %7 = load ptr, ptr %_0, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  %9 = load ptr, ptr %8, align 8
  %10 = insertvalue { ptr, ptr } poison, ptr %7, 0
  %11 = insertvalue { ptr, ptr } %10, ptr %9, 1
  ret { ptr, ptr } %11

bb13:                                             ; preds = %funclet_bb13
  cleanupret from %cleanuppad unwind to caller

funclet_bb13:                                     ; preds = %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb13

bb3:                                              ; preds = %bb1
  store ptr %_7, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %_9, ptr %12, align 8
  br label %bb12

bb5:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable

bb7:                                              ; No predecessors!
  unreachable

bb8:                                              ; No predecessors!
  unreachable

bb9:                                              ; No predecessors!
  unreachable
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17h6f76164e010a751cE"() unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  store i8 1, ptr %_0, align 1
  %0 = load i8, ptr %_0, align 1
  %1 = trunc nuw i8 %0 to i1
  ret i1 %1
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17hfe5976fe6c5a8973E"(i64 %0) unnamed_addr #1 {
start:
  %_0 = alloca [16 x i8], align 8
  %residual = alloca [8 x i8], align 8
  store i64 %0, ptr %residual, align 8
  %_2 = load i64, ptr %residual, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_2, ptr %1, align 8
  store i64 1, ptr %_0, align 8
  %2 = load i64, ptr %_0, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = insertvalue { i64, i64 } poison, i64 %2, 0
  %6 = insertvalue { i64, i64 } %5, i64 %4, 1
  ret { i64, i64 } %6
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h0c1d30d854da2e0bE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #2 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf71c0a080a4baa4cE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h881084b905af28deE(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hccb9f7d5a3f43b78E"()
  ret i32 %self
}

; std::f64::<impl f64>::powf
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17h35c51cd84474d0a0E"(double %self, double %n) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.pow.f64(double %self, double %n)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h881084b905af28deE(ptr %f) unnamed_addr #3 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h51a02ee90d4e50bbE(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h20822639e97afc2eE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #2 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17hcf230d7033da7ea5E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #2 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hb553f54c1bc7b320E"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.e9ee036f1d998da69b02774f266e1b43.0, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.0, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h9eae9061f7a95b34E() #20
  unreachable
}

; <usize as core::iter::range::Step>::backward_unchecked
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN49_$LT$usize$u20$as$u20$core..iter..range..Step$GT$18backward_unchecked17h1e3d437d39c386e9E"(i64 %start1, i64 %n) unnamed_addr #1 {
start:
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl usize>::unchecked_sub::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_sub18precondition_check17hb7209f147b98244eE"(i64 %start1, i64 %n) #20
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = sub nuw i64 %start1, %n
  ret i64 %_0
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h582ff1bb5db590feE(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #21 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #18
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17h563d084221e01e3aE() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::Ord::max
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core3cmp3Ord3max17h6f2a6a8eb8d692e5E(i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %other = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  store i64 %0, ptr %self, align 8
  store i64 %1, ptr %other, align 8
  store i8 1, ptr %_6, align 1
  %_3.i = load i64, ptr %other, align 8
  %_4.i = load i64, ptr %self, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load i64, ptr %other, align 8
  store i64 %2, ptr %_0, align 8
  %3 = load i8, ptr %_6, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %5 = load i64, ptr %self, align 8
  store i64 %5, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load i64, ptr %_0, align 8
  ret i64 %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::cmp::Ord::min
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core3cmp3Ord3min17h7657b4296391d1f3E(i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %other = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  store i64 %0, ptr %self, align 8
  store i64 %1, ptr %other, align 8
  store i8 1, ptr %_6, align 1
  %_3.i = load i64, ptr %other, align 8
  %_4.i = load i64, ptr %self, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %2 = load i64, ptr %self, align 8
  store i64 %2, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb1
  %3 = load i64, ptr %other, align 8
  store i64 %3, ptr %_0, align 8
  %4 = load i8, ptr %_6, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb4

bb4:                                              ; preds = %bb7, %bb2, %bb3
  %6 = load i64, ptr %_0, align 8
  ret i64 %6

bb7:                                              ; preds = %bb2
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::cmp::impls::<impl core::cmp::PartialEq for ()>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls59_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$$LP$$RP$$GT$2eq17h6e2aebfa362c6b51E"(ptr align 1 %self, ptr align 1 %_other) unnamed_addr #1 {
start:
  ret i1 true
}

; core::fmt::rt::Placeholder::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_0, i64 %position, i32 %flags, ptr align 8 %precision, ptr align 8 %width) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 %position, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 40
  store i32 %flags, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %precision, i64 16, i1 false)
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %width, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h0aef883fdb288095E(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h2d435e4fb848148eE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17hcf230d7033da7ea5E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h7fde9e90f267252eE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h20822639e97afc2eE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h95a43ed099c205cbE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hc3803372caf5fa16E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17ha64df5f042808efbE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::UnsafeArg::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E() unnamed_addr #1 {
start:
  ret void
}

; core::fmt::Arguments::new_v1_formatted
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces.0, i64 %pieces.1, ptr align 8 %args.0, i64 %args.1, ptr align 8 %fmt.0, i64 %fmt.1) unnamed_addr #1 {
start:
  %_5 = alloca [16 x i8], align 8
  store ptr %fmt.0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %fmt.1, ptr %0, align 8
  store ptr %pieces.0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pieces.1, ptr %1, align 8
  %2 = load ptr, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args.0, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  store i64 %args.1, ptr %8, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h63dfc173c89a2e2cE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h7a570e7703b14cd0E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hb89f567ae42befd5E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h9308eeef7bdc30a2E"(i64 %lhs, i64 %rhs) unnamed_addr #0 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #18
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h5565109c55262212E"(i64 %lhs, i64 %rhs) unnamed_addr #0 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #18
  unreachable
}

; core::num::<impl usize>::unchecked_sub::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_sub18precondition_check17hb7209f147b98244eE"(i64 %lhs, i64 %rhs) unnamed_addr #0 {
start:
  %_5.0 = sub i64 %lhs, %rhs
  %_5.1 = icmp ult i64 %lhs, %rhs
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_846c8306b730cf00804b037ffdb61170, i64 186) #18
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h45baeeda5b16936cE"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #1 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h91104e2ed3d9b4f7E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h0d660f0b1ba61687E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h0d660f0b1ba61687E(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hf71c0a080a4baa4cE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h51a02ee90d4e50bbE(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17hd711498901682c7fE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load i64, ptr %4, align 8
; call alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
  call void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h425057862889e85eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %3, i64 %5)
  ret void
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17h8999dcff0e84df57E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #18
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #21 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::read_unaligned
; Function Attrs: inlinehint uwtable
define internal <16 x i8> @_ZN4core3ptr14read_unaligned17h3def2c5b182a2c6aE(ptr %src) unnamed_addr #1 {
start:
  %tmp = alloca [16 x i8], align 16
  br label %bb1

bb1:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %src, ptr %tmp, i64 1, i64 1, i64 16) #20
  br label %bb3

bb3:                                              ; preds = %bb1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %tmp, ptr align 1 %src, i64 16, i1 false)
  %self = load <16 x i8>, ptr %tmp, align 16
  ret <16 x i8> %self
}

; core::ptr::read_unaligned
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ptr14read_unaligned17h6401ad35ba225740E(ptr %src) unnamed_addr #1 {
start:
  %tmp = alloca [4 x i8], align 4
  br label %bb1

bb1:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %src, ptr %tmp, i64 1, i64 1, i64 4) #20
  br label %bb3

bb3:                                              ; preds = %bb1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %tmp, ptr align 1 %src, i64 4, i1 false)
  %self = load i32, ptr %tmp, align 4
  ret i32 %self
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h1fdd9146890aa38fE"(ptr align 8 %_1) unnamed_addr #2 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hb3ba2b4bf63cc4dfE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hb3ba2b4bf63cc4dfE"(ptr align 8 %_1) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9e04f286c389dc9fE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h31a37265436af94dE"(ptr align 8 %_1) #22 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h31a37265436af94dE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h31a37265436af94dE"(ptr align 8 %_1) unnamed_addr #2 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h7845d2abc342d103E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h0e5a1d74fa69e887E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h58dce6c9e864df72E"(ptr %ptr) unnamed_addr #0 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h6777e3b08ffba055E"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h3fb82e4dc9bc0fabE"(ptr %self, ptr %subtracted) #20
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 1
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #19
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h3fb82e4dc9bc0fabE"(ptr %this, ptr %origin) unnamed_addr #0 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #18
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::str::validations::next_code_point
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @_ZN4core3str11validations15next_code_point17he658d20941288e12E(ptr align 8 %bytes) unnamed_addr #1 {
start:
  %self3 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %ch = alloca [4 x i8], align 4
  %self1 = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 4
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfbd6bab7cec622e7E"(ptr align 8 %bytes)
  store ptr %0, ptr %self, align 8
  %1 = load ptr, ptr %self, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_29 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_29 to i1
  br i1 %4, label %bb14, label %bb13

bb14:                                             ; preds = %start
  %v = load ptr, ptr %self, align 8
  store ptr %v, ptr %_3, align 8
  %val = load ptr, ptr %_3, align 8
  %x = load i8, ptr %val, align 1
  %_6 = icmp ult i8 %x, -128
  br i1 %_6, label %bb3, label %bb4

bb13:                                             ; preds = %start
  %5 = load i32, ptr @anon.e9ee036f1d998da69b02774f266e1b43.0, align 4
  %6 = load i32, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.0, i64 4), align 4
  store i32 %5, ptr %_0, align 4
  %7 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %6, ptr %7, align 4
  br label %bb12

bb12:                                             ; preds = %bb3, %bb11, %bb13
  %8 = load i32, ptr %_0, align 4
  %9 = getelementptr inbounds i8, ptr %_0, i64 4
  %10 = load i32, ptr %9, align 4
  %11 = insertvalue { i32, i32 } poison, i32 %8, 0
  %12 = insertvalue { i32, i32 } %11, i32 %10, 1
  ret { i32, i32 } %12

bb4:                                              ; preds = %bb14
  %_31 = and i8 %x, 31
  %init = zext i8 %_31 to i32
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %13 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfbd6bab7cec622e7E"(ptr align 8 %bytes)
  store ptr %13, ptr %self1, align 8
  %14 = load ptr, ptr %self1, align 8
  %15 = ptrtoint ptr %14 to i64
  %16 = icmp eq i64 %15, 0
  %_32 = select i1 %16, i64 0, i64 1
  %17 = trunc nuw i64 %_32 to i1
  br i1 %17, label %bb16, label %bb15

bb3:                                              ; preds = %bb14
  %_7 = zext i8 %x to i32
  %18 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_7, ptr %18, align 4
  store i32 1, ptr %_0, align 4
  br label %bb12

bb16:                                             ; preds = %bb4
  %val4 = load ptr, ptr %self1, align 8
  %y = load i8, ptr %val4, align 1
  %_35 = shl i32 %init, 6
  %_37 = and i8 %y, 63
  %_36 = zext i8 %_37 to i32
  %19 = or i32 %_35, %_36
  store i32 %19, ptr %ch, align 4
  %_13 = icmp uge i8 %x, -32
  br i1 %_13, label %bb6, label %bb11

bb15:                                             ; preds = %bb4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h9eae9061f7a95b34E() #20
  br label %bb2

bb2:                                              ; preds = %bb19, %bb17, %bb15
  unreachable

bb11:                                             ; preds = %bb10, %bb16
  %_28 = load i32, ptr %ch, align 4
  %20 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_28, ptr %20, align 4
  store i32 1, ptr %_0, align 4
  br label %bb12

bb6:                                              ; preds = %bb16
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %21 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfbd6bab7cec622e7E"(ptr align 8 %bytes)
  store ptr %21, ptr %self2, align 8
  %22 = load ptr, ptr %self2, align 8
  %23 = ptrtoint ptr %22 to i64
  %24 = icmp eq i64 %23, 0
  %_38 = select i1 %24, i64 0, i64 1
  %25 = trunc nuw i64 %_38 to i1
  br i1 %25, label %bb18, label %bb17

bb18:                                             ; preds = %bb6
  %val5 = load ptr, ptr %self2, align 8
  %z = load i8, ptr %val5, align 1
  %_19 = and i8 %y, 63
  %ch6 = zext i8 %_19 to i32
  %_41 = shl i32 %ch6, 6
  %_43 = and i8 %z, 63
  %_42 = zext i8 %_43 to i32
  %y_z = or i32 %_41, %_42
  %_20 = shl i32 %init, 12
  %26 = or i32 %_20, %y_z
  store i32 %26, ptr %ch, align 4
  %_21 = icmp uge i8 %x, -16
  br i1 %_21, label %bb8, label %bb10

bb17:                                             ; preds = %bb6
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h9eae9061f7a95b34E() #20
  br label %bb2

bb10:                                             ; preds = %bb20, %bb18
  br label %bb11

bb8:                                              ; preds = %bb18
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %27 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfbd6bab7cec622e7E"(ptr align 8 %bytes)
  store ptr %27, ptr %self3, align 8
  %28 = load ptr, ptr %self3, align 8
  %29 = ptrtoint ptr %28 to i64
  %30 = icmp eq i64 %29, 0
  %_44 = select i1 %30, i64 0, i64 1
  %31 = trunc nuw i64 %_44 to i1
  br i1 %31, label %bb20, label %bb19

bb20:                                             ; preds = %bb8
  %val7 = load ptr, ptr %self3, align 8
  %w = load i8, ptr %val7, align 1
  %_26 = and i32 %init, 7
  %_25 = shl i32 %_26, 18
  %_47 = shl i32 %y_z, 6
  %_49 = and i8 %w, 63
  %_48 = zext i8 %_49 to i32
  %_27 = or i32 %_47, %_48
  %32 = or i32 %_25, %_27
  store i32 %32, ptr %ch, align 4
  br label %bb10

bb19:                                             ; preds = %bb8
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h9eae9061f7a95b34E() #20
  br label %bb2
}

; core::str::<impl str>::len
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3str21_$LT$impl$u20$str$GT$3len17h594821fd03921d22E"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  ret i64 %self.1
}

; core::str::<impl str>::contains
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str21_$LT$impl$u20$str$GT$8contains17hb958b3b8cde887c5E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %pat.0, i64 %pat.1) unnamed_addr #1 {
start:
; call <&str as core::str::pattern::Pattern>::is_contained_in
  %_0 = call zeroext i1 @"_ZN55_$LT$$RF$str$u20$as$u20$core..str..pattern..Pattern$GT$15is_contained_in17h14a198d9036d5031E"(ptr align 1 %pat.0, i64 %pat.1, ptr align 1 %self.0, i64 %self.1)
  ret i1 %_0
}

; core::str::traits::<impl core::slice::index::SliceIndex<str> for core::ops::range::Range<usize>>::get_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3str6traits108_$LT$impl$u20$core..slice..index..SliceIndex$LT$str$GT$$u20$for$u20$core..ops..range..Range$LT$usize$GT$$GT$13get_unchecked18precondition_check17h200533f9aca55651E"(i64 %start1, i64 %end, i64 %len) unnamed_addr #0 {
start:
  %_4 = icmp uge i64 %end, %start1
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb1, %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_91f2a00ff2cd9cdc4bb205a66832e2bb, i64 219) #18
  unreachable

bb1:                                              ; preds = %start
  %_5 = icmp ule i64 %end, %len
  br i1 %_5, label %bb2, label %bb3

bb2:                                              ; preds = %bb1
  ret void
}

; core::str::traits::<impl core::slice::index::SliceIndex<str> for core::ops::range::RangeFrom<usize>>::get
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN4core3str6traits112_$LT$impl$u20$core..slice..index..SliceIndex$LT$str$GT$$u20$for$u20$core..ops..range..RangeFrom$LT$usize$GT$$GT$3get17h791804cc555cad26E"(i64 %self, ptr align 1 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %_3 = alloca [1 x i8], align 1
  %_0 = alloca [16 x i8], align 8
  %0 = icmp eq i64 %self, 0
  br i1 %0, label %bb4, label %bb5

bb4:                                              ; preds = %start
  br label %bb1

bb5:                                              ; preds = %start
  %_8 = icmp uge i64 %self, %slice.1
  br i1 %_8, label %bb6, label %bb7

bb1:                                              ; preds = %bb9, %bb4
  br label %bb10

bb7:                                              ; preds = %bb5
  %_11 = icmp ult i64 %self, %slice.1
  br i1 %_11, label %bb8, label %panic

bb6:                                              ; preds = %bb5
  %1 = icmp eq i64 %self, %slice.1
  %2 = zext i1 %1 to i8
  store i8 %2, ptr %_3, align 1
  br label %bb9

bb8:                                              ; preds = %bb7
  %3 = getelementptr inbounds nuw i8, ptr %slice.0, i64 %self
  %self1 = load i8, ptr %3, align 1
  %4 = icmp sge i8 %self1, -64
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_3, align 1
  br label %bb9

panic:                                            ; preds = %bb7
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %self, i64 %slice.1, ptr align 8 @alloc_7afbf74ed710c75f5dddd72ec933c2e5) #19
  unreachable

bb9:                                              ; preds = %bb6, %bb8
  %6 = load i8, ptr %_3, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb1, label %bb2

bb2:                                              ; preds = %bb9
  %8 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store ptr %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb3

bb3:                                              ; preds = %bb11, %bb2
  %11 = load ptr, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { ptr, i64 } poison, ptr %11, 0
  %15 = insertvalue { ptr, i64 } %14, i64 %13, 1
  ret { ptr, i64 } %15

bb10:                                             ; preds = %bb1
; call core::str::traits::<impl core::slice::index::SliceIndex<str> for core::ops::range::Range<usize>>::get_unchecked::precondition_check
  call void @"_ZN4core3str6traits108_$LT$impl$u20$core..slice..index..SliceIndex$LT$str$GT$$u20$for$u20$core..ops..range..Range$LT$usize$GT$$GT$13get_unchecked18precondition_check17h200533f9aca55651E"(i64 %self, i64 %slice.1, i64 %slice.1) #20
  br label %bb11

bb11:                                             ; preds = %bb10
  %new_len = sub nuw i64 %slice.1, %self
  %data = getelementptr inbounds nuw i8, ptr %slice.0, i64 %self
  store ptr %data, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_len, ptr %16, align 8
  br label %bb3
}

; core::str::pattern::simd_contains
; Function Attrs: inlinehint uwtable
define internal i8 @_ZN4core3str7pattern13simd_contains17h54b071f46a772999E(ptr align 1 %needle.0, i64 %needle.1, ptr align 1 %haystack.0, i64 %haystack.1) unnamed_addr #1 {
start:
  %0 = alloca [1 x i8], align 1
  %1 = alloca [1 x i8], align 1
  %2 = alloca [8 x i8], align 8
  %array6 = alloca [1 x i8], align 1
  %array5 = alloca [1 x i8], align 1
  %array4 = alloca [1 x i8], align 1
  %array = alloca [1 x i8], align 1
  %_104 = alloca [1 x i8], align 1
  %self3 = alloca [8 x i8], align 8
  %_96 = alloca [24 x i8], align 8
  %self = alloca [16 x i8], align 8
  %_92 = alloca [16 x i8], align 8
  %_82 = alloca [16 x i8], align 8
  %_66 = alloca [16 x i8], align 8
  %_58 = alloca [16 x i8], align 8
  %iter2 = alloca [16 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %iter = alloca [16 x i8], align 8
  %masks = alloca [8 x i8], align 2
  %result = alloca [1 x i8], align 1
  %i = alloca [8 x i8], align 8
  %test_chunk = alloca [40 x i8], align 8
  %check_mask = alloca [32 x i8], align 8
  %second_probe = alloca [16 x i8], align 16
  %first_probe1 = alloca [16 x i8], align 16
  %_23 = alloca [24 x i8], align 8
  %predicate = alloca [24 x i8], align 8
  %_13 = alloca [16 x i8], align 8
  %_11 = alloca [16 x i8], align 8
  %second_probe_offset = alloca [8 x i8], align 8
  %first_probe = alloca [1 x i8], align 1
  %needle = alloca [16 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  store ptr %needle.0, ptr %needle, align 8
  %3 = getelementptr inbounds i8, ptr %needle, i64 8
  store i64 %needle.1, ptr %3, align 8
  %4 = load ptr, ptr %needle, align 8
  %5 = getelementptr inbounds i8, ptr %needle, i64 8
  %self7 = load i64, ptr %5, align 8
  %_7 = icmp ult i64 0, %self7
  br i1 %_7, label %bb1, label %panic

bb1:                                              ; preds = %start
  %6 = load ptr, ptr %needle, align 8
  %7 = getelementptr inbounds i8, ptr %needle, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = getelementptr inbounds nuw i8, ptr %6, i64 0
  %10 = load i8, ptr %9, align 1
  store i8 %10, ptr %first_probe, align 1
  %last_byte_offset = sub i64 %self7, 1
  %11 = icmp eq i64 %self7, 2
  br i1 %11, label %bb2, label %bb3

panic:                                            ; preds = %start
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 0, i64 %self7, ptr align 8 @alloc_94daefe3bc4de5a6c4a5a8e87168a971) #19
  unreachable

bb2:                                              ; preds = %bb1
  store i64 1, ptr %second_probe_offset, align 8
  br label %bb6

bb3:                                              ; preds = %bb1
  %12 = call i64 @llvm.usub.sat.i64(i64 %self7, i64 4)
  store i64 %12, ptr %2, align 8
  %_14 = load i64, ptr %2, align 8
  store i64 %_14, ptr %_13, align 8
  %13 = getelementptr inbounds i8, ptr %_13, i64 8
  store i64 %self7, ptr %13, align 8
  %14 = load ptr, ptr %needle, align 8
  %15 = getelementptr inbounds i8, ptr %needle, i64 8
  %16 = load i64, ptr %15, align 8
  store ptr %14, ptr %predicate, align 8
  %17 = getelementptr inbounds i8, ptr %predicate, i64 8
  store i64 %16, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %predicate, i64 16
  store ptr %first_probe, ptr %18, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_96, ptr align 8 %predicate, i64 24, i1 false)
; call core::iter::traits::double_ended::DoubleEndedIterator::try_rfold
  %19 = call { i64, i64 } @_ZN4core4iter6traits12double_ended19DoubleEndedIterator9try_rfold17h5ef4f9bf880ffaecE(ptr align 8 %_13, ptr align 8 %_96)
  %20 = extractvalue { i64, i64 } %19, 0
  %21 = extractvalue { i64, i64 } %19, 1
  store i64 %20, ptr %self, align 8
  %22 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %21, ptr %22, align 8
  %_97 = load i64, ptr %self, align 8
  %23 = getelementptr inbounds i8, ptr %self, i64 8
  %24 = load i64, ptr %23, align 8
  %25 = trunc nuw i64 %_97 to i1
  br i1 %25, label %bb45, label %bb46

bb6:                                              ; preds = %bb4, %bb2
  %_20 = add i64 16, %last_byte_offset
  %_18 = icmp ult i64 %haystack.1, %_20
  br i1 %_18, label %bb7, label %bb8

bb45:                                             ; preds = %bb3
  %26 = getelementptr inbounds i8, ptr %self, i64 8
  %x = load i64, ptr %26, align 8
  %27 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 %x, ptr %27, align 8
  store i64 1, ptr %_11, align 8
  br label %bb47

bb46:                                             ; preds = %bb3
  %28 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %29 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %28, ptr %_11, align 8
  %30 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 %29, ptr %30, align 8
  br label %bb47

bb47:                                             ; preds = %bb45, %bb46
  %_17 = load i64, ptr %_11, align 8
  %31 = getelementptr inbounds i8, ptr %_11, i64 8
  %32 = load i64, ptr %31, align 8
  %33 = trunc nuw i64 %_17 to i1
  br i1 %33, label %bb4, label %bb5

bb4:                                              ; preds = %bb47
  %34 = getelementptr inbounds i8, ptr %_11, i64 8
  %second_probe_offset8 = load i64, ptr %34, align 8
  store i64 %second_probe_offset8, ptr %second_probe_offset, align 8
  br label %bb6

bb5:                                              ; preds = %bb47
  store i8 2, ptr %_0, align 1
  br label %bb41

bb8:                                              ; preds = %bb6
  %35 = load i8, ptr %first_probe, align 1
  %36 = getelementptr inbounds nuw i8, ptr %array, i64 0
  store i8 %35, ptr %36, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array4, ptr align 1 %array, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h37cc6d4fb7dd7cc4E"(ptr sret([1 x i8]) align 1 %1, ptr %array4)
  %vector = load <1 x i8>, ptr %1, align 1
  %37 = shufflevector <1 x i8> %vector, <1 x i8> %vector, <16 x i32> zeroinitializer
  store <16 x i8> %37, ptr %first_probe1, align 16
  %_29 = load i64, ptr %second_probe_offset, align 8
  %_30 = icmp ult i64 %_29, %self7
  br i1 %_30, label %bb9, label %panic9

bb7:                                              ; preds = %bb6
  store i64 %self7, ptr %self3, align 8
  %38 = load i64, ptr %self3, align 8
  %39 = icmp eq i64 %38, 0
  %_101 = select i1 %39, i64 0, i64 1
  %40 = trunc nuw i64 %_101 to i1
  br i1 %40, label %bb49, label %bb48

bb9:                                              ; preds = %bb8
  %41 = load ptr, ptr %needle, align 8
  %42 = getelementptr inbounds i8, ptr %needle, i64 8
  %43 = load i64, ptr %42, align 8
  %44 = getelementptr inbounds nuw i8, ptr %41, i64 %_29
  %value = load i8, ptr %44, align 1
  %45 = getelementptr inbounds nuw i8, ptr %array5, i64 0
  store i8 %value, ptr %45, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array6, ptr align 1 %array5, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h37cc6d4fb7dd7cc4E"(ptr sret([1 x i8]) align 1 %0, ptr %array6)
  %vector10 = load <1 x i8>, ptr %0, align 1
  %46 = shufflevector <1 x i8> %vector10, <1 x i8> %vector10, <16 x i32> zeroinitializer
  store <16 x i8> %46, ptr %second_probe, align 16
  %47 = load ptr, ptr %needle, align 8
  %48 = getelementptr inbounds i8, ptr %needle, i64 8
  %49 = load i64, ptr %48, align 8
; call <core::ops::range::RangeFrom<usize> as core::slice::index::SliceIndex<[T]>>::index
  %50 = call { ptr, i64 } @"_ZN110_$LT$core..ops..range..RangeFrom$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hdeeb06dd3690dc0bE"(i64 1, ptr align 1 %47, i64 %49, ptr align 8 @alloc_1399bee46b96c7f2f59a90d1c4d86e8d)
  %trimmed_needle.0 = extractvalue { ptr, i64 } %50, 0
  %trimmed_needle.1 = extractvalue { ptr, i64 } %50, 1
  store ptr %haystack.0, ptr %check_mask, align 8
  %51 = getelementptr inbounds i8, ptr %check_mask, i64 8
  store i64 %haystack.1, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %check_mask, i64 16
  store ptr %trimmed_needle.0, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %52, i64 8
  store i64 %trimmed_needle.1, ptr %53, align 8
  store ptr %haystack.0, ptr %test_chunk, align 8
  %54 = getelementptr inbounds i8, ptr %test_chunk, i64 8
  store i64 %haystack.1, ptr %54, align 8
  %55 = getelementptr inbounds i8, ptr %test_chunk, i64 16
  store ptr %second_probe_offset, ptr %55, align 8
  %56 = getelementptr inbounds i8, ptr %test_chunk, i64 24
  store ptr %first_probe1, ptr %56, align 8
  %57 = getelementptr inbounds i8, ptr %test_chunk, i64 32
  store ptr %second_probe, ptr %57, align 8
  store i64 0, ptr %i, align 8
  store i8 0, ptr %result, align 1
  br label %bb10

panic9:                                           ; preds = %bb8
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_29, i64 %self7, ptr align 8 @alloc_98442059e4852e99c56931d49fa198af) #19
  unreachable

bb10:                                             ; preds = %bb21, %bb9
  %_42 = load i64, ptr %i, align 8
  %_41 = add i64 %_42, %last_byte_offset
  %_40 = add i64 %_41, 64
  %_39 = icmp ult i64 %_40, %haystack.1
  br i1 %_39, label %bb11, label %bb26

bb26:                                             ; preds = %bb10
  br label %bb27

bb11:                                             ; preds = %bb10
  %58 = load i8, ptr %result, align 1
  %_43 = trunc nuw i8 %58 to i1
  br i1 %_43, label %bb27, label %bb12

bb27:                                             ; preds = %bb11, %bb26
  br label %bb28

bb12:                                             ; preds = %bb11
  call void @llvm.memset.p0.i64(ptr align 2 %masks, i8 0, i64 8, i1 false)
  store i64 0, ptr %iter, align 8
  %59 = getelementptr inbounds i8, ptr %iter, i64 8
  store i64 4, ptr %59, align 8
  br label %bb13

bb13:                                             ; preds = %bb18, %bb12
  %other = getelementptr inbounds i8, ptr %iter, i64 8
  %_119 = load i64, ptr %iter, align 8
  %60 = getelementptr inbounds i8, ptr %iter, i64 8
  %_120 = load i64, ptr %60, align 8
  %_114 = icmp ult i64 %_119, %_120
  br i1 %_114, label %bb57, label %bb58

bb58:                                             ; preds = %bb13
  %61 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %62 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %61, ptr %_46, align 8
  %63 = getelementptr inbounds i8, ptr %_46, i64 8
  store i64 %62, ptr %63, align 8
  br label %bb59

bb57:                                             ; preds = %bb13
  %old = load i64, ptr %iter, align 8
  br label %bb60

bb59:                                             ; preds = %bb61, %bb58
  %_48 = load i64, ptr %_46, align 8
  %64 = getelementptr inbounds i8, ptr %_46, i64 8
  %65 = load i64, ptr %64, align 8
  %66 = trunc nuw i64 %_48 to i1
  br i1 %66, label %bb15, label %bb16

bb60:                                             ; preds = %bb57
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h9308eeef7bdc30a2E"(i64 %old, i64 1) #20
  br label %bb61

bb61:                                             ; preds = %bb60
  %_118 = add nuw i64 %old, 1
  store i64 %_118, ptr %iter, align 8
  %67 = getelementptr inbounds i8, ptr %_46, i64 8
  store i64 %old, ptr %67, align 8
  store i64 1, ptr %_46, align 8
  br label %bb59

bb15:                                             ; preds = %bb59
  %68 = getelementptr inbounds i8, ptr %_46, i64 8
  %j14 = load i64, ptr %68, align 8
  %_54 = load i64, ptr %i, align 8
  %_55 = mul i64 %j14, 16
  %_53 = add i64 %_54, %_55
; call core::str::pattern::simd_contains::{{closure}}
  %_50 = call i16 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h837c1a2cb42025acE"(ptr align 8 %test_chunk, i64 %_53)
  %_56 = icmp ult i64 %j14, 4
  br i1 %_56, label %bb18, label %panic15

bb16:                                             ; preds = %bb59
  store i64 0, ptr %iter2, align 8
  %69 = getelementptr inbounds i8, ptr %iter2, i64 8
  store i64 4, ptr %69, align 8
  br label %bb19

bb19:                                             ; preds = %bb25, %bb16
  %other11 = getelementptr inbounds i8, ptr %iter2, i64 8
  %_128 = load i64, ptr %iter2, align 8
  %70 = getelementptr inbounds i8, ptr %iter2, i64 8
  %_129 = load i64, ptr %70, align 8
  %_123 = icmp ult i64 %_128, %_129
  br i1 %_123, label %bb62, label %bb63

bb63:                                             ; preds = %bb19
  %71 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %72 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %71, ptr %_58, align 8
  %73 = getelementptr inbounds i8, ptr %_58, i64 8
  store i64 %72, ptr %73, align 8
  br label %bb64

bb62:                                             ; preds = %bb19
  %old12 = load i64, ptr %iter2, align 8
  br label %bb65

bb64:                                             ; preds = %bb66, %bb63
  %_60 = load i64, ptr %_58, align 8
  %74 = getelementptr inbounds i8, ptr %_58, i64 8
  %75 = load i64, ptr %74, align 8
  %76 = trunc nuw i64 %_60 to i1
  br i1 %76, label %bb20, label %bb21

bb65:                                             ; preds = %bb62
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h9308eeef7bdc30a2E"(i64 %old12, i64 1) #20
  br label %bb66

bb66:                                             ; preds = %bb65
  %_127 = add nuw i64 %old12, 1
  store i64 %_127, ptr %iter2, align 8
  %77 = getelementptr inbounds i8, ptr %_58, i64 8
  store i64 %old12, ptr %77, align 8
  store i64 1, ptr %_58, align 8
  br label %bb64

bb20:                                             ; preds = %bb64
  %78 = getelementptr inbounds i8, ptr %_58, i64 8
  %j = load i64, ptr %78, align 8
  %_63 = icmp ult i64 %j, 4
  br i1 %_63, label %bb22, label %panic13

bb21:                                             ; preds = %bb64
  %79 = load i64, ptr %i, align 8
  %80 = add i64 %79, 64
  store i64 %80, ptr %i, align 8
  br label %bb10

bb22:                                             ; preds = %bb20
  %81 = getelementptr inbounds nuw i16, ptr %masks, i64 %j
  %mask = load i16, ptr %81, align 2
  %82 = icmp eq i16 %mask, 0
  br i1 %82, label %bb25, label %bb23

panic13:                                          ; preds = %bb20
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %j, i64 4, ptr align 8 @alloc_e85b6867b441d45a32f5c9e81ee13411) #19
  unreachable

bb25:                                             ; preds = %bb23, %bb22
  br label %bb19

bb23:                                             ; preds = %bb22
  %_68 = load i64, ptr %i, align 8
  %_69 = mul i64 %j, 16
  %_67 = add i64 %_68, %_69
  %83 = load i8, ptr %result, align 1
  %_70 = trunc nuw i8 %83 to i1
  store i64 %_67, ptr %_66, align 8
  %84 = getelementptr inbounds i8, ptr %_66, i64 8
  store i16 %mask, ptr %84, align 8
  %85 = getelementptr inbounds i8, ptr %_66, i64 10
  %86 = zext i1 %_70 to i8
  store i8 %86, ptr %85, align 2
  %87 = load i64, ptr %_66, align 8
  %88 = getelementptr inbounds i8, ptr %_66, i64 8
  %89 = load i16, ptr %88, align 8
  %90 = getelementptr inbounds i8, ptr %_66, i64 10
  %91 = load i8, ptr %90, align 2
  %92 = trunc nuw i8 %91 to i1
; call core::str::pattern::simd_contains::{{closure}}
  %_64 = call zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h5b319960cf604b51E"(ptr align 8 %check_mask, i64 %87, i16 %89, i1 zeroext %92)
  %93 = load i8, ptr %result, align 1
  %94 = trunc nuw i8 %93 to i1
  %95 = or i1 %94, %_64
  %96 = zext i1 %95 to i8
  store i8 %96, ptr %result, align 1
  br label %bb25

bb18:                                             ; preds = %bb15
  %97 = getelementptr inbounds nuw i16, ptr %masks, i64 %j14
  store i16 %_50, ptr %97, align 2
  br label %bb13

panic15:                                          ; preds = %bb15
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %j14, i64 4, ptr align 8 @alloc_162b8c84a21eee0918482f7aa1726e6f) #19
  unreachable

bb28:                                             ; preds = %bb34, %bb27
  %_74 = load i64, ptr %i, align 8
  %_73 = add i64 %_74, %last_byte_offset
  %_72 = add i64 %_73, 16
  %_71 = icmp ult i64 %_72, %haystack.1
  br i1 %_71, label %bb29, label %bb35

bb35:                                             ; preds = %bb28
  br label %bb36

bb29:                                             ; preds = %bb28
  %98 = load i8, ptr %result, align 1
  %_75 = trunc nuw i8 %98 to i1
  br i1 %_75, label %bb36, label %bb30

bb36:                                             ; preds = %bb29, %bb35
  %_86 = sub i64 %haystack.1, %last_byte_offset
  %i17 = sub i64 %_86, 16
; call core::str::pattern::simd_contains::{{closure}}
  %mask18 = call i16 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h837c1a2cb42025acE"(ptr align 8 %test_chunk, i64 %i17)
  %99 = icmp eq i16 %mask18, 0
  br i1 %99, label %bb40, label %bb38

bb30:                                             ; preds = %bb29
  %_79 = load i64, ptr %i, align 8
; call core::str::pattern::simd_contains::{{closure}}
  %mask16 = call i16 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h837c1a2cb42025acE"(ptr align 8 %test_chunk, i64 %_79)
  %100 = icmp eq i16 %mask16, 0
  br i1 %100, label %bb34, label %bb32

bb34:                                             ; preds = %bb32, %bb30
  %101 = load i64, ptr %i, align 8
  %102 = add i64 %101, 16
  store i64 %102, ptr %i, align 8
  br label %bb28

bb32:                                             ; preds = %bb30
  %_83 = load i64, ptr %i, align 8
  %103 = load i8, ptr %result, align 1
  %_84 = trunc nuw i8 %103 to i1
  store i64 %_83, ptr %_82, align 8
  %104 = getelementptr inbounds i8, ptr %_82, i64 8
  store i16 %mask16, ptr %104, align 8
  %105 = getelementptr inbounds i8, ptr %_82, i64 10
  %106 = zext i1 %_84 to i8
  store i8 %106, ptr %105, align 2
  %107 = load i64, ptr %_82, align 8
  %108 = getelementptr inbounds i8, ptr %_82, i64 8
  %109 = load i16, ptr %108, align 8
  %110 = getelementptr inbounds i8, ptr %_82, i64 10
  %111 = load i8, ptr %110, align 2
  %112 = trunc nuw i8 %111 to i1
; call core::str::pattern::simd_contains::{{closure}}
  %_80 = call zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h5b319960cf604b51E"(ptr align 8 %check_mask, i64 %107, i16 %109, i1 zeroext %112)
  %113 = load i8, ptr %result, align 1
  %114 = trunc nuw i8 %113 to i1
  %115 = or i1 %114, %_80
  %116 = zext i1 %115 to i8
  store i8 %116, ptr %result, align 1
  br label %bb34

bb40:                                             ; preds = %bb38, %bb36
  %117 = load i8, ptr %result, align 1
  %_94 = trunc nuw i8 %117 to i1
  %118 = zext i1 %_94 to i8
  store i8 %118, ptr %_0, align 1
  br label %bb42

bb38:                                             ; preds = %bb36
  %119 = load i8, ptr %result, align 1
  %_93 = trunc nuw i8 %119 to i1
  store i64 %i17, ptr %_92, align 8
  %120 = getelementptr inbounds i8, ptr %_92, i64 8
  store i16 %mask18, ptr %120, align 8
  %121 = getelementptr inbounds i8, ptr %_92, i64 10
  %122 = zext i1 %_93 to i8
  store i8 %122, ptr %121, align 2
  %123 = load i64, ptr %_92, align 8
  %124 = getelementptr inbounds i8, ptr %_92, i64 8
  %125 = load i16, ptr %124, align 8
  %126 = getelementptr inbounds i8, ptr %_92, i64 10
  %127 = load i8, ptr %126, align 2
  %128 = trunc nuw i8 %127 to i1
; call core::str::pattern::simd_contains::{{closure}}
  %_90 = call zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h5b319960cf604b51E"(ptr align 8 %check_mask, i64 %123, i16 %125, i1 zeroext %128)
  %129 = load i8, ptr %result, align 1
  %130 = trunc nuw i8 %129 to i1
  %131 = or i1 %130, %_90
  %132 = zext i1 %131 to i8
  store i8 %132, ptr %result, align 1
  br label %bb40

bb42:                                             ; preds = %bb41, %bb40
  %133 = load i8, ptr %_0, align 1
  ret i8 %133

bb49:                                             ; preds = %bb7
  %size = load i64, ptr %self3, align 8
  store ptr %haystack.0, ptr %_23, align 8
  %134 = getelementptr inbounds i8, ptr %_23, i64 8
  store i64 %haystack.1, ptr %134, align 8
  %135 = getelementptr inbounds i8, ptr %_23, i64 16
  store i64 %size, ptr %135, align 8
; call core::iter::traits::iterator::Iterator::try_fold
  %136 = call zeroext i1 @_ZN4core4iter6traits8iterator8Iterator8try_fold17h9f18bf4acbcdd83aE(ptr align 8 %_23, ptr align 8 %needle)
  %137 = zext i1 %136 to i8
  store i8 %137, ptr %_104, align 1
; call <core::ops::control_flow::ControlFlow<B,C> as core::cmp::PartialEq>::eq
  %_21 = call zeroext i1 @"_ZN90_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..cmp..PartialEq$GT$2eq17hb9db17e21f752620E"(ptr align 1 %_104, ptr align 1 @alloc_8821998f047ca62cad40e6bc4e4d87c4)
  %138 = zext i1 %_21 to i8
  store i8 %138, ptr %_0, align 1
  br label %bb41

bb48:                                             ; preds = %bb7
; call core::option::expect_failed
  call void @_ZN4core6option13expect_failed17h3c22898af43fbfd1E(ptr align 1 @alloc_d0b5540d38aa67683bd942cb6db4a43b, i64 28, ptr align 8 @alloc_29949ac893d7f5b8b2595a656c15fe2e) #19
  unreachable

bb41:                                             ; preds = %bb5, %bb49
  br label %bb42

bb14:                                             ; No predecessors!
  unreachable
}

; core::str::pattern::simd_contains::{{closure}}
; Function Attrs: cold inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h5b319960cf604b51E"(ptr align 8 %_1, i64 %idx, i16 %mask, i1 zeroext %skip) unnamed_addr #5 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [8 x i8], align 8
  %new_len = alloca [8 x i8], align 8
  %_13 = alloca [8 x i8], align 8
  %mask1 = alloca [2 x i8], align 2
  %_0 = alloca [1 x i8], align 1
  br i1 %skip, label %bb1, label %bb2

bb2:                                              ; preds = %start
  store i16 %mask, ptr %mask1, align 2
  br label %bb3

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb9

bb3:                                              ; preds = %bb7, %bb2
  %_6 = load i16, ptr %mask1, align 2
  %1 = icmp eq i16 %_6, 0
  br i1 %1, label %bb8, label %bb4

bb8:                                              ; preds = %bb3
  store i8 0, ptr %_0, align 1
  br label %bb9

bb4:                                              ; preds = %bb3
  %self = load i16, ptr %mask1, align 2
  %2 = call i16 @llvm.cttz.i16(i16 %self, i1 false)
  %3 = zext i16 %2 to i32
  store i32 %3, ptr %0, align 4
  %trailing = load i32, ptr %0, align 4
  %_11 = zext i32 %trailing to i64
  %_10 = add i64 %idx, %_11
  %offset = add i64 %_10, 1
  %self.0 = load ptr, ptr %_1, align 8
  %4 = getelementptr inbounds i8, ptr %_1, i64 8
  %self.1 = load i64, ptr %4, align 8
  br label %bb11

bb9:                                              ; preds = %bb1, %bb6, %bb8
  %5 = load i8, ptr %_0, align 1
  %6 = trunc nuw i8 %5 to i1
  ret i1 %6

bb11:                                             ; preds = %bb4
; call <core::ops::range::Range<usize> as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17hba97ba0ada474577E"(i64 %offset, i64 %self.1, i64 %self.1) #20
  %7 = sub nuw i64 %self.1, %offset
  store i64 %7, ptr %new_len, align 8
  %8 = getelementptr inbounds nuw i8, ptr %self.0, i64 %offset
  store ptr %8, ptr %_26, align 8
  %self.02 = load ptr, ptr %_26, align 8
  %self.13 = load i64, ptr %new_len, align 8
  %9 = getelementptr inbounds i8, ptr %_1, i64 16
  %_18.0 = load ptr, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %9, i64 8
  %_18.1 = load i64, ptr %10, align 8
  store i64 %_18.1, ptr %_13, align 8
  %index = load i64, ptr %_13, align 8
  %self4 = load i64, ptr %_13, align 8
  br label %bb13

bb13:                                             ; preds = %bb11
  %11 = load i64, ptr %_13, align 8
  %12 = load i64, ptr %new_len, align 8
; call <core::ops::range::Range<usize> as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17hba97ba0ada474577E"(i64 0, i64 %11, i64 %12) #20
  br label %bb14

bb14:                                             ; preds = %bb13
  %new_len5 = load i64, ptr %_13, align 8
  %sub.0 = load ptr, ptr %_26, align 8
  %sub.1 = load i64, ptr %_13, align 8
  %13 = getelementptr inbounds i8, ptr %_1, i64 16
  %_19.0 = load ptr, ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %13, i64 8
  %_19.1 = load i64, ptr %14, align 8
; call core::str::pattern::small_slice_eq
  %_14 = call zeroext i1 @_ZN4core3str7pattern14small_slice_eq17hfc87e697cb957c94E(ptr align 1 %sub.0, i64 %sub.1, ptr align 1 %_19.0, i64 %_19.1)
  br i1 %_14, label %bb6, label %bb7

bb7:                                              ; preds = %bb14
  %15 = and i32 %trailing, 15
  %16 = trunc i32 %15 to i16
  %_16 = shl i16 1, %16
  %_15 = xor i16 %_16, -1
  %17 = load i16, ptr %mask1, align 2
  %18 = and i16 %17, %_15
  store i16 %18, ptr %mask1, align 2
  br label %bb3

bb6:                                              ; preds = %bb14
  store i8 1, ptr %_0, align 1
  br label %bb9

bb12:                                             ; No predecessors!
  unreachable
}

; core::str::pattern::simd_contains::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h7de5f766c273ea1dE"(ptr align 8 %_1, ptr align 8 %_2) unnamed_addr #1 {
start:
  %idx = load i64, ptr %_2, align 8
  %_8.0 = load ptr, ptr %_1, align 8
  %0 = getelementptr inbounds i8, ptr %_1, i64 8
  %_8.1 = load i64, ptr %0, align 8
  %_6 = icmp ult i64 %idx, %_8.1
  br i1 %_6, label %bb1, label %panic

bb1:                                              ; preds = %start
  %_9.0 = load ptr, ptr %_1, align 8
  %1 = getelementptr inbounds i8, ptr %_1, i64 8
  %_9.1 = load i64, ptr %1, align 8
  %2 = getelementptr inbounds nuw i8, ptr %_9.0, i64 %idx
  %_4 = load i8, ptr %2, align 1
  %3 = getelementptr inbounds i8, ptr %_1, i64 16
  %_10 = load ptr, ptr %3, align 8
  %_7 = load i8, ptr %_10, align 1
  %_0 = icmp ne i8 %_4, %_7
  ret i1 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %idx, i64 %_8.1, ptr align 8 @alloc_a2b3767d37061bfef0bfcdf089c5053c) #19
  unreachable
}

; core::str::pattern::simd_contains::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i16 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h837c1a2cb42025acE"(ptr align 8 %_1, i64 %idx) unnamed_addr #1 {
start:
  %0 = alloca [16 x i8], align 16
  %1 = alloca [16 x i8], align 16
  %2 = alloca [16 x i8], align 16
  %self.0 = load ptr, ptr %_1, align 8
  %3 = getelementptr inbounds i8, ptr %_1, i64 8
  %self.1 = load i64, ptr %3, align 8
  %self = getelementptr inbounds nuw i8, ptr %self.0, i64 %idx
; call core::ptr::read_unaligned
  %a = call <16 x i8> @_ZN4core3ptr14read_unaligned17h3def2c5b182a2c6aE(ptr %self)
  %self.01 = load ptr, ptr %_1, align 8
  %4 = getelementptr inbounds i8, ptr %_1, i64 8
  %self.12 = load i64, ptr %4, align 8
  %self3 = getelementptr inbounds nuw i8, ptr %self.01, i64 %idx
  %5 = getelementptr inbounds i8, ptr %_1, i64 16
  %_18 = load ptr, ptr %5, align 8
  %count = load i64, ptr %_18, align 8
  %self4 = getelementptr inbounds nuw i8, ptr %self3, i64 %count
; call core::ptr::read_unaligned
  %b = call <16 x i8> @_ZN4core3ptr14read_unaligned17h3def2c5b182a2c6aE(ptr %self4)
  %6 = getelementptr inbounds i8, ptr %_1, i64 24
  %_19 = load ptr, ptr %6, align 8
  %other = load <16 x i8>, ptr %_19, align 16
  %7 = icmp eq <16 x i8> %a, %other
  %8 = sext <16 x i1> %7 to <16 x i8>
  store <16 x i8> %8, ptr %2, align 16
  %eq_first = load <16 x i8>, ptr %2, align 16
; call core::core_simd::masks::<impl core::core_simd::masks::sealed::Sealed for i8>::valid
  %_24 = call zeroext i1 @"_ZN4core9core_simd5masks71_$LT$impl$u20$core..core_simd..masks..sealed..Sealed$u20$for$u20$i8$GT$5valid17h9f67b3d8761714e0E"(<16 x i8> %eq_first)
  %9 = getelementptr inbounds i8, ptr %_1, i64 32
  %_20 = load ptr, ptr %9, align 8
  %other5 = load <16 x i8>, ptr %_20, align 16
  %10 = icmp eq <16 x i8> %b, %other5
  %11 = sext <16 x i1> %10 to <16 x i8>
  store <16 x i8> %11, ptr %1, align 16
  %eq_last = load <16 x i8>, ptr %1, align 16
; call core::core_simd::masks::<impl core::core_simd::masks::sealed::Sealed for i8>::valid
  %_26 = call zeroext i1 @"_ZN4core9core_simd5masks71_$LT$impl$u20$core..core_simd..masks..sealed..Sealed$u20$for$u20$i8$GT$5valid17h9f67b3d8761714e0E"(<16 x i8> %eq_last)
  %12 = and <16 x i8> %eq_first, %eq_last
  store <16 x i8> %12, ptr %0, align 16
  %_28 = load <16 x i8>, ptr %0, align 16
; call core::core_simd::masks::mask_impl::Mask<T,_>::to_bitmask_integer
  %_15 = call i64 @"_ZN4core9core_simd5masks9mask_impl17Mask$LT$T$C$_$GT$18to_bitmask_integer17h481681f9d696fef7E"(<16 x i8> %_28)
  %mask = trunc i64 %_15 to i16
  ret i16 %mask
}

; core::str::pattern::simd_contains::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17hb3951c5ed916f9c9E"(ptr align 8 %_1, ptr align 1 %0, i64 %1) unnamed_addr #1 {
start:
  %c = alloca [16 x i8], align 8
  store ptr %0, ptr %c, align 8
  %2 = getelementptr inbounds i8, ptr %c, i64 8
  store i64 %1, ptr %2, align 8
  %other = load ptr, ptr %_1, align 8
  %other.0 = load ptr, ptr %other, align 8
  %3 = getelementptr inbounds i8, ptr %other, i64 8
  %other.1 = load i64, ptr %3, align 8
  %4 = load ptr, ptr %c, align 8
  %5 = getelementptr inbounds i8, ptr %c, i64 8
  %6 = load i64, ptr %5, align 8
; call <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
  %_0 = call zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17h7715a4528a246ec0E"(ptr align 1 %4, i64 %6, ptr align 1 %other.0, i64 %other.1)
  ret i1 %_0
}

; core::str::pattern::TwoWaySearcher::next
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3str7pattern14TwoWaySearcher4next17hc5e2753abaf6d66fE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, ptr align 1 %haystack.0, i64 %haystack.1, ptr align 1 %needle.0, i64 %needle.1, i1 zeroext %long_period) unnamed_addr #1 {
start:
  %_42 = alloca [16 x i8], align 8
  %iter3 = alloca [16 x i8], align 8
  %start2 = alloca [8 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %iter = alloca [16 x i8], align 8
  %start1 = alloca [8 x i8], align 8
  %_8 = alloca [8 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  %old_pos = load i64, ptr %0, align 8
  %needle_last = sub i64 %needle.1, 1
  br label %bb1

bb1:                                              ; preds = %bb37, %start
  %1 = getelementptr inbounds i8, ptr %self, i64 32
  %_10 = load i64, ptr %1, align 8
  %index = add i64 %_10, %needle_last
  %_57 = icmp ult i64 %index, %haystack.1
  br i1 %_57, label %bb39, label %bb40

bb40:                                             ; preds = %bb1
  %2 = getelementptr inbounds i8, ptr %self, i64 32
  store i64 %haystack.1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 32
  %_12 = load i64, ptr %3, align 8
; call <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::rejecting
  call void @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h0a0fb1d1877ff12eE"(ptr sret([24 x i8]) align 8 %_0, i64 %old_pos, i64 %_12)
  br label %bb38

bb39:                                             ; preds = %bb1
  %_60 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %index
  store ptr %_60, ptr %_8, align 8
  %_56 = load ptr, ptr %_8, align 8
  %tail_byte = load i8, ptr %_56, align 1
; call <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::use_early_reject
  %_13 = call zeroext i1 @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$16use_early_reject17hcc537a7fe963f812E"()
  br i1 %_13, label %bb4, label %bb8

bb38:                                             ; preds = %bb5, %bb34, %bb40
  ret void

bb8:                                              ; preds = %bb7, %bb39
  %4 = getelementptr inbounds i8, ptr %self, i64 24
  %_65 = load i64, ptr %4, align 8
  %_67 = and i8 %tail_byte, 63
  %_66 = zext i8 %_67 to i64
  %5 = and i64 %_66, 63
  %_64 = lshr i64 %_65, %5
  %_63 = and i64 %_64, 1
  %6 = icmp eq i64 %_63, 0
  br i1 %6, label %bb10, label %bb9

bb4:                                              ; preds = %bb39
  %7 = getelementptr inbounds i8, ptr %self, i64 32
  %_15 = load i64, ptr %7, align 8
  %_14 = icmp ne i64 %old_pos, %_15
  br i1 %_14, label %bb5, label %bb7

bb7:                                              ; preds = %bb4
  br label %bb8

bb5:                                              ; preds = %bb4
  %8 = getelementptr inbounds i8, ptr %self, i64 32
  %_16 = load i64, ptr %8, align 8
; call <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::rejecting
  call void @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h0a0fb1d1877ff12eE"(ptr sret([24 x i8]) align 8 %_0, i64 %old_pos, i64 %_16)
  br label %bb38

bb10:                                             ; preds = %bb8
  %9 = getelementptr inbounds i8, ptr %self, i64 32
  %10 = getelementptr inbounds i8, ptr %self, i64 32
  %11 = load i64, ptr %10, align 8
  %12 = add i64 %11, %needle.1
  store i64 %12, ptr %9, align 8
  br i1 %long_period, label %bb12, label %bb11

bb9:                                              ; preds = %bb8
  br i1 %long_period, label %bb13, label %bb14

bb11:                                             ; preds = %bb10
  %13 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %13, align 8
  br label %bb12

bb12:                                             ; preds = %bb11, %bb10
  br label %bb37

bb37:                                             ; preds = %bb36, %bb12
  br label %bb1

bb14:                                             ; preds = %bb9
  %v1 = load i64, ptr %self, align 8
  %14 = getelementptr inbounds i8, ptr %self, i64 48
  %v2 = load i64, ptr %14, align 8
; call core::cmp::Ord::max
  %15 = call i64 @_ZN4core3cmp3Ord3max17h6f2a6a8eb8d692e5E(i64 %v1, i64 %v2)
  store i64 %15, ptr %start1, align 8
  br label %bb15

bb13:                                             ; preds = %bb9
  %16 = load i64, ptr %self, align 8
  store i64 %16, ptr %start1, align 8
  br label %bb15

bb15:                                             ; preds = %bb13, %bb14
  %_21 = load i64, ptr %start1, align 8
  store i64 %_21, ptr %iter, align 8
  %17 = getelementptr inbounds i8, ptr %iter, i64 8
  store i64 %needle.1, ptr %17, align 8
  br label %bb16

bb16:                                             ; preds = %bb22, %bb15
  %other = getelementptr inbounds i8, ptr %iter, i64 8
  %_73 = load i64, ptr %iter, align 8
  %18 = getelementptr inbounds i8, ptr %iter, i64 8
  %_74 = load i64, ptr %18, align 8
  %_68 = icmp ult i64 %_73, %_74
  br i1 %_68, label %bb42, label %bb43

bb43:                                             ; preds = %bb16
  br i1 %long_period, label %bb23, label %bb24

bb42:                                             ; preds = %bb16
  %old = load i64, ptr %iter, align 8
  br label %bb44

bb24:                                             ; preds = %bb43
  %19 = getelementptr inbounds i8, ptr %self, i64 48
  %20 = load i64, ptr %19, align 8
  store i64 %20, ptr %start2, align 8
  br label %bb25

bb23:                                             ; preds = %bb43
  store i64 0, ptr %start2, align 8
  br label %bb25

bb25:                                             ; preds = %bb23, %bb24
  %_39 = load i64, ptr %start2, align 8
  %_40 = load i64, ptr %self, align 8
  store i64 %_39, ptr %iter3, align 8
  %21 = getelementptr inbounds i8, ptr %iter3, i64 8
  store i64 %_40, ptr %21, align 8
  br label %bb26

bb26:                                             ; preds = %bb32, %bb25
  %other4 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_84 = load i64, ptr %iter3, align 8
  %22 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_85 = load i64, ptr %22, align 8
  %_78 = icmp ult i64 %_84, %_85
  br i1 %_78, label %bb46, label %bb47

bb47:                                             ; preds = %bb26
  %23 = getelementptr inbounds i8, ptr %self, i64 32
  %match_pos = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %self, i64 32
  %25 = getelementptr inbounds i8, ptr %self, i64 32
  %26 = load i64, ptr %25, align 8
  %27 = add i64 %26, %needle.1
  store i64 %27, ptr %24, align 8
  br i1 %long_period, label %bb34, label %bb33

bb46:                                             ; preds = %bb26
  %28 = getelementptr inbounds i8, ptr %iter3, i64 8
  %start5 = load i64, ptr %28, align 8
  br label %bb48

bb33:                                             ; preds = %bb47
  %29 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %29, align 8
  br label %bb34

bb34:                                             ; preds = %bb33, %bb47
  %_55 = add i64 %match_pos, %needle.1
; call <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::matching
  call void @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$8matching17h5d7354f08f3a4641E"(ptr sret([24 x i8]) align 8 %_0, i64 %match_pos, i64 %_55)
  br label %bb38

bb48:                                             ; preds = %bb46
; call core::num::<impl usize>::unchecked_sub::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_sub18precondition_check17hb7209f147b98244eE"(i64 %start5, i64 1) #20
  br label %bb49

bb49:                                             ; preds = %bb48
  %_81 = sub nuw i64 %start5, 1
  %30 = getelementptr inbounds i8, ptr %iter3, i64 8
  store i64 %_81, ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_83 = load i64, ptr %31, align 8
  %32 = getelementptr inbounds i8, ptr %_42, i64 8
  store i64 %_83, ptr %32, align 8
  store i64 1, ptr %_42, align 8
  %33 = getelementptr inbounds i8, ptr %_42, i64 8
  %i = load i64, ptr %33, align 8
  %_47 = icmp ult i64 %i, %needle.1
  br i1 %_47, label %bb27, label %panic

bb27:                                             ; preds = %bb49
  %34 = getelementptr inbounds nuw i8, ptr %needle.0, i64 %i
  %_46 = load i8, ptr %34, align 1
  %35 = getelementptr inbounds i8, ptr %self, i64 32
  %_50 = load i64, ptr %35, align 8
  %_49 = add i64 %_50, %i
  %_51 = icmp ult i64 %_49, %haystack.1
  br i1 %_51, label %bb28, label %panic6

panic:                                            ; preds = %bb49
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i, i64 %needle.1, ptr align 8 @alloc_8113544254eb174f4af9b7b42b23e9d1) #19
  unreachable

bb28:                                             ; preds = %bb27
  %36 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %_49
  %_48 = load i8, ptr %36, align 1
  %_45 = icmp ne i8 %_46, %_48
  br i1 %_45, label %bb29, label %bb32

panic6:                                           ; preds = %bb27
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_49, i64 %haystack.1, ptr align 8 @alloc_7131847d62882af9d6c09c2b5050c8ea) #19
  unreachable

bb32:                                             ; preds = %bb28
  br label %bb26

bb29:                                             ; preds = %bb28
  %37 = getelementptr inbounds i8, ptr %self, i64 16
  %_52 = load i64, ptr %37, align 8
  %38 = getelementptr inbounds i8, ptr %self, i64 32
  %39 = getelementptr inbounds i8, ptr %self, i64 32
  %40 = load i64, ptr %39, align 8
  %41 = add i64 %40, %_52
  store i64 %41, ptr %38, align 8
  br i1 %long_period, label %bb31, label %bb30

bb30:                                             ; preds = %bb29
  %42 = getelementptr inbounds i8, ptr %self, i64 16
  %_53 = load i64, ptr %42, align 8
  %43 = getelementptr inbounds i8, ptr %self, i64 48
  %44 = sub i64 %needle.1, %_53
  store i64 %44, ptr %43, align 8
  br label %bb31

bb31:                                             ; preds = %bb30, %bb29
  br label %bb36

bb36:                                             ; preds = %bb21, %bb31
  br label %bb37

bb44:                                             ; preds = %bb42
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h9308eeef7bdc30a2E"(i64 %old, i64 1) #20
  br label %bb45

bb45:                                             ; preds = %bb44
  %_72 = add nuw i64 %old, 1
  store i64 %_72, ptr %iter, align 8
  %45 = getelementptr inbounds i8, ptr %_23, i64 8
  store i64 %old, ptr %45, align 8
  store i64 1, ptr %_23, align 8
  %46 = getelementptr inbounds i8, ptr %_23, i64 8
  %i7 = load i64, ptr %46, align 8
  %_28 = icmp ult i64 %i7, %needle.1
  br i1 %_28, label %bb17, label %panic8

bb17:                                             ; preds = %bb45
  %47 = getelementptr inbounds nuw i8, ptr %needle.0, i64 %i7
  %_27 = load i8, ptr %47, align 1
  %48 = getelementptr inbounds i8, ptr %self, i64 32
  %_31 = load i64, ptr %48, align 8
  %_30 = add i64 %_31, %i7
  %_32 = icmp ult i64 %_30, %haystack.1
  br i1 %_32, label %bb18, label %panic9

panic8:                                           ; preds = %bb45
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i7, i64 %needle.1, ptr align 8 @alloc_85f8ff8172fa0a155a0a9d540f76803c) #19
  unreachable

bb18:                                             ; preds = %bb17
  %49 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %_30
  %_29 = load i8, ptr %49, align 1
  %_26 = icmp ne i8 %_27, %_29
  br i1 %_26, label %bb19, label %bb22

panic9:                                           ; preds = %bb17
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_30, i64 %haystack.1, ptr align 8 @alloc_5e95671221ecfb0d1badc509254cde7b) #19
  unreachable

bb22:                                             ; preds = %bb18
  br label %bb16

bb19:                                             ; preds = %bb18
  %_35 = load i64, ptr %self, align 8
  %_34 = sub i64 %i7, %_35
  %_33 = add i64 %_34, 1
  %50 = getelementptr inbounds i8, ptr %self, i64 32
  %51 = getelementptr inbounds i8, ptr %self, i64 32
  %52 = load i64, ptr %51, align 8
  %53 = add i64 %52, %_33
  store i64 %53, ptr %50, align 8
  br i1 %long_period, label %bb21, label %bb20

bb20:                                             ; preds = %bb19
  %54 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %54, align 8
  br label %bb21

bb21:                                             ; preds = %bb20, %bb19
  br label %bb36
}

; core::str::pattern::TwoWaySearcher::next
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3str7pattern14TwoWaySearcher4next17hd2f940f223d4d143E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, ptr align 1 %haystack.0, i64 %haystack.1, ptr align 1 %needle.0, i64 %needle.1, i1 zeroext %long_period) unnamed_addr #1 {
start:
  %_42 = alloca [16 x i8], align 8
  %iter3 = alloca [16 x i8], align 8
  %start2 = alloca [8 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %iter = alloca [16 x i8], align 8
  %start1 = alloca [8 x i8], align 8
  %_8 = alloca [8 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  %old_pos = load i64, ptr %0, align 8
  %needle_last = sub i64 %needle.1, 1
  br label %bb1

bb1:                                              ; preds = %bb37, %start
  %1 = getelementptr inbounds i8, ptr %self, i64 32
  %_10 = load i64, ptr %1, align 8
  %index = add i64 %_10, %needle_last
  %_57 = icmp ult i64 %index, %haystack.1
  br i1 %_57, label %bb39, label %bb40

bb40:                                             ; preds = %bb1
  %2 = getelementptr inbounds i8, ptr %self, i64 32
  store i64 %haystack.1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 32
  %_12 = load i64, ptr %3, align 8
; call <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::rejecting
  call void @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h3fb8f6b1c25833cbE"(ptr sret([24 x i8]) align 8 %_0, i64 %old_pos, i64 %_12)
  br label %bb38

bb39:                                             ; preds = %bb1
  %_60 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %index
  store ptr %_60, ptr %_8, align 8
  %_56 = load ptr, ptr %_8, align 8
  %tail_byte = load i8, ptr %_56, align 1
; call <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::use_early_reject
  %_13 = call zeroext i1 @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$16use_early_reject17hdee8f106adddafd7E"()
  br i1 %_13, label %bb4, label %bb8

bb38:                                             ; preds = %bb5, %bb34, %bb40
  ret void

bb8:                                              ; preds = %bb7, %bb39
  %4 = getelementptr inbounds i8, ptr %self, i64 24
  %_65 = load i64, ptr %4, align 8
  %_67 = and i8 %tail_byte, 63
  %_66 = zext i8 %_67 to i64
  %5 = and i64 %_66, 63
  %_64 = lshr i64 %_65, %5
  %_63 = and i64 %_64, 1
  %6 = icmp eq i64 %_63, 0
  br i1 %6, label %bb10, label %bb9

bb4:                                              ; preds = %bb39
  %7 = getelementptr inbounds i8, ptr %self, i64 32
  %_15 = load i64, ptr %7, align 8
  %_14 = icmp ne i64 %old_pos, %_15
  br i1 %_14, label %bb5, label %bb7

bb7:                                              ; preds = %bb4
  br label %bb8

bb5:                                              ; preds = %bb4
  %8 = getelementptr inbounds i8, ptr %self, i64 32
  %_16 = load i64, ptr %8, align 8
; call <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::rejecting
  call void @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h3fb8f6b1c25833cbE"(ptr sret([24 x i8]) align 8 %_0, i64 %old_pos, i64 %_16)
  br label %bb38

bb10:                                             ; preds = %bb8
  %9 = getelementptr inbounds i8, ptr %self, i64 32
  %10 = getelementptr inbounds i8, ptr %self, i64 32
  %11 = load i64, ptr %10, align 8
  %12 = add i64 %11, %needle.1
  store i64 %12, ptr %9, align 8
  br i1 %long_period, label %bb12, label %bb11

bb9:                                              ; preds = %bb8
  br i1 %long_period, label %bb13, label %bb14

bb11:                                             ; preds = %bb10
  %13 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %13, align 8
  br label %bb12

bb12:                                             ; preds = %bb11, %bb10
  br label %bb37

bb37:                                             ; preds = %bb36, %bb12
  br label %bb1

bb14:                                             ; preds = %bb9
  %v1 = load i64, ptr %self, align 8
  %14 = getelementptr inbounds i8, ptr %self, i64 48
  %v2 = load i64, ptr %14, align 8
; call core::cmp::Ord::max
  %15 = call i64 @_ZN4core3cmp3Ord3max17h6f2a6a8eb8d692e5E(i64 %v1, i64 %v2)
  store i64 %15, ptr %start1, align 8
  br label %bb15

bb13:                                             ; preds = %bb9
  %16 = load i64, ptr %self, align 8
  store i64 %16, ptr %start1, align 8
  br label %bb15

bb15:                                             ; preds = %bb13, %bb14
  %_21 = load i64, ptr %start1, align 8
  store i64 %_21, ptr %iter, align 8
  %17 = getelementptr inbounds i8, ptr %iter, i64 8
  store i64 %needle.1, ptr %17, align 8
  br label %bb16

bb16:                                             ; preds = %bb22, %bb15
  %other = getelementptr inbounds i8, ptr %iter, i64 8
  %_73 = load i64, ptr %iter, align 8
  %18 = getelementptr inbounds i8, ptr %iter, i64 8
  %_74 = load i64, ptr %18, align 8
  %_68 = icmp ult i64 %_73, %_74
  br i1 %_68, label %bb42, label %bb43

bb43:                                             ; preds = %bb16
  br i1 %long_period, label %bb23, label %bb24

bb42:                                             ; preds = %bb16
  %old = load i64, ptr %iter, align 8
  br label %bb44

bb24:                                             ; preds = %bb43
  %19 = getelementptr inbounds i8, ptr %self, i64 48
  %20 = load i64, ptr %19, align 8
  store i64 %20, ptr %start2, align 8
  br label %bb25

bb23:                                             ; preds = %bb43
  store i64 0, ptr %start2, align 8
  br label %bb25

bb25:                                             ; preds = %bb23, %bb24
  %_39 = load i64, ptr %start2, align 8
  %_40 = load i64, ptr %self, align 8
  store i64 %_39, ptr %iter3, align 8
  %21 = getelementptr inbounds i8, ptr %iter3, i64 8
  store i64 %_40, ptr %21, align 8
  br label %bb26

bb26:                                             ; preds = %bb32, %bb25
  %other4 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_84 = load i64, ptr %iter3, align 8
  %22 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_85 = load i64, ptr %22, align 8
  %_78 = icmp ult i64 %_84, %_85
  br i1 %_78, label %bb46, label %bb47

bb47:                                             ; preds = %bb26
  %23 = getelementptr inbounds i8, ptr %self, i64 32
  %match_pos = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %self, i64 32
  %25 = getelementptr inbounds i8, ptr %self, i64 32
  %26 = load i64, ptr %25, align 8
  %27 = add i64 %26, %needle.1
  store i64 %27, ptr %24, align 8
  br i1 %long_period, label %bb34, label %bb33

bb46:                                             ; preds = %bb26
  %28 = getelementptr inbounds i8, ptr %iter3, i64 8
  %start5 = load i64, ptr %28, align 8
  br label %bb48

bb33:                                             ; preds = %bb47
  %29 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %29, align 8
  br label %bb34

bb34:                                             ; preds = %bb33, %bb47
  %_55 = add i64 %match_pos, %needle.1
; call <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::matching
  call void @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$8matching17h828b2bbd329342eeE"(ptr sret([24 x i8]) align 8 %_0, i64 %match_pos, i64 %_55)
  br label %bb38

bb48:                                             ; preds = %bb46
; call core::num::<impl usize>::unchecked_sub::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_sub18precondition_check17hb7209f147b98244eE"(i64 %start5, i64 1) #20
  br label %bb49

bb49:                                             ; preds = %bb48
  %_81 = sub nuw i64 %start5, 1
  %30 = getelementptr inbounds i8, ptr %iter3, i64 8
  store i64 %_81, ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %iter3, i64 8
  %_83 = load i64, ptr %31, align 8
  %32 = getelementptr inbounds i8, ptr %_42, i64 8
  store i64 %_83, ptr %32, align 8
  store i64 1, ptr %_42, align 8
  %33 = getelementptr inbounds i8, ptr %_42, i64 8
  %i = load i64, ptr %33, align 8
  %_47 = icmp ult i64 %i, %needle.1
  br i1 %_47, label %bb27, label %panic

bb27:                                             ; preds = %bb49
  %34 = getelementptr inbounds nuw i8, ptr %needle.0, i64 %i
  %_46 = load i8, ptr %34, align 1
  %35 = getelementptr inbounds i8, ptr %self, i64 32
  %_50 = load i64, ptr %35, align 8
  %_49 = add i64 %_50, %i
  %_51 = icmp ult i64 %_49, %haystack.1
  br i1 %_51, label %bb28, label %panic6

panic:                                            ; preds = %bb49
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i, i64 %needle.1, ptr align 8 @alloc_8113544254eb174f4af9b7b42b23e9d1) #19
  unreachable

bb28:                                             ; preds = %bb27
  %36 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %_49
  %_48 = load i8, ptr %36, align 1
  %_45 = icmp ne i8 %_46, %_48
  br i1 %_45, label %bb29, label %bb32

panic6:                                           ; preds = %bb27
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_49, i64 %haystack.1, ptr align 8 @alloc_7131847d62882af9d6c09c2b5050c8ea) #19
  unreachable

bb32:                                             ; preds = %bb28
  br label %bb26

bb29:                                             ; preds = %bb28
  %37 = getelementptr inbounds i8, ptr %self, i64 16
  %_52 = load i64, ptr %37, align 8
  %38 = getelementptr inbounds i8, ptr %self, i64 32
  %39 = getelementptr inbounds i8, ptr %self, i64 32
  %40 = load i64, ptr %39, align 8
  %41 = add i64 %40, %_52
  store i64 %41, ptr %38, align 8
  br i1 %long_period, label %bb31, label %bb30

bb30:                                             ; preds = %bb29
  %42 = getelementptr inbounds i8, ptr %self, i64 16
  %_53 = load i64, ptr %42, align 8
  %43 = getelementptr inbounds i8, ptr %self, i64 48
  %44 = sub i64 %needle.1, %_53
  store i64 %44, ptr %43, align 8
  br label %bb31

bb31:                                             ; preds = %bb30, %bb29
  br label %bb36

bb36:                                             ; preds = %bb21, %bb31
  br label %bb37

bb44:                                             ; preds = %bb42
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h9308eeef7bdc30a2E"(i64 %old, i64 1) #20
  br label %bb45

bb45:                                             ; preds = %bb44
  %_72 = add nuw i64 %old, 1
  store i64 %_72, ptr %iter, align 8
  %45 = getelementptr inbounds i8, ptr %_23, i64 8
  store i64 %old, ptr %45, align 8
  store i64 1, ptr %_23, align 8
  %46 = getelementptr inbounds i8, ptr %_23, i64 8
  %i7 = load i64, ptr %46, align 8
  %_28 = icmp ult i64 %i7, %needle.1
  br i1 %_28, label %bb17, label %panic8

bb17:                                             ; preds = %bb45
  %47 = getelementptr inbounds nuw i8, ptr %needle.0, i64 %i7
  %_27 = load i8, ptr %47, align 1
  %48 = getelementptr inbounds i8, ptr %self, i64 32
  %_31 = load i64, ptr %48, align 8
  %_30 = add i64 %_31, %i7
  %_32 = icmp ult i64 %_30, %haystack.1
  br i1 %_32, label %bb18, label %panic9

panic8:                                           ; preds = %bb45
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i7, i64 %needle.1, ptr align 8 @alloc_85f8ff8172fa0a155a0a9d540f76803c) #19
  unreachable

bb18:                                             ; preds = %bb17
  %49 = getelementptr inbounds nuw i8, ptr %haystack.0, i64 %_30
  %_29 = load i8, ptr %49, align 1
  %_26 = icmp ne i8 %_27, %_29
  br i1 %_26, label %bb19, label %bb22

panic9:                                           ; preds = %bb17
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_30, i64 %haystack.1, ptr align 8 @alloc_5e95671221ecfb0d1badc509254cde7b) #19
  unreachable

bb22:                                             ; preds = %bb18
  br label %bb16

bb19:                                             ; preds = %bb18
  %_35 = load i64, ptr %self, align 8
  %_34 = sub i64 %i7, %_35
  %_33 = add i64 %_34, 1
  %50 = getelementptr inbounds i8, ptr %self, i64 32
  %51 = getelementptr inbounds i8, ptr %self, i64 32
  %52 = load i64, ptr %51, align 8
  %53 = add i64 %52, %_33
  store i64 %53, ptr %50, align 8
  br i1 %long_period, label %bb21, label %bb20

bb20:                                             ; preds = %bb19
  %54 = getelementptr inbounds i8, ptr %self, i64 48
  store i64 0, ptr %54, align 8
  br label %bb21

bb21:                                             ; preds = %bb20, %bb19
  br label %bb36
}

; core::str::pattern::small_slice_eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core3str7pattern14small_slice_eq17hfc87e697cb957c94E(ptr align 1 %x.0, i64 %x.1, ptr align 1 %y.0, i64 %y.1) unnamed_addr #1 {
start:
  %py = alloca [8 x i8], align 8
  %px = alloca [8 x i8], align 8
  %_10 = alloca [16 x i8], align 8
  %iter = alloca [56 x i8], align 8
  %self = alloca [56 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %_5 = icmp ult i64 %x.1, 4
  br i1 %_5, label %bb1, label %bb9

bb9:                                              ; preds = %start
  store ptr %x.0, ptr %px, align 8
  store ptr %y.0, ptr %py, align 8
  %self1 = load ptr, ptr %px, align 8
  %count = sub i64 %x.1, 4
  %pxend = getelementptr inbounds nuw i8, ptr %self1, i64 %count
  %self2 = load ptr, ptr %py, align 8
  %count3 = sub i64 %y.1, 4
  %pyend = getelementptr inbounds nuw i8, ptr %self2, i64 %count3
  br label %bb10

bb1:                                              ; preds = %start
  %_49 = getelementptr inbounds nuw i8, ptr %x.0, i64 %x.1
; call core::iter::traits::iterator::Iterator::zip
  call void @_ZN4core4iter6traits8iterator8Iterator3zip17h15f29d0915aa0627E(ptr sret([56 x i8]) align 8 %self, ptr %x.0, ptr %_49, ptr align 1 %y.0, i64 %y.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %self, i64 56, i1 false)
  br label %bb3

bb10:                                             ; preds = %bb13, %bb9
  %_28 = load ptr, ptr %px, align 8
  %_27 = icmp ult ptr %_28, %pxend
  br i1 %_27, label %bb11, label %bb14

bb14:                                             ; preds = %bb10
; call core::ptr::read_unaligned
  %vx = call i32 @_ZN4core3ptr14read_unaligned17h6401ad35ba225740E(ptr %pxend)
; call core::ptr::read_unaligned
  %vy = call i32 @_ZN4core3ptr14read_unaligned17h6401ad35ba225740E(ptr %pyend)
  %0 = icmp eq i32 %vx, %vy
  %1 = zext i1 %0 to i8
  store i8 %1, ptr %_0, align 1
  br label %bb16

bb11:                                             ; preds = %bb10
  %_31 = load ptr, ptr %px, align 8
; call core::ptr::read_unaligned
  %vx4 = call i32 @_ZN4core3ptr14read_unaligned17h6401ad35ba225740E(ptr %_31)
  %_34 = load ptr, ptr %py, align 8
; call core::ptr::read_unaligned
  %vy5 = call i32 @_ZN4core3ptr14read_unaligned17h6401ad35ba225740E(ptr %_34)
  %_35 = icmp ne i32 %vx4, %vy5
  br i1 %_35, label %bb12, label %bb13

bb16:                                             ; preds = %bb15, %bb12, %bb14
  %2 = load i8, ptr %_0, align 1
  %3 = trunc nuw i8 %2 to i1
  ret i1 %3

bb13:                                             ; preds = %bb11
  %self6 = load ptr, ptr %px, align 8
  %_36 = getelementptr inbounds nuw i8, ptr %self6, i64 4
  store ptr %_36, ptr %px, align 8
  %self7 = load ptr, ptr %py, align 8
  %_38 = getelementptr inbounds nuw i8, ptr %self7, i64 4
  store ptr %_38, ptr %py, align 8
  br label %bb10

bb12:                                             ; preds = %bb11
  store i8 0, ptr %_0, align 1
  br label %bb16

bb3:                                              ; preds = %bb8, %bb1
; call <core::iter::adapters::zip::Zip<A,B> as core::iter::adapters::zip::ZipImpl<A,B>>::next
  %4 = call { ptr, ptr } @"_ZN111_$LT$core..iter..adapters..zip..Zip$LT$A$C$B$GT$$u20$as$u20$core..iter..adapters..zip..ZipImpl$LT$A$C$B$GT$$GT$4next17h17d502eef7beb895E"(ptr align 8 %iter)
  %5 = extractvalue { ptr, ptr } %4, 0
  %6 = extractvalue { ptr, ptr } %4, 1
  store ptr %5, ptr %_10, align 8
  %7 = getelementptr inbounds i8, ptr %_10, i64 8
  store ptr %6, ptr %7, align 8
  %8 = load ptr, ptr %_10, align 8
  %9 = getelementptr inbounds i8, ptr %_10, i64 8
  %10 = load ptr, ptr %9, align 8
  %11 = ptrtoint ptr %8 to i64
  %12 = icmp eq i64 %11, 0
  %_12 = select i1 %12, i64 0, i64 1
  %13 = trunc nuw i64 %_12 to i1
  br i1 %13, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %_44 = load ptr, ptr %_10, align 8
  %b1 = load i8, ptr %_44, align 1
  %14 = getelementptr inbounds i8, ptr %_10, i64 8
  %_45 = load ptr, ptr %14, align 8
  %b2 = load i8, ptr %_45, align 1
  %_15 = icmp ne i8 %b1, %b2
  br i1 %_15, label %bb7, label %bb8

bb6:                                              ; preds = %bb3
  store i8 1, ptr %_0, align 1
  br label %bb15

bb15:                                             ; preds = %bb7, %bb6
  br label %bb16

bb8:                                              ; preds = %bb5
  br label %bb3

bb7:                                              ; preds = %bb5
  store i8 0, ptr %_0, align 1
  br label %bb15

bb4:                                              ; No predecessors!
  unreachable
}

; core::char::convert::from_u32_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4char7convert18from_u32_unchecked18precondition_check17h9b90564ee3fe611bE(i32 %i) unnamed_addr #0 {
start:
  %_3 = alloca [4 x i8], align 4
  %self = xor i32 %i, 55296
  %_6 = sub i32 %self, 2048
  %_5 = icmp uge i32 %_6, 1112064
  br i1 %_5, label %bb3, label %bb4

bb4:                                              ; preds = %start
  store i32 %i, ptr %_3, align 4
  br label %bb5

bb3:                                              ; preds = %start
  store i32 1114112, ptr %_3, align 4
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %0 = load i32, ptr %_3, align 4
  %1 = icmp eq i32 %0, 1114112
  %_9 = select i1 %1, i64 1, i64 0
  %2 = icmp eq i64 %_9, 0
  br i1 %2, label %bb1, label %bb2

bb1:                                              ; preds = %bb5
  ret void

bb2:                                              ; preds = %bb5
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_4dc07e69e5d34e9d1484dfbbef0bb9b1, i64 174) #18
  unreachable
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17h729df72228684868E(i1 zeroext %cond) unnamed_addr #0 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17h9eae9061f7a95b34E() unnamed_addr #0 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #18
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hd1e7f34fc82c970eE"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17ha95a14a4f1b9db44E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::double_ended::DoubleEndedIterator for core::ops::range::Range<A>>::next_back
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter5range116_$LT$impl$u20$core..iter..traits..double_ended..DoubleEndedIterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$9next_back17h3996d4f11e30d459E"(ptr align 8 %self) unnamed_addr #1 {
start:
; call <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next_back
  %0 = call { i64, i64 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$14spec_next_back17hbcf64cad164e1fdaE"(ptr align 8 %self)
  %_0.0 = extractvalue { i64, i64 } %0, 0
  %_0.1 = extractvalue { i64, i64 } %0, 1
  %1 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %2 = insertvalue { i64, i64 } %1, i64 %_0.1, 1
  ret { i64, i64 } %2
}

; core::iter::traits::double_ended::DoubleEndedIterator::rfind::check::{{closure}}
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter6traits12double_ended19DoubleEndedIterator5rfind5check28_$u7b$$u7b$closure$u7d$$u7d$17h600f7087f4d6eaa5E"(ptr align 8 %_1, i64 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_0 = alloca [16 x i8], align 8
  %x = alloca [8 x i8], align 8
  store i64 %0, ptr %x, align 8
; invoke core::str::pattern::simd_contains::{{closure}}
  %_4 = invoke zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17h7de5f766c273ea1dE"(ptr align 8 %_1, ptr align 8 %x)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind to caller

funclet_bb5:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_4, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb1
  %_8 = load i64, ptr %x, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_8, ptr %1, align 8
  store i64 1, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %2 = load i64, ptr %_0, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = insertvalue { i64, i64 } poison, i64 %2, 0
  %6 = insertvalue { i64, i64 } %5, i64 %4, 1
  ret { i64, i64 } %6
}

; core::iter::traits::double_ended::DoubleEndedIterator::try_rfold
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core4iter6traits12double_ended19DoubleEndedIterator9try_rfold17h5ef4f9bf880ffaecE(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_17 = alloca [1 x i8], align 1
  %_8 = alloca [16 x i8], align 8
  %_5 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  store i8 1, ptr %_17, align 1
  br label %bb1

bb1:                                              ; preds = %bb7, %start
; invoke core::iter::range::<impl core::iter::traits::double_ended::DoubleEndedIterator for core::ops::range::Range<A>>::next_back
  %0 = invoke { i64, i64 } @"_ZN4core4iter5range116_$LT$impl$u20$core..iter..traits..double_ended..DoubleEndedIterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$9next_back17h3996d4f11e30d459E"(ptr align 8 %self)
          to label %bb2 unwind label %funclet_bb17

bb17:                                             ; preds = %funclet_bb17
  %1 = load i8, ptr %_17, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb16, label %bb14

funclet_bb17:                                     ; preds = %bb11, %bb8, %bb4, %bb3, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb17

bb2:                                              ; preds = %bb1
  %3 = extractvalue { i64, i64 } %0, 0
  %4 = extractvalue { i64, i64 } %0, 1
  store i64 %3, ptr %_5, align 8
  %5 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %4, ptr %5, align 8
  %_6 = load i64, ptr %_5, align 8
  %6 = getelementptr inbounds i8, ptr %_5, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = trunc nuw i64 %_6 to i1
  br i1 %8, label %bb3, label %bb10

bb3:                                              ; preds = %bb2
  %9 = getelementptr inbounds i8, ptr %_5, i64 8
  %x = load i64, ptr %9, align 8
  store i8 0, ptr %_17, align 1
; invoke core::iter::traits::double_ended::DoubleEndedIterator::rfind::check::{{closure}}
  %10 = invoke { i64, i64 } @"_ZN4core4iter6traits12double_ended19DoubleEndedIterator5rfind5check28_$u7b$$u7b$closure$u7d$$u7d$17h600f7087f4d6eaa5E"(ptr align 8 %f, i64 %x)
          to label %bb4 unwind label %funclet_bb17

bb10:                                             ; preds = %bb2
  br label %bb11

bb4:                                              ; preds = %bb3
  %_9.0 = extractvalue { i64, i64 } %10, 0
  %_9.1 = extractvalue { i64, i64 } %10, 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
  %11 = invoke { i64, i64 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h469d74f6a6b7eb2eE"(i64 %_9.0, i64 %_9.1)
          to label %bb5 unwind label %funclet_bb17

bb5:                                              ; preds = %bb4
  %12 = extractvalue { i64, i64 } %11, 0
  %13 = extractvalue { i64, i64 } %11, 1
  store i64 %12, ptr %_8, align 8
  %14 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %13, ptr %14, align 8
  %_13 = load i64, ptr %_8, align 8
  %15 = getelementptr inbounds i8, ptr %_8, i64 8
  %16 = load i64, ptr %15, align 8
  %17 = trunc nuw i64 %_13 to i1
  br i1 %17, label %bb8, label %bb7

bb8:                                              ; preds = %bb5
  %18 = getelementptr inbounds i8, ptr %_8, i64 8
  %residual = load i64, ptr %18, align 8
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
  %19 = invoke { i64, i64 } @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17hfe5976fe6c5a8973E"(i64 %residual)
          to label %bb9 unwind label %funclet_bb17

bb7:                                              ; preds = %bb5
  store i8 1, ptr %_17, align 1
  br label %bb1

bb9:                                              ; preds = %bb8
  %20 = extractvalue { i64, i64 } %19, 0
  %21 = extractvalue { i64, i64 } %19, 1
  store i64 %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb13

bb13:                                             ; preds = %bb12, %bb9
  %23 = load i64, ptr %_0, align 8
  %24 = getelementptr inbounds i8, ptr %_0, i64 8
  %25 = load i64, ptr %24, align 8
  %26 = insertvalue { i64, i64 } poison, i64 %23, 0
  %27 = insertvalue { i64, i64 } %26, i64 %25, 1
  ret { i64, i64 } %27

bb11:                                             ; preds = %bb10
  store i8 0, ptr %_17, align 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
  %28 = invoke { i64, i64 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17hfcc4a54d575fe48fE"()
          to label %bb12 unwind label %funclet_bb17

bb12:                                             ; preds = %bb11
  %29 = extractvalue { i64, i64 } %28, 0
  %30 = extractvalue { i64, i64 } %28, 1
  store i64 %29, ptr %_0, align 8
  %31 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %30, ptr %31, align 8
  br label %bb13

bb6:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb16, %bb17
  cleanupret from %cleanuppad unwind to caller

bb16:                                             ; preds = %bb17
  br label %bb14
}

; core::iter::traits::iterator::Iterator::any::check::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core4iter6traits8iterator8Iterator3any5check28_$u7b$$u7b$closure$u7d$$u7d$17hda4813f019edb11cE"(ptr align 8 %_1, ptr align 1 %x.0, i64 %x.1) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
; call core::str::pattern::simd_contains::{{closure}}
  %_4 = call zeroext i1 @"_ZN4core3str7pattern13simd_contains28_$u7b$$u7b$closure$u7d$$u7d$17hb3951c5ed916f9c9E"(ptr align 8 %_1, ptr align 1 %x.0, i64 %x.1)
  br i1 %_4, label %bb2, label %bb3

bb3:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb2:                                              ; preds = %start
  store i8 1, ptr %_0, align 1
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %0 = load i8, ptr %_0, align 1
  %1 = trunc nuw i8 %0 to i1
  ret i1 %1
}

; core::iter::traits::iterator::Iterator::zip
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator3zip17h15f29d0915aa0627E(ptr sret([56 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_5 = alloca [1 x i8], align 1
  store i8 1, ptr %_5, align 1
; invoke core::slice::iter::<impl core::iter::traits::collect::IntoIterator for &[T]>::into_iter
  %0 = invoke { ptr, ptr } @"_ZN4core5slice4iter87_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u5d$$GT$9into_iter17h720ba0736b985f78E"(ptr align 1 %other.0, i64 %other.1)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  %1 = load i8, ptr %_5, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb3, label %bb2

funclet_bb4:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  %b.0 = extractvalue { ptr, ptr } %0, 0
  %b.1 = extractvalue { ptr, ptr } %0, 1
  store i8 0, ptr %_5, align 1
; invoke <core::iter::adapters::zip::Zip<A,B> as core::iter::adapters::zip::ZipImpl<A,B>>::new
  invoke void @"_ZN111_$LT$core..iter..adapters..zip..Zip$LT$A$C$B$GT$$u20$as$u20$core..iter..adapters..zip..ZipImpl$LT$A$C$B$GT$$GT$3new17ha26505436e2ca055E"(ptr sret([56 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr %b.0, ptr %b.1)
          to label %bb5 unwind label %funclet_bb4

bb5:                                              ; preds = %bb1
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; preds = %bb4
  br label %bb2
}

; core::iter::traits::iterator::Iterator::try_fold
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core4iter6traits8iterator8Iterator8try_fold17h9f18bf4acbcdd83aE(ptr align 8 %self, ptr align 8 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_17 = alloca [1 x i8], align 1
  %_5 = alloca [16 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %f = alloca [8 x i8], align 8
  store ptr %0, ptr %f, align 8
  store i8 1, ptr %_17, align 1
  br label %bb1

bb1:                                              ; preds = %bb7, %start
; invoke <core::slice::iter::Windows<T> as core::iter::traits::iterator::Iterator>::next
  %1 = invoke { ptr, i64 } @"_ZN94_$LT$core..slice..iter..Windows$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h8639275e5b6423e2E"(ptr align 8 %self)
          to label %bb2 unwind label %funclet_bb17

bb17:                                             ; preds = %funclet_bb17
  %2 = load i8, ptr %_17, align 1
  %3 = trunc nuw i8 %2 to i1
  br i1 %3, label %bb16, label %bb14

funclet_bb17:                                     ; preds = %bb11, %bb8, %bb4, %bb3, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb17

bb2:                                              ; preds = %bb1
  %4 = extractvalue { ptr, i64 } %1, 0
  %5 = extractvalue { ptr, i64 } %1, 1
  store ptr %4, ptr %_5, align 8
  %6 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %5, ptr %6, align 8
  %7 = load ptr, ptr %_5, align 8
  %8 = getelementptr inbounds i8, ptr %_5, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = ptrtoint ptr %7 to i64
  %11 = icmp eq i64 %10, 0
  %_6 = select i1 %11, i64 0, i64 1
  %12 = trunc nuw i64 %_6 to i1
  br i1 %12, label %bb3, label %bb10

bb3:                                              ; preds = %bb2
  %x.0 = load ptr, ptr %_5, align 8
  %13 = getelementptr inbounds i8, ptr %_5, i64 8
  %x.1 = load i64, ptr %13, align 8
  store i8 0, ptr %_17, align 1
; invoke core::iter::traits::iterator::Iterator::any::check::{{closure}}
  %_9 = invoke zeroext i1 @"_ZN4core4iter6traits8iterator8Iterator3any5check28_$u7b$$u7b$closure$u7d$$u7d$17hda4813f019edb11cE"(ptr align 8 %f, ptr align 1 %x.0, i64 %x.1)
          to label %bb4 unwind label %funclet_bb17

bb10:                                             ; preds = %bb2
  br label %bb11

bb4:                                              ; preds = %bb3
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
  %_8 = invoke zeroext i1 @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17hcaab5eb48c5ad543E"(i1 zeroext %_9)
          to label %bb5 unwind label %funclet_bb17

bb5:                                              ; preds = %bb4
  %_13 = zext i1 %_8 to i64
  %14 = trunc nuw i64 %_13 to i1
  br i1 %14, label %bb8, label %bb7

bb8:                                              ; preds = %bb5
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
  %15 = invoke zeroext i1 @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17h6f76164e010a751cE"()
          to label %bb9 unwind label %funclet_bb17

bb7:                                              ; preds = %bb5
  store i8 1, ptr %_17, align 1
  br label %bb1

bb9:                                              ; preds = %bb8
  %16 = zext i1 %15 to i8
  store i8 %16, ptr %_0, align 1
  br label %bb13

bb13:                                             ; preds = %bb12, %bb9
  %17 = load i8, ptr %_0, align 1
  %18 = trunc nuw i8 %17 to i1
  ret i1 %18

bb11:                                             ; preds = %bb10
  store i8 0, ptr %_17, align 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
  %19 = invoke zeroext i1 @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h5d70df295b1066c9E"()
          to label %bb12 unwind label %funclet_bb17

bb12:                                             ; preds = %bb11
  %20 = zext i1 %19 to i8
  store i8 %20, ptr %_0, align 1
  br label %bb13

bb6:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb16, %bb17
  cleanupret from %cleanuppad unwind to caller

bb16:                                             ; preds = %bb17
  br label %bb14
}

; core::iter::traits::iterator::Iterator::enumerate
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h8f8e741f03926ee9E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  store ptr %self.0, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.1, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %1, align 8
  ret void
}

; core::iter::adapters::zip::TrustedRandomAccessNoCoerce::size
; Function Attrs: uwtable
define internal i64 @_ZN4core4iter8adapters3zip27TrustedRandomAccessNoCoerce4size17h2511ccbe9624bb78E(ptr align 8 %self) unnamed_addr #2 {
start:
  %_2 = alloca [24 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17heafc30ce1e2a6791E"(ptr sret([24 x i8]) align 8 %_2, ptr align 8 %self)
  %_0 = load i64, ptr %_2, align 8
  ret i64 %_0
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hc0bd40f78d0bd31cE(ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h8ef25de63334e3faE(i64 %size, i64 %align) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #21 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #18
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h38c8fb1fafc19f9eE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h8ef25de63334e3faE(i64 %new_size, i64 %_23) #20
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hc0bd40f78d0bd31cE(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h860d66e6a6dd6688E"(ptr align 4 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hfe9af102e50bf4f5E"(ptr align 4 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17he79ed7524be5e0e9E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #19
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #21 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #18
  unreachable
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hfe9af102e50bf4f5E"(ptr align 4 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::slice::iter::<impl core::iter::traits::collect::IntoIterator for &[T]>::into_iter
; Function Attrs: uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter87_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u5d$$GT$9into_iter17h720ba0736b985f78E"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i8, ptr %self.0, i64 %self.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::slice::memchr::memchr
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5slice6memchr6memchr17h45153c8f6dd61448E(i8 %x, ptr align 1 %text.0, i64 %text.1) unnamed_addr #1 {
start:
  %i = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %_3 = icmp ult i64 %text.1, 16
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::slice::memchr::memchr_aligned
  %0 = call { i64, i64 } @_ZN4core5slice6memchr14memchr_aligned17hcb56f13120f06db1E(i8 %x, ptr align 1 %text.0, i64 %text.1)
  %1 = extractvalue { i64, i64 } %0, 0
  %2 = extractvalue { i64, i64 } %0, 1
  store i64 %1, ptr %_0, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %2, ptr %3, align 8
  br label %bb3

bb1:                                              ; preds = %start
  store i64 0, ptr %i, align 8
  br label %bb5

bb3:                                              ; preds = %bb4, %bb2
  %4 = load i64, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = insertvalue { i64, i64 } poison, i64 %4, 0
  %8 = insertvalue { i64, i64 } %7, i64 %6, 1
  ret { i64, i64 } %8

bb5:                                              ; preds = %bb9, %bb1
  %_7 = load i64, ptr %i, align 8
  %_6 = icmp ult i64 %_7, %text.1
  br i1 %_6, label %bb6, label %bb10

bb10:                                             ; preds = %bb5
  %9 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %10 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %9, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %10, ptr %11, align 8
  br label %bb4

bb6:                                              ; preds = %bb5
  %_10 = load i64, ptr %i, align 8
  %_11 = icmp ult i64 %_10, %text.1
  br i1 %_11, label %bb7, label %panic

bb4:                                              ; preds = %bb8, %bb10
  br label %bb3

bb7:                                              ; preds = %bb6
  %12 = getelementptr inbounds nuw i8, ptr %text.0, i64 %_10
  %_9 = load i8, ptr %12, align 1
  %_8 = icmp eq i8 %_9, %x
  br i1 %_8, label %bb8, label %bb9

panic:                                            ; preds = %bb6
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_10, i64 %text.1, ptr align 8 @alloc_c4a687d06f875c3cd6d9b59a57eb65ae) #19
  unreachable

bb9:                                              ; preds = %bb7
  %13 = load i64, ptr %i, align 8
  %14 = add i64 %13, 1
  store i64 %14, ptr %i, align 8
  br label %bb5

bb8:                                              ; preds = %bb7
  %_12 = load i64, ptr %i, align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_12, ptr %15, align 8
  store i64 1, ptr %_0, align 8
  br label %bb4
}

; core::option::Option<T>::map_or_else
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hffd784a61d128740E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 8 %default) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_9 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  store i8 1, ptr %_10, align 1
  store i8 1, ptr %_9, align 1
  %3 = load ptr, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = ptrtoint ptr %3 to i64
  %7 = icmp eq i64 %6, 0
  %_4 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_4 to i1
  br i1 %8, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %t.0 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %9, align 8
  store i8 0, ptr %_9, align 1
; invoke core::ops::function::FnOnce::call_once
  invoke void @_ZN4core3ops8function6FnOnce9call_once17hd711498901682c7fE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %t.0, i64 %t.1)
          to label %bb4 unwind label %funclet_bb11

bb2:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke alloc::fmt::format::{{closure}}
  invoke void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h7d044deb4b335858E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %default)
          to label %bb5 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %10 = load i8, ptr %_9, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb10, label %bb11_cleanup_trampoline_bb7

funclet_bb11:                                     ; preds = %bb3, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb2
  br label %bb6

bb6:                                              ; preds = %bb9, %bb4, %bb5
  ret void

bb4:                                              ; preds = %bb3
  %12 = load i8, ptr %_10, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

bb9:                                              ; preds = %bb4
  br label %bb6

bb7:                                              ; preds = %funclet_bb7
  %14 = load i8, ptr %_10, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb12, label %bb8

funclet_bb7:                                      ; preds = %bb10, %bb11_cleanup_trampoline_bb7
  %cleanuppad1 = cleanuppad within none []
  br label %bb7

bb11_cleanup_trampoline_bb7:                      ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb10:                                             ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb8:                                              ; preds = %bb12, %bb7
  cleanupret from %cleanuppad1 unwind to caller

bb12:                                             ; preds = %bb7
  br label %bb8

bb1:                                              ; No predecessors!
  unreachable
}

; core::core_simd::masks::<impl core::core_simd::masks::sealed::Sealed for i8>::valid
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core9core_simd5masks71_$LT$impl$u20$core..core_simd..masks..sealed..Sealed$u20$for$u20$i8$GT$5valid17h9f67b3d8761714e0E"(<16 x i8> %value) unnamed_addr #1 {
start:
  %0 = alloca [1 x i8], align 1
  %1 = alloca [16 x i8], align 16
  %2 = alloca [16 x i8], align 16
  %3 = alloca [16 x i8], align 16
  %4 = alloca [1 x i8], align 1
  %5 = alloca [16 x i8], align 16
  %6 = alloca [16 x i8], align 16
  %7 = alloca [1 x i8], align 1
  %array3 = alloca [1 x i8], align 1
  %array2 = alloca [1 x i8], align 1
  %array1 = alloca [1 x i8], align 1
  %array = alloca [1 x i8], align 1
  %8 = getelementptr inbounds nuw i8, ptr %array, i64 0
  store i8 0, ptr %8, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array1, ptr align 1 %array, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %7, ptr %array1)
  %vector = load <1 x i8>, ptr %7, align 1
  %9 = shufflevector <1 x i8> %vector, <1 x i8> %vector, <16 x i32> zeroinitializer
  store <16 x i8> %9, ptr %6, align 16
  %_3 = load <16 x i8>, ptr %6, align 16
  %10 = icmp eq <16 x i8> %value, %_3
  %11 = sext <16 x i1> %10 to <16 x i8>
  store <16 x i8> %11, ptr %5, align 16
  %falses = load <16 x i8>, ptr %5, align 16
  %12 = getelementptr inbounds nuw i8, ptr %array2, i64 0
  store i8 -1, ptr %12, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array3, ptr align 1 %array2, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %4, ptr %array3)
  %vector4 = load <1 x i8>, ptr %4, align 1
  %13 = shufflevector <1 x i8> %vector4, <1 x i8> %vector4, <16 x i32> zeroinitializer
  store <16 x i8> %13, ptr %3, align 16
  %_5 = load <16 x i8>, ptr %3, align 16
  %14 = icmp eq <16 x i8> %value, %_5
  %15 = sext <16 x i1> %14 to <16 x i8>
  store <16 x i8> %15, ptr %2, align 16
  %trues = load <16 x i8>, ptr %2, align 16
  %16 = or <16 x i8> %falses, %trues
  store <16 x i8> %16, ptr %1, align 16
  %valid = load <16 x i8>, ptr %1, align 16
  %17 = lshr <16 x i8> %valid, splat (i8 7)
  %18 = trunc <16 x i8> %17 to <16 x i1>
  %19 = call i1 @llvm.vector.reduce.and.v16i1(<16 x i1> %18)
  %20 = zext i1 %19 to i8
  store i8 %20, ptr %0, align 1
  %21 = load i8, ptr %0, align 1
  %_0 = trunc nuw i8 %21 to i1
  ret i1 %_0
}

; core::core_simd::masks::mask_impl::Mask<T,_>::to_bitmask_integer
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core9core_simd5masks9mask_impl17Mask$LT$T$C$_$GT$18to_bitmask_integer17h481681f9d696fef7E"(<16 x i8> %self) unnamed_addr #1 {
start:
  %0 = alloca [1 x i8], align 1
  %1 = alloca [8 x i8], align 8
  %2 = alloca [16 x i8], align 16
  %3 = alloca [1 x i8], align 1
  %4 = alloca [2 x i8], align 2
  %5 = alloca [16 x i8], align 16
  %6 = alloca [16 x i8], align 16
  %7 = alloca [1 x i8], align 1
  %8 = alloca [4 x i8], align 4
  %9 = alloca [32 x i8], align 32
  %10 = alloca [16 x i8], align 16
  %11 = alloca [1 x i8], align 1
  %12 = alloca [64 x i8], align 64
  %13 = alloca [16 x i8], align 16
  %14 = alloca [1 x i8], align 1
  %array7 = alloca [1 x i8], align 1
  %array6 = alloca [1 x i8], align 1
  %array5 = alloca [1 x i8], align 1
  %array4 = alloca [1 x i8], align 1
  %array3 = alloca [1 x i8], align 1
  %array2 = alloca [1 x i8], align 1
  %array1 = alloca [1 x i8], align 1
  %array = alloca [1 x i8], align 1
  %bitmask = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  br label %bb3

bb3:                                              ; preds = %bb2
  %15 = getelementptr inbounds nuw i8, ptr %array2, i64 0
  store i8 0, ptr %15, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array3, ptr align 1 %array2, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %7, ptr %array3)
  %vector12 = load <1 x i8>, ptr %7, align 1
  %16 = shufflevector <1 x i8> %vector12, <1 x i8> %vector12, <16 x i32> zeroinitializer
  store <16 x i8> %16, ptr %6, align 16
  %second13 = load <16 x i8>, ptr %6, align 16
  %17 = shufflevector <16 x i8> %self, <16 x i8> %second13, <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
  store <16 x i8> %17, ptr %5, align 16
  %resized14 = load <16 x i8>, ptr %5, align 16
  %18 = lshr <16 x i8> %resized14, splat (i8 7)
  %19 = trunc <16 x i8> %18 to <16 x i1>
  %20 = bitcast <16 x i1> %19 to i16
  store i16 %20, ptr %4, align 2
  %bitmask15 = load i16, ptr %4, align 2
  %21 = zext i16 %bitmask15 to i64
  store i64 %21, ptr %bitmask, align 8
  br label %bb8

bb4:                                              ; No predecessors!
  br label %bb5

bb5:                                              ; preds = %bb4
  %22 = getelementptr inbounds nuw i8, ptr %array4, i64 0
  store i8 0, ptr %22, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array5, ptr align 1 %array4, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %11, ptr %array5)
  %vector8 = load <1 x i8>, ptr %11, align 1
  %23 = shufflevector <1 x i8> %vector8, <1 x i8> %vector8, <16 x i32> zeroinitializer
  store <16 x i8> %23, ptr %10, align 16
  %second9 = load <16 x i8>, ptr %10, align 16
  %24 = shufflevector <16 x i8> %self, <16 x i8> %second9, <32 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16>
  store <32 x i8> %24, ptr %9, align 32
  %resized10 = load <32 x i8>, ptr %9, align 32
  %25 = lshr <32 x i8> %resized10, splat (i8 7)
  %26 = trunc <32 x i8> %25 to <32 x i1>
  %27 = bitcast <32 x i1> %26 to i32
  store i32 %27, ptr %8, align 4
  %bitmask11 = load i32, ptr %8, align 4
  %28 = zext i32 %bitmask11 to i64
  store i64 %28, ptr %bitmask, align 8
  br label %bb7

bb6:                                              ; No predecessors!
  %29 = getelementptr inbounds nuw i8, ptr %array6, i64 0
  store i8 0, ptr %29, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array7, ptr align 1 %array6, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %14, ptr %array7)
  %vector = load <1 x i8>, ptr %14, align 1
  %30 = shufflevector <1 x i8> %vector, <1 x i8> %vector, <16 x i32> zeroinitializer
  store <16 x i8> %30, ptr %13, align 16
  %second = load <16 x i8>, ptr %13, align 16
  %31 = shufflevector <16 x i8> %self, <16 x i8> %second, <64 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16, i32 16>
  store <64 x i8> %31, ptr %12, align 64
  %resized = load <64 x i8>, ptr %12, align 64
  %32 = lshr <64 x i8> %resized, splat (i8 7)
  %33 = trunc <64 x i8> %32 to <64 x i1>
  %34 = bitcast <64 x i1> %33 to i64
  store i64 %34, ptr %bitmask, align 8
  br label %bb7

bb7:                                              ; preds = %bb5, %bb6
  br label %bb8

bb8:                                              ; preds = %bb3, %bb7
  br label %bb9

bb9:                                              ; preds = %bb1, %bb8
  %35 = load i64, ptr %bitmask, align 8
  ret i64 %35

bb1:                                              ; No predecessors!
  %36 = getelementptr inbounds nuw i8, ptr %array, i64 0
  store i8 0, ptr %36, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %array1, ptr align 1 %array, i64 1, i1 false)
; call core::core_simd::vector::Simd<T,_>::load
  call void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %3, ptr %array1)
  %vector16 = load <1 x i8>, ptr %3, align 1
  %37 = shufflevector <1 x i8> %vector16, <1 x i8> %vector16, <16 x i32> zeroinitializer
  store <16 x i8> %37, ptr %2, align 16
  %second17 = load <16 x i8>, ptr %2, align 16
  %38 = shufflevector <16 x i8> %self, <16 x i8> %second17, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  store <8 x i8> %38, ptr %1, align 8
  %resized18 = load <8 x i8>, ptr %1, align 8
  %39 = lshr <8 x i8> %resized18, splat (i8 7)
  %40 = trunc <8 x i8> %39 to <8 x i1>
  %41 = bitcast <8 x i1> %40 to i8
  store i8 %41, ptr %0, align 1
  %bitmask19 = load i8, ptr %0, align 1
  %42 = zext i8 %bitmask19 to i64
  store i64 %42, ptr %bitmask, align 8
  br label %bb9
}

; core::core_simd::vector::Simd<T,_>::load
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h1ccaa8d76e43a7a9E"(ptr sret([1 x i8]) align 1 %_0, ptr %ptr) unnamed_addr #1 {
start:
  %tmp = alloca [1 x i8], align 1
  br label %bb1

bb1:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %ptr, ptr %tmp, i64 1, i64 1, i64 1) #20
  br label %bb3

bb3:                                              ; preds = %bb1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %tmp, ptr align 1 %ptr, i64 1, i1 false)
  %self = load <1 x i8>, ptr %tmp, align 1
  store <1 x i8> %self, ptr %_0, align 1
  ret void
}

; core::core_simd::vector::Simd<T,_>::load
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core9core_simd6vector17Simd$LT$T$C$_$GT$4load17h37cc6d4fb7dd7cc4E"(ptr sret([1 x i8]) align 1 %_0, ptr %ptr) unnamed_addr #1 {
start:
  %tmp = alloca [1 x i8], align 1
  br label %bb1

bb1:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %ptr, ptr %tmp, i64 1, i64 1, i64 1) #20
  br label %bb3

bb3:                                              ; preds = %bb1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %tmp, ptr align 1 %ptr, i64 1, i1 false)
  %self = load <1 x i8>, ptr %tmp, align 1
  store <1 x i8> %self, ptr %_0, align 1
  ret void
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h582ff1bb5db590feE(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #1 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #18
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hccb9f7d5a3f43b78E"() unnamed_addr #1 {
start:
  ret i32 0
}

; <&str as core::str::pattern::Pattern>::is_contained_in
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN55_$LT$$RF$str$u20$as$u20$core..str..pattern..Pattern$GT$15is_contained_in17h14a198d9036d5031E"(ptr align 1 %0, i64 %1, ptr align 1 %2, i64 %3) unnamed_addr #1 {
start:
  %_29 = alloca [16 x i8], align 8
  %other = alloca [16 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_19 = alloca [104 x i8], align 8
  %_17 = alloca [24 x i8], align 8
  %_13 = alloca [1 x i8], align 1
  %_8 = alloca [8 x i8], align 8
  %_6 = alloca [8 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %haystack = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %4, align 8
  store ptr %2, ptr %haystack, align 8
  %5 = getelementptr inbounds i8, ptr %haystack, i64 8
  store i64 %3, ptr %5, align 8
  %_22.0 = load ptr, ptr %self, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %6, align 8
  %7 = icmp eq i64 %_22.1, 0
  br i1 %7, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %_0, align 1
  br label %bb16

bb2:                                              ; preds = %start
  store i64 %_22.1, ptr %_6, align 8
  %self.0 = load ptr, ptr %haystack, align 8
  %8 = getelementptr inbounds i8, ptr %haystack, i64 8
  %self.1 = load i64, ptr %8, align 8
  store i64 %self.1, ptr %_8, align 8
  %9 = load i64, ptr %_8, align 8
  %_4 = call i8 @llvm.ucmp.i8.i64(i64 %_22.1, i64 %9)
  %10 = icmp eq i8 %_4, -1
  br i1 %10, label %bb4, label %bb3

bb16:                                             ; preds = %bb14, %bb15, %bb1
  %11 = load i8, ptr %_0, align 1
  %12 = trunc nuw i8 %11 to i1
  ret i1 %12

bb4:                                              ; preds = %bb2
  %13 = icmp eq i64 %_22.1, 1
  br i1 %13, label %bb5, label %bb7

bb3:                                              ; preds = %bb2
  store ptr %_22.0, ptr %self1, align 8
  %14 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %_22.1, ptr %14, align 8
  store ptr %self.0, ptr %other, align 8
  %15 = getelementptr inbounds i8, ptr %other, i64 8
  store i64 %self.1, ptr %15, align 8
; call <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
  %16 = call zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17h7715a4528a246ec0E"(ptr align 1 %_22.0, i64 %_22.1, ptr align 1 %self.0, i64 %self.1)
  %17 = zext i1 %16 to i8
  store i8 %17, ptr %_0, align 1
  br label %bb14

bb5:                                              ; preds = %bb4
  %_11 = icmp ult i64 0, %_22.1
  br i1 %_11, label %bb6, label %panic

bb7:                                              ; preds = %bb4
  %_12 = icmp ule i64 %_22.1, 32
  br i1 %_12, label %bb8, label %bb12

bb6:                                              ; preds = %bb5
  %x = getelementptr inbounds nuw i8, ptr %_22.0, i64 0
  %_30 = load i8, ptr %x, align 1
; call core::slice::memchr::memchr
  %18 = call { i64, i64 } @_ZN4core5slice6memchr6memchr17h45153c8f6dd61448E(i8 %_30, ptr align 1 %self.0, i64 %self.1)
  %19 = extractvalue { i64, i64 } %18, 0
  %20 = extractvalue { i64, i64 } %18, 1
  store i64 %19, ptr %_29, align 8
  %21 = getelementptr inbounds i8, ptr %_29, i64 8
  store i64 %20, ptr %21, align 8
  %_31 = load i64, ptr %_29, align 8
  %22 = getelementptr inbounds i8, ptr %_29, i64 8
  %23 = load i64, ptr %22, align 8
  %24 = icmp eq i64 %_31, 1
  %25 = zext i1 %24 to i8
  store i8 %25, ptr %_0, align 1
  br label %bb15

panic:                                            ; preds = %bb5
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 0, i64 %_22.1, ptr align 8 @alloc_3ec2d20c60de7d2fe53bb70f0ca537b8) #19
  unreachable

bb15:                                             ; preds = %bb10, %bb6
  br label %bb16

bb12:                                             ; preds = %bb11, %bb7
  %26 = load ptr, ptr %haystack, align 8
  %27 = getelementptr inbounds i8, ptr %haystack, i64 8
  %28 = load i64, ptr %27, align 8
  %29 = load ptr, ptr %self, align 8
  %30 = getelementptr inbounds i8, ptr %self, i64 8
  %31 = load i64, ptr %30, align 8
; call core::str::pattern::StrSearcher::new
  call void @_ZN4core3str7pattern11StrSearcher3new17h35c9b30eeae16457E(ptr sret([104 x i8]) align 8 %_19, ptr align 1 %26, i64 %28, ptr align 1 %29, i64 %31)
; call <core::str::pattern::StrSearcher as core::str::pattern::Searcher>::next_match
  call void @"_ZN80_$LT$core..str..pattern..StrSearcher$u20$as$u20$core..str..pattern..Searcher$GT$10next_match17h575393af1014bbfbE"(ptr sret([24 x i8]) align 8 %_17, ptr align 8 %_19)
  %_32 = load i64, ptr %_17, align 8
  %32 = icmp eq i64 %_32, 1
  %33 = zext i1 %32 to i8
  store i8 %33, ptr %_0, align 1
  br label %bb14

bb8:                                              ; preds = %bb7
  %34 = load ptr, ptr %self, align 8
  %35 = getelementptr inbounds i8, ptr %self, i64 8
  %36 = load i64, ptr %35, align 8
  %37 = load ptr, ptr %haystack, align 8
  %38 = getelementptr inbounds i8, ptr %haystack, i64 8
  %39 = load i64, ptr %38, align 8
; call core::str::pattern::simd_contains
  %40 = call i8 @_ZN4core3str7pattern13simd_contains17h54b071f46a772999E(ptr align 1 %34, i64 %36, ptr align 1 %37, i64 %39)
  store i8 %40, ptr %_13, align 1
  %41 = load i8, ptr %_13, align 1
  %42 = icmp eq i8 %41, 2
  %_14 = select i1 %42, i64 0, i64 1
  %43 = trunc nuw i64 %_14 to i1
  br i1 %43, label %bb10, label %bb11

bb10:                                             ; preds = %bb8
  %44 = load i8, ptr %_13, align 1
  %result = trunc nuw i8 %44 to i1
  %45 = zext i1 %result to i8
  store i8 %45, ptr %_0, align 1
  br label %bb15

bb11:                                             ; preds = %bb8
  br label %bb12

bb14:                                             ; preds = %bb3, %bb12
  br label %bb16

bb20:                                             ; No predecessors!
  unreachable
}

; alloc::fmt::format
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc3fmt6format17h220daf6567fc508cE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %args) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  %_6.0 = load ptr, ptr %args, align 8
  %0 = getelementptr inbounds i8, ptr %args, i64 8
  %_6.1 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %args, i64 16
  %_7.0 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7.1 = load i64, ptr %2, align 8
  %3 = icmp eq i64 %_6.1, 0
  br i1 %3, label %bb4, label %bb5

bb4:                                              ; preds = %start
  %4 = icmp eq i64 %_7.1, 0
  br i1 %4, label %bb8, label %bb3

bb5:                                              ; preds = %start
  %5 = icmp eq i64 %_6.1, 1
  br i1 %5, label %bb6, label %bb3

bb8:                                              ; preds = %bb4
  store ptr inttoptr (i64 1 to ptr), ptr %_2, align 8
  %6 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 0, ptr %6, align 8
  br label %bb2

bb3:                                              ; preds = %bb6, %bb5, %bb4
  %7 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %8 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store ptr %7, ptr %_2, align 8
  %9 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb2

bb2:                                              ; preds = %bb3, %bb7, %bb8
  %10 = load ptr, ptr %_2, align 8
  %11 = getelementptr inbounds i8, ptr %_2, i64 8
  %12 = load i64, ptr %11, align 8
; call core::option::Option<T>::map_or_else
  call void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hffd784a61d128740E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %10, i64 %12, ptr align 8 %args)
  ret void

bb6:                                              ; preds = %bb5
  %13 = icmp eq i64 %_7.1, 0
  br i1 %13, label %bb7, label %bb3

bb7:                                              ; preds = %bb6
  %s = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %14 = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %_13.0 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  %_13.1 = load i64, ptr %15, align 8
  store ptr %_13.0, ptr %_2, align 8
  %16 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %_13.1, ptr %16, align 8
  br label %bb2
}

; alloc::fmt::format::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h7d044deb4b335858E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_1) unnamed_addr #1 {
start:
  %_2 = alloca [48 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %_1, i64 48, i1 false)
; call alloc::fmt::format::format_inner
  call void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_2)
  ret void
}

; alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h425057862889e85eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17haa7ba49bde010229E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17he5116432b74af116E(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h8999dcff0e84df57E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #20
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #20
  ret ptr %_0
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17hb02dc2529b44cf2bE(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h8999dcff0e84df57E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #20
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #20
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h5516b9ccc28583c8E(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h58dce6c9e864df72E"(ptr %_23) #20
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17hb02dc2529b44cf2bE(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17he5116432b74af116E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h58dce6c9e864df72E"(ptr %ptr) #20
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h58dce6c9e864df72E"(ptr %ptr4) #20
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h10db10909fb400a7E"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #2 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h436053481d75bbadE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h4170bcb6fae88dc2E"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h436053481d75bbadE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h5565109c55262212E"(i64 %self1, i64 %self3) #20
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h8ef25de63334e3faE(i64 %8, i64 %9) #20
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17hba6c3abcfec1c926E"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h38c8fb1fafc19f9eE(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h49d7180c84c13a07E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17hac165001fcd81966E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h1fefe4385230d103E"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17hba6c3abcfec1c926E"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #19
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h729df72228684868E(i1 zeroext %cond) #20
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17ha64df5f042808efbE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17he79ed7524be5e0e9E(ptr %_8, i64 1, i64 1, i64 %len) #20
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h2fcede11498f29d8E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h78e2b9e84b7604dfE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h4170bcb6fae88dc2E"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #20
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h49d7180c84c13a07E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h5516b9ccc28583c8E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17hac165001fcd81966E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h5516b9ccc28583c8E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9e04f286c389dc9fE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17h7715a4528a246ec0E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #2 {
start:
  %0 = alloca [4 x i8], align 4
  %1 = alloca [8 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %_3 = icmp ne i64 %self.1, %other.1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %2 = mul nuw nsw i64 %self.1, 1
  store i64 %2, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  %3 = call i32 @memcmp(ptr %self.0, ptr %other.0, i64 %size)
  store i32 %3, ptr %0, align 4
  %_7 = load i32, ptr %0, align 4
  %4 = icmp eq i32 %_7, 0
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb4

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb4:                                              ; preds = %bb1, %bb2
  %6 = load i8, ptr %_0, align 1
  %7 = trunc nuw i8 %6 to i1
  ret i1 %7
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h7845d2abc342d103E"(ptr align 8 %self) unnamed_addr #2 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h10db10909fb400a7E"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <core::str::pattern::StrSearcher as core::str::pattern::Searcher>::next_match
; Function Attrs: inlinehint uwtable
define internal void @"_ZN80_$LT$core..str..pattern..StrSearcher$u20$as$u20$core..str..pattern..Searcher$GT$10next_match17h575393af1014bbfbE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
  %_2 = load i64, ptr %self, align 8
  %0 = trunc nuw i64 %_2 to i1
  br i1 %0, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %searcher = getelementptr inbounds i8, ptr %self, i64 8
  %1 = getelementptr inbounds i8, ptr %searcher, i64 48
  %_10 = load i64, ptr %1, align 8
  %is_long = icmp eq i64 %_10, -1
  br i1 %is_long, label %bb8, label %bb9

bb3:                                              ; preds = %bb5, %start
; call <core::str::pattern::StrSearcher as core::str::pattern::Searcher>::next
  call void @"_ZN80_$LT$core..str..pattern..StrSearcher$u20$as$u20$core..str..pattern..Searcher$GT$4next17hb22326a7804e3090E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self)
  %_4 = load i64, ptr %_3, align 8
  switch i64 %_4, label %bb1 [
    i64 0, label %bb7
    i64 1, label %bb5
    i64 2, label %bb6
  ]

bb1:                                              ; preds = %bb3
  unreachable

bb7:                                              ; preds = %bb3
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  %a = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 16
  %b = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %a, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  store i64 %b, ptr %5, align 8
  store i64 1, ptr %_0, align 8
  br label %bb10

bb5:                                              ; preds = %bb3
  br label %bb3

bb6:                                              ; preds = %bb3
  store i64 0, ptr %_0, align 8
  br label %bb10

bb10:                                             ; preds = %bb6, %bb7
  br label %bb11

bb11:                                             ; preds = %bb8, %bb9, %bb10
  ret void

bb9:                                              ; preds = %bb2
  %6 = getelementptr inbounds i8, ptr %self, i64 72
  %self.0 = load ptr, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  %self.1 = load i64, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %self, i64 88
  %self.01 = load ptr, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  %self.12 = load i64, ptr %9, align 8
; call core::str::pattern::TwoWaySearcher::next
  call void @_ZN4core3str7pattern14TwoWaySearcher4next17hc5e2753abaf6d66fE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %searcher, ptr align 1 %self.0, i64 %self.1, ptr align 1 %self.01, i64 %self.12, i1 zeroext false)
  br label %bb11

bb8:                                              ; preds = %bb2
  %10 = getelementptr inbounds i8, ptr %self, i64 72
  %self.03 = load ptr, ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  %self.14 = load i64, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %self, i64 88
  %self.05 = load ptr, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %12, i64 8
  %self.16 = load i64, ptr %13, align 8
; call core::str::pattern::TwoWaySearcher::next
  call void @_ZN4core3str7pattern14TwoWaySearcher4next17hc5e2753abaf6d66fE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %searcher, ptr align 1 %self.03, i64 %self.14, ptr align 1 %self.05, i64 %self.16, i1 zeroext true)
  br label %bb11
}

; <core::str::pattern::StrSearcher as core::str::pattern::Searcher>::next
; Function Attrs: inlinehint uwtable
define internal void @"_ZN80_$LT$core..str..pattern..StrSearcher$u20$as$u20$core..str..pattern..Searcher$GT$4next17hb22326a7804e3090E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %self1 = alloca [8 x i8], align 4
  %_41 = alloca [16 x i8], align 8
  %_28 = alloca [1 x i8], align 1
  %b = alloca [8 x i8], align 8
  %otherwise = alloca [24 x i8], align 8
  %_14 = alloca [8 x i8], align 8
  %_10 = alloca [16 x i8], align 8
  %_8 = alloca [4 x i8], align 4
  %_2 = load i64, ptr %self, align 8
  %0 = trunc nuw i64 %_2 to i1
  br i1 %0, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %searcher2 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = getelementptr inbounds i8, ptr %searcher2, i64 32
  %_18 = load i64, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 72
  %self.03 = load ptr, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %self.14 = load i64, ptr %3, align 8
  %_17 = icmp eq i64 %_18, %self.14
  br i1 %_17, label %bb9, label %bb10

bb3:                                              ; preds = %start
  %searcher = getelementptr inbounds i8, ptr %self, i64 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = getelementptr inbounds i8, ptr %4, i64 18
  %6 = load i8, ptr %5, align 2
  %_4 = trunc nuw i8 %6 to i1
  br i1 %_4, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  %7 = getelementptr inbounds i8, ptr %self, i64 8
  %8 = getelementptr inbounds i8, ptr %7, i64 16
  %9 = load i8, ptr %8, align 8
  %is_match = trunc nuw i8 %9 to i1
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %11 = getelementptr inbounds i8, ptr %10, i64 16
  %12 = load i8, ptr %11, align 8
  %_6 = trunc nuw i8 %12 to i1
  %13 = getelementptr inbounds i8, ptr %self, i64 8
  %14 = getelementptr inbounds i8, ptr %13, i64 16
  %15 = xor i1 %_6, true
  %16 = zext i1 %15 to i8
  store i8 %16, ptr %14, align 8
  %17 = getelementptr inbounds i8, ptr %self, i64 8
  %pos = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self, i64 72
  %self.0 = load ptr, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %18, i64 8
  %self.1 = load i64, ptr %19, align 8
; call core::str::traits::<impl core::slice::index::SliceIndex<str> for core::ops::range::RangeFrom<usize>>::get
  %20 = call { ptr, i64 } @"_ZN4core3str6traits112_$LT$impl$u20$core..slice..index..SliceIndex$LT$str$GT$$u20$for$u20$core..ops..range..RangeFrom$LT$usize$GT$$GT$3get17h791804cc555cad26E"(i64 %pos, ptr align 1 %self.0, i64 %self.1)
  %21 = extractvalue { ptr, i64 } %20, 0
  %22 = extractvalue { ptr, i64 } %20, 1
  store ptr %21, ptr %_41, align 8
  %23 = getelementptr inbounds i8, ptr %_41, i64 8
  store i64 %22, ptr %23, align 8
  %24 = load ptr, ptr %_41, align 8
  %25 = getelementptr inbounds i8, ptr %_41, i64 8
  %26 = load i64, ptr %25, align 8
  %27 = ptrtoint ptr %24 to i64
  %28 = icmp eq i64 %27, 0
  %_42 = select i1 %28, i64 0, i64 1
  %29 = trunc nuw i64 %_42 to i1
  br i1 %29, label %bb20, label %bb19

bb4:                                              ; preds = %bb3
  store i64 2, ptr %_0, align 8
  br label %bb17

bb20:                                             ; preds = %bb5
  %s.0 = load ptr, ptr %_41, align 8
  %30 = getelementptr inbounds i8, ptr %_41, i64 8
  %s.1 = load i64, ptr %30, align 8
  %_50 = getelementptr inbounds nuw i8, ptr %s.0, i64 %s.1
  store ptr %s.0, ptr %_10, align 8
  %31 = getelementptr inbounds i8, ptr %_10, i64 8
  store ptr %_50, ptr %31, align 8
; call core::str::validations::next_code_point
  %32 = call { i32, i32 } @_ZN4core3str11validations15next_code_point17he658d20941288e12E(ptr align 8 %_10)
  %33 = extractvalue { i32, i32 } %32, 0
  %34 = extractvalue { i32, i32 } %32, 1
  store i32 %33, ptr %self1, align 4
  %35 = getelementptr inbounds i8, ptr %self1, i64 4
  store i32 %34, ptr %35, align 4
  %36 = load i32, ptr %self1, align 4
  %37 = getelementptr inbounds i8, ptr %self1, i64 4
  %38 = load i32, ptr %37, align 4
  %_56 = zext i32 %36 to i64
  %39 = trunc nuw i64 %_56 to i1
  br i1 %39, label %bb23, label %bb22

bb19:                                             ; preds = %bb5
; call core::str::slice_error_fail
  call void @_ZN4core3str16slice_error_fail17h9592f524d3a93436E(ptr align 1 %self.0, i64 %self.1, i64 %pos, i64 %self.1, ptr align 8 @alloc_ce48450aae05af7a24a801fc57e6a3b8) #19
  unreachable

bb23:                                             ; preds = %bb20
  %40 = getelementptr inbounds i8, ptr %self1, i64 4
  %x = load i32, ptr %40, align 4
  br label %bb24

bb22:                                             ; preds = %bb20
  br i1 %is_match, label %bb6, label %bb40

bb40:                                             ; preds = %bb22
  %41 = getelementptr inbounds i8, ptr %self, i64 8
  %42 = getelementptr inbounds i8, ptr %41, i64 18
  store i8 1, ptr %42, align 2
  store i64 2, ptr %_0, align 8
  br label %bb8

bb6:                                              ; preds = %bb25, %bb22
  %43 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pos, ptr %43, align 8
  %44 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %pos, ptr %44, align 8
  store i64 0, ptr %_0, align 8
  br label %bb8

bb8:                                              ; preds = %bb6, %bb26, %bb40
  br label %bb17

bb24:                                             ; preds = %bb23
; call core::char::convert::from_u32_unchecked::precondition_check
  call void @_ZN4core4char7convert18from_u32_unchecked18precondition_check17h9b90564ee3fe611bE(i32 %x) #20
  br label %bb25

bb25:                                             ; preds = %bb24
  store i32 %x, ptr %_8, align 4
  br i1 %is_match, label %bb6, label %bb7

bb7:                                              ; preds = %bb25
  %ch = load i32, ptr %_8, align 4
  %_62 = icmp ult i32 %ch, 128
  br i1 %_62, label %bb32, label %bb27

bb27:                                             ; preds = %bb7
  %_63 = icmp ult i32 %ch, 2048
  br i1 %_63, label %bb31, label %bb28

bb32:                                             ; preds = %bb7
  store i64 1, ptr %_14, align 8
  br label %bb26

bb28:                                             ; preds = %bb27
  %_64 = icmp ult i32 %ch, 65536
  br i1 %_64, label %bb30, label %bb29

bb31:                                             ; preds = %bb27
  store i64 2, ptr %_14, align 8
  br label %bb26

bb29:                                             ; preds = %bb28
  store i64 4, ptr %_14, align 8
  br label %bb26

bb30:                                             ; preds = %bb28
  store i64 3, ptr %_14, align 8
  br label %bb26

bb26:                                             ; preds = %bb32, %bb31, %bb30, %bb29
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %46 = getelementptr inbounds i8, ptr %self, i64 8
  %47 = load i64, ptr %46, align 8
  %48 = load i64, ptr %_14, align 8
  %49 = add i64 %47, %48
  store i64 %49, ptr %45, align 8
  %50 = getelementptr inbounds i8, ptr %self, i64 8
  %_15 = load i64, ptr %50, align 8
  %51 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pos, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %_15, ptr %52, align 8
  store i64 1, ptr %_0, align 8
  br label %bb8

bb17:                                             ; preds = %bb9, %bb12, %bb15, %bb4, %bb8
  ret void

bb10:                                             ; preds = %bb2
  %53 = getelementptr inbounds i8, ptr %searcher2, i64 48
  %_21 = load i64, ptr %53, align 8
  %is_long = icmp eq i64 %_21, -1
  %54 = getelementptr inbounds i8, ptr %self, i64 72
  %self.05 = load ptr, ptr %54, align 8
  %55 = getelementptr inbounds i8, ptr %54, i64 8
  %self.16 = load i64, ptr %55, align 8
  %56 = getelementptr inbounds i8, ptr %self, i64 88
  %self.07 = load ptr, ptr %56, align 8
  %57 = getelementptr inbounds i8, ptr %56, i64 8
  %self.18 = load i64, ptr %57, align 8
; call core::str::pattern::TwoWaySearcher::next
  call void @_ZN4core3str7pattern14TwoWaySearcher4next17hd2f940f223d4d143E(ptr sret([24 x i8]) align 8 %otherwise, ptr align 8 %searcher2, ptr align 1 %self.05, i64 %self.16, ptr align 1 %self.07, i64 %self.18, i1 zeroext %is_long)
  %_25 = load i64, ptr %otherwise, align 8
  %58 = icmp eq i64 %_25, 1
  br i1 %58, label %bb13, label %bb12

bb9:                                              ; preds = %bb2
  store i64 2, ptr %_0, align 8
  br label %bb17

bb13:                                             ; preds = %bb10
  %59 = getelementptr inbounds i8, ptr %otherwise, i64 8
  %a = load i64, ptr %59, align 8
  %60 = getelementptr inbounds i8, ptr %otherwise, i64 16
  %61 = load i64, ptr %60, align 8
  store i64 %61, ptr %b, align 8
  br label %bb14

bb12:                                             ; preds = %bb10
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %otherwise, i64 24, i1 false)
  br label %bb17

bb14:                                             ; preds = %bb16, %bb13
  %62 = getelementptr inbounds i8, ptr %self, i64 72
  %self.09 = load ptr, ptr %62, align 8
  %63 = getelementptr inbounds i8, ptr %62, i64 8
  %self.110 = load i64, ptr %63, align 8
  %index = load i64, ptr %b, align 8
  %64 = icmp eq i64 %index, 0
  br i1 %64, label %bb33, label %bb34

bb33:                                             ; preds = %bb14
  br label %bb15

bb34:                                             ; preds = %bb14
  %_65 = icmp uge i64 %index, %self.110
  br i1 %_65, label %bb35, label %bb36

bb15:                                             ; preds = %bb38, %bb33
  %v1 = load i64, ptr %b, align 8
  %65 = getelementptr inbounds i8, ptr %searcher2, i64 32
  %v2 = load i64, ptr %65, align 8
; call core::cmp::Ord::max
  %_30 = call i64 @_ZN4core3cmp3Ord3max17h6f2a6a8eb8d692e5E(i64 %v1, i64 %v2)
  %66 = getelementptr inbounds i8, ptr %searcher2, i64 32
  store i64 %_30, ptr %66, align 8
  %_33 = load i64, ptr %b, align 8
  %67 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %a, ptr %67, align 8
  %68 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %_33, ptr %68, align 8
  store i64 1, ptr %_0, align 8
  br label %bb17

bb36:                                             ; preds = %bb34
  %_68 = icmp ult i64 %index, %self.110
  br i1 %_68, label %bb37, label %panic

bb35:                                             ; preds = %bb34
  %69 = icmp eq i64 %index, %self.110
  %70 = zext i1 %69 to i8
  store i8 %70, ptr %_28, align 1
  br label %bb38

bb37:                                             ; preds = %bb36
  %71 = getelementptr inbounds nuw i8, ptr %self.09, i64 %index
  %self11 = load i8, ptr %71, align 1
  %72 = icmp sge i8 %self11, -64
  %73 = zext i1 %72 to i8
  store i8 %73, ptr %_28, align 1
  br label %bb38

panic:                                            ; preds = %bb36
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %index, i64 %self.110, ptr align 8 @alloc_7afbf74ed710c75f5dddd72ec933c2e5) #19
  unreachable

bb38:                                             ; preds = %bb35, %bb37
  %74 = load i8, ptr %_28, align 1
  %75 = trunc nuw i8 %74 to i1
  br i1 %75, label %bb15, label %bb16

bb16:                                             ; preds = %bb38
  %76 = load i64, ptr %b, align 8
  %77 = add i64 %76, 1
  store i64 %77, ptr %b, align 8
  br label %bb14

bb1:                                              ; No predecessors!
  unreachable
}

; <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::use_early_reject
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$16use_early_reject17hcc537a7fe963f812E"() unnamed_addr #1 {
start:
  ret i1 false
}

; <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::matching
; Function Attrs: inlinehint uwtable
define internal void @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$8matching17h5d7354f08f3a4641E"(ptr sret([24 x i8]) align 8 %_0, i64 %a, i64 %b) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %a, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  store i64 %b, ptr %1, align 8
  store i64 1, ptr %_0, align 8
  ret void
}

; <core::str::pattern::MatchOnly as core::str::pattern::TwoWayStrategy>::rejecting
; Function Attrs: inlinehint uwtable
define internal void @"_ZN84_$LT$core..str..pattern..MatchOnly$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h0a0fb1d1877ff12eE"(ptr sret([24 x i8]) align 8 %_0, i64 %_a, i64 %_b) unnamed_addr #1 {
start:
  store i64 0, ptr %_0, align 8
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17haa7ba49bde010229E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #1 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h1fefe4385230d103E"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h91e4cf3a4100fd2fE(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #20
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next_back
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$14spec_next_back17hbcf64cad164e1fdaE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_0 = alloca [16 x i8], align 8
  %_4 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_4, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br i1 %_0.i, label %bb2, label %bb4

bb4:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb5

bb2:                                              ; preds = %start
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load i64, ptr %0, align 8
; call <usize as core::iter::range::Step>::backward_unchecked
  %_5 = call i64 @"_ZN49_$LT$usize$u20$as$u20$core..iter..range..Step$GT$18backward_unchecked17h1e3d437d39c386e9E"(i64 %_6, i64 1)
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %_5, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  %_7 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_7, ptr %3, align 8
  store i64 1, ptr %_0, align 8
  br label %bb5

bb5:                                              ; preds = %bb2, %bb4
  %4 = load i64, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = insertvalue { i64, i64 } poison, i64 %4, 0
  %8 = insertvalue { i64, i64 } %7, i64 %6, 1
  ret { i64, i64 } %8
}

; <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::use_early_reject
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$16use_early_reject17hdee8f106adddafd7E"() unnamed_addr #1 {
start:
  ret i1 true
}

; <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::matching
; Function Attrs: inlinehint uwtable
define internal void @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$8matching17h828b2bbd329342eeE"(ptr sret([24 x i8]) align 8 %_0, i64 %a, i64 %b) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %a, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %b, ptr %1, align 8
  store i64 0, ptr %_0, align 8
  ret void
}

; <core::str::pattern::RejectAndMatch as core::str::pattern::TwoWayStrategy>::rejecting
; Function Attrs: inlinehint uwtable
define internal void @"_ZN89_$LT$core..str..pattern..RejectAndMatch$u20$as$u20$core..str..pattern..TwoWayStrategy$GT$9rejecting17h3fb8f6b1c25833cbE"(ptr sret([24 x i8]) align 8 %_0, i64 %a, i64 %b) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %a, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %b, ptr %1, align 8
  store i64 1, ptr %_0, align 8
  ret void
}

; <core::ops::control_flow::ControlFlow<B,C> as core::cmp::PartialEq>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN90_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..cmp..PartialEq$GT$2eq17hb9db17e21f752620E"(ptr align 1 %self, ptr align 1 %other) unnamed_addr #1 {
start:
  %__arg1_02 = alloca [8 x i8], align 8
  %__self_01 = alloca [8 x i8], align 8
  %__arg1_0 = alloca [8 x i8], align 8
  %__self_0 = alloca [8 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %0 = load i8, ptr %self, align 1
  %1 = trunc nuw i8 %0 to i1
  %__self_discr = zext i1 %1 to i64
  %2 = load i8, ptr %other, align 1
  %3 = trunc nuw i8 %2 to i1
  %__arg1_discr = zext i1 %3 to i64
  %_5 = icmp eq i64 %__self_discr, %__arg1_discr
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb5

bb1:                                              ; preds = %start
  %4 = load i8, ptr %self, align 1
  %5 = trunc nuw i8 %4 to i1
  %_8 = zext i1 %5 to i64
  %6 = trunc nuw i64 %_8 to i1
  br i1 %6, label %bb4, label %bb3

bb5:                                              ; preds = %bb4, %bb3, %bb2
  %7 = load i8, ptr %_0, align 1
  %8 = trunc nuw i8 %7 to i1
  ret i1 %8

bb4:                                              ; preds = %bb1
  %9 = load i8, ptr %other, align 1
  %10 = trunc nuw i8 %9 to i1
  %_7 = zext i1 %10 to i64
  %_17 = icmp eq i64 %_7, 1
  %11 = getelementptr inbounds i8, ptr %self, i64 1
  store ptr %11, ptr %__self_01, align 8
  %12 = getelementptr inbounds i8, ptr %other, i64 1
  store ptr %12, ptr %__arg1_02, align 8
  %13 = load ptr, ptr %__self_01, align 8
  %14 = load ptr, ptr %__arg1_02, align 8
; call core::cmp::impls::<impl core::cmp::PartialEq for ()>::eq
  %15 = call zeroext i1 @"_ZN4core3cmp5impls59_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$$LP$$RP$$GT$2eq17h6e2aebfa362c6b51E"(ptr align 1 %13, ptr align 1 %14)
  %16 = zext i1 %15 to i8
  store i8 %16, ptr %_0, align 1
  br label %bb5

bb3:                                              ; preds = %bb1
  %17 = load i8, ptr %other, align 1
  %18 = trunc nuw i8 %17 to i1
  %_6 = zext i1 %18 to i64
  %_18 = icmp eq i64 %_6, 0
  %19 = getelementptr inbounds i8, ptr %self, i64 1
  store ptr %19, ptr %__self_0, align 8
  %20 = getelementptr inbounds i8, ptr %other, i64 1
  store ptr %20, ptr %__arg1_0, align 8
  %21 = load ptr, ptr %__self_0, align 8
  %22 = load ptr, ptr %__arg1_0, align 8
; call core::cmp::impls::<impl core::cmp::PartialEq for ()>::eq
  %23 = call zeroext i1 @"_ZN4core3cmp5impls59_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$$LP$$RP$$GT$2eq17h6e2aebfa362c6b51E"(ptr align 1 %21, ptr align 1 %22)
  %24 = zext i1 %23 to i8
  store i8 %24, ptr %_0, align 1
  br label %bb5

bb8:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::__iterator_get_unchecked
; Function Attrs: inlinehint uwtable
define internal align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$24__iterator_get_unchecked17h91f48be79c413c22E"(ptr align 8 %self, i64 %idx) unnamed_addr #1 {
start:
  %_5 = load ptr, ptr %self, align 8
  %_3 = getelementptr inbounds nuw i8, ptr %_5, i64 %idx
  ret ptr %_3
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h9217bc7b5ffadb4bE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i32, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfbd6bab7cec622e7E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i8, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17heafc30ce1e2a6791E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_9 = alloca [16 x i8], align 8
  %exact = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %_7 = load ptr, ptr %self, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %1 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h6777e3b08ffba055E"(ptr %_6, ptr %_7)
  store i64 %1, ptr %exact, align 8
  br label %bb4

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %exact, align 8
  %_10 = load i64, ptr %exact, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_10, ptr %2, align 8
  store i64 1, ptr %_9, align 8
  store i64 %_8, ptr %_0, align 8
  %3 = load i64, ptr %_9, align 8
  %4 = getelementptr inbounds i8, ptr %_9, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 %5, ptr %7, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Windows<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN94_$LT$core..slice..iter..Windows$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h8639275e5b6423e2E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_5.i = alloca [16 x i8], align 8
  %ret = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %self1 = load i64, ptr %0, align 8
  %_11.0 = load ptr, ptr %self, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %_11.1 = load i64, ptr %1, align 8
  %_2 = icmp ugt i64 %self1, %_11.1
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %self.0 = load ptr, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  %self.1 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 16
  %self2 = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %_5.i, i64 8
  store i64 %self2, ptr %4, align 8
  store i64 1, ptr %_5.i, align 8
  %5 = getelementptr inbounds i8, ptr %_5.i, i64 8
  %new_len.i = load i64, ptr %5, align 8
  %_8.i = icmp ugt i64 %self2, %self.1
  br i1 %_8.i, label %bb1.i, label %"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hde1fa0ba7dc673a4E.exit"

bb1.i:                                            ; preds = %bb2
; call core::slice::index::slice_end_index_len_fail
  call void @_ZN4core5slice5index24slice_end_index_len_fail17ha318ba5d7b15d3edE(i64 %self2, i64 %self.1, ptr align 8 @alloc_1ce40ffc6da8a10f7c1ede63115687a0) #19
  unreachable

"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hde1fa0ba7dc673a4E.exit": ; preds = %bb2
  %6 = insertvalue { ptr, i64 } poison, ptr %self.0, 0
  %7 = insertvalue { ptr, i64 } %6, i64 %new_len.i, 1
  %_7.0 = extractvalue { ptr, i64 } %7, 0
  %_7.1 = extractvalue { ptr, i64 } %7, 1
  store ptr %_7.0, ptr %ret, align 8
  %8 = getelementptr inbounds i8, ptr %ret, i64 8
  store i64 %_7.1, ptr %8, align 8
  %self.03 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %self.14 = load i64, ptr %9, align 8
; call <core::ops::range::RangeFrom<usize> as core::slice::index::SliceIndex<[T]>>::index
  %10 = call { ptr, i64 } @"_ZN110_$LT$core..ops..range..RangeFrom$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hdeeb06dd3690dc0bE"(i64 1, ptr align 1 %self.03, i64 %self.14, ptr align 8 @alloc_ac94b0f15567a3bff9a57d750dd3b469)
  %_10.0 = extractvalue { ptr, i64 } %10, 0
  %_10.1 = extractvalue { ptr, i64 } %10, 1
  store ptr %_10.0, ptr %self, align 8
  %11 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %_10.1, ptr %11, align 8
  %12 = load ptr, ptr %ret, align 8
  %13 = getelementptr inbounds i8, ptr %ret, i64 8
  %14 = load i64, ptr %13, align 8
  store ptr %12, ptr %_0, align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %14, ptr %15, align 8
  br label %bb3

bb1:                                              ; preds = %start
  %16 = load ptr, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, align 8
  %17 = load i64, ptr getelementptr inbounds (i8, ptr @anon.e9ee036f1d998da69b02774f266e1b43.1, i64 8), align 8
  store ptr %16, ptr %_0, align 8
  %18 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %17, ptr %18, align 8
  br label %bb3

bb3:                                              ; preds = %bb1, %"_ZN106_$LT$core..ops..range..Range$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hde1fa0ba7dc673a4E.exit"
  %19 = load ptr, ptr %_0, align 8
  %20 = getelementptr inbounds i8, ptr %_0, i64 8
  %21 = load i64, ptr %20, align 8
  %22 = insertvalue { ptr, i64 } poison, ptr %19, 0
  %23 = insertvalue { ptr, i64 } %22, i64 %21, 1
  ret { ptr, i64 } %23
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h5d70df295b1066c9E"() unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  store i8 0, ptr %_0, align 1
  %0 = load i8, ptr %_0, align 1
  %1 = trunc nuw i8 %0 to i1
  ret i1 %1
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17hfcc4a54d575fe48fE"() unnamed_addr #1 {
start:
  %_0 = alloca [16 x i8], align 8
  store i64 0, ptr %_0, align 8
  %0 = load i64, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  %2 = load i64, ptr %1, align 8
  %3 = insertvalue { i64, i64 } poison, i64 %0, 0
  %4 = insertvalue { i64, i64 } %3, i64 %2, 1
  ret { i64, i64 } %4
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h469d74f6a6b7eb2eE"(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %_5 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  store i64 %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  %_2 = load i64, ptr %self, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = trunc nuw i64 %_2 to i1
  br i1 %5, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %6 = getelementptr inbounds i8, ptr %self, i64 8
  %b = load i64, ptr %6, align 8
  store i64 %b, ptr %_5, align 8
  %7 = load i64, ptr %_5, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %7, ptr %8, align 8
  store i64 1, ptr %_0, align 8
  br label %bb4

bb3:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %9 = load i64, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  %11 = load i64, ptr %10, align 8
  %12 = insertvalue { i64, i64 } poison, i64 %9, 0
  %13 = insertvalue { i64, i64 } %12, i64 %11, 1
  ret { i64, i64 } %13

bb1:                                              ; No predecessors!
  unreachable
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17hcaab5eb48c5ad543E"(i1 zeroext %self) unnamed_addr #1 {
start:
  %_5 = alloca [0 x i8], align 1
  %_0 = alloca [1 x i8], align 1
  %_2 = zext i1 %self to i64
  %0 = trunc nuw i64 %_2 to i1
  br i1 %0, label %bb2, label %bb3

bb2:                                              ; preds = %start
  store i8 1, ptr %_0, align 1
  br label %bb4

bb3:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %1 = load i8, ptr %_0, align 1
  %2 = trunc nuw i8 %1 to i1
  ret i1 %2

bb1:                                              ; No predecessors!
  unreachable
}

; _12_comments::main
; Function Attrs: uwtable
define internal void @_ZN12_12_comments4main17hf1bb7d95dc082421E() unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_402 = alloca [48 x i8], align 8
  %_399 = alloca [48 x i8], align 8
  %_396 = alloca [16 x i8], align 8
  %_395 = alloca [16 x i8], align 8
  %_394 = alloca [48 x i8], align 8
  %_393 = alloca [48 x i8], align 8
  %_389 = alloca [16 x i8], align 8
  %_388 = alloca [16 x i8], align 8
  %_383 = alloca [48 x i8], align 8
  %_380 = alloca [16 x i8], align 8
  %_379 = alloca [16 x i8], align 8
  %_378 = alloca [48 x i8], align 8
  %_377 = alloca [48 x i8], align 8
  %_373 = alloca [16 x i8], align 8
  %_372 = alloca [16 x i8], align 8
  %_367 = alloca [48 x i8], align 8
  %_364 = alloca [16 x i8], align 8
  %_363 = alloca [16 x i8], align 8
  %_362 = alloca [48 x i8], align 8
  %_361 = alloca [48 x i8], align 8
  %_357 = alloca [16 x i8], align 8
  %_356 = alloca [16 x i8], align 8
  %_351 = alloca [48 x i8], align 8
  %_348 = alloca [16 x i8], align 8
  %_347 = alloca [16 x i8], align 8
  %_346 = alloca [48 x i8], align 8
  %_345 = alloca [48 x i8], align 8
  %_342 = alloca [8 x i8], align 8
  %_340 = alloca [16 x i8], align 8
  %_339 = alloca [16 x i8], align 8
  %_334 = alloca [48 x i8], align 8
  %_331 = alloca [16 x i8], align 8
  %_330 = alloca [16 x i8], align 8
  %_329 = alloca [48 x i8], align 8
  %_328 = alloca [48 x i8], align 8
  %_324 = alloca [16 x i8], align 8
  %_323 = alloca [16 x i8], align 8
  %_318 = alloca [48 x i8], align 8
  %_315 = alloca [48 x i8], align 8
  %interest = alloca [8 x i8], align 8
  %amount = alloca [8 x i8], align 8
  %time = alloca [8 x i8], align 8
  %principal = alloca [8 x i8], align 8
  %_305 = alloca [48 x i8], align 8
  %_302 = alloca [16 x i8], align 8
  %_301 = alloca [16 x i8], align 8
  %_298 = alloca [48 x i8], align 8
  %_295 = alloca [16 x i8], align 8
  %_294 = alloca [16 x i8], align 8
  %_291 = alloca [48 x i8], align 8
  %_288 = alloca [16 x i8], align 8
  %_287 = alloca [16 x i8], align 8
  %_284 = alloca [48 x i8], align 8
  %_281 = alloca [48 x i8], align 8
  %is_eligible = alloca [1 x i8], align 1
  %has_income = alloca [1 x i8], align 1
  %is_adult = alloca [1 x i8], align 1
  %_275 = alloca [48 x i8], align 8
  %_273 = alloca [1 x i8], align 1
  %_271 = alloca [16 x i8], align 8
  %_270 = alloca [16 x i8], align 8
  %_267 = alloca [48 x i8], align 8
  %_265 = alloca [8 x i8], align 8
  %_263 = alloca [16 x i8], align 8
  %_262 = alloca [16 x i8], align 8
  %_259 = alloca [48 x i8], align 8
  %_256 = alloca [16 x i8], align 8
  %_255 = alloca [16 x i8], align 8
  %_252 = alloca [48 x i8], align 8
  %text = alloca [16 x i8], align 8
  %_248 = alloca [16 x i8], align 8
  %_247 = alloca [16 x i8], align 8
  %_244 = alloca [48 x i8], align 8
  %_241 = alloca [48 x i8], align 8
  %_238 = alloca [16 x i8], align 8
  %_236 = alloca [16 x i8], align 8
  %_235 = alloca [32 x i8], align 8
  %_232 = alloca [48 x i8], align 8
  %res = alloca [24 x i8], align 8
  %full_name = alloca [24 x i8], align 8
  %last_name = alloca [16 x i8], align 8
  %first_name = alloca [16 x i8], align 8
  %_226 = alloca [48 x i8], align 8
  %_222 = alloca [16 x i8], align 8
  %_221 = alloca [16 x i8], align 8
  %_218 = alloca [48 x i8], align 8
  %_213 = alloca [48 x i8], align 8
  %counter = alloca [4 x i8], align 4
  %_209 = alloca [16 x i8], align 8
  %_208 = alloca [16 x i8], align 8
  %_205 = alloca [48 x i8], align 8
  %i = alloca [4 x i8], align 4
  %_200 = alloca [8 x i8], align 4
  %iter1 = alloca [12 x i8], align 4
  %_198 = alloca [12 x i8], align 4
  %_197 = alloca [12 x i8], align 4
  %_195 = alloca [48 x i8], align 8
  %_192 = alloca [48 x i8], align 8
  %_189 = alloca [48 x i8], align 8
  %_186 = alloca [16 x i8], align 8
  %_185 = alloca [16 x i8], align 8
  %_182 = alloca [48 x i8], align 8
  %area = alloca [4 x i8], align 4
  %_178 = alloca [48 x i8], align 8
  %_175 = alloca [48 x i8], align 8
  %_172 = alloca [48 x i8], align 8
  %_168 = alloca [48 x i8], align 8
  %_164 = alloca [48 x i8], align 8
  %_160 = alloca [48 x i8], align 8
  %_155 = alloca [48 x i8], align 8
  %_152 = alloca [16 x i8], align 8
  %_150 = alloca [16 x i8], align 8
  %_149 = alloca [32 x i8], align 8
  %_146 = alloca [48 x i8], align 8
  %value = alloca [8 x i8], align 8
  %index = alloca [8 x i8], align 8
  %_140 = alloca [16 x i8], align 8
  %iter = alloca [24 x i8], align 8
  %_135 = alloca [24 x i8], align 8
  %_134 = alloca [24 x i8], align 8
  %_132 = alloca [48 x i8], align 8
  %_127 = alloca [16 x i8], align 8
  %_126 = alloca [16 x i8], align 8
  %_123 = alloca [48 x i8], align 8
  %_118 = alloca [16 x i8], align 8
  %_117 = alloca [16 x i8], align 8
  %_114 = alloca [48 x i8], align 8
  %_111 = alloca [48 x i8], align 8
  %numbers = alloca [20 x i8], align 4
  %_107 = alloca [48 x i8], align 8
  %_104 = alloca [16 x i8], align 8
  %_102 = alloca [16 x i8], align 8
  %_100 = alloca [16 x i8], align 8
  %_99 = alloca [48 x i8], align 8
  %_96 = alloca [48 x i8], align 8
  %_93 = alloca [16 x i8], align 8
  %_91 = alloca [16 x i8], align 8
  %_89 = alloca [16 x i8], align 8
  %_88 = alloca [48 x i8], align 8
  %_85 = alloca [48 x i8], align 8
  %_82 = alloca [16 x i8], align 8
  %_80 = alloca [16 x i8], align 8
  %_78 = alloca [16 x i8], align 8
  %_77 = alloca [48 x i8], align 8
  %_74 = alloca [48 x i8], align 8
  %_71 = alloca [16 x i8], align 8
  %_69 = alloca [16 x i8], align 8
  %_67 = alloca [16 x i8], align 8
  %_66 = alloca [48 x i8], align 8
  %_63 = alloca [48 x i8], align 8
  %_60 = alloca [48 x i8], align 8
  %quotient = alloca [4 x i8], align 4
  %product = alloca [4 x i8], align 4
  %difference = alloca [4 x i8], align 4
  %sum = alloca [4 x i8], align 4
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_44 = alloca [48 x i8], align 8
  %_41 = alloca [16 x i8], align 8
  %_40 = alloca [16 x i8], align 8
  %_37 = alloca [48 x i8], align 8
  %_34 = alloca [16 x i8], align 8
  %_33 = alloca [16 x i8], align 8
  %_30 = alloca [48 x i8], align 8
  %_27 = alloca [16 x i8], align 8
  %_26 = alloca [16 x i8], align 8
  %_23 = alloca [48 x i8], align 8
  %_20 = alloca [48 x i8], align 8
  %salary = alloca [8 x i8], align 8
  %age = alloca [4 x i8], align 4
  %name = alloca [16 x i8], align 8
  %_14 = alloca [48 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_e34eb99905e2bb144bb353ec27805dce)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_87cc6b5b2b729ec9d079d553cad80a51)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_dfcbfb69979486380580e63e3b259b40)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
  store ptr @alloc_b468ef00fa5916b1335583d25c8252ad, ptr %name, align 8
  %0 = getelementptr inbounds i8, ptr %name, i64 8
  store i64 5, ptr %0, align 8
  store i32 25, ptr %age, align 4
  store double 5.000000e+03, ptr %salary, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_20, ptr align 8 @alloc_2aaa6cfbb39584e321d4f0520cd114ac)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_20)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h2d435e4fb848148eE(ptr sret([16 x i8]) align 8 %_27, ptr align 8 %name)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_26, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_27, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_23, ptr align 8 @alloc_cd99b202573048149e4ac0d7df184409, ptr align 8 %_26)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_23)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_34, ptr align 4 %age)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_33, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_34, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_30, ptr align 8 @alloc_c8d066cadf1ba35bf631428b9f61b62c, ptr align 8 %_33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_30)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_41, ptr align 8 %salary)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_40, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_41, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_37, ptr align 8 @alloc_828475e8994c9a2156e0be0dce462ed4, ptr align 8 %_40)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_37)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_44, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_44)
  store i32 10, ptr %a, align 4
  store i32 5, ptr %b, align 4
  %4 = load i32, ptr %a, align 4
  %5 = load i32, ptr %b, align 4
  %6 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %4, i32 %5)
  %_49.0 = extractvalue { i32, i1 } %6, 0
  %_49.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_49.1, label %panic, label %bb24

bb24:                                             ; preds = %start
  store i32 %_49.0, ptr %sum, align 4
  %7 = load i32, ptr %a, align 4
  %8 = load i32, ptr %b, align 4
  %9 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %7, i32 %8)
  %_51.0 = extractvalue { i32, i1 } %9, 0
  %_51.1 = extractvalue { i32, i1 } %9, 1
  br i1 %_51.1, label %panic2, label %bb25

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_114b365722e71454eef4238a6d3bd7eb) #19
  unreachable

bb25:                                             ; preds = %bb24
  store i32 %_51.0, ptr %difference, align 4
  %10 = load i32, ptr %a, align 4
  %11 = load i32, ptr %b, align 4
  %12 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %10, i32 %11)
  %_53.0 = extractvalue { i32, i1 } %12, 0
  %_53.1 = extractvalue { i32, i1 } %12, 1
  br i1 %_53.1, label %panic3, label %bb26

panic2:                                           ; preds = %bb24
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_866d7070cf81be649d420795782544b0) #19
  unreachable

bb26:                                             ; preds = %bb25
  store i32 %_53.0, ptr %product, align 4
  %13 = load i32, ptr %b, align 4
  %_55 = icmp eq i32 %13, 0
  br i1 %_55, label %panic4, label %bb27

panic3:                                           ; preds = %bb25
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_0906f7dc9a28da801694102873cb9feb) #19
  unreachable

bb27:                                             ; preds = %bb26
  %14 = load i32, ptr %b, align 4
  %_56 = icmp eq i32 %14, -1
  %15 = load i32, ptr %a, align 4
  %_57 = icmp eq i32 %15, -2147483648
  %_58 = and i1 %_56, %_57
  br i1 %_58, label %panic5, label %bb28

panic4:                                           ; preds = %bb26
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_4b26373a2560346ffe2f9e63d6fc6076) #19
  unreachable

bb28:                                             ; preds = %bb27
  %16 = load i32, ptr %a, align 4
  %17 = load i32, ptr %b, align 4
  %18 = sdiv i32 %16, %17
  store i32 %18, ptr %quotient, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_60, ptr align 8 @alloc_c617fe87cce53e57948be4e098b3f9cf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_60)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_67, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_69, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_71, ptr align 4 %sum)
  %19 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_66, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %19, ptr align 8 %_67, i64 16, i1 false)
  %20 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_66, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %20, ptr align 8 %_69, i64 16, i1 false)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_66, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_71, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h63dfc173c89a2e2cE(ptr sret([48 x i8]) align 8 %_63, ptr align 8 @alloc_75b10f0b41c742fe880b0f6947bcc0f9, ptr align 8 %_66)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_63)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_78, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_80, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_82, ptr align 4 %difference)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_77, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_78, i64 16, i1 false)
  %23 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_77, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %23, ptr align 8 %_80, i64 16, i1 false)
  %24 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_77, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %24, ptr align 8 %_82, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h63dfc173c89a2e2cE(ptr sret([48 x i8]) align 8 %_74, ptr align 8 @alloc_505609185f08173c2506561fcfd13b85, ptr align 8 %_77)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_74)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_89, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_91, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_93, ptr align 4 %product)
  %25 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_88, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %25, ptr align 8 %_89, i64 16, i1 false)
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_88, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_91, i64 16, i1 false)
  %27 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_88, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %27, ptr align 8 %_93, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h63dfc173c89a2e2cE(ptr sret([48 x i8]) align 8 %_85, ptr align 8 @alloc_0170cf9431e7bc8b96f6d1590e9f77a7, ptr align 8 %_88)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_85)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_100, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_102, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_104, ptr align 4 %quotient)
  %28 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %28, ptr align 8 %_100, i64 16, i1 false)
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_102, i64 16, i1 false)
  %30 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %30, ptr align 8 %_104, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h63dfc173c89a2e2cE(ptr sret([48 x i8]) align 8 %_96, ptr align 8 @alloc_fd5119e50c7c3e81e4bb265e08fca1f1, ptr align 8 %_99)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_96)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_107, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_107)
  %31 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
  store i32 1, ptr %31, align 4
  %32 = getelementptr inbounds nuw i32, ptr %numbers, i64 1
  store i32 2, ptr %32, align 4
  %33 = getelementptr inbounds nuw i32, ptr %numbers, i64 2
  store i32 3, ptr %33, align 4
  %34 = getelementptr inbounds nuw i32, ptr %numbers, i64 3
  store i32 4, ptr %34, align 4
  %35 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
  store i32 5, ptr %35, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_111, ptr align 8 @alloc_5f58f8ffc7ffdf80f12f01c6e973b49f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_111)
  %_119 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_118, ptr align 4 %_119)
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_117, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_118, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_114, ptr align 8 @alloc_730290c59e61c25b88204a03dbba8f8e, ptr align 8 %_117)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_114)
  %_128 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_127, ptr align 4 %_128)
  %37 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_126, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %37, ptr align 8 %_127, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_123, ptr align 8 @alloc_8fb8157e70b102d0cc050be818399ca1, ptr align 8 %_126)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_123)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_132, ptr align 8 @alloc_bc32138ead3b39df8991b59e9ca42df3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_132)
; call core::slice::<impl [T]>::iter
  %38 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h860d66e6a6dd6688E"(ptr align 4 %numbers, i64 5)
  %_136.0 = extractvalue { ptr, ptr } %38, 0
  %_136.1 = extractvalue { ptr, ptr } %38, 1
; call core::iter::traits::iterator::Iterator::enumerate
  call void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h8f8e741f03926ee9E(ptr sret([24 x i8]) align 8 %_135, ptr %_136.0, ptr %_136.1)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h2fcede11498f29d8E"(ptr sret([24 x i8]) align 8 %_134, ptr align 8 %_135)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %_134, i64 24, i1 false)
  br label %bb68

panic5:                                           ; preds = %bb27
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_4b26373a2560346ffe2f9e63d6fc6076) #19
  unreachable

bb68:                                             ; preds = %bb71, %bb28
; call <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
  %39 = call { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hbb728d0a8b28748cE"(ptr align 8 %iter)
  %40 = extractvalue { i64, ptr } %39, 0
  %41 = extractvalue { i64, ptr } %39, 1
  store i64 %40, ptr %_140, align 8
  %42 = getelementptr inbounds i8, ptr %_140, i64 8
  store ptr %41, ptr %42, align 8
  %43 = load i64, ptr %_140, align 8
  %44 = getelementptr inbounds i8, ptr %_140, i64 8
  %45 = load ptr, ptr %44, align 8
  %46 = ptrtoint ptr %45 to i64
  %47 = icmp eq i64 %46, 0
  %_142 = select i1 %47, i64 0, i64 1
  %48 = trunc nuw i64 %_142 to i1
  br i1 %48, label %bb71, label %bb72

bb71:                                             ; preds = %bb68
  %49 = load i64, ptr %_140, align 8
  store i64 %49, ptr %index, align 8
  %50 = getelementptr inbounds i8, ptr %_140, i64 8
  %51 = load ptr, ptr %50, align 8
  store ptr %51, ptr %value, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h95a43ed099c205cbE(ptr sret([16 x i8]) align 8 %_150, ptr align 8 %index)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7fde9e90f267252eE(ptr sret([16 x i8]) align 8 %_152, ptr align 8 %value)
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_149, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_150, i64 16, i1 false)
  %53 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_149, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %53, ptr align 8 %_152, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h7a570e7703b14cd0E(ptr sret([48 x i8]) align 8 %_146, ptr align 8 @alloc_f1f6cfcfa934f782575feca97693e20e, ptr align 8 %_149)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_146)
  br label %bb68

bb72:                                             ; preds = %bb68
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_155, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_155)
  br label %bb80

bb80:                                             ; preds = %bb72
  br label %bb81

bb81:                                             ; preds = %bb80
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_164, ptr align 8 @alloc_5700194d0098aef4e3f0ea17d9f81b0f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_164)
  br label %bb91

bb83:                                             ; No predecessors!
  br label %bb84

bb84:                                             ; preds = %bb83
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_168, ptr align 8 @alloc_3e1dc8319c2e9f7badc1123d1aa42be1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_168)
  br label %bb91

bb86:                                             ; No predecessors!
  br label %bb87

bb87:                                             ; preds = %bb86
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_172, ptr align 8 @alloc_07322d9824ec4d35e74eb9033c8d2e87)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_172)
  br label %bb91

bb89:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_175, ptr align 8 @alloc_86c76d745292d3636de6d7979ede66a6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_175)
  br label %bb91

bb91:                                             ; preds = %bb78, %bb81, %bb84, %bb87, %bb89
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_178, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_178)
; call _12_comments::calculate_rectangle_area
  %54 = call i32 @_ZN12_12_comments24calculate_rectangle_area17hac7ae20ad3b396eaE(i32 12, i32 8)
  store i32 %54, ptr %area, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_186, ptr align 4 %area)
  %55 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_185, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %55, ptr align 8 %_186, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_182, ptr align 8 @alloc_2804d0d6d39c2a65bce727b0ddd23f07, ptr align 8 %_185)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_182)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_189, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_189)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_192, ptr align 8 @alloc_e041f18fa8d6c8ef82d4bcc70d963ca6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_192)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_195, ptr align 8 @alloc_81c0284ff39ce0cdb71367c51f0cea33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_195)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h45baeeda5b16936cE"(ptr sret([12 x i8]) align 4 %_198, i32 1, i32 5)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h78e2b9e84b7604dfE"(ptr sret([12 x i8]) align 4 %_197, ptr align 4 %_198)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter1, ptr align 4 %_197, i64 12, i1 false)
  br label %bb106

bb78:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_160, ptr align 8 @alloc_56d6c3511debb15c6eed4ca29f926493)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_160)
  br label %bb91

bb106:                                            ; preds = %bb108, %bb91
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %56 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hd1e7f34fc82c970eE"(ptr align 4 %iter1)
  %57 = extractvalue { i32, i32 } %56, 0
  %58 = extractvalue { i32, i32 } %56, 1
  store i32 %57, ptr %_200, align 4
  %59 = getelementptr inbounds i8, ptr %_200, i64 4
  store i32 %58, ptr %59, align 4
  %60 = load i32, ptr %_200, align 4
  %61 = getelementptr inbounds i8, ptr %_200, i64 4
  %62 = load i32, ptr %61, align 4
  %_202 = zext i32 %60 to i64
  %63 = trunc nuw i64 %_202 to i1
  br i1 %63, label %bb108, label %bb109

bb108:                                            ; preds = %bb106
  %64 = getelementptr inbounds i8, ptr %_200, i64 4
  %65 = load i32, ptr %64, align 4
  store i32 %65, ptr %i, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_209, ptr align 4 %i)
  %66 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_208, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %66, ptr align 8 %_209, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_205, ptr align 8 @alloc_2ba1ec6542c75bf98ebfb61a85a9b057, ptr align 8 %_208)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_205)
  br label %bb106

bb109:                                            ; preds = %bb106
  store i32 1, ptr %counter, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_213, ptr align 8 @alloc_30662e36b0588b19e27c7bf09c057fa1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_213)
  br label %bb113

bb113:                                            ; preds = %bb118, %bb109
  %_216 = load i32, ptr %counter, align 4
  %_215 = icmp sle i32 %_216, 3
  br i1 %_215, label %bb114, label %bb119

bb119:                                            ; preds = %bb113
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_226, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_226)
  store ptr @alloc_b468ef00fa5916b1335583d25c8252ad, ptr %first_name, align 8
  %67 = getelementptr inbounds i8, ptr %first_name, i64 8
  store i64 5, ptr %67, align 8
  store ptr @alloc_a10b29fa1aded051447dbb8c90cd1047, ptr %last_name, align 8
  %68 = getelementptr inbounds i8, ptr %last_name, i64 8
  store i64 6, ptr %68, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h2d435e4fb848148eE(ptr sret([16 x i8]) align 8 %_236, ptr align 8 %first_name)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h2d435e4fb848148eE(ptr sret([16 x i8]) align 8 %_238, ptr align 8 %last_name)
  %69 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_235, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %69, ptr align 8 %_236, i64 16, i1 false)
  %70 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_235, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %70, ptr align 8 %_238, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb89f567ae42befd5E(ptr sret([48 x i8]) align 8 %_232, ptr align 8 @alloc_a0434d70b15bcc9ff1a7b717be16ed72, ptr align 8 %_235)
; call alloc::fmt::format
  call void @_ZN5alloc3fmt6format17h220daf6567fc508cE(ptr sret([24 x i8]) align 8 %res, ptr align 8 %_232)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %full_name, ptr align 8 %res, i64 24, i1 false)
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_241, ptr align 8 @alloc_14d01a973725b6c430f6d6d1b8fcf186)
          to label %bb127 unwind label %funclet_bb194

bb114:                                            ; preds = %bb113
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hdcf09425894ef461E(ptr sret([16 x i8]) align 8 %_222, ptr align 4 %counter)
  %71 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_221, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %71, ptr align 8 %_222, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_218, ptr align 8 @alloc_c3ef747d882502a007264a7eed8d1f64, ptr align 8 %_221)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_218)
  %72 = load i32, ptr %counter, align 4
  %73 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %72, i32 1)
  %_224.0 = extractvalue { i32, i1 } %73, 0
  %_224.1 = extractvalue { i32, i1 } %73, 1
  br i1 %_224.1, label %panic6, label %bb118

bb194:                                            ; preds = %funclet_bb194
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h1fdd9146890aa38fE"(ptr align 8 %full_name) #22 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb194:                                    ; preds = %bb191, %bb190, %bb189, %bb188, %bb187, %bb186, %bb185, %bb184, %bb183, %bb182, %bb181, %bb180, %bb179, %bb178, %bb177, %bb176, %bb175, %bb174, %bb173, %bb172, %bb171, %bb170, %bb169, %bb168, %bb167, %bb166, %bb165, %bb164, %bb163, %bb162, %bb161, %bb160, %bb159, %bb158, %bb157, %bb156, %bb155, %bb154, %bb153, %bb152, %bb151, %bb150, %bb149, %bb148, %bb147, %bb143, %bb142, %bb141, %bb140, %bb139, %bb138, %bb137, %bb136, %bb135, %bb134, %bb133, %bb132, %bb131, %bb130, %bb129, %bb128, %bb127, %bb119
  %cleanuppad = cleanuppad within none []
  br label %bb194

bb127:                                            ; preds = %bb119
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_241)
          to label %bb128 unwind label %funclet_bb194

bb128:                                            ; preds = %bb127
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hc3803372caf5fa16E(ptr sret([16 x i8]) align 8 %_248, ptr align 8 %full_name)
          to label %bb129 unwind label %funclet_bb194

bb129:                                            ; preds = %bb128
  %74 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %74, ptr align 8 %_248, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_244, ptr align 8 @alloc_e2ba38de3c2433e25c1ef4e475e0ae91, ptr align 8 %_247)
          to label %bb130 unwind label %funclet_bb194

bb130:                                            ; preds = %bb129
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_244)
          to label %bb131 unwind label %funclet_bb194

bb131:                                            ; preds = %bb130
  store ptr @alloc_7099dfc5bf30ed4602dad14f59e74cfe, ptr %text, align 8
  %75 = getelementptr inbounds i8, ptr %text, i64 8
  store i64 13, ptr %75, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h2d435e4fb848148eE(ptr sret([16 x i8]) align 8 %_256, ptr align 8 %text)
          to label %bb132 unwind label %funclet_bb194

bb132:                                            ; preds = %bb131
  %76 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_255, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %76, ptr align 8 %_256, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_252, ptr align 8 @alloc_2079bb73470e11856b0abd402a928650, ptr align 8 %_255)
          to label %bb133 unwind label %funclet_bb194

bb133:                                            ; preds = %bb132
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_252)
          to label %bb134 unwind label %funclet_bb194

bb134:                                            ; preds = %bb133
  %77 = load ptr, ptr %text, align 8
  %78 = getelementptr inbounds i8, ptr %text, i64 8
  %79 = load i64, ptr %78, align 8
; invoke core::str::<impl str>::len
  %80 = invoke i64 @"_ZN4core3str21_$LT$impl$u20$str$GT$3len17h594821fd03921d22E"(ptr align 1 %77, i64 %79)
          to label %bb135 unwind label %funclet_bb194

bb135:                                            ; preds = %bb134
  store i64 %80, ptr %_265, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h95a43ed099c205cbE(ptr sret([16 x i8]) align 8 %_263, ptr align 8 %_265)
          to label %bb136 unwind label %funclet_bb194

bb136:                                            ; preds = %bb135
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_262, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_263, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_259, ptr align 8 @alloc_5301d26306a7a7874f2dc8a275fdbe65, ptr align 8 %_262)
          to label %bb137 unwind label %funclet_bb194

bb137:                                            ; preds = %bb136
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_259)
          to label %bb138 unwind label %funclet_bb194

bb138:                                            ; preds = %bb137
  %82 = load ptr, ptr %text, align 8
  %83 = getelementptr inbounds i8, ptr %text, i64 8
  %84 = load i64, ptr %83, align 8
; invoke core::str::<impl str>::contains
  %85 = invoke zeroext i1 @"_ZN4core3str21_$LT$impl$u20$str$GT$8contains17hb958b3b8cde887c5E"(ptr align 1 %82, i64 %84, ptr align 1 @alloc_7c36acd6e5096800e8610a1984ba6ddd, i64 5)
          to label %bb139 unwind label %funclet_bb194

bb139:                                            ; preds = %bb138
  %86 = zext i1 %85 to i8
  store i8 %86, ptr %_273, align 1
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h0aef883fdb288095E(ptr sret([16 x i8]) align 8 %_271, ptr align 1 %_273)
          to label %bb140 unwind label %funclet_bb194

bb140:                                            ; preds = %bb139
  %87 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_270, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %87, ptr align 8 %_271, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_267, ptr align 8 @alloc_fddd4bceb3c789185e3334d008fe2223, ptr align 8 %_270)
          to label %bb141 unwind label %funclet_bb194

bb141:                                            ; preds = %bb140
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_267)
          to label %bb142 unwind label %funclet_bb194

bb142:                                            ; preds = %bb141
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_275, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb143 unwind label %funclet_bb194

bb143:                                            ; preds = %bb142
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_275)
          to label %bb144 unwind label %funclet_bb194

bb144:                                            ; preds = %bb143
  %88 = load i32, ptr %age, align 4
  %89 = icmp sge i32 %88, 18
  %90 = zext i1 %89 to i8
  store i8 %90, ptr %is_adult, align 1
  %91 = load double, ptr %salary, align 8
  %92 = fcmp ogt double %91, 0.000000e+00
  %93 = zext i1 %92 to i8
  store i8 %93, ptr %has_income, align 1
  %94 = load i8, ptr %is_adult, align 1
  %95 = trunc nuw i8 %94 to i1
  br i1 %95, label %bb145, label %bb146

bb146:                                            ; preds = %bb144
  store i8 0, ptr %is_eligible, align 1
  br label %bb147

bb145:                                            ; preds = %bb144
  %96 = load i8, ptr %has_income, align 1
  %97 = trunc nuw i8 %96 to i1
  %98 = zext i1 %97 to i8
  store i8 %98, ptr %is_eligible, align 1
  br label %bb147

bb147:                                            ; preds = %bb145, %bb146
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_281, ptr align 8 @alloc_2ef2a44534689a452eb3d6048a546613)
          to label %bb148 unwind label %funclet_bb194

bb148:                                            ; preds = %bb147
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_281)
          to label %bb149 unwind label %funclet_bb194

bb149:                                            ; preds = %bb148
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h0aef883fdb288095E(ptr sret([16 x i8]) align 8 %_288, ptr align 1 %is_adult)
          to label %bb150 unwind label %funclet_bb194

bb150:                                            ; preds = %bb149
  %99 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_287, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %99, ptr align 8 %_288, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_284, ptr align 8 @alloc_fbe59325d2f56c01006873536ec16a23, ptr align 8 %_287)
          to label %bb151 unwind label %funclet_bb194

bb151:                                            ; preds = %bb150
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_284)
          to label %bb152 unwind label %funclet_bb194

bb152:                                            ; preds = %bb151
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h0aef883fdb288095E(ptr sret([16 x i8]) align 8 %_295, ptr align 1 %has_income)
          to label %bb153 unwind label %funclet_bb194

bb153:                                            ; preds = %bb152
  %100 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_294, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %100, ptr align 8 %_295, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_291, ptr align 8 @alloc_aa1ad406e09f54625f465fc645fd2dc8, ptr align 8 %_294)
          to label %bb154 unwind label %funclet_bb194

bb154:                                            ; preds = %bb153
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_291)
          to label %bb155 unwind label %funclet_bb194

bb155:                                            ; preds = %bb154
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h0aef883fdb288095E(ptr sret([16 x i8]) align 8 %_302, ptr align 1 %is_eligible)
          to label %bb156 unwind label %funclet_bb194

bb156:                                            ; preds = %bb155
  %101 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_301, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %101, ptr align 8 %_302, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h31a6b2d78000159aE(ptr sret([48 x i8]) align 8 %_298, ptr align 8 @alloc_0ed86fe2d083c03bac3f5ae9948cd9e2, ptr align 8 %_301)
          to label %bb157 unwind label %funclet_bb194

bb157:                                            ; preds = %bb156
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_298)
          to label %bb158 unwind label %funclet_bb194

bb158:                                            ; preds = %bb157
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_305, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb159 unwind label %funclet_bb194

bb159:                                            ; preds = %bb158
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_305)
          to label %bb160 unwind label %funclet_bb194

bb160:                                            ; preds = %bb159
  store double 1.000000e+03, ptr %principal, align 8
  store double 2.000000e+00, ptr %time, align 8
  %102 = load double, ptr %time, align 8
; invoke std::f64::<impl f64>::powf
  %_311 = invoke double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17h35c51cd84474d0a0E"(double 1.050000e+00, double %102)
          to label %bb161 unwind label %funclet_bb194

bb161:                                            ; preds = %bb160
  %103 = load double, ptr %principal, align 8
  %104 = fmul double %103, %_311
  store double %104, ptr %amount, align 8
  %105 = load double, ptr %amount, align 8
  %106 = load double, ptr %principal, align 8
  %107 = fsub double %105, %106
  store double %107, ptr %interest, align 8
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_315, ptr align 8 @alloc_2c79ae6afef2713bf7bdf4a91ceb42d4)
          to label %bb162 unwind label %funclet_bb194

bb162:                                            ; preds = %bb161
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_315)
          to label %bb163 unwind label %funclet_bb194

bb163:                                            ; preds = %bb162
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_324, ptr align 8 %principal)
          to label %bb164 unwind label %funclet_bb194

bb164:                                            ; preds = %bb163
  %108 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_323, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %108, ptr align 8 %_324, i64 16, i1 false)
  %109 = getelementptr inbounds i8, ptr %_330, i64 2
  store i16 2, ptr %109, align 2
  store i16 0, ptr %_330, align 8
  store i16 2, ptr %_331, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_329, i64 0, i32 -268435424, ptr align 8 %_330, ptr align 8 %_331)
          to label %bb165 unwind label %funclet_bb194

bb165:                                            ; preds = %bb164
  %110 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_328, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %110, ptr align 8 %_329, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E()
          to label %bb166 unwind label %funclet_bb194

bb166:                                            ; preds = %bb165
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_318, ptr align 8 @alloc_f3605924ed5959fc81c8e45edc7a92e7, i64 2, ptr align 8 %_323, i64 1, ptr align 8 %_328, i64 1)
          to label %bb167 unwind label %funclet_bb194

bb167:                                            ; preds = %bb166
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_318)
          to label %bb168 unwind label %funclet_bb194

bb168:                                            ; preds = %bb167
  store double 5.000000e+00, ptr %_342, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_340, ptr align 8 %_342)
          to label %bb169 unwind label %funclet_bb194

bb169:                                            ; preds = %bb168
  %111 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_339, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %111, ptr align 8 %_340, i64 16, i1 false)
  %112 = getelementptr inbounds i8, ptr %_347, i64 2
  store i16 1, ptr %112, align 2
  store i16 0, ptr %_347, align 8
  store i16 2, ptr %_348, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_346, i64 0, i32 -268435424, ptr align 8 %_347, ptr align 8 %_348)
          to label %bb170 unwind label %funclet_bb194

bb170:                                            ; preds = %bb169
  %113 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_345, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %113, ptr align 8 %_346, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E()
          to label %bb171 unwind label %funclet_bb194

bb171:                                            ; preds = %bb170
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_334, ptr align 8 @alloc_280cc4bd13691fcf8685661ccf810602, i64 2, ptr align 8 %_339, i64 1, ptr align 8 %_345, i64 1)
          to label %bb172 unwind label %funclet_bb194

bb172:                                            ; preds = %bb171
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_334)
          to label %bb173 unwind label %funclet_bb194

bb173:                                            ; preds = %bb172
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_357, ptr align 8 %time)
          to label %bb174 unwind label %funclet_bb194

bb174:                                            ; preds = %bb173
  %114 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_356, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %114, ptr align 8 %_357, i64 16, i1 false)
  %115 = getelementptr inbounds i8, ptr %_363, i64 2
  store i16 0, ptr %115, align 2
  store i16 0, ptr %_363, align 8
  store i16 2, ptr %_364, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_362, i64 0, i32 -268435424, ptr align 8 %_363, ptr align 8 %_364)
          to label %bb175 unwind label %funclet_bb194

bb175:                                            ; preds = %bb174
  %116 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_361, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %116, ptr align 8 %_362, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E()
          to label %bb176 unwind label %funclet_bb194

bb176:                                            ; preds = %bb175
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_351, ptr align 8 @alloc_ab67f7fe1291fe0d5abc97efdf942547, i64 2, ptr align 8 %_356, i64 1, ptr align 8 %_361, i64 1)
          to label %bb177 unwind label %funclet_bb194

bb177:                                            ; preds = %bb176
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_351)
          to label %bb178 unwind label %funclet_bb194

bb178:                                            ; preds = %bb177
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_373, ptr align 8 %amount)
          to label %bb179 unwind label %funclet_bb194

bb179:                                            ; preds = %bb178
  %117 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_372, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %117, ptr align 8 %_373, i64 16, i1 false)
  %118 = getelementptr inbounds i8, ptr %_379, i64 2
  store i16 2, ptr %118, align 2
  store i16 0, ptr %_379, align 8
  store i16 2, ptr %_380, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_378, i64 0, i32 -268435424, ptr align 8 %_379, ptr align 8 %_380)
          to label %bb180 unwind label %funclet_bb194

bb180:                                            ; preds = %bb179
  %119 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_377, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %119, ptr align 8 %_378, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E()
          to label %bb181 unwind label %funclet_bb194

bb181:                                            ; preds = %bb180
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_367, ptr align 8 @alloc_484598a9a2d17a346ada0a4a52f37371, i64 2, ptr align 8 %_372, i64 1, ptr align 8 %_377, i64 1)
          to label %bb182 unwind label %funclet_bb194

bb182:                                            ; preds = %bb181
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_367)
          to label %bb183 unwind label %funclet_bb194

bb183:                                            ; preds = %bb182
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5c655c5a30cfa731E(ptr sret([16 x i8]) align 8 %_389, ptr align 8 %interest)
          to label %bb184 unwind label %funclet_bb194

bb184:                                            ; preds = %bb183
  %120 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_388, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %120, ptr align 8 %_389, i64 16, i1 false)
  %121 = getelementptr inbounds i8, ptr %_395, i64 2
  store i16 2, ptr %121, align 2
  store i16 0, ptr %_395, align 8
  store i16 2, ptr %_396, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h0f8432fde59e6a32E(ptr sret([48 x i8]) align 8 %_394, i64 0, i32 -268435424, ptr align 8 %_395, ptr align 8 %_396)
          to label %bb185 unwind label %funclet_bb194

bb185:                                            ; preds = %bb184
  %122 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_393, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %122, ptr align 8 %_394, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17ha6080549ff9428e5E()
          to label %bb186 unwind label %funclet_bb194

bb186:                                            ; preds = %bb185
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17ha3af7ad5dfa11aa1E(ptr sret([48 x i8]) align 8 %_383, ptr align 8 @alloc_024462f0deb68215125012e9bae6f974, i64 2, ptr align 8 %_388, i64 1, ptr align 8 %_393, i64 1)
          to label %bb187 unwind label %funclet_bb194

bb187:                                            ; preds = %bb186
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_383)
          to label %bb188 unwind label %funclet_bb194

bb188:                                            ; preds = %bb187
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_399, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb189 unwind label %funclet_bb194

bb189:                                            ; preds = %bb188
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_399)
          to label %bb190 unwind label %funclet_bb194

bb190:                                            ; preds = %bb189
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17he339f35ca466d2f6E(ptr sret([48 x i8]) align 8 %_402, ptr align 8 @alloc_87cbbb52c89333e8d849f3aecffe4859)
          to label %bb191 unwind label %funclet_bb194

bb191:                                            ; preds = %bb190
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_402)
          to label %bb192 unwind label %funclet_bb194

bb192:                                            ; preds = %bb191
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h1fdd9146890aa38fE"(ptr align 8 %full_name)
  ret void

bb118:                                            ; preds = %bb114
  store i32 %_224.0, ptr %counter, align 4
  br label %bb113

panic6:                                           ; preds = %bb114
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2ff50d90c9886a185b4d1047e9156af3) #19
  unreachable

bb70:                                             ; No predecessors!
  unreachable
}

; _12_comments::calculate_rectangle_area
; Function Attrs: uwtable
define internal i32 @_ZN12_12_comments24calculate_rectangle_area17hac7ae20ad3b396eaE(i32 %length, i32 %width) unnamed_addr #2 {
start:
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %length, i32 %width)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_1b7075675b896b820b4618344fc982b4) #19
  unreachable
}

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #6

; core::slice::index::slice_end_index_len_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core5slice5index24slice_end_index_len_fail17ha318ba5d7b15d3edE(i64, i64, ptr align 8) unnamed_addr #7

; core::slice::index::slice_index_order_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core5slice5index22slice_index_order_fail17hdf994c7f82cfe787E(i64, i64, ptr align 8) unnamed_addr #7

declare i32 @__CxxFrameHandler3(...) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #9

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #7

; core::slice::index::slice_start_index_len_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core5slice5index26slice_start_index_len_fail17hc7aabe8c812252f8E(i64, i64, ptr align 8) unnamed_addr #7

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #2

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.pow.f64(double, double) #9

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #2

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #2

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #9

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #10

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #7

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #11

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #2

; core::fmt::float::<impl core::fmt::Display for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E"(ptr align 8, ptr align 8) unnamed_addr #2

; core::fmt::num::imp::<impl core::fmt::Display for usize>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE"(ptr align 8, ptr align 8) unnamed_addr #2

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #9

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #7

; core::panicking::panic_bounds_check
; Function Attrs: cold minsize noinline noreturn optsize uwtable
declare void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64, i64, ptr align 8) unnamed_addr #12

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.usub.sat.i64(i64, i64) #9

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: write)
declare void @llvm.memset.p0.i64(ptr nocapture writeonly, i8, i64, i1 immarg) #13

; core::option::expect_failed
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core6option13expect_failed17h3c22898af43fbfd1E(ptr align 1, i64, ptr align 8) unnamed_addr #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i16 @llvm.cttz.i16(i16, i1 immarg) #9

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #2

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #7

; core::slice::memchr::memchr_aligned
; Function Attrs: uwtable
declare { i64, i64 } @_ZN4core5slice6memchr14memchr_aligned17hcb56f13120f06db1E(i8, ptr align 1, i64) unnamed_addr #2

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i1 @llvm.vector.reduce.and.v16i1(<16 x i1>) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i8 @llvm.ucmp.i8.i64(i64, i64) #9

; core::str::pattern::StrSearcher::new
; Function Attrs: uwtable
declare void @_ZN4core3str7pattern11StrSearcher3new17h35c9b30eeae16457E(ptr sret([104 x i8]) align 8, ptr align 1, i64, ptr align 1, i64) unnamed_addr #2

; alloc::fmt::format::format_inner
; Function Attrs: uwtable
declare void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8, ptr align 8) unnamed_addr #2

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #14

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #15

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #16

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #17

declare i32 @memcmp(ptr, ptr, i64)

; core::str::slice_error_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core3str16slice_error_fail17h9592f524d3a93436E(ptr align 1, i64, i64, i64, ptr align 8) unnamed_addr #7

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #2

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #7

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #7

define i32 @main(i32 %0, ptr %1) unnamed_addr #8 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h0c1d30d854da2e0bE(ptr @_ZN12_12_comments4main17hf1bb7d95dc082421E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { cold inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #7 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { "target-cpu"="x86-64" }
attributes #9 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #10 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #11 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #12 = { cold minsize noinline noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { nocallback nofree nounwind willreturn memory(argmem: write) }
attributes #14 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #16 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #17 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #18 = { noreturn nounwind }
attributes #19 = { noreturn }
attributes #20 = { nounwind }
attributes #21 = { cold noreturn nounwind }
attributes #22 = { cold }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 6130610549933983}
