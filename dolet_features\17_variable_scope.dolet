# 17. Variable Scope - Local, Global, and Function Scope
# This file demonstrates variable scope in Dolet

print "=== Variable Scope Demo ==="
print ""

# Global variables
print "Global Variables:"
int global_counter = 0
string global_name = "Global Variable"
bool global_flag = true

print "global_counter = " + global_counter
print "global_name = " + global_name
print "global_flag = " + global_flag
print ""

# Function with local variables
print "Function with Local Variables:"

fun test_local_scope() {
    # Local variables - only accessible within this function
    int local_counter = 10
    string local_name = "Local Variable"
    bool local_flag = false
    
    print "Inside function:"
    print "  local_counter = " + local_counter
    print "  local_name = " + local_name
    print "  local_flag = " + local_flag
    
    # Accessing global variables from within function
    print "  global_counter (from global) = " + global_counter
    print "  global_name (from global) = " + global_name
}

test_local_scope()
print "After function call - global variables still accessible:"
print "global_counter = " + global_counter
print "global_name = " + global_name
print ""

# Variable shadowing
print "Variable Shadowing:"
int number = 100  # Global variable

fun test_shadowing() {
    int number = 200  # Local variable shadows global
    print "Inside function - number = " + number  # Prints 200
    
    # Modifying local variable
    set number = 250
    print "After modification - number = " + number  # Prints 250
}

print "Before function - global number = " + number  # Prints 100
test_shadowing()
print "After function - global number = " + number   # Still prints 100
print ""

# Function parameters scope
print "Function Parameters Scope:"

fun calculate_area(length, width) {
    # Parameters are local to the function
    print "Inside calculate_area:"
    print "  length = " + length
    print "  width = " + width
    
    int area = length * width  # Local variable
    print "  area = " + area
    
    return area
}

int room_length = 12
int room_width = 8
print "Before function call:"
print "room_length = " + room_length
print "room_width = " + room_width

int result = calculate_area(room_length, room_width)
print "After function call:"
print "result = " + result
print "room_length = " + room_length  # Unchanged
print "room_width = " + room_width    # Unchanged
print ""

# Nested function scope
print "Nested Function Scope:"

fun outer_function() {
    int outer_var = 30
    string outer_message = "From outer function"
    
    print "In outer function:"
    print "  outer_var = " + outer_var
    print "  outer_message = " + outer_message
    
    fun inner_function() {
        int inner_var = 40
        string inner_message = "From inner function"
        
        print "  In inner function:"
        print "    inner_var = " + inner_var
        print "    inner_message = " + inner_message
        print "    outer_var (accessible) = " + outer_var
        print "    outer_message (accessible) = " + outer_message
    }
    
    inner_function()
    print "  Back in outer function"
}

outer_function()
print ""

# Loop variable scope
print "Loop Variable Scope:"

for i = 1 to 3
    int loop_var = i * 10
    print "Loop iteration " + i + ": loop_var = " + loop_var
    
    # Nested loop with different scope
    for j = 1 to 2
        int nested_loop_var = j * 100
        print "  Nested loop " + j + ": nested_loop_var = " + nested_loop_var
        print "  Can access outer loop_var = " + loop_var

print "After loops completed"
print ""

# Conditional scope
print "Conditional Scope:"
int condition_test = 5

if condition_test > 0
    int positive_var = 100
    print "In positive condition: positive_var = " + positive_var
    
    if condition_test > 3
        int very_positive_var = 200
        print "  In nested condition: very_positive_var = " + very_positive_var
        print "  Can access positive_var = " + positive_var
else
    int negative_var = -100
    print "In negative condition: negative_var = " + negative_var

print "After conditional blocks"
print ""

# Global variable modification in functions
print "Global Variable Modification:"
int global_score = 0

fun increase_score(points) {
    print "Before increase: global_score = " + global_score
    set global_score = global_score + points
    print "After increase: global_score = " + global_score
}

fun reset_score() {
    print "Resetting global_score to 0"
    set global_score = 0
}

print "Initial global_score = " + global_score
increase_score(10)
increase_score(25)
print "Final global_score = " + global_score
reset_score()
print "After reset: global_score = " + global_score
print ""

# Variable lifetime demonstration
print "Variable Lifetime:"

fun create_temporary_variables() {
    int temp1 = 111
    int temp2 = 222
    string temp_message = "Temporary variables"
    
    print "Temporary variables created:"
    print "  temp1 = " + temp1
    print "  temp2 = " + temp2
    print "  temp_message = " + temp_message
    
    return temp1 + temp2
}

int sum_of_temps = create_temporary_variables()
print "Sum returned from function: " + sum_of_temps
print "Temporary variables are now out of scope"
print ""

# Scope with arrays
print "Scope with Arrays:"
array int global_array = [1, 2, 3, 4, 5]

fun modify_array() {
    print "In function - accessing global array:"
    for i = 0 to 4
        print "  global_array[" + i + "] = " + global_array[i]
    
    # Modify global array
    set global_array[0] = 100
    set global_array[4] = 500
    
    # Create local array
    array int local_array = [10, 20, 30]
    print "Local array created:"
    for i = 0 to 2
        print "  local_array[" + i + "] = " + local_array[i]
}

print "Before function call:"
for i = 0 to 4
    print "global_array[" + i + "] = " + global_array[i]

modify_array()

print "After function call:"
for i = 0 to 4
    print "global_array[" + i + "] = " + global_array[i]
print ""

# Best practices demonstration
print "Best Practices:"

# Use descriptive names for different scopes
int application_setting = 1000  # Global setting
string application_name = "Dolet Demo"  # Global constant

fun process_user_data(user_id, user_name) {
    # Function parameters with clear names
    int processing_status = 0  # Local processing variable
    string processing_message = "Processing user: " + user_name
    
    print processing_message
    print "User ID: " + user_id
    print "Application: " + application_name  # Access global
    
    # Local calculation
    int user_score = user_id * 10
    return user_score
}

int result_score = process_user_data(123, "Ahmed")
print "Processing completed. Score: " + result_score
print ""

print "=== End of Variable Scope Demo ==="
