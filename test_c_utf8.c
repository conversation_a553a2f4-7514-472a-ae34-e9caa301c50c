#include <stdio.h>
#include <locale.h>

int main() {
    // Set locale to UTF-8
    setlocale(LC_ALL, "");
    
    printf("🎉 Hello World! 🚀\n");
    printf("✅ This has emojis ❌ but will work fine 💯\n");
    printf("Arabic: مرحبا بالعالم\n");
    printf("Chinese: 你好世界\n");
    printf("Regular text works fine\n");
    
    int x = 10;
    int y = 20;
    printf("🔢 Math: %d + %d = %d 🎯\n", x, y, x + y);
    printf("🌟 Unicode test in C! 🌟\n");
    
    return 0;
}
