# 07. Functions - Reusable Code Blocks
# This file demonstrates function definition and usage in Dolet

print "=== Functions Demo ==="
print ""

# Simple function without parameters
fun greet() {
    print "Hello from function!"
    print "This is a simple function"
}

print "Calling simple function:"
greet()
print ""

# Function with parameters
fun greet_person(name) {
    print "Hello, " + name + "!"
    print "Nice to meet you"
}

print "Calling function with parameter:"
greet_person("<PERSON>")
greet_person("<PERSON>")
print ""

# Function with multiple parameters
fun introduce(name, age, city) {
    print "Name: " + name
    print "Age: " + age
    print "City: " + city
    print "---"
}

print "Calling function with multiple parameters:"
introduce("Ali", 25, "Cairo")
introduce("Fatima", 30, "Alexandria")
print ""

# Function with return value
fun add_numbers(a, b) {
    int result = a + b
    return result
}

print "Function with return value:"
int sum1 = add_numbers(5, 3)
int sum2 = add_numbers(10, 20)
print "5 + 3 = " + sum1
print "10 + 20 = " + sum2
print ""

# Function with calculations
fun calculate_area(length, width) {
    int area = length * width
    print "Calculating area..."
    print "Length: " + length
    print "Width: " + width
    print "Area: " + area
    return area
}

print "Area calculation function:"
int room_area = calculate_area(12, 8)
print "Room area: " + room_area
print ""

# Function with conditional logic
fun check_grade(score) {
    if score >= 90
        print "Excellent! Grade A"
        return "A"
    elif score >= 80
        print "Very Good! Grade B"
        return "B"
    elif score >= 70
        print "Good! Grade C"
        return "C"
    elif score >= 60
        print "Pass! Grade D"
        return "D"
    else
        print "Fail! Grade F"
        return "F"
}

print "Grade checking function:"
string grade1 = check_grade(95)
string grade2 = check_grade(75)
string grade3 = check_grade(55)
print ""

# Function with loops
fun print_table(number) {
    print "Multiplication table for " + number + ":"
    for i = 1 to 10
        int result = number * i
        print number + " x " + i + " = " + result
}

print "Multiplication table function:"
print_table(7)
print ""

# Recursive function (factorial)
fun factorial(n) {
    if n <= 1
        return 1
    else
        int prev = factorial(n - 1)
        return n * prev
}

print "Recursive factorial function:"
int fact5 = factorial(5)
int fact6 = factorial(6)
print "5! = " + fact5
print "6! = " + fact6
print ""

# Function with string operations
fun create_message(greeting, name, punctuation) {
    string message = greeting + ", " + name + punctuation
    return message
}

print "String manipulation function:"
string msg1 = create_message("Hello", "World", "!")
string msg2 = create_message("Good morning", "Ahmed", ".")
print msg1
print msg2
print ""

# Function with boolean logic
fun is_even(number) {
    if number % 2 == 0
        return true
    else
        return false
}

print "Boolean function:"
bool result1 = is_even(10)
bool result2 = is_even(7)
print "10 is even: " + result1
print "7 is even: " + result2
print ""

# Function calling other functions
fun process_number(num) {
    print "Processing number: " + num
    
    bool even = is_even(num)
    if even
        print num + " is even"
    else
        print num + " is odd"
    
    if num > 0
        int fact = factorial(num)
        print num + "! = " + fact
}

print "Function calling other functions:"
process_number(4)
print ""
process_number(5)
print ""

# Function with default behavior
fun display_info(name, title) {
    if title == ""
        set title = "Person"
    
    print "Title: " + title
    print "Name: " + name
    print "Full: " + title + " " + name
}

print "Function with default behavior:"
display_info("Ahmed", "Dr.")
display_info("Sara", "")
print ""

print "=== End of Functions Demo ==="
