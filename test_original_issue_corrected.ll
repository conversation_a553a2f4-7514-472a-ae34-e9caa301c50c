; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 4 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [87 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\22,./<>?   \0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [17 x i8] c"Special string: \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1
@str_dyn_0 = private unnamed_addr constant [86 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\\\22,./<>?   \00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_special_string = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "Testing original string issue:" (inline)
  %tmp_1_str = alloca [31 x i8], align 1
  store [31 x i8] c"Testing original string issue:\00", [31 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [31 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string special_string = "   Multiple   Spaces   And   Special   Characters   !@#$%^&*()_+-=[]{}|;':\",./<>?   "
  store i8* getelementptr inbounds ([86 x i8], [86 x i8]* @str_dyn_0, i64 0, i64 0), i8** @global_special_string, align 8
  ; print expression: "Special string: " + special_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_3 = load i8*, i8** @global_special_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_3)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Test completed!" (inline)
  %tmp_4_str = alloca [16 x i8], align 1
  store [16 x i8] c"Test completed!\00", [16 x i8]* %tmp_4_str, align 1
  %tmp_5 = bitcast [16 x i8]* %tmp_4_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_5)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
