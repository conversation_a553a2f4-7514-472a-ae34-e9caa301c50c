# 31. Algorithm Examples - Classic Algorithms Implementation
# This file demonstrates classic algorithms in Dolet

print "=== Algorithm Examples Demo ==="
print ""

# Sorting Algorithms
print "Sorting Algorithms:"

# Bubble Sort
fun bubble_sort(arr, size) {
    print "Bubble Sort Algorithm:"
    print "  Input: array of size " + size
    
    for i = 0 to size - 2
        bool swapped = false
        print "  Pass " + (i + 1) + ":"
        
        for j = 0 to size - 2 - i
            if arr[j] > arr[j + 1]
                print "    Swapping " + arr[j] + " and " + arr[j + 1]
                int temp = arr[j]
                set arr[j] = arr[j + 1]
                set arr[j + 1] = temp
                set swapped = true
        
        if not swapped
            print "    No swaps needed - array is sorted"
            break
    
    print "  Bubble sort completed"
}

# Selection Sort
fun selection_sort(arr, size) {
    print "Selection Sort Algorithm:"
    print "  Input: array of size " + size
    
    for i = 0 to size - 2
        int min_index = i
        print "  Finding minimum from index " + i + ":"
        
        for j = i + 1 to size - 1
            if arr[j] < arr[min_index]
                set min_index = j
                print "    New minimum: " + arr[j] + " at index " + j
        
        if min_index != i
            print "    Swapping " + arr[i] + " and " + arr[min_index]
            int temp = arr[i]
            set arr[i] = arr[min_index]
            set arr[min_index] = temp
    
    print "  Selection sort completed"
}

array int sort_test1 = [64, 34, 25, 12, 22, 11, 90]
array int sort_test2 = [64, 34, 25, 12, 22, 11, 90]

print "Original array: [64, 34, 25, 12, 22, 11, 90]"
bubble_sort(sort_test1, 7)
print "Bubble sorted: [11, 12, 22, 25, 34, 64, 90]"

selection_sort(sort_test2, 7)
print "Selection sorted: [11, 12, 22, 25, 34, 64, 90]"
print ""

# Search Algorithms
print "Search Algorithms:"

# Linear Search
fun linear_search_detailed(arr, size, target) {
    print "Linear Search for " + target + ":"
    
    for i = 0 to size - 1
        print "  Checking index " + i + ": " + arr[i]
        
        if arr[i] == target
            print "  Found " + target + " at index " + i
            return i
    
    print "  " + target + " not found"
    return -1
}

# Binary Search
fun binary_search_detailed(arr, size, target) {
    print "Binary Search for " + target + " (array must be sorted):"
    
    int left = 0
    int right = size - 1
    
    while left <= right
        int mid = (left + right) / 2
        print "  Checking middle index " + mid + ": " + arr[mid]
        
        if arr[mid] == target
            print "  Found " + target + " at index " + mid
            return mid
        elif arr[mid] < target
            print "    Target is larger, searching right half"
            set left = mid + 1
        else
            print "    Target is smaller, searching left half"
            set right = mid - 1
    
    print "  " + target + " not found"
    return -1
}

array int search_array = [11, 12, 22, 25, 34, 64, 90]
print "Searching in sorted array: [11, 12, 22, 25, 34, 64, 90]"

int linear_result = linear_search_detailed(search_array, 7, 25)
int binary_result = binary_search_detailed(search_array, 7, 25)
print ""

# Graph Algorithms
print "Graph Algorithms:"

# Graph representation using adjacency matrix
array int graph_row0 = [0, 1, 1, 0, 0]
array int graph_row1 = [1, 0, 1, 1, 0]
array int graph_row2 = [1, 1, 0, 1, 1]
array int graph_row3 = [0, 1, 1, 0, 1]
array int graph_row4 = [0, 0, 1, 1, 0]

fun get_graph_edge(from, to) {
    if from == 0
        return graph_row0[to]
    elif from == 1
        return graph_row1[to]
    elif from == 2
        return graph_row2[to]
    elif from == 3
        return graph_row3[to]
    else
        return graph_row4[to]
}

# Depth-First Search
array bool dfs_visited = [false, false, false, false, false]

fun dfs(node) {
    print "  Visiting node " + node
    set dfs_visited[node] = true
    
    for neighbor = 0 to 4
        int edge = get_graph_edge(node, neighbor)
        if edge == 1 and not dfs_visited[neighbor]
            print "    Found unvisited neighbor " + neighbor
            dfs(neighbor)
}

print "Depth-First Search starting from node 0:"
dfs(0)
print ""

# Breadth-First Search simulation
fun bfs(start_node) {
    print "Breadth-First Search starting from node " + start_node + ":"
    
    array bool bfs_visited = [false, false, false, false, false]
    array int queue = [0, 0, 0, 0, 0]
    int queue_front = 0
    int queue_rear = 0
    
    set queue[queue_rear] = start_node
    set queue_rear = queue_rear + 1
    set bfs_visited[start_node] = true
    
    while queue_front < queue_rear
        int current = queue[queue_front]
        set queue_front = queue_front + 1
        
        print "  Visiting node " + current
        
        for neighbor = 0 to 4
            int edge = get_graph_edge(current, neighbor)
            if edge == 1 and not bfs_visited[neighbor]
                print "    Adding neighbor " + neighbor + " to queue"
                set queue[queue_rear] = neighbor
                set queue_rear = queue_rear + 1
                set bfs_visited[neighbor] = true
}

bfs(0)
print ""

# Dynamic Programming
print "Dynamic Programming:"

# Fibonacci with memoization
array int fib_memo = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

fun fibonacci_dp(n) {
    print "Fibonacci(" + n + ") using dynamic programming:"
    
    if n <= 1
        return n
    
    if fib_memo[n] != 0
        print "  Using memoized value: " + fib_memo[n]
        return fib_memo[n]
    
    print "  Computing fibonacci(" + n + ")"
    int result = fibonacci_dp(n - 1) + fibonacci_dp(n - 2)
    set fib_memo[n] = result
    
    print "  Memoizing fibonacci(" + n + ") = " + result
    return result
}

print "Computing fibonacci(8) with memoization:"
int fib_result = fibonacci_dp(8)
print "Result: " + fib_result
print ""

# Longest Common Subsequence (simplified)
fun lcs_length(str1_len, str2_len) {
    print "Longest Common Subsequence (simplified):"
    print "  String 1 length: " + str1_len
    print "  String 2 length: " + str2_len
    
    # Simplified LCS using dynamic programming concept
    array int dp_row0 = [0, 0, 0, 0, 0]
    array int dp_row1 = [0, 0, 0, 0, 0]
    array int dp_row2 = [0, 0, 0, 0, 0]
    array int dp_row3 = [0, 0, 0, 0, 0]
    
    # Simulate filling DP table
    for i = 1 to str1_len
        for j = 1 to str2_len
            # Simplified logic
            int diagonal = 0
            int left = 0
            int top = 0
            
            if i == j  # Simulate character match
                set diagonal = 1
            
            int max_val = diagonal
            if left > max_val
                set max_val = left
            if top > max_val
                set max_val = top
            
            print "    dp[" + i + "][" + j + "] = " + max_val
    
    print "  LCS length: " + str1_len  # Simplified result
    return str1_len
}

int lcs_result = lcs_length(4, 4)
print ""

# Greedy Algorithms
print "Greedy Algorithms:"

# Coin Change (Greedy approach)
fun coin_change_greedy(amount) {
    print "Coin Change (Greedy) for amount: " + amount
    
    array int coins = [25, 10, 5, 1]  # Quarter, Dime, Nickel, Penny
    array string coin_names = ["Quarter", "Dime", "Nickel", "Penny"]
    int remaining = amount
    int total_coins = 0
    
    for i = 0 to 3
        if remaining >= coins[i]
            int count = remaining / coins[i]
            set remaining = remaining % coins[i]
            set total_coins = total_coins + count
            
            print "  Use " + count + " " + coin_names[i] + "(s) = " + (count * coins[i]) + " cents"
    
    print "  Total coins used: " + total_coins
    print "  Remaining amount: " + remaining
    
    return total_coins
}

int coins_needed = coin_change_greedy(67)
print ""

# Activity Selection Problem
fun activity_selection() {
    print "Activity Selection Problem (Greedy):"
    
    array int start_times = [1, 3, 0, 5, 8, 5]
    array int end_times = [2, 4, 6, 7, 9, 9]
    array string activities = ["A1", "A2", "A3", "A4", "A5", "A6"]
    
    print "  Activities with start and end times:"
    for i = 0 to 5
        print "    " + activities[i] + ": [" + start_times[i] + ", " + end_times[i] + "]"
    
    # Greedy selection (assuming sorted by end time)
    array bool selected = [false, false, false, false, false, false]
    set selected[0] = true  # Select first activity
    int last_end_time = end_times[0]
    int selected_count = 1
    
    print "  Selected " + activities[0] + " (ends at " + end_times[0] + ")"
    
    for i = 1 to 5
        if start_times[i] >= last_end_time
            set selected[i] = true
            set last_end_time = end_times[i]
            set selected_count = selected_count + 1
            print "  Selected " + activities[i] + " (starts at " + start_times[i] + ", ends at " + end_times[i] + ")"
    
    print "  Total activities selected: " + selected_count
    return selected_count
}

int activities_selected = activity_selection()
print ""

print "=== End of Algorithm Examples Demo ==="
