// 17. Recursive Functions - functions calling themselves (Rust version)
// This file demonstrates recursive functions in Rust for comparison with Dolet

fn main() {
    println!("=== Recursive Functions Demo (Rust) ===");
    println!();

    // Simple factorial recursion
    println!("Factorial recursion:");
    for i in 0..=6 {
        let result = factorial(i);
        println!("factorial({}) = {}", i, result);
    }
    println!();

    // Fibonacci sequence recursion
    println!("Fibonacci sequence recursion:");
    for i in 0..=10 {
        let result = fibonacci(i);
        println!("fibonacci({}) = {}", i, result);
    }
    println!();

    // Power function recursion
    println!("Power function recursion:");
    let test_cases = [(2, 0), (2, 1), (2, 3), (3, 4), (5, 2)];
    for (base, exp) in test_cases {
        let result = power(base, exp);
        println!("power({}, {}) = {}", base, exp, result);
    }
    println!();

    // Sum of digits recursion
    println!("Sum of digits recursion:");
    let numbers = [123, 456, 789, 1000, 9876];
    for num in numbers {
        let result = sum_of_digits(num);
        println!("sum_of_digits({}) = {}", num, result);
    }
    println!();

    // Countdown recursion
    println!("Countdown recursion:");
    countdown(5);
    println!();

    // Count up recursion
    println!("Count up recursion:");
    count_up(1, 5);
    println!();

    // Array sum recursion
    println!("Array sum recursion:");
    let arr1 = vec![1, 2, 3, 4, 5];
    let arr2 = vec![10, 20, 30];
    let arr3 = vec![7];
    
    println!("array_sum({:?}) = {}", arr1, array_sum(&arr1, 0));
    println!("array_sum({:?}) = {}", arr2, array_sum(&arr2, 0));
    println!("array_sum({:?}) = {}", arr3, array_sum(&arr3, 0));
    println!();

    // Greatest Common Divisor (GCD) recursion
    println!("GCD recursion:");
    let gcd_cases = [(48, 18), (100, 25), (17, 13), (56, 42)];
    for (a, b) in gcd_cases {
        let result = gcd(a, b);
        println!("gcd({}, {}) = {}", a, b, result);
    }
    println!();

    // Binary search recursion
    println!("Binary search recursion:");
    let sorted_array = vec![1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
    let search_values = [7, 15, 2, 19, 20];
    
    println!("Searching in array: {:?}", sorted_array);
    for value in search_values {
        match binary_search(&sorted_array, value, 0, sorted_array.len() - 1) {
            Some(index) => println!("binary_search({}) = found at index {}", value, index),
            None => println!("binary_search({}) = not found", value),
        }
    }
    println!();

    // String reversal recursion
    println!("String reversal recursion:");
    let strings = ["hello", "world", "rust", "recursion", "a"];
    for s in strings {
        let result = reverse_string(s);
        println!("reverse_string('{}') = '{}'", s, result);
    }
    println!();

    // Palindrome check recursion
    println!("Palindrome check recursion:");
    let test_strings = ["racecar", "hello", "madam", "rust", "level"];
    for s in test_strings {
        let result = is_palindrome(s);
        println!("is_palindrome('{}') = {}", s, result);
    }
    println!();

    // Tree traversal simulation
    println!("Tree traversal simulation:");
    print_tree_levels(1, 4);
    println!();

    // Hanoi towers recursion
    println!("Hanoi towers recursion (3 disks):");
    hanoi(3, 'A', 'C', 'B');
    println!();

    // Nested recursion example
    println!("Nested recursion (Ackermann function):");
    let ackermann_cases = [(0, 0), (0, 1), (1, 0), (1, 1), (2, 2), (3, 1)];
    for (m, n) in ackermann_cases {
        let result = ackermann(m, n);
        println!("ackermann({}, {}) = {}", m, n, result);
    }
    println!();

    // Mutual recursion example
    println!("Mutual recursion (even/odd check):");
    for i in 0..=6 {
        println!("is_even_mutual({}) = {}, is_odd_mutual({}) = {}", 
                 i, is_even_mutual(i), i, is_odd_mutual(i));
    }
    println!();

    println!("=== End of Recursive Functions Demo ===");
}

// Simple factorial recursion
fn factorial(n: u64) -> u64 {
    if n <= 1 {
        1
    } else {
        n * factorial(n - 1)
    }
}

// Fibonacci sequence recursion
fn fibonacci(n: u32) -> u64 {
    match n {
        0 => 0,
        1 => 1,
        _ => fibonacci(n - 1) + fibonacci(n - 2),
    }
}

// Power function recursion
fn power(base: i32, exp: u32) -> i32 {
    if exp == 0 {
        1
    } else {
        base * power(base, exp - 1)
    }
}

// Sum of digits recursion
fn sum_of_digits(n: u32) -> u32 {
    if n < 10 {
        n
    } else {
        (n % 10) + sum_of_digits(n / 10)
    }
}

// Countdown recursion
fn countdown(n: i32) {
    if n > 0 {
        println!("  Countdown: {}", n);
        countdown(n - 1);
    } else {
        println!("  Countdown finished!");
    }
}

// Count up recursion
fn count_up(current: i32, target: i32) {
    if current <= target {
        println!("  Count up: {}", current);
        count_up(current + 1, target);
    } else {
        println!("  Count up finished!");
    }
}

// Array sum recursion
fn array_sum(arr: &[i32], index: usize) -> i32 {
    if index >= arr.len() {
        0
    } else {
        arr[index] + array_sum(arr, index + 1)
    }
}

// Greatest Common Divisor recursion
fn gcd(a: u32, b: u32) -> u32 {
    if b == 0 {
        a
    } else {
        gcd(b, a % b)
    }
}

// Binary search recursion
fn binary_search(arr: &[i32], target: i32, left: usize, right: usize) -> Option<usize> {
    if left > right {
        return None;
    }
    
    let mid = left + (right - left) / 2;
    
    if arr[mid] == target {
        Some(mid)
    } else if arr[mid] > target {
        if mid == 0 {
            None
        } else {
            binary_search(arr, target, left, mid - 1)
        }
    } else {
        binary_search(arr, target, mid + 1, right)
    }
}

// String reversal recursion
fn reverse_string(s: &str) -> String {
    if s.len() <= 1 {
        s.to_string()
    } else {
        let chars: Vec<char> = s.chars().collect();
        let last_char = chars[chars.len() - 1];
        let rest: String = chars[..chars.len() - 1].iter().collect();
        format!("{}{}", last_char, reverse_string(&rest))
    }
}

// Palindrome check recursion
fn is_palindrome(s: &str) -> bool {
    let chars: Vec<char> = s.chars().collect();
    is_palindrome_helper(&chars, 0, chars.len() - 1)
}

fn is_palindrome_helper(chars: &[char], left: usize, right: usize) -> bool {
    if left >= right {
        true
    } else if chars[left] != chars[right] {
        false
    } else {
        is_palindrome_helper(chars, left + 1, right - 1)
    }
}

// Tree traversal simulation
fn print_tree_levels(level: i32, max_level: i32) {
    if level <= max_level {
        let indent = "  ".repeat((level - 1) as usize);
        println!("{}Level {}", indent, level);
        print_tree_levels(level + 1, max_level);
    }
}

// Hanoi towers recursion
fn hanoi(n: i32, from: char, to: char, aux: char) {
    if n == 1 {
        println!("  Move disk 1 from {} to {}", from, to);
    } else {
        hanoi(n - 1, from, aux, to);
        println!("  Move disk {} from {} to {}", n, from, to);
        hanoi(n - 1, aux, to, from);
    }
}

// Ackermann function (nested recursion)
fn ackermann(m: u32, n: u32) -> u32 {
    match (m, n) {
        (0, n) => n + 1,
        (m, 0) => ackermann(m - 1, 1),
        (m, n) => ackermann(m - 1, ackermann(m, n - 1)),
    }
}

// Mutual recursion example
fn is_even_mutual(n: u32) -> bool {
    if n == 0 {
        true
    } else {
        is_odd_mutual(n - 1)
    }
}

fn is_odd_mutual(n: u32) -> bool {
    if n == 0 {
        false
    } else {
        is_even_mutual(n - 1)
    }
}
