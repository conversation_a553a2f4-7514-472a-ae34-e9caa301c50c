// 10. Built-in Functions - math, string, system functions (Rust version)
// This file demonstrates built-in functions in Rust for comparison with Dolet

use std::time::{SystemTime, UNIX_EPOCH};

fn main() {
    println!("=== Built-in Functions Demo (Rust) ===");
    println!();

    // Mathematical built-in functions
    println!("Mathematical Functions:");

    // Absolute value
    let neg_num: i32 = -15;
    let abs_result = neg_num.abs();
    println!("abs(-15) = {}", abs_result);

    // Maximum and minimum functions
    let max_result = 10.max(7);
    let min_result = 10.min(7);
    println!("max(10, 7) = {}", max_result);
    println!("min(10, 7) = {}", min_result);
    println!();

    // Power function
    let power_result = 2_i32.pow(4);
    println!("power(2, 4) = {}", power_result);

    // Square root
    let sqrt_result = (16.0_f64).sqrt();
    println!("sqrt(16) = {}", sqrt_result);
    println!();

    // String built-in functions
    println!("String Functions:");

    // String length
    let test_str = "Hello";
    let str_len = test_str.len();
    println!("length('{}') = {}", test_str, str_len);

    // String case conversion
    let upper_result = "hello".to_uppercase();
    let lower_result = "WORLD".to_lowercase();
    println!("to_upper('hello') = '{}'", upper_result);
    println!("to_lower('WORLD') = '{}'", lower_result);
    println!();

    // Array utility functions
    println!("Array Utility Functions:");

    // Array operations
    let test_array = [1, 2, 3, 4, 5];
    let arr_len = test_array.len();
    println!("array_length([1,2,3,4,5]) = {}", arr_len);

    let array_sum: i32 = test_array.iter().sum();
    println!("array_sum([1,2,3,4,5]) = {}", array_sum);

    let array_avg = array_sum / test_array.len() as i32;
    println!("array_average([1,2,3,4,5]) = {}", array_avg);
    println!();

    // Type checking functions
    println!("Type Checking Functions:");

    let pos_check = is_positive(10);
    println!("is_positive(10) = {}", pos_check);

    let even_check = 8 % 2 == 0;
    println!("is_even(8) = {}", even_check);

    let odd_check = 7 % 2 != 0;
    println!("is_odd(7) = {}", odd_check);
    println!();

    // Random number simulation (using simple method)
    println!("Random Number Functions:");
    let rand_num = simple_random(100);
    println!("random_int(100) = {}", rand_num);

    // Date/Time functions
    println!("Date/Time Functions:");
    let current_year = 2024;
    let current_month = 7;
    let current_day = 18;
    println!("Current date: {}-{}-{}", current_year, current_month, current_day);
    
    // System time
    let now = SystemTime::now();
    let since_epoch = now.duration_since(UNIX_EPOCH).unwrap();
    println!("Unix timestamp: {}", since_epoch.as_secs());
    println!();

    // System functions
    println!("System Functions:");
    let sys_info = "Rust Runtime v1.0";
    println!("System: {}", sys_info);

    let memory_usage = 1024; // KB
    println!("Memory usage: {} KB", memory_usage);
    println!();

    // Utility functions
    println!("Utility Functions:");

    let clamped = clamp(15, 5, 10);
    println!("clamp(15, 5, 10) = {}", clamped);

    let fact_result = factorial(5);
    println!("factorial(5) = {}", fact_result);
    println!();

    // Floating point functions
    println!("Floating Point Functions:");
    let pi = std::f64::consts::PI;
    let e = std::f64::consts::E;
    println!("π = {:.5}", pi);
    println!("e = {:.5}", e);

    let sin_result = (30.0_f64.to_radians()).sin();
    let cos_result = (30.0_f64.to_radians()).cos();
    println!("sin(30°) = {:.3}", sin_result);
    println!("cos(30°) = {:.3}", cos_result);
    println!();

    // Circle calculations
    println!("Circle calculations:");
    let radius = 5.0;
    let area = pi * radius * radius;
    let circumference = 2.0 * pi * radius;
    println!("Circle with radius {}:", radius);
    println!("  Area = {:.2}", area);
    println!("  Circumference = {:.2}", circumference);
    println!();

    // Statistical functions
    println!("Statistical functions:");
    let data = [10, 20, 30, 40, 50];
    let data_mean = data.iter().sum::<i32>() / data.len() as i32;
    let data_min = *data.iter().min().unwrap();
    let data_max = *data.iter().max().unwrap();
    
    println!("Data: {:?}", data);
    println!("Mean: {}", data_mean);
    println!("Min: {}", data_min);
    println!("Max: {}", data_max);
    println!("Range: {}", data_max - data_min);
    println!();

    // String manipulation functions
    println!("String manipulation functions:");
    let text = "Hello, World!";
    println!("Original: '{}'", text);
    println!("Contains 'World': {}", text.contains("World"));
    println!("Starts with 'Hello': {}", text.starts_with("Hello"));
    println!("Ends with '!': {}", text.ends_with("!"));
    
    let words: Vec<&str> = text.split(", ").collect();
    println!("Split by ', ': {:?}", words);
    
    let replaced = text.replace("World", "Rust");
    println!("Replace 'World' with 'Rust': '{}'", replaced);
    println!();

    // Number conversion functions
    println!("Number conversion functions:");
    let int_val = 42;
    let float_val = int_val as f64;
    let string_val = int_val.to_string();
    
    println!("Integer: {}", int_val);
    println!("As float: {}", float_val);
    println!("As string: '{}'", string_val);
    
    let parsed_int: Result<i32, _> = "123".parse();
    match parsed_int {
        Ok(num) => println!("Parsed '123' as integer: {}", num),
        Err(_) => println!("Failed to parse '123'"),
    }
    println!();

    // Collection functions
    println!("Collection functions:");
    let mut vec = vec![3, 1, 4, 1, 5, 9, 2, 6];
    println!("Original vector: {:?}", vec);
    
    vec.sort();
    println!("Sorted: {:?}", vec);
    
    vec.reverse();
    println!("Reversed: {:?}", vec);
    
    let unique: std::collections::HashSet<_> = vec.iter().collect();
    println!("Unique elements: {:?}", unique);
    println!();

    println!("=== End of Built-in Functions Demo ===");
}

fn is_positive(num: i32) -> bool {
    num > 0
}

fn simple_random(max: i32) -> i32 {
    // Simple pseudo-random using system time
    let now = SystemTime::now();
    let since_epoch = now.duration_since(UNIX_EPOCH).unwrap();
    (since_epoch.as_nanos() % max as u128) as i32
}

fn clamp(value: i32, min_val: i32, max_val: i32) -> i32 {
    if value < min_val {
        min_val
    } else if value > max_val {
        max_val
    } else {
        value
    }
}

fn factorial(n: i32) -> i32 {
    if n <= 1 {
        1
    } else {
        (1..=n).product()
    }
}
