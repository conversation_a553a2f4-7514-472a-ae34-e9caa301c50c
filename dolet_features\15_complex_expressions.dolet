# 15. Complex Expressions - Advanced Expression Evaluation
# This file demonstrates complex expressions in Dolet

print "=== Complex Expressions Demo ==="
print ""

# Nested arithmetic expressions
print "Nested Arithmetic Expressions:"
int a = 5
int b = 3
int c = 7
int d = 2

int result1 = ((a + b) * c) - (d * 2)
int result2 = (a * (b + c)) / (d + 1)
int result3 = ((a - b) * (c + d)) + (a * b)

print "a=" + a + ", b=" + b + ", c=" + c + ", d=" + d
print "((a + b) * c) - (d * 2) = " + result1
print "(a * (b + c)) / (d + 1) = " + result2
print "((a - b) * (c + d)) + (a * b) = " + result3
print ""

# Complex boolean expressions
print "Complex Boolean Expressions:"
int x = 10
int y = 20
int z = 15
bool flag1 = true
bool flag2 = false

bool complex1 = (x > 5) and (y < 25) and (z >= 10)
bool complex2 = (x == 10) or (y == 30) or (z != 15)
bool complex3 = ((x > y) or (y > z)) and (flag1 == true)
bool complex4 = not ((x < 5) and (y > 30)) or flag2

print "x=" + x + ", y=" + y + ", z=" + z
print "flag1=" + flag1 + ", flag2=" + flag2
print "(x > 5) and (y < 25) and (z >= 10) = " + complex1
print "(x == 10) or (y == 30) or (z != 15) = " + complex2
print "((x > y) or (y > z)) and (flag1 == true) = " + complex3
print "not ((x < 5) and (y > 30)) or flag2 = " + complex4
print ""

# Mixed arithmetic and boolean
print "Mixed Arithmetic and Boolean:"
int score = 85
int max_score = 100
int min_passing = 60

bool is_excellent = (score >= 90) and (score <= max_score)
bool is_passing = (score >= min_passing) and (score < max_score)
bool needs_improvement = (score < min_passing) or (score > max_score)

print "score=" + score + ", max_score=" + max_score + ", min_passing=" + min_passing
print "is_excellent = " + is_excellent
print "is_passing = " + is_passing
print "needs_improvement = " + needs_improvement
print ""

# String concatenation expressions
print "String Concatenation Expressions:"
string first = "Hello"
string middle = "Beautiful"
string last = "World"
string punctuation = "!"

string message1 = first + " " + middle + " " + last + punctuation
string message2 = "Greeting: " + first + ", Adjective: " + middle + ", Noun: " + last
string message3 = (first + " ") * 3 + last  # Simulated repetition

print "first='" + first + "', middle='" + middle + "', last='" + last + "'"
print "message1 = '" + message1 + "'"
print "message2 = '" + message2 + "'"
print "message3 = '" + message3 + "'"
print ""

# Array index expressions
print "Array Index Expressions:"
array int numbers = [10, 20, 30, 40, 50]
int index1 = 2
int index2 = 1

int value1 = numbers[index1 + 1]  # numbers[3]
int value2 = numbers[(index1 * index2) + 1]  # numbers[3]
int value3 = numbers[numbers[0] / 10]  # numbers[1] if numbers[0] = 10

print "numbers = [10, 20, 30, 40, 50]"
print "index1=" + index1 + ", index2=" + index2
print "numbers[index1 + 1] = " + value1
print "numbers[(index1 * index2) + 1] = " + value2
print "numbers[numbers[0] / 10] = " + value3
print ""

# Function call expressions
print "Function Call Expressions:"

fun add(x, y) {
    return x + y
}

fun multiply(x, y) {
    return x * y
}

fun max_of_three(a, b, c) {
    if a >= b and a >= c
        return a
    elif b >= a and b >= c
        return b
    else
        return c
}

int expr1 = add(multiply(3, 4), add(2, 5))  # add(12, 7) = 19
int expr2 = multiply(add(2, 3), add(4, 1))  # multiply(5, 5) = 25
int expr3 = max_of_three(add(1, 2), multiply(2, 3), add(4, 5))  # max_of_three(3, 6, 9) = 9

print "add(multiply(3, 4), add(2, 5)) = " + expr1
print "multiply(add(2, 3), add(4, 1)) = " + expr2
print "max_of_three(add(1, 2), multiply(2, 3), add(4, 5)) = " + expr3
print ""

# Conditional expressions (ternary-like)
print "Conditional Expressions:"
int age = 20
int price = 100

string status = (age >= 18) ? "Adult" : "Minor"  # Simulated ternary
int discount = (age >= 65) ? 20 : ((age >= 18) ? 10 : 0)
float final_price = price - (price * discount / 100.0)

print "age=" + age + ", price=" + price
print "status = " + status
print "discount = " + discount + "%"
print "final_price = " + final_price
print ""

# Complex loop expressions
print "Complex Loop Expressions:"
int sum = 0
int product = 1

for i = 1 to 5
    int current_value = (i * 2) + 1  # 3, 5, 7, 9, 11
    set sum = sum + current_value
    set product = product * (current_value % 3)  # Modulo operation
    print "i=" + i + ", current_value=" + current_value + ", sum=" + sum + ", product=" + product

print "Final sum = " + sum
print "Final product = " + product
print ""

# Nested function expressions
print "Nested Function Expressions:"

fun square(n) {
    return n * n
}

fun cube(n) {
    return n * n * n
}

fun sum_of_squares(a, b) {
    return square(a) + square(b)
}

int nested1 = square(add(3, 2))  # square(5) = 25
int nested2 = add(square(3), cube(2))  # add(9, 8) = 17
int nested3 = sum_of_squares(add(1, 2), multiply(2, 2))  # sum_of_squares(3, 4) = 25

print "square(add(3, 2)) = " + nested1
print "add(square(3), cube(2)) = " + nested2
print "sum_of_squares(add(1, 2), multiply(2, 2)) = " + nested3
print ""

# Complex array operations
print "Complex Array Operations:"
array int data = [1, 4, 9, 16, 25]
int sum_of_array = 0
int max_value = data[0]

for i = 0 to 4
    set sum_of_array = sum_of_array + data[i]
    if data[i] > max_value
        set max_value = data[i]

float average = sum_of_array / 5.0
bool all_positive = (sum_of_array > 0) and (max_value > 0)

print "data = [1, 4, 9, 16, 25]"
print "sum_of_array = " + sum_of_array
print "max_value = " + max_value
print "average = " + average
print "all_positive = " + all_positive
print ""

print "=== End of Complex Expressions Demo ==="
