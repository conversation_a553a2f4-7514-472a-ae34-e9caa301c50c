# 21. File Operations - File I/O and Management
# This file demonstrates file operations in Dolet

print "=== File Operations Demo ==="
print ""

# File existence checking
print "File Existence Checking:"
string filename1 = "data.txt"
string filename2 = "config.ini"
string filename3 = "nonexistent.txt"

print "Checking if files exist:"
print "file_exists('" + filename1 + "') = true (simulated)"
print "file_exists('" + filename2 + "') = true (simulated)"
print "file_exists('" + filename3 + "') = false (simulated)"
print ""

# File reading
print "File Reading Operations:"

fun read_text_file(filename) {
    print "Reading file: '" + filename + "'"
    
    if filename == "data.txt"
        print "File content:"
        print "  Line 1: Hello World"
        print "  Line 2: This is a test file"
        print "  Line 3: End of file"
        return "Hello World\nThis is a test file\nEnd of file"
    else
        print "File not found or empty"
        return ""
}

string file_content = read_text_file("data.txt")
print "Content stored in variable"
print ""

# File writing
print "File Writing Operations:"

fun write_text_file(filename, content) {
    print "Writing to file: '" + filename + "'"
    print "Content to write:"
    print "'" + content + "'"
    print "File written successfully (simulated)"
    return true
}

string output_content = "This is new content\nWritten by Dolet program\nTimestamp: 2024-01-15"
bool write_success = write_text_file("output.txt", output_content)
print "Write operation successful: " + write_success
print ""

# File appending
print "File Appending Operations:"

fun append_to_file(filename, content) {
    print "Appending to file: '" + filename + "'"
    print "Content to append: '" + content + "'"
    print "Content appended successfully (simulated)"
    return true
}

string append_content = "\nAppended line 1\nAppended line 2"
bool append_success = append_to_file("output.txt", append_content)
print "Append operation successful: " + append_success
print ""

# File copying
print "File Copying Operations:"

fun copy_file(source, destination) {
    print "Copying file:"
    print "  Source: '" + source + "'"
    print "  Destination: '" + destination + "'"
    
    # Simulate reading source and writing to destination
    print "  Reading source file..."
    print "  Writing to destination file..."
    print "File copied successfully (simulated)"
    return true
}

bool copy_success = copy_file("data.txt", "data_backup.txt")
print "Copy operation successful: " + copy_success
print ""

# File deletion
print "File Deletion Operations:"

fun delete_file(filename) {
    print "Deleting file: '" + filename + "'"
    print "File deleted successfully (simulated)"
    return true
}

bool delete_success = delete_file("temp.txt")
print "Delete operation successful: " + delete_success
print ""

# File information
print "File Information:"

fun get_file_info(filename) {
    print "Getting information for: '" + filename + "'"
    
    if filename == "data.txt"
        print "  File size: 1024 bytes"
        print "  Created: 2024-01-10 10:30:00"
        print "  Modified: 2024-01-15 14:20:00"
        print "  Type: Text file"
        print "  Permissions: Read/Write"
    else
        print "  File information not available"
}

get_file_info("data.txt")
get_file_info("unknown.txt")
print ""

# Directory operations
print "Directory Operations:"

fun create_directory(dirname) {
    print "Creating directory: '" + dirname + "'"
    print "Directory created successfully (simulated)"
    return true
}

fun list_directory(dirname) {
    print "Listing contents of directory: '" + dirname + "'"
    print "  Files found:"
    print "    - file1.txt"
    print "    - file2.txt"
    print "    - subdirectory/"
    print "    - config.ini"
}

bool dir_created = create_directory("new_folder")
print "Directory creation successful: " + dir_created
list_directory(".")
print ""

# CSV file operations
print "CSV File Operations:"

fun write_csv_file(filename, data) {
    print "Writing CSV file: '" + filename + "'"
    print "CSV data:"
    print "  Name,Age,City,Job"
    print "  Ahmed,25,Cairo,Engineer"
    print "  Sara,30,Alexandria,Doctor"
    print "  Ali,28,Giza,Teacher"
    print "CSV file written successfully (simulated)"
}

fun read_csv_file(filename) {
    print "Reading CSV file: '" + filename + "'"
    print "Parsed data:"
    print "  Record 1: Name=Ahmed, Age=25, City=Cairo, Job=Engineer"
    print "  Record 2: Name=Sara, Age=30, City=Alexandria, Job=Doctor"
    print "  Record 3: Name=Ali, Age=28, City=Giza, Job=Teacher"
}

write_csv_file("employees.csv", "csv_data")
read_csv_file("employees.csv")
print ""

# Configuration file operations
print "Configuration File Operations:"

fun write_config_file(filename) {
    print "Writing configuration file: '" + filename + "'"
    print "Configuration settings:"
    print "  [Database]"
    print "  host=localhost"
    print "  port=5432"
    print "  username=admin"
    print "  [Application]"
    print "  debug=true"
    print "  max_connections=100"
    print "Config file written successfully (simulated)"
}

fun read_config_file(filename) {
    print "Reading configuration file: '" + filename + "'"
    print "Loaded settings:"
    print "  Database host: localhost"
    print "  Database port: 5432"
    print "  Debug mode: enabled"
    print "  Max connections: 100"
}

write_config_file("app.config")
read_config_file("app.config")
print ""

# Log file operations
print "Log File Operations:"

fun write_log_entry(filename, level, message) {
    print "Writing log entry to: '" + filename + "'"
    print "Log level: " + level
    print "Message: '" + message + "'"
    print "Timestamp: 2024-01-15 14:30:00 (simulated)"
    print "Log entry written successfully"
}

fun read_log_file(filename) {
    print "Reading log file: '" + filename + "'"
    print "Recent log entries:"
    print "  2024-01-15 14:25:00 [INFO] Application started"
    print "  2024-01-15 14:26:00 [DEBUG] Database connection established"
    print "  2024-01-15 14:27:00 [WARNING] High memory usage detected"
    print "  2024-01-15 14:28:00 [ERROR] Failed to process request"
}

write_log_entry("app.log", "INFO", "User login successful")
write_log_entry("app.log", "ERROR", "Database connection failed")
read_log_file("app.log")
print ""

# Binary file operations simulation
print "Binary File Operations:"

fun write_binary_file(filename, data) {
    print "Writing binary file: '" + filename + "'"
    print "Binary data size: 2048 bytes (simulated)"
    print "Binary file written successfully"
}

fun read_binary_file(filename) {
    print "Reading binary file: '" + filename + "'"
    print "Binary data loaded: 2048 bytes"
    print "File type: Image/PNG (simulated)"
}

write_binary_file("image.png", "binary_data")
read_binary_file("image.png")
print ""

# File search operations
print "File Search Operations:"

fun search_files_by_extension(directory, extension) {
    print "Searching for '" + extension + "' files in: '" + directory + "'"
    print "Files found:"
    print "  - document1.txt"
    print "  - document2.txt"
    print "  - readme.txt"
    print "Total: 3 files found"
}

fun search_files_by_name(directory, pattern) {
    print "Searching for files matching '" + pattern + "' in: '" + directory + "'"
    print "Files found:"
    print "  - config.ini"
    print "  - config_backup.ini"
    print "Total: 2 files found"
}

search_files_by_extension(".", "txt")
search_files_by_name(".", "config")
print ""

# File backup operations
print "File Backup Operations:"

fun backup_file(filename) {
    string backup_name = filename + ".backup"
    print "Creating backup of: '" + filename + "'"
    print "Backup name: '" + backup_name + "'"
    print "Backup created successfully (simulated)"
    return backup_name
}

fun restore_from_backup(backup_filename, original_filename) {
    print "Restoring from backup:"
    print "  Backup file: '" + backup_filename + "'"
    print "  Original file: '" + original_filename + "'"
    print "File restored successfully (simulated)"
}

string backup_file_name = backup_file("important_data.txt")
restore_from_backup(backup_file_name, "important_data.txt")
print ""

print "=== End of File Operations Demo ==="
