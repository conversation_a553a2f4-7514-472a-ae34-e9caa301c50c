; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 126 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [5 x i8] c"Ali\0A\00", align 1
@.str_2 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [18 x i8] c"Test values: a = \00", align 1
@.str_expr_1 = private unnamed_addr constant [7 x i8] c", b = \00", align 1
@.str_expr_2 = private unnamed_addr constant [7 x i8] c", c = \00", align 1
@.str_expr_3 = private unnamed_addr constant [5 x i8] c"\E2\9C\93 \00", align 1
@.str_expr_4 = private unnamed_addr constant [4 x i8] c" > \00", align 1
@.str_expr_5 = private unnamed_addr constant [9 x i8] c" is TRUE\00", align 1
@.str_expr_6 = private unnamed_addr constant [5 x i8] c"\E2\9C\97 \00", align 1
@.str_expr_7 = private unnamed_addr constant [10 x i8] c" is FALSE\00", align 1
@.str_expr_8 = private unnamed_addr constant [5 x i8] c" >= \00", align 1
@.str_expr_9 = private unnamed_addr constant [4 x i8] c" < \00", align 1
@.str_expr_10 = private unnamed_addr constant [5 x i8] c" <= \00", align 1
@.str_expr_11 = private unnamed_addr constant [5 x i8] c" == \00", align 1
@.str_expr_12 = private unnamed_addr constant [5 x i8] c" != \00", align 1
@.str_expr_13 = private unnamed_addr constant [19 x i8] c"Strings: name1 = '\00", align 1
@.str_expr_14 = private unnamed_addr constant [13 x i8] c"', name2 = '\00", align 1
@.str_expr_15 = private unnamed_addr constant [13 x i8] c"', name3 = '\00", align 1
@.str_expr_16 = private unnamed_addr constant [2 x i8] c"'\00", align 1
@.str_expr_17 = private unnamed_addr constant [19 x i8] c"Booleans: flag1 = \00", align 1
@.str_expr_18 = private unnamed_addr constant [11 x i8] c", flag2 = \00", align 1
@.str_expr_19 = private unnamed_addr constant [11 x i8] c", flag3 = \00", align 1
@.str_expr_20 = private unnamed_addr constant [13 x i8] c"Floats: x = \00", align 1
@.str_expr_21 = private unnamed_addr constant [7 x i8] c", y = \00", align 1
@.str_expr_22 = private unnamed_addr constant [7 x i8] c", z = \00", align 1
@.str_expr_23 = private unnamed_addr constant [30 x i8] c"\E2\9C\93 (a + b) > (c * 2) is TRUE\00", align 1
@.str_expr_24 = private unnamed_addr constant [31 x i8] c"\E2\9C\97 (a + b) > (c * 2) is FALSE\00", align 1
@.str_expr_25 = private unnamed_addr constant [31 x i8] c"\E2\9C\93 (a * 2) == (c + a) is TRUE\00", align 1
@.str_expr_26 = private unnamed_addr constant [32 x i8] c"\E2\9C\97 (a * 2) == (c + a) is FALSE\00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4
@global_b = global i32 0, align 4
@global_c = global i32 0, align 4
@global_name1 = global i8* null, align 8
@global_name2 = global i8* null, align 8
@global_name3 = global i8* null, align 8
@global_flag1 = global i1 0, align 1
@global_flag2 = global i1 0, align 1
@global_flag3 = global i1 0, align 1
@global_x = global float 0.0, align 4
@global_y = global float 0.0, align 4
@global_z = global float 0.0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== Comparison Operators Demo ===" (inline)
  %tmp_1_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== Comparison Operators Demo ===\00", [34 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [34 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_3_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [1 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int a = 10
  store i32 10, i32* @global_a, align 4
  ; int b = 5
  store i32 5, i32* @global_b, align 4
  ; int c = 10
  store i32 10, i32* @global_c, align 4
  ; print expression: "Test values: a = " + a + ", b = " + b + ", c = " ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_5 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_5)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_6 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_7 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_8_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_8_str, align 1
  %tmp_9 = bitcast [1 x i8]* %tmp_8_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Greater Than (>) Tests:" (inline)
  %tmp_10_str = alloca [24 x i8], align 1
  store [24 x i8] c"Greater Than (>) Tests:\00", [24 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [24 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a > b
  %tmp_12 = load i32, i32* @global_a, align 4
  %tmp_13 = load i32, i32* @global_b, align 4
  %tmp_14 = icmp sgt i32 %tmp_12, %tmp_13
  br i1 %tmp_14, label %if_true_1, label %if_else_3
if_true_1:
  ; print expression: "✓ " + a + " > " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_15 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_16 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_else_3:
  ; print expression: "✗ " + a + " > " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_17 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_18 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_end_2:
  ; print expression: "✗ " + a + " > " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_19 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_20 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if b > a
  %tmp_21 = load i32, i32* @global_b, align 4
  %tmp_22 = load i32, i32* @global_a, align 4
  %tmp_23 = icmp sgt i32 %tmp_21, %tmp_22
  br i1 %tmp_23, label %if_true_4, label %if_else_6
if_true_4:
  ; print expression: "✓ " + b + " > " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_24 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_25 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_25)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_else_6:
  ; print expression: "✗ " + b + " > " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_26 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_27 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_27)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_end_5:
  ; print expression: "✗ " + b + " > " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_28 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_28)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_29 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_29)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_30_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_30_str, align 1
  %tmp_31 = bitcast [1 x i8]* %tmp_30_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_31)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Greater Than or Equal (>=) Tests:" (inline)
  %tmp_32_str = alloca [34 x i8], align 1
  store [34 x i8] c"Greater Than or Equal (>=) Tests:\00", [34 x i8]* %tmp_32_str, align 1
  %tmp_33 = bitcast [34 x i8]* %tmp_32_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_33)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a >= c
  %tmp_34 = load i32, i32* @global_a, align 4
  %tmp_35 = load i32, i32* @global_c, align 4
  %tmp_36 = icmp sge i32 %tmp_34, %tmp_35
  br i1 %tmp_36, label %if_true_7, label %if_else_9
if_true_7:
  ; print expression: "✓ " + a + " >= " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_37 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_37)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_38 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_38)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_else_9:
  ; print expression: "✗ " + a + " >= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_39 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_39)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_40 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_end_8:
  ; print expression: "✗ " + a + " >= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_41 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_41)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_42 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a >= b
  %tmp_43 = load i32, i32* @global_a, align 4
  %tmp_44 = load i32, i32* @global_b, align 4
  %tmp_45 = icmp sge i32 %tmp_43, %tmp_44
  br i1 %tmp_45, label %if_true_10, label %if_else_12
if_true_10:
  ; print expression: "✓ " + a + " >= " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_46 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_46)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_47 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_47)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_else_12:
  ; print expression: "✗ " + a + " >= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_48 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_48)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_49 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_49)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_end_11:
  ; print expression: "✗ " + a + " >= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_50 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_50)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_51 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_51)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if b >= a
  %tmp_52 = load i32, i32* @global_b, align 4
  %tmp_53 = load i32, i32* @global_a, align 4
  %tmp_54 = icmp sge i32 %tmp_52, %tmp_53
  br i1 %tmp_54, label %if_true_13, label %if_else_15
if_true_13:
  ; print expression: "✓ " + b + " >= " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_55 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_55)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_56 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_56)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_else_15:
  ; print expression: "✗ " + b + " >= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_57 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_57)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_58 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_58)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_end_14:
  ; print expression: "✗ " + b + " >= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_59 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_59)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_60 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_60)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_61_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_61_str, align 1
  %tmp_62 = bitcast [1 x i8]* %tmp_61_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_62)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Less Than (<) Tests:" (inline)
  %tmp_63_str = alloca [21 x i8], align 1
  store [21 x i8] c"Less Than (<) Tests:\00", [21 x i8]* %tmp_63_str, align 1
  %tmp_64 = bitcast [21 x i8]* %tmp_63_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_64)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if b < a
  %tmp_65 = load i32, i32* @global_b, align 4
  %tmp_66 = load i32, i32* @global_a, align 4
  %tmp_67 = icmp slt i32 %tmp_65, %tmp_66
  br i1 %tmp_67, label %if_true_16, label %if_else_18
if_true_16:
  ; print expression: "✓ " + b + " < " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_68 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_68)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_69 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_69)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_else_18:
  ; print expression: "✗ " + b + " < " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_70 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_70)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_71 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_71)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_end_17:
  ; print expression: "✗ " + b + " < " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_72 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_72)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_73 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_73)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a < b
  %tmp_74 = load i32, i32* @global_a, align 4
  %tmp_75 = load i32, i32* @global_b, align 4
  %tmp_76 = icmp slt i32 %tmp_74, %tmp_75
  br i1 %tmp_76, label %if_true_19, label %if_else_21
if_true_19:
  ; print expression: "✓ " + a + " < " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_77 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_77)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_78 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_78)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_else_21:
  ; print expression: "✗ " + a + " < " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_79 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_79)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_80 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_80)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_end_20:
  ; print expression: "✗ " + a + " < " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_81 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_81)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_82 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_82)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_83_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_83_str, align 1
  %tmp_84 = bitcast [1 x i8]* %tmp_83_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Less Than or Equal (<=) Tests:" (inline)
  %tmp_85_str = alloca [31 x i8], align 1
  store [31 x i8] c"Less Than or Equal (<=) Tests:\00", [31 x i8]* %tmp_85_str, align 1
  %tmp_86 = bitcast [31 x i8]* %tmp_85_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a <= c
  %tmp_87 = load i32, i32* @global_a, align 4
  %tmp_88 = load i32, i32* @global_c, align 4
  %tmp_89 = icmp sle i32 %tmp_87, %tmp_88
  br i1 %tmp_89, label %if_true_22, label %if_else_24
if_true_22:
  ; print expression: "✓ " + a + " <= " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_90 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_91 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_91)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_else_24:
  ; print expression: "✗ " + a + " <= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_92 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_92)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_93 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_end_23:
  ; print expression: "✗ " + a + " <= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_94 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_95 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_95)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if b <= a
  %tmp_96 = load i32, i32* @global_b, align 4
  %tmp_97 = load i32, i32* @global_a, align 4
  %tmp_98 = icmp sle i32 %tmp_96, %tmp_97
  br i1 %tmp_98, label %if_true_25, label %if_else_27
if_true_25:
  ; print expression: "✓ " + b + " <= " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_99 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_99)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_100 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_100)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_else_27:
  ; print expression: "✗ " + b + " <= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_101 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_102 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_102)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_end_26:
  ; print expression: "✗ " + b + " <= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_103 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_103)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_104 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_104)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a <= b
  %tmp_105 = load i32, i32* @global_a, align 4
  %tmp_106 = load i32, i32* @global_b, align 4
  %tmp_107 = icmp sle i32 %tmp_105, %tmp_106
  br i1 %tmp_107, label %if_true_28, label %if_else_30
if_true_28:
  ; print expression: "✓ " + a + " <= " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_108 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_108)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_109 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_109)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_else_30:
  ; print expression: "✗ " + a + " <= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_110 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_110)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_111 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_111)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_end_29:
  ; print expression: "✗ " + a + " <= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_112 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_112)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_113 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_113)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_114_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_114_str, align 1
  %tmp_115 = bitcast [1 x i8]* %tmp_114_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_115)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Equal To (==) Tests:" (inline)
  %tmp_116_str = alloca [21 x i8], align 1
  store [21 x i8] c"Equal To (==) Tests:\00", [21 x i8]* %tmp_116_str, align 1
  %tmp_117 = bitcast [21 x i8]* %tmp_116_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_117)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a == c
  %tmp_118 = load i32, i32* @global_a, align 4
  %tmp_119 = load i32, i32* @global_c, align 4
  %tmp_120 = icmp eq i32 %tmp_118, %tmp_119
  br i1 %tmp_120, label %if_true_31, label %if_else_33
if_true_31:
  ; print expression: "✓ " + a + " == " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_121 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_121)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_122 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_122)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_else_33:
  ; print expression: "✗ " + a + " == " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_123 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_123)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_124 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_124)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_end_32:
  ; print expression: "✗ " + a + " == " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_125 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_125)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_126 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_126)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a == b
  %tmp_127 = load i32, i32* @global_a, align 4
  %tmp_128 = load i32, i32* @global_b, align 4
  %tmp_129 = icmp eq i32 %tmp_127, %tmp_128
  br i1 %tmp_129, label %if_true_34, label %if_else_36
if_true_34:
  ; print expression: "✓ " + a + " == " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_130 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_130)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_131 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_else_36:
  ; print expression: "✗ " + a + " == " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_132 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_132)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_133 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_133)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_end_35:
  ; print expression: "✗ " + a + " == " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_134 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_134)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_135 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_135)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_136_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_136_str, align 1
  %tmp_137 = bitcast [1 x i8]* %tmp_136_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_137)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Not Equal To (!=) Tests:" (inline)
  %tmp_138_str = alloca [25 x i8], align 1
  store [25 x i8] c"Not Equal To (!=) Tests:\00", [25 x i8]* %tmp_138_str, align 1
  %tmp_139 = bitcast [25 x i8]* %tmp_138_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_139)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a != b
  %tmp_140 = load i32, i32* @global_a, align 4
  %tmp_141 = load i32, i32* @global_b, align 4
  %tmp_142 = icmp ne i32 %tmp_140, %tmp_141
  br i1 %tmp_142, label %if_true_37, label %if_else_39
if_true_37:
  ; print expression: "✓ " + a + " != " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_143 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_143)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_144 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_144)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_else_39:
  ; print expression: "✗ " + a + " != " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_145 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_145)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_146 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_end_38:
  ; print expression: "✗ " + a + " != " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_147 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_147)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_148 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_148)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a != c
  %tmp_149 = load i32, i32* @global_a, align 4
  %tmp_150 = load i32, i32* @global_c, align 4
  %tmp_151 = icmp ne i32 %tmp_149, %tmp_150
  br i1 %tmp_151, label %if_true_40, label %if_else_42
if_true_40:
  ; print expression: "✓ " + a + " != " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_152 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_152)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_153 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_153)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_else_42:
  ; print expression: "✗ " + a + " != " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_154 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_154)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_155 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_155)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_end_41:
  ; print expression: "✗ " + a + " != " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_156 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_156)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_157 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_157)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_158_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_158_str, align 1
  %tmp_159 = bitcast [1 x i8]* %tmp_158_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_159)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "String Comparison Tests:" (inline)
  %tmp_160_str = alloca [25 x i8], align 1
  store [25 x i8] c"String Comparison Tests:\00", [25 x i8]* %tmp_160_str, align 1
  %tmp_161 = bitcast [25 x i8]* %tmp_160_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_161)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string name1 = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_name1, align 8
  ; string name2 = "Ali"
  store i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_1, i64 0, i64 0), i8** @global_name2, align 8
  ; string name3 = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_name3, align 8
  ; print expression: "Strings: name1 = '" + name1 + "', name2 = '" + na...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_162 = load i8*, i8** @global_name1, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_162)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_163 = load i8*, i8** @global_name2, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_163)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_164 = load i8*, i8** @global_name3, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_164)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_16, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if name1 == name3
  %tmp_165 = icmp eq i32 0, 0
  br i1 %tmp_165, label %if_true_43, label %if_else_45
if_true_43:
  ; print "✓ name1 == name3 is TRUE" (inline)
  %tmp_166_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 name1 == name3 is TRUE\00", [27 x i8]* %tmp_166_str, align 1
  %tmp_167 = bitcast [27 x i8]* %tmp_166_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_167)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_else_45:
  ; print "✗ name1 == name3 is FALSE" (inline)
  %tmp_168_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 == name3 is FALSE\00", [28 x i8]* %tmp_168_str, align 1
  %tmp_169 = bitcast [28 x i8]* %tmp_168_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_169)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_end_44:
  ; print "✗ name1 == name3 is FALSE" (inline)
  %tmp_170_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 == name3 is FALSE\00", [28 x i8]* %tmp_170_str, align 1
  %tmp_171 = bitcast [28 x i8]* %tmp_170_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_171)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if name1 != name2
  %tmp_172 = icmp ne i32 0, 0
  br i1 %tmp_172, label %if_true_46, label %if_else_48
if_true_46:
  ; print "✓ name1 != name2 is TRUE" (inline)
  %tmp_173_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 name1 != name2 is TRUE\00", [27 x i8]* %tmp_173_str, align 1
  %tmp_174 = bitcast [27 x i8]* %tmp_173_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_174)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_else_48:
  ; print "✗ name1 != name2 is FALSE" (inline)
  %tmp_175_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 != name2 is FALSE\00", [28 x i8]* %tmp_175_str, align 1
  %tmp_176 = bitcast [28 x i8]* %tmp_175_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_176)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_end_47:
  ; print "✗ name1 != name2 is FALSE" (inline)
  %tmp_177_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 != name2 is FALSE\00", [28 x i8]* %tmp_177_str, align 1
  %tmp_178 = bitcast [28 x i8]* %tmp_177_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_178)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_179_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_179_str, align 1
  %tmp_180 = bitcast [1 x i8]* %tmp_179_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_180)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Boolean Comparison Tests:" (inline)
  %tmp_181_str = alloca [26 x i8], align 1
  store [26 x i8] c"Boolean Comparison Tests:\00", [26 x i8]* %tmp_181_str, align 1
  %tmp_182 = bitcast [26 x i8]* %tmp_181_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_182)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; bool flag1 = true
  store i1 1, i1* @global_flag1, align 1
  ; bool flag2 = false
  store i1 0, i1* @global_flag2, align 1
  ; bool flag3 = true
  store i1 1, i1* @global_flag3, align 1
  ; print expression: "Booleans: flag1 = " + flag1 + ", flag2 = " + flag...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_183 = load i1, i1* @global_flag1, align 1
  br i1 %tmp_183, label %tmp_184, label %tmp_185
tmp_184:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_186
tmp_185:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_186
tmp_186:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_187 = load i1, i1* @global_flag2, align 1
  br i1 %tmp_187, label %tmp_188, label %tmp_189
tmp_188:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_190
tmp_189:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_190
tmp_190:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_191 = load i1, i1* @global_flag3, align 1
  br i1 %tmp_191, label %tmp_192, label %tmp_193
tmp_192:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_194
tmp_193:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_194
tmp_194:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if flag1 == flag3
  %tmp_195 = load i1, i1* @global_flag1, align 1
  %tmp_196 = zext i1 %tmp_195 to i32
  %tmp_197 = load i1, i1* @global_flag3, align 1
  %tmp_198 = zext i1 %tmp_197 to i32
  %tmp_199 = icmp eq i32 %tmp_196, %tmp_198
  br i1 %tmp_199, label %if_true_49, label %if_else_51
if_true_49:
  ; print "✓ flag1 == flag3 is TRUE" (inline)
  %tmp_200_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 flag1 == flag3 is TRUE\00", [27 x i8]* %tmp_200_str, align 1
  %tmp_201 = bitcast [27 x i8]* %tmp_200_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_201)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_else_51:
  ; print "✗ flag1 == flag3 is FALSE" (inline)
  %tmp_202_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 == flag3 is FALSE\00", [28 x i8]* %tmp_202_str, align 1
  %tmp_203 = bitcast [28 x i8]* %tmp_202_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_203)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_end_50:
  ; print "✗ flag1 == flag3 is FALSE" (inline)
  %tmp_204_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 == flag3 is FALSE\00", [28 x i8]* %tmp_204_str, align 1
  %tmp_205 = bitcast [28 x i8]* %tmp_204_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_205)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if flag1 != flag2
  %tmp_206 = load i1, i1* @global_flag1, align 1
  %tmp_207 = zext i1 %tmp_206 to i32
  %tmp_208 = load i1, i1* @global_flag2, align 1
  %tmp_209 = zext i1 %tmp_208 to i32
  %tmp_210 = icmp ne i32 %tmp_207, %tmp_209
  br i1 %tmp_210, label %if_true_52, label %if_else_54
if_true_52:
  ; print "✓ flag1 != flag2 is TRUE" (inline)
  %tmp_211_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 flag1 != flag2 is TRUE\00", [27 x i8]* %tmp_211_str, align 1
  %tmp_212 = bitcast [27 x i8]* %tmp_211_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_212)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_else_54:
  ; print "✗ flag1 != flag2 is FALSE" (inline)
  %tmp_213_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 != flag2 is FALSE\00", [28 x i8]* %tmp_213_str, align 1
  %tmp_214 = bitcast [28 x i8]* %tmp_213_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_214)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_end_53:
  ; print "✗ flag1 != flag2 is FALSE" (inline)
  %tmp_215_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 != flag2 is FALSE\00", [28 x i8]* %tmp_215_str, align 1
  %tmp_216 = bitcast [28 x i8]* %tmp_215_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_216)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_217_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_217_str, align 1
  %tmp_218 = bitcast [1 x i8]* %tmp_217_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_218)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Float Comparison Tests:" (inline)
  %tmp_219_str = alloca [24 x i8], align 1
  store [24 x i8] c"Float Comparison Tests:\00", [24 x i8]* %tmp_219_str, align 1
  %tmp_220 = bitcast [24 x i8]* %tmp_219_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_220)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float x = 5.5
  %tmp_221 = bitcast i32 1085276160 to float
  store float %tmp_221, float* @global_x, align 4
  ; float y = 3.2
  %tmp_222 = bitcast i32 1078774989 to float
  store float %tmp_222, float* @global_y, align 4
  ; float z = 5.5
  %tmp_223 = bitcast i32 1085276160 to float
  store float %tmp_223, float* @global_z, align 4
  ; print expression: "Floats: x = " + x + ", y = " + y + ", z = " + z
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_224 = load float, float* @global_x, align 4
  %tmp_225 = fpext float %tmp_224 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_225)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_226 = load float, float* @global_y, align 4
  %tmp_227 = fpext float %tmp_226 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_227)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_228 = load float, float* @global_z, align 4
  %tmp_229 = fpext float %tmp_228 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_229)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if x > y
  %tmp_230 = icmp sgt i32 0, 0
  br i1 %tmp_230, label %if_true_55, label %if_end_56
if_true_55:
  ; print "✓ x > y is TRUE" (inline)
  %tmp_231_str = alloca [18 x i8], align 1
  store [18 x i8] c"\E2\9C\93 x > y is TRUE\00", [18 x i8]* %tmp_231_str, align 1
  %tmp_232 = bitcast [18 x i8]* %tmp_231_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_232)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_end_56:
  ; if x == z
  %tmp_233 = icmp eq i32 0, 0
  br i1 %tmp_233, label %if_true_57, label %if_end_58
if_true_57:
  ; print "✓ x == z is TRUE" (inline)
  %tmp_234_str = alloca [19 x i8], align 1
  store [19 x i8] c"\E2\9C\93 x == z is TRUE\00", [19 x i8]* %tmp_234_str, align 1
  %tmp_235 = bitcast [19 x i8]* %tmp_234_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_235)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_58
if_end_58:
  ; if y < x
  %tmp_236 = icmp slt i32 0, 0
  br i1 %tmp_236, label %if_true_59, label %if_end_60
if_true_59:
  ; print "✓ y < x is TRUE" (inline)
  %tmp_237_str = alloca [18 x i8], align 1
  store [18 x i8] c"\E2\9C\93 y < x is TRUE\00", [18 x i8]* %tmp_237_str, align 1
  %tmp_238 = bitcast [18 x i8]* %tmp_237_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_238)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_60
if_end_60:
  ; print "" (inline)
  %tmp_239_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_239_str, align 1
  %tmp_240 = bitcast [1 x i8]* %tmp_239_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_240)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Complex Expression Comparisons:" (inline)
  %tmp_241_str = alloca [32 x i8], align 1
  store [32 x i8] c"Complex Expression Comparisons:\00", [32 x i8]* %tmp_241_str, align 1
  %tmp_242 = bitcast [32 x i8]* %tmp_241_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_242)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (a + b) > (c * 2)
  %tmp_243 = icmp sgt i32 0, 0
  br i1 %tmp_243, label %if_true_61, label %if_else_63
if_true_61:
  ; print expression: "✓ (a + b) > (c * 2) is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_23, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_else_63:
  ; print expression: "✗ (a + b) > (c * 2) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_24, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_end_62:
  ; print expression: "✗ (a + b) > (c * 2) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_24, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (a * 2) == (c + a)
  %tmp_244 = icmp eq i32 0, 0
  br i1 %tmp_244, label %if_true_64, label %if_else_66
if_true_64:
  ; print expression: "✓ (a * 2) == (c + a) is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_25, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_else_66:
  ; print expression: "✗ (a * 2) == (c + a) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([30 x i8], [30 x i8]* @.str_expr_26, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_end_65:
  ; print expression: "✗ (a * 2) == (c + a) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([30 x i8], [30 x i8]* @.str_expr_26, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_245_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_245_str, align 1
  %tmp_246 = bitcast [1 x i8]* %tmp_245_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_246)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== End of Comparison Operators Demo ===" (inline)
  %tmp_247_str = alloca [41 x i8], align 1
  store [41 x i8] c"=== End of Comparison Operators Demo ===\00", [41 x i8]* %tmp_247_str, align 1
  %tmp_248 = bitcast [41 x i8]* %tmp_247_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_248)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
