; ModuleID = '06_functions.63d0ca3ffac27459-cgu.0'
source_filename = "06_functions.63d0ca3ffac27459-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h9ba782ab727c9434E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd549237333deab59E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd549237333deab59E" }>, align 8
@anon.3ecf432813bebb659822bf8c7145f793.0 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@anon.3ecf432813bebb659822bf8c7145f793.1 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_7b9375d24056248d1eed183cf148827e = private unnamed_addr constant [30 x i8] c"=== Functions Demo (Rust) ===\0A", align 1
@alloc_412b738e3d68629d280067e90c1bf715 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7b9375d24056248d1eed183cf148827e, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_eafbb853fce4bb28be3b51e62ba9989d = private unnamed_addr constant [25 x i8] c"Calling simple function:\0A", align 1
@alloc_aa0d2daae5fffa2e8c5bdcc5300d9315 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_eafbb853fce4bb28be3b51e62ba9989d, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_2106170b992eab23140f256e1b412296 = private unnamed_addr constant [32 x i8] c"Function with single parameter:\0A", align 1
@alloc_63cf7cafac715399a827add19fbe35cd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2106170b992eab23140f256e1b412296, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_b468ef00fa5916b1335583d25c8252ad = private unnamed_addr constant [5 x i8] c"Ahmed", align 1
@alloc_8a3ff4f0306cccf00c85887c84f94191 = private unnamed_addr constant [4 x i8] c"Sara", align 1
@alloc_973315942ac1db05f9bdcb206b861f83 = private unnamed_addr constant [35 x i8] c"Function with multiple parameters:\0A", align 1
@alloc_3a9385f08799a7ca71dc66a21de865ca = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_973315942ac1db05f9bdcb206b861f83, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_a118b2b99663e794e1f4a92820d83446 = private unnamed_addr constant [3 x i8] c"Ali", align 1
@alloc_67eb4974a8310b72f6f5dca9d9c7655a = private unnamed_addr constant [5 x i8] c"Cairo", align 1
@alloc_addbe31108129e35061bea48eb70422e = private unnamed_addr constant [6 x i8] c"Fatima", align 1
@alloc_6641fa9b931d9297b00a346a0030367b = private unnamed_addr constant [10 x i8] c"Alexandria", align 1
@alloc_1a1bb239cc83e87cdad7c534028e6356 = private unnamed_addr constant [28 x i8] c"Function with return value:\0A", align 1
@alloc_9389381634d84f4e21238264e60bfd68 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1a1bb239cc83e87cdad7c534028e6356, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_a384d4fee6486862ef26632512459f70 = private unnamed_addr constant [8 x i8] c"5 + 3 = ", align 1
@alloc_75dbb2a3aa7c11da7588e47b7a8643a5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a384d4fee6486862ef26632512459f70, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_cfbbe488c8e30874a3b3c7ffecbd3c11 = private unnamed_addr constant [9 x i8] c"10 + 7 = ", align 1
@alloc_1f99729cc2e0ddb19b0ac1eeb429b722 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_cfbbe488c8e30874a3b3c7ffecbd3c11, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_03b8cbff9bbd575a4a2e8583734733af = private unnamed_addr constant [27 x i8] c"Function calculating area:\0A", align 1
@alloc_675ef80cf6e969d19d9fc86e2b98a76a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_03b8cbff9bbd575a4a2e8583734733af, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_4abd4e317d90e51512d3564e16defb2b = private unnamed_addr constant [11 x i8] c"Room area: ", align 1
@alloc_dd74a8c530f7c74691d7235b3574559d = private unnamed_addr constant [15 x i8] c" square meters\0A", align 1
@alloc_d700338e9e98b8bc294fddc9f7393bda = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4abd4e317d90e51512d3564e16defb2b, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_dd74a8c530f7c74691d7235b3574559d, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_4006dfd554802d8c7b537a8378006cfb = private unnamed_addr constant [33 x i8] c"Function with string operations:\0A", align 1
@alloc_30e1a9a0136fde75a2ba0d979ac7f362 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4006dfd554802d8c7b537a8378006cfb, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_a10b29fa1aded051447dbb8c90cd1047 = private unnamed_addr constant [6 x i8] c"Hassan", align 1
@alloc_0fb98253c046950db1b55b037cb0697e = private unnamed_addr constant [13 x i8] c"Full name 1: ", align 1
@alloc_a2110adb6bccdfbd261c6c42c8b11247 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0fb98253c046950db1b55b037cb0697e, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ffd61fdf645e00d9651e0dd946c88b3c = private unnamed_addr constant [13 x i8] c"Full name 2: ", align 1
@alloc_01c96032bef09c594370c69d89bae6c9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ffd61fdf645e00d9651e0dd946c88b3c, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_dd855cc080aae12cd4984efe3260e546 = private unnamed_addr constant [33 x i8] c"Function with conditional logic:\0A", align 1
@alloc_83036713c175257378775d9ac655e7bc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_dd855cc080aae12cd4984efe3260e546, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_a93a2794569a5b2e84c44f09a9e8b866 = private unnamed_addr constant [16 x i8] c"Score 95: Grade ", align 1
@alloc_6fe55a16628a090bfa2308a2f7e5477b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a93a2794569a5b2e84c44f09a9e8b866, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ea204ee25adc5da39550197086e98c01 = private unnamed_addr constant [16 x i8] c"Score 82: Grade ", align 1
@alloc_2d534af47d9132840829c12e0541f575 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ea204ee25adc5da39550197086e98c01, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0ed23f9902934dd7760f137b6580efcb = private unnamed_addr constant [16 x i8] c"Score 67: Grade ", align 1
@alloc_77be2dd9e1e44d565676ec4fe6730769 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0ed23f9902934dd7760f137b6580efcb, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aecbcf0fd35616c157f8aeeee4615d11 = private unnamed_addr constant [16 x i8] c"Score 45: Grade ", align 1
@alloc_716aa76184a210c2d2d85f03bcc0a687 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aecbcf0fd35616c157f8aeeee4615d11, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f72f79130ce2de0907e8ba87e94f91da = private unnamed_addr constant [20 x i8] c"Function with loop:\0A", align 1
@alloc_49491038decea3c877d8cd4cde561412 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f72f79130ce2de0907e8ba87e94f91da, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_66d43da9793d94922acdbbc24d487a92 = private unnamed_addr constant [35 x i8] c"Function calling another function:\0A", align 1
@alloc_72589839e6144e408cb17c8c135cec0b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_66d43da9793d94922acdbbc24d487a92, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_9f02e137ddc81d1e7096ff184733dcad = private unnamed_addr constant [16 x i8] c"Rectangle area: ", align 1
@alloc_b0f09e196835742a43ff6dcb694cd2b9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9f02e137ddc81d1e7096ff184733dcad, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b228271df2cfc149e1cdc873ba75704c = private unnamed_addr constant [34 x i8] c"Function with complex parameters:\0A", align 1
@alloc_a9487ce1b5a2625bfc2feca950a3842d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b228271df2cfc149e1cdc873ba75704c, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_359b7dc4bc3bfd93aae4f476e001fa3b = private unnamed_addr constant [6 x i8] c"Laptop", align 1
@alloc_f769a395077a11c4f6799d75b7c43ddc = private unnamed_addr constant [13 x i8] c"Order total: ", align 1
@alloc_073164b02770862c0c8190ac9124619a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f769a395077a11c4f6799d75b7c43ddc, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8b2860acd779fc14ded4e422f0a2bdd0 = private unnamed_addr constant [30 x i8] c"Function with boolean return:\0A", align 1
@alloc_59c8909478ade0ccfbb6684b487c7b62 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8b2860acd779fc14ded4e422f0a2bdd0, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_ed2c459bb01cc462892a2c5c6e7c337c = private unnamed_addr constant [11 x i8] c"8 is even: ", align 1
@alloc_292f94325ac15fc605c06cd3b325f6df = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ed2c459bb01cc462892a2c5c6e7c337c, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2af3a0dd61a430b6fd0d1f4fb001ccec = private unnamed_addr constant [11 x i8] c"7 is even: ", align 1
@alloc_8aa911b34eb555601d932384272196f0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2af3a0dd61a430b6fd0d1f4fb001ccec, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d29be7bc76c88093dbe79fe409295434 = private unnamed_addr constant [37 x i8] c"Function with multiple calculations:\0A", align 1
@alloc_6281d340c61abba486a666be191c5933 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d29be7bc76c88093dbe79fe409295434, [8 x i8] c"%\00\00\00\00\00\00\00" }>, align 8
@alloc_200ee8e988d254d03f9d3e81cad8dd8d = private unnamed_addr constant [14 x i8] c"Returned sum: ", align 1
@alloc_af37571da156c7615d758b5866ebbeb3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_200ee8e988d254d03f9d3e81cad8dd8d, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e0950fb16700d0fc40790d4fa3b4e6fe = private unnamed_addr constant [20 x i8] c"Recursive function:\0A", align 1
@alloc_bfc1bdd740c806a41983b43f753811a0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e0950fb16700d0fc40790d4fa3b4e6fe, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_772aebfd0fc0d5ea283b4cde214f6ef0 = private unnamed_addr constant [30 x i8] c"=== End of Functions Demo ===\0A", align 1
@alloc_8daf9431b03f1668e441d341766c3093 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_772aebfd0fc0d5ea283b4cde214f6ef0, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_8c0f55101f188e3b5193e6cef37db8a9 = private unnamed_addr constant [23 x i8] c"Hello from a function!\0A", align 1
@alloc_28b5813c72d8a880a0af8d2715ce2ded = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8c0f55101f188e3b5193e6cef37db8a9, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_cc97ff70f5a27a26cd7b3dace62318e0 = private unnamed_addr constant [26 x i8] c"This is a simple function\0A", align 1
@alloc_2fb46f35f370de4750a024ee09e3ae88 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cc97ff70f5a27a26cd7b3dace62318e0, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_8da64394926b2865e2bcc88447d89de2 = private unnamed_addr constant [7 x i8] c"Hello, ", align 1
@alloc_aa8949e1fb3783c4ba3f17396bbaa3bf = private unnamed_addr constant [2 x i8] c"!\0A", align 1
@alloc_787dfce4267000cb09059ad6c4d3ec65 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8da64394926b2865e2bcc88447d89de2, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_aa8949e1fb3783c4ba3f17396bbaa3bf, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_46b02be4bb6dd0f5d4eed42bc91b9e1b = private unnamed_addr constant [18 x i8] c"Nice to meet you!\0A", align 1
@alloc_cf8b2d15eafa850be62cacf9239f3b32 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_46b02be4bb6dd0f5d4eed42bc91b9e1b, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a = private unnamed_addr constant [6 x i8] c"Name: ", align 1
@alloc_cd99b202573048149e4ac0d7df184409 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aa11b714384947dbd3caef261ef0dc2e = private unnamed_addr constant [5 x i8] c"Age: ", align 1
@alloc_c8d066cadf1ba35bf631428b9f61b62c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aa11b714384947dbd3caef261ef0dc2e, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e62944320169190b752f59d178535afe = private unnamed_addr constant [6 x i8] c"City: ", align 1
@alloc_78fc66fc056454fd6d04e37046180610 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e62944320169190b752f59d178535afe, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_12d75d101fb0ae1c4d2a5dda8c55dbd9 = private unnamed_addr constant [4 x i8] c"---\0A", align 1
@alloc_62864721e615d98884de57efc7309d65 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_12d75d101fb0ae1c4d2a5dda8c55dbd9, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_9c13379859a5f39f7de5df98d58c93cb = private unnamed_addr constant [15 x i8] c"06_functions.rs", align 1
@alloc_a46d947ecf1b055d4d64ce85f8fdf80e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00x\00\00\00\05\00\00\00" }>, align 8
@alloc_da8c6d0770a6a9ff62f4a7885e86a060 = private unnamed_addr constant [20 x i8] c"Calculating area of ", align 1
@alloc_512d341499b7f53469cfbe4313f7e34a = private unnamed_addr constant [3 x i8] c" x ", align 1
@alloc_ba7f45492dd62671365e7ae8a93c61a5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_da8c6d0770a6a9ff62f4a7885e86a060, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_512d341499b7f53469cfbe4313f7e34a, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f0c451bc7d51e40082606c65e41ceded = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00~\00\00\00\05\00\00\00" }>, align 8
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_a0434d70b15bcc9ff1a7b717be16ed72 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4b372b42a7e59c4e87186c5d2ddb750d = private unnamed_addr constant [1 x i8] c"F", align 1
@alloc_fba4efe8e4f7fab8265f1b3a352c9317 = private unnamed_addr constant [1 x i8] c"D", align 1
@alloc_e57470275a219d8492d489e56910499e = private unnamed_addr constant [1 x i8] c"C", align 1
@alloc_d3bbdebcd7d668a59dc59a90afdc2fa1 = private unnamed_addr constant [1 x i8] c"B", align 1
@alloc_e2ead6761956d440a2a6c3412b417ffa = private unnamed_addr constant [1 x i8] c"A", align 1
@alloc_c0fca04bd2a619721b366d7ae178c0d5 = private unnamed_addr constant [12 x i8] c"Counting to ", align 1
@alloc_2c871cb4eca8f760225510900b094559 = private unnamed_addr constant [2 x i8] c":\0A", align 1
@alloc_7af8315c33b07853f447458817adb541 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c0fca04bd2a619721b366d7ae178c0d5, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_2c871cb4eca8f760225510900b094559, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_09e0634e380a1afaba2caba42a65356f = private unnamed_addr constant [15 x i8] c"Done counting!\0A", align 1
@alloc_0d63fa60b0e3a914d02f0426749bd353 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09e0634e380a1afaba2caba42a65356f, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_34310cabf6554cc6123dbb21242f43b8 = private unnamed_addr constant [2 x i8] c"  ", align 1
@alloc_498dd48fed0743022e84e831ad90c0fe = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_34310cabf6554cc6123dbb21242f43b8, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9be85685af010fcc809b95c5f6b9c444 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00\A0\00\00\00\05\00\00\00" }>, align 8
@alloc_16c59e5715f3b55d0f26b6c2959b76b9 = private unnamed_addr constant [22 x i8] c"Rectangle dimensions: ", align 1
@alloc_9246bf7f4e945f8fc2b0e945666d28d9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_16c59e5715f3b55d0f26b6c2959b76b9, [8 x i8] c"\16\00\00\00\00\00\00\00", ptr @alloc_512d341499b7f53469cfbe4313f7e34a, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e67303b97ed45a897ea83a476975613e = private unnamed_addr constant [18 x i8] c"Processing order:\0A", align 1
@alloc_15521d023f0435c64581ef7338a86e7b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e67303b97ed45a897ea83a476975613e, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_0c401e82156cc3ea9e66af7f555ca6d2 = private unnamed_addr constant [8 x i8] c"  Item: ", align 1
@alloc_97c0c5c8cddcc587a067d2866cb74391 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0c401e82156cc3ea9e66af7f555ca6d2, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e3e47f6b01636ffe550152c45d9eb0f3 = private unnamed_addr constant [12 x i8] c"  Quantity: ", align 1
@alloc_b4ae93f91e238f75c44dc51bb7f4bac4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e3e47f6b01636ffe550152c45d9eb0f3, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_33c03bc0d05d550a0839ebdaf0a40643 = private unnamed_addr constant [14 x i8] c"  Unit price: ", align 1
@alloc_5f54a043a0ae8a622697b92e037019bb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_33c03bc0d05d550a0839ebdaf0a40643, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c275171b0c4684a57ad93515dc051d7c = private unnamed_addr constant [9 x i8] c"  Total: ", align 1
@alloc_d3e9b451ca27d86b4691fd1eafc58fee = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c275171b0c4684a57ad93515dc051d7c, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_75657af196a4d1d8fe3d560ef93da88a = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00\B5\00\00\00\05\00\00\00" }>, align 8
@alloc_055cccb03818a0be630c75fbc16d5190 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00\BA\00\00\00\0F\00\00\00" }>, align 8
@alloc_53b4740c186af9da93046ff6029548a4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00\BB\00\00\00\13\00\00\00" }>, align 8
@alloc_490cb4e03a0a74e11e380be5f72eed5c = private unnamed_addr constant [9 x i8] c"Numbers: ", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_705789b1971018fff4a7b5cb70e0a81a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_490cb4e03a0a74e11e380be5f72eed5c, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5b0102dc258292fd5e83bca8293a8d53 = private unnamed_addr constant [5 x i8] c"Sum: ", align 1
@alloc_fae7b78f3403b48b2870d7d78fcc4226 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5b0102dc258292fd5e83bca8293a8d53, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e0410df4b5c730b8f5eb072f94c35732 = private unnamed_addr constant [9 x i8] c"Average: ", align 1
@alloc_e93491e3b40beb615f607f92c37da4a2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e0410df4b5c730b8f5eb072f94c35732, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c4128a4d91a1164e83aa65bff8a5ab8d = private unnamed_addr constant [9 x i8] c"Maximum: ", align 1
@alloc_3fed9fdde7e6ef1b81519ccb53d625ea = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c4128a4d91a1164e83aa65bff8a5ab8d, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_55ccbe0dde2c69e416d4a1feaf2b0692 = private unnamed_addr constant [20 x i8] c"Countdown finished!\0A", align 1
@alloc_e6b3cba32edd29e8a7e82f55b972199e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_55ccbe0dde2c69e416d4a1feaf2b0692, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_052a2038c5f165e5d575887b9f967efd = private unnamed_addr constant [11 x i8] c"Countdown: ", align 1
@alloc_10015caba44e6f8e925ee543e933f28f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_052a2038c5f165e5d575887b9f967efd, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ea8ea86aee137f89f0b14aab690e99d8 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_9c13379859a5f39f7de5df98d58c93cb, [16 x i8] c"\0F\00\00\00\00\00\00\00\C9\00\00\00\1C\00\00\00" }>, align 8

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hfc6b4a105c7f1129E"(ptr align 4 %self) unnamed_addr #0 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h4af7e492bf055039E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hd8ea22520bc7f9f0E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #1 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd549237333deab59E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h2a047d34759526e0E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7fe3979ee821d82eE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h2a047d34759526e0E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h9463338f4233aee1E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha01772421588af70E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h4af7e492bf055039E"(i32 %start1, i64 %n) unnamed_addr #0 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.3ecf432813bebb659822bf8c7145f793.0, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.0, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h91b17b9f283074bbE() #15
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h0c3f8c51ee0b4befE(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17he008184dc572fef4E(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #18
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17hb9e6058c42c531e4E() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::Ord::max
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3cmp3Ord3max17h8823a45b7a12162bE(i32 %0, i32 %1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [4 x i8], align 4
  %other = alloca [4 x i8], align 4
  %self = alloca [4 x i8], align 4
  store i32 %0, ptr %self, align 4
  store i32 %1, ptr %other, align 4
  store i8 1, ptr %_6, align 1
  %_3.i = load i32, ptr %other, align 4
  %_4.i = load i32, ptr %self, align 4
  %_0.i = icmp slt i32 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load i32, ptr %other, align 4
  store i32 %2, ptr %_0, align 4
  %3 = load i8, ptr %_6, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %5 = load i32, ptr %self, align 4
  store i32 %5, ptr %_0, align 4
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load i32, ptr %_0, align 4
  ret i32 %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha01772421588af70E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h5025bce8325c9c02E(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hbfdd13d54314b2bfE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hcaac4dc5dcf91284E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h17c184b1ba60ed33E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h52e78590a3b3e311E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117ha68ff56c70acfa41E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h337a15895a0e5060E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #18
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h406cd8834cc3213cE"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #0 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h9ba782ab727c9434E"(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17hc90c233b05b8063fE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h5dfde4e70640661dE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #0 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load i64, ptr %4, align 8
; call alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
  call void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h869ac34ddca54751E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %3, i64 %5)
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h9463338f4233aee1E(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17hc90c233b05b8063fE(ptr %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd549237333deab59E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17h21c6e1027621d573E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #18
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h7e6617bb3f22f376E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hde3e016324c84ff8E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hde3e016324c84ff8E"(ptr align 8 %_1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h15970252b7b70fb2E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h30fa67be49326729E"(ptr align 8 %_1) #19 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h30fa67be49326729E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h30fa67be49326729E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h33dbe62dc90f2ec3E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h69b1ef3196160c98E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h0435670999875b9aE"(ptr %ptr) unnamed_addr #3 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17h8e05d0124e60ee6dE(i1 zeroext %cond) unnamed_addr #3 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17h91b17b9f283074bbE() unnamed_addr #3 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #18
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hdd3750f80fdf231dE"(ptr align 4 %self) unnamed_addr #0 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hfc6b4a105c7f1129E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hf50dc4de84b1b9d0E(ptr align 8 %self, i64 %n) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h51df3a4b1aeb17e4E(i64 %size, i64 %align) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #18
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h76da71659644c57aE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #0 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h51df3a4b1aeb17e4E(i64 %new_size, i64 %_23) #15
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hf50dc4de84b1b9d0E(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h74efd7b9af4fc9deE(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #16
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #18
  unreachable
}

; core::option::Option<T>::map_or_else
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hb58e938fea6ba568E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 8 %default) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_9 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  store i8 1, ptr %_10, align 1
  store i8 1, ptr %_9, align 1
  %3 = load ptr, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = ptrtoint ptr %3 to i64
  %7 = icmp eq i64 %6, 0
  %_4 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_4 to i1
  br i1 %8, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %t.0 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %9, align 8
  store i8 0, ptr %_9, align 1
; invoke core::ops::function::FnOnce::call_once
  invoke void @_ZN4core3ops8function6FnOnce9call_once17h5dfde4e70640661dE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %t.0, i64 %t.1)
          to label %bb4 unwind label %funclet_bb11

bb2:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke alloc::fmt::format::{{closure}}
  invoke void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17hc9616a9a0113c680E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %default)
          to label %bb5 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %10 = load i8, ptr %_9, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb10, label %bb11_cleanup_trampoline_bb7

funclet_bb11:                                     ; preds = %bb3, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb2
  br label %bb6

bb6:                                              ; preds = %bb9, %bb4, %bb5
  ret void

bb4:                                              ; preds = %bb3
  %12 = load i8, ptr %_10, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

bb9:                                              ; preds = %bb4
  br label %bb6

bb7:                                              ; preds = %funclet_bb7
  %14 = load i8, ptr %_10, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb12, label %bb8

funclet_bb7:                                      ; preds = %bb10, %bb11_cleanup_trampoline_bb7
  %cleanuppad1 = cleanuppad within none []
  br label %bb7

bb11_cleanup_trampoline_bb7:                      ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb10:                                             ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb8:                                              ; preds = %bb12, %bb7
  cleanupret from %cleanuppad1 unwind to caller

bb12:                                             ; preds = %bb7
  br label %bb8

bb1:                                              ; No predecessors!
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17he008184dc572fef4E(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #0 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #18
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7fe3979ee821d82eE"() unnamed_addr #0 {
start:
  ret i32 0
}

; alloc::fmt::format
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc3fmt6format17hd114ec5f7231d234E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %args) unnamed_addr #0 {
start:
  %_2 = alloca [16 x i8], align 8
  %_6.0 = load ptr, ptr %args, align 8
  %0 = getelementptr inbounds i8, ptr %args, i64 8
  %_6.1 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %args, i64 16
  %_7.0 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7.1 = load i64, ptr %2, align 8
  %3 = icmp eq i64 %_6.1, 0
  br i1 %3, label %bb4, label %bb5

bb4:                                              ; preds = %start
  %4 = icmp eq i64 %_7.1, 0
  br i1 %4, label %bb8, label %bb3

bb5:                                              ; preds = %start
  %5 = icmp eq i64 %_6.1, 1
  br i1 %5, label %bb6, label %bb3

bb8:                                              ; preds = %bb4
  store ptr inttoptr (i64 1 to ptr), ptr %_2, align 8
  %6 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 0, ptr %6, align 8
  br label %bb2

bb3:                                              ; preds = %bb6, %bb5, %bb4
  %7 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %8 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  store ptr %7, ptr %_2, align 8
  %9 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb2

bb2:                                              ; preds = %bb3, %bb7, %bb8
  %10 = load ptr, ptr %_2, align 8
  %11 = getelementptr inbounds i8, ptr %_2, i64 8
  %12 = load i64, ptr %11, align 8
; call core::option::Option<T>::map_or_else
  call void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hb58e938fea6ba568E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %10, i64 %12, ptr align 8 %args)
  ret void

bb6:                                              ; preds = %bb5
  %13 = icmp eq i64 %_7.1, 0
  br i1 %13, label %bb7, label %bb3

bb7:                                              ; preds = %bb6
  %s = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %14 = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %_13.0 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  %_13.1 = load i64, ptr %15, align 8
  store ptr %_13.0, ptr %_2, align 8
  %16 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %_13.1, ptr %16, align 8
  br label %bb2
}

; alloc::fmt::format::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17hc9616a9a0113c680E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_1) unnamed_addr #0 {
start:
  %_2 = alloca [48 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %_1, i64 48, i1 false)
; call alloc::fmt::format::format_inner
  call void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_2)
  ret void
}

; alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h869ac34ddca54751E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h6a36b3fd3c308688E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17h6aad7f3a878df253E(i64 %0, i64 %1) unnamed_addr #0 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h21c6e1027621d573E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #15
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #15
  ret ptr %_0
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h633970258827b23aE(i64 %0, i64 %1) unnamed_addr #0 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h21c6e1027621d573E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #15
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #15
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc6954cd032a8bc22E(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #0 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h0435670999875b9aE"(ptr %_23) #15
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h633970258827b23aE(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17h6aad7f3a878df253E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h0435670999875b9aE"(ptr %ptr) #15
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h0435670999875b9aE"(ptr %ptr4) #15
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h5cf32d76e7239c0fE"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hcfc011219c462e6cE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17hc3c93527e5442d4bE"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hcfc011219c462e6cE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h337a15895a0e5060E"(i64 %self1, i64 %self3) #15
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h51df3a4b1aeb17e4E(i64 %8, i64 %9) #15
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h1f8baef11eae2b25E"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h76da71659644c57aE(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.3ecf432813bebb659822bf8c7145f793.1, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17he84bf654b819be01E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17he62a1af36156631aE"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17hedaaf3db0794a773E"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #0 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h1f8baef11eae2b25E"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #16
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h8e05d0124e60ee6dE(i1 zeroext %cond) #15
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hcaac4dc5dcf91284E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h74efd7b9af4fc9deE(ptr %_8, i64 1, i64 1, i64 %len) #15
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hb2a0fd3d65d27a4eE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17hc3c93527e5442d4bE"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #15
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17he84bf654b819be01E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #0 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc6954cd032a8bc22E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17he62a1af36156631aE"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #0 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc6954cd032a8bc22E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h15970252b7b70fb2E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h33dbe62dc90f2ec3E"(ptr align 8 %self) unnamed_addr #1 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h5cf32d76e7239c0fE"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h6a36b3fd3c308688E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #0 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17hedaaf3db0794a773E"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h0c3f8c51ee0b4befE(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #15
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; _06_functions::main
; Function Attrs: uwtable
define internal void @_ZN13_06_functions4main17h0a74e8e48b324a1fE() unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_216 = alloca [48 x i8], align 8
  %_213 = alloca [48 x i8], align 8
  %_209 = alloca [48 x i8], align 8
  %_206 = alloca [48 x i8], align 8
  %_203 = alloca [16 x i8], align 8
  %_202 = alloca [16 x i8], align 8
  %_199 = alloca [48 x i8], align 8
  %total = alloca [4 x i8], align 4
  %_195 = alloca [48 x i8], align 8
  %_192 = alloca [48 x i8], align 8
  %_189 = alloca [16 x i8], align 8
  %_188 = alloca [16 x i8], align 8
  %_185 = alloca [48 x i8], align 8
  %_182 = alloca [16 x i8], align 8
  %_181 = alloca [16 x i8], align 8
  %_178 = alloca [48 x i8], align 8
  %check2 = alloca [1 x i8], align 1
  %check1 = alloca [1 x i8], align 1
  %_173 = alloca [48 x i8], align 8
  %_170 = alloca [48 x i8], align 8
  %_167 = alloca [16 x i8], align 8
  %_166 = alloca [16 x i8], align 8
  %_163 = alloca [48 x i8], align 8
  %order_total = alloca [4 x i8], align 4
  %_158 = alloca [48 x i8], align 8
  %_155 = alloca [48 x i8], align 8
  %_152 = alloca [16 x i8], align 8
  %_151 = alloca [16 x i8], align 8
  %_148 = alloca [48 x i8], align 8
  %rect_area = alloca [4 x i8], align 4
  %_144 = alloca [48 x i8], align 8
  %_141 = alloca [48 x i8], align 8
  %_137 = alloca [48 x i8], align 8
  %_134 = alloca [48 x i8], align 8
  %_131 = alloca [16 x i8], align 8
  %_130 = alloca [16 x i8], align 8
  %_127 = alloca [48 x i8], align 8
  %_124 = alloca [16 x i8], align 8
  %_123 = alloca [16 x i8], align 8
  %_120 = alloca [48 x i8], align 8
  %_117 = alloca [16 x i8], align 8
  %_116 = alloca [16 x i8], align 8
  %_113 = alloca [48 x i8], align 8
  %_110 = alloca [16 x i8], align 8
  %_109 = alloca [16 x i8], align 8
  %_106 = alloca [48 x i8], align 8
  %grade4 = alloca [16 x i8], align 8
  %grade3 = alloca [16 x i8], align 8
  %grade2 = alloca [16 x i8], align 8
  %grade1 = alloca [16 x i8], align 8
  %_99 = alloca [48 x i8], align 8
  %_96 = alloca [48 x i8], align 8
  %_93 = alloca [16 x i8], align 8
  %_92 = alloca [16 x i8], align 8
  %_89 = alloca [48 x i8], align 8
  %_86 = alloca [16 x i8], align 8
  %_85 = alloca [16 x i8], align 8
  %_82 = alloca [48 x i8], align 8
  %name2 = alloca [24 x i8], align 8
  %name1 = alloca [24 x i8], align 8
  %_73 = alloca [48 x i8], align 8
  %_70 = alloca [48 x i8], align 8
  %_67 = alloca [16 x i8], align 8
  %_66 = alloca [16 x i8], align 8
  %_63 = alloca [48 x i8], align 8
  %room_area = alloca [4 x i8], align 4
  %_59 = alloca [48 x i8], align 8
  %_56 = alloca [48 x i8], align 8
  %_53 = alloca [16 x i8], align 8
  %_52 = alloca [16 x i8], align 8
  %_49 = alloca [48 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %_45 = alloca [16 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %result2 = alloca [4 x i8], align 4
  %result1 = alloca [4 x i8], align 4
  %_37 = alloca [48 x i8], align 8
  %_34 = alloca [48 x i8], align 8
  %_25 = alloca [48 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_15 = alloca [48 x i8], align 8
  %_12 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_412b738e3d68629d280067e90c1bf715)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_aa0d2daae5fffa2e8c5bdcc5300d9315)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call _06_functions::greet
  call void @_ZN13_06_functions5greet17hf0c80722a1fc23beE()
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_12, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_12)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_15, ptr align 8 @alloc_63cf7cafac715399a827add19fbe35cd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_15)
; call _06_functions::greet_person
  call void @_ZN13_06_functions12greet_person17hdb8af59b3e9d632dE(ptr align 1 @alloc_b468ef00fa5916b1335583d25c8252ad, i64 5)
; call _06_functions::greet_person
  call void @_ZN13_06_functions12greet_person17hdb8af59b3e9d632dE(ptr align 1 @alloc_8a3ff4f0306cccf00c85887c84f94191, i64 4)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_22, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_22)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_25, ptr align 8 @alloc_3a9385f08799a7ca71dc66a21de865ca)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_25)
; call _06_functions::introduce
  call void @_ZN13_06_functions9introduce17h86882895169b5dc0E(ptr align 1 @alloc_a118b2b99663e794e1f4a92820d83446, i64 3, i32 25, ptr align 1 @alloc_67eb4974a8310b72f6f5dca9d9c7655a, i64 5)
; call _06_functions::introduce
  call void @_ZN13_06_functions9introduce17h86882895169b5dc0E(ptr align 1 @alloc_addbe31108129e35061bea48eb70422e, i64 6, i32 30, ptr align 1 @alloc_6641fa9b931d9297b00a346a0030367b, i64 10)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_34, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_34)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_37, ptr align 8 @alloc_9389381634d84f4e21238264e60bfd68)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_37)
; call _06_functions::add_numbers
  %0 = call i32 @_ZN13_06_functions11add_numbers17h73d509c184646ef7E(i32 5, i32 3)
  store i32 %0, ptr %result1, align 4
; call _06_functions::add_numbers
  %1 = call i32 @_ZN13_06_functions11add_numbers17h73d509c184646ef7E(i32 10, i32 7)
  store i32 %1, ptr %result2, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_46, ptr align 4 %result1)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_45, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_46, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_42, ptr align 8 @alloc_75dbb2a3aa7c11da7588e47b7a8643a5, ptr align 8 %_45)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_42)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_53, ptr align 4 %result2)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_52, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_53, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_49, ptr align 8 @alloc_1f99729cc2e0ddb19b0ac1eeb429b722, ptr align 8 %_52)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_49)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_56, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_56)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_59, ptr align 8 @alloc_675ef80cf6e969d19d9fc86e2b98a76a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_59)
; call _06_functions::calculate_area
  %4 = call i32 @_ZN13_06_functions14calculate_area17h4f650efaaa61cb12E(i32 12, i32 8)
  store i32 %4, ptr %room_area, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_67, ptr align 4 %room_area)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_66, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_67, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_63, ptr align 8 @alloc_d700338e9e98b8bc294fddc9f7393bda, ptr align 8 %_66)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_63)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_70, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_70)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_73, ptr align 8 @alloc_30e1a9a0136fde75a2ba0d979ac7f362)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_73)
; call _06_functions::create_full_name
  call void @_ZN13_06_functions16create_full_name17h59c6000770acd8e7E(ptr sret([24 x i8]) align 8 %name1, ptr align 1 @alloc_b468ef00fa5916b1335583d25c8252ad, i64 5, ptr align 1 @alloc_a10b29fa1aded051447dbb8c90cd1047, i64 6)
; invoke _06_functions::create_full_name
  invoke void @_ZN13_06_functions16create_full_name17h59c6000770acd8e7E(ptr sret([24 x i8]) align 8 %name2, ptr align 1 @alloc_8a3ff4f0306cccf00c85887c84f94191, i64 4, ptr align 1 @alloc_a118b2b99663e794e1f4a92820d83446, i64 3)
          to label %bb45 unwind label %funclet_bb125

bb125:                                            ; preds = %funclet_bb125
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h7e6617bb3f22f376E"(ptr align 8 %name1) #19 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb125:                                    ; preds = %bb124, %bb121, %start
  %cleanuppad = cleanuppad within none []
  br label %bb125

bb45:                                             ; preds = %start
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbfdd13d54314b2bfE(ptr sret([16 x i8]) align 8 %_86, ptr align 8 %name1)
          to label %bb46 unwind label %funclet_bb124

bb124:                                            ; preds = %funclet_bb124
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h7e6617bb3f22f376E"(ptr align 8 %name2) #19 [ "funclet"(token %cleanuppad1) ]
  cleanupret from %cleanuppad1 unwind label %funclet_bb125

funclet_bb124:                                    ; preds = %bb120, %bb119, %bb118, %bb117, %bb116, %bb115, %bb114, %bb113, %bb112, %bb111, %bb110, %bb109, %bb108, %bb107, %bb106, %bb105, %bb104, %bb103, %bb102, %bb101, %bb100, %bb99, %bb98, %bb97, %bb96, %bb95, %bb94, %bb93, %bb92, %bb91, %bb90, %bb89, %bb88, %bb87, %bb86, %bb85, %bb84, %bb83, %bb82, %bb81, %bb80, %bb79, %bb78, %bb77, %bb76, %bb75, %bb74, %bb73, %bb72, %bb71, %bb70, %bb69, %bb68, %bb67, %bb66, %bb65, %bb64, %bb63, %bb62, %bb61, %bb60, %bb59, %bb58, %bb57, %bb56, %bb55, %bb54, %bb53, %bb52, %bb51, %bb50, %bb49, %bb48, %bb47, %bb46, %bb45
  %cleanuppad1 = cleanuppad within none []
  br label %bb124

bb46:                                             ; preds = %bb45
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_85, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_86, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_82, ptr align 8 @alloc_a2110adb6bccdfbd261c6c42c8b11247, ptr align 8 %_85)
          to label %bb47 unwind label %funclet_bb124

bb47:                                             ; preds = %bb46
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_82)
          to label %bb48 unwind label %funclet_bb124

bb48:                                             ; preds = %bb47
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbfdd13d54314b2bfE(ptr sret([16 x i8]) align 8 %_93, ptr align 8 %name2)
          to label %bb49 unwind label %funclet_bb124

bb49:                                             ; preds = %bb48
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_92, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_93, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_89, ptr align 8 @alloc_01c96032bef09c594370c69d89bae6c9, ptr align 8 %_92)
          to label %bb50 unwind label %funclet_bb124

bb50:                                             ; preds = %bb49
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_89)
          to label %bb51 unwind label %funclet_bb124

bb51:                                             ; preds = %bb50
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_96, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb52 unwind label %funclet_bb124

bb52:                                             ; preds = %bb51
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_96)
          to label %bb53 unwind label %funclet_bb124

bb53:                                             ; preds = %bb52
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_99, ptr align 8 @alloc_83036713c175257378775d9ac655e7bc)
          to label %bb54 unwind label %funclet_bb124

bb54:                                             ; preds = %bb53
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_99)
          to label %bb55 unwind label %funclet_bb124

bb55:                                             ; preds = %bb54
; invoke _06_functions::check_grade
  %8 = invoke { ptr, i64 } @_ZN13_06_functions11check_grade17h4be00949ab8772e9E(i32 95)
          to label %bb56 unwind label %funclet_bb124

bb56:                                             ; preds = %bb55
  %9 = extractvalue { ptr, i64 } %8, 0
  %10 = extractvalue { ptr, i64 } %8, 1
  store ptr %9, ptr %grade1, align 8
  %11 = getelementptr inbounds i8, ptr %grade1, i64 8
  store i64 %10, ptr %11, align 8
; invoke _06_functions::check_grade
  %12 = invoke { ptr, i64 } @_ZN13_06_functions11check_grade17h4be00949ab8772e9E(i32 82)
          to label %bb57 unwind label %funclet_bb124

bb57:                                             ; preds = %bb56
  %13 = extractvalue { ptr, i64 } %12, 0
  %14 = extractvalue { ptr, i64 } %12, 1
  store ptr %13, ptr %grade2, align 8
  %15 = getelementptr inbounds i8, ptr %grade2, i64 8
  store i64 %14, ptr %15, align 8
; invoke _06_functions::check_grade
  %16 = invoke { ptr, i64 } @_ZN13_06_functions11check_grade17h4be00949ab8772e9E(i32 67)
          to label %bb58 unwind label %funclet_bb124

bb58:                                             ; preds = %bb57
  %17 = extractvalue { ptr, i64 } %16, 0
  %18 = extractvalue { ptr, i64 } %16, 1
  store ptr %17, ptr %grade3, align 8
  %19 = getelementptr inbounds i8, ptr %grade3, i64 8
  store i64 %18, ptr %19, align 8
; invoke _06_functions::check_grade
  %20 = invoke { ptr, i64 } @_ZN13_06_functions11check_grade17h4be00949ab8772e9E(i32 45)
          to label %bb59 unwind label %funclet_bb124

bb59:                                             ; preds = %bb58
  %21 = extractvalue { ptr, i64 } %20, 0
  %22 = extractvalue { ptr, i64 } %20, 1
  store ptr %21, ptr %grade4, align 8
  %23 = getelementptr inbounds i8, ptr %grade4, i64 8
  store i64 %22, ptr %23, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_110, ptr align 8 %grade1)
          to label %bb60 unwind label %funclet_bb124

bb60:                                             ; preds = %bb59
  %24 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_109, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %24, ptr align 8 %_110, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_106, ptr align 8 @alloc_6fe55a16628a090bfa2308a2f7e5477b, ptr align 8 %_109)
          to label %bb61 unwind label %funclet_bb124

bb61:                                             ; preds = %bb60
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_106)
          to label %bb62 unwind label %funclet_bb124

bb62:                                             ; preds = %bb61
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_117, ptr align 8 %grade2)
          to label %bb63 unwind label %funclet_bb124

bb63:                                             ; preds = %bb62
  %25 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_116, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %25, ptr align 8 %_117, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_113, ptr align 8 @alloc_2d534af47d9132840829c12e0541f575, ptr align 8 %_116)
          to label %bb64 unwind label %funclet_bb124

bb64:                                             ; preds = %bb63
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_113)
          to label %bb65 unwind label %funclet_bb124

bb65:                                             ; preds = %bb64
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_124, ptr align 8 %grade3)
          to label %bb66 unwind label %funclet_bb124

bb66:                                             ; preds = %bb65
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_123, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_124, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_120, ptr align 8 @alloc_77be2dd9e1e44d565676ec4fe6730769, ptr align 8 %_123)
          to label %bb67 unwind label %funclet_bb124

bb67:                                             ; preds = %bb66
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_120)
          to label %bb68 unwind label %funclet_bb124

bb68:                                             ; preds = %bb67
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_131, ptr align 8 %grade4)
          to label %bb69 unwind label %funclet_bb124

bb69:                                             ; preds = %bb68
  %27 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_130, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %27, ptr align 8 %_131, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_127, ptr align 8 @alloc_716aa76184a210c2d2d85f03bcc0a687, ptr align 8 %_130)
          to label %bb70 unwind label %funclet_bb124

bb70:                                             ; preds = %bb69
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_127)
          to label %bb71 unwind label %funclet_bb124

bb71:                                             ; preds = %bb70
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_134, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb72 unwind label %funclet_bb124

bb72:                                             ; preds = %bb71
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_134)
          to label %bb73 unwind label %funclet_bb124

bb73:                                             ; preds = %bb72
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_137, ptr align 8 @alloc_49491038decea3c877d8cd4cde561412)
          to label %bb74 unwind label %funclet_bb124

bb74:                                             ; preds = %bb73
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_137)
          to label %bb75 unwind label %funclet_bb124

bb75:                                             ; preds = %bb74
; invoke _06_functions::count_to_n
  invoke void @_ZN13_06_functions10count_to_n17h5f0a0bd3443af4e0E(i32 5)
          to label %bb76 unwind label %funclet_bb124

bb76:                                             ; preds = %bb75
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_141, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb77 unwind label %funclet_bb124

bb77:                                             ; preds = %bb76
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_141)
          to label %bb78 unwind label %funclet_bb124

bb78:                                             ; preds = %bb77
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_144, ptr align 8 @alloc_72589839e6144e408cb17c8c135cec0b)
          to label %bb79 unwind label %funclet_bb124

bb79:                                             ; preds = %bb78
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_144)
          to label %bb80 unwind label %funclet_bb124

bb80:                                             ; preds = %bb79
; invoke _06_functions::calculate_rectangle_area
  %28 = invoke i32 @_ZN13_06_functions24calculate_rectangle_area17h26e12de91f870420E(i32 6, i32 4)
          to label %bb81 unwind label %funclet_bb124

bb81:                                             ; preds = %bb80
  store i32 %28, ptr %rect_area, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_152, ptr align 4 %rect_area)
          to label %bb82 unwind label %funclet_bb124

bb82:                                             ; preds = %bb81
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_151, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_152, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_148, ptr align 8 @alloc_b0f09e196835742a43ff6dcb694cd2b9, ptr align 8 %_151)
          to label %bb83 unwind label %funclet_bb124

bb83:                                             ; preds = %bb82
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_148)
          to label %bb84 unwind label %funclet_bb124

bb84:                                             ; preds = %bb83
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_155, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb85 unwind label %funclet_bb124

bb85:                                             ; preds = %bb84
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_155)
          to label %bb86 unwind label %funclet_bb124

bb86:                                             ; preds = %bb85
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_158, ptr align 8 @alloc_a9487ce1b5a2625bfc2feca950a3842d)
          to label %bb87 unwind label %funclet_bb124

bb87:                                             ; preds = %bb86
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_158)
          to label %bb88 unwind label %funclet_bb124

bb88:                                             ; preds = %bb87
; invoke _06_functions::process_order
  %30 = invoke i32 @_ZN13_06_functions13process_order17h7b16850bdc0921d7E(ptr align 1 @alloc_359b7dc4bc3bfd93aae4f476e001fa3b, i64 6, i32 2, i32 1500)
          to label %bb89 unwind label %funclet_bb124

bb89:                                             ; preds = %bb88
  store i32 %30, ptr %order_total, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_167, ptr align 4 %order_total)
          to label %bb90 unwind label %funclet_bb124

bb90:                                             ; preds = %bb89
  %31 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_166, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %31, ptr align 8 %_167, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_163, ptr align 8 @alloc_073164b02770862c0c8190ac9124619a, ptr align 8 %_166)
          to label %bb91 unwind label %funclet_bb124

bb91:                                             ; preds = %bb90
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_163)
          to label %bb92 unwind label %funclet_bb124

bb92:                                             ; preds = %bb91
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_170, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb93 unwind label %funclet_bb124

bb93:                                             ; preds = %bb92
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_170)
          to label %bb94 unwind label %funclet_bb124

bb94:                                             ; preds = %bb93
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_173, ptr align 8 @alloc_59c8909478ade0ccfbb6684b487c7b62)
          to label %bb95 unwind label %funclet_bb124

bb95:                                             ; preds = %bb94
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_173)
          to label %bb96 unwind label %funclet_bb124

bb96:                                             ; preds = %bb95
; invoke _06_functions::is_even
  %32 = invoke zeroext i1 @_ZN13_06_functions7is_even17he21757918335f842E(i32 8)
          to label %bb97 unwind label %funclet_bb124

bb97:                                             ; preds = %bb96
  %33 = zext i1 %32 to i8
  store i8 %33, ptr %check1, align 1
; invoke _06_functions::is_even
  %34 = invoke zeroext i1 @_ZN13_06_functions7is_even17he21757918335f842E(i32 7)
          to label %bb98 unwind label %funclet_bb124

bb98:                                             ; preds = %bb97
  %35 = zext i1 %34 to i8
  store i8 %35, ptr %check2, align 1
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5025bce8325c9c02E(ptr sret([16 x i8]) align 8 %_182, ptr align 1 %check1)
          to label %bb99 unwind label %funclet_bb124

bb99:                                             ; preds = %bb98
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_181, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_182, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_178, ptr align 8 @alloc_292f94325ac15fc605c06cd3b325f6df, ptr align 8 %_181)
          to label %bb100 unwind label %funclet_bb124

bb100:                                            ; preds = %bb99
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_178)
          to label %bb101 unwind label %funclet_bb124

bb101:                                            ; preds = %bb100
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5025bce8325c9c02E(ptr sret([16 x i8]) align 8 %_189, ptr align 1 %check2)
          to label %bb102 unwind label %funclet_bb124

bb102:                                            ; preds = %bb101
  %37 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_188, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %37, ptr align 8 %_189, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_185, ptr align 8 @alloc_8aa911b34eb555601d932384272196f0, ptr align 8 %_188)
          to label %bb103 unwind label %funclet_bb124

bb103:                                            ; preds = %bb102
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_185)
          to label %bb104 unwind label %funclet_bb124

bb104:                                            ; preds = %bb103
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_192, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb105 unwind label %funclet_bb124

bb105:                                            ; preds = %bb104
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_192)
          to label %bb106 unwind label %funclet_bb124

bb106:                                            ; preds = %bb105
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_195, ptr align 8 @alloc_6281d340c61abba486a666be191c5933)
          to label %bb107 unwind label %funclet_bb124

bb107:                                            ; preds = %bb106
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_195)
          to label %bb108 unwind label %funclet_bb124

bb108:                                            ; preds = %bb107
; invoke _06_functions::calculate_stats
  %38 = invoke i32 @_ZN13_06_functions15calculate_stats17hbf304b161766b69aE(i32 10, i32 15, i32 8)
          to label %bb109 unwind label %funclet_bb124

bb109:                                            ; preds = %bb108
  store i32 %38, ptr %total, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_203, ptr align 4 %total)
          to label %bb110 unwind label %funclet_bb124

bb110:                                            ; preds = %bb109
  %39 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_202, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %39, ptr align 8 %_203, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_199, ptr align 8 @alloc_af37571da156c7615d758b5866ebbeb3, ptr align 8 %_202)
          to label %bb111 unwind label %funclet_bb124

bb111:                                            ; preds = %bb110
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_199)
          to label %bb112 unwind label %funclet_bb124

bb112:                                            ; preds = %bb111
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_206, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb113 unwind label %funclet_bb124

bb113:                                            ; preds = %bb112
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_206)
          to label %bb114 unwind label %funclet_bb124

bb114:                                            ; preds = %bb113
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_209, ptr align 8 @alloc_bfc1bdd740c806a41983b43f753811a0)
          to label %bb115 unwind label %funclet_bb124

bb115:                                            ; preds = %bb114
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_209)
          to label %bb116 unwind label %funclet_bb124

bb116:                                            ; preds = %bb115
; invoke _06_functions::countdown_function
  invoke void @_ZN13_06_functions18countdown_function17h11197c0a2bea966bE(i32 5)
          to label %bb117 unwind label %funclet_bb124

bb117:                                            ; preds = %bb116
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_213, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb118 unwind label %funclet_bb124

bb118:                                            ; preds = %bb117
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_213)
          to label %bb119 unwind label %funclet_bb124

bb119:                                            ; preds = %bb118
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_216, ptr align 8 @alloc_8daf9431b03f1668e441d341766c3093)
          to label %bb120 unwind label %funclet_bb124

bb120:                                            ; preds = %bb119
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_216)
          to label %bb121 unwind label %funclet_bb124

bb121:                                            ; preds = %bb120
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h7e6617bb3f22f376E"(ptr align 8 %name2)
          to label %bb122 unwind label %funclet_bb125

bb122:                                            ; preds = %bb121
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h7e6617bb3f22f376E"(ptr align 8 %name1)
  ret void
}

; _06_functions::greet
; Function Attrs: uwtable
define internal void @_ZN13_06_functions5greet17hf0c80722a1fc23beE() unnamed_addr #1 {
start:
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_28b5813c72d8a880a0af8d2715ce2ded)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_2fb46f35f370de4750a024ee09e3ae88)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  ret void
}

; _06_functions::greet_person
; Function Attrs: uwtable
define internal void @_ZN13_06_functions12greet_person17hdb8af59b3e9d632dE(ptr align 1 %0, i64 %1) unnamed_addr #1 {
start:
  %_10 = alloca [48 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_6 = alloca [16 x i8], align 8
  %_3 = alloca [48 x i8], align 8
  %name = alloca [16 x i8], align 8
  store ptr %0, ptr %name, align 8
  %2 = getelementptr inbounds i8, ptr %name, i64 8
  store i64 %1, ptr %2, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_7, ptr align 8 %name)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_6, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_7, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_3, ptr align 8 @alloc_787dfce4267000cb09059ad6c4d3ec65, ptr align 8 %_6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_3)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_10, ptr align 8 @alloc_cf8b2d15eafa850be62cacf9239f3b32)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_10)
  ret void
}

; _06_functions::introduce
; Function Attrs: uwtable
define internal void @_ZN13_06_functions9introduce17h86882895169b5dc0E(ptr align 1 %0, i64 %1, i32 %2, ptr align 1 %3, i64 %4) unnamed_addr #1 {
start:
  %_26 = alloca [48 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %_22 = alloca [16 x i8], align 8
  %_19 = alloca [48 x i8], align 8
  %_16 = alloca [16 x i8], align 8
  %_15 = alloca [16 x i8], align 8
  %_12 = alloca [48 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %city = alloca [16 x i8], align 8
  %age = alloca [4 x i8], align 4
  %name = alloca [16 x i8], align 8
  store ptr %0, ptr %name, align 8
  %5 = getelementptr inbounds i8, ptr %name, i64 8
  store i64 %1, ptr %5, align 8
  store i32 %2, ptr %age, align 4
  store ptr %3, ptr %city, align 8
  %6 = getelementptr inbounds i8, ptr %city, i64 8
  store i64 %4, ptr %6, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_9, ptr align 8 %name)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_9, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_cd99b202573048149e4ac0d7df184409, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_16, ptr align 4 %age)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_15, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_16, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_12, ptr align 8 @alloc_c8d066cadf1ba35bf631428b9f61b62c, ptr align 8 %_15)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_12)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_23, ptr align 8 %city)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_23, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_19, ptr align 8 @alloc_78fc66fc056454fd6d04e37046180610, ptr align 8 %_22)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_19)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_26, ptr align 8 @alloc_62864721e615d98884de57efc7309d65)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_26)
  ret void
}

; _06_functions::add_numbers
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions11add_numbers17h73d509c184646ef7E(i32 %a, i32 %b) unnamed_addr #1 {
start:
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %a, i32 %b)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_a46d947ecf1b055d4d64ce85f8fdf80e) #16
  unreachable
}

; _06_functions::calculate_area
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions14calculate_area17h4f650efaaa61cb12E(i32 %0, i32 %1) unnamed_addr #1 {
start:
  %_10 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [32 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %width = alloca [4 x i8], align 4
  %length = alloca [4 x i8], align 4
  store i32 %0, ptr %length, align 4
  store i32 %1, ptr %width, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_8, ptr align 4 %length)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_10, ptr align 4 %width)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_8, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_10, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h17c184b1ba60ed33E(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_ba7f45492dd62671365e7ae8a93c61a5, ptr align 8 %_7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_4)
  %4 = load i32, ptr %length, align 4
  %5 = load i32, ptr %width, align 4
  %6 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %4, i32 %5)
  %_12.0 = extractvalue { i32, i1 } %6, 0
  %_12.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_12.1, label %panic, label %bb5

bb5:                                              ; preds = %start
  ret i32 %_12.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_f0c451bc7d51e40082606c65e41ceded) #16
  unreachable
}

; _06_functions::create_full_name
; Function Attrs: uwtable
define internal void @_ZN13_06_functions16create_full_name17h59c6000770acd8e7E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 1 %2, i64 %3) unnamed_addr #1 {
start:
  %_10 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [32 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %res = alloca [24 x i8], align 8
  %last = alloca [16 x i8], align 8
  %first = alloca [16 x i8], align 8
  store ptr %0, ptr %first, align 8
  %4 = getelementptr inbounds i8, ptr %first, i64 8
  store i64 %1, ptr %4, align 8
  store ptr %2, ptr %last, align 8
  %5 = getelementptr inbounds i8, ptr %last, i64 8
  store i64 %3, ptr %5, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_8, ptr align 8 %first)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_10, ptr align 8 %last)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_8, i64 16, i1 false)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_10, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117ha68ff56c70acfa41E(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_a0434d70b15bcc9ff1a7b717be16ed72, ptr align 8 %_7)
; call alloc::fmt::format
  call void @_ZN5alloc3fmt6format17hd114ec5f7231d234E(ptr sret([24 x i8]) align 8 %res, ptr align 8 %_4)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %res, i64 24, i1 false)
  ret void
}

; _06_functions::check_grade
; Function Attrs: uwtable
define internal { ptr, i64 } @_ZN13_06_functions11check_grade17h4be00949ab8772e9E(i32 %score) unnamed_addr #1 {
start:
  %_0 = alloca [16 x i8], align 8
  %_2 = icmp sge i32 %score, 90
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_3 = icmp sge i32 %score, 80
  br i1 %_3, label %bb3, label %bb4

bb1:                                              ; preds = %start
  store ptr @alloc_e2ead6761956d440a2a6c3412b417ffa, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  br label %bb9

bb4:                                              ; preds = %bb2
  %_4 = icmp sge i32 %score, 70
  br i1 %_4, label %bb5, label %bb6

bb3:                                              ; preds = %bb2
  store ptr @alloc_d3bbdebcd7d668a59dc59a90afdc2fa1, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %1, align 8
  br label %bb9

bb6:                                              ; preds = %bb4
  %_5 = icmp sge i32 %score, 60
  br i1 %_5, label %bb7, label %bb8

bb5:                                              ; preds = %bb4
  store ptr @alloc_e57470275a219d8492d489e56910499e, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %2, align 8
  br label %bb9

bb8:                                              ; preds = %bb6
  store ptr @alloc_4b372b42a7e59c4e87186c5d2ddb750d, ptr %_0, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %3, align 8
  br label %bb9

bb7:                                              ; preds = %bb6
  store ptr @alloc_fba4efe8e4f7fab8265f1b3a352c9317, ptr %_0, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %4, align 8
  br label %bb9

bb9:                                              ; preds = %bb1, %bb3, %bb5, %bb7, %bb8
  %5 = load ptr, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = insertvalue { ptr, i64 } poison, ptr %5, 0
  %9 = insertvalue { ptr, i64 } %8, i64 %7, 1
  ret { ptr, i64 } %9
}

; _06_functions::count_to_n
; Function Attrs: uwtable
define internal void @_ZN13_06_functions10count_to_n17h5f0a0bd3443af4e0E(i32 %0) unnamed_addr #1 {
start:
  %_24 = alloca [48 x i8], align 8
  %_21 = alloca [16 x i8], align 8
  %_20 = alloca [16 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %i = alloca [4 x i8], align 4
  %_12 = alloca [8 x i8], align 4
  %iter = alloca [12 x i8], align 4
  %_10 = alloca [12 x i8], align 4
  %_9 = alloca [12 x i8], align 4
  %_7 = alloca [16 x i8], align 8
  %_6 = alloca [16 x i8], align 8
  %_3 = alloca [48 x i8], align 8
  %n = alloca [4 x i8], align 4
  store i32 %0, ptr %n, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_7, ptr align 4 %n)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_6, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_7, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_3, ptr align 8 @alloc_7af8315c33b07853f447458817adb541, ptr align 8 %_6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_3)
  %2 = load i32, ptr %n, align 4
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h406cd8834cc3213cE"(ptr sret([12 x i8]) align 4 %_10, i32 1, i32 %2)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hb2a0fd3d65d27a4eE"(ptr sret([12 x i8]) align 4 %_9, ptr align 4 %_10)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter, ptr align 4 %_9, i64 12, i1 false)
  br label %bb6

bb6:                                              ; preds = %bb9, %start
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %3 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hdd3750f80fdf231dE"(ptr align 4 %iter)
  %4 = extractvalue { i32, i32 } %3, 0
  %5 = extractvalue { i32, i32 } %3, 1
  store i32 %4, ptr %_12, align 4
  %6 = getelementptr inbounds i8, ptr %_12, i64 4
  store i32 %5, ptr %6, align 4
  %7 = load i32, ptr %_12, align 4
  %8 = getelementptr inbounds i8, ptr %_12, i64 4
  %9 = load i32, ptr %8, align 4
  %_14 = zext i32 %7 to i64
  %10 = trunc nuw i64 %_14 to i1
  br i1 %10, label %bb9, label %bb10

bb9:                                              ; preds = %bb6
  %11 = getelementptr inbounds i8, ptr %_12, i64 4
  %12 = load i32, ptr %11, align 4
  store i32 %12, ptr %i, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_21, ptr align 4 %i)
  %13 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_20, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %13, ptr align 8 %_21, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_17, ptr align 8 @alloc_498dd48fed0743022e84e831ad90c0fe, ptr align 8 %_20)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_17)
  br label %bb6

bb10:                                             ; preds = %bb6
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_24, ptr align 8 @alloc_0d63fa60b0e3a914d02f0426749bd353)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_24)
  ret void

bb8:                                              ; No predecessors!
  unreachable
}

; _06_functions::multiply
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions8multiply17h82615768494738c7E(i32 %a, i32 %b) unnamed_addr #1 {
start:
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %a, i32 %b)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_9be85685af010fcc809b95c5f6b9c444) #16
  unreachable
}

; _06_functions::calculate_rectangle_area
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions24calculate_rectangle_area17h26e12de91f870420E(i32 %0, i32 %1) unnamed_addr #1 {
start:
  %_10 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [32 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %width = alloca [4 x i8], align 4
  %length = alloca [4 x i8], align 4
  store i32 %0, ptr %length, align 4
  store i32 %1, ptr %width, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_8, ptr align 4 %length)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_10, ptr align 4 %width)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_8, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_10, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h17c184b1ba60ed33E(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_9246bf7f4e945f8fc2b0e945666d28d9, ptr align 8 %_7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_4)
  %4 = load i32, ptr %length, align 4
  %5 = load i32, ptr %width, align 4
; call _06_functions::multiply
  %_0 = call i32 @_ZN13_06_functions8multiply17h82615768494738c7E(i32 %4, i32 %5)
  ret i32 %_0
}

; _06_functions::process_order
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions13process_order17h7b16850bdc0921d7E(ptr align 1 %0, i64 %1, i32 %2, i32 %3) unnamed_addr #1 {
start:
  %_34 = alloca [16 x i8], align 8
  %_33 = alloca [16 x i8], align 8
  %_30 = alloca [48 x i8], align 8
  %total = alloca [4 x i8], align 4
  %_26 = alloca [16 x i8], align 8
  %_25 = alloca [16 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_19 = alloca [16 x i8], align 8
  %_18 = alloca [16 x i8], align 8
  %_15 = alloca [48 x i8], align 8
  %_12 = alloca [16 x i8], align 8
  %_11 = alloca [16 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %price = alloca [4 x i8], align 4
  %quantity = alloca [4 x i8], align 4
  %item = alloca [16 x i8], align 8
  store ptr %0, ptr %item, align 8
  %4 = getelementptr inbounds i8, ptr %item, i64 8
  store i64 %1, ptr %4, align 8
  store i32 %2, ptr %quantity, align 4
  store i32 %3, ptr %price, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_15521d023f0435c64581ef7338a86e7b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22f39df31f1c3784E(ptr sret([16 x i8]) align 8 %_12, ptr align 8 %item)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_11, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_12, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_97c0c5c8cddcc587a067d2866cb74391, ptr align 8 %_11)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_19, ptr align 4 %quantity)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_18, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_19, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_15, ptr align 8 @alloc_b4ae93f91e238f75c44dc51bb7f4bac4, ptr align 8 %_18)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_15)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_26, ptr align 4 %price)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_25, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_26, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_22, ptr align 8 @alloc_5f54a043a0ae8a622697b92e037019bb, ptr align 8 %_25)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_22)
  %8 = load i32, ptr %quantity, align 4
  %9 = load i32, ptr %price, align 4
; call _06_functions::multiply
  %10 = call i32 @_ZN13_06_functions8multiply17h82615768494738c7E(i32 %8, i32 %9)
  store i32 %10, ptr %total, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_34, ptr align 4 %total)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_33, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_34, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_30, ptr align 8 @alloc_d3e9b451ca27d86b4691fd1eafc58fee, ptr align 8 %_33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_30)
  %_0 = load i32, ptr %total, align 4
  ret i32 %_0
}

; _06_functions::is_even
; Function Attrs: uwtable
define internal zeroext i1 @_ZN13_06_functions7is_even17he21757918335f842E(i32 %number) unnamed_addr #1 {
start:
  %_5 = icmp eq i32 %number, -2147483648
  %_6 = and i1 false, %_5
  br i1 %_6, label %panic, label %bb2

bb2:                                              ; preds = %start
  %_2 = srem i32 %number, 2
  %_0 = icmp eq i32 %_2, 0
  ret i1 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_75657af196a4d1d8fe3d560ef93da88a) #16
  unreachable
}

; _06_functions::calculate_stats
; Function Attrs: uwtable
define internal i32 @_ZN13_06_functions15calculate_stats17hbf304b161766b69aE(i32 %0, i32 %1, i32 %2) unnamed_addr #1 {
start:
  %_45 = alloca [16 x i8], align 8
  %_44 = alloca [16 x i8], align 8
  %_41 = alloca [48 x i8], align 8
  %_38 = alloca [16 x i8], align 8
  %_37 = alloca [16 x i8], align 8
  %_34 = alloca [48 x i8], align 8
  %_31 = alloca [16 x i8], align 8
  %_30 = alloca [16 x i8], align 8
  %_27 = alloca [48 x i8], align 8
  %_24 = alloca [16 x i8], align 8
  %_22 = alloca [16 x i8], align 8
  %_20 = alloca [16 x i8], align 8
  %_19 = alloca [48 x i8], align 8
  %_16 = alloca [48 x i8], align 8
  %max_val = alloca [4 x i8], align 4
  %average = alloca [4 x i8], align 4
  %sum = alloca [4 x i8], align 4
  %_0 = alloca [4 x i8], align 4
  %num3 = alloca [4 x i8], align 4
  %num2 = alloca [4 x i8], align 4
  %num1 = alloca [4 x i8], align 4
  store i32 %0, ptr %num1, align 4
  store i32 %1, ptr %num2, align 4
  store i32 %2, ptr %num3, align 4
  %3 = load i32, ptr %num1, align 4
  %4 = load i32, ptr %num2, align 4
  %5 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %3, i32 %4)
  %_6.0 = extractvalue { i32, i1 } %5, 0
  %_6.1 = extractvalue { i32, i1 } %5, 1
  br i1 %_6.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  %6 = load i32, ptr %num3, align 4
  %7 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_6.0, i32 %6)
  %_7.0 = extractvalue { i32, i1 } %7, 0
  %_7.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_7.1, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_055cccb03818a0be630c75fbc16d5190) #16
  unreachable

bb2:                                              ; preds = %bb1
  store i32 %_7.0, ptr %sum, align 4
  %8 = load i32, ptr %sum, align 4
  store i32 %8, ptr %_0, align 4
  %9 = load i32, ptr %_0, align 4
  %_11 = icmp eq i32 %9, -2147483648
  %_12 = and i1 false, %_11
  br i1 %_12, label %panic2, label %bb4

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_055cccb03818a0be630c75fbc16d5190) #16
  unreachable

bb4:                                              ; preds = %bb2
  %10 = load i32, ptr %_0, align 4
  %11 = sdiv i32 %10, 3
  store i32 %11, ptr %average, align 4
  %12 = load i32, ptr %num1, align 4
  %13 = load i32, ptr %num2, align 4
; call core::cmp::Ord::max
  %_14 = call i32 @_ZN4core3cmp3Ord3max17h8823a45b7a12162bE(i32 %12, i32 %13)
  %14 = load i32, ptr %num3, align 4
; call core::cmp::Ord::max
  %15 = call i32 @_ZN4core3cmp3Ord3max17h8823a45b7a12162bE(i32 %_14, i32 %14)
  store i32 %15, ptr %max_val, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_20, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_22, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_24, ptr align 4 %num3)
  %16 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_19, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %16, ptr align 8 %_20, i64 16, i1 false)
  %17 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_19, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %17, ptr align 8 %_22, i64 16, i1 false)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_19, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_24, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h52e78590a3b3e311E(ptr sret([48 x i8]) align 8 %_16, ptr align 8 @alloc_705789b1971018fff4a7b5cb70e0a81a, ptr align 8 %_19)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_16)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_31, ptr align 4 %sum)
  %19 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_30, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %19, ptr align 8 %_31, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_27, ptr align 8 @alloc_fae7b78f3403b48b2870d7d78fcc4226, ptr align 8 %_30)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_27)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_38, ptr align 4 %average)
  %20 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_37, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %20, ptr align 8 %_38, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_34, ptr align 8 @alloc_e93491e3b40beb615f607f92c37da4a2, ptr align 8 %_37)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_34)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_45, ptr align 4 %max_val)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_44, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_45, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_41, ptr align 8 @alloc_3fed9fdde7e6ef1b81519ccb53d625ea, ptr align 8 %_44)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_41)
  %22 = load i32, ptr %sum, align 4
  store i32 %22, ptr %_0, align 4
  %23 = load i32, ptr %_0, align 4
  ret i32 %23

panic2:                                           ; preds = %bb2
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_53b4740c186af9da93046ff6029548a4) #16
  unreachable
}

; _06_functions::countdown_function
; Function Attrs: uwtable
define internal void @_ZN13_06_functions18countdown_function17h11197c0a2bea966bE(i32 %0) unnamed_addr #1 {
start:
  %_14 = alloca [48 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %n = alloca [4 x i8], align 4
  store i32 %0, ptr %n, align 4
  %1 = load i32, ptr %n, align 4
  %_2 = icmp sgt i32 %1, 0
  br i1 %_2, label %bb1, label %bb6

bb6:                                              ; preds = %start
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h84afa8e5040d7d90E(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_e6b3cba32edd29e8a7e82f55b972199e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
  br label %bb8

bb1:                                              ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hcc1890134543b055E(ptr sret([16 x i8]) align 8 %_8, ptr align 4 %n)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_8, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0aecbf6bd4c0e0e6E(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_10015caba44e6f8e925ee543e933f28f, ptr align 8 %_7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_4)
  %3 = load i32, ptr %n, align 4
  %4 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %3, i32 1)
  %_12.0 = extractvalue { i32, i1 } %4, 0
  %_12.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_12.1, label %panic, label %bb5

bb8:                                              ; preds = %bb5, %bb6
  ret void

bb5:                                              ; preds = %bb1
; call _06_functions::countdown_function
  call void @_ZN13_06_functions18countdown_function17h11197c0a2bea966bE(i32 %_12.0)
  br label %bb8

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_ea8ea86aee137f89f0b14aab690e99d8) #16
  unreachable
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #1

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #5

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #5

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #7

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #9

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #10

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #5

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #1

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; alloc::fmt::format::format_inner
; Function Attrs: uwtable
declare void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8, ptr align 8) unnamed_addr #1

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #11

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #12

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #13

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #14

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #1

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_rem_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #8

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hd8ea22520bc7f9f0E(ptr @_ZN13_06_functions4main17h0a74e8e48b324a1fE, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #10 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #11 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind }
attributes #16 = { noreturn }
attributes #17 = { cold noreturn nounwind }
attributes #18 = { noreturn nounwind }
attributes #19 = { cold }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 6125740057019185}
