; ModuleID = '09_input_output.22baa1f8c51b83db-cgu.0'
source_filename = "09_input_output.22baa1f8c51b83db-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
%"core::fmt::rt::Placeholder" = type { %"core::fmt::rt::Count", %"core::fmt::rt::Count", i64, i32, [1 x i32] }
%"core::fmt::rt::Count" = type { i16, [7 x i16] }

@alloc_a500d906b91607583596fa15e63c2ada = private unnamed_addr constant [40 x i8] c"internal error: entered unreachable code", align 1
@alloc_3995eb8defd5c4a2a9a5b6a5b08bb2c5 = private unnamed_addr constant [90 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\std\\src\\io\\error\\repr_bitpacked.rs", align 1
@alloc_684dcb29e286381a0922901efff1b793 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3995eb8defd5c4a2a9a5b6a5b08bb2c5, [16 x i8] c"Z\00\00\00\00\00\00\00\22\01\00\00\0D\00\00\00" }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0edd086e36b86a51E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hc583c25b330be4bfE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hc583c25b330be4bfE" }>, align 8
@anon.83db9855118e6db4b4ca651d0b944742.0 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@anon.83db9855118e6db4b4ca651d0b944742.1 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@vtable.1 = private unnamed_addr constant <{ ptr, [16 x i8], ptr }> <{ ptr @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E", [16 x i8] c"\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN58_$LT$std..io..error..Error$u20$as$u20$core..fmt..Debug$GT$3fmt17h3543db37740775b4E" }>, align 8
@alloc_00ae4b301f7fab8ac9617c03fcbd7274 = private unnamed_addr constant [43 x i8] c"called `Result::unwrap()` on an `Err` value", align 1
@alloc_e57e7880016f8461cfa078377c265643 = private unnamed_addr constant [33 x i8] c"=== Input/Output Demo (Rust) ===\0A", align 1
@alloc_c3e061720ebbdacd35aec2f418194dc3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e57e7880016f8461cfa078377c265643, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_73fbe3c25d563681bccd3afd89e8bd02 = private unnamed_addr constant [24 x i8] c"Basic print statements:\0A", align 1
@alloc_805ff2639b6981ce8ad51d0c1645c415 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_73fbe3c25d563681bccd3afd89e8bd02, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_a68e306779a6bc58513a59c41bd89d84 = private unnamed_addr constant [14 x i8] c"Hello, World!\0A", align 1
@alloc_73c3f88632bc92f9edb5547b1f3ddcf7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a68e306779a6bc58513a59c41bd89d84, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_04428ab76e0ec110c11e0d4a1f8bee40 = private unnamed_addr constant [25 x i8] c"This is a simple message\0A", align 1
@alloc_81ece93ea33556630ac5b0794a2e3d7b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_04428ab76e0ec110c11e0d4a1f8bee40, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_59bc9cf74a705cf64426454471b5a56e = private unnamed_addr constant [28 x i8] c"Numbers can be printed: 123\0A", align 1
@alloc_8cc1c97804ef8846064004743e2ef597 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_59bc9cf74a705cf64426454471b5a56e, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_b468ef00fa5916b1335583d25c8252ad = private unnamed_addr constant [5 x i8] c"Ahmed", align 1
@alloc_01d4d708d716e3b86807c9565ed214c1 = private unnamed_addr constant [22 x i8] c"Print with variables:\0A", align 1
@alloc_485fa2c17771af57e2a313186610a1af = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_01d4d708d716e3b86807c9565ed214c1, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a = private unnamed_addr constant [6 x i8] c"Name: ", align 1
@alloc_cd99b202573048149e4ac0d7df184409 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aa11b714384947dbd3caef261ef0dc2e = private unnamed_addr constant [5 x i8] c"Age: ", align 1
@alloc_c8d066cadf1ba35bf631428b9f61b62c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aa11b714384947dbd3caef261ef0dc2e, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5fb5ea3f1a2b2f8bfd25c3cbe1f2e205 = private unnamed_addr constant [8 x i8] c"Salary: ", align 1
@alloc_828475e8994c9a2156e0be0dce462ed4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5fb5ea3f1a2b2f8bfd25c3cbe1f2e205, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9e1cb957a623ee86f42463926a127bb7 = private unnamed_addr constant [8 x i8] c"Active: ", align 1
@alloc_c5ed655fc10e235cc96c31418ed2f1c1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9e1cb957a623ee86f42463926a127bb7, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_30f7f96145362f213266cab8b55a1352 = private unnamed_addr constant [24 x i8] c"Print with expressions:\0A", align 1
@alloc_4ae04cd9dc27ff4823c35eeaadfd85d9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_30f7f96145362f213266cab8b55a1352, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_a76930d30bf61abe0cd5eb50a74786f2 = private unnamed_addr constant [8 x i8] c"a + b = ", align 1
@alloc_67b556d4697490fde72675e6c5b1cf2a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a76930d30bf61abe0cd5eb50a74786f2, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_66bdded3e81e355683ea6461ce7efb66 = private unnamed_addr constant [18 x i8] c"09_input_output.rs", align 1
@alloc_933ff4ab8fb15dec9fd11b245e4cef35 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\22\00\00\00\1C\00\00\00" }>, align 8
@alloc_39f01263a22767f57019cb44d399032d = private unnamed_addr constant [8 x i8] c"a * b = ", align 1
@alloc_8fd1e52dbef5c4d413d27adb1634112a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_39f01263a22767f57019cb44d399032d, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3dd01e17664da0064953556811c2c1ac = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00#\00\00\00\1C\00\00\00" }>, align 8
@alloc_8099b46bb1ac44b8f87b553c36a55438 = private unnamed_addr constant [9 x i8] c"a > b is ", align 1
@alloc_7b6728db17d1974cc3d35482a11fa539 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8099b46bb1ac44b8f87b553c36a55438, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8a3ff4f0306cccf00c85887c84f94191 = private unnamed_addr constant [4 x i8] c"Sara", align 1
@alloc_da176e364e2c37a043e0b0bf98f57c2b = private unnamed_addr constant [4 x i8] c"Omar", align 1
@alloc_a118b2b99663e794e1f4a92820d83446 = private unnamed_addr constant [3 x i8] c"Ali", align 1
@alloc_9fbee326b114f3ce46d34640615ddc03 = private unnamed_addr constant [14 x i8] c"Print arrays:\0A", align 1
@alloc_3d5ffcf2d85f12c5f29f4bb6f05da63d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9fbee326b114f3ce46d34640615ddc03, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_9c19a112250a05d379606088044af67a = private unnamed_addr constant [14 x i8] c"First number: ", align 1
@alloc_ede98dbfb88e192dbe6887052b52c4b0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9c19a112250a05d379606088044af67a, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e02106825024b8e979aeb917ddb9b1b1 = private unnamed_addr constant [13 x i8] c"Last number: ", align 1
@alloc_1e01683ddb04f11db4c3485d7ead6feb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e02106825024b8e979aeb917ddb9b1b1, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4ab72a1c9a00f1eb3770a1c2c8a6e7e2 = private unnamed_addr constant [12 x i8] c"First name: ", align 1
@alloc_2bc058ba28e18b154f47b76e97a52f54 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4ab72a1c9a00f1eb3770a1c2c8a6e7e2, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c16cf69a66f203fa758c809335295a4c = private unnamed_addr constant [13 x i8] c"Second name: ", align 1
@alloc_9766be8e007a1167a20c3d088b19674d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c16cf69a66f203fa758c809335295a4c, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_749c8ea9fc29d06f08d94ce8a73822d1 = private unnamed_addr constant [16 x i8] c"Print in loops:\0A", align 1
@alloc_78aa2fdf2ef0958f5275162238d836c7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_749c8ea9fc29d06f08d94ce8a73822d1, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_df92c3cf288e08ab302f5a22d0d1b486 = private unnamed_addr constant [23 x i8] c"Print with conditions:\0A", align 1
@alloc_3a020404d831ddb266f8ee21ad441c83 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_df92c3cf288e08ab302f5a22d0d1b486, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_c01f2d93bf6edc13d7a7e33e81f8eb3f = private unnamed_addr constant [17 x i8] c"Need improvement\0A", align 1
@alloc_5784c00ff35f9f0cce9c19d935203fb1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c01f2d93bf6edc13d7a7e33e81f8eb3f, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_004c4a494c050dfbb9312040bdf2d897 = private unnamed_addr constant [12 x i8] c"Good score!\0A", align 1
@alloc_97ff94fc2a2ef58145964554ece39dc5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_004c4a494c050dfbb9312040bdf2d897, [8 x i8] c"\0C\00\00\00\00\00\00\00" }>, align 8
@alloc_6080e5b8c46590342cf60a73ec023638 = private unnamed_addr constant [17 x i8] c"Excellent score!\0A", align 1
@alloc_a7c9335b38e7effff7e3d8a2b29a68d6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6080e5b8c46590342cf60a73ec023638, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_f1ed3fd375c98ccbc8c4e3ce12579b51 = private unnamed_addr constant [18 x i8] c"Formatted output:\0A", align 1
@alloc_4fe744e4e492b99653634ac8d0da54ca = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f1ed3fd375c98ccbc8c4e3ce12579b51, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_359b7dc4bc3bfd93aae4f476e001fa3b = private unnamed_addr constant [6 x i8] c"Laptop", align 1
@alloc_64440f85962ece9aa0ede3693201d86d = private unnamed_addr constant [22 x i8] c"=== ORDER SUMMARY ===\0A", align 1
@alloc_a8458c4b95a0fce5f585d40ae5719977 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_64440f85962ece9aa0ede3693201d86d, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_b1f2c95baf3fd0056321c45a348389c5 = private unnamed_addr constant [9 x i8] c"Product: ", align 1
@alloc_5a4976cd5630db43316aecd3ccde6279 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b1f2c95baf3fd0056321c45a348389c5, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0ef9a0d97ba47b7981d0dc11f1b7d1f5 = private unnamed_addr constant [10 x i8] c"Quantity: ", align 1
@alloc_917729db758871874fa4dcb3ec4c460b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0ef9a0d97ba47b7981d0dc11f1b7d1f5, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_078bcda68dbf193e409f5e5123b79d0e = private unnamed_addr constant [13 x i8] c"Unit Price: $", align 1
@alloc_486ee92fa4c34c397a15696871c71440 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_078bcda68dbf193e409f5e5123b79d0e, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e7520c35c039ef752af9a8862484b01c = private unnamed_addr constant [8 x i8] c"Total: $", align 1
@alloc_fd1d874d5e4f19c1a2253bab3e592ab8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e7520c35c039ef752af9a8862484b01c, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4ece63c9d4a6197460fdec44f3d3a0b7 = private unnamed_addr constant [19 x i8] c"==================\0A", align 1
@alloc_4e2bd1292c06ad40c42e950226c30848 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4ece63c9d4a6197460fdec44f3d3a0b7, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_df3e988d5f9daba39b3b5d9554ca26c3 = private unnamed_addr constant [24 x i8] c"Print function results:\0A", align 1
@alloc_765d87e7ac92e9e0b0879f8312a94ebf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_df3e988d5f9daba39b3b5d9554ca26c3, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_74fcb93fdd8693191050ce8284725618 = private unnamed_addr constant [14 x i8] c"Room area is: ", align 1
@alloc_dd74a8c530f7c74691d7235b3574559d = private unnamed_addr constant [15 x i8] c" square meters\0A", align 1
@alloc_055f882ec0a68cc9edbe8416b2bfff19 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_74fcb93fdd8693191050ce8284725618, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_dd74a8c530f7c74691d7235b3574559d, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_27101c824e95b1093f4c0f423f9aa8c7 = private unnamed_addr constant [19 x i8] c"Multi-line output:\0A", align 1
@alloc_3a207195f10926972cbcd1b972a06645 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_27101c824e95b1093f4c0f423f9aa8c7, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_a6c012bcd33d9952e4899ab3ba8e606c = private unnamed_addr constant [21 x i8] c"Line 1: Introduction\0A", align 1
@alloc_082464557d449b38b557963fea9ba625 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a6c012bcd33d9952e4899ab3ba8e606c, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_bee7df91baa9bc3c8b9d27af1d14cbcc = private unnamed_addr constant [21 x i8] c"Line 2: Main content\0A", align 1
@alloc_903bb3d7b5aca6273efac4a74b85c029 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_bee7df91baa9bc3c8b9d27af1d14cbcc, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_1bee0e2f4895304d09c0631e5fd44844 = private unnamed_addr constant [19 x i8] c"Line 3: Conclusion\0A", align 1
@alloc_8a64ab18a349b89c21e50949abf512d1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1bee0e2f4895304d09c0631e5fd44844, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_12d75d101fb0ae1c4d2a5dda8c55dbd9 = private unnamed_addr constant [4 x i8] c"---\0A", align 1
@alloc_62864721e615d98884de57efc7309d65 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_12d75d101fb0ae1c4d2a5dda8c55dbd9, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_fe1cc5dd3f4dbfab940876aaf35c0eeb = private unnamed_addr constant [31 x i8] c"Print with special formatting:\0A", align 1
@alloc_8e0012e88499f35bbf28d5fd60e27ba3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fe1cc5dd3f4dbfab940876aaf35c0eeb, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_49a2b2285850767ee64cfec74ed2f605 = private unnamed_addr constant [17 x i8] c"* Bullet point 1\0A", align 1
@alloc_6bc61fc06a2fab620a781dc864de6052 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a2b2285850767ee64cfec74ed2f605, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_ff90e7ac098bfd174801546c49c6cad9 = private unnamed_addr constant [17 x i8] c"* Bullet point 2\0A", align 1
@alloc_586bf349966591063ad25b556f0670e6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ff90e7ac098bfd174801546c49c6cad9, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_b41acea84b022b7c35bccfa33caff678 = private unnamed_addr constant [17 x i8] c"* Bullet point 3\0A", align 1
@alloc_388802831c9d5ff708fff8aef763d8a3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b41acea84b022b7c35bccfa33caff678, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_d1a858f616431950278f80a6076df43e = private unnamed_addr constant [19 x i8] c"1. Numbered item 1\0A", align 1
@alloc_7dfdc93c86623f2d0997a58cf9538408 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d1a858f616431950278f80a6076df43e, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_b3c2fdb727fbd2539319c0dcd0b6cd6b = private unnamed_addr constant [19 x i8] c"2. Numbered item 2\0A", align 1
@alloc_64b9705d0826e8969a0bdf9e1c5597d9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b3c2fdb727fbd2539319c0dcd0b6cd6b, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_561b3f8421c71321694e518e797ee319 = private unnamed_addr constant [19 x i8] c"3. Numbered item 3\0A", align 1
@alloc_1aecdb82084e035877a62a50d2bf474f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_561b3f8421c71321694e518e797ee319, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_d556abb4817793db438017e3207957e5 = private unnamed_addr constant [26 x i8] c"Error message simulation:\0A", align 1
@alloc_23713c3ab34fba4056a3015b130ec045 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d556abb4817793db438017e3207957e5, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_36595d6b9b28825e4c9229f813f36c56 = private unnamed_addr constant [3 x i8] c"404", align 1
@alloc_46701e9604e3f3dcda75663c1d13ead1 = private unnamed_addr constant [14 x i8] c"File not found", align 1
@alloc_9b228f3c615034d41bd7e362987cd2f1 = private unnamed_addr constant [6 x i8] c"ERROR ", align 1
@alloc_556e4180596b5b612bb6ed6c0cbb55e1 = private unnamed_addr constant [2 x i8] c": ", align 1
@alloc_0eeef3e7ac8e296725bc8c49acedc3c5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9b228f3c615034d41bd7e362987cd2f1, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_556e4180596b5b612bb6ed6c0cbb55e1, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c7c0854def21ae574df89492f6deebbd = private unnamed_addr constant [41 x i8] c"Please check the file path and try again\0A", align 1
@alloc_c283654c67423f8d2af52893954fe3db = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c7c0854def21ae574df89492f6deebbd, [8 x i8] c")\00\00\00\00\00\00\00" }>, align 8
@alloc_f0984c23934764fa5c5da667f837f71d = private unnamed_addr constant [17 x i8] c"Status messages:\0A", align 1
@alloc_d920a6dd3added7a13211fc30fa90b58 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f0984c23934764fa5c5da667f837f71d, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_5c380b986bd4ecf6335295020fd119b0 = private unnamed_addr constant [26 x i8] c"[INFO] System starting...\0A", align 1
@alloc_44062cd7068d6796ca36d315fc5e2d2f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5c380b986bd4ecf6335295020fd119b0, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_1b3c7b790225353a9d30c0bc904dd272 = private unnamed_addr constant [32 x i8] c"[INFO] Loading configuration...\0A", align 1
@alloc_13fd9d667a364a7eb2f52d4fca397dbe = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1b3c7b790225353a9d30c0bc904dd272, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_4ad3fcb6adae8c0f761cf0754b12608f = private unnamed_addr constant [24 x i8] c"[SUCCESS] System ready!\0A", align 1
@alloc_d062f17ca78d9e7c3c14097c56f9eb55 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4ad3fcb6adae8c0f761cf0754b12608f, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_8a8bb1916f563bfca2b7bbc591593ac9 = private unnamed_addr constant [25 x i8] c"[WARNING] Low disk space\0A", align 1
@alloc_40ec521de9aa644eae2e0a4b8c1d1c48 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8a8bb1916f563bfca2b7bbc591593ac9, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_eecb8a5981c2d8c60b4fa1b45fd5755f = private unnamed_addr constant [19 x i8] c"Table-like output:\0A", align 1
@alloc_dd8903d6cbfd70b78cdf545df4e702d1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_eecb8a5981c2d8c60b4fa1b45fd5755f, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_a73d0e35045fd787c81346701178a687 = private unnamed_addr constant [3 x i8] c" | ", align 1
@alloc_aa2e0d3624ba64e906307f087624c174 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_a73d0e35045fd787c81346701178a687, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_a73d0e35045fd787c81346701178a687, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b3cc70575bc30a281a317a0b656d79ab = private unnamed_addr constant [4 x i8] c"Name", align 1
@alloc_e5ce79e13152ffbb6c0fd479fc5cb7e1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b3cc70575bc30a281a317a0b656d79ab, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_b5beac9021e4c787f4ddc4d3f1016b81 = private unnamed_addr constant [3 x i8] c"Age", align 1
@alloc_6c096ef680caa061837220a81c45c3c2 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b5beac9021e4c787f4ddc4d3f1016b81, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_8824fcbba54f784c1c85f47846b8f913 = private unnamed_addr constant [4 x i8] c"City", align 1
@alloc_87eb6937e9b0b11eb3cea5f19ff8bfff = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8824fcbba54f784c1c85f47846b8f913, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_4383d3c3d1130ba649b844e675a987d2 = private unnamed_addr constant [3 x i8] c"-+-", align 1
@alloc_a985d56ac986e481bfd643faa099cd3f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_4383d3c3d1130ba649b844e675a987d2, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_4383d3c3d1130ba649b844e675a987d2, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_eab5d04767146d7d9b93b60d28ef530a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer }>, align 8
@alloc_5bdf718b424cb792ca3cbf634962adb7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b468ef00fa5916b1335583d25c8252ad, [8 x i8] c"\05\00\00\00\00\00\00\00" }>, align 8
@alloc_ab479dd233ae0d27e03a3201d55bd7c4 = private unnamed_addr constant [2 x i8] c"25", align 1
@alloc_0cbf628e89013526399bbd4e8b99526e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ab479dd233ae0d27e03a3201d55bd7c4, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_67eb4974a8310b72f6f5dca9d9c7655a = private unnamed_addr constant [5 x i8] c"Cairo", align 1
@alloc_9142414879623d4e4d7aa9d73a44bd16 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_67eb4974a8310b72f6f5dca9d9c7655a, [8 x i8] c"\05\00\00\00\00\00\00\00" }>, align 8
@alloc_7e186732ac075cfc40a0cd21f3f42305 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8a3ff4f0306cccf00c85887c84f94191, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_c3fbebdb0220fda2a43de759023090c3 = private unnamed_addr constant [2 x i8] c"30", align 1
@alloc_2ddcc46b5a700442bf5d4ab2d617d9bb = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c3fbebdb0220fda2a43de759023090c3, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_6641fa9b931d9297b00a346a0030367b = private unnamed_addr constant [10 x i8] c"Alexandria", align 1
@alloc_af52064429eeca105700498c62b17f2d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6641fa9b931d9297b00a346a0030367b, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_a45c339e1376d9d8acdc5100b100f554 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_da176e364e2c37a043e0b0bf98f57c2b, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_8ca48e4dc75c7ea96b4d377f7833d004 = private unnamed_addr constant [2 x i8] c"28", align 1
@alloc_ed0db35e8673a7bcf95eed760e24378f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8ca48e4dc75c7ea96b4d377f7833d004, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_1676c93f3e8eaa269e928cdb5900fa20 = private unnamed_addr constant [4 x i8] c"Giza", align 1
@alloc_825c34bdd7b9b2ff5db60e5a2656d138 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1676c93f3e8eaa269e928cdb5900fa20, [8 x i8] c"\04\00\00\00\00\00\00\00" }>, align 8
@alloc_b707279ada66f9e35f179728118aacf7 = private unnamed_addr constant [32 x i8] c"Progress indication simulation:\0A", align 1
@alloc_4e4b3e0b123465db16c5cfe680767665 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b707279ada66f9e35f179728118aacf7, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_f7f31f9a70c7d8209e0e73e35718eea7 = private unnamed_addr constant [10 x i8] c"Complete!\0A", align 1
@alloc_6435c432427367cb5f3a0a820cf4849a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f7f31f9a70c7d8209e0e73e35718eea7, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_82c755ff765c0fdef396fc6cb81901f0 = private unnamed_addr constant [25 x i8] c"Debug output simulation:\0A", align 1
@alloc_c3a212f4a106eb63eaae2d9337b7ccdc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_82c755ff765c0fdef396fc6cb81901f0, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_0bde02fab3cfc6d461f0601471c09499 = private unnamed_addr constant [12 x i8] c"[DEBUG] x = ", align 1
@alloc_731b0cccf2ee2689857c103cf868dc19 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0bde02fab3cfc6d461f0601471c09499, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6e649f37312cab57b8136fd870edefbe = private unnamed_addr constant [12 x i8] c"[DEBUG] y = ", align 1
@alloc_e6f6d49181b11a1ce46abdf9c3e8176d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6e649f37312cab57b8136fd870edefbe, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_93c4c6f26d8c260e3edcb53dd359aa1d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\99\00\00\00\12\00\00\00" }>, align 8
@alloc_228318def25462369397cedb202cc3da = private unnamed_addr constant [16 x i8] c"[DEBUG] x + y = ", align 1
@alloc_dcef080d2dece423b893c7f7420e1e0e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_228318def25462369397cedb202cc3da, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d99957eabdc826d250bdf881153df082 = private unnamed_addr constant [28 x i8] c"[DEBUG] Operation completed\0A", align 1
@alloc_42fd79be5b99df107783671694aa83fb = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d99957eabdc826d250bdf881153df082, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_405fd093191e5c43531ae7eddff896d6 = private unnamed_addr constant [25 x i8] c"Menu display simulation:\0A", align 1
@alloc_d78a203f293e89e0225d313f148c0473 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_405fd093191e5c43531ae7eddff896d6, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_6ab6904c111d56357baa848befef82b8 = private unnamed_addr constant [18 x i8] c"=== MAIN MENU ===\0A", align 1
@alloc_95dea72bf2093ed094e0c292ab361599 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6ab6904c111d56357baa848befef82b8, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_c12042d47df068d437745461be25bc7e = private unnamed_addr constant [16 x i8] c"1. View Profile\0A", align 1
@alloc_8e12fef1d9e53c91a5b67b7aaf239a6c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c12042d47df068d437745461be25bc7e, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_a4febbcde645a7c5f1290b5fb86c62e8 = private unnamed_addr constant [17 x i8] c"2. Edit Settings\0A", align 1
@alloc_ab91f66a0cc4676a20d2f8ab0e173b41 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a4febbcde645a7c5f1290b5fb86c62e8, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_0d8b74e1c795a56ca411195c2c865dbc = private unnamed_addr constant [16 x i8] c"3. View Reports\0A", align 1
@alloc_89b5efa7d5670ac10081973608ca27ce = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0d8b74e1c795a56ca411195c2c865dbc, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_361d2e29977764df3ff714d42b579501 = private unnamed_addr constant [8 x i8] c"4. Exit\0A", align 1
@alloc_0bca21b0b160e31124deecde4f7a6785 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_361d2e29977764df3ff714d42b579501, [8 x i8] c"\08\00\00\00\00\00\00\00" }>, align 8
@alloc_67d41ad97daf5e47cd76376fa56b25be = private unnamed_addr constant [14 x i8] c"Data display:\0A", align 1
@alloc_3468b979ca58c790a6b8c481647f5d15 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_67d41ad97daf5e47cd76376fa56b25be, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_9ac483a2dbb643edcf3d4ff37d31b3e4 = private unnamed_addr constant [5 x i8] c"user1", align 1
@alloc_30e3e1d2fc714fecf2e248d9fa5fbc01 = private unnamed_addr constant [5 x i8] c"guest", align 1
@alloc_f876b28ff895ac67fe3c6bfc02a403c9 = private unnamed_addr constant [5 x i8] c"admin", align 1
@alloc_9be7dccb4d3cbf2cbdf38699f1535749 = private unnamed_addr constant [11 x i8] c"User Data:\0A", align 1
@alloc_fa0f75cc9638dd86f443c61bd4fd2b5b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9be7dccb4d3cbf2cbdf38699f1535749, [8 x i8] c"\0B\00\00\00\00\00\00\00" }>, align 8
@alloc_80ad137f70def252ad73dea1b9957742 = private unnamed_addr constant [16 x i8] c"Summary output:\0A", align 1
@alloc_f2994e6355023507b001b4fafe586514 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_80ad137f70def252ad73dea1b9957742, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_06a0f119ba043361ab73f0df12ec7d01 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\B6\00\00\00\1A\00\00\00" }>, align 8
@alloc_4350d11607b11d82e04c81d7de96e203 = private unnamed_addr constant [24 x i8] c"=== USER STATISTICS ===\0A", align 1
@alloc_3cbe2c8695a0872b4199390575289d0c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4350d11607b11d82e04c81d7de96e203, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_b825824214052fe52699e7f67e500abe = private unnamed_addr constant [13 x i8] c"Total Users: ", align 1
@alloc_5643e2aeb8e73a0f552bcc2267a2aaa0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b825824214052fe52699e7f67e500abe, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8f2911f3dbdb6dbb2d1f1ae5d59bce31 = private unnamed_addr constant [14 x i8] c"Active Users: ", align 1
@alloc_be05a9dc80da435fb2f8bf2c61526740 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8f2911f3dbdb6dbb2d1f1ae5d59bce31, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b8ba03a6f149d674c3467db4b7a80eb1 = private unnamed_addr constant [16 x i8] c"Inactive Users: ", align 1
@alloc_d825ff3c095e89b0caed0068dca1e154 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b8ba03a6f149d674c3467db4b7a80eb1, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f30e4e710d2959c9acbd1a302baead62 = private unnamed_addr constant [15 x i8] c"Activity Rate: ", align 1
@alloc_1c66b5fb2c6e357af3588db0097b572a = private unnamed_addr constant [2 x i8] c"%\0A", align 1
@alloc_b8102c06b2f5ecc4e6c8f29d75c6ba7b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f30e4e710d2959c9acbd1a302baead62, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_1c66b5fb2c6e357af3588db0097b572a, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_41a78173d65b403cc8bce28e26e78932 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\BB\00\00\00$\00\00\00" }>, align 8
@alloc_e47f0847cf58eb59252f57d04b8bf36e = private unnamed_addr constant [24 x i8] c"File I/O demonstration:\0A", align 1
@alloc_708a65d6c6bb252c0c943fb53ccfebfa = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e47f0847cf58eb59252f57d04b8bf36e, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_f9339f1766e63eb3b6eb399bdf9692b6 = private unnamed_addr constant [64 x i8] c"Hello from Rust!\0AThis is a test file.\0AGenerated by Rust program.", align 1
@alloc_60b9f4702ec126d25fdeb69748ec9d78 = private unnamed_addr constant [15 x i8] c"rust_output.txt", align 1
@alloc_2972e7bed1eb0bfe0396667019bf48af = private unnamed_addr constant [47 x i8] c"\E2\9C\93 File written successfully: rust_output.txt\0A", align 1
@alloc_e79f8af5aecf48b181af4a501dea6487 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2972e7bed1eb0bfe0396667019bf48af, [8 x i8] c"/\00\00\00\00\00\00\00" }>, align 8
@alloc_c53af88b662e6c617c0a22b2c284ba78 = private unnamed_addr constant [26 x i8] c"\E2\9C\97 Failed to write file: ", align 1
@alloc_b87479678d9376c6bd17f1bbcddc345c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c53af88b662e6c617c0a22b2c284ba78, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_eb6863c629cda0c891cd7a87488ba826 = private unnamed_addr constant [28 x i8] c"\E2\9C\93 File read successfully:\0A", align 1
@alloc_f64a78d6a34d2f14910c0c82a128a59f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_eb6863c629cda0c891cd7a87488ba826, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_9bdf5226eba0e502c11ea2f99fd7013a = private unnamed_addr constant [9 x i8] c"Content: ", align 1
@alloc_cad5995fdf8eaabc391223263b7889a1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9bdf5226eba0e502c11ea2f99fd7013a, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0b574f8878193c5ffbf64c2747692b68 = private unnamed_addr constant [25 x i8] c"\E2\9C\97 Failed to read file: ", align 1
@alloc_8c8e48f63f2fc3246831cf72f7c1285a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0b574f8878193c5ffbf64c2747692b68, [8 x i8] c"\19\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_40fb1439cb54dddf92e1893846ace81e = private unnamed_addr constant [23 x i8] c"Standard error output:\0A", align 1
@alloc_db2ad1749f2ee56255989a5d933c944f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_40fb1439cb54dddf92e1893846ace81e, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_2f3d840aaedfd38eab716e4d3cefe040 = private unnamed_addr constant [28 x i8] c"This message goes to stderr\0A", align 1
@alloc_70dd82b5dcdd91744fa7db2669f54d4d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2f3d840aaedfd38eab716e4d3cefe040, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_831b3fd860ef89cb4e00f5d7bde9b61b = private unnamed_addr constant [28 x i8] c"This message goes to stdout\0A", align 1
@alloc_b47c44eb3e083756329dd297420521f7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_831b3fd860ef89cb4e00f5d7bde9b61b, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_cc1992ac886e27782d73aba09f8bd0bd = private unnamed_addr constant [19 x i8] c"Flushing output... ", align 1
@alloc_41ce7501d0e67f2cded5a2eb9d12698f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cc1992ac886e27782d73aba09f8bd0bd, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_360ab5239dc8850314272a96c3d26b6d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\DA\00\00\00\1A\00\00\00" }>, align 8
@alloc_432e14a56299f22b2646efeab7c7892a = private unnamed_addr constant [6 x i8] c"Done!\0A", align 1
@alloc_0aa9c20ff196c3f9f760fa19975923f2 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_432e14a56299f22b2646efeab7c7892a, [8 x i8] c"\06\00\00\00\00\00\00\00" }>, align 8
@alloc_9feec2e0299552041d36264852be2cbc = private unnamed_addr constant [33 x i8] c"=== End of Input/Output Demo ===\0A", align 1
@alloc_f7882b960da409af89040bec8615e8c3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9feec2e0299552041d36264852be2cbc, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_6f7eaf01a0cce0a1ee4a801bc3e90bda = private unnamed_addr constant [6 x i8] c"User: ", align 1
@alloc_8242596180c359307a46f2d5c659a7d8 = private unnamed_addr constant [10 x i8] c" | Score: ", align 1
@alloc_e2ba7b1287c02995784aa847c59514c8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6f7eaf01a0cce0a1ee4a801bc3e90bda, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_8242596180c359307a46f2d5c659a7d8, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4a391371c50eca003c26d27cd6a67af0 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\AE\00\00\00*\00\00\00" }>, align 8
@alloc_0a6c12ea9a671ab17f9beffed06f42d4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\AE\00\00\004\00\00\00" }>, align 8
@alloc_28a862eab43efae8c30a1b094e365204 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\8D\00\00\00\18\00\00\00" }>, align 8
@alloc_0ce39fd6e2c457334168d6828eeb5373 = private unnamed_addr constant [10 x i8] c"Progress: ", align 1
@alloc_4486a02231778503b475edac208bf33a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0ce39fd6e2c457334168d6828eeb5373, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_1c66b5fb2c6e357af3588db0097b572a, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_1a259b709c19fe2bfca0b8f6003322fe = private unnamed_addr constant [18 x i8] c"While loop count: ", align 1
@alloc_1a81a0ffa1c9f25f28c8a1a7e4aa0a21 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1a259b709c19fe2bfca0b8f6003322fe, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5b35e0a02a3002a05bec5eeb94c8f182 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00:\00\00\00\09\00\00\00" }>, align 8
@alloc_197bc5eb4508f969bdb73f3f57765d7a = private unnamed_addr constant [16 x i8] c"Loop iteration: ", align 1
@alloc_f72ca9a573e1f3362fa118e901f1fed1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_197bc5eb4508f969bdb73f3f57765d7a, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_25bd883b28c85841db0ebda950595e70 = private unnamed_addr constant [18 x i8] c"Calculating area: ", align 1
@alloc_512d341499b7f53469cfbe4313f7e34a = private unnamed_addr constant [3 x i8] c" x ", align 1
@alloc_a5cdec5909fbda735160b00578dc37df = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_25bd883b28c85841db0ebda950595e70, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_512d341499b7f53469cfbe4313f7e34a, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_67c7c4f743ad034b17a86975730367c5 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_66bdded3e81e355683ea6461ce7efb66, [16 x i8] c"\12\00\00\00\00\00\00\00\E3\00\00\00\05\00\00\00" }>, align 8

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h6b045f251fe5556aE"(ptr align 4 %self) unnamed_addr #0 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17he5e9fd4e327749a3E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; std::fs::read_to_string
; Function Attrs: uwtable
define internal void @_ZN3std2fs14read_to_string17h38e285916c01fbc6E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %path = alloca [16 x i8], align 8
  store ptr %0, ptr %path, align 8
  %2 = getelementptr inbounds i8, ptr %path, i64 8
  store i64 %1, ptr %2, align 8
; invoke <&T as core::convert::AsRef<U>>::as_ref
  %3 = invoke { ptr, i64 } @"_ZN55_$LT$$RF$T$u20$as$u20$core..convert..AsRef$LT$U$GT$$GT$6as_ref17hee7487cabaf629d4E"(ptr align 8 %path)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  cleanupret from %cleanuppad unwind to caller

funclet_bb4:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  %_2.0 = extractvalue { ptr, i64 } %3, 0
  %_2.1 = extractvalue { ptr, i64 } %3, 1
; invoke std::fs::read_to_string::inner
  invoke void @_ZN3std2fs14read_to_string5inner17h66216e5eba8d5f0eE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %_2.0, i64 %_2.1)
          to label %bb2 unwind label %funclet_bb4

bb2:                                              ; preds = %bb1
  ret void
}

; std::fs::write
; Function Attrs: uwtable
define internal ptr @_ZN3std2fs5write17h1e28c32aecccac62E(ptr align 1 %0, i64 %1, ptr align 1 %2, i64 %3) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %contents = alloca [16 x i8], align 8
  %path = alloca [16 x i8], align 8
  store ptr %0, ptr %path, align 8
  %4 = getelementptr inbounds i8, ptr %path, i64 8
  store i64 %1, ptr %4, align 8
  store ptr %2, ptr %contents, align 8
  %5 = getelementptr inbounds i8, ptr %contents, i64 8
  store i64 %3, ptr %5, align 8
; invoke <&T as core::convert::AsRef<U>>::as_ref
  %6 = invoke { ptr, i64 } @"_ZN55_$LT$$RF$T$u20$as$u20$core..convert..AsRef$LT$U$GT$$GT$6as_ref17hee7487cabaf629d4E"(ptr align 8 %path)
          to label %bb1 unwind label %funclet_bb6

bb6:                                              ; preds = %funclet_bb6
  cleanupret from %cleanuppad unwind label %funclet_bb7

funclet_bb6:                                      ; preds = %bb2, %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb6

bb1:                                              ; preds = %start
  %_3.0 = extractvalue { ptr, i64 } %6, 0
  %_3.1 = extractvalue { ptr, i64 } %6, 1
; invoke <&T as core::convert::AsRef<U>>::as_ref
  %7 = invoke { ptr, i64 } @"_ZN55_$LT$$RF$T$u20$as$u20$core..convert..AsRef$LT$U$GT$$GT$6as_ref17h40e68f7d7ed64104E"(ptr align 8 %contents)
          to label %bb2 unwind label %funclet_bb6

bb2:                                              ; preds = %bb1
  %_5.0 = extractvalue { ptr, i64 } %7, 0
  %_5.1 = extractvalue { ptr, i64 } %7, 1
; invoke std::fs::write::inner
  %_0 = invoke ptr @_ZN3std2fs5write5inner17hfa09bd9ccb8b3913E(ptr align 1 %_3.0, i64 %_3.1, ptr align 1 %_5.0, i64 %_5.1)
          to label %bb3 unwind label %funclet_bb6

bb3:                                              ; preds = %bb2
  br label %bb4

bb4:                                              ; preds = %bb3
  ret ptr %_0

bb7:                                              ; preds = %funclet_bb7
  cleanupret from %cleanuppad1 unwind to caller

funclet_bb7:                                      ; preds = %bb6
  %cleanuppad1 = cleanuppad within none []
  br label %bb7
}

; std::io::error::repr_bitpacked::decode_repr
; Function Attrs: inlinehint uwtable
define internal void @_ZN3std2io5error14repr_bitpacked11decode_repr17hc86234f27705acb4E(ptr sret([16 x i8]) align 8 %_0, ptr %ptr) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [8 x i8], align 8
  %_21 = alloca [1 x i8], align 1
  %self = alloca [1 x i8], align 1
  %bits = alloca [8 x i8], align 8
  store i8 1, ptr %_21, align 1
  %1 = ptrtoint ptr %ptr to i64
  store i64 %1, ptr %bits, align 8
  %2 = load i64, ptr %bits, align 8
  %_5 = and i64 %2, 3
  switch i64 %_5, label %bb1 [
    i64 2, label %bb5
    i64 3, label %bb4
    i64 0, label %bb3
    i64 1, label %bb2
  ]

bb1:                                              ; preds = %start
; invoke core::panicking::panic
  invoke void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_a500d906b91607583596fa15e63c2ada, i64 40, ptr align 8 @alloc_684dcb29e286381a0922901efff1b793) #13
          to label %unreachable unwind label %funclet_bb12

bb5:                                              ; preds = %start
  %_8 = load i64, ptr %bits, align 8
  %_7 = ashr i64 %_8, 32
  %code = trunc i64 %_7 to i32
  %3 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %code, ptr %3, align 4
  store i8 0, ptr %_0, align 8
  br label %bb10

bb4:                                              ; preds = %start
  %4 = load i64, ptr %bits, align 8
  %_10 = lshr i64 %4, 32
  %kind_bits = trunc i64 %_10 to i32
; invoke std::io::error::repr_bitpacked::kind_from_prim
  %5 = invoke i8 @_ZN3std2io5error14repr_bitpacked14kind_from_prim17h1d07aa2371e54466E(i32 %kind_bits)
          to label %bb6 unwind label %funclet_bb12

bb3:                                              ; preds = %start
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %ptr, ptr %6, align 8
  store i8 2, ptr %_0, align 8
  br label %bb10

bb2:                                              ; preds = %start
  %7 = getelementptr i8, ptr %ptr, i64 -1
  store ptr %7, ptr %0, align 8
  %_36 = load ptr, ptr %0, align 8
  store i8 0, ptr %_21, align 1
; invoke <std::io::error::repr_bitpacked::Repr as core::ops::drop::Drop>::drop::{{closure}}
  %_17 = invoke align 8 ptr @"_ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop28_$u7b$$u7b$closure$u7d$$u7d$17h6c1c08bf0924d16dE"(ptr %_36)
          to label %bb7 unwind label %funclet_bb12

bb10:                                             ; preds = %bb3, %bb15, %bb5
  br label %bb8

bb12:                                             ; preds = %funclet_bb12
  %8 = load i8, ptr %_21, align 1
  %9 = trunc nuw i8 %8 to i1
  br i1 %9, label %bb11, label %bb9

funclet_bb12:                                     ; preds = %bb1, %bb2, %bb4
  %cleanuppad = cleanuppad within none []
  br label %bb12

bb6:                                              ; preds = %bb4
  store i8 %5, ptr %self, align 1
  %10 = load i8, ptr %self, align 1
  %11 = icmp eq i8 %10, 42
  %_23 = select i1 %11, i64 0, i64 1
  %12 = trunc nuw i64 %_23 to i1
  br i1 %12, label %bb15, label %bb14

bb15:                                             ; preds = %bb6
  %kind = load i8, ptr %self, align 1
  %13 = getelementptr inbounds i8, ptr %_0, i64 1
  store i8 %kind, ptr %13, align 1
  store i8 1, ptr %_0, align 8
  br label %bb10

bb14:                                             ; preds = %bb6
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h7e182edc8de9c09cE() #14
  br label %bb13

bb13:                                             ; preds = %bb14
  unreachable

bb8:                                              ; preds = %bb7, %bb10
  ret void

bb7:                                              ; preds = %bb2
  %14 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %_17, ptr %14, align 8
  store i8 3, ptr %_0, align 8
  br label %bb8

unreachable:                                      ; preds = %bb1
  unreachable

bb9:                                              ; preds = %bb11, %bb12
  cleanupret from %cleanuppad unwind to caller

bb11:                                             ; preds = %bb12
  br label %bb9
}

; std::io::error::repr_bitpacked::kind_from_prim
; Function Attrs: inlinehint uwtable
define internal i8 @_ZN3std2io5error14repr_bitpacked14kind_from_prim17h1d07aa2371e54466E(i32 %0) unnamed_addr #0 {
start:
  %_0 = alloca [1 x i8], align 1
  %ek = alloca [4 x i8], align 4
  store i32 %0, ptr %ek, align 4
  %1 = load i32, ptr %ek, align 4
  %2 = icmp eq i32 %1, 0
  br i1 %2, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb85

bb2:                                              ; preds = %start
  %3 = load i32, ptr %ek, align 4
  %4 = icmp eq i32 %3, 1
  br i1 %4, label %bb3, label %bb4

bb85:                                             ; preds = %bb84, %bb83, %bb81, %bb79, %bb77, %bb75, %bb73, %bb71, %bb69, %bb67, %bb65, %bb63, %bb61, %bb59, %bb57, %bb55, %bb53, %bb51, %bb49, %bb47, %bb45, %bb43, %bb41, %bb39, %bb37, %bb35, %bb33, %bb31, %bb29, %bb27, %bb25, %bb23, %bb21, %bb19, %bb17, %bb15, %bb13, %bb11, %bb9, %bb7, %bb5, %bb3, %bb1
  %5 = load i8, ptr %_0, align 1
  ret i8 %5

bb3:                                              ; preds = %bb2
  store i8 1, ptr %_0, align 1
  br label %bb85

bb4:                                              ; preds = %bb2
  %6 = load i32, ptr %ek, align 4
  %7 = icmp eq i32 %6, 2
  br i1 %7, label %bb5, label %bb6

bb5:                                              ; preds = %bb4
  store i8 2, ptr %_0, align 1
  br label %bb85

bb6:                                              ; preds = %bb4
  %8 = load i32, ptr %ek, align 4
  %9 = icmp eq i32 %8, 3
  br i1 %9, label %bb7, label %bb8

bb7:                                              ; preds = %bb6
  store i8 3, ptr %_0, align 1
  br label %bb85

bb8:                                              ; preds = %bb6
  %10 = load i32, ptr %ek, align 4
  %11 = icmp eq i32 %10, 4
  br i1 %11, label %bb9, label %bb10

bb9:                                              ; preds = %bb8
  store i8 4, ptr %_0, align 1
  br label %bb85

bb10:                                             ; preds = %bb8
  %12 = load i32, ptr %ek, align 4
  %13 = icmp eq i32 %12, 5
  br i1 %13, label %bb11, label %bb12

bb11:                                             ; preds = %bb10
  store i8 5, ptr %_0, align 1
  br label %bb85

bb12:                                             ; preds = %bb10
  %14 = load i32, ptr %ek, align 4
  %15 = icmp eq i32 %14, 6
  br i1 %15, label %bb13, label %bb14

bb13:                                             ; preds = %bb12
  store i8 6, ptr %_0, align 1
  br label %bb85

bb14:                                             ; preds = %bb12
  %16 = load i32, ptr %ek, align 4
  %17 = icmp eq i32 %16, 7
  br i1 %17, label %bb15, label %bb16

bb15:                                             ; preds = %bb14
  store i8 7, ptr %_0, align 1
  br label %bb85

bb16:                                             ; preds = %bb14
  %18 = load i32, ptr %ek, align 4
  %19 = icmp eq i32 %18, 8
  br i1 %19, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  store i8 8, ptr %_0, align 1
  br label %bb85

bb18:                                             ; preds = %bb16
  %20 = load i32, ptr %ek, align 4
  %21 = icmp eq i32 %20, 9
  br i1 %21, label %bb19, label %bb20

bb19:                                             ; preds = %bb18
  store i8 9, ptr %_0, align 1
  br label %bb85

bb20:                                             ; preds = %bb18
  %22 = load i32, ptr %ek, align 4
  %23 = icmp eq i32 %22, 10
  br i1 %23, label %bb21, label %bb22

bb21:                                             ; preds = %bb20
  store i8 10, ptr %_0, align 1
  br label %bb85

bb22:                                             ; preds = %bb20
  %24 = load i32, ptr %ek, align 4
  %25 = icmp eq i32 %24, 11
  br i1 %25, label %bb23, label %bb24

bb23:                                             ; preds = %bb22
  store i8 11, ptr %_0, align 1
  br label %bb85

bb24:                                             ; preds = %bb22
  %26 = load i32, ptr %ek, align 4
  %27 = icmp eq i32 %26, 12
  br i1 %27, label %bb25, label %bb26

bb25:                                             ; preds = %bb24
  store i8 12, ptr %_0, align 1
  br label %bb85

bb26:                                             ; preds = %bb24
  %28 = load i32, ptr %ek, align 4
  %29 = icmp eq i32 %28, 13
  br i1 %29, label %bb27, label %bb28

bb27:                                             ; preds = %bb26
  store i8 13, ptr %_0, align 1
  br label %bb85

bb28:                                             ; preds = %bb26
  %30 = load i32, ptr %ek, align 4
  %31 = icmp eq i32 %30, 14
  br i1 %31, label %bb29, label %bb30

bb29:                                             ; preds = %bb28
  store i8 14, ptr %_0, align 1
  br label %bb85

bb30:                                             ; preds = %bb28
  %32 = load i32, ptr %ek, align 4
  %33 = icmp eq i32 %32, 15
  br i1 %33, label %bb31, label %bb32

bb31:                                             ; preds = %bb30
  store i8 15, ptr %_0, align 1
  br label %bb85

bb32:                                             ; preds = %bb30
  %34 = load i32, ptr %ek, align 4
  %35 = icmp eq i32 %34, 16
  br i1 %35, label %bb33, label %bb34

bb33:                                             ; preds = %bb32
  store i8 16, ptr %_0, align 1
  br label %bb85

bb34:                                             ; preds = %bb32
  %36 = load i32, ptr %ek, align 4
  %37 = icmp eq i32 %36, 17
  br i1 %37, label %bb35, label %bb36

bb35:                                             ; preds = %bb34
  store i8 17, ptr %_0, align 1
  br label %bb85

bb36:                                             ; preds = %bb34
  %38 = load i32, ptr %ek, align 4
  %39 = icmp eq i32 %38, 18
  br i1 %39, label %bb37, label %bb38

bb37:                                             ; preds = %bb36
  store i8 18, ptr %_0, align 1
  br label %bb85

bb38:                                             ; preds = %bb36
  %40 = load i32, ptr %ek, align 4
  %41 = icmp eq i32 %40, 19
  br i1 %41, label %bb39, label %bb40

bb39:                                             ; preds = %bb38
  store i8 19, ptr %_0, align 1
  br label %bb85

bb40:                                             ; preds = %bb38
  %42 = load i32, ptr %ek, align 4
  %43 = icmp eq i32 %42, 20
  br i1 %43, label %bb41, label %bb42

bb41:                                             ; preds = %bb40
  store i8 20, ptr %_0, align 1
  br label %bb85

bb42:                                             ; preds = %bb40
  %44 = load i32, ptr %ek, align 4
  %45 = icmp eq i32 %44, 21
  br i1 %45, label %bb43, label %bb44

bb43:                                             ; preds = %bb42
  store i8 21, ptr %_0, align 1
  br label %bb85

bb44:                                             ; preds = %bb42
  %46 = load i32, ptr %ek, align 4
  %47 = icmp eq i32 %46, 22
  br i1 %47, label %bb45, label %bb46

bb45:                                             ; preds = %bb44
  store i8 22, ptr %_0, align 1
  br label %bb85

bb46:                                             ; preds = %bb44
  %48 = load i32, ptr %ek, align 4
  %49 = icmp eq i32 %48, 23
  br i1 %49, label %bb47, label %bb48

bb47:                                             ; preds = %bb46
  store i8 23, ptr %_0, align 1
  br label %bb85

bb48:                                             ; preds = %bb46
  %50 = load i32, ptr %ek, align 4
  %51 = icmp eq i32 %50, 24
  br i1 %51, label %bb49, label %bb50

bb49:                                             ; preds = %bb48
  store i8 24, ptr %_0, align 1
  br label %bb85

bb50:                                             ; preds = %bb48
  %52 = load i32, ptr %ek, align 4
  %53 = icmp eq i32 %52, 25
  br i1 %53, label %bb51, label %bb52

bb51:                                             ; preds = %bb50
  store i8 25, ptr %_0, align 1
  br label %bb85

bb52:                                             ; preds = %bb50
  %54 = load i32, ptr %ek, align 4
  %55 = icmp eq i32 %54, 26
  br i1 %55, label %bb53, label %bb54

bb53:                                             ; preds = %bb52
  store i8 26, ptr %_0, align 1
  br label %bb85

bb54:                                             ; preds = %bb52
  %56 = load i32, ptr %ek, align 4
  %57 = icmp eq i32 %56, 27
  br i1 %57, label %bb55, label %bb56

bb55:                                             ; preds = %bb54
  store i8 27, ptr %_0, align 1
  br label %bb85

bb56:                                             ; preds = %bb54
  %58 = load i32, ptr %ek, align 4
  %59 = icmp eq i32 %58, 28
  br i1 %59, label %bb57, label %bb58

bb57:                                             ; preds = %bb56
  store i8 28, ptr %_0, align 1
  br label %bb85

bb58:                                             ; preds = %bb56
  %60 = load i32, ptr %ek, align 4
  %61 = icmp eq i32 %60, 29
  br i1 %61, label %bb59, label %bb60

bb59:                                             ; preds = %bb58
  store i8 29, ptr %_0, align 1
  br label %bb85

bb60:                                             ; preds = %bb58
  %62 = load i32, ptr %ek, align 4
  %63 = icmp eq i32 %62, 30
  br i1 %63, label %bb61, label %bb62

bb61:                                             ; preds = %bb60
  store i8 30, ptr %_0, align 1
  br label %bb85

bb62:                                             ; preds = %bb60
  %64 = load i32, ptr %ek, align 4
  %65 = icmp eq i32 %64, 31
  br i1 %65, label %bb63, label %bb64

bb63:                                             ; preds = %bb62
  store i8 31, ptr %_0, align 1
  br label %bb85

bb64:                                             ; preds = %bb62
  %66 = load i32, ptr %ek, align 4
  %67 = icmp eq i32 %66, 32
  br i1 %67, label %bb65, label %bb66

bb65:                                             ; preds = %bb64
  store i8 32, ptr %_0, align 1
  br label %bb85

bb66:                                             ; preds = %bb64
  %68 = load i32, ptr %ek, align 4
  %69 = icmp eq i32 %68, 33
  br i1 %69, label %bb67, label %bb68

bb67:                                             ; preds = %bb66
  store i8 33, ptr %_0, align 1
  br label %bb85

bb68:                                             ; preds = %bb66
  %70 = load i32, ptr %ek, align 4
  %71 = icmp eq i32 %70, 34
  br i1 %71, label %bb69, label %bb70

bb69:                                             ; preds = %bb68
  store i8 34, ptr %_0, align 1
  br label %bb85

bb70:                                             ; preds = %bb68
  %72 = load i32, ptr %ek, align 4
  %73 = icmp eq i32 %72, 35
  br i1 %73, label %bb71, label %bb72

bb71:                                             ; preds = %bb70
  store i8 35, ptr %_0, align 1
  br label %bb85

bb72:                                             ; preds = %bb70
  %74 = load i32, ptr %ek, align 4
  %75 = icmp eq i32 %74, 40
  br i1 %75, label %bb73, label %bb74

bb73:                                             ; preds = %bb72
  store i8 40, ptr %_0, align 1
  br label %bb85

bb74:                                             ; preds = %bb72
  %76 = load i32, ptr %ek, align 4
  %77 = icmp eq i32 %76, 37
  br i1 %77, label %bb75, label %bb76

bb75:                                             ; preds = %bb74
  store i8 37, ptr %_0, align 1
  br label %bb85

bb76:                                             ; preds = %bb74
  %78 = load i32, ptr %ek, align 4
  %79 = icmp eq i32 %78, 36
  br i1 %79, label %bb77, label %bb78

bb77:                                             ; preds = %bb76
  store i8 36, ptr %_0, align 1
  br label %bb85

bb78:                                             ; preds = %bb76
  %80 = load i32, ptr %ek, align 4
  %81 = icmp eq i32 %80, 38
  br i1 %81, label %bb79, label %bb80

bb79:                                             ; preds = %bb78
  store i8 38, ptr %_0, align 1
  br label %bb85

bb80:                                             ; preds = %bb78
  %82 = load i32, ptr %ek, align 4
  %83 = icmp eq i32 %82, 39
  br i1 %83, label %bb81, label %bb82

bb81:                                             ; preds = %bb80
  store i8 39, ptr %_0, align 1
  br label %bb85

bb82:                                             ; preds = %bb80
  %84 = load i32, ptr %ek, align 4
  %85 = icmp eq i32 %84, 41
  br i1 %85, label %bb83, label %bb84

bb83:                                             ; preds = %bb82
  store i8 41, ptr %_0, align 1
  br label %bb85

bb84:                                             ; preds = %bb82
  store i8 42, ptr %_0, align 1
  br label %bb85
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hf9ce7bbae2051936E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #1 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hc583c25b330be4bfE"(ptr align 8 %_1) unnamed_addr #0 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hd7762eacd56da957E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hb87b130b9df80c78E"()
  ret i32 %self
}

; std::ffi::os_str::<impl core::convert::AsRef<std::ffi::os_str::OsStr> for str>::as_ref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN3std3ffi6os_str85_$LT$impl$u20$core..convert..AsRef$LT$std..ffi..os_str..OsStr$GT$$u20$for$u20$str$GT$6as_ref17h7d0c9ebdd16361bfE"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { ptr, i64 } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, i64 } %0, i64 %self.1, 1
  ret { ptr, i64 } %1
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hd7762eacd56da957E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17he24d60a5ca68a32bE(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; std::path::<impl core::convert::AsRef<std::path::Path> for str>::as_ref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN3std4path77_$LT$impl$u20$core..convert..AsRef$LT$std..path..Path$GT$$u20$for$u20$str$GT$6as_ref17h33ce209a70c02e7fE"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
; call std::ffi::os_str::<impl core::convert::AsRef<std::ffi::os_str::OsStr> for str>::as_ref
  %0 = call { ptr, i64 } @"_ZN3std3ffi6os_str85_$LT$impl$u20$core..convert..AsRef$LT$std..ffi..os_str..OsStr$GT$$u20$for$u20$str$GT$6as_ref17h7d0c9ebdd16361bfE"(ptr align 1 %self.0, i64 %self.1)
  %_4.0 = extractvalue { ptr, i64 } %0, 0
  %_4.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_4.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_4.1, 1
  ret { ptr, i64 } %2
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h9c6fabe868969ed9E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17he5e9fd4e327749a3E"(i32 %start1, i64 %n) unnamed_addr #0 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.83db9855118e6db4b4ca651d0b944742.0, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.83db9855118e6db4b4ca651d0b944742.0, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h7e182edc8de9c09cE() #14
  unreachable
}

; <usize as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN49_$LT$usize$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h2bfb61c1e645ed03E"(i64 %start1, i64 %n) unnamed_addr #0 {
start:
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h230837d0633bb106E"(i64 %start1, i64 %n) #14
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = add nuw i64 %start1, %n
  ret i64 %_0
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17h7d88a56184a482c3E() unnamed_addr #3 {
start:
  ret void
}

; core::fmt::rt::Placeholder::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_0, i64 %position, i32 %flags, ptr align 8 %precision, ptr align 8 %width) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 %position, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 40
  store i32 %flags, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %precision, i64 16, i1 false)
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %width, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h9c6fabe868969ed9E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h330ca224aea349b9E(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h3b2964d79eb00dabE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h482c90503d82e1c8E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$std..io..error..Error$u20$as$u20$core..fmt..Display$GT$3fmt17hff338456df8a6ec6E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hcad21941d51a5e59E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hac41727c0bae05a5E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::UnsafeArg::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E() unnamed_addr #0 {
start:
  ret void
}

; core::fmt::Arguments::new_v1_formatted
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces.0, i64 %pieces.1, ptr align 8 %args.0, i64 %args.1, ptr align 8 %fmt.0, i64 %fmt.1) unnamed_addr #0 {
start:
  %_5 = alloca [16 x i8], align 8
  store ptr %fmt.0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %fmt.1, ptr %0, align 8
  store ptr %pieces.0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pieces.1, ptr %1, align 8
  %2 = load ptr, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args.0, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  store i64 %args.1, ptr %8, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h7a72f3daa34490b0E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h230837d0633bb106E"(i64 %lhs, i64 %rhs) unnamed_addr #4 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #15
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h35db1577a2a8a533E"(i64 %lhs, i64 %rhs) unnamed_addr #4 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #15
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hc8ba31cfeea827baE"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #0 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0edd086e36b86a51E"(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h87742fcfde71711dE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h87742fcfde71711dE(ptr %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hc583c25b330be4bfE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17he24d60a5ca68a32bE(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ptr::drop_in_place<std::io::error::ErrorData<alloc::boxed::Box<std::io::error::Custom>>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr101drop_in_place$LT$std..io..error..ErrorData$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$$GT$17hc13101e97964c8e8E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %0 = load i8, ptr %_1, align 8
  %_2 = zext i8 %0 to i64
  switch i64 %_2, label %bb2 [
    i64 0, label %bb1
    i64 1, label %bb1
    i64 2, label %bb1
  ]

bb2:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_1, i64 8
; call core::ptr::drop_in_place<alloc::boxed::Box<std::io::error::Custom>>
  call void @"_ZN4core3ptr68drop_in_place$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$17h7440fc0f551b2fc7E"(ptr align 8 %1)
  br label %bb1

bb1:                                              ; preds = %bb2, %start, %start, %start
  ret void
}

; core::ptr::drop_in_place<alloc::boxed::Box<dyn core::error::Error+core::marker::Sync+core::marker::Send>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr118drop_in_place$LT$alloc..boxed..Box$LT$dyn$u20$core..error..Error$u2b$core..marker..Sync$u2b$core..marker..Send$GT$$GT$17h017cc41da89c227bE"(ptr align 8 %_1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6.0 = load ptr, ptr %_1, align 8
  %0 = getelementptr inbounds i8, ptr %_1, i64 8
  %_6.1 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_6.1, i64 0
  %2 = load ptr, ptr %1, align 8, !invariant.load !4
  %3 = icmp ne ptr %2, null
  br i1 %3, label %is_not_null, label %bb3

is_not_null:                                      ; preds = %start
  invoke void %2(ptr %_6.0)
          to label %bb3 unwind label %funclet_bb4

bb3:                                              ; preds = %is_not_null, %start
; call <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb75aa8739083681aE"(ptr align 8 %_1)
  ret void

bb4:                                              ; preds = %funclet_bb4
; call <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb75aa8739083681aE"(ptr align 8 %_1) #16 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb4:                                      ; preds = %is_not_null
  %cleanuppad = cleanuppad within none []
  br label %bb4
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h50d4c76614800282E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hd896ac0fe51d7577E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::io::error::Error>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call core::ptr::drop_in_place<std::io::error::repr_bitpacked::Repr>
  call void @"_ZN4core3ptr57drop_in_place$LT$std..io..error..repr_bitpacked..Repr$GT$17hf495b12e9b4217f8E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::io::error::Custom>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr43drop_in_place$LT$std..io..error..Custom$GT$17h96b36edd28294314E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call core::ptr::drop_in_place<alloc::boxed::Box<dyn core::error::Error+core::marker::Sync+core::marker::Send>>
  call void @"_ZN4core3ptr118drop_in_place$LT$alloc..boxed..Box$LT$dyn$u20$core..error..Error$u2b$core..marker..Sync$u2b$core..marker..Send$GT$$GT$17h017cc41da89c227bE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hd896ac0fe51d7577E"(ptr align 8 %_1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h0cc5604b22b17c8fE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha806bc4f6059f40fE"(ptr align 8 %_1) #16 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha806bc4f6059f40fE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha806bc4f6059f40fE"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h6e2f8310dd989626E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::io::error::repr_bitpacked::Repr>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr57drop_in_place$LT$std..io..error..repr_bitpacked..Repr$GT$17hf495b12e9b4217f8E"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call <std::io::error::repr_bitpacked::Repr as core::ops::drop::Drop>::drop
  call void @"_ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha9aa30c408fe5186E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::boxed::Box<std::io::error::Custom>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr68drop_in_place$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$17h7440fc0f551b2fc7E"(ptr align 8 %_1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = load ptr, ptr %_1, align 8
; invoke core::ptr::drop_in_place<std::io::error::Custom>
  invoke void @"_ZN4core3ptr43drop_in_place$LT$std..io..error..Custom$GT$17h96b36edd28294314E"(ptr align 8 %_6)
          to label %bb3 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
; call <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb99ad9f6b54ea87bE"(ptr align 8 %_1) #16 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb3:                                              ; preds = %start
; call <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb99ad9f6b54ea87bE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h4b3c0415b3691fb1E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h91d5d4cc9a7b147bE"(ptr %ptr) unnamed_addr #4 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #15
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::drop_in_place<dyn core::error::Error+core::marker::Sync+core::marker::Send>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr93drop_in_place$LT$dyn$u20$core..error..Error$u2b$core..marker..Sync$u2b$core..marker..Send$GT$17h29a634a391ee70e0E"(ptr align 1 %_1.0, ptr align 8 %_1.1) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_1.1, i64 0
  %1 = load ptr, ptr %0, align 8, !invariant.load !4
  %2 = icmp ne ptr %1, null
  br i1 %2, label %is_not_null, label %bb1

is_not_null:                                      ; preds = %start
  call void %1(ptr %_1.0)
  br label %bb1

bb1:                                              ; preds = %is_not_null, %start
  ret void
}

; core::str::<impl core::convert::AsRef<[u8]> for str>::as_ref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN4core3str74_$LT$impl$u20$core..convert..AsRef$LT$$u5b$u8$u5d$$GT$$u20$for$u20$str$GT$6as_ref17h0686c7c3e5e79fefE"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { ptr, i64 } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, i64 } %0, i64 %self.1, 1
  ret { ptr, i64 } %1
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17h7e182edc8de9c09cE() unnamed_addr #4 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #15
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h972d3d0a27183a77E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
  %0 = call { i64, i64 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17h524fb6596d3ff16aE"(ptr align 8 %self)
  %_0.0 = extractvalue { i64, i64 } %0, 0
  %_0.1 = extractvalue { i64, i64 } %0, 1
  %1 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %2 = insertvalue { i64, i64 } %1, i64 %_0.1, 1
  ret { i64, i64 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hdf8ee135f37d6549E"(ptr align 4 %self) unnamed_addr #0 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h6b045f251fe5556aE"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17he3bede6364a52a82E(i64 %size, i64 %align) unnamed_addr #4 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #15
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17he76abe216fd88418E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #4 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.83db9855118e6db4b4ca651d0b944742.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #13
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #13
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #15
  unreachable
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hb87b130b9df80c78E"() unnamed_addr #0 {
start:
  ret i32 0
}

; <&T as core::convert::AsRef<U>>::as_ref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN55_$LT$$RF$T$u20$as$u20$core..convert..AsRef$LT$U$GT$$GT$6as_ref17h40e68f7d7ed64104E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %_2.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_2.1 = load i64, ptr %0, align 8
; call core::str::<impl core::convert::AsRef<[u8]> for str>::as_ref
  %1 = call { ptr, i64 } @"_ZN4core3str74_$LT$impl$u20$core..convert..AsRef$LT$$u5b$u8$u5d$$GT$$u20$for$u20$str$GT$6as_ref17h0686c7c3e5e79fefE"(ptr align 1 %_2.0, i64 %_2.1)
  %_0.0 = extractvalue { ptr, i64 } %1, 0
  %_0.1 = extractvalue { ptr, i64 } %1, 1
  %2 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %_0.1, 1
  ret { ptr, i64 } %3
}

; <&T as core::convert::AsRef<U>>::as_ref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN55_$LT$$RF$T$u20$as$u20$core..convert..AsRef$LT$U$GT$$GT$6as_ref17hee7487cabaf629d4E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %_2.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_2.1 = load i64, ptr %0, align 8
; call std::path::<impl core::convert::AsRef<std::path::Path> for str>::as_ref
  %1 = call { ptr, i64 } @"_ZN3std4path77_$LT$impl$u20$core..convert..AsRef$LT$std..path..Path$GT$$u20$for$u20$str$GT$6as_ref17h33ce209a70c02e7fE"(ptr align 1 %_2.0, i64 %_2.1)
  %_0.0 = extractvalue { ptr, i64 } %1, 0
  %_0.1 = extractvalue { ptr, i64 } %1, 1
  %2 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %_0.1, 1
  ret { ptr, i64 } %3
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17hde67a1e62f8e79baE"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hccb0ecdd58977df1E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h5493bbda2ec7bfe3E"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hccb0ecdd58977df1E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h35db1577a2a8a533E"(i64 %self1, i64 %self3) #14
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17he3bede6364a52a82E(i64 %8, i64 %9) #14
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hac41727c0bae05a5E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17he76abe216fd88418E(ptr %_8, i64 1, i64 1, i64 %len) #14
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3fcde743b1928163E"(i64 %self.0, i64 %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { i64, i64 } poison, i64 %self.0, 0
  %1 = insertvalue { i64, i64 } %0, i64 %self.1, 1
  ret { i64, i64 } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hda20e4209b3ab305E"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h5493bbda2ec7bfe3E"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #14
  br label %bb2
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h0cc5604b22b17c8fE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb75aa8739083681aE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %ptr.0 = load ptr, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  %ptr.1 = load ptr, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %ptr.1, i64 8
  %4 = load i64, ptr %3, align 8, !invariant.load !4
  %5 = getelementptr inbounds i8, ptr %ptr.1, i64 16
  %6 = load i64, ptr %5, align 8, !invariant.load !4
  store i64 %4, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  %7 = getelementptr inbounds i8, ptr %ptr.1, i64 8
  %8 = load i64, ptr %7, align 8, !invariant.load !4
  %9 = getelementptr inbounds i8, ptr %ptr.1, i64 16
  %10 = load i64, ptr %9, align 8, !invariant.load !4
  store i64 %10, ptr %0, align 8
  %align = load i64, ptr %0, align 8
  br label %bb6

bb6:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17he3bede6364a52a82E(i64 %size, i64 %align) #14
  br label %bb7

bb7:                                              ; preds = %bb6
  %11 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %size, ptr %11, align 8
  store i64 %align, ptr %layout, align 8
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb3, label %bb1

bb3:                                              ; preds = %bb1, %bb7
  ret void

bb1:                                              ; preds = %bb7
  %_7 = getelementptr inbounds i8, ptr %self, i64 16
  %13 = load i64, ptr %layout, align 8
  %14 = getelementptr inbounds i8, ptr %layout, i64 8
  %15 = load i64, ptr %14, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h5493bbda2ec7bfe3E"(ptr align 1 %_7, ptr %ptr.0, i64 %13, i64 %15)
  br label %bb3
}

; <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb99ad9f6b54ea87bE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %ptr = load ptr, ptr %self, align 8
  store i64 24, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  store i64 8, ptr %0, align 8
  %align = load i64, ptr %0, align 8
  br label %bb6

bb6:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17he3bede6364a52a82E(i64 %size, i64 %align) #14
  br label %bb7

bb7:                                              ; preds = %bb6
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %size, ptr %2, align 8
  store i64 %align, ptr %layout, align 8
  %3 = icmp eq i64 %size, 0
  br i1 %3, label %bb3, label %bb1

bb3:                                              ; preds = %bb1, %bb7
  ret void

bb1:                                              ; preds = %bb7
  %_7 = getelementptr inbounds i8, ptr %self, i64 8
  %4 = load i64, ptr %layout, align 8
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %6 = load i64, ptr %5, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h5493bbda2ec7bfe3E"(ptr align 1 %_7, ptr %ptr, i64 %4, i64 %6)
  br label %bb3
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h6e2f8310dd989626E"(ptr align 8 %self) unnamed_addr #1 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17hde67a1e62f8e79baE"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <std::io::error::repr_bitpacked::Repr as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha9aa30c408fe5186E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %_2 = alloca [16 x i8], align 8
  %_3 = load ptr, ptr %self, align 8
; call std::io::error::repr_bitpacked::decode_repr
  call void @_ZN3std2io5error14repr_bitpacked11decode_repr17hc86234f27705acb4E(ptr sret([16 x i8]) align 8 %_2, ptr %_3)
; call core::ptr::drop_in_place<std::io::error::ErrorData<alloc::boxed::Box<std::io::error::Custom>>>
  call void @"_ZN4core3ptr101drop_in_place$LT$std..io..error..ErrorData$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$$GT$17hc13101e97964c8e8E"(ptr align 8 %_2)
  ret void
}

; <std::io::error::repr_bitpacked::Repr as core::ops::drop::Drop>::drop::{{closure}}
; Function Attrs: inlinehint uwtable
define internal align 8 ptr @"_ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop28_$u7b$$u7b$closure$u7d$$u7d$17h6c1c08bf0924d16dE"(ptr %p) unnamed_addr #0 {
start:
  br label %bb1

bb1:                                              ; preds = %start
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h91d5d4cc9a7b147bE"(ptr %p) #14
  br label %bb3

bb3:                                              ; preds = %bb1
  ret ptr %p
}

; <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17h524fb6596d3ff16aE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %_0 = alloca [16 x i8], align 8
  %_4 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_4, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br i1 %_0.i, label %bb2, label %bb4

bb4:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb5

bb2:                                              ; preds = %start
  %old = load i64, ptr %self, align 8
; call <usize as core::iter::range::Step>::forward_unchecked
  %_6 = call i64 @"_ZN49_$LT$usize$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h2bfb61c1e645ed03E"(i64 %old, i64 1)
  store i64 %_6, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %old, ptr %0, align 8
  store i64 1, ptr %_0, align 8
  br label %bb5

bb5:                                              ; preds = %bb2, %bb4
  %1 = load i64, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  %3 = load i64, ptr %2, align 8
  %4 = insertvalue { i64, i64 } poison, i64 %1, 0
  %5 = insertvalue { i64, i64 } %4, i64 %3, 1
  ret { i64, i64 } %5
}

; _09_input_output::main
; Function Attrs: uwtable
define internal void @_ZN16_09_input_output4main17h8b64dcba5a975af2E() unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %e.i = alloca [8 x i8], align 8
  %self.i = alloca [8 x i8], align 8
  %_717 = alloca [48 x i8], align 8
  %_714 = alloca [48 x i8], align 8
  %_711 = alloca [48 x i8], align 8
  %_709 = alloca [8 x i8], align 8
  %_704 = alloca [48 x i8], align 8
  %_701 = alloca [48 x i8], align 8
  %_698 = alloca [48 x i8], align 8
  %_695 = alloca [48 x i8], align 8
  %_692 = alloca [48 x i8], align 8
  %_689 = alloca [48 x i8], align 8
  %_686 = alloca [16 x i8], align 8
  %_685 = alloca [16 x i8], align 8
  %_682 = alloca [48 x i8], align 8
  %e3 = alloca [8 x i8], align 8
  %_678 = alloca [16 x i8], align 8
  %_677 = alloca [16 x i8], align 8
  %_674 = alloca [48 x i8], align 8
  %_671 = alloca [48 x i8], align 8
  %file_content = alloca [24 x i8], align 8
  %_667 = alloca [24 x i8], align 8
  %_665 = alloca [16 x i8], align 8
  %_664 = alloca [16 x i8], align 8
  %_661 = alloca [48 x i8], align 8
  %e = alloca [8 x i8], align 8
  %_657 = alloca [48 x i8], align 8
  %_653 = alloca [8 x i8], align 8
  %_651 = alloca [48 x i8], align 8
  %_648 = alloca [48 x i8], align 8
  %_640 = alloca [4 x i8], align 4
  %_638 = alloca [16 x i8], align 8
  %_637 = alloca [16 x i8], align 8
  %_634 = alloca [48 x i8], align 8
  %_631 = alloca [16 x i8], align 8
  %_630 = alloca [16 x i8], align 8
  %_627 = alloca [48 x i8], align 8
  %_624 = alloca [16 x i8], align 8
  %_623 = alloca [16 x i8], align 8
  %_620 = alloca [48 x i8], align 8
  %_617 = alloca [16 x i8], align 8
  %_616 = alloca [16 x i8], align 8
  %_613 = alloca [48 x i8], align 8
  %_610 = alloca [48 x i8], align 8
  %inactive_users = alloca [4 x i8], align 4
  %active_users = alloca [4 x i8], align 4
  %total_users = alloca [4 x i8], align 4
  %_603 = alloca [48 x i8], align 8
  %_600 = alloca [48 x i8], align 8
  %_596 = alloca [16 x i8], align 8
  %_593 = alloca [16 x i8], align 8
  %_592 = alloca [32 x i8], align 8
  %_589 = alloca [48 x i8], align 8
  %_584 = alloca [16 x i8], align 8
  %iter2 = alloca [16 x i8], align 8
  %_576 = alloca [48 x i8], align 8
  %scores = alloca [12 x i8], align 4
  %users = alloca [48 x i8], align 8
  %_569 = alloca [48 x i8], align 8
  %_566 = alloca [48 x i8], align 8
  %_563 = alloca [48 x i8], align 8
  %_560 = alloca [48 x i8], align 8
  %_557 = alloca [48 x i8], align 8
  %_554 = alloca [48 x i8], align 8
  %_551 = alloca [48 x i8], align 8
  %_548 = alloca [48 x i8], align 8
  %_545 = alloca [48 x i8], align 8
  %_542 = alloca [48 x i8], align 8
  %_539 = alloca [48 x i8], align 8
  %_536 = alloca [16 x i8], align 8
  %_535 = alloca [16 x i8], align 8
  %_532 = alloca [48 x i8], align 8
  %result = alloca [4 x i8], align 4
  %_527 = alloca [16 x i8], align 8
  %_526 = alloca [16 x i8], align 8
  %_523 = alloca [48 x i8], align 8
  %_520 = alloca [16 x i8], align 8
  %_519 = alloca [16 x i8], align 8
  %_516 = alloca [48 x i8], align 8
  %y = alloca [4 x i8], align 4
  %x = alloca [4 x i8], align 4
  %_511 = alloca [48 x i8], align 8
  %_508 = alloca [48 x i8], align 8
  %_505 = alloca [48 x i8], align 8
  %_502 = alloca [16 x i8], align 8
  %_501 = alloca [16 x i8], align 8
  %_498 = alloca [48 x i8], align 8
  %progress = alloca [4 x i8], align 4
  %_491 = alloca [8 x i8], align 4
  %iter1 = alloca [12 x i8], align 4
  %_489 = alloca [12 x i8], align 4
  %_488 = alloca [12 x i8], align 4
  %_486 = alloca [48 x i8], align 8
  %_483 = alloca [48 x i8], align 8
  %_480 = alloca [16 x i8], align 8
  %_479 = alloca [16 x i8], align 8
  %_478 = alloca [48 x i8], align 8
  %_477 = alloca [16 x i8], align 8
  %_476 = alloca [16 x i8], align 8
  %_475 = alloca [48 x i8], align 8
  %_474 = alloca [16 x i8], align 8
  %_473 = alloca [16 x i8], align 8
  %_472 = alloca [48 x i8], align 8
  %_471 = alloca [144 x i8], align 8
  %_467 = alloca [16 x i8], align 8
  %_465 = alloca [16 x i8], align 8
  %_463 = alloca [16 x i8], align 8
  %_462 = alloca [48 x i8], align 8
  %_457 = alloca [48 x i8], align 8
  %_454 = alloca [16 x i8], align 8
  %_453 = alloca [16 x i8], align 8
  %_452 = alloca [48 x i8], align 8
  %_451 = alloca [16 x i8], align 8
  %_450 = alloca [16 x i8], align 8
  %_449 = alloca [48 x i8], align 8
  %_448 = alloca [16 x i8], align 8
  %_447 = alloca [16 x i8], align 8
  %_446 = alloca [48 x i8], align 8
  %_445 = alloca [144 x i8], align 8
  %_441 = alloca [16 x i8], align 8
  %_439 = alloca [16 x i8], align 8
  %_437 = alloca [16 x i8], align 8
  %_436 = alloca [48 x i8], align 8
  %_431 = alloca [48 x i8], align 8
  %_428 = alloca [16 x i8], align 8
  %_427 = alloca [16 x i8], align 8
  %_426 = alloca [48 x i8], align 8
  %_425 = alloca [16 x i8], align 8
  %_424 = alloca [16 x i8], align 8
  %_423 = alloca [48 x i8], align 8
  %_422 = alloca [16 x i8], align 8
  %_421 = alloca [16 x i8], align 8
  %_420 = alloca [48 x i8], align 8
  %_419 = alloca [144 x i8], align 8
  %_415 = alloca [16 x i8], align 8
  %_413 = alloca [16 x i8], align 8
  %_411 = alloca [16 x i8], align 8
  %_410 = alloca [48 x i8], align 8
  %_405 = alloca [48 x i8], align 8
  %_402 = alloca [16 x i8], align 8
  %_401 = alloca [16 x i8], align 8
  %_400 = alloca [48 x i8], align 8
  %_399 = alloca [16 x i8], align 8
  %_398 = alloca [16 x i8], align 8
  %_397 = alloca [48 x i8], align 8
  %_396 = alloca [16 x i8], align 8
  %_395 = alloca [16 x i8], align 8
  %_394 = alloca [48 x i8], align 8
  %_393 = alloca [144 x i8], align 8
  %_389 = alloca [16 x i8], align 8
  %_387 = alloca [16 x i8], align 8
  %_385 = alloca [16 x i8], align 8
  %_384 = alloca [48 x i8], align 8
  %_379 = alloca [48 x i8], align 8
  %_376 = alloca [16 x i8], align 8
  %_375 = alloca [16 x i8], align 8
  %_374 = alloca [48 x i8], align 8
  %_373 = alloca [16 x i8], align 8
  %_372 = alloca [16 x i8], align 8
  %_371 = alloca [48 x i8], align 8
  %_370 = alloca [16 x i8], align 8
  %_369 = alloca [16 x i8], align 8
  %_368 = alloca [48 x i8], align 8
  %_367 = alloca [144 x i8], align 8
  %_363 = alloca [16 x i8], align 8
  %_361 = alloca [16 x i8], align 8
  %_359 = alloca [16 x i8], align 8
  %_358 = alloca [48 x i8], align 8
  %_353 = alloca [48 x i8], align 8
  %_350 = alloca [48 x i8], align 8
  %_347 = alloca [48 x i8], align 8
  %_344 = alloca [48 x i8], align 8
  %_341 = alloca [48 x i8], align 8
  %_338 = alloca [48 x i8], align 8
  %_335 = alloca [48 x i8], align 8
  %_332 = alloca [48 x i8], align 8
  %_329 = alloca [48 x i8], align 8
  %_326 = alloca [48 x i8], align 8
  %_323 = alloca [16 x i8], align 8
  %_321 = alloca [16 x i8], align 8
  %_320 = alloca [32 x i8], align 8
  %_317 = alloca [48 x i8], align 8
  %error_msg = alloca [16 x i8], align 8
  %error_code = alloca [16 x i8], align 8
  %_312 = alloca [48 x i8], align 8
  %_309 = alloca [48 x i8], align 8
  %_306 = alloca [48 x i8], align 8
  %_303 = alloca [48 x i8], align 8
  %_300 = alloca [48 x i8], align 8
  %_297 = alloca [48 x i8], align 8
  %_294 = alloca [48 x i8], align 8
  %_291 = alloca [48 x i8], align 8
  %_288 = alloca [48 x i8], align 8
  %_285 = alloca [48 x i8], align 8
  %_282 = alloca [48 x i8], align 8
  %_279 = alloca [48 x i8], align 8
  %_276 = alloca [48 x i8], align 8
  %_273 = alloca [48 x i8], align 8
  %_270 = alloca [48 x i8], align 8
  %_267 = alloca [48 x i8], align 8
  %_264 = alloca [48 x i8], align 8
  %_261 = alloca [16 x i8], align 8
  %_260 = alloca [16 x i8], align 8
  %_257 = alloca [48 x i8], align 8
  %room_area = alloca [4 x i8], align 4
  %_253 = alloca [48 x i8], align 8
  %_250 = alloca [48 x i8], align 8
  %_247 = alloca [48 x i8], align 8
  %_244 = alloca [16 x i8], align 8
  %_243 = alloca [16 x i8], align 8
  %_242 = alloca [48 x i8], align 8
  %_241 = alloca [48 x i8], align 8
  %_237 = alloca [16 x i8], align 8
  %_236 = alloca [16 x i8], align 8
  %_231 = alloca [48 x i8], align 8
  %_228 = alloca [16 x i8], align 8
  %_227 = alloca [16 x i8], align 8
  %_226 = alloca [48 x i8], align 8
  %_225 = alloca [48 x i8], align 8
  %_221 = alloca [16 x i8], align 8
  %_220 = alloca [16 x i8], align 8
  %_215 = alloca [48 x i8], align 8
  %_212 = alloca [16 x i8], align 8
  %_211 = alloca [16 x i8], align 8
  %_208 = alloca [48 x i8], align 8
  %_205 = alloca [16 x i8], align 8
  %_204 = alloca [16 x i8], align 8
  %_201 = alloca [48 x i8], align 8
  %_198 = alloca [48 x i8], align 8
  %total = alloca [8 x i8], align 8
  %unit_price = alloca [8 x i8], align 8
  %quantity = alloca [4 x i8], align 4
  %product = alloca [16 x i8], align 8
  %_190 = alloca [48 x i8], align 8
  %_187 = alloca [48 x i8], align 8
  %_184 = alloca [48 x i8], align 8
  %_181 = alloca [48 x i8], align 8
  %_177 = alloca [48 x i8], align 8
  %_173 = alloca [48 x i8], align 8
  %_169 = alloca [48 x i8], align 8
  %_165 = alloca [16 x i8], align 8
  %_164 = alloca [16 x i8], align 8
  %_161 = alloca [48 x i8], align 8
  %counter = alloca [4 x i8], align 4
  %_155 = alloca [16 x i8], align 8
  %_154 = alloca [16 x i8], align 8
  %_151 = alloca [48 x i8], align 8
  %i = alloca [4 x i8], align 4
  %_146 = alloca [8 x i8], align 4
  %iter = alloca [12 x i8], align 4
  %_144 = alloca [12 x i8], align 4
  %_143 = alloca [12 x i8], align 4
  %_141 = alloca [48 x i8], align 8
  %_138 = alloca [48 x i8], align 8
  %_133 = alloca [16 x i8], align 8
  %_132 = alloca [16 x i8], align 8
  %_129 = alloca [48 x i8], align 8
  %_124 = alloca [16 x i8], align 8
  %_123 = alloca [16 x i8], align 8
  %_120 = alloca [48 x i8], align 8
  %_115 = alloca [16 x i8], align 8
  %_114 = alloca [16 x i8], align 8
  %_111 = alloca [48 x i8], align 8
  %_106 = alloca [16 x i8], align 8
  %_105 = alloca [16 x i8], align 8
  %_102 = alloca [48 x i8], align 8
  %_99 = alloca [48 x i8], align 8
  %names = alloca [48 x i8], align 8
  %numbers = alloca [20 x i8], align 4
  %_92 = alloca [48 x i8], align 8
  %_90 = alloca [1 x i8], align 1
  %_88 = alloca [16 x i8], align 8
  %_87 = alloca [16 x i8], align 8
  %_84 = alloca [48 x i8], align 8
  %_81 = alloca [4 x i8], align 4
  %_79 = alloca [16 x i8], align 8
  %_78 = alloca [16 x i8], align 8
  %_75 = alloca [48 x i8], align 8
  %_72 = alloca [4 x i8], align 4
  %_70 = alloca [16 x i8], align 8
  %_69 = alloca [16 x i8], align 8
  %_66 = alloca [48 x i8], align 8
  %_63 = alloca [48 x i8], align 8
  %_58 = alloca [48 x i8], align 8
  %_55 = alloca [16 x i8], align 8
  %_54 = alloca [16 x i8], align 8
  %_51 = alloca [48 x i8], align 8
  %_48 = alloca [16 x i8], align 8
  %_47 = alloca [16 x i8], align 8
  %_44 = alloca [48 x i8], align 8
  %_41 = alloca [16 x i8], align 8
  %_40 = alloca [16 x i8], align 8
  %_37 = alloca [48 x i8], align 8
  %_34 = alloca [16 x i8], align 8
  %_33 = alloca [16 x i8], align 8
  %_30 = alloca [48 x i8], align 8
  %_27 = alloca [48 x i8], align 8
  %active = alloca [1 x i8], align 1
  %salary = alloca [8 x i8], align 8
  %age = alloca [4 x i8], align 4
  %name = alloca [16 x i8], align 8
  %_20 = alloca [48 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %_14 = alloca [48 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_c3e061720ebbdacd35aec2f418194dc3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_805ff2639b6981ce8ad51d0c1645c415)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_73c3f88632bc92f9edb5547b1f3ddcf7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_81ece93ea33556630ac5b0794a2e3d7b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_17, ptr align 8 @alloc_8cc1c97804ef8846064004743e2ef597)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_17)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_20, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_20)
  store ptr @alloc_b468ef00fa5916b1335583d25c8252ad, ptr %name, align 8
  %0 = getelementptr inbounds i8, ptr %name, i64 8
  store i64 5, ptr %0, align 8
  store i32 25, ptr %age, align 4
  store double 5.000500e+03, ptr %salary, align 8
  store i8 1, ptr %active, align 1
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_27, ptr align 8 @alloc_485fa2c17771af57e2a313186610a1af)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_27)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_34, ptr align 8 %name)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_33, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_34, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_30, ptr align 8 @alloc_cd99b202573048149e4ac0d7df184409, ptr align 8 %_33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_30)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_41, ptr align 4 %age)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_40, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_41, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_37, ptr align 8 @alloc_c8d066cadf1ba35bf631428b9f61b62c, ptr align 8 %_40)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_37)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3b2964d79eb00dabE(ptr sret([16 x i8]) align 8 %_48, ptr align 8 %salary)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_47, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_48, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_44, ptr align 8 @alloc_828475e8994c9a2156e0be0dce462ed4, ptr align 8 %_47)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_44)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h330ca224aea349b9E(ptr sret([16 x i8]) align 8 %_55, ptr align 1 %active)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_54, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_55, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_51, ptr align 8 @alloc_c5ed655fc10e235cc96c31418ed2f1c1, ptr align 8 %_54)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_51)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_58, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_58)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_63, ptr align 8 @alloc_4ae04cd9dc27ff4823c35eeaadfd85d9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_63)
  %5 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 10, i32 5)
  %_73.0 = extractvalue { i32, i1 } %5, 0
  %_73.1 = extractvalue { i32, i1 } %5, 1
  br i1 %_73.1, label %panic, label %bb33

bb33:                                             ; preds = %start
  store i32 %_73.0, ptr %_72, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_70, ptr align 4 %_72)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_69, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_70, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_66, ptr align 8 @alloc_67b556d4697490fde72675e6c5b1cf2a, ptr align 8 %_69)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_66)
  %7 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 10, i32 5)
  %_82.0 = extractvalue { i32, i1 } %7, 0
  %_82.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_82.1, label %panic4, label %bb37

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_933ff4ab8fb15dec9fd11b245e4cef35) #13
  unreachable

bb37:                                             ; preds = %bb33
  store i32 %_82.0, ptr %_81, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_79, ptr align 4 %_81)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_78, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_79, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_75, ptr align 8 @alloc_8fd1e52dbef5c4d413d27adb1634112a, ptr align 8 %_78)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_75)
  store i8 1, ptr %_90, align 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h330ca224aea349b9E(ptr sret([16 x i8]) align 8 %_88, ptr align 1 %_90)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_87, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_88, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_84, ptr align 8 @alloc_7b6728db17d1974cc3d35482a11fa539, ptr align 8 %_87)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_84)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_92, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_92)
  %10 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
  store i32 1, ptr %10, align 4
  %11 = getelementptr inbounds nuw i32, ptr %numbers, i64 1
  store i32 2, ptr %11, align 4
  %12 = getelementptr inbounds nuw i32, ptr %numbers, i64 2
  store i32 3, ptr %12, align 4
  %13 = getelementptr inbounds nuw i32, ptr %numbers, i64 3
  store i32 4, ptr %13, align 4
  %14 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
  store i32 5, ptr %14, align 4
  %15 = getelementptr inbounds nuw { ptr, i64 }, ptr %names, i64 0
  store ptr @alloc_a118b2b99663e794e1f4a92820d83446, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 3, ptr %16, align 8
  %17 = getelementptr inbounds nuw { ptr, i64 }, ptr %names, i64 1
  store ptr @alloc_8a3ff4f0306cccf00c85887c84f94191, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %17, i64 8
  store i64 4, ptr %18, align 8
  %19 = getelementptr inbounds nuw { ptr, i64 }, ptr %names, i64 2
  store ptr @alloc_da176e364e2c37a043e0b0bf98f57c2b, ptr %19, align 8
  %20 = getelementptr inbounds i8, ptr %19, i64 8
  store i64 4, ptr %20, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_99, ptr align 8 @alloc_3d5ffcf2d85f12c5f29f4bb6f05da63d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_99)
  %_107 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_106, ptr align 4 %_107)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_105, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_106, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_102, ptr align 8 @alloc_ede98dbfb88e192dbe6887052b52c4b0, ptr align 8 %_105)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_102)
  %_116 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_115, ptr align 4 %_116)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_114, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_115, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_111, ptr align 8 @alloc_1e01683ddb04f11db4c3485d7ead6feb, ptr align 8 %_114)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_111)
  %_125 = getelementptr inbounds nuw { ptr, i64 }, ptr %names, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_124, ptr align 8 %_125)
  %23 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_123, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %23, ptr align 8 %_124, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_120, ptr align 8 @alloc_2bc058ba28e18b154f47b76e97a52f54, ptr align 8 %_123)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_120)
  %_134 = getelementptr inbounds nuw { ptr, i64 }, ptr %names, i64 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_133, ptr align 8 %_134)
  %24 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_132, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %24, ptr align 8 %_133, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_129, ptr align 8 @alloc_9766be8e007a1167a20c3d088b19674d, ptr align 8 %_132)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_129)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_138, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_138)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_141, ptr align 8 @alloc_78aa2fdf2ef0958f5275162238d836c7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_141)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hc8ba31cfeea827baE"(ptr sret([12 x i8]) align 4 %_144, i32 1, i32 5)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hda20e4209b3ab305E"(ptr sret([12 x i8]) align 4 %_143, ptr align 4 %_144)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter, ptr align 4 %_143, i64 12, i1 false)
  br label %bb70

panic4:                                           ; preds = %bb33
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_3dd01e17664da0064953556811c2c1ac) #13
  unreachable

bb70:                                             ; preds = %bb73, %bb37
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %25 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hdf8ee135f37d6549E"(ptr align 4 %iter)
  %26 = extractvalue { i32, i32 } %25, 0
  %27 = extractvalue { i32, i32 } %25, 1
  store i32 %26, ptr %_146, align 4
  %28 = getelementptr inbounds i8, ptr %_146, i64 4
  store i32 %27, ptr %28, align 4
  %29 = load i32, ptr %_146, align 4
  %30 = getelementptr inbounds i8, ptr %_146, i64 4
  %31 = load i32, ptr %30, align 4
  %_148 = zext i32 %29 to i64
  %32 = trunc nuw i64 %_148 to i1
  br i1 %32, label %bb73, label %bb74

bb73:                                             ; preds = %bb70
  %33 = getelementptr inbounds i8, ptr %_146, i64 4
  %34 = load i32, ptr %33, align 4
  store i32 %34, ptr %i, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_155, ptr align 4 %i)
  %35 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_154, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %35, ptr align 8 %_155, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_151, ptr align 8 @alloc_f72ca9a573e1f3362fa118e901f1fed1, ptr align 8 %_154)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_151)
  br label %bb70

bb74:                                             ; preds = %bb70
  store i32 1, ptr %counter, align 4
  br label %bb77

bb77:                                             ; preds = %bb82, %bb74
  %_159 = load i32, ptr %counter, align 4
  %_158 = icmp sle i32 %_159, 3
  br i1 %_158, label %bb78, label %bb83

bb83:                                             ; preds = %bb77
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_169, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_169)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_173, ptr align 8 @alloc_3a020404d831ddb266f8ee21ad441c83)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_173)
  br label %bb90

bb78:                                             ; preds = %bb77
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_165, ptr align 4 %counter)
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_164, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_165, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_161, ptr align 8 @alloc_1a81a0ffa1c9f25f28c8a1a7e4aa0a21, ptr align 8 %_164)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_161)
  %37 = load i32, ptr %counter, align 4
  %38 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %37, i32 1)
  %_167.0 = extractvalue { i32, i1 } %38, 0
  %_167.1 = extractvalue { i32, i1 } %38, 1
  br i1 %_167.1, label %panic18, label %bb82

bb90:                                             ; preds = %bb83
  br label %bb91

bb91:                                             ; preds = %bb90
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_181, ptr align 8 @alloc_97ff94fc2a2ef58145964554ece39dc5)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_181)
  br label %bb95

bb93:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_184, ptr align 8 @alloc_5784c00ff35f9f0cce9c19d935203fb1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_184)
  br label %bb95

bb95:                                             ; preds = %bb88, %bb91, %bb93
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_187, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_187)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_190, ptr align 8 @alloc_4fe744e4e492b99653634ac8d0da54ca)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_190)
  store ptr @alloc_359b7dc4bc3bfd93aae4f476e001fa3b, ptr %product, align 8
  %39 = getelementptr inbounds i8, ptr %product, i64 8
  store i64 6, ptr %39, align 8
  store i32 2, ptr %quantity, align 4
  store double 1.299990e+03, ptr %unit_price, align 8
  %40 = load i32, ptr %quantity, align 4
  %_196 = sitofp i32 %40 to double
  %41 = load double, ptr %unit_price, align 8
  %42 = fmul double %_196, %41
  store double %42, ptr %total, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_198, ptr align 8 @alloc_a8458c4b95a0fce5f585d40ae5719977)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_198)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_205, ptr align 8 %product)
  %43 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_204, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %43, ptr align 8 %_205, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_201, ptr align 8 @alloc_5a4976cd5630db43316aecd3ccde6279, ptr align 8 %_204)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_201)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_212, ptr align 4 %quantity)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_211, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_212, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_208, ptr align 8 @alloc_917729db758871874fa4dcb3ec4c460b, ptr align 8 %_211)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_208)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3b2964d79eb00dabE(ptr sret([16 x i8]) align 8 %_221, ptr align 8 %unit_price)
  %45 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_220, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %45, ptr align 8 %_221, i64 16, i1 false)
  %46 = getelementptr inbounds i8, ptr %_227, i64 2
  store i16 2, ptr %46, align 2
  store i16 0, ptr %_227, align 8
  store i16 2, ptr %_228, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_226, i64 0, i32 -268435424, ptr align 8 %_227, ptr align 8 %_228)
  %47 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_225, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %47, ptr align 8 %_226, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_215, ptr align 8 @alloc_486ee92fa4c34c397a15696871c71440, i64 2, ptr align 8 %_220, i64 1, ptr align 8 %_225, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_215)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3b2964d79eb00dabE(ptr sret([16 x i8]) align 8 %_237, ptr align 8 %total)
  %48 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_236, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %48, ptr align 8 %_237, i64 16, i1 false)
  %49 = getelementptr inbounds i8, ptr %_243, i64 2
  store i16 2, ptr %49, align 2
  store i16 0, ptr %_243, align 8
  store i16 2, ptr %_244, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_242, i64 0, i32 -268435424, ptr align 8 %_243, ptr align 8 %_244)
  %50 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_241, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %50, ptr align 8 %_242, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_231, ptr align 8 @alloc_fd1d874d5e4f19c1a2253bab3e592ab8, i64 2, ptr align 8 %_236, i64 1, ptr align 8 %_241, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_231)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_247, ptr align 8 @alloc_4e2bd1292c06ad40c42e950226c30848)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_247)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_250, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_250)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_253, ptr align 8 @alloc_765d87e7ac92e9e0b0879f8312a94ebf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_253)
; call _09_input_output::calculate_area
  %51 = call i32 @_ZN16_09_input_output14calculate_area17h7d67b8432eb71e21E(i32 12, i32 8)
  store i32 %51, ptr %room_area, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_261, ptr align 4 %room_area)
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_260, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_261, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_257, ptr align 8 @alloc_055f882ec0a68cc9edbe8416b2bfff19, ptr align 8 %_260)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_257)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_264, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_264)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_267, ptr align 8 @alloc_3a207195f10926972cbcd1b972a06645)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_267)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_270, ptr align 8 @alloc_082464557d449b38b557963fea9ba625)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_270)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_273, ptr align 8 @alloc_903bb3d7b5aca6273efac4a74b85c029)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_273)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_276, ptr align 8 @alloc_8a64ab18a349b89c21e50949abf512d1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_276)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_279, ptr align 8 @alloc_62864721e615d98884de57efc7309d65)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_279)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_282, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_282)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_285, ptr align 8 @alloc_8e0012e88499f35bbf28d5fd60e27ba3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_285)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_288, ptr align 8 @alloc_6bc61fc06a2fab620a781dc864de6052)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_288)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_291, ptr align 8 @alloc_586bf349966591063ad25b556f0670e6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_291)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_294, ptr align 8 @alloc_388802831c9d5ff708fff8aef763d8a3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_294)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_297, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_297)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_300, ptr align 8 @alloc_7dfdc93c86623f2d0997a58cf9538408)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_300)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_303, ptr align 8 @alloc_64b9705d0826e8969a0bdf9e1c5597d9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_303)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_306, ptr align 8 @alloc_1aecdb82084e035877a62a50d2bf474f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_306)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_309, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_309)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_312, ptr align 8 @alloc_23713c3ab34fba4056a3015b130ec045)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_312)
  store ptr @alloc_36595d6b9b28825e4c9229f813f36c56, ptr %error_code, align 8
  %53 = getelementptr inbounds i8, ptr %error_code, i64 8
  store i64 3, ptr %53, align 8
  store ptr @alloc_46701e9604e3f3dcda75663c1d13ead1, ptr %error_msg, align 8
  %54 = getelementptr inbounds i8, ptr %error_msg, i64 8
  store i64 14, ptr %54, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_321, ptr align 8 %error_code)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_323, ptr align 8 %error_msg)
  %55 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_320, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %55, ptr align 8 %_321, i64 16, i1 false)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_320, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_323, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h7a72f3daa34490b0E(ptr sret([48 x i8]) align 8 %_317, ptr align 8 @alloc_0eeef3e7ac8e296725bc8c49acedc3c5, ptr align 8 %_320)
; call std::io::stdio::_eprint
  call void @_ZN3std2io5stdio7_eprint17h9e95d6085ad392ebE(ptr align 8 %_317)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_326, ptr align 8 @alloc_c283654c67423f8d2af52893954fe3db)
; call std::io::stdio::_eprint
  call void @_ZN3std2io5stdio7_eprint17h9e95d6085ad392ebE(ptr align 8 %_326)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_329, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_329)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_332, ptr align 8 @alloc_d920a6dd3added7a13211fc30fa90b58)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_332)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_335, ptr align 8 @alloc_44062cd7068d6796ca36d315fc5e2d2f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_335)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_338, ptr align 8 @alloc_13fd9d667a364a7eb2f52d4fca397dbe)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_338)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_341, ptr align 8 @alloc_d062f17ca78d9e7c3c14097c56f9eb55)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_341)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_344, ptr align 8 @alloc_40ec521de9aa644eae2e0a4b8c1d1c48)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_344)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_347, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_347)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_350, ptr align 8 @alloc_dd8903d6cbfd70b78cdf545df4e702d1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_350)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_359, ptr align 8 @alloc_e5ce79e13152ffbb6c0fd479fc5cb7e1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_361, ptr align 8 @alloc_6c096ef680caa061837220a81c45c3c2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_363, ptr align 8 @alloc_87eb6937e9b0b11eb3cea5f19ff8bfff)
  %57 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_358, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %57, ptr align 8 %_359, i64 16, i1 false)
  %58 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_358, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %58, ptr align 8 %_361, i64 16, i1 false)
  %59 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_358, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %59, ptr align 8 %_363, i64 16, i1 false)
  store i16 2, ptr %_369, align 8
  %60 = getelementptr inbounds i8, ptr %_370, i64 2
  store i16 10, ptr %60, align 2
  store i16 0, ptr %_370, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_368, i64 0, i32 -2013265888, ptr align 8 %_369, ptr align 8 %_370)
  store i16 2, ptr %_372, align 8
  %61 = getelementptr inbounds i8, ptr %_373, i64 2
  store i16 3, ptr %61, align 2
  store i16 0, ptr %_373, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_371, i64 1, i32 -2013265888, ptr align 8 %_372, ptr align 8 %_373)
  store i16 2, ptr %_375, align 8
  %62 = getelementptr inbounds i8, ptr %_376, i64 2
  store i16 10, ptr %62, align 2
  store i16 0, ptr %_376, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_374, i64 2, i32 -2013265888, ptr align 8 %_375, ptr align 8 %_376)
  %63 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_367, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %63, ptr align 8 %_368, i64 48, i1 false)
  %64 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_367, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %64, ptr align 8 %_371, i64 48, i1 false)
  %65 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_367, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %65, ptr align 8 %_374, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_353, ptr align 8 @alloc_aa2e0d3624ba64e906307f087624c174, i64 4, ptr align 8 %_358, i64 3, ptr align 8 %_367, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_353)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_385, ptr align 8 @alloc_eab5d04767146d7d9b93b60d28ef530a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_387, ptr align 8 @alloc_eab5d04767146d7d9b93b60d28ef530a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_389, ptr align 8 @alloc_eab5d04767146d7d9b93b60d28ef530a)
  %66 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_384, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %66, ptr align 8 %_385, i64 16, i1 false)
  %67 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_384, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %67, ptr align 8 %_387, i64 16, i1 false)
  %68 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_384, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %68, ptr align 8 %_389, i64 16, i1 false)
  store i16 2, ptr %_395, align 8
  %69 = getelementptr inbounds i8, ptr %_396, i64 2
  store i16 10, ptr %69, align 2
  store i16 0, ptr %_396, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_394, i64 0, i32 -2013265875, ptr align 8 %_395, ptr align 8 %_396)
  store i16 2, ptr %_398, align 8
  %70 = getelementptr inbounds i8, ptr %_399, i64 2
  store i16 3, ptr %70, align 2
  store i16 0, ptr %_399, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_397, i64 1, i32 -2013265875, ptr align 8 %_398, ptr align 8 %_399)
  store i16 2, ptr %_401, align 8
  %71 = getelementptr inbounds i8, ptr %_402, i64 2
  store i16 10, ptr %71, align 2
  store i16 0, ptr %_402, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_400, i64 2, i32 -2013265875, ptr align 8 %_401, ptr align 8 %_402)
  %72 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_393, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %72, ptr align 8 %_394, i64 48, i1 false)
  %73 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_393, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %73, ptr align 8 %_397, i64 48, i1 false)
  %74 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_393, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %74, ptr align 8 %_400, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_379, ptr align 8 @alloc_a985d56ac986e481bfd643faa099cd3f, i64 4, ptr align 8 %_384, i64 3, ptr align 8 %_393, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_379)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_411, ptr align 8 @alloc_5bdf718b424cb792ca3cbf634962adb7)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_413, ptr align 8 @alloc_0cbf628e89013526399bbd4e8b99526e)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_415, ptr align 8 @alloc_9142414879623d4e4d7aa9d73a44bd16)
  %75 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_410, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %75, ptr align 8 %_411, i64 16, i1 false)
  %76 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_410, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %76, ptr align 8 %_413, i64 16, i1 false)
  %77 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_410, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %77, ptr align 8 %_415, i64 16, i1 false)
  store i16 2, ptr %_421, align 8
  %78 = getelementptr inbounds i8, ptr %_422, i64 2
  store i16 10, ptr %78, align 2
  store i16 0, ptr %_422, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_420, i64 0, i32 -2013265888, ptr align 8 %_421, ptr align 8 %_422)
  store i16 2, ptr %_424, align 8
  %79 = getelementptr inbounds i8, ptr %_425, i64 2
  store i16 3, ptr %79, align 2
  store i16 0, ptr %_425, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_423, i64 1, i32 -2013265888, ptr align 8 %_424, ptr align 8 %_425)
  store i16 2, ptr %_427, align 8
  %80 = getelementptr inbounds i8, ptr %_428, i64 2
  store i16 10, ptr %80, align 2
  store i16 0, ptr %_428, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_426, i64 2, i32 -2013265888, ptr align 8 %_427, ptr align 8 %_428)
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_419, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_420, i64 48, i1 false)
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_419, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_423, i64 48, i1 false)
  %83 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_419, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %83, ptr align 8 %_426, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_405, ptr align 8 @alloc_aa2e0d3624ba64e906307f087624c174, i64 4, ptr align 8 %_410, i64 3, ptr align 8 %_419, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_405)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_437, ptr align 8 @alloc_7e186732ac075cfc40a0cd21f3f42305)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_439, ptr align 8 @alloc_2ddcc46b5a700442bf5d4ab2d617d9bb)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_441, ptr align 8 @alloc_af52064429eeca105700498c62b17f2d)
  %84 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_436, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %84, ptr align 8 %_437, i64 16, i1 false)
  %85 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_436, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %85, ptr align 8 %_439, i64 16, i1 false)
  %86 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_436, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %86, ptr align 8 %_441, i64 16, i1 false)
  store i16 2, ptr %_447, align 8
  %87 = getelementptr inbounds i8, ptr %_448, i64 2
  store i16 10, ptr %87, align 2
  store i16 0, ptr %_448, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_446, i64 0, i32 -2013265888, ptr align 8 %_447, ptr align 8 %_448)
  store i16 2, ptr %_450, align 8
  %88 = getelementptr inbounds i8, ptr %_451, i64 2
  store i16 3, ptr %88, align 2
  store i16 0, ptr %_451, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_449, i64 1, i32 -2013265888, ptr align 8 %_450, ptr align 8 %_451)
  store i16 2, ptr %_453, align 8
  %89 = getelementptr inbounds i8, ptr %_454, i64 2
  store i16 10, ptr %89, align 2
  store i16 0, ptr %_454, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_452, i64 2, i32 -2013265888, ptr align 8 %_453, ptr align 8 %_454)
  %90 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_445, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %90, ptr align 8 %_446, i64 48, i1 false)
  %91 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_445, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %91, ptr align 8 %_449, i64 48, i1 false)
  %92 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_445, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %92, ptr align 8 %_452, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_431, ptr align 8 @alloc_aa2e0d3624ba64e906307f087624c174, i64 4, ptr align 8 %_436, i64 3, ptr align 8 %_445, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_431)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_463, ptr align 8 @alloc_a45c339e1376d9d8acdc5100b100f554)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_465, ptr align 8 @alloc_ed0db35e8673a7bcf95eed760e24378f)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_467, ptr align 8 @alloc_825c34bdd7b9b2ff5db60e5a2656d138)
  %93 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_462, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %93, ptr align 8 %_463, i64 16, i1 false)
  %94 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_462, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %94, ptr align 8 %_465, i64 16, i1 false)
  %95 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_462, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %95, ptr align 8 %_467, i64 16, i1 false)
  store i16 2, ptr %_473, align 8
  %96 = getelementptr inbounds i8, ptr %_474, i64 2
  store i16 10, ptr %96, align 2
  store i16 0, ptr %_474, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_472, i64 0, i32 -2013265888, ptr align 8 %_473, ptr align 8 %_474)
  store i16 2, ptr %_476, align 8
  %97 = getelementptr inbounds i8, ptr %_477, i64 2
  store i16 3, ptr %97, align 2
  store i16 0, ptr %_477, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_475, i64 1, i32 -2013265888, ptr align 8 %_476, ptr align 8 %_477)
  store i16 2, ptr %_479, align 8
  %98 = getelementptr inbounds i8, ptr %_480, i64 2
  store i16 10, ptr %98, align 2
  store i16 0, ptr %_480, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h86e0bc4460992ac6E(ptr sret([48 x i8]) align 8 %_478, i64 2, i32 -2013265888, ptr align 8 %_479, ptr align 8 %_480)
  %99 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_471, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %99, ptr align 8 %_472, i64 48, i1 false)
  %100 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_471, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %100, ptr align 8 %_475, i64 48, i1 false)
  %101 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_471, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %101, ptr align 8 %_478, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h9975ce53941b6a18E()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h4f759cb87c4ff987E(ptr sret([48 x i8]) align 8 %_457, ptr align 8 @alloc_aa2e0d3624ba64e906307f087624c174, i64 4, ptr align 8 %_462, i64 3, ptr align 8 %_471, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_457)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_483, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_483)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_486, ptr align 8 @alloc_4e4b3e0b123465db16c5cfe680767665)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_486)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hc8ba31cfeea827baE"(ptr sret([12 x i8]) align 4 %_489, i32 0, i32 5)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hda20e4209b3ab305E"(ptr sret([12 x i8]) align 4 %_488, ptr align 4 %_489)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter1, ptr align 4 %_488, i64 12, i1 false)
  br label %bb235

bb88:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_177, ptr align 8 @alloc_a7c9335b38e7effff7e3d8a2b29a68d6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_177)
  br label %bb95

bb235:                                            ; preds = %bb239, %bb95
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %102 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hdf8ee135f37d6549E"(ptr align 4 %iter1)
  %103 = extractvalue { i32, i32 } %102, 0
  %104 = extractvalue { i32, i32 } %102, 1
  store i32 %103, ptr %_491, align 4
  %105 = getelementptr inbounds i8, ptr %_491, i64 4
  store i32 %104, ptr %105, align 4
  %106 = load i32, ptr %_491, align 4
  %107 = getelementptr inbounds i8, ptr %_491, i64 4
  %108 = load i32, ptr %107, align 4
  %_493 = zext i32 %106 to i64
  %109 = trunc nuw i64 %_493 to i1
  br i1 %109, label %bb237, label %bb238

bb237:                                            ; preds = %bb235
  %110 = getelementptr inbounds i8, ptr %_491, i64 4
  %i16 = load i32, ptr %110, align 4
  %111 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %i16, i32 20)
  %_496.0 = extractvalue { i32, i1 } %111, 0
  %_496.1 = extractvalue { i32, i1 } %111, 1
  br i1 %_496.1, label %panic17, label %bb239

bb238:                                            ; preds = %bb235
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_505, ptr align 8 @alloc_6435c432427367cb5f3a0a820cf4849a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_505)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_508, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_508)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_511, ptr align 8 @alloc_c3a212f4a106eb63eaae2d9337b7ccdc)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_511)
  store i32 10, ptr %x, align 4
  store i32 5, ptr %y, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_520, ptr align 4 %x)
  %112 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_519, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %112, ptr align 8 %_520, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_516, ptr align 8 @alloc_731b0cccf2ee2689857c103cf868dc19, ptr align 8 %_519)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_516)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_527, ptr align 4 %y)
  %113 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_526, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %113, ptr align 8 %_527, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_523, ptr align 8 @alloc_e6f6d49181b11a1ce46abdf9c3e8176d, ptr align 8 %_526)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_523)
  %114 = load i32, ptr %x, align 4
  %115 = load i32, ptr %y, align 4
  %116 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %114, i32 %115)
  %_530.0 = extractvalue { i32, i1 } %116, 0
  %_530.1 = extractvalue { i32, i1 } %116, 1
  br i1 %_530.1, label %panic5, label %bb254

bb254:                                            ; preds = %bb238
  store i32 %_530.0, ptr %result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_536, ptr align 4 %result)
  %117 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_535, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %117, ptr align 8 %_536, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_532, ptr align 8 @alloc_dcef080d2dece423b893c7f7420e1e0e, ptr align 8 %_535)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_532)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_539, ptr align 8 @alloc_42fd79be5b99df107783671694aa83fb)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_539)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_542, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_542)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_545, ptr align 8 @alloc_d78a203f293e89e0225d313f148c0473)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_545)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_548, ptr align 8 @alloc_95dea72bf2093ed094e0c292ab361599)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_548)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_551, ptr align 8 @alloc_8e12fef1d9e53c91a5b67b7aaf239a6c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_551)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_554, ptr align 8 @alloc_ab91f66a0cc4676a20d2f8ab0e173b41)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_554)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_557, ptr align 8 @alloc_89b5efa7d5670ac10081973608ca27ce)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_557)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_560, ptr align 8 @alloc_0bca21b0b160e31124deecde4f7a6785)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_560)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_563, ptr align 8 @alloc_4e2bd1292c06ad40c42e950226c30848)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_563)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_566, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_566)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_569, ptr align 8 @alloc_3468b979ca58c790a6b8c481647f5d15)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_569)
  %118 = getelementptr inbounds nuw { ptr, i64 }, ptr %users, i64 0
  store ptr @alloc_f876b28ff895ac67fe3c6bfc02a403c9, ptr %118, align 8
  %119 = getelementptr inbounds i8, ptr %118, i64 8
  store i64 5, ptr %119, align 8
  %120 = getelementptr inbounds nuw { ptr, i64 }, ptr %users, i64 1
  store ptr @alloc_9ac483a2dbb643edcf3d4ff37d31b3e4, ptr %120, align 8
  %121 = getelementptr inbounds i8, ptr %120, i64 8
  store i64 5, ptr %121, align 8
  %122 = getelementptr inbounds nuw { ptr, i64 }, ptr %users, i64 2
  store ptr @alloc_30e3e1d2fc714fecf2e248d9fa5fbc01, ptr %122, align 8
  %123 = getelementptr inbounds i8, ptr %122, i64 8
  store i64 5, ptr %123, align 8
  %124 = getelementptr inbounds nuw i32, ptr %scores, i64 0
  store i32 100, ptr %124, align 4
  %125 = getelementptr inbounds nuw i32, ptr %scores, i64 1
  store i32 85, ptr %125, align 4
  %126 = getelementptr inbounds nuw i32, ptr %scores, i64 2
  store i32 45, ptr %126, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_576, ptr align 8 @alloc_fa0f75cc9638dd86f443c61bd4fd2b5b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_576)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %127 = call { i64, i64 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3fcde743b1928163E"(i64 0, i64 3)
  %_578.0 = extractvalue { i64, i64 } %127, 0
  %_578.1 = extractvalue { i64, i64 } %127, 1
  store i64 %_578.0, ptr %iter2, align 8
  %128 = getelementptr inbounds i8, ptr %iter2, i64 8
  store i64 %_578.1, ptr %128, align 8
  br label %bb283

panic5:                                           ; preds = %bb238
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_93c4c6f26d8c260e3edcb53dd359aa1d) #13
  unreachable

bb283:                                            ; preds = %bb289, %bb254
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
  %129 = call { i64, i64 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h972d3d0a27183a77E"(ptr align 8 %iter2)
  %130 = extractvalue { i64, i64 } %129, 0
  %131 = extractvalue { i64, i64 } %129, 1
  store i64 %130, ptr %_584, align 8
  %132 = getelementptr inbounds i8, ptr %_584, i64 8
  store i64 %131, ptr %132, align 8
  %_586 = load i64, ptr %_584, align 8
  %133 = getelementptr inbounds i8, ptr %_584, i64 8
  %134 = load i64, ptr %133, align 8
  %135 = trunc nuw i64 %_586 to i1
  br i1 %135, label %bb285, label %bb286

bb285:                                            ; preds = %bb283
  %136 = getelementptr inbounds i8, ptr %_584, i64 8
  %i13 = load i64, ptr %136, align 8
  %_595 = icmp ult i64 %i13, 3
  br i1 %_595, label %bb287, label %panic14

bb286:                                            ; preds = %bb283
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_600, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_600)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_603, ptr align 8 @alloc_f2994e6355023507b001b4fafe586514)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_603)
  store i32 150, ptr %total_users, align 4
  store i32 120, ptr %active_users, align 4
  %137 = load i32, ptr %total_users, align 4
  %138 = load i32, ptr %active_users, align 4
  %139 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %137, i32 %138)
  %_608.0 = extractvalue { i32, i1 } %139, 0
  %_608.1 = extractvalue { i32, i1 } %139, 1
  br i1 %_608.1, label %panic6, label %bb296

bb296:                                            ; preds = %bb286
  store i32 %_608.0, ptr %inactive_users, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_610, ptr align 8 @alloc_3cbe2c8695a0872b4199390575289d0c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_610)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_617, ptr align 4 %total_users)
  %140 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_616, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %140, ptr align 8 %_617, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_613, ptr align 8 @alloc_5643e2aeb8e73a0f552bcc2267a2aaa0, ptr align 8 %_616)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_613)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_624, ptr align 4 %active_users)
  %141 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_623, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %141, ptr align 8 %_624, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_620, ptr align 8 @alloc_be05a9dc80da435fb2f8bf2c61526740, ptr align 8 %_623)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_620)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_631, ptr align 4 %inactive_users)
  %142 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_630, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %142, ptr align 8 %_631, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_627, ptr align 8 @alloc_d825ff3c095e89b0caed0068dca1e154, ptr align 8 %_630)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_627)
  %143 = load i32, ptr %active_users, align 4
  %144 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %143, i32 100)
  %_642.0 = extractvalue { i32, i1 } %144, 0
  %_642.1 = extractvalue { i32, i1 } %144, 1
  br i1 %_642.1, label %panic7, label %bb308

panic6:                                           ; preds = %bb286
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_06a0f119ba043361ab73f0df12ec7d01) #13
  unreachable

bb308:                                            ; preds = %bb296
  %145 = load i32, ptr %total_users, align 4
  %_643 = icmp eq i32 %145, 0
  br i1 %_643, label %panic8, label %bb309

panic7:                                           ; preds = %bb296
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_41a78173d65b403cc8bce28e26e78932) #13
  unreachable

bb309:                                            ; preds = %bb308
  %146 = load i32, ptr %total_users, align 4
  %_644 = icmp eq i32 %146, -1
  %_645 = icmp eq i32 %_642.0, -2147483648
  %_646 = and i1 %_644, %_645
  br i1 %_646, label %panic9, label %bb310

panic8:                                           ; preds = %bb308
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_41a78173d65b403cc8bce28e26e78932) #13
  unreachable

bb310:                                            ; preds = %bb309
  %147 = load i32, ptr %total_users, align 4
  %148 = sdiv i32 %_642.0, %147
  store i32 %148, ptr %_640, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_638, ptr align 4 %_640)
  %149 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_637, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %149, ptr align 8 %_638, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_634, ptr align 8 @alloc_b8102c06b2f5ecc4e6c8f29d75c6ba7b, ptr align 8 %_637)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_634)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_648, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_648)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_651, ptr align 8 @alloc_708a65d6c6bb252c0c943fb53ccfebfa)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_651)
; call std::fs::write
  %150 = call ptr @_ZN3std2fs5write17h1e28c32aecccac62E(ptr align 1 @alloc_60b9f4702ec126d25fdeb69748ec9d78, i64 15, ptr align 1 @alloc_f9339f1766e63eb3b6eb399bdf9692b6, i64 64)
  store ptr %150, ptr %_653, align 8
  %151 = load ptr, ptr %_653, align 8
  %152 = ptrtoint ptr %151 to i64
  %153 = icmp eq i64 %152, 0
  %_655 = select i1 %153, i64 0, i64 1
  %154 = trunc nuw i64 %_655 to i1
  br i1 %154, label %bb319, label %bb320

panic9:                                           ; preds = %bb309
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_41a78173d65b403cc8bce28e26e78932) #13
  unreachable

bb319:                                            ; preds = %bb310
  %155 = load ptr, ptr %_653, align 8
  store ptr %155, ptr %e, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h482c90503d82e1c8E(ptr sret([16 x i8]) align 8 %_665, ptr align 8 %e)
          to label %bb322 unwind label %funclet_bb359

bb320:                                            ; preds = %bb310
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_657, ptr align 8 @alloc_e79f8af5aecf48b181af4a501dea6487)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_657)
  br label %bb361

bb361:                                            ; preds = %bb324, %bb320
; call std::fs::read_to_string
  call void @_ZN3std2fs14read_to_string17h38e285916c01fbc6E(ptr sret([24 x i8]) align 8 %_667, ptr align 1 @alloc_60b9f4702ec126d25fdeb69748ec9d78, i64 15)
  %156 = load i64, ptr %_667, align 8
  %157 = icmp eq i64 %156, -9223372036854775808
  %_668 = select i1 %157, i64 1, i64 0
  %158 = trunc nuw i64 %_668 to i1
  br i1 %158, label %bb326, label %bb327

bb359:                                            ; preds = %funclet_bb359
; call core::ptr::drop_in_place<std::io::error::Error>
  call void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %e) #16 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb360

funclet_bb359:                                    ; preds = %bb323, %bb322, %bb319
  %cleanuppad = cleanuppad within none []
  br label %bb359

bb322:                                            ; preds = %bb319
  %159 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_664, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %159, ptr align 8 %_665, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_661, ptr align 8 @alloc_b87479678d9376c6bd17f1bbcddc345c, ptr align 8 %_664)
          to label %bb323 unwind label %funclet_bb359

bb323:                                            ; preds = %bb322
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_661)
          to label %bb324 unwind label %funclet_bb359

bb324:                                            ; preds = %bb323
; call core::ptr::drop_in_place<std::io::error::Error>
  call void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %e)
  br label %bb361

bb326:                                            ; preds = %bb361
  %160 = getelementptr inbounds i8, ptr %_667, i64 8
  %161 = load ptr, ptr %160, align 8
  store ptr %161, ptr %e3, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h482c90503d82e1c8E(ptr sret([16 x i8]) align 8 %_686, ptr align 8 %e3)
          to label %bb333 unwind label %funclet_bb357

bb327:                                            ; preds = %bb361
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %file_content, ptr align 8 %_667, i64 24, i1 false)
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_671, ptr align 8 @alloc_f64a78d6a34d2f14910c0c82a128a59f)
          to label %bb328 unwind label %funclet_bb358

bb358:                                            ; preds = %funclet_bb358
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h50d4c76614800282E"(ptr align 8 %file_content) #16 [ "funclet"(token %cleanuppad10) ]
  cleanupret from %cleanuppad10 unwind label %funclet_bb360

funclet_bb358:                                    ; preds = %bb331, %bb330, %bb329, %bb328, %bb327
  %cleanuppad10 = cleanuppad within none []
  br label %bb358

bb328:                                            ; preds = %bb327
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_671)
          to label %bb329 unwind label %funclet_bb358

bb329:                                            ; preds = %bb328
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hcad21941d51a5e59E(ptr sret([16 x i8]) align 8 %_678, ptr align 8 %file_content)
          to label %bb330 unwind label %funclet_bb358

bb330:                                            ; preds = %bb329
  %162 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_677, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %162, ptr align 8 %_678, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_674, ptr align 8 @alloc_cad5995fdf8eaabc391223263b7889a1, ptr align 8 %_677)
          to label %bb331 unwind label %funclet_bb358

bb331:                                            ; preds = %bb330
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_674)
          to label %bb332 unwind label %funclet_bb358

bb332:                                            ; preds = %bb331
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h50d4c76614800282E"(ptr align 8 %file_content)
  br label %bb362

bb362:                                            ; preds = %bb335, %bb332
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_689, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_689)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_692, ptr align 8 @alloc_db2ad1749f2ee56255989a5d933c944f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_692)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_695, ptr align 8 @alloc_70dd82b5dcdd91744fa7db2669f54d4d)
; call std::io::stdio::_eprint
  call void @_ZN3std2io5stdio7_eprint17h9e95d6085ad392ebE(ptr align 8 %_695)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_698, ptr align 8 @alloc_b47c44eb3e083756329dd297420521f7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_698)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_701, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_701)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_704, ptr align 8 @alloc_41ce7501d0e67f2cded5a2eb9d12698f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_704)
; call std::io::stdio::stdout
  %163 = call align 8 ptr @_ZN3std2io5stdio6stdout17h65ccd88ceafe9573E()
  store ptr %163, ptr %_709, align 8
; call <std::io::stdio::Stdout as std::io::Write>::flush
  %_707 = call ptr @"_ZN57_$LT$std..io..stdio..Stdout$u20$as$u20$std..io..Write$GT$5flush17h86d79d4f46659905E"(ptr align 8 %_709)
  store ptr %_707, ptr %self.i, align 8
  %164 = load ptr, ptr %self.i, align 8
  %165 = ptrtoint ptr %164 to i64
  %166 = icmp eq i64 %165, 0
  %_2.i = select i1 %166, i64 0, i64 1
  %167 = trunc nuw i64 %_2.i to i1
  br i1 %167, label %bb2.i, label %"_ZN4core6result19Result$LT$T$C$E$GT$6unwrap17hb453f52080757a68E.exit"

bb2.i:                                            ; preds = %bb362
  %168 = load ptr, ptr %self.i, align 8
  store ptr %168, ptr %e.i, align 8
; invoke core::result::unwrap_failed
  invoke void @_ZN4core6result13unwrap_failed17h0f17cbb1395461e1E(ptr align 1 @alloc_00ae4b301f7fab8ac9617c03fcbd7274, i64 43, ptr align 1 %e.i, ptr align 8 @vtable.1, ptr align 8 @alloc_360ab5239dc8850314272a96c3d26b6d) #13
          to label %unreachable.i unwind label %funclet_bb4.i

funclet_bb4.i:                                    ; preds = %bb2.i
  %cleanuppad.i = cleanuppad within none []
; call core::ptr::drop_in_place<std::io::error::Error>
  call void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %e.i) #16 [ "funclet"(token %cleanuppad.i) ]
  cleanupret from %cleanuppad.i unwind to caller

unreachable.i:                                    ; preds = %bb2.i
  unreachable

"_ZN4core6result19Result$LT$T$C$E$GT$6unwrap17hb453f52080757a68E.exit": ; preds = %bb362
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_711, ptr align 8 @alloc_0aa9c20ff196c3f9f760fa19975923f2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_711)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_714, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_714)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hbeb4b43ad1e4814bE(ptr sret([48 x i8]) align 8 %_717, ptr align 8 @alloc_f7882b960da409af89040bec8615e8c3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_717)
  ret void

bb360:                                            ; preds = %funclet_bb360
  cleanupret from %cleanuppad11 unwind to caller

funclet_bb360:                                    ; preds = %bb359, %bb357, %bb358
  %cleanuppad11 = cleanuppad within none []
  br label %bb360

bb357:                                            ; preds = %funclet_bb357
; call core::ptr::drop_in_place<std::io::error::Error>
  call void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %e3) #16 [ "funclet"(token %cleanuppad12) ]
  cleanupret from %cleanuppad12 unwind label %funclet_bb360

funclet_bb357:                                    ; preds = %bb334, %bb333, %bb326
  %cleanuppad12 = cleanuppad within none []
  br label %bb357

bb333:                                            ; preds = %bb326
  %169 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_685, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %169, ptr align 8 %_686, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_682, ptr align 8 @alloc_8c8e48f63f2fc3246831cf72f7c1285a, ptr align 8 %_685)
          to label %bb334 unwind label %funclet_bb357

bb334:                                            ; preds = %bb333
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_682)
          to label %bb335 unwind label %funclet_bb357

bb335:                                            ; preds = %bb334
; call core::ptr::drop_in_place<std::io::error::Error>
  call void @"_ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h9d760c3827389717E"(ptr align 8 %e3)
  br label %bb362

bb287:                                            ; preds = %bb285
  %_594 = getelementptr inbounds nuw { ptr, i64 }, ptr %users, i64 %i13
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h1314cffbc3bc0308E(ptr sret([16 x i8]) align 8 %_593, ptr align 8 %_594)
  %_598 = icmp ult i64 %i13, 3
  br i1 %_598, label %bb289, label %panic15

panic14:                                          ; preds = %bb285
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i13, i64 3, ptr align 8 @alloc_4a391371c50eca003c26d27cd6a67af0) #13
  unreachable

bb289:                                            ; preds = %bb287
  %_597 = getelementptr inbounds nuw i32, ptr %scores, i64 %i13
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_596, ptr align 4 %_597)
  %170 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_592, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %170, ptr align 8 %_593, i64 16, i1 false)
  %171 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_592, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %171, ptr align 8 %_596, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h7a72f3daa34490b0E(ptr sret([48 x i8]) align 8 %_589, ptr align 8 @alloc_e2ba7b1287c02995784aa847c59514c8, ptr align 8 %_592)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_589)
  br label %bb283

panic15:                                          ; preds = %bb287
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i13, i64 3, ptr align 8 @alloc_0a6c12ea9a671ab17f9beffed06f42d4) #13
  unreachable

bb239:                                            ; preds = %bb237
  store i32 %_496.0, ptr %progress, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_502, ptr align 4 %progress)
  %172 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_501, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %172, ptr align 8 %_502, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0c645de223eb120dE(ptr sret([48 x i8]) align 8 %_498, ptr align 8 @alloc_4486a02231778503b475edac208bf33a, ptr align 8 %_501)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_498)
  br label %bb235

panic17:                                          ; preds = %bb237
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_28a862eab43efae8c30a1b094e365204) #13
  unreachable

bb82:                                             ; preds = %bb78
  store i32 %_167.0, ptr %counter, align 4
  br label %bb77

panic18:                                          ; preds = %bb78
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_5b35e0a02a3002a05bec5eeb94c8f182) #13
  unreachable

bb72:                                             ; No predecessors!
  unreachable
}

; _09_input_output::calculate_area
; Function Attrs: uwtable
define internal i32 @_ZN16_09_input_output14calculate_area17h7d67b8432eb71e21E(i32 %0, i32 %1) unnamed_addr #1 {
start:
  %_10 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [32 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %width = alloca [4 x i8], align 4
  %length = alloca [4 x i8], align 4
  store i32 %0, ptr %length, align 4
  store i32 %1, ptr %width, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_8, ptr align 4 %length)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7bd1bf79512cb996E(ptr sret([16 x i8]) align 8 %_10, ptr align 4 %width)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_8, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_10, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h7a72f3daa34490b0E(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_a5cdec5909fbda735160b00578dc37df, ptr align 8 %_7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_4)
  %4 = load i32, ptr %length, align 4
  %5 = load i32, ptr %width, align 4
  %6 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %4, i32 %5)
  %_12.0 = extractvalue { i32, i1 } %6, 0
  %_12.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_12.1, label %panic, label %bb5

bb5:                                              ; preds = %start
  ret i32 %_12.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_67c7c4f743ad034b17a86975730367c5) #13
  unreachable
}

declare i32 @__CxxFrameHandler3(...) unnamed_addr #5

; std::fs::read_to_string::inner
; Function Attrs: uwtable
declare void @_ZN3std2fs14read_to_string5inner17h66216e5eba8d5f0eE(ptr sret([24 x i8]) align 8, ptr align 1, i64) unnamed_addr #1

; std::fs::write::inner
; Function Attrs: uwtable
declare ptr @_ZN3std2fs5write5inner17hfa09bd9ccb8b3913E(ptr align 1, i64, ptr align 1, i64) unnamed_addr #1

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #6

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #1

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #7

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #8

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #1

; core::fmt::float::<impl core::fmt::Display for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E"(ptr align 8, ptr align 8) unnamed_addr #1

; <std::io::error::Error as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN60_$LT$std..io..error..Error$u20$as$u20$core..fmt..Display$GT$3fmt17hff338456df8a6ec6E"(ptr align 8, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #7

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #7

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #1

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #10

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #7

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #6

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #6

; <std::io::error::Error as core::fmt::Debug>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN58_$LT$std..io..error..Error$u20$as$u20$core..fmt..Debug$GT$3fmt17h3543db37740775b4E"(ptr align 8, ptr align 8) unnamed_addr #1

; core::result::unwrap_failed
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core6result13unwrap_failed17h0f17cbb1395461e1E(ptr align 1, i64, ptr align 1, ptr align 8, ptr align 8) unnamed_addr #6

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #11

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #1

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #7

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #6

; std::io::stdio::_eprint
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio7_eprint17h9e95d6085ad392ebE(ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #7

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #6

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #6

; std::io::stdio::stdout
; Function Attrs: uwtable
declare align 8 ptr @_ZN3std2io5stdio6stdout17h65ccd88ceafe9573E() unnamed_addr #1

; <std::io::stdio::Stdout as std::io::Write>::flush
; Function Attrs: uwtable
declare ptr @"_ZN57_$LT$std..io..stdio..Stdout$u20$as$u20$std..io..Write$GT$5flush17h86d79d4f46659905E"(ptr align 8) unnamed_addr #1

; core::panicking::panic_bounds_check
; Function Attrs: cold minsize noinline noreturn optsize uwtable
declare void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64, i64, ptr align 8) unnamed_addr #12

define i32 @main(i32 %0, ptr %1) unnamed_addr #5 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hf9ce7bbae2051936E(ptr @_ZN16_09_input_output4main17h8b64dcba5a975af2E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { "target-cpu"="x86-64" }
attributes #6 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #7 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #8 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #9 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #10 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #11 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { cold minsize noinline noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { noreturn }
attributes #14 = { nounwind }
attributes #15 = { noreturn nounwind }
attributes #16 = { cold }
attributes #17 = { cold noreturn nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 13723193608028425}
!4 = !{}
