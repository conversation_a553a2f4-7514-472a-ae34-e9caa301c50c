# 19. Array Operations - Advanced Array Manipulation
# This file demonstrates advanced array operations in Dolet

print "=== Array Operations Demo ==="
print ""

# Array initialization and basic operations
print "Array Initialization:"
array int numbers = [5, 2, 8, 1, 9, 3, 7, 4, 6]
array string names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
array float prices = [19.99, 25.50, 12.75, 8.25, 45.00]

print "Numbers: [5, 2, 8, 1, 9, 3, 7, 4, 6]"
print "Names: [<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>]"
print "Prices: [19.99, 25.50, 12.75, 8.25, 45.00]"
print ""

# Array traversal and display
print "Array Traversal:"

fun display_array(arr, size, name) {
    print name + ": ["
    for i = 0 to size - 1
        if i == size - 1
            print arr[i] + "]"
        else
            print arr[i] + ", "
}

display_array(numbers, 9, "Numbers")
display_array(names, 5, "Names")
display_array(prices, 5, "Prices")
print ""

# Array searching
print "Array Searching:"

fun linear_search(arr, size, target) {
    print "Searching for " + target + " in array..."
    
    for i = 0 to size - 1
        print "  Checking index " + i + ": " + arr[i]
        if arr[i] == target
            print "  Found " + target + " at index " + i
            return i
    
    print "  " + target + " not found in array"
    return -1
}

int search_index = linear_search(numbers, 9, 8)
print "Search result: index = " + search_index
print ""

string name_search = linear_search(names, 5, "Charlie")
print "Name search result: index = " + name_search
print ""

# Array sorting - Bubble Sort
print "Array Sorting (Bubble Sort):"

fun bubble_sort(arr, size) {
    print "Sorting array of size " + size
    
    for i = 0 to size - 2
        print "Pass " + (i + 1) + ":"
        
        for j = 0 to size - 2 - i
            print "  Comparing arr[" + j + "]=" + arr[j] + " with arr[" + (j+1) + "]=" + arr[j+1]
            
            if arr[j] > arr[j + 1]
                print "    Swapping " + arr[j] + " and " + arr[j + 1]
                int temp = arr[j]
                set arr[j] = arr[j + 1]
                set arr[j + 1] = temp
        
        print "  After pass " + (i + 1) + ": "
        display_array(arr, size, "Array")
}

array int unsorted = [64, 34, 25, 12, 22, 11, 90]
print "Original array: [64, 34, 25, 12, 22, 11, 90]"
bubble_sort(unsorted, 7)
print "Final sorted array:"
display_array(unsorted, 7, "Sorted")
print ""

# Array statistics
print "Array Statistics:"

fun calculate_statistics(arr, size) {
    int sum = 0
    int min_val = arr[0]
    int max_val = arr[0]
    
    print "Calculating statistics for array of size " + size
    
    for i = 0 to size - 1
        set sum = sum + arr[i]
        
        if arr[i] < min_val
            set min_val = arr[i]
        
        if arr[i] > max_val
            set max_val = arr[i]
    
    float average = sum / size
    
    print "Sum: " + sum
    print "Average: " + average
    print "Minimum: " + min_val
    print "Maximum: " + max_val
    
    return sum
}

array int stats_array = [10, 20, 30, 40, 50]
print "Statistics for [10, 20, 30, 40, 50]:"
int total = calculate_statistics(stats_array, 5)
print ""

# Array reversal
print "Array Reversal:"

fun reverse_array(arr, size) {
    print "Reversing array of size " + size
    
    for i = 0 to (size / 2) - 1
        int opposite = size - 1 - i
        print "Swapping arr[" + i + "]=" + arr[i] + " with arr[" + opposite + "]=" + arr[opposite]
        
        int temp = arr[i]
        set arr[i] = arr[opposite]
        set arr[opposite] = temp
}

array int reverse_test = [1, 2, 3, 4, 5, 6]
print "Original: [1, 2, 3, 4, 5, 6]"
reverse_array(reverse_test, 6)
print "Reversed:"
display_array(reverse_test, 6, "Array")
print ""

# Array copying
print "Array Copying:"

fun copy_array(source, dest, size) {
    print "Copying array of size " + size
    
    for i = 0 to size - 1
        set dest[i] = source[i]
        print "  Copied " + source[i] + " to dest[" + i + "]"
}

array int source_array = [100, 200, 300, 400, 500]
array int dest_array = [0, 0, 0, 0, 0]

print "Source: [100, 200, 300, 400, 500]"
print "Destination before copy: [0, 0, 0, 0, 0]"
copy_array(source_array, dest_array, 5)
print "Destination after copy:"
display_array(dest_array, 5, "Copied Array")
print ""

# Array filtering
print "Array Filtering:"

fun filter_even_numbers(arr, size) {
    print "Filtering even numbers from array:"
    array int filtered = [0, 0, 0, 0, 0, 0, 0, 0, 0]  # Max size
    int filtered_count = 0
    
    for i = 0 to size - 1
        if arr[i] % 2 == 0
            print "  " + arr[i] + " is even - adding to filtered array"
            set filtered[filtered_count] = arr[i]
            set filtered_count = filtered_count + 1
        else
            print "  " + arr[i] + " is odd - skipping"
    
    print "Filtered array (" + filtered_count + " elements):"
    for i = 0 to filtered_count - 1
        print filtered[i] + " "
    
    return filtered_count
}

array int mixed_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
print "Original: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
int even_count = filter_even_numbers(mixed_numbers, 10)
print "Found " + even_count + " even numbers"
print ""

# Array rotation
print "Array Rotation:"

fun rotate_left(arr, size, positions) {
    print "Rotating array left by " + positions + " positions"
    
    for p = 1 to positions
        print "  Rotation " + p + ":"
        int first = arr[0]
        
        for i = 0 to size - 2
            set arr[i] = arr[i + 1]
        
        set arr[size - 1] = first
        display_array(arr, size, "    After rotation " + p)
}

array int rotate_test = [1, 2, 3, 4, 5]
print "Original: [1, 2, 3, 4, 5]"
rotate_left(rotate_test, 5, 2)
print "Final result after 2 left rotations:"
display_array(rotate_test, 5, "Rotated Array")
print ""

# Multi-dimensional array simulation
print "Multi-dimensional Array Simulation:"

# Simulating a 3x3 matrix using separate arrays
array int row1 = [1, 2, 3]
array int row2 = [4, 5, 6]
array int row3 = [7, 8, 9]

print "3x3 Matrix:"
print "Row 1: " + row1[0] + " " + row1[1] + " " + row1[2]
print "Row 2: " + row2[0] + " " + row2[1] + " " + row2[2]
print "Row 3: " + row3[0] + " " + row3[1] + " " + row3[2]

# Matrix operations
int sum_diagonal = row1[0] + row2[1] + row3[2]
print "Sum of main diagonal: " + sum_diagonal

int sum_anti_diagonal = row1[2] + row2[1] + row3[0]
print "Sum of anti-diagonal: " + sum_anti_diagonal
print ""

# Array merging
print "Array Merging:"

fun merge_arrays(arr1, size1, arr2, size2, merged) {
    print "Merging two arrays:"
    print "Array 1 size: " + size1
    print "Array 2 size: " + size2
    
    int merged_index = 0
    
    # Copy first array
    for i = 0 to size1 - 1
        set merged[merged_index] = arr1[i]
        set merged_index = merged_index + 1
    
    # Copy second array
    for i = 0 to size2 - 1
        set merged[merged_index] = arr2[i]
        set merged_index = merged_index + 1
    
    return merged_index
}

array int first_part = [10, 20, 30]
array int second_part = [40, 50, 60, 70]
array int merged_result = [0, 0, 0, 0, 0, 0, 0]

print "First array: [10, 20, 30]"
print "Second array: [40, 50, 60, 70]"

int merged_size = merge_arrays(first_part, 3, second_part, 4, merged_result)
print "Merged array (" + merged_size + " elements):"
display_array(merged_result, merged_size, "Merged")
print ""

print "=== End of Array Operations Demo ==="
