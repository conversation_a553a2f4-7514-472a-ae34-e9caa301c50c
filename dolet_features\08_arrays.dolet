# 08. Arrays - Collections and Lists
# This file demonstrates array operations in Dolet

print "=== Arrays Demo ==="
print ""

# Integer array declaration and initialization
print "Integer Arrays:"
array int numbers = [1, 2, 3, 4, 5]
print "Array created: [1, 2, 3, 4, 5]"

# Accessing array elements
print "Accessing elements:"
print "numbers[0] = " + numbers[0]
print "numbers[1] = " + numbers[1]
print "numbers[4] = " + numbers[4]
print ""

# String array
print "String Arrays:"
array string names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]
print "Names array created"

print "Accessing string elements:"
print "names[0] = " + names[0]
print "names[1] = " + names[1]
print "names[2] = " + names[2]
print "names[3] = " + names[3]
print ""

# Array with loops
print "Array with Loops:"
print "Printing all numbers:"
for i = 0 to 4
    print "numbers[" + i + "] = " + numbers[i]
print ""

print "Printing all names:"
for i = 0 to 3
    print "names[" + i + "] = " + names[i]
print ""

# Modifying array elements
print "Modifying Array Elements:"
set numbers[0] = 10
set numbers[2] = 30
print "After modification:"
for i = 0 to 4
    print "numbers[" + i + "] = " + numbers[i]
print ""

# Array operations
print "Array Operations:"
int sum = 0
for i = 0 to 4
    set sum = sum + numbers[i]
print "Sum of array elements: " + sum

int max = numbers[0]
for i = 1 to 4
    if numbers[i] > max
        set max = numbers[i]
print "Maximum element: " + max
print ""

# Float array
print "Float Arrays:"
array float prices = [19.99, 25.50, 12.75, 8.25, 45.00]
print "Prices array created"

print "All prices:"
for i = 0 to 4
    print "Price " + i + ": $" + prices[i]

float total = 0.0
for i = 0 to 4
    set total = total + prices[i]
print "Total price: $" + total
print ""

# Boolean array
print "Boolean Arrays:"
array bool flags = [true, false, true, true, false]
print "Boolean array created"

print "Flag status:"
for i = 0 to 4
    if flags[i]
        print "Flag " + i + ": ON"
    else
        print "Flag " + i + ": OFF"
print ""

# Character array
print "Character Arrays:"
array char letters = ['A', 'B', 'C', 'D', 'E']
print "Character array created"

print "Letters:"
for i = 0 to 4
    print "letters[" + i + "] = " + letters[i]
print ""

# Multi-dimensional array simulation
print "Multi-dimensional Array Simulation:"
array int row1 = [1, 2, 3]
array int row2 = [4, 5, 6]
array int row3 = [7, 8, 9]

print "Matrix representation:"
print "Row 1: " + row1[0] + " " + row1[1] + " " + row1[2]
print "Row 2: " + row2[0] + " " + row2[1] + " " + row2[2]
print "Row 3: " + row3[0] + " " + row3[1] + " " + row3[2]
print ""

# Array search
print "Array Search:"
string search_name = "Ali"
bool found = false
int found_index = -1

for i = 0 to 3
    if names[i] == search_name
        set found = true
        set found_index = i

if found
    print "Found '" + search_name + "' at index " + found_index
else
    print "'" + search_name + "' not found"
print ""

# Array sorting simulation (bubble sort)
print "Array Sorting Simulation:"
array int unsorted = [64, 34, 25, 12, 22, 11, 90]
print "Original array:"
for i = 0 to 6
    print unsorted[i] + " "

# Simple sorting logic (partial implementation)
print "Sorting process (simplified):"
for i = 0 to 5
    for j = 0 to 5
        if unsorted[j] > unsorted[j + 1]
            int temp = unsorted[j]
            set unsorted[j] = unsorted[j + 1]
            set unsorted[j + 1] = temp

print "After sorting:"
for i = 0 to 6
    print unsorted[i] + " "
print ""

# Array with functions
fun print_array(arr, size) {
    print "Array contents:"
    for i = 0 to size - 1
        print "  [" + i + "] = " + arr[i]
}

fun find_min(arr, size) {
    int min = arr[0]
    for i = 1 to size - 1
        if arr[i] < min
            set min = arr[i]
    return min
}

print "Array with Functions:"
array int test_array = [45, 23, 67, 12, 89, 34]
print_array(test_array, 6)
int minimum = find_min(test_array, 6)
print "Minimum value: " + minimum
print ""

print "=== End of Arrays Demo ==="
