// 9. Input/Output - print, input, file I/O (Rust version)
// This file demonstrates all input/output operations in Rust for comparison with Dolet

use std::fs;
use std::io::{self, Write};

fn main() {
    println!("=== Input/Output Demo (Rust) ===");
    println!();

    // Basic print statements
    println!("Basic print statements:");
    println!("Hello, World!");
    println!("This is a simple message");
    println!("Numbers can be printed: 123");
    println!();

    // Print with variables
    let name = "<PERSON>";
    let age = 25;
    let salary = 5000.50;
    let active = true;
    println!("Print with variables:");
    println!("Name: {}", name);
    println!("Age: {}", age);
    println!("Salary: {}", salary);
    println!("Active: {}", active);
    println!();

    // Print with expressions
    let a = 10;
    let b = 5;
    println!("Print with expressions:");
    println!("a + b = {}", a + b);
    println!("a * b = {}", a * b);
    println!("a > b is {}", a > b);
    println!();

    // Print arrays
    let numbers = [1, 2, 3, 4, 5];
    let names = ["Ali", "Sara", "Omar"];
    println!("Print arrays:");
    println!("First number: {}", numbers[0]);
    println!("Last number: {}", numbers[4]);
    println!("First name: {}", names[0]);
    println!("Second name: {}", names[1]);
    println!();

    // Print in loops
    println!("Print in loops:");
    for i in 1..=5 {
        println!("Loop iteration: {}", i);
    }

    let mut counter = 1;
    while counter <= 3 {
        println!("While loop count: {}", counter);
        counter += 1;
    }
    println!();

    // Print with conditions
    let score = 85;
    println!("Print with conditions:");
    if score >= 90 {
        println!("Excellent score!");
    } else if score >= 80 {
        println!("Good score!");
    } else {
        println!("Need improvement");
    }
    println!();

    // Formatted output
    println!("Formatted output:");
    let product = "Laptop";
    let quantity = 2;
    let unit_price = 1299.99;
    let total = quantity as f64 * unit_price;
    println!("=== ORDER SUMMARY ===");
    println!("Product: {}", product);
    println!("Quantity: {}", quantity);
    println!("Unit Price: ${:.2}", unit_price);
    println!("Total: ${:.2}", total);
    println!("==================");
    println!();

    // Print function results
    println!("Print function results:");
    let room_area = calculate_area(12, 8);
    println!("Room area is: {} square meters", room_area);
    println!();

    // Multi-line output
    println!("Multi-line output:");
    println!("Line 1: Introduction");
    println!("Line 2: Main content");
    println!("Line 3: Conclusion");
    println!("---");
    println!();

    // Print with special formatting
    println!("Print with special formatting:");
    println!("* Bullet point 1");
    println!("* Bullet point 2");
    println!("* Bullet point 3");
    println!();
    println!("1. Numbered item 1");
    println!("2. Numbered item 2");
    println!("3. Numbered item 3");
    println!();

    // Error message simulation
    println!("Error message simulation:");
    let error_code = "404";
    let error_msg = "File not found";
    eprintln!("ERROR {}: {}", error_code, error_msg);
    eprintln!("Please check the file path and try again");
    println!();

    // Status messages
    println!("Status messages:");
    println!("[INFO] System starting...");
    println!("[INFO] Loading configuration...");
    println!("[SUCCESS] System ready!");
    println!("[WARNING] Low disk space");
    println!();

    // Table-like output
    println!("Table-like output:");
    println!("{:<10} | {:<3} | {:<10}", "Name", "Age", "City");
    println!("{:-<10}-+-{:-<3}-+-{:-<10}", "", "", "");
    println!("{:<10} | {:<3} | {:<10}", "Ahmed", "25", "Cairo");
    println!("{:<10} | {:<3} | {:<10}", "Sara", "30", "Alexandria");
    println!("{:<10} | {:<3} | {:<10}", "Omar", "28", "Giza");
    println!();

    // Progress indication simulation
    println!("Progress indication simulation:");
    for i in 0..=5 {
        let progress = i * 20;
        println!("Progress: {}%", progress);
    }
    println!("Complete!");
    println!();

    // Debug output simulation
    println!("Debug output simulation:");
    let x = 10;
    let y = 5;
    println!("[DEBUG] x = {}", x);
    println!("[DEBUG] y = {}", y);
    let result = x + y;
    println!("[DEBUG] x + y = {}", result);
    println!("[DEBUG] Operation completed");
    println!();

    // Menu display simulation
    println!("Menu display simulation:");
    println!("=== MAIN MENU ===");
    println!("1. View Profile");
    println!("2. Edit Settings");
    println!("3. View Reports");
    println!("4. Exit");
    println!("==================");
    println!();

    // Data display
    println!("Data display:");
    let users = ["admin", "user1", "guest"];
    let scores = [100, 85, 45];
    println!("User Data:");
    for i in 0..users.len() {
        println!("User: {} | Score: {}", users[i], scores[i]);
    }
    println!();

    // Summary output
    println!("Summary output:");
    let total_users = 150;
    let active_users = 120;
    let inactive_users = total_users - active_users;
    println!("=== USER STATISTICS ===");
    println!("Total Users: {}", total_users);
    println!("Active Users: {}", active_users);
    println!("Inactive Users: {}", inactive_users);
    println!("Activity Rate: {}%", (active_users * 100) / total_users);
    println!();

    // File I/O demonstration
    println!("File I/O demonstration:");
    
    // Write to file
    let content = "Hello from Rust!\nThis is a test file.\nGenerated by Rust program.";
    match fs::write("rust_output.txt", content) {
        Ok(_) => println!("✓ File written successfully: rust_output.txt"),
        Err(e) => println!("✗ Failed to write file: {}", e),
    }

    // Read from file
    match fs::read_to_string("rust_output.txt") {
        Ok(file_content) => {
            println!("✓ File read successfully:");
            println!("Content: {}", file_content);
        },
        Err(e) => println!("✗ Failed to read file: {}", e),
    }
    println!();

    // Standard error output
    println!("Standard error output:");
    eprintln!("This message goes to stderr");
    println!("This message goes to stdout");
    println!();

    // Flush output
    print!("Flushing output... ");
    io::stdout().flush().unwrap();
    println!("Done!");
    println!();

    println!("=== End of Input/Output Demo ===");
}

fn calculate_area(length: i32, width: i32) -> i32 {
    println!("Calculating area: {} x {}", length, width);
    length * width
}
