#!/usr/bin/env python3

def test_string_constants():
    # Simulate the string_constants dictionary
    string_constants = {}
    
    # Add a string with actual newline (like after parsing)
    test_string = "Line1\nLine2"
    const_name = "@str_dyn_0"
    string_constants[test_string] = const_name
    
    print(f"Original string: {repr(test_string)}")
    print(f"String constants dict: {string_constants}")
    
    # Simulate the _escape_string function
    def escape_string(text):
        utf8_bytes = text.encode('utf-8')
        result = ""
        for byte in utf8_bytes:
            if byte == 10:  # newline
                result += '\\0A'
            elif byte == 9:  # tab
                result += '\\09'
            else:
                result += chr(byte)
        return result
    
    # Process like in the actual code
    for string_text, const_name in string_constants.items():
        print(f"Processing: {repr(string_text)}")
        escaped_text = escape_string(string_text)
        print(f"Escaped: {repr(escaped_text)}")
        llvm_line = f"{const_name} = private unnamed_addr constant [13 x i8] c\"{escaped_text}\\00\", align 1"
        print(f"LLVM line: {llvm_line}")

if __name__ == "__main__":
    test_string_constants()
