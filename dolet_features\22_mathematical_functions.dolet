# 22. Mathematical Functions - Advanced Math Operations
# This file demonstrates mathematical functions in Dolet

print "=== Mathematical Functions Demo ==="
print ""

# Basic mathematical operations
print "Basic Mathematical Operations:"
int a = 15
int b = 4
float x = 7.5
float y = 2.3

print "a = " + a + ", b = " + b
print "x = " + x + ", y = " + y
print "a + b = " + (a + b)
print "a - b = " + (a - b)
print "a * b = " + (a * b)
print "a / b = " + (a / b)
print "a % b = " + (a % b)
print "x + y = " + (x + y)
print "x * y = " + (x * y)
print ""

# Power and root functions
print "Power and Root Functions:"

fun power(base, exponent) {
    int result = 1
    for i = 1 to exponent
        set result = result * base
    return result
}

fun square_root_approx(number) {
    # <PERSON>'s method approximation
    float guess = number / 2.0
    for i = 1 to 10
        set guess = (guess + (number / guess)) / 2.0
    return guess
}

int pow_result = power(2, 8)
float sqrt_result = square_root_approx(25.0)

print "2^8 = " + pow_result
print "sqrt(25) ≈ " + sqrt_result
print "3^4 = " + power(3, 4)
print "sqrt(16) ≈ " + square_root_approx(16.0)
print ""

# Trigonometric functions (approximations)
print "Trigonometric Functions:"
float pi = 3.14159265359
float degrees_to_radians = pi / 180.0

fun sin_approx(angle_degrees) {
    float radians = angle_degrees * degrees_to_radians
    # Taylor series approximation (simplified)
    return radians - (radians * radians * radians / 6.0)
}

fun cos_approx(angle_degrees) {
    float radians = angle_degrees * degrees_to_radians
    # Taylor series approximation (simplified)
    return 1.0 - (radians * radians / 2.0)
}

print "sin(30°) ≈ " + sin_approx(30.0)
print "cos(60°) ≈ " + cos_approx(60.0)
print "sin(45°) ≈ " + sin_approx(45.0)
print "cos(45°) ≈ " + cos_approx(45.0)
print ""

# Logarithmic functions
print "Logarithmic Functions:"

fun natural_log_approx(x) {
    # Simple approximation for ln(x)
    if x <= 0
        return 0.0
    
    float result = 0.0
    float term = (x - 1.0) / x
    
    # Series approximation
    for i = 1 to 10
        set result = result + term
        set term = term * (x - 1.0) / x
    
    return result
}

fun log_base_10(x) {
    return natural_log_approx(x) / natural_log_approx(10.0)
}

print "ln(2) ≈ " + natural_log_approx(2.0)
print "ln(10) ≈ " + natural_log_approx(10.0)
print "log10(100) ≈ " + log_base_10(100.0)
print ""

# Factorial and combinatorics
print "Factorial and Combinatorics:"

fun factorial(n) {
    if n <= 1
        return 1
    else
        return n * factorial(n - 1)
}

fun combination(n, r) {
    return factorial(n) / (factorial(r) * factorial(n - r))
}

fun permutation(n, r) {
    return factorial(n) / factorial(n - r)
}

print "5! = " + factorial(5)
print "7! = " + factorial(7)
print "C(5,2) = " + combination(5, 2)
print "P(5,2) = " + permutation(5, 2)
print ""

# Number theory functions
print "Number Theory Functions:"

fun is_prime(n) {
    if n <= 1
        return false
    if n <= 3
        return true
    if n % 2 == 0 or n % 3 == 0
        return false
    
    for i = 5 to n - 1
        if n % i == 0
            return false
    
    return true
}

fun gcd(a, b) {
    while b != 0
        int temp = b
        set b = a % b
        set a = temp
    return a
}

fun lcm(a, b) {
    return (a * b) / gcd(a, b)
}

print "is_prime(17) = " + is_prime(17)
print "is_prime(15) = " + is_prime(15)
print "gcd(48, 18) = " + gcd(48, 18)
print "lcm(12, 8) = " + lcm(12, 8)
print ""

# Statistical functions
print "Statistical Functions:"

fun mean(arr, size) {
    int sum = 0
    for i = 0 to size - 1
        set sum = sum + arr[i]
    return sum / size
}

fun median(arr, size) {
    # Simplified - assumes sorted array
    if size % 2 == 1
        return arr[size / 2]
    else
        return (arr[size / 2 - 1] + arr[size / 2]) / 2
}

fun variance(arr, size) {
    float avg = mean(arr, size)
    float sum_sq_diff = 0.0
    
    for i = 0 to size - 1
        float diff = arr[i] - avg
        set sum_sq_diff = sum_sq_diff + (diff * diff)
    
    return sum_sq_diff / size
}

array int data = [2, 4, 6, 8, 10, 12, 14]
print "Data: [2, 4, 6, 8, 10, 12, 14]"
print "Mean = " + mean(data, 7)
print "Median = " + median(data, 7)
print "Variance = " + variance(data, 7)
print ""

# Geometric calculations
print "Geometric Calculations:"

fun circle_area(radius) {
    return pi * radius * radius
}

fun circle_circumference(radius) {
    return 2.0 * pi * radius
}

fun rectangle_area(length, width) {
    return length * width
}

fun triangle_area(base, height) {
    return (base * height) / 2.0
}

float radius = 5.0
print "Circle with radius " + radius + ":"
print "  Area = " + circle_area(radius)
print "  Circumference = " + circle_circumference(radius)

print "Rectangle 8x6:"
print "  Area = " + rectangle_area(8.0, 6.0)

print "Triangle base=10, height=6:"
print "  Area = " + triangle_area(10.0, 6.0)
print ""

# Financial calculations
print "Financial Calculations:"

fun simple_interest(principal, rate, time) {
    return (principal * rate * time) / 100.0
}

fun compound_interest(principal, rate, time) {
    float amount = principal
    for i = 1 to time
        set amount = amount * (1.0 + rate / 100.0)
    return amount - principal
}

float principal = 1000.0
float rate = 5.0
int time = 3

print "Principal: $" + principal
print "Rate: " + rate + "%"
print "Time: " + time + " years"
print "Simple Interest = $" + simple_interest(principal, rate, time)
print "Compound Interest = $" + compound_interest(principal, rate, time)
print ""

# Sequence generators
print "Sequence Generators:"

fun fibonacci_sequence(n) {
    print "Fibonacci sequence (first " + n + " terms):"
    int a = 0
    int b = 1
    
    for i = 1 to n
        if i == 1
            print a + " "
        elif i == 2
            print b + " "
        else
            int next = a + b
            print next + " "
            set a = b
            set b = next
}

fun prime_sequence(count) {
    print "First " + count + " prime numbers:"
    int found = 0
    int num = 2
    
    while found < count
        if is_prime(num)
            print num + " "
            set found = found + 1
        set num = num + 1
}

fibonacci_sequence(10)
prime_sequence(10)
print ""

# Random number generation (simulation)
print "Random Number Generation:"

fun linear_congruential_generator(seed, a, c, m) {
    return (a * seed + c) % m
}

int seed = 12345
print "Pseudo-random numbers (using LCG):"
for i = 1 to 10
    set seed = linear_congruential_generator(seed, 1664525, 1013904223, 2147483647)
    int random_num = seed % 100  # Scale to 0-99
    print "Random[" + i + "] = " + random_num
print ""

print "=== End of Mathematical Functions Demo ==="
