# 02. Variables and Data Types - Complete Type System
# This file demonstrates all variable types in Dolet

print "=== Variables and Data Types ==="
print ""

# Integer variables
print "Integer Variables:"
int age = 25
int year = 2024
int negative = -100
int zero = 0
int large = 1000000

print "age = " + age
print "year = " + year  
print "negative = " + negative
print "zero = " + zero
print "large = " + large
print ""

# String variables
print "String Variables:"
string name = "Ahmed"
string city = "Cairo"
string empty = ""
#string special = "Hello, World! @#$%"
#string special2 = "%" the issue is with %
"""
the error :
#PS C:\Users\<USER>\Desktop\dolet lang\underdev\Dolet\Dolet-bootstrap-compiler\src> clang test.ll -o test.exe
#test.ll:33:50: error: constant expression type mismatch: got type '[4 x i8]' but expected '[3 x i8]'
#   33 | @.str_3 = private unnamed_addr constant [3 x i8] c"%%\0A\00", align 1
#      |                                                  ^
#1 error generated.

"""

string multiword = "This is a long sentence"

print "name = " + name
print "city = " + city
print "empty = '" + empty + "'"
print "special = " + special
print "multiword = " + multiword
print ""

# Float variables
print "Float Variables:"
float price = 19.99
float pi = 3.14159
float negative_float = -2.5
float zero_float = 0.0
float small = 0.001

print "price = " + price
print "pi = " + pi
print "negative_float = " + negative_float
print "zero_float = " + zero_float
print "small = " + small
print ""

# Double variables (higher precision)
print "Double Variables:"
double precise = 3.141592653589793
double large_double = 1234567.89012345
double scientific = 1.23e10
double tiny = 1.23e-10

print "precise = " + precise
print "large_double = " + large_double
print "scientific = " + scientific
print "tiny = " + tiny
print ""

# Character variables
print "Character Variables:"
char letter = 'A'
char digit = '5'
char symbol = '@'
char space = ' '
char newline = '\n'

print "letter = " + letter
print "digit = " + digit
print "symbol = " + symbol
print "space = '" + space + "'"
print ""

# Boolean variables
print "Boolean Variables:"
bool active = true
bool finished = false
bool ready = true
bool error = false

print "active = " + active
print "finished = " + finished
print "ready = " + ready
print "error = " + error
print ""

# Variable modification
print "Variable Modification:"
set age = 26
set name = "Ali"
set price = 29.99
set active = false

print "After modification:"
print "age = " + age
print "name = " + name
print "price = " + price
print "active = " + active
print ""

# Multiple variables of same type
print "Multiple Variables:"
int x = 10
int y = 20
int z = 30

string first = "Hello"
string second = "Beautiful"
string third = "World"

print "Integers: " + x + ", " + y + ", " + z
print "Strings: " + first + " " + second + " " + third
print "...................."

print "=== End of Variables and Types Demo ==="
