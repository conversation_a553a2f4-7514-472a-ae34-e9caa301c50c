// 2. Mathematical Operations - Basic and complex math (Rust version)
// This file demonstrates all mathematical operations in Rust for comparison with Dolet

fn main() {
    println!("=== Mathematical Operations Demo (Rust) ===");
    println!();

    // Basic arithmetic operations
    let a: i32 = 15;
    let b: i32 = 4;
    println!("Basic Arithmetic (a=15, b=4):");
    println!("Addition: {} + {} = {}", a, b, a + b);
    println!("Subtraction: {} - {} = {}", a, b, a - b);
    println!("Multiplication: {} * {} = {}", a, b, a * b);
    println!("Division: {} / {} = {}", a, b, a / b);
    println!("Modulo: {} % {} = {}", a, b, a % b);
    println!();

    // Operations with parentheses
    let result1 = (a + b) * 2;
    let result2 = a * (b + 5);
    let result3 = (a * 2) + (b * 3);
    println!("Operations with Parentheses:");
    println!("(a + b) * 2 = {}", result1);
    println!("a * (b + 5) = {}", result2);
    println!("(a * 2) + (b * 3) = {}", result3);
    println!();

    // Complex nested expressions
    let complex1 = ((a + b) * 2) - (a / b);
    let complex2 = (a * b) + ((a - b) * 2);
    let complex3 = ((a + 5) * (b - 1)) / 2;
    println!("Complex Nested Expressions:");
    println!("((a + b) * 2) - (a / b) = {}", complex1);
    println!("(a * b) + ((a - b) * 2) = {}", complex2);
    println!("((a + 5) * (b - 1)) / 2 = {}", complex3);
    println!();

    // Float operations
    let x: f32 = 10.5;
    let y: f32 = 3.2;
    println!("Float Operations (x=10.5, y=3.2):");
    println!("x + y = {}", x + y);
    println!("x - y = {}", x - y);
    println!("x * y = {}", x * y);
    println!("x / y = {}", x / y);
    println!();

    // Mixed type operations (requires explicit casting in Rust)
    let int_val: i32 = 10;
    let float_val: f32 = 2.5;
    println!("Mixed Type Operations:");
    println!("int + float: {} + {} = {}", int_val, float_val, int_val as f32 + float_val);
    println!("int * float: {} * {} = {}", int_val, float_val, int_val as f32 * float_val);
    println!();

    // Negative numbers
    let neg1: i32 = -5;
    let neg2: i32 = -3;
    println!("Negative Number Operations:");
    println!("(-5) + (-3) = {}", neg1 + neg2);
    println!("(-5) * (-3) = {}", neg1 * neg2);
    println!("(-5) - (-3) = {}", neg1 - neg2);
    println!();

    // Assignment with operations (mutable variables)
    let mut counter = 0;
    println!("Assignment with operations:");
    counter += 1;
    println!("counter after increment: {}", counter);
    counter *= 2;
    println!("counter after doubling: {}", counter);
    counter -= 5;
    println!("counter after subtracting 5: {}", counter);
    println!();

    // Chain operations
    let chain_result = 1 + 2 + 3 + 4 + 5;
    println!("Chain operations: 1 + 2 + 3 + 4 + 5 = {}", chain_result);

    // Power operations (using pow method)
    let base: i32 = 2;
    let power2 = base.pow(2);
    let power3 = base.pow(3);
    let power4 = base.pow(4);
    println!("Power operations (base=2):");
    println!("2^2 = {}", power2);
    println!("2^3 = {}", power3);
    println!("2^4 = {}", power4);
    println!();

    // Mathematical expressions in conditions
    let num = 15;
    if (num * 2) > 25 {
        println!("num * 2 is greater than 25");
    }

    if (num + 5) == 20 {
        println!("num + 5 equals 20");
    }

    println!();
    println!("=== End of Mathematical Operations Demo ===");
}
