; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 307 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_expr_0 = private unnamed_addr constant [3 x i8] c"a=\00", align 1
@.str_expr_1 = private unnamed_addr constant [5 x i8] c", b=\00", align 1
@.str_expr_2 = private unnamed_addr constant [5 x i8] c", c=\00", align 1
@.str_expr_3 = private unnamed_addr constant [5 x i8] c", d=\00", align 1
@.str_expr_4 = private unnamed_addr constant [3 x i8] c"x=\00", align 1
@.str_expr_5 = private unnamed_addr constant [5 x i8] c", y=\00", align 1
@.str_expr_6 = private unnamed_addr constant [5 x i8] c", z=\00", align 1
@.str_expr_7 = private unnamed_addr constant [10 x i8] c"negative=\00", align 1
@.str_expr_8 = private unnamed_addr constant [8 x i8] c", zero=\00", align 1
@.str_expr_9 = private unnamed_addr constant [9 x i8] c", large=\00", align 1
@.str_expr_10 = private unnamed_addr constant [9 x i8] c"a + b = \00", align 1
@.str_expr_11 = private unnamed_addr constant [15 x i8] c" (10 + 3 = 13)\00", align 1
@.str_expr_12 = private unnamed_addr constant [9 x i8] c"a + 5 = \00", align 1
@.str_expr_13 = private unnamed_addr constant [15 x i8] c" (10 + 5 = 15)\00", align 1
@.str_expr_14 = private unnamed_addr constant [10 x i8] c"15 + b = \00", align 1
@.str_expr_15 = private unnamed_addr constant [15 x i8] c" (15 + 3 = 18)\00", align 1
@.str_expr_16 = private unnamed_addr constant [9 x i8] c"7 + 8 = \00", align 1
@.str_expr_17 = private unnamed_addr constant [14 x i8] c" (7 + 8 = 15)\00", align 1
@.str_expr_18 = private unnamed_addr constant [9 x i8] c"x + y = \00", align 1
@.str_expr_19 = private unnamed_addr constant [20 x i8] c" (7.5 + 2.5 = 10.0)\00", align 1
@.str_expr_20 = private unnamed_addr constant [11 x i8] c"x + 3.0 = \00", align 1
@.str_expr_21 = private unnamed_addr constant [20 x i8] c" (7.5 + 3.0 = 10.5)\00", align 1
@.str_expr_22 = private unnamed_addr constant [16 x i8] c"negative + a = \00", align 1
@.str_expr_23 = private unnamed_addr constant [15 x i8] c" (-4 + 10 = 6)\00", align 1
@.str_expr_24 = private unnamed_addr constant [12 x i8] c"zero + b = \00", align 1
@.str_expr_25 = private unnamed_addr constant [13 x i8] c" (0 + 3 = 3)\00", align 1
@.str_expr_26 = private unnamed_addr constant [9 x i8] c"a - b = \00", align 1
@.str_expr_27 = private unnamed_addr constant [14 x i8] c" (10 - 3 = 7)\00", align 1
@.str_expr_28 = private unnamed_addr constant [9 x i8] c"a - 7 = \00", align 1
@.str_expr_29 = private unnamed_addr constant [14 x i8] c" (10 - 7 = 3)\00", align 1
@.str_expr_30 = private unnamed_addr constant [10 x i8] c"20 - b = \00", align 1
@.str_expr_31 = private unnamed_addr constant [15 x i8] c" (20 - 3 = 17)\00", align 1
@.str_expr_32 = private unnamed_addr constant [10 x i8] c"15 - 8 = \00", align 1
@.str_expr_33 = private unnamed_addr constant [14 x i8] c" (15 - 8 = 7)\00", align 1
@.str_expr_34 = private unnamed_addr constant [9 x i8] c"x - y = \00", align 1
@.str_expr_35 = private unnamed_addr constant [19 x i8] c" (7.5 - 2.5 = 5.0)\00", align 1
@.str_expr_36 = private unnamed_addr constant [16 x i8] c"a - negative = \00", align 1
@.str_expr_37 = private unnamed_addr constant [18 x i8] c" (10 - (-4) = 14)\00", align 1
@.str_expr_38 = private unnamed_addr constant [12 x i8] c"zero - b = \00", align 1
@.str_expr_39 = private unnamed_addr constant [14 x i8] c" (0 - 3 = -3)\00", align 1
@.str_expr_40 = private unnamed_addr constant [9 x i8] c"a * b = \00", align 1
@.str_expr_41 = private unnamed_addr constant [15 x i8] c" (10 * 3 = 30)\00", align 1
@.str_expr_42 = private unnamed_addr constant [9 x i8] c"a * 4 = \00", align 1
@.str_expr_43 = private unnamed_addr constant [15 x i8] c" (10 * 4 = 40)\00", align 1
@.str_expr_44 = private unnamed_addr constant [9 x i8] c"6 * b = \00", align 1
@.str_expr_45 = private unnamed_addr constant [14 x i8] c" (6 * 3 = 18)\00", align 1
@.str_expr_46 = private unnamed_addr constant [9 x i8] c"7 * 8 = \00", align 1
@.str_expr_47 = private unnamed_addr constant [14 x i8] c" (7 * 8 = 56)\00", align 1
@.str_expr_48 = private unnamed_addr constant [9 x i8] c"x * y = \00", align 1
@.str_expr_49 = private unnamed_addr constant [21 x i8] c" (7.5 * 2.5 = 18.75)\00", align 1
@.str_expr_50 = private unnamed_addr constant [16 x i8] c"negative * b = \00", align 1
@.str_expr_51 = private unnamed_addr constant [16 x i8] c" (-4 * 3 = -12)\00", align 1
@.str_expr_52 = private unnamed_addr constant [12 x i8] c"zero * a = \00", align 1
@.str_expr_53 = private unnamed_addr constant [14 x i8] c" (0 * 10 = 0)\00", align 1
@.str_expr_54 = private unnamed_addr constant [9 x i8] c"a / b = \00", align 1
@.str_expr_55 = private unnamed_addr constant [14 x i8] c" (10 / 3 = 3)\00", align 1
@.str_expr_56 = private unnamed_addr constant [9 x i8] c"a / 2 = \00", align 1
@.str_expr_57 = private unnamed_addr constant [14 x i8] c" (10 / 2 = 5)\00", align 1
@.str_expr_58 = private unnamed_addr constant [10 x i8] c"20 / b = \00", align 1
@.str_expr_59 = private unnamed_addr constant [14 x i8] c" (20 / 3 = 6)\00", align 1
@.str_expr_60 = private unnamed_addr constant [10 x i8] c"15 / 3 = \00", align 1
@.str_expr_61 = private unnamed_addr constant [14 x i8] c" (15 / 3 = 5)\00", align 1
@.str_expr_62 = private unnamed_addr constant [9 x i8] c"x / y = \00", align 1
@.str_expr_63 = private unnamed_addr constant [19 x i8] c" (7.5 / 2.5 = 3.0)\00", align 1
@.str_expr_64 = private unnamed_addr constant [16 x i8] c"negative / d = \00", align 1
@.str_expr_65 = private unnamed_addr constant [15 x i8] c" (-4 / 2 = -2)\00", align 1
@.str_expr_66 = private unnamed_addr constant [13 x i8] c"large / a = \00", align 1
@.str_expr_67 = private unnamed_addr constant [17 x i8] c" (100 / 10 = 10)\00", align 1
@.str_expr_68 = private unnamed_addr constant [10 x i8] c"a %% b = \00", align 1
@.str_expr_69 = private unnamed_addr constant [15 x i8] c" (10 %% 3 = 1)\00", align 1
@.str_expr_70 = private unnamed_addr constant [10 x i8] c"a %% 4 = \00", align 1
@.str_expr_71 = private unnamed_addr constant [15 x i8] c" (10 %% 4 = 2)\00", align 1
@.str_expr_72 = private unnamed_addr constant [11 x i8] c"17 %% b = \00", align 1
@.str_expr_73 = private unnamed_addr constant [15 x i8] c" (17 %% 3 = 2)\00", align 1
@.str_expr_74 = private unnamed_addr constant [11 x i8] c"15 %% 4 = \00", align 1
@.str_expr_75 = private unnamed_addr constant [15 x i8] c" (15 %% 4 = 3)\00", align 1
@.str_expr_76 = private unnamed_addr constant [14 x i8] c"large %% a = \00", align 1
@.str_expr_77 = private unnamed_addr constant [17 x i8] c" (100 %% 10 = 0)\00", align 1
@.str_expr_78 = private unnamed_addr constant [11 x i8] c"13 %% 5 = \00", align 1
@.str_expr_79 = private unnamed_addr constant [15 x i8] c" (13 %% 5 = 3)\00", align 1
@.str_expr_80 = private unnamed_addr constant [15 x i8] c"(a + b) * c = \00", align 1
@.str_expr_81 = private unnamed_addr constant [21 x i8] c" ((10 + 3) * 5 = 65)\00", align 1
@.str_expr_82 = private unnamed_addr constant [15 x i8] c"a * (b + c) = \00", align 1
@.str_expr_83 = private unnamed_addr constant [21 x i8] c" (10 * (3 + 5) = 80)\00", align 1
@.str_expr_84 = private unnamed_addr constant [21 x i8] c"(a - b) + (c * d) = \00", align 1
@.str_expr_85 = private unnamed_addr constant [27 x i8] c" ((10 - 3) + (5 * 2) = 17)\00", align 1
@.str_expr_86 = private unnamed_addr constant [21 x i8] c"(a + b) - (c - d) = \00", align 1
@.str_expr_87 = private unnamed_addr constant [27 x i8] c" ((10 + 3) - (5 - 2) = 10)\00", align 1
@.str_expr_88 = private unnamed_addr constant [21 x i8] c"(a * b) / (c - d) = \00", align 1
@.str_expr_89 = private unnamed_addr constant [27 x i8] c" ((10 * 3) / (5 - 2) = 10)\00", align 1
@.str_expr_90 = private unnamed_addr constant [19 x i8] c"(a + b + c) * d = \00", align 1
@.str_expr_91 = private unnamed_addr constant [25 x i8] c" ((10 + 3 + 5) * 2 = 36)\00", align 1
@.str_expr_92 = private unnamed_addr constant [15 x i8] c"(x + y) * z = \00", align 1
@.str_expr_93 = private unnamed_addr constant [28 x i8] c" ((7.5 + 2.5) * 1.2 = 12.0)\00", align 1
@.str_expr_94 = private unnamed_addr constant [22 x i8] c"(negative + a) * b = \00", align 1
@.str_expr_95 = private unnamed_addr constant [22 x i8] c" ((-4 + 10) * 3 = 18)\00", align 1
@.str_expr_96 = private unnamed_addr constant [21 x i8] c"((a + b) * c) - d = \00", align 1
@.str_expr_97 = private unnamed_addr constant [27 x i8] c" (((10 + 3) * 5) - 2 = 63)\00", align 1
@.str_expr_98 = private unnamed_addr constant [21 x i8] c"a + ((b * c) - d) = \00", align 1
@.str_expr_99 = private unnamed_addr constant [27 x i8] c" (10 + ((3 * 5) - 2) = 23)\00", align 1
@.str_expr_100 = private unnamed_addr constant [21 x i8] c"(a * (b + c)) / d = \00", align 1
@.str_expr_101 = private unnamed_addr constant [27 x i8] c" ((10 * (3 + 5)) / 2 = 40)\00", align 1
@.str_expr_102 = private unnamed_addr constant [27 x i8] c"((a - b) + c) * (d + 1) = \00", align 1
@.str_expr_103 = private unnamed_addr constant [33 x i8] c" (((10 - 3) + 5) * (2 + 1) = 36)\00", align 1
@.str_expr_104 = private unnamed_addr constant [27 x i8] c"((a + b) * (c - d)) + 1 = \00", align 1
@.str_expr_105 = private unnamed_addr constant [33 x i8] c" (((10 + 3) * (5 - 2)) + 1 = 40)\00", align 1
@.str_expr_106 = private unnamed_addr constant [27 x i8] c"(a + (b * (c + d))) - 5 = \00", align 1
@.str_expr_107 = private unnamed_addr constant [33 x i8] c" ((10 + (3 * (5 + 2))) - 5 = 26)\00", align 1
@.str_expr_108 = private unnamed_addr constant [23 x i8] c"((x + y) / z) * 2.0 = \00", align 1
@.str_expr_109 = private unnamed_addr constant [38 x i8] c" (((7.5 + 2.5) / 1.2) * 2.0 = 16.667)\00", align 1
@.str_expr_110 = private unnamed_addr constant [26 x i8] c"(negative * b + a) / d = \00", align 1
@.str_expr_111 = private unnamed_addr constant [28 x i8] c" (((-4 * 3) + 10) / 2 = -1)\00", align 1
@.str_expr_112 = private unnamed_addr constant [17 x i8] c"a + b + c + d = \00", align 1
@.str_expr_113 = private unnamed_addr constant [23 x i8] c" (10 + 3 + 5 + 2 = 20)\00", align 1
@.str_expr_114 = private unnamed_addr constant [17 x i8] c"a - b - c - d = \00", align 1
@.str_expr_115 = private unnamed_addr constant [22 x i8] c" (10 - 3 - 5 - 2 = 0)\00", align 1
@.str_expr_116 = private unnamed_addr constant [17 x i8] c"a * b * c * d = \00", align 1
@.str_expr_117 = private unnamed_addr constant [24 x i8] c" (10 * 3 * 5 * 2 = 300)\00", align 1
@.str_expr_118 = private unnamed_addr constant [25 x i8] c"1 + 2 + 3 + 4 + 5 + 6 = \00", align 1
@.str_expr_119 = private unnamed_addr constant [20 x i8] c" (1+2+3+4+5+6 = 21)\00", align 1
@.str_expr_120 = private unnamed_addr constant [17 x i8] c"2 * 3 * 4 * 5 = \00", align 1
@.str_expr_121 = private unnamed_addr constant [17 x i8] c" (2*3*4*5 = 120)\00", align 1
@.str_expr_122 = private unnamed_addr constant [24 x i8] c"100 - 10 - 5 - 3 - 2 = \00", align 1
@.str_expr_123 = private unnamed_addr constant [21 x i8] c" (100-10-5-3-2 = 80)\00", align 1
@.str_expr_124 = private unnamed_addr constant [19 x i8] c"x + y + z + 1.0 = \00", align 1
@.str_expr_125 = private unnamed_addr constant [26 x i8] c" (7.5************ = 12.2)\00", align 1
@.str_expr_126 = private unnamed_addr constant [21 x i8] c"a + b - c + d - 1 = \00", align 1
@.str_expr_127 = private unnamed_addr constant [18 x i8] c" (10******** = 9)\00", align 1
@.str_expr_128 = private unnamed_addr constant [13 x i8] c"a + b * c = \00", align 1
@.str_expr_129 = private unnamed_addr constant [21 x i8] c" (10 + (3 * 5) = 25)\00", align 1
@.str_expr_130 = private unnamed_addr constant [13 x i8] c"a * b + c = \00", align 1
@.str_expr_131 = private unnamed_addr constant [21 x i8] c" ((10 * 3) + 5 = 35)\00", align 1
@.str_expr_132 = private unnamed_addr constant [17 x i8] c"a + b * c - d = \00", align 1
@.str_expr_133 = private unnamed_addr constant [25 x i8] c" (10 + (3 * 5) - 2 = 23)\00", align 1
@.str_expr_134 = private unnamed_addr constant [17 x i8] c"a - b * c + d = \00", align 1
@.str_expr_135 = private unnamed_addr constant [25 x i8] c" (10 - (3 * 5) + 2 = -3)\00", align 1
@.str_expr_136 = private unnamed_addr constant [17 x i8] c"a * b + c * d = \00", align 1
@.str_expr_137 = private unnamed_addr constant [27 x i8] c" ((10 * 3) + (5 * 2) = 40)\00", align 1
@.str_expr_138 = private unnamed_addr constant [17 x i8] c"a * b - c * d = \00", align 1
@.str_expr_139 = private unnamed_addr constant [27 x i8] c" ((10 * 3) - (5 * 2) = 20)\00", align 1
@.str_expr_140 = private unnamed_addr constant [17 x i8] c"a + b / c * d = \00", align 1
@.str_expr_141 = private unnamed_addr constant [27 x i8] c" (10 + ((3 / 5) * 2) = 10)\00", align 1
@.str_expr_142 = private unnamed_addr constant [17 x i8] c"a * b / c + d = \00", align 1
@.str_expr_143 = private unnamed_addr constant [26 x i8] c" (((10 * 3) / 5) + 2 = 8)\00", align 1
@.str_expr_144 = private unnamed_addr constant [13 x i8] c"x + y * z = \00", align 1
@.str_expr_145 = private unnamed_addr constant [28 x i8] c" (7.5 + (2.5 * 1.2) = 10.5)\00", align 1
@.str_expr_146 = private unnamed_addr constant [18 x i8] c"a %% b + c * d = \00", align 1
@.str_expr_147 = private unnamed_addr constant [28 x i8] c" ((10 %% 3) + (5 * 2) = 11)\00", align 1
@.str_expr_148 = private unnamed_addr constant [19 x i8] c"(a + b) * c - d = \00", align 1
@.str_expr_149 = private unnamed_addr constant [25 x i8] c" ((10 + 3) * 5 - 2 = 63)\00", align 1
@.str_expr_150 = private unnamed_addr constant [19 x i8] c"a + (b * c) - d = \00", align 1
@.str_expr_151 = private unnamed_addr constant [21 x i8] c"(a + b) * (c - d) = \00", align 1
@.str_expr_152 = private unnamed_addr constant [27 x i8] c" ((10 + 3) * (5 - 2) = 39)\00", align 1
@.str_expr_153 = private unnamed_addr constant [25 x i8] c"a * (b + c) - (d * 2) = \00", align 1
@.str_expr_154 = private unnamed_addr constant [31 x i8] c" (10 * (3 + 5) - (2 * 2) = 76)\00", align 1
@.str_expr_155 = private unnamed_addr constant [25 x i8] c"(a - b) * c + (d * 3) = \00", align 1
@.str_expr_156 = private unnamed_addr constant [31 x i8] c" ((10 - 3) * 5 + (2 * 3) = 41)\00", align 1
@.str_expr_157 = private unnamed_addr constant [23 x i8] c"a + b * (c - d) + 1 = \00", align 1
@.str_expr_158 = private unnamed_addr constant [29 x i8] c" (10 + 3 * (5 - 2) + 1 = 20)\00", align 1
@.str_expr_159 = private unnamed_addr constant [25 x i8] c"(a * b) + c - (d / 2) = \00", align 1
@.str_expr_160 = private unnamed_addr constant [31 x i8] c" ((10 * 3) + 5 - (2 / 2) = 34)\00", align 1
@.str_expr_161 = private unnamed_addr constant [23 x i8] c"a - (b + c) * d + 5 = \00", align 1
@.str_expr_162 = private unnamed_addr constant [29 x i8] c" (10 - (3 + 5) * 2 + 5 = -1)\00", align 1
@.str_expr_163 = private unnamed_addr constant [12 x i8] c"zero + a = \00", align 1
@.str_expr_164 = private unnamed_addr constant [15 x i8] c" (0 + 10 = 10)\00", align 1
@.str_expr_165 = private unnamed_addr constant [12 x i8] c"a + zero = \00", align 1
@.str_expr_166 = private unnamed_addr constant [15 x i8] c" (10 + 0 = 10)\00", align 1
@.str_expr_167 = private unnamed_addr constant [12 x i8] c"a * zero = \00", align 1
@.str_expr_168 = private unnamed_addr constant [14 x i8] c" (10 * 0 = 0)\00", align 1
@.str_expr_169 = private unnamed_addr constant [12 x i8] c"zero - a = \00", align 1
@.str_expr_170 = private unnamed_addr constant [16 x i8] c" (0 - 10 = -10)\00", align 1
@.str_expr_171 = private unnamed_addr constant [12 x i8] c"a - zero = \00", align 1
@.str_expr_172 = private unnamed_addr constant [15 x i8] c" (10 - 0 = 10)\00", align 1
@.str_expr_173 = private unnamed_addr constant [23 x i8] c"negative + negative = \00", align 1
@.str_expr_174 = private unnamed_addr constant [18 x i8] c" (-4 + (-4) = -8)\00", align 1
@.str_expr_175 = private unnamed_addr constant [23 x i8] c"negative * negative = \00", align 1
@.str_expr_176 = private unnamed_addr constant [18 x i8] c" (-4 * (-4) = 16)\00", align 1
@.str_expr_177 = private unnamed_addr constant [23 x i8] c"negative - negative = \00", align 1
@.str_expr_178 = private unnamed_addr constant [17 x i8] c" (-4 - (-4) = 0)\00", align 1
@.str_expr_179 = private unnamed_addr constant [16 x i8] c"a + negative = \00", align 1
@.str_expr_180 = private unnamed_addr constant [17 x i8] c" (10 + (-4) = 6)\00", align 1
@.str_expr_181 = private unnamed_addr constant [16 x i8] c"a * negative = \00", align 1
@.str_expr_182 = private unnamed_addr constant [19 x i8] c" (10 * (-4) = -40)\00", align 1
@.str_expr_183 = private unnamed_addr constant [17 x i8] c"large + large = \00", align 1
@.str_expr_184 = private unnamed_addr constant [19 x i8] c" (100 + 100 = 200)\00", align 1
@.str_expr_185 = private unnamed_addr constant [13 x i8] c"large * a = \00", align 1
@.str_expr_186 = private unnamed_addr constant [19 x i8] c" (100 * 10 = 1000)\00", align 1
@.str_expr_187 = private unnamed_addr constant [13 x i8] c"large - a = \00", align 1
@.str_expr_188 = private unnamed_addr constant [17 x i8] c" (100 - 10 = 90)\00", align 1
@.str_expr_189 = private unnamed_addr constant [26 x i8] c"Rectangle area (a * b) = \00", align 1
@.str_expr_190 = private unnamed_addr constant [37 x i8] c"Rectangle perimeter ((a + b) * 2) = \00", align 1
@.str_expr_191 = private unnamed_addr constant [21 x i8] c" ((10 + 3) * 2 = 26)\00", align 1
@.str_expr_192 = private unnamed_addr constant [31 x i8] c"Triangle area ((a * b) / 2) = \00", align 1
@.str_expr_193 = private unnamed_addr constant [21 x i8] c" ((10 * 3) / 2 = 15)\00", align 1
@.str_expr_194 = private unnamed_addr constant [35 x i8] c"Pythagorean ((a * a) + (b * b)) = \00", align 1
@.str_expr_195 = private unnamed_addr constant [29 x i8] c" ((10 * 10) + (3 * 3) = 109)\00", align 1
@.str_expr_196 = private unnamed_addr constant [45 x i8] c"Simple interest (large * (1 + (c / 100))) = \00", align 1
@.str_expr_197 = private unnamed_addr constant [31 x i8] c" (100 * (1 + (5 / 100)) = 105)\00", align 1
@.str_expr_198 = private unnamed_addr constant [33 x i8] c"Average ((a + b + c + d) / 4) = \00", align 1
@.str_expr_199 = private unnamed_addr constant [28 x i8] c" ((10 + 3 + 5 + 2) / 4 = 5)\00", align 1
@.str_expr_200 = private unnamed_addr constant [33 x i8] c"((a + b) * c) + ((d * 2) - 1) = \00", align 1
@.str_expr_201 = private unnamed_addr constant [39 x i8] c" (((10 + 3) * 5) + ((2 * 2) - 1) = 68)\00", align 1
@.str_expr_202 = private unnamed_addr constant [33 x i8] c"(a * (b + c)) - ((d + 1) * 2) = \00", align 1
@.str_expr_203 = private unnamed_addr constant [39 x i8] c" ((10 * (3 + 5)) - ((2 + 1) * 2) = 74)\00", align 1
@.str_expr_204 = private unnamed_addr constant [39 x i8] c"((a - b) + (c * d)) * ((a / b) + 1) = \00", align 1
@.str_expr_205 = private unnamed_addr constant [46 x i8] c" (((10 - 3) + (5 * 2)) * ((10 / 3) + 1) = 68)\00", align 1
@.str_expr_206 = private unnamed_addr constant [40 x i8] c"(((a + b) * c) / d) + ((a * b) %% c) = \00", align 1
@.str_expr_207 = private unnamed_addr constant [47 x i8] c" ((((10 + 3) * 5) / 2) + ((10 * 3) %% 5) = 32)\00", align 1
@.str_expr_208 = private unnamed_addr constant [35 x i8] c"((x + y) * z) - ((x - y) / 2.0) = \00", align 1
@.str_expr_209 = private unnamed_addr constant [51 x i8] c" (((7.5 + 2.5) * 1.2) - ((7.5 - 2.5) / 2.0) = 9.5)\00", align 1
@.str_expr_210 = private unnamed_addr constant [27 x i8] c"(((a + b) * c) - d) + 1 = \00", align 1
@.str_expr_211 = private unnamed_addr constant [33 x i8] c" ((((10 + 3) * 5) - 2) + 1 = 64)\00", align 1
@.str_expr_212 = private unnamed_addr constant [27 x i8] c"a + (((b * c) - d) * 2) = \00", align 1
@.str_expr_213 = private unnamed_addr constant [33 x i8] c" (10 + (((3 * 5) - 2) * 2) = 36)\00", align 1
@.str_expr_214 = private unnamed_addr constant [33 x i8] c"((a + (b * c)) - (d + 1)) * 2 = \00", align 1
@.str_expr_215 = private unnamed_addr constant [39 x i8] c" (((10 + (3 * 5)) - (2 + 1)) * 2 = 44)\00", align 1
@.str_expr_216 = private unnamed_addr constant [39 x i8] c"(a * ((b + c) - d)) + ((a - b) * c) = \00", align 1
@.str_expr_217 = private unnamed_addr constant [46 x i8] c" ((10 * ((3 + 5) - 2)) + ((10 - 3) * 5) = 95)\00", align 1
@.str_expr_218 = private unnamed_addr constant [45 x i8] c"((a + b) * (c + d)) - ((a - b) * (c - d)) = \00", align 1
@.str_expr_219 = private unnamed_addr constant [52 x i8] c" (((10 + 3) * (5 + 2)) - ((10 - 3) * (5 - 2)) = 70)\00", align 1
@.str_expr_220 = private unnamed_addr constant [19 x i8] c"Simple: (a + b) = \00", align 1
@.str_expr_221 = private unnamed_addr constant [26 x i8] c"Complex: ((a + b) * c) = \00", align 1
@.str_expr_222 = private unnamed_addr constant [31 x i8] c"Nested: (((a + b) * c) - d) = \00", align 1
@.str_expr_223 = private unnamed_addr constant [28 x i8] c"Mixed: (a * b) + (c - d) = \00", align 1
@.str_expr_224 = private unnamed_addr constant [22 x i8] c"Float: (x + y) * z = \00", align 1
@.str_expr_225 = private unnamed_addr constant [24 x i8] c"Chain: a + b + c + d = \00", align 1
@.str_expr_226 = private unnamed_addr constant [25 x i8] c"Precedence: a + b * c = \00", align 1
@.str_expr_227 = private unnamed_addr constant [37 x i8] c"Parentheses override: (a + b) * c = \00", align 1
@.str_expr_228 = private unnamed_addr constant [19 x i8] c"Initial counter = \00", align 1
@.str_expr_229 = private unnamed_addr constant [11 x i8] c"After +1: \00", align 1
@.str_expr_230 = private unnamed_addr constant [11 x i8] c"After *3: \00", align 1
@.str_expr_231 = private unnamed_addr constant [11 x i8] c"After -2: \00", align 1
@.str_expr_232 = private unnamed_addr constant [11 x i8] c"After /2: \00", align 1
@.str_expr_233 = private unnamed_addr constant [26 x i8] c"After (counter + 5) * 2: \00", align 1
@.str_expr_234 = private unnamed_addr constant [32 x i8] c"After ((counter - 3) * 2) + 1: \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4
@global_b = global i32 0, align 4
@global_c = global i32 0, align 4
@global_d = global i32 0, align 4
@global_x = global float 0.0, align 4
@global_y = global float 0.0, align 4
@global_z = global float 0.0, align 4
@global_negative = global i32 0, align 4
@global_zero = global i32 0, align 4
@global_large = global i32 0, align 4
@global_add1 = global i32 0, align 4
@global_add2 = global i32 0, align 4
@global_add3 = global i32 0, align 4
@global_add4 = global i32 0, align 4
@global_add5 = global float 0.0, align 4
@global_add6 = global float 0.0, align 4
@global_add7 = global i32 0, align 4
@global_add8 = global i32 0, align 4
@global_sub1 = global i32 0, align 4
@global_sub2 = global i32 0, align 4
@global_sub3 = global i32 0, align 4
@global_sub4 = global i32 0, align 4
@global_sub5 = global float 0.0, align 4
@global_sub6 = global i32 0, align 4
@global_sub7 = global i32 0, align 4
@global_mul1 = global i32 0, align 4
@global_mul2 = global i32 0, align 4
@global_mul3 = global i32 0, align 4
@global_mul4 = global i32 0, align 4
@global_mul5 = global float 0.0, align 4
@global_mul6 = global i32 0, align 4
@global_mul7 = global i32 0, align 4
@global_div1 = global i32 0, align 4
@global_div2 = global i32 0, align 4
@global_div3 = global i32 0, align 4
@global_div4 = global i32 0, align 4
@global_div5 = global float 0.0, align 4
@global_div6 = global i32 0, align 4
@global_div7 = global i32 0, align 4
@global_mod1 = global i32 0, align 4
@global_mod2 = global i32 0, align 4
@global_mod3 = global i32 0, align 4
@global_mod4 = global i32 0, align 4
@global_mod5 = global i32 0, align 4
@global_mod6 = global i32 0, align 4
@global_paren1 = global i32 0, align 4
@global_paren2 = global i32 0, align 4
@global_paren3 = global i32 0, align 4
@global_paren4 = global i32 0, align 4
@global_paren5 = global i32 0, align 4
@global_paren6 = global i32 0, align 4
@global_paren7 = global float 0.0, align 4
@global_paren8 = global i32 0, align 4
@global_nested1 = global i32 0, align 4
@global_nested2 = global i32 0, align 4
@global_nested3 = global i32 0, align 4
@global_nested4 = global i32 0, align 4
@global_nested5 = global i32 0, align 4
@global_nested6 = global i32 0, align 4
@global_nested7 = global float 0.0, align 4
@global_nested8 = global i32 0, align 4
@global_chain1 = global i32 0, align 4
@global_chain2 = global i32 0, align 4
@global_chain3 = global i32 0, align 4
@global_chain4 = global i32 0, align 4
@global_chain5 = global i32 0, align 4
@global_chain6 = global i32 0, align 4
@global_chain7 = global float 0.0, align 4
@global_chain8 = global i32 0, align 4
@global_prec1 = global i32 0, align 4
@global_prec2 = global i32 0, align 4
@global_prec3 = global i32 0, align 4
@global_prec4 = global i32 0, align 4
@global_prec5 = global i32 0, align 4
@global_prec6 = global i32 0, align 4
@global_prec7 = global i32 0, align 4
@global_prec8 = global i32 0, align 4
@global_prec9 = global float 0.0, align 4
@global_prec10 = global i32 0, align 4
@global_mixed1 = global i32 0, align 4
@global_mixed2 = global i32 0, align 4
@global_mixed3 = global i32 0, align 4
@global_mixed4 = global i32 0, align 4
@global_mixed5 = global i32 0, align 4
@global_mixed6 = global i32 0, align 4
@global_mixed7 = global i32 0, align 4
@global_mixed8 = global i32 0, align 4
@global_zero1 = global i32 0, align 4
@global_zero2 = global i32 0, align 4
@global_zero3 = global i32 0, align 4
@global_zero4 = global i32 0, align 4
@global_zero5 = global i32 0, align 4
@global_zero6 = global i32 0, align 4
@global_neg1 = global i32 0, align 4
@global_neg2 = global i32 0, align 4
@global_neg3 = global i32 0, align 4
@global_neg4 = global i32 0, align 4
@global_neg5 = global i32 0, align 4
@global_neg6 = global i32 0, align 4
@global_large1 = global i32 0, align 4
@global_large2 = global i32 0, align 4
@global_large3 = global i32 0, align 4
@global_large4 = global i32 0, align 4
@global_large5 = global i32 0, align 4
@global_area_rectangle = global i32 0, align 4
@global_perimeter_rectangle = global i32 0, align 4
@global_area_triangle = global i32 0, align 4
@global_pythagorean = global i32 0, align 4
@global_compound_interest = global i32 0, align 4
@global_average = global i32 0, align 4
@global_calc1 = global i32 0, align 4
@global_calc2 = global i32 0, align 4
@global_calc3 = global i32 0, align 4
@global_calc4 = global i32 0, align 4
@global_calc5 = global float 0.0, align 4
@global_extreme1 = global i32 0, align 4
@global_extreme2 = global i32 0, align 4
@global_extreme3 = global i32 0, align 4
@global_extreme4 = global i32 0, align 4
@global_extreme5 = global i32 0, align 4
@global_counter = global i32 0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== COMPREHENSIVE ARITHMETIC OPERATIONS TEST ===" (inline)
  %tmp_1_str = alloca [49 x i8], align 1
  store [49 x i8] c"=== COMPREHENSIVE ARITHMETIC OPERATIONS TEST ===\00", [49 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [49 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_3_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [1 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int a = 10
  store i32 10, i32* @global_a, align 4
  ; int b = 3
  store i32 3, i32* @global_b, align 4
  ; int c = 5
  store i32 5, i32* @global_c, align 4
  ; int d = 2
  store i32 2, i32* @global_d, align 4
  ; float x = 7.5
  %tmp_5 = bitcast i32 1089470464 to float
  store float %tmp_5, float* @global_x, align 4
  ; float y = 2.5
  %tmp_6 = bitcast i32 1075838976 to float
  store float %tmp_6, float* @global_y, align 4
  ; float z = 1.2
  %tmp_7 = bitcast i32 1067030938 to float
  store float %tmp_7, float* @global_z, align 4
  ; int negative = -4
  store i32 -4, i32* @global_negative, align 4
  ; int zero = 0
  store i32 0, i32* @global_zero, align 4
  ; int large = 100
  store i32 100, i32* @global_large, align 4
  ; print "=== TEST VARIABLES ===" (inline)
  %tmp_8_str = alloca [23 x i8], align 1
  store [23 x i8] c"=== TEST VARIABLES ===\00", [23 x i8]* %tmp_8_str, align 1
  %tmp_9 = bitcast [23 x i8]* %tmp_8_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a=" + a + ", b=" + b + ", c=" + c + ", d=" + d
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_10 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_11 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_12 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_13 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x=" + x + ", y=" + y + ", z=" + z
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_14 = load float, float* @global_x, align 4
  %tmp_15 = fpext float %tmp_14 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_16 = load float, float* @global_y, align 4
  %tmp_17 = fpext float %tmp_16 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_18 = load float, float* @global_z, align 4
  %tmp_19 = fpext float %tmp_18 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative=" + negative + ", zero=" + zero + ", lar...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_20 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_21 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_21)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_22 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_23_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_23_str, align 1
  %tmp_24 = bitcast [1 x i8]* %tmp_23_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== SIMPLE BINARY OPERATIONS ===" (inline)
  %tmp_25_str = alloca [33 x i8], align 1
  store [33 x i8] c"=== SIMPLE BINARY OPERATIONS ===\00", [33 x i8]* %tmp_25_str, align 1
  %tmp_26 = bitcast [33 x i8]* %tmp_25_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int add1 = a + b
  %tmp_27 = load i32, i32* @global_a, align 4
  %tmp_28 = load i32, i32* @global_b, align 4
  %tmp_29 = add i32 %tmp_27, %tmp_28
  store i32 %tmp_29, i32* @global_add1, align 4
  ; int add2 = a + 5
  %tmp_30 = load i32, i32* @global_a, align 4
  %tmp_31 = add i32 0, 5
  %tmp_32 = add i32 %tmp_30, %tmp_31
  store i32 %tmp_32, i32* @global_add2, align 4
  ; int add3 = 15 + b
  %tmp_33 = add i32 0, 15
  %tmp_34 = load i32, i32* @global_b, align 4
  %tmp_35 = add i32 %tmp_33, %tmp_34
  store i32 %tmp_35, i32* @global_add3, align 4
  ; int add4 = 7 + 8
  %tmp_36 = add i32 0, 7
  %tmp_37 = add i32 0, 8
  %tmp_38 = add i32 %tmp_36, %tmp_37
  store i32 %tmp_38, i32* @global_add4, align 4
  ; float add5 = x + y
  %tmp_39 = load float, float* @global_x, align 4
  %tmp_40 = load float, float* @global_y, align 4
  %tmp_41 = fadd float %tmp_39, %tmp_40
  store float %tmp_41, float* @global_add5, align 4
  ; float add6 = x + 3.0
  %tmp_42 = load float, float* @global_x, align 4
  %tmp_43 = bitcast i32 1077936128 to float
  %tmp_44 = fadd float %tmp_42, %tmp_43
  store float %tmp_44, float* @global_add6, align 4
  ; int add7 = negative + a
  %tmp_45 = load i32, i32* @global_negative, align 4
  %tmp_46 = load i32, i32* @global_a, align 4
  %tmp_47 = add i32 %tmp_45, %tmp_46
  store i32 %tmp_47, i32* @global_add7, align 4
  ; int add8 = zero + b
  %tmp_48 = load i32, i32* @global_zero, align 4
  %tmp_49 = load i32, i32* @global_b, align 4
  %tmp_50 = add i32 %tmp_48, %tmp_49
  store i32 %tmp_50, i32* @global_add8, align 4
  ; print "Addition Tests:" (inline)
  %tmp_51_str = alloca [16 x i8], align 1
  store [16 x i8] c"Addition Tests:\00", [16 x i8]* %tmp_51_str, align 1
  %tmp_52 = bitcast [16 x i8]* %tmp_51_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_52)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b = " + add1 + " (10 + 3 = 13)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_53 = load i32, i32* @global_add1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_53)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_11, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + 5 = " + add2 + " (10 + 5 = 15)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_54 = load i32, i32* @global_add2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_54)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_13, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "15 + b = " + add3 + " (15 + 3 = 18)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_55 = load i32, i32* @global_add3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_55)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_15, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "7 + 8 = " + add4 + " (7 + 8 = 15)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_56 = load i32, i32* @global_add4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_56)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + y = " + add5 + " (7.5 + 2.5 = 10.0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_57 = load float, float* @global_add5, align 4
  %tmp_58 = fpext float %tmp_57 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_58)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_19, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + 3.0 = " + add6 + " (7.5 + 3.0 = 10.5)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_59 = load float, float* @global_add6, align 4
  %tmp_60 = fpext float %tmp_59 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_60)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_21, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative + a = " + add7 + " (-4 + 10 = 6)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_61 = load i32, i32* @global_add7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_61)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_23, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero + b = " + add8 + " (0 + 3 = 3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_62 = load i32, i32* @global_add8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_62)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_25, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_63_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_63_str, align 1
  %tmp_64 = bitcast [1 x i8]* %tmp_63_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_64)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int sub1 = a - b
  %tmp_65 = load i32, i32* @global_a, align 4
  %tmp_66 = load i32, i32* @global_b, align 4
  %tmp_67 = sub i32 %tmp_65, %tmp_66
  store i32 %tmp_67, i32* @global_sub1, align 4
  ; int sub2 = a - 7
  %tmp_68 = load i32, i32* @global_a, align 4
  %tmp_69 = add i32 0, 7
  %tmp_70 = sub i32 %tmp_68, %tmp_69
  store i32 %tmp_70, i32* @global_sub2, align 4
  ; int sub3 = 20 - b
  %tmp_71 = add i32 0, 20
  %tmp_72 = load i32, i32* @global_b, align 4
  %tmp_73 = sub i32 %tmp_71, %tmp_72
  store i32 %tmp_73, i32* @global_sub3, align 4
  ; int sub4 = 15 - 8
  %tmp_74 = add i32 0, 15
  %tmp_75 = add i32 0, 8
  %tmp_76 = sub i32 %tmp_74, %tmp_75
  store i32 %tmp_76, i32* @global_sub4, align 4
  ; float sub5 = x - y
  %tmp_77 = load float, float* @global_x, align 4
  %tmp_78 = load float, float* @global_y, align 4
  %tmp_79 = fsub float %tmp_77, %tmp_78
  store float %tmp_79, float* @global_sub5, align 4
  ; int sub6 = a - negative
  %tmp_80 = load i32, i32* @global_a, align 4
  %tmp_81 = load i32, i32* @global_negative, align 4
  %tmp_82 = sub i32 %tmp_80, %tmp_81
  store i32 %tmp_82, i32* @global_sub6, align 4
  ; int sub7 = zero - b
  %tmp_83 = load i32, i32* @global_zero, align 4
  %tmp_84 = load i32, i32* @global_b, align 4
  %tmp_85 = sub i32 %tmp_83, %tmp_84
  store i32 %tmp_85, i32* @global_sub7, align 4
  ; print "Subtraction Tests:" (inline)
  %tmp_86_str = alloca [19 x i8], align 1
  store [19 x i8] c"Subtraction Tests:\00", [19 x i8]* %tmp_86_str, align 1
  %tmp_87 = bitcast [19 x i8]* %tmp_86_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_87)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - b = " + sub1 + " (10 - 3 = 7)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_26, i32 0, i32 0))
  %tmp_88 = load i32, i32* @global_sub1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_88)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_27, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - 7 = " + sub2 + " (10 - 7 = 3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_89 = load i32, i32* @global_sub2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_89)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_29, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "20 - b = " + sub3 + " (20 - 3 = 17)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_90 = load i32, i32* @global_sub3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_31, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "15 - 8 = " + sub4 + " (15 - 8 = 7)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_91 = load i32, i32* @global_sub4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_91)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_33, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x - y = " + sub5 + " (7.5 - 2.5 = 5.0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_34, i32 0, i32 0))
  %tmp_92 = load float, float* @global_sub5, align 4
  %tmp_93 = fpext float %tmp_92 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_35, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - negative = " + sub6 + " (10 - (-4) = 14)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_36, i32 0, i32 0))
  %tmp_94 = load i32, i32* @global_sub6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_37, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero - b = " + sub7 + " (0 - 3 = -3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_95 = load i32, i32* @global_sub7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_95)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_39, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_96_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_96_str, align 1
  %tmp_97 = bitcast [1 x i8]* %tmp_96_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_97)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int mul1 = a * b
  %tmp_98 = load i32, i32* @global_a, align 4
  %tmp_99 = load i32, i32* @global_b, align 4
  %tmp_100 = mul i32 %tmp_98, %tmp_99
  store i32 %tmp_100, i32* @global_mul1, align 4
  ; int mul2 = a * 4
  %tmp_101 = load i32, i32* @global_a, align 4
  %tmp_102 = add i32 0, 4
  %tmp_103 = mul i32 %tmp_101, %tmp_102
  store i32 %tmp_103, i32* @global_mul2, align 4
  ; int mul3 = 6 * b
  %tmp_104 = add i32 0, 6
  %tmp_105 = load i32, i32* @global_b, align 4
  %tmp_106 = mul i32 %tmp_104, %tmp_105
  store i32 %tmp_106, i32* @global_mul3, align 4
  ; int mul4 = 7 * 8
  %tmp_107 = add i32 0, 7
  %tmp_108 = add i32 0, 8
  %tmp_109 = mul i32 %tmp_107, %tmp_108
  store i32 %tmp_109, i32* @global_mul4, align 4
  ; float mul5 = x * y
  %tmp_110 = load float, float* @global_x, align 4
  %tmp_111 = load float, float* @global_y, align 4
  %tmp_112 = fmul float %tmp_110, %tmp_111
  store float %tmp_112, float* @global_mul5, align 4
  ; int mul6 = negative * b
  %tmp_113 = load i32, i32* @global_negative, align 4
  %tmp_114 = load i32, i32* @global_b, align 4
  %tmp_115 = mul i32 %tmp_113, %tmp_114
  store i32 %tmp_115, i32* @global_mul6, align 4
  ; int mul7 = zero * a
  %tmp_116 = load i32, i32* @global_zero, align 4
  %tmp_117 = load i32, i32* @global_a, align 4
  %tmp_118 = mul i32 %tmp_116, %tmp_117
  store i32 %tmp_118, i32* @global_mul7, align 4
  ; print "Multiplication Tests:" (inline)
  %tmp_119_str = alloca [22 x i8], align 1
  store [22 x i8] c"Multiplication Tests:\00", [22 x i8]* %tmp_119_str, align 1
  %tmp_120 = bitcast [22 x i8]* %tmp_119_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_120)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b = " + mul1 + " (10 * 3 = 30)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_40, i32 0, i32 0))
  %tmp_121 = load i32, i32* @global_mul1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_121)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_41, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * 4 = " + mul2 + " (10 * 4 = 40)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_42, i32 0, i32 0))
  %tmp_122 = load i32, i32* @global_mul2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_122)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_43, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "6 * b = " + mul3 + " (6 * 3 = 18)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_123 = load i32, i32* @global_mul3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_123)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_45, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "7 * 8 = " + mul4 + " (7 * 8 = 56)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_46, i32 0, i32 0))
  %tmp_124 = load i32, i32* @global_mul4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_124)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_47, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x * y = " + mul5 + " (7.5 * 2.5 = 18.75)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_48, i32 0, i32 0))
  %tmp_125 = load float, float* @global_mul5, align 4
  %tmp_126 = fpext float %tmp_125 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_126)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_49, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative * b = " + mul6 + " (-4 * 3 = -12)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_50, i32 0, i32 0))
  %tmp_127 = load i32, i32* @global_mul6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_127)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_51, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero * a = " + mul7 + " (0 * 10 = 0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_52, i32 0, i32 0))
  %tmp_128 = load i32, i32* @global_mul7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_128)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_53, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_129_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_129_str, align 1
  %tmp_130 = bitcast [1 x i8]* %tmp_129_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_130)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int div1 = a / b
  %tmp_131 = load i32, i32* @global_a, align 4
  %tmp_132 = load i32, i32* @global_b, align 4
  %tmp_133 = sdiv i32 %tmp_131, %tmp_132
  store i32 %tmp_133, i32* @global_div1, align 4
  ; int div2 = a / 2
  %tmp_134 = load i32, i32* @global_a, align 4
  %tmp_135 = add i32 0, 2
  %tmp_136 = sdiv i32 %tmp_134, %tmp_135
  store i32 %tmp_136, i32* @global_div2, align 4
  ; int div3 = 20 / b
  %tmp_137 = add i32 0, 20
  %tmp_138 = load i32, i32* @global_b, align 4
  %tmp_139 = sdiv i32 %tmp_137, %tmp_138
  store i32 %tmp_139, i32* @global_div3, align 4
  ; int div4 = 15 / 3
  %tmp_140 = add i32 0, 15
  %tmp_141 = add i32 0, 3
  %tmp_142 = sdiv i32 %tmp_140, %tmp_141
  store i32 %tmp_142, i32* @global_div4, align 4
  ; float div5 = x / y
  %tmp_143 = load float, float* @global_x, align 4
  %tmp_144 = load float, float* @global_y, align 4
  %tmp_145 = fdiv float %tmp_143, %tmp_144
  store float %tmp_145, float* @global_div5, align 4
  ; int div6 = negative / d
  %tmp_146 = load i32, i32* @global_negative, align 4
  %tmp_147 = load i32, i32* @global_d, align 4
  %tmp_148 = sdiv i32 %tmp_146, %tmp_147
  store i32 %tmp_148, i32* @global_div6, align 4
  ; int div7 = large / a
  %tmp_149 = load i32, i32* @global_large, align 4
  %tmp_150 = load i32, i32* @global_a, align 4
  %tmp_151 = sdiv i32 %tmp_149, %tmp_150
  store i32 %tmp_151, i32* @global_div7, align 4
  ; print "Division Tests:" (inline)
  %tmp_152_str = alloca [16 x i8], align 1
  store [16 x i8] c"Division Tests:\00", [16 x i8]* %tmp_152_str, align 1
  %tmp_153 = bitcast [16 x i8]* %tmp_152_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_153)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a / b = " + div1 + " (10 / 3 = 3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_54, i32 0, i32 0))
  %tmp_154 = load i32, i32* @global_div1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_154)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_55, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a / 2 = " + div2 + " (10 / 2 = 5)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_56, i32 0, i32 0))
  %tmp_155 = load i32, i32* @global_div2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_155)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_57, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "20 / b = " + div3 + " (20 / 3 = 6)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_58, i32 0, i32 0))
  %tmp_156 = load i32, i32* @global_div3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_156)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_59, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "15 / 3 = " + div4 + " (15 / 3 = 5)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_60, i32 0, i32 0))
  %tmp_157 = load i32, i32* @global_div4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_157)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_61, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x / y = " + div5 + " (7.5 / 2.5 = 3.0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_62, i32 0, i32 0))
  %tmp_158 = load float, float* @global_div5, align 4
  %tmp_159 = fpext float %tmp_158 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_159)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_63, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative / d = " + div6 + " (-4 / 2 = -2)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_64, i32 0, i32 0))
  %tmp_160 = load i32, i32* @global_div6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_160)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_65, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large / a = " + div7 + " (100 / 10 = 10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_66, i32 0, i32 0))
  %tmp_161 = load i32, i32* @global_div7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_161)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_67, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_162_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_162_str, align 1
  %tmp_163 = bitcast [1 x i8]* %tmp_162_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_163)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int mod1 = a % b
  %tmp_164 = load i32, i32* @global_a, align 4
  %tmp_165 = load i32, i32* @global_b, align 4
  %tmp_166 = srem i32 %tmp_164, %tmp_165
  store i32 %tmp_166, i32* @global_mod1, align 4
  ; int mod2 = a % 4
  %tmp_167 = load i32, i32* @global_a, align 4
  %tmp_168 = add i32 0, 4
  %tmp_169 = srem i32 %tmp_167, %tmp_168
  store i32 %tmp_169, i32* @global_mod2, align 4
  ; int mod3 = 17 % b
  %tmp_170 = add i32 0, 17
  %tmp_171 = load i32, i32* @global_b, align 4
  %tmp_172 = srem i32 %tmp_170, %tmp_171
  store i32 %tmp_172, i32* @global_mod3, align 4
  ; int mod4 = 15 % 4
  %tmp_173 = add i32 0, 15
  %tmp_174 = add i32 0, 4
  %tmp_175 = srem i32 %tmp_173, %tmp_174
  store i32 %tmp_175, i32* @global_mod4, align 4
  ; int mod5 = large % a
  %tmp_176 = load i32, i32* @global_large, align 4
  %tmp_177 = load i32, i32* @global_a, align 4
  %tmp_178 = srem i32 %tmp_176, %tmp_177
  store i32 %tmp_178, i32* @global_mod5, align 4
  ; int mod6 = 13 % 5
  %tmp_179 = add i32 0, 13
  %tmp_180 = add i32 0, 5
  %tmp_181 = srem i32 %tmp_179, %tmp_180
  store i32 %tmp_181, i32* @global_mod6, align 4
  ; print "Modulo Tests:" (inline)
  %tmp_182_str = alloca [14 x i8], align 1
  store [14 x i8] c"Modulo Tests:\00", [14 x i8]* %tmp_182_str, align 1
  %tmp_183 = bitcast [14 x i8]* %tmp_182_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_183)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a % b = " + mod1 + " (10 % 3 = 1)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_68, i32 0, i32 0))
  %tmp_184 = load i32, i32* @global_mod1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_184)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_69, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a % 4 = " + mod2 + " (10 % 4 = 2)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_70, i32 0, i32 0))
  %tmp_185 = load i32, i32* @global_mod2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_185)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_71, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "17 % b = " + mod3 + " (17 % 3 = 2)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_72, i32 0, i32 0))
  %tmp_186 = load i32, i32* @global_mod3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_186)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_73, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "15 % 4 = " + mod4 + " (15 % 4 = 3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_74, i32 0, i32 0))
  %tmp_187 = load i32, i32* @global_mod4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_187)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_75, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large % a = " + mod5 + " (100 % 10 = 0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_76, i32 0, i32 0))
  %tmp_188 = load i32, i32* @global_mod5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_188)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_77, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "13 % 5 = " + mod6 + " (13 % 5 = 3)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_78, i32 0, i32 0))
  %tmp_189 = load i32, i32* @global_mod6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_189)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_79, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_190_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_190_str, align 1
  %tmp_191 = bitcast [1 x i8]* %tmp_190_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_191)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PARENTHESES OPERATIONS ===" (inline)
  %tmp_192_str = alloca [31 x i8], align 1
  store [31 x i8] c"=== PARENTHESES OPERATIONS ===\00", [31 x i8]* %tmp_192_str, align 1
  %tmp_193 = bitcast [31 x i8]* %tmp_192_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_193)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int paren1 = (a + b) * c
  %tmp_194 = load i32, i32* @global_a, align 4
  %tmp_195 = load i32, i32* @global_b, align 4
  %tmp_196 = add i32 %tmp_194, %tmp_195
  %tmp_197 = load i32, i32* @global_c, align 4
  %tmp_198 = mul i32 %tmp_196, %tmp_197
  store i32 %tmp_198, i32* @global_paren1, align 4
  ; int paren2 = a * (b + c)
  %tmp_199 = load i32, i32* @global_b, align 4
  %tmp_200 = load i32, i32* @global_c, align 4
  %tmp_201 = add i32 %tmp_199, %tmp_200
  %tmp_202 = load i32, i32* @global_a, align 4
  %tmp_203 = mul i32 %tmp_202, %tmp_201
  store i32 %tmp_203, i32* @global_paren2, align 4
  ; int paren3 = (a - b) + (c * d)
  %tmp_204 = load i32, i32* @global_a, align 4
  %tmp_205 = load i32, i32* @global_b, align 4
  %tmp_206 = sub i32 %tmp_204, %tmp_205
  %tmp_207 = load i32, i32* @global_c, align 4
  %tmp_208 = load i32, i32* @global_d, align 4
  %tmp_209 = mul i32 %tmp_207, %tmp_208
  %tmp_210 = add i32 %tmp_206, %tmp_209
  store i32 %tmp_210, i32* @global_paren3, align 4
  ; int paren4 = (a + b) - (c - d)
  %tmp_211 = load i32, i32* @global_a, align 4
  %tmp_212 = load i32, i32* @global_b, align 4
  %tmp_213 = add i32 %tmp_211, %tmp_212
  %tmp_214 = load i32, i32* @global_c, align 4
  %tmp_215 = load i32, i32* @global_d, align 4
  %tmp_216 = sub i32 %tmp_214, %tmp_215
  %tmp_217 = sub i32 %tmp_213, %tmp_216
  store i32 %tmp_217, i32* @global_paren4, align 4
  ; int paren5 = (a * b) / (c - d)
  %tmp_218 = load i32, i32* @global_a, align 4
  %tmp_219 = load i32, i32* @global_b, align 4
  %tmp_220 = mul i32 %tmp_218, %tmp_219
  %tmp_221 = load i32, i32* @global_c, align 4
  %tmp_222 = load i32, i32* @global_d, align 4
  %tmp_223 = sub i32 %tmp_221, %tmp_222
  %tmp_224 = sdiv i32 %tmp_220, %tmp_223
  store i32 %tmp_224, i32* @global_paren5, align 4
  ; int paren6 = (a + b + c) * d
  %tmp_225 = load i32, i32* @global_a, align 4
  %tmp_226 = load i32, i32* @global_b, align 4
  %tmp_227 = add i32 %tmp_225, %tmp_226
  %tmp_228 = load i32, i32* @global_c, align 4
  %tmp_229 = add i32 %tmp_227, %tmp_228
  %tmp_230 = load i32, i32* @global_d, align 4
  %tmp_231 = mul i32 %tmp_229, %tmp_230
  store i32 %tmp_231, i32* @global_paren6, align 4
  ; float paren7 = (x + y) * z
  %tmp_232 = load float, float* @global_x, align 4
  %tmp_233 = load float, float* @global_y, align 4
  %tmp_234 = fadd float %tmp_232, %tmp_233
  %tmp_235 = load float, float* @global_z, align 4
  %tmp_236 = fmul float %tmp_234, %tmp_235
  store float %tmp_236, float* @global_paren7, align 4
  ; int paren8 = (negative + a) * b
  %tmp_237 = load i32, i32* @global_negative, align 4
  %tmp_238 = load i32, i32* @global_a, align 4
  %tmp_239 = add i32 %tmp_237, %tmp_238
  %tmp_240 = load i32, i32* @global_b, align 4
  %tmp_241 = mul i32 %tmp_239, %tmp_240
  store i32 %tmp_241, i32* @global_paren8, align 4
  ; print "Parentheses Tests:" (inline)
  %tmp_242_str = alloca [19 x i8], align 1
  store [19 x i8] c"Parentheses Tests:\00", [19 x i8]* %tmp_242_str, align 1
  %tmp_243 = bitcast [19 x i8]* %tmp_242_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_243)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) * c = " + paren1 + " ((10 + 3) * 5 = 65)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_80, i32 0, i32 0))
  %tmp_244 = load i32, i32* @global_paren1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_244)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_81, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * (b + c) = " + paren2 + " (10 * (3 + 5) = 80)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_82, i32 0, i32 0))
  %tmp_245 = load i32, i32* @global_paren2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_245)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_83, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a - b) + (c * d) = " + paren3 + " ((10 - 3) + (5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_84, i32 0, i32 0))
  %tmp_246 = load i32, i32* @global_paren3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_246)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_85, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) - (c - d) = " + paren4 + " ((10 + 3) - (5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_86, i32 0, i32 0))
  %tmp_247 = load i32, i32* @global_paren4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_247)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_87, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * b) / (c - d) = " + paren5 + " ((10 * 3) / (5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_88, i32 0, i32 0))
  %tmp_248 = load i32, i32* @global_paren5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_248)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_89, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b + c) * d = " + paren6 + " ((10 + 3 + 5) * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_90, i32 0, i32 0))
  %tmp_249 = load i32, i32* @global_paren6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_249)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_91, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(x + y) * z = " + paren7 + " ((7.5 + 2.5) * 1.2 =...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_92, i32 0, i32 0))
  %tmp_250 = load float, float* @global_paren7, align 4
  %tmp_251 = fpext float %tmp_250 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_251)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_93, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(negative + a) * b = " + paren8 + " ((-4 + 10) * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_94, i32 0, i32 0))
  %tmp_252 = load i32, i32* @global_paren8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_252)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_95, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_253_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_253_str, align 1
  %tmp_254 = bitcast [1 x i8]* %tmp_253_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_254)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== NESTED PARENTHESES ===" (inline)
  %tmp_255_str = alloca [27 x i8], align 1
  store [27 x i8] c"=== NESTED PARENTHESES ===\00", [27 x i8]* %tmp_255_str, align 1
  %tmp_256 = bitcast [27 x i8]* %tmp_255_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_256)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int nested1 = ((a + b) * c) - d
  %tmp_257 = load i32, i32* @global_a, align 4
  %tmp_258 = load i32, i32* @global_b, align 4
  %tmp_259 = add i32 %tmp_257, %tmp_258
  %tmp_260 = load i32, i32* @global_c, align 4
  %tmp_261 = mul i32 %tmp_259, %tmp_260
  %tmp_262 = load i32, i32* @global_d, align 4
  %tmp_263 = sub i32 %tmp_261, %tmp_262
  store i32 %tmp_263, i32* @global_nested1, align 4
  ; int nested2 = a + ((b * c) - d)
  %tmp_264 = load i32, i32* @global_b, align 4
  %tmp_265 = load i32, i32* @global_c, align 4
  %tmp_266 = mul i32 %tmp_264, %tmp_265
  %tmp_267 = load i32, i32* @global_d, align 4
  %tmp_268 = sub i32 %tmp_266, %tmp_267
  %tmp_269 = load i32, i32* @global_a, align 4
  %tmp_270 = add i32 %tmp_269, %tmp_268
  store i32 %tmp_270, i32* @global_nested2, align 4
  ; int nested3 = (a * (b + c)) / d
  %tmp_271 = load i32, i32* @global_b, align 4
  %tmp_272 = load i32, i32* @global_c, align 4
  %tmp_273 = add i32 %tmp_271, %tmp_272
  %tmp_274 = load i32, i32* @global_a, align 4
  %tmp_275 = mul i32 %tmp_274, %tmp_273
  %tmp_276 = load i32, i32* @global_d, align 4
  %tmp_277 = sdiv i32 %tmp_275, %tmp_276
  store i32 %tmp_277, i32* @global_nested3, align 4
  ; int nested4 = ((a - b) + c) * (d + 1)
  %tmp_278 = load i32, i32* @global_a, align 4
  %tmp_279 = load i32, i32* @global_b, align 4
  %tmp_280 = sub i32 %tmp_278, %tmp_279
  %tmp_281 = load i32, i32* @global_c, align 4
  %tmp_282 = add i32 %tmp_280, %tmp_281
  %tmp_283 = load i32, i32* @global_d, align 4
  %tmp_284 = add i32 %tmp_283, 1
  %tmp_285 = mul i32 %tmp_282, %tmp_284
  store i32 %tmp_285, i32* @global_nested4, align 4
  ; int nested5 = ((a + b) * (c - d)) + 1
  %tmp_286 = load i32, i32* @global_a, align 4
  %tmp_287 = load i32, i32* @global_b, align 4
  %tmp_288 = add i32 %tmp_286, %tmp_287
  %tmp_289 = load i32, i32* @global_c, align 4
  %tmp_290 = load i32, i32* @global_d, align 4
  %tmp_291 = sub i32 %tmp_289, %tmp_290
  %tmp_292 = mul i32 %tmp_288, %tmp_291
  %tmp_293 = add i32 %tmp_292, 1
  store i32 %tmp_293, i32* @global_nested5, align 4
  ; int nested6 = (a + (b * (c + d))) - 5
  %tmp_294 = load i32, i32* @global_c, align 4
  %tmp_295 = load i32, i32* @global_d, align 4
  %tmp_296 = add i32 %tmp_294, %tmp_295
  %tmp_297 = load i32, i32* @global_b, align 4
  %tmp_298 = mul i32 %tmp_297, %tmp_296
  %tmp_299 = load i32, i32* @global_a, align 4
  %tmp_300 = add i32 %tmp_299, %tmp_298
  %tmp_301 = sub i32 %tmp_300, 5
  store i32 %tmp_301, i32* @global_nested6, align 4
  ; float nested7 = ((x + y) / z) * 2.0
  %tmp_302 = load float, float* @global_x, align 4
  %tmp_303 = load float, float* @global_y, align 4
  %tmp_304 = fadd float %tmp_302, %tmp_303
  %tmp_305 = load float, float* @global_z, align 4
  %tmp_306 = fdiv float %tmp_304, %tmp_305
  %tmp_307 = bitcast i32 1073741824 to float
  %tmp_308 = fmul float %tmp_306, %tmp_307
  store float %tmp_308, float* @global_nested7, align 4
  ; int nested8 = ((negative * b) + a) / d
  %tmp_309 = load i32, i32* @global_negative, align 4
  %tmp_310 = load i32, i32* @global_b, align 4
  %tmp_311 = mul i32 %tmp_309, %tmp_310
  %tmp_312 = load i32, i32* @global_a, align 4
  %tmp_313 = add i32 %tmp_311, %tmp_312
  %tmp_314 = load i32, i32* @global_d, align 4
  %tmp_315 = sdiv i32 %tmp_313, %tmp_314
  store i32 %tmp_315, i32* @global_nested8, align 4
  ; print "Nested Parentheses Tests:" (inline)
  %tmp_316_str = alloca [26 x i8], align 1
  store [26 x i8] c"Nested Parentheses Tests:\00", [26 x i8]* %tmp_316_str, align 1
  %tmp_317 = bitcast [26 x i8]* %tmp_316_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_317)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + b) * c) - d = " + nested1 + " (((10 + 3) * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_96, i32 0, i32 0))
  %tmp_318 = load i32, i32* @global_nested1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_318)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_97, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + ((b * c) - d) = " + nested2 + " (10 + ((3 * 5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_98, i32 0, i32 0))
  %tmp_319 = load i32, i32* @global_nested2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_319)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_99, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * (b + c)) / d = " + nested3 + " ((10 * (3 + 5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_100, i32 0, i32 0))
  %tmp_320 = load i32, i32* @global_nested3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_320)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_101, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a - b) + c) * (d + 1) = " + nested4 + " (((10 -...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_102, i32 0, i32 0))
  %tmp_321 = load i32, i32* @global_nested4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_321)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_103, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + b) * (c - d)) + 1 = " + nested5 + " (((10 +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_104, i32 0, i32 0))
  %tmp_322 = load i32, i32* @global_nested5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_322)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_105, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + (b * (c + d))) - 5 = " + nested6 + " ((10 + ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_106, i32 0, i32 0))
  %tmp_323 = load i32, i32* @global_nested6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_323)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_107, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((x + y) / z) * 2.0 = " + nested7 + " (((7.5 + 2....
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_108, i32 0, i32 0))
  %tmp_324 = load float, float* @global_nested7, align 4
  %tmp_325 = fpext float %tmp_324 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_325)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([38 x i8], [38 x i8]* @.str_expr_109, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(negative * b + a) / d = " + nested8 + " (((-4 * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_110, i32 0, i32 0))
  %tmp_326 = load i32, i32* @global_nested8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_326)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_111, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_327_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_327_str, align 1
  %tmp_328 = bitcast [1 x i8]* %tmp_327_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_328)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== CHAIN OPERATIONS ===" (inline)
  %tmp_329_str = alloca [25 x i8], align 1
  store [25 x i8] c"=== CHAIN OPERATIONS ===\00", [25 x i8]* %tmp_329_str, align 1
  %tmp_330 = bitcast [25 x i8]* %tmp_329_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_330)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int chain1 = a + b + c + d
  %tmp_331 = load i32, i32* @global_a, align 4
  %tmp_332 = load i32, i32* @global_b, align 4
  %tmp_333 = add i32 %tmp_331, %tmp_332
  %tmp_334 = load i32, i32* @global_c, align 4
  %tmp_335 = add i32 %tmp_333, %tmp_334
  %tmp_336 = load i32, i32* @global_d, align 4
  %tmp_337 = add i32 %tmp_335, %tmp_336
  store i32 %tmp_337, i32* @global_chain1, align 4
  ; int chain2 = a - b - c - d
  %tmp_338 = load i32, i32* @global_a, align 4
  %tmp_339 = load i32, i32* @global_b, align 4
  %tmp_340 = sub i32 %tmp_338, %tmp_339
  %tmp_341 = load i32, i32* @global_c, align 4
  %tmp_342 = sub i32 %tmp_340, %tmp_341
  %tmp_343 = load i32, i32* @global_d, align 4
  %tmp_344 = sub i32 %tmp_342, %tmp_343
  store i32 %tmp_344, i32* @global_chain2, align 4
  ; int chain3 = a * b * c * d
  %tmp_345 = load i32, i32* @global_a, align 4
  %tmp_346 = load i32, i32* @global_b, align 4
  %tmp_347 = mul i32 %tmp_345, %tmp_346
  %tmp_348 = load i32, i32* @global_c, align 4
  %tmp_349 = mul i32 %tmp_347, %tmp_348
  %tmp_350 = load i32, i32* @global_d, align 4
  %tmp_351 = mul i32 %tmp_349, %tmp_350
  store i32 %tmp_351, i32* @global_chain3, align 4
  ; int chain4 = 1 + 2 + 3 + 4 + 5 + 6
  %tmp_352 = add i32 1, 2
  %tmp_353 = add i32 %tmp_352, 3
  %tmp_354 = add i32 %tmp_353, 4
  %tmp_355 = add i32 %tmp_354, 5
  %tmp_356 = add i32 %tmp_355, 6
  store i32 %tmp_356, i32* @global_chain4, align 4
  ; int chain5 = 2 * 3 * 4 * 5
  %tmp_357 = mul i32 2, 3
  %tmp_358 = mul i32 %tmp_357, 4
  %tmp_359 = mul i32 %tmp_358, 5
  store i32 %tmp_359, i32* @global_chain5, align 4
  ; int chain6 = 100 - 10 - 5 - 3 - 2
  %tmp_360 = sub i32 100, 10
  %tmp_361 = sub i32 %tmp_360, 5
  %tmp_362 = sub i32 %tmp_361, 3
  %tmp_363 = sub i32 %tmp_362, 2
  store i32 %tmp_363, i32* @global_chain6, align 4
  ; float chain7 = x + y + z + 1.0
  %tmp_364 = load float, float* @global_x, align 4
  %tmp_365 = load float, float* @global_y, align 4
  %tmp_366 = fadd float %tmp_364, %tmp_365
  %tmp_367 = load float, float* @global_z, align 4
  %tmp_368 = fadd float %tmp_366, %tmp_367
  %tmp_369 = bitcast i32 1065353216 to float
  %tmp_370 = fadd float %tmp_368, %tmp_369
  store float %tmp_370, float* @global_chain7, align 4
  ; int chain8 = a + b - c + d - 1
  %tmp_371 = load i32, i32* @global_a, align 4
  %tmp_372 = load i32, i32* @global_b, align 4
  %tmp_373 = add i32 %tmp_371, %tmp_372
  %tmp_374 = load i32, i32* @global_c, align 4
  %tmp_375 = sub i32 %tmp_373, %tmp_374
  %tmp_376 = load i32, i32* @global_d, align 4
  %tmp_377 = add i32 %tmp_375, %tmp_376
  %tmp_378 = sub i32 %tmp_377, 1
  store i32 %tmp_378, i32* @global_chain8, align 4
  ; print "Chain Operations Tests:" (inline)
  %tmp_379_str = alloca [24 x i8], align 1
  store [24 x i8] c"Chain Operations Tests:\00", [24 x i8]* %tmp_379_str, align 1
  %tmp_380 = bitcast [24 x i8]* %tmp_379_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_380)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b + c + d = " + chain1 + " (10 + 3 + 5 + 2 = ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_112, i32 0, i32 0))
  %tmp_381 = load i32, i32* @global_chain1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_381)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_113, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - b - c - d = " + chain2 + " (10 - 3 - 5 - 2 = ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_114, i32 0, i32 0))
  %tmp_382 = load i32, i32* @global_chain2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_382)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_115, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b * c * d = " + chain3 + " (10 * 3 * 5 * 2 = ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_116, i32 0, i32 0))
  %tmp_383 = load i32, i32* @global_chain3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_383)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_expr_117, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "1 + 2 + 3 + 4 + 5 + 6 = " + chain4 + " (1+2+3+4+5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_118, i32 0, i32 0))
  %tmp_384 = load i32, i32* @global_chain4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_384)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_119, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2 * 3 * 4 * 5 = " + chain5 + " (2*3*4*5 = 120)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_120, i32 0, i32 0))
  %tmp_385 = load i32, i32* @global_chain5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_385)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_121, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "100 - 10 - 5 - 3 - 2 = " + chain6 + " (100-10-5-3...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_expr_122, i32 0, i32 0))
  %tmp_386 = load i32, i32* @global_chain6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_386)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_123, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + y + z + 1.0 = " + chain7 + " (7.5************...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_124, i32 0, i32 0))
  %tmp_387 = load float, float* @global_chain7, align 4
  %tmp_388 = fpext float %tmp_387 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_388)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_125, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b - c + d - 1 = " + chain8 + " (10******** = ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_126, i32 0, i32 0))
  %tmp_389 = load i32, i32* @global_chain8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_389)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_127, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_390_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_390_str, align 1
  %tmp_391 = bitcast [1 x i8]* %tmp_390_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_391)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== OPERATOR PRECEDENCE ===" (inline)
  %tmp_392_str = alloca [28 x i8], align 1
  store [28 x i8] c"=== OPERATOR PRECEDENCE ===\00", [28 x i8]* %tmp_392_str, align 1
  %tmp_393 = bitcast [28 x i8]* %tmp_392_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_393)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int prec1 = a + b * c
  %tmp_394 = load i32, i32* @global_b, align 4
  %tmp_395 = load i32, i32* @global_c, align 4
  %tmp_396 = mul i32 %tmp_394, %tmp_395
  %tmp_397 = load i32, i32* @global_a, align 4
  %tmp_398 = add i32 %tmp_397, %tmp_396
  store i32 %tmp_398, i32* @global_prec1, align 4
  ; int prec2 = a * b + c
  %tmp_399 = load i32, i32* @global_a, align 4
  %tmp_400 = load i32, i32* @global_b, align 4
  %tmp_401 = mul i32 %tmp_399, %tmp_400
  %tmp_402 = load i32, i32* @global_c, align 4
  %tmp_403 = add i32 %tmp_401, %tmp_402
  store i32 %tmp_403, i32* @global_prec2, align 4
  ; int prec3 = a + b * c - d
  %tmp_404 = load i32, i32* @global_b, align 4
  %tmp_405 = load i32, i32* @global_c, align 4
  %tmp_406 = mul i32 %tmp_404, %tmp_405
  %tmp_407 = load i32, i32* @global_a, align 4
  %tmp_408 = add i32 %tmp_407, %tmp_406
  %tmp_409 = load i32, i32* @global_d, align 4
  %tmp_410 = sub i32 %tmp_408, %tmp_409
  store i32 %tmp_410, i32* @global_prec3, align 4
  ; int prec4 = a - b * c + d
  %tmp_411 = load i32, i32* @global_b, align 4
  %tmp_412 = load i32, i32* @global_c, align 4
  %tmp_413 = mul i32 %tmp_411, %tmp_412
  %tmp_414 = load i32, i32* @global_a, align 4
  %tmp_415 = sub i32 %tmp_414, %tmp_413
  %tmp_416 = load i32, i32* @global_d, align 4
  %tmp_417 = add i32 %tmp_415, %tmp_416
  store i32 %tmp_417, i32* @global_prec4, align 4
  ; int prec5 = a * b + c * d
  %tmp_418 = load i32, i32* @global_a, align 4
  %tmp_419 = load i32, i32* @global_b, align 4
  %tmp_420 = mul i32 %tmp_418, %tmp_419
  %tmp_421 = load i32, i32* @global_c, align 4
  %tmp_422 = load i32, i32* @global_d, align 4
  %tmp_423 = mul i32 %tmp_421, %tmp_422
  %tmp_424 = add i32 %tmp_420, %tmp_423
  store i32 %tmp_424, i32* @global_prec5, align 4
  ; int prec6 = a * b - c * d
  %tmp_425 = load i32, i32* @global_a, align 4
  %tmp_426 = load i32, i32* @global_b, align 4
  %tmp_427 = mul i32 %tmp_425, %tmp_426
  %tmp_428 = load i32, i32* @global_c, align 4
  %tmp_429 = load i32, i32* @global_d, align 4
  %tmp_430 = mul i32 %tmp_428, %tmp_429
  %tmp_431 = sub i32 %tmp_427, %tmp_430
  store i32 %tmp_431, i32* @global_prec6, align 4
  ; int prec7 = a + b / c * d
  %tmp_432 = load i32, i32* @global_b, align 4
  %tmp_433 = load i32, i32* @global_c, align 4
  %tmp_434 = sdiv i32 %tmp_432, %tmp_433
  %tmp_435 = load i32, i32* @global_d, align 4
  %tmp_436 = mul i32 %tmp_434, %tmp_435
  %tmp_437 = load i32, i32* @global_a, align 4
  %tmp_438 = add i32 %tmp_437, %tmp_436
  store i32 %tmp_438, i32* @global_prec7, align 4
  ; int prec8 = a * b / c + d
  %tmp_439 = load i32, i32* @global_a, align 4
  %tmp_440 = load i32, i32* @global_b, align 4
  %tmp_441 = mul i32 %tmp_439, %tmp_440
  %tmp_442 = load i32, i32* @global_c, align 4
  %tmp_443 = sdiv i32 %tmp_441, %tmp_442
  %tmp_444 = load i32, i32* @global_d, align 4
  %tmp_445 = add i32 %tmp_443, %tmp_444
  store i32 %tmp_445, i32* @global_prec8, align 4
  ; float prec9 = x + y * z
  %tmp_446 = load float, float* @global_y, align 4
  %tmp_447 = load float, float* @global_z, align 4
  %tmp_448 = fmul float %tmp_446, %tmp_447
  %tmp_449 = load float, float* @global_x, align 4
  %tmp_450 = fadd float %tmp_449, %tmp_448
  store float %tmp_450, float* @global_prec9, align 4
  ; int prec10 = a % b + c * d
  %tmp_451 = load i32, i32* @global_a, align 4
  %tmp_452 = load i32, i32* @global_b, align 4
  %tmp_453 = srem i32 %tmp_451, %tmp_452
  %tmp_454 = load i32, i32* @global_c, align 4
  %tmp_455 = load i32, i32* @global_d, align 4
  %tmp_456 = mul i32 %tmp_454, %tmp_455
  %tmp_457 = add i32 %tmp_453, %tmp_456
  store i32 %tmp_457, i32* @global_prec10, align 4
  ; print "Operator Precedence Tests:" (inline)
  %tmp_458_str = alloca [27 x i8], align 1
  store [27 x i8] c"Operator Precedence Tests:\00", [27 x i8]* %tmp_458_str, align 1
  %tmp_459 = bitcast [27 x i8]* %tmp_458_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_459)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b * c = " + prec1 + " (10 + (3 * 5) = 25)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_128, i32 0, i32 0))
  %tmp_460 = load i32, i32* @global_prec1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_460)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_129, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b + c = " + prec2 + " ((10 * 3) + 5 = 35)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_130, i32 0, i32 0))
  %tmp_461 = load i32, i32* @global_prec2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_461)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_131, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b * c - d = " + prec3 + " (10 + (3 * 5) - 2 =...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_132, i32 0, i32 0))
  %tmp_462 = load i32, i32* @global_prec3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_462)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_133, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - b * c + d = " + prec4 + " (10 - (3 * 5) + 2 =...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_134, i32 0, i32 0))
  %tmp_463 = load i32, i32* @global_prec4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_463)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_135, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b + c * d = " + prec5 + " ((10 * 3) + (5 * 2)...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_136, i32 0, i32 0))
  %tmp_464 = load i32, i32* @global_prec5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_464)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_137, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b - c * d = " + prec6 + " ((10 * 3) - (5 * 2)...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_138, i32 0, i32 0))
  %tmp_465 = load i32, i32* @global_prec6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_465)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_139, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b / c * d = " + prec7 + " (10 + ((3 / 5) * 2)...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_140, i32 0, i32 0))
  %tmp_466 = load i32, i32* @global_prec7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_466)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_141, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b / c + d = " + prec8 + " (((10 * 3) / 5) + 2...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_142, i32 0, i32 0))
  %tmp_467 = load i32, i32* @global_prec8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_467)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_143, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + y * z = " + prec9 + " (7.5 + (2.5 * 1.2) = 10...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_144, i32 0, i32 0))
  %tmp_468 = load float, float* @global_prec9, align 4
  %tmp_469 = fpext float %tmp_468 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_469)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_145, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a % b + c * d = " + prec10 + " ((10 % 3) + (5 * 2...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_146, i32 0, i32 0))
  %tmp_470 = load i32, i32* @global_prec10, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_470)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_147, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_471_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_471_str, align 1
  %tmp_472 = bitcast [1 x i8]* %tmp_471_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_472)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== MIXED PRECEDENCE WITH PARENTHESES ===" (inline)
  %tmp_473_str = alloca [42 x i8], align 1
  store [42 x i8] c"=== MIXED PRECEDENCE WITH PARENTHESES ===\00", [42 x i8]* %tmp_473_str, align 1
  %tmp_474 = bitcast [42 x i8]* %tmp_473_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_474)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int mixed1 = (a + b) * c - d
  %tmp_475 = load i32, i32* @global_a, align 4
  %tmp_476 = load i32, i32* @global_b, align 4
  %tmp_477 = add i32 %tmp_475, %tmp_476
  %tmp_478 = load i32, i32* @global_c, align 4
  %tmp_479 = mul i32 %tmp_477, %tmp_478
  %tmp_480 = load i32, i32* @global_d, align 4
  %tmp_481 = sub i32 %tmp_479, %tmp_480
  store i32 %tmp_481, i32* @global_mixed1, align 4
  ; int mixed2 = a + (b * c) - d
  %tmp_482 = load i32, i32* @global_b, align 4
  %tmp_483 = load i32, i32* @global_c, align 4
  %tmp_484 = mul i32 %tmp_482, %tmp_483
  %tmp_485 = load i32, i32* @global_a, align 4
  %tmp_486 = add i32 %tmp_485, %tmp_484
  %tmp_487 = load i32, i32* @global_d, align 4
  %tmp_488 = sub i32 %tmp_486, %tmp_487
  store i32 %tmp_488, i32* @global_mixed2, align 4
  ; int mixed3 = (a + b) * (c - d)
  %tmp_489 = load i32, i32* @global_a, align 4
  %tmp_490 = load i32, i32* @global_b, align 4
  %tmp_491 = add i32 %tmp_489, %tmp_490
  %tmp_492 = load i32, i32* @global_c, align 4
  %tmp_493 = load i32, i32* @global_d, align 4
  %tmp_494 = sub i32 %tmp_492, %tmp_493
  %tmp_495 = mul i32 %tmp_491, %tmp_494
  store i32 %tmp_495, i32* @global_mixed3, align 4
  ; int mixed4 = a * (b + c) - (d * 2)
  %tmp_496 = load i32, i32* @global_b, align 4
  %tmp_497 = load i32, i32* @global_c, align 4
  %tmp_498 = add i32 %tmp_496, %tmp_497
  %tmp_499 = load i32, i32* @global_d, align 4
  %tmp_500 = mul i32 %tmp_499, 2
  %tmp_501 = load i32, i32* @global_a, align 4
  %tmp_502 = mul i32 %tmp_501, %tmp_498
  %tmp_503 = sub i32 %tmp_502, %tmp_500
  store i32 %tmp_503, i32* @global_mixed4, align 4
  ; int mixed5 = (a - b) * c + (d * 3)
  %tmp_504 = load i32, i32* @global_a, align 4
  %tmp_505 = load i32, i32* @global_b, align 4
  %tmp_506 = sub i32 %tmp_504, %tmp_505
  %tmp_507 = load i32, i32* @global_d, align 4
  %tmp_508 = mul i32 %tmp_507, 3
  %tmp_509 = load i32, i32* @global_c, align 4
  %tmp_510 = mul i32 %tmp_506, %tmp_509
  %tmp_511 = add i32 %tmp_510, %tmp_508
  store i32 %tmp_511, i32* @global_mixed5, align 4
  ; int mixed6 = a + b * (c - d) + 1
  %tmp_512 = load i32, i32* @global_c, align 4
  %tmp_513 = load i32, i32* @global_d, align 4
  %tmp_514 = sub i32 %tmp_512, %tmp_513
  %tmp_515 = load i32, i32* @global_b, align 4
  %tmp_516 = mul i32 %tmp_515, %tmp_514
  %tmp_517 = load i32, i32* @global_a, align 4
  %tmp_518 = add i32 %tmp_517, %tmp_516
  %tmp_519 = add i32 %tmp_518, 1
  store i32 %tmp_519, i32* @global_mixed6, align 4
  ; int mixed7 = (a * b) + c - (d / 2)
  %tmp_520 = load i32, i32* @global_a, align 4
  %tmp_521 = load i32, i32* @global_b, align 4
  %tmp_522 = mul i32 %tmp_520, %tmp_521
  %tmp_523 = load i32, i32* @global_d, align 4
  %tmp_524 = sdiv i32 %tmp_523, 2
  %tmp_525 = load i32, i32* @global_c, align 4
  %tmp_526 = add i32 %tmp_522, %tmp_525
  %tmp_527 = sub i32 %tmp_526, %tmp_524
  store i32 %tmp_527, i32* @global_mixed7, align 4
  ; int mixed8 = a - (b + c) * d + 5
  %tmp_528 = load i32, i32* @global_b, align 4
  %tmp_529 = load i32, i32* @global_c, align 4
  %tmp_530 = add i32 %tmp_528, %tmp_529
  %tmp_531 = load i32, i32* @global_d, align 4
  %tmp_532 = mul i32 %tmp_530, %tmp_531
  %tmp_533 = load i32, i32* @global_a, align 4
  %tmp_534 = sub i32 %tmp_533, %tmp_532
  %tmp_535 = add i32 %tmp_534, 5
  store i32 %tmp_535, i32* @global_mixed8, align 4
  ; print "Mixed Precedence Tests:" (inline)
  %tmp_536_str = alloca [24 x i8], align 1
  store [24 x i8] c"Mixed Precedence Tests:\00", [24 x i8]* %tmp_536_str, align 1
  %tmp_537 = bitcast [24 x i8]* %tmp_536_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_537)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) * c - d = " + mixed1 + " ((10 + 3) * 5 - ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_148, i32 0, i32 0))
  %tmp_538 = load i32, i32* @global_mixed1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_538)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_149, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + (b * c) - d = " + mixed2 + " (10 + (3 * 5) - ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_150, i32 0, i32 0))
  %tmp_539 = load i32, i32* @global_mixed2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_539)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_133, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) * (c - d) = " + mixed3 + " ((10 + 3) * (5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_151, i32 0, i32 0))
  %tmp_540 = load i32, i32* @global_mixed3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_540)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_152, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * (b + c) - (d * 2) = " + mixed4 + " (10 * (3 +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_153, i32 0, i32 0))
  %tmp_541 = load i32, i32* @global_mixed4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_541)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_154, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a - b) * c + (d * 3) = " + mixed5 + " ((10 - 3) ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_155, i32 0, i32 0))
  %tmp_542 = load i32, i32* @global_mixed5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_542)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_156, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b * (c - d) + 1 = " + mixed6 + " (10 + 3 * (5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_157, i32 0, i32 0))
  %tmp_543 = load i32, i32* @global_mixed6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_543)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_158, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * b) + c - (d / 2) = " + mixed7 + " ((10 * 3) ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_159, i32 0, i32 0))
  %tmp_544 = load i32, i32* @global_mixed7, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_544)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_160, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - (b + c) * d + 5 = " + mixed8 + " (10 - (3 + 5...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_161, i32 0, i32 0))
  %tmp_545 = load i32, i32* @global_mixed8, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_545)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_162, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_546_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_546_str, align 1
  %tmp_547 = bitcast [1 x i8]* %tmp_546_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_547)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EDGE CASES AND SPECIAL SCENARIOS ===" (inline)
  %tmp_548_str = alloca [41 x i8], align 1
  store [41 x i8] c"=== EDGE CASES AND SPECIAL SCENARIOS ===\00", [41 x i8]* %tmp_548_str, align 1
  %tmp_549 = bitcast [41 x i8]* %tmp_548_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_549)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int zero1 = zero + a
  %tmp_550 = load i32, i32* @global_zero, align 4
  %tmp_551 = load i32, i32* @global_a, align 4
  %tmp_552 = add i32 %tmp_550, %tmp_551
  store i32 %tmp_552, i32* @global_zero1, align 4
  ; int zero2 = zero * a
  %tmp_553 = load i32, i32* @global_zero, align 4
  %tmp_554 = load i32, i32* @global_a, align 4
  %tmp_555 = mul i32 %tmp_553, %tmp_554
  store i32 %tmp_555, i32* @global_zero2, align 4
  ; int zero3 = a + zero
  %tmp_556 = load i32, i32* @global_a, align 4
  %tmp_557 = load i32, i32* @global_zero, align 4
  %tmp_558 = add i32 %tmp_556, %tmp_557
  store i32 %tmp_558, i32* @global_zero3, align 4
  ; int zero4 = a * zero
  %tmp_559 = load i32, i32* @global_a, align 4
  %tmp_560 = load i32, i32* @global_zero, align 4
  %tmp_561 = mul i32 %tmp_559, %tmp_560
  store i32 %tmp_561, i32* @global_zero4, align 4
  ; int zero5 = zero - a
  %tmp_562 = load i32, i32* @global_zero, align 4
  %tmp_563 = load i32, i32* @global_a, align 4
  %tmp_564 = sub i32 %tmp_562, %tmp_563
  store i32 %tmp_564, i32* @global_zero5, align 4
  ; int zero6 = a - zero
  %tmp_565 = load i32, i32* @global_a, align 4
  %tmp_566 = load i32, i32* @global_zero, align 4
  %tmp_567 = sub i32 %tmp_565, %tmp_566
  store i32 %tmp_567, i32* @global_zero6, align 4
  ; print "Zero Operations:" (inline)
  %tmp_568_str = alloca [17 x i8], align 1
  store [17 x i8] c"Zero Operations:\00", [17 x i8]* %tmp_568_str, align 1
  %tmp_569 = bitcast [17 x i8]* %tmp_568_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_569)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero + a = " + zero1 + " (0 + 10 = 10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_163, i32 0, i32 0))
  %tmp_570 = load i32, i32* @global_zero1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_570)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_164, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero * a = " + zero2 + " (0 * 10 = 0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_52, i32 0, i32 0))
  %tmp_571 = load i32, i32* @global_zero2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_571)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_53, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + zero = " + zero3 + " (10 + 0 = 10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_165, i32 0, i32 0))
  %tmp_572 = load i32, i32* @global_zero3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_572)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_166, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * zero = " + zero4 + " (10 * 0 = 0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_167, i32 0, i32 0))
  %tmp_573 = load i32, i32* @global_zero4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_573)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_168, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero - a = " + zero5 + " (0 - 10 = -10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_169, i32 0, i32 0))
  %tmp_574 = load i32, i32* @global_zero5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_574)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_170, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - zero = " + zero6 + " (10 - 0 = 10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_171, i32 0, i32 0))
  %tmp_575 = load i32, i32* @global_zero6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_575)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_172, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_576_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_576_str, align 1
  %tmp_577 = bitcast [1 x i8]* %tmp_576_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_577)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int neg1 = negative + negative
  %tmp_578 = load i32, i32* @global_negative, align 4
  %tmp_579 = load i32, i32* @global_negative, align 4
  %tmp_580 = add i32 %tmp_578, %tmp_579
  store i32 %tmp_580, i32* @global_neg1, align 4
  ; int neg2 = negative * negative
  %tmp_581 = load i32, i32* @global_negative, align 4
  %tmp_582 = load i32, i32* @global_negative, align 4
  %tmp_583 = mul i32 %tmp_581, %tmp_582
  store i32 %tmp_583, i32* @global_neg2, align 4
  ; int neg3 = negative - negative
  %tmp_584 = load i32, i32* @global_negative, align 4
  %tmp_585 = load i32, i32* @global_negative, align 4
  %tmp_586 = sub i32 %tmp_584, %tmp_585
  store i32 %tmp_586, i32* @global_neg3, align 4
  ; int neg4 = a + negative
  %tmp_587 = load i32, i32* @global_a, align 4
  %tmp_588 = load i32, i32* @global_negative, align 4
  %tmp_589 = add i32 %tmp_587, %tmp_588
  store i32 %tmp_589, i32* @global_neg4, align 4
  ; int neg5 = a * negative
  %tmp_590 = load i32, i32* @global_a, align 4
  %tmp_591 = load i32, i32* @global_negative, align 4
  %tmp_592 = mul i32 %tmp_590, %tmp_591
  store i32 %tmp_592, i32* @global_neg5, align 4
  ; int neg6 = negative / d
  %tmp_593 = load i32, i32* @global_negative, align 4
  %tmp_594 = load i32, i32* @global_d, align 4
  %tmp_595 = sdiv i32 %tmp_593, %tmp_594
  store i32 %tmp_595, i32* @global_neg6, align 4
  ; print "Negative Number Operations:" (inline)
  %tmp_596_str = alloca [28 x i8], align 1
  store [28 x i8] c"Negative Number Operations:\00", [28 x i8]* %tmp_596_str, align 1
  %tmp_597 = bitcast [28 x i8]* %tmp_596_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_597)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative + negative = " + neg1 + " (-4 + (-4) = -...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_173, i32 0, i32 0))
  %tmp_598 = load i32, i32* @global_neg1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_598)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_174, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative * negative = " + neg2 + " (-4 * (-4) = 1...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_175, i32 0, i32 0))
  %tmp_599 = load i32, i32* @global_neg2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_599)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_176, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative - negative = " + neg3 + " (-4 - (-4) = 0...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_177, i32 0, i32 0))
  %tmp_600 = load i32, i32* @global_neg3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_600)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_178, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + negative = " + neg4 + " (10 + (-4) = 6)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_179, i32 0, i32 0))
  %tmp_601 = load i32, i32* @global_neg4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_601)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_180, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * negative = " + neg5 + " (10 * (-4) = -40)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_181, i32 0, i32 0))
  %tmp_602 = load i32, i32* @global_neg5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_602)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_182, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative / d = " + neg6 + " (-4 / 2 = -2)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_64, i32 0, i32 0))
  %tmp_603 = load i32, i32* @global_neg6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_603)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_65, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_604_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_604_str, align 1
  %tmp_605 = bitcast [1 x i8]* %tmp_604_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_605)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int large1 = large + large
  %tmp_606 = load i32, i32* @global_large, align 4
  %tmp_607 = load i32, i32* @global_large, align 4
  %tmp_608 = add i32 %tmp_606, %tmp_607
  store i32 %tmp_608, i32* @global_large1, align 4
  ; int large2 = large * a
  %tmp_609 = load i32, i32* @global_large, align 4
  %tmp_610 = load i32, i32* @global_a, align 4
  %tmp_611 = mul i32 %tmp_609, %tmp_610
  store i32 %tmp_611, i32* @global_large2, align 4
  ; int large3 = large / a
  %tmp_612 = load i32, i32* @global_large, align 4
  %tmp_613 = load i32, i32* @global_a, align 4
  %tmp_614 = sdiv i32 %tmp_612, %tmp_613
  store i32 %tmp_614, i32* @global_large3, align 4
  ; int large4 = large % a
  %tmp_615 = load i32, i32* @global_large, align 4
  %tmp_616 = load i32, i32* @global_a, align 4
  %tmp_617 = srem i32 %tmp_615, %tmp_616
  store i32 %tmp_617, i32* @global_large4, align 4
  ; int large5 = large - a
  %tmp_618 = load i32, i32* @global_large, align 4
  %tmp_619 = load i32, i32* @global_a, align 4
  %tmp_620 = sub i32 %tmp_618, %tmp_619
  store i32 %tmp_620, i32* @global_large5, align 4
  ; print "Large Number Operations:" (inline)
  %tmp_621_str = alloca [25 x i8], align 1
  store [25 x i8] c"Large Number Operations:\00", [25 x i8]* %tmp_621_str, align 1
  %tmp_622 = bitcast [25 x i8]* %tmp_621_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_622)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large + large = " + large1 + " (100 + 100 = 200)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_183, i32 0, i32 0))
  %tmp_623 = load i32, i32* @global_large1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_623)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_184, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large * a = " + large2 + " (100 * 10 = 1000)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_185, i32 0, i32 0))
  %tmp_624 = load i32, i32* @global_large2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_624)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_186, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large / a = " + large3 + " (100 / 10 = 10)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_66, i32 0, i32 0))
  %tmp_625 = load i32, i32* @global_large3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_625)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_67, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large % a = " + large4 + " (100 % 10 = 0)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_76, i32 0, i32 0))
  %tmp_626 = load i32, i32* @global_large4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_626)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_77, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large - a = " + large5 + " (100 - 10 = 90)"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_187, i32 0, i32 0))
  %tmp_627 = load i32, i32* @global_large5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_627)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_188, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_628_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_628_str, align 1
  %tmp_629 = bitcast [1 x i8]* %tmp_628_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_629)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== COMPLEX REAL-WORLD SCENARIOS ===" (inline)
  %tmp_630_str = alloca [37 x i8], align 1
  store [37 x i8] c"=== COMPLEX REAL-WORLD SCENARIOS ===\00", [37 x i8]* %tmp_630_str, align 1
  %tmp_631 = bitcast [37 x i8]* %tmp_630_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_631)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int area_rectangle = a * b
  %tmp_632 = load i32, i32* @global_a, align 4
  %tmp_633 = load i32, i32* @global_b, align 4
  %tmp_634 = mul i32 %tmp_632, %tmp_633
  store i32 %tmp_634, i32* @global_area_rectangle, align 4
  ; int perimeter_rectangle = (a + b) * 2
  %tmp_635 = load i32, i32* @global_a, align 4
  %tmp_636 = load i32, i32* @global_b, align 4
  %tmp_637 = add i32 %tmp_635, %tmp_636
  %tmp_638 = mul i32 %tmp_637, 2
  store i32 %tmp_638, i32* @global_perimeter_rectangle, align 4
  ; int area_triangle = (a * b) / 2
  %tmp_639 = load i32, i32* @global_a, align 4
  %tmp_640 = load i32, i32* @global_b, align 4
  %tmp_641 = mul i32 %tmp_639, %tmp_640
  %tmp_642 = sdiv i32 %tmp_641, 2
  store i32 %tmp_642, i32* @global_area_triangle, align 4
  ; int pythagorean = (a * a) + (b * b)
  %tmp_643 = load i32, i32* @global_a, align 4
  %tmp_644 = load i32, i32* @global_a, align 4
  %tmp_645 = mul i32 %tmp_643, %tmp_644
  %tmp_646 = load i32, i32* @global_b, align 4
  %tmp_647 = load i32, i32* @global_b, align 4
  %tmp_648 = mul i32 %tmp_646, %tmp_647
  %tmp_649 = add i32 %tmp_645, %tmp_648
  store i32 %tmp_649, i32* @global_pythagorean, align 4
  ; int compound_interest = large * (1 + (c / 100))
  %tmp_650 = load i32, i32* @global_c, align 4
  %tmp_651 = sdiv i32 %tmp_650, 100
  %tmp_652 = add i32 1, %tmp_651
  %tmp_653 = load i32, i32* @global_large, align 4
  %tmp_654 = mul i32 %tmp_653, %tmp_652
  store i32 %tmp_654, i32* @global_compound_interest, align 4
  ; int average = (a + b + c + d) / 4
  %tmp_655 = load i32, i32* @global_a, align 4
  %tmp_656 = load i32, i32* @global_b, align 4
  %tmp_657 = add i32 %tmp_655, %tmp_656
  %tmp_658 = load i32, i32* @global_c, align 4
  %tmp_659 = add i32 %tmp_657, %tmp_658
  %tmp_660 = load i32, i32* @global_d, align 4
  %tmp_661 = add i32 %tmp_659, %tmp_660
  %tmp_662 = sdiv i32 %tmp_661, 4
  store i32 %tmp_662, i32* @global_average, align 4
  ; print "Mathematical Formulas:" (inline)
  %tmp_663_str = alloca [23 x i8], align 1
  store [23 x i8] c"Mathematical Formulas:\00", [23 x i8]* %tmp_663_str, align 1
  %tmp_664 = bitcast [23 x i8]* %tmp_663_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_664)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Rectangle area (a * b) = " + area_rectangle + " (...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_189, i32 0, i32 0))
  %tmp_665 = load i32, i32* @global_area_rectangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_665)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_41, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Rectangle perimeter ((a + b) * 2) = " + perimeter...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([37 x i8], [37 x i8]* @.str_expr_190, i32 0, i32 0))
  %tmp_666 = load i32, i32* @global_perimeter_rectangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_666)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_191, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Triangle area ((a * b) / 2) = " + area_triangle +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_192, i32 0, i32 0))
  %tmp_667 = load i32, i32* @global_area_triangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_667)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_193, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Pythagorean ((a * a) + (b * b)) = " + pythagorean...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([35 x i8], [35 x i8]* @.str_expr_194, i32 0, i32 0))
  %tmp_668 = load i32, i32* @global_pythagorean, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_668)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_195, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Simple interest (large * (1 + (c / 100))) = " + c...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([45 x i8], [45 x i8]* @.str_expr_196, i32 0, i32 0))
  %tmp_669 = load i32, i32* @global_compound_interest, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_669)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_197, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Average ((a + b + c + d) / 4) = " + average + " (...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_198, i32 0, i32 0))
  %tmp_670 = load i32, i32* @global_average, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_670)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_199, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_671_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_671_str, align 1
  %tmp_672 = bitcast [1 x i8]* %tmp_671_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_672)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int calc1 = ((a + b) * c) + ((d * 2) - 1)
  %tmp_673 = load i32, i32* @global_a, align 4
  %tmp_674 = load i32, i32* @global_b, align 4
  %tmp_675 = add i32 %tmp_673, %tmp_674
  %tmp_676 = load i32, i32* @global_c, align 4
  %tmp_677 = mul i32 %tmp_675, %tmp_676
  %tmp_678 = load i32, i32* @global_d, align 4
  %tmp_679 = mul i32 %tmp_678, 2
  %tmp_680 = sub i32 %tmp_679, 1
  %tmp_681 = add i32 %tmp_677, %tmp_680
  store i32 %tmp_681, i32* @global_calc1, align 4
  ; int calc2 = (a * (b + c)) - ((d + 1) * 2)
  %tmp_682 = load i32, i32* @global_b, align 4
  %tmp_683 = load i32, i32* @global_c, align 4
  %tmp_684 = add i32 %tmp_682, %tmp_683
  %tmp_685 = load i32, i32* @global_a, align 4
  %tmp_686 = mul i32 %tmp_685, %tmp_684
  %tmp_687 = load i32, i32* @global_d, align 4
  %tmp_688 = add i32 %tmp_687, 1
  %tmp_689 = mul i32 %tmp_688, 2
  %tmp_690 = sub i32 %tmp_686, %tmp_689
  store i32 %tmp_690, i32* @global_calc2, align 4
  ; int calc3 = ((a - b) + (c * d)) * ((a / b) + 1)
  %tmp_691 = load i32, i32* @global_a, align 4
  %tmp_692 = load i32, i32* @global_b, align 4
  %tmp_693 = sub i32 %tmp_691, %tmp_692
  %tmp_694 = load i32, i32* @global_c, align 4
  %tmp_695 = load i32, i32* @global_d, align 4
  %tmp_696 = mul i32 %tmp_694, %tmp_695
  %tmp_697 = add i32 %tmp_693, %tmp_696
  %tmp_698 = load i32, i32* @global_a, align 4
  %tmp_699 = load i32, i32* @global_b, align 4
  %tmp_700 = sdiv i32 %tmp_698, %tmp_699
  %tmp_701 = add i32 %tmp_700, 1
  %tmp_702 = mul i32 %tmp_697, %tmp_701
  store i32 %tmp_702, i32* @global_calc3, align 4
  ; int calc4 = (((a + b) * c) / d) + ((a * b) % c)
  %tmp_703 = load i32, i32* @global_a, align 4
  %tmp_704 = load i32, i32* @global_b, align 4
  %tmp_705 = add i32 %tmp_703, %tmp_704
  %tmp_706 = load i32, i32* @global_c, align 4
  %tmp_707 = mul i32 %tmp_705, %tmp_706
  %tmp_708 = load i32, i32* @global_d, align 4
  %tmp_709 = sdiv i32 %tmp_707, %tmp_708
  %tmp_710 = load i32, i32* @global_a, align 4
  %tmp_711 = load i32, i32* @global_b, align 4
  %tmp_712 = mul i32 %tmp_710, %tmp_711
  %tmp_713 = load i32, i32* @global_c, align 4
  %tmp_714 = srem i32 %tmp_712, %tmp_713
  %tmp_715 = add i32 %tmp_709, %tmp_714
  store i32 %tmp_715, i32* @global_calc4, align 4
  ; float calc5 = ((x + y) * z) - ((x - y) / 2.0)
  %tmp_716 = load float, float* @global_x, align 4
  %tmp_717 = load float, float* @global_y, align 4
  %tmp_718 = fadd float %tmp_716, %tmp_717
  %tmp_719 = load float, float* @global_z, align 4
  %tmp_720 = fmul float %tmp_718, %tmp_719
  %tmp_721 = load float, float* @global_x, align 4
  %tmp_722 = load float, float* @global_y, align 4
  %tmp_723 = fsub float %tmp_721, %tmp_722
  %tmp_724 = bitcast i32 1073741824 to float
  %tmp_725 = fdiv float %tmp_723, %tmp_724
  %tmp_726 = fsub float %tmp_720, %tmp_725
  store float %tmp_726, float* @global_calc5, align 4
  ; print "Complex Nested Calculations:" (inline)
  %tmp_727_str = alloca [29 x i8], align 1
  store [29 x i8] c"Complex Nested Calculations:\00", [29 x i8]* %tmp_727_str, align 1
  %tmp_728 = bitcast [29 x i8]* %tmp_727_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_728)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + b) * c) + ((d * 2) - 1) = " + calc1 + " (((...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_200, i32 0, i32 0))
  %tmp_729 = load i32, i32* @global_calc1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_729)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_201, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * (b + c)) - ((d + 1) * 2) = " + calc2 + " ((1...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_202, i32 0, i32 0))
  %tmp_730 = load i32, i32* @global_calc2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_730)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_203, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a - b) + (c * d)) * ((a / b) + 1) = " + calc3 +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_204, i32 0, i32 0))
  %tmp_731 = load i32, i32* @global_calc3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_731)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([46 x i8], [46 x i8]* @.str_expr_205, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(((a + b) * c) / d) + ((a * b) % c) = " + calc4 +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_206, i32 0, i32 0))
  %tmp_732 = load i32, i32* @global_calc4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_732)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([46 x i8], [46 x i8]* @.str_expr_207, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((x + y) * z) - ((x - y) / 2.0) = " + calc5 + " (...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([35 x i8], [35 x i8]* @.str_expr_208, i32 0, i32 0))
  %tmp_733 = load float, float* @global_calc5, align 4
  %tmp_734 = fpext float %tmp_733 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_734)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([51 x i8], [51 x i8]* @.str_expr_209, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_735_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_735_str, align 1
  %tmp_736 = bitcast [1 x i8]* %tmp_735_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_736)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EXTREME NESTING TESTS ===" (inline)
  %tmp_737_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== EXTREME NESTING TESTS ===\00", [30 x i8]* %tmp_737_str, align 1
  %tmp_738 = bitcast [30 x i8]* %tmp_737_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_738)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int extreme1 = (((a + b) * c) - d) + 1
  %tmp_739 = load i32, i32* @global_a, align 4
  %tmp_740 = load i32, i32* @global_b, align 4
  %tmp_741 = add i32 %tmp_739, %tmp_740
  %tmp_742 = load i32, i32* @global_c, align 4
  %tmp_743 = mul i32 %tmp_741, %tmp_742
  %tmp_744 = load i32, i32* @global_d, align 4
  %tmp_745 = sub i32 %tmp_743, %tmp_744
  %tmp_746 = add i32 %tmp_745, 1
  store i32 %tmp_746, i32* @global_extreme1, align 4
  ; int extreme2 = a + (((b * c) - d) * 2)
  %tmp_747 = load i32, i32* @global_b, align 4
  %tmp_748 = load i32, i32* @global_c, align 4
  %tmp_749 = mul i32 %tmp_747, %tmp_748
  %tmp_750 = load i32, i32* @global_d, align 4
  %tmp_751 = sub i32 %tmp_749, %tmp_750
  %tmp_752 = mul i32 %tmp_751, 2
  %tmp_753 = load i32, i32* @global_a, align 4
  %tmp_754 = add i32 %tmp_753, %tmp_752
  store i32 %tmp_754, i32* @global_extreme2, align 4
  ; int extreme3 = ((a + (b * c)) - (d + 1)) * 2
  %tmp_755 = load i32, i32* @global_b, align 4
  %tmp_756 = load i32, i32* @global_c, align 4
  %tmp_757 = mul i32 %tmp_755, %tmp_756
  %tmp_758 = load i32, i32* @global_a, align 4
  %tmp_759 = add i32 %tmp_758, %tmp_757
  %tmp_760 = load i32, i32* @global_d, align 4
  %tmp_761 = add i32 %tmp_760, 1
  %tmp_762 = sub i32 %tmp_759, %tmp_761
  %tmp_763 = mul i32 %tmp_762, 2
  store i32 %tmp_763, i32* @global_extreme3, align 4
  ; int extreme4 = (a * ((b + c) - d)) + ((a - b) * c)
  %tmp_764 = load i32, i32* @global_b, align 4
  %tmp_765 = load i32, i32* @global_c, align 4
  %tmp_766 = add i32 %tmp_764, %tmp_765
  %tmp_767 = load i32, i32* @global_d, align 4
  %tmp_768 = sub i32 %tmp_766, %tmp_767
  %tmp_769 = load i32, i32* @global_a, align 4
  %tmp_770 = mul i32 %tmp_769, %tmp_768
  %tmp_771 = load i32, i32* @global_a, align 4
  %tmp_772 = load i32, i32* @global_b, align 4
  %tmp_773 = sub i32 %tmp_771, %tmp_772
  %tmp_774 = load i32, i32* @global_c, align 4
  %tmp_775 = mul i32 %tmp_773, %tmp_774
  %tmp_776 = add i32 %tmp_770, %tmp_775
  store i32 %tmp_776, i32* @global_extreme4, align 4
  ; int extreme5 = ((a + b) * (c + d)) - ((a - b) * (c - d))
  %tmp_777 = load i32, i32* @global_a, align 4
  %tmp_778 = load i32, i32* @global_b, align 4
  %tmp_779 = add i32 %tmp_777, %tmp_778
  %tmp_780 = load i32, i32* @global_c, align 4
  %tmp_781 = load i32, i32* @global_d, align 4
  %tmp_782 = add i32 %tmp_780, %tmp_781
  %tmp_783 = mul i32 %tmp_779, %tmp_782
  %tmp_784 = load i32, i32* @global_a, align 4
  %tmp_785 = load i32, i32* @global_b, align 4
  %tmp_786 = sub i32 %tmp_784, %tmp_785
  %tmp_787 = load i32, i32* @global_c, align 4
  %tmp_788 = load i32, i32* @global_d, align 4
  %tmp_789 = sub i32 %tmp_787, %tmp_788
  %tmp_790 = mul i32 %tmp_786, %tmp_789
  %tmp_791 = sub i32 %tmp_783, %tmp_790
  store i32 %tmp_791, i32* @global_extreme5, align 4
  ; print "Extreme Nesting Tests:" (inline)
  %tmp_792_str = alloca [23 x i8], align 1
  store [23 x i8] c"Extreme Nesting Tests:\00", [23 x i8]* %tmp_792_str, align 1
  %tmp_793 = bitcast [23 x i8]* %tmp_792_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_793)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(((a + b) * c) - d) + 1 = " + extreme1 + " ((((10...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_210, i32 0, i32 0))
  %tmp_794 = load i32, i32* @global_extreme1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_794)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_211, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + (((b * c) - d) * 2) = " + extreme2 + " (10 + ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_212, i32 0, i32 0))
  %tmp_795 = load i32, i32* @global_extreme2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_795)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_213, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + (b * c)) - (d + 1)) * 2 = " + extreme3 + " ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([33 x i8], [33 x i8]* @.str_expr_214, i32 0, i32 0))
  %tmp_796 = load i32, i32* @global_extreme3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_796)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_215, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * ((b + c) - d)) + ((a - b) * c) = " + extreme...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_expr_216, i32 0, i32 0))
  %tmp_797 = load i32, i32* @global_extreme4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_797)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([46 x i8], [46 x i8]* @.str_expr_217, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + b) * (c + d)) - ((a - b) * (c - d)) = " + e...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([45 x i8], [45 x i8]* @.str_expr_218, i32 0, i32 0))
  %tmp_798 = load i32, i32* @global_extreme5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_798)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([52 x i8], [52 x i8]* @.str_expr_219, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_799_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_799_str, align 1
  %tmp_800 = bitcast [1 x i8]* %tmp_799_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_800)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PRINT EXPRESSION CALCULATIONS ===" (inline)
  %tmp_801_str = alloca [38 x i8], align 1
  store [38 x i8] c"=== PRINT EXPRESSION CALCULATIONS ===\00", [38 x i8]* %tmp_801_str, align 1
  %tmp_802 = bitcast [38 x i8]* %tmp_801_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_802)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Direct calculations in print:" (inline)
  %tmp_803_str = alloca [30 x i8], align 1
  store [30 x i8] c"Direct calculations in print:\00", [30 x i8]* %tmp_803_str, align 1
  %tmp_804 = bitcast [30 x i8]* %tmp_803_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_804)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Simple: (a + b) = " + (a + b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_220, i32 0, i32 0))
  %tmp_805 = load i32, i32* @global_a, align 4
  %tmp_806 = load i32, i32* @global_b, align 4
  %tmp_807 = add i32 %tmp_805, %tmp_806
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_807)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Complex: ((a + b) * c) = " + ((a + b) * c)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_221, i32 0, i32 0))
  %tmp_808 = load i32, i32* @global_a, align 4
  %tmp_809 = load i32, i32* @global_b, align 4
  %tmp_810 = add i32 %tmp_808, %tmp_809
  %tmp_811 = load i32, i32* @global_c, align 4
  %tmp_812 = mul i32 %tmp_810, %tmp_811
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_812)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Nested: (((a + b) * c) - d) = " + (((a + b) * c) ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_222, i32 0, i32 0))
  %tmp_813 = load i32, i32* @global_a, align 4
  %tmp_814 = load i32, i32* @global_b, align 4
  %tmp_815 = add i32 %tmp_813, %tmp_814
  %tmp_816 = load i32, i32* @global_c, align 4
  %tmp_817 = mul i32 %tmp_815, %tmp_816
  %tmp_818 = load i32, i32* @global_d, align 4
  %tmp_819 = sub i32 %tmp_817, %tmp_818
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_819)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Mixed: (a * b) + (c - d) = " + ((a * b) + (c - d)...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_223, i32 0, i32 0))
  %tmp_820 = load i32, i32* @global_a, align 4
  %tmp_821 = load i32, i32* @global_b, align 4
  %tmp_822 = mul i32 %tmp_820, %tmp_821
  %tmp_823 = load i32, i32* @global_c, align 4
  %tmp_824 = load i32, i32* @global_d, align 4
  %tmp_825 = sub i32 %tmp_823, %tmp_824
  %tmp_826 = add i32 %tmp_822, %tmp_825
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_826)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float: (x + y) * z = " + ((x + y) * z)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_224, i32 0, i32 0))
  %tmp_827 = load float, float* @global_x, align 4
  %tmp_828 = load float, float* @global_y, align 4
  %tmp_829 = add i32 %tmp_827, %tmp_828
  %tmp_830 = load float, float* @global_z, align 4
  %tmp_831 = mul i32 %tmp_829, %tmp_830
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_831)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Chain: a + b + c + d = " + (a + b + c + d)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_expr_225, i32 0, i32 0))
  %tmp_832 = load i32, i32* @global_a, align 4
  %tmp_833 = load i32, i32* @global_b, align 4
  %tmp_834 = add i32 %tmp_832, %tmp_833
  %tmp_835 = load i32, i32* @global_c, align 4
  %tmp_836 = add i32 %tmp_834, %tmp_835
  %tmp_837 = load i32, i32* @global_d, align 4
  %tmp_838 = add i32 %tmp_836, %tmp_837
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_838)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Precedence: a + b * c = " + (a + b * c)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_226, i32 0, i32 0))
  %tmp_839 = load i32, i32* @global_b, align 4
  %tmp_840 = load i32, i32* @global_c, align 4
  %tmp_841 = mul i32 %tmp_839, %tmp_840
  %tmp_842 = load i32, i32* @global_a, align 4
  %tmp_843 = add i32 %tmp_842, %tmp_841
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_843)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Parentheses override: (a + b) * c = " + ((a + b) ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([37 x i8], [37 x i8]* @.str_expr_227, i32 0, i32 0))
  %tmp_844 = load i32, i32* @global_a, align 4
  %tmp_845 = load i32, i32* @global_b, align 4
  %tmp_846 = add i32 %tmp_844, %tmp_845
  %tmp_847 = load i32, i32* @global_c, align 4
  %tmp_848 = mul i32 %tmp_846, %tmp_847
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_848)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_849_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_849_str, align 1
  %tmp_850 = bitcast [1 x i8]* %tmp_849_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_850)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== ASSIGNMENT OPERATIONS ===" (inline)
  %tmp_851_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== ASSIGNMENT OPERATIONS ===\00", [30 x i8]* %tmp_851_str, align 1
  %tmp_852 = bitcast [30 x i8]* %tmp_851_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_852)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int counter = 0
  store i32 0, i32* @global_counter, align 4
  ; print expression: "Initial counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_228, i32 0, i32 0))
  %tmp_853 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_853)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter + 1
  %tmp_854 = load i32, i32* @global_counter, align 4
  %tmp_855 = add i32 %tmp_854, 1
  store i32 %tmp_855, i32* @global_counter, align 4
  ; print expression: "After +1: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_229, i32 0, i32 0))
  %tmp_856 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_856)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter * 3
  %tmp_857 = load i32, i32* @global_counter, align 4
  %tmp_858 = mul i32 %tmp_857, 3
  store i32 %tmp_858, i32* @global_counter, align 4
  ; print expression: "After *3: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_230, i32 0, i32 0))
  %tmp_859 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_859)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter - 2
  %tmp_860 = load i32, i32* @global_counter, align 4
  %tmp_861 = sub i32 %tmp_860, 2
  store i32 %tmp_861, i32* @global_counter, align 4
  ; print expression: "After -2: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_231, i32 0, i32 0))
  %tmp_862 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_862)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter / 2
  %tmp_863 = load i32, i32* @global_counter, align 4
  %tmp_864 = sdiv i32 %tmp_863, 2
  store i32 %tmp_864, i32* @global_counter, align 4
  ; print expression: "After /2: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_232, i32 0, i32 0))
  %tmp_865 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_865)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = (counter + 5) * 2
  %tmp_866 = load i32, i32* @global_counter, align 4
  %tmp_867 = add i32 %tmp_866, 5
  %tmp_868 = mul i32 %tmp_867, 2
  store i32 %tmp_868, i32* @global_counter, align 4
  ; print expression: "After (counter + 5) * 2: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_233, i32 0, i32 0))
  %tmp_869 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_869)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = ((counter - 3) * 2) + 1
  %tmp_870 = load i32, i32* @global_counter, align 4
  %tmp_871 = sub i32 %tmp_870, 3
  %tmp_872 = mul i32 %tmp_871, 2
  %tmp_873 = add i32 %tmp_872, 1
  store i32 %tmp_873, i32* @global_counter, align 4
  ; print expression: "After ((counter - 3) * 2) + 1: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([32 x i8], [32 x i8]* @.str_expr_234, i32 0, i32 0))
  %tmp_874 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_874)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_875_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_875_str, align 1
  %tmp_876 = bitcast [1 x i8]* %tmp_875_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_876)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== END OF COMPREHENSIVE ARITHMETIC TEST ===" (inline)
  %tmp_877_str = alloca [45 x i8], align 1
  store [45 x i8] c"=== END OF COMPREHENSIVE ARITHMETIC TEST ===\00", [45 x i8]* %tmp_877_str, align 1
  %tmp_878 = bitcast [45 x i8]* %tmp_877_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_878)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "All arithmetic operations tested successfully!" (inline)
  %tmp_879_str = alloca [47 x i8], align 1
  store [47 x i8] c"All arithmetic operations tested successfully!\00", [47 x i8]* %tmp_879_str, align 1
  %tmp_880 = bitcast [47 x i8]* %tmp_879_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_880)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
