// 24. Error Handling - try-catch, exceptions, error recovery (Rust version)
// This file demonstrates error handling in Rust for comparison with Dolet

use std::fs;
use std::io;
use std::num::ParseIntError;

fn main() {
    println!("=== Error Handling Demo (Rust) ===");
    println!();

    // Basic Result handling with match
    println!("Basic Result handling:");
    let division_result = safe_divide(10, 2);
    match division_result {
        Ok(result) => println!("10 ÷ 2 = {}", result),
        Err(error) => println!("Error: {}", error),
    }
    
    let error_result = safe_divide(10, 0);
    match error_result {
        Ok(result) => println!("10 ÷ 0 = {}", result),
        Err(error) => println!("Error: {}", error),
    }
    println!();

    // Using unwrap_or for default values
    println!("Using unwrap_or for default values:");
    let good_result = safe_divide(20, 4).unwrap_or(0);
    let bad_result = safe_divide(20, 0).unwrap_or(-1);
    
    println!("Good division result (or 0): {}", good_result);
    println!("Bad division result (or -1): {}", bad_result);
    println!();

    // Using unwrap_or_else for computed defaults
    println!("Using unwrap_or_else:");
    let computed_default = safe_divide(15, 0).unwrap_or_else(|err| {
        println!("Computing default due to error: {}", err);
        -999
    });
    println!("Result with computed default: {}", computed_default);
    println!();

    // Option handling
    println!("Option handling:");
    let numbers = vec![1, 2, 3, 4, 5];
    
    match find_number(&numbers, 3) {
        Some(index) => println!("Found 3 at index {}", index),
        None => println!("Number 3 not found"),
    }
    
    match find_number(&numbers, 10) {
        Some(index) => println!("Found 10 at index {}", index),
        None => println!("Number 10 not found"),
    }
    println!();

    // String parsing with error handling
    println!("String parsing with error handling:");
    let valid_numbers = vec!["42", "123", "0", "-17"];
    let invalid_numbers = vec!["abc", "12.5", "", "42x"];
    
    println!("Valid number strings:");
    for num_str in valid_numbers {
        match parse_number(num_str) {
            Ok(num) => println!("  '{}' parsed as {}", num_str, num),
            Err(e) => println!("  '{}' failed to parse: {}", num_str, e),
        }
    }
    
    println!("Invalid number strings:");
    for num_str in invalid_numbers {
        match parse_number(num_str) {
            Ok(num) => println!("  '{}' parsed as {}", num_str, num),
            Err(e) => println!("  '{}' failed to parse: {}", num_str, e),
        }
    }
    println!();

    // File operations with error handling
    println!("File operations with error handling:");
    
    // Try to read a file that exists
    match read_file_safe("Cargo.toml") {
        Ok(content) => println!("✓ Successfully read file (first 100 chars): {}", 
                               &content.chars().take(100).collect::<String>()),
        Err(e) => println!("✗ Failed to read file: {}", e),
    }
    
    // Try to read a file that doesn't exist
    match read_file_safe("nonexistent.txt") {
        Ok(content) => println!("✓ Successfully read file: {}", content),
        Err(e) => println!("✗ Failed to read file: {}", e),
    }
    println!();

    // Multiple error types with custom error enum
    println!("Multiple error types with custom error enum:");
    let test_cases = vec![
        ("42", 2),
        ("0", 5),
        ("abc", 3),
        ("10", 0),
    ];
    
    for (input, divisor) in test_cases {
        match parse_and_divide(input, divisor) {
            Ok(result) => println!("  '{}' ÷ {} = {}", input, divisor, result),
            Err(e) => println!("  Error with '{}' ÷ {}: {}", input, divisor, e),
        }
    }
    println!();

    // Error propagation with ? operator
    println!("Error propagation with ? operator:");
    match complex_operation("5", "3") {
        Ok(result) => println!("Complex operation result: {}", result),
        Err(e) => println!("Complex operation failed: {}", e),
    }
    
    match complex_operation("abc", "3") {
        Ok(result) => println!("Complex operation result: {}", result),
        Err(e) => println!("Complex operation failed: {}", e),
    }
    println!();

    // Panic handling (demonstration)
    println!("Panic handling demonstration:");
    
    // Safe array access
    let array = [1, 2, 3, 4, 5];
    match safe_array_access(&array, 2) {
        Some(value) => println!("Array[2] = {}", value),
        None => println!("Index 2 is out of bounds"),
    }
    
    match safe_array_access(&array, 10) {
        Some(value) => println!("Array[10] = {}", value),
        None => println!("Index 10 is out of bounds"),
    }
    println!();

    // Recovery strategies
    println!("Recovery strategies:");
    
    // Retry mechanism
    let mut attempts = 0;
    let max_attempts = 3;
    
    loop {
        attempts += 1;
        println!("Attempt {} of {}", attempts, max_attempts);
        
        match unreliable_operation(attempts) {
            Ok(result) => {
                println!("✓ Operation succeeded: {}", result);
                break;
            }
            Err(e) => {
                println!("✗ Operation failed: {}", e);
                if attempts >= max_attempts {
                    println!("Max attempts reached, giving up");
                    break;
                } else {
                    println!("Retrying...");
                }
            }
        }
    }
    println!();

    // Fallback values
    println!("Fallback values:");
    let config_result = load_config()
        .or_else(|_| load_default_config())
        .unwrap_or_else(|_| Config::emergency_default());
    
    println!("Loaded config: {:?}", config_result);
    println!();

    // Error logging and reporting
    println!("Error logging and reporting:");
    let operations = vec![
        ("valid_op", true),
        ("invalid_op", false),
        ("another_valid", true),
        ("another_invalid", false),
    ];
    
    for (op_name, should_succeed) in operations {
        match logged_operation(op_name, should_succeed) {
            Ok(result) => println!("✓ {}: {}", op_name, result),
            Err(e) => println!("✗ {}: {}", op_name, e),
        }
    }
    println!();

    // Validation with multiple checks
    println!("Validation with multiple checks:");
    let user_inputs = vec![
        UserInput { name: "Ahmed".to_string(), age: 25, email: "<EMAIL>".to_string() },
        UserInput { name: "".to_string(), age: 25, email: "<EMAIL>".to_string() },
        UserInput { name: "Sara".to_string(), age: 15, email: "<EMAIL>".to_string() },
        UserInput { name: "Omar".to_string(), age: 30, email: "invalid-email".to_string() },
    ];
    
    for input in user_inputs {
        match validate_user_input(&input) {
            Ok(_) => println!("✓ Valid user: {}", input.name),
            Err(errors) => {
                println!("✗ Invalid user '{}': {}", input.name, errors.join(", "));
            }
        }
    }
    println!();

    // Graceful degradation
    println!("Graceful degradation:");
    let service_result = call_external_service();
    match service_result {
        Ok(data) => println!("✓ External service data: {}", data),
        Err(e) => {
            println!("✗ External service failed: {}", e);
            println!("ℹ Using cached data instead");
            let cached_data = get_cached_data();
            println!("✓ Cached data: {}", cached_data);
        }
    }
    println!();

    println!("=== End of Error Handling Demo ===");
}

// Basic error handling functions
fn safe_divide(a: i32, b: i32) -> Result<i32, String> {
    if b == 0 {
        Err("Division by zero".to_string())
    } else {
        Ok(a / b)
    }
}

fn find_number(numbers: &[i32], target: i32) -> Option<usize> {
    numbers.iter().position(|&x| x == target)
}

fn parse_number(s: &str) -> Result<i32, ParseIntError> {
    s.parse::<i32>()
}

fn read_file_safe(filename: &str) -> Result<String, io::Error> {
    fs::read_to_string(filename)
}

// Custom error types
#[derive(Debug)]
enum MathError {
    ParseError(ParseIntError),
    DivisionByZero,
}

impl std::fmt::Display for MathError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            MathError::ParseError(e) => write!(f, "Parse error: {}", e),
            MathError::DivisionByZero => write!(f, "Division by zero"),
        }
    }
}

impl From<ParseIntError> for MathError {
    fn from(error: ParseIntError) -> Self {
        MathError::ParseError(error)
    }
}

fn parse_and_divide(input: &str, divisor: i32) -> Result<i32, MathError> {
    let number = input.parse::<i32>()?;
    
    if divisor == 0 {
        return Err(MathError::DivisionByZero);
    }
    
    Ok(number / divisor)
}

// Error propagation
fn complex_operation(a: &str, b: &str) -> Result<i32, MathError> {
    let num_a = a.parse::<i32>()?;
    let num_b = b.parse::<i32>()?;
    
    if num_b == 0 {
        return Err(MathError::DivisionByZero);
    }
    
    let result = (num_a + num_b) / num_b;
    Ok(result * 2)
}

// Safe array access
fn safe_array_access(arr: &[i32], index: usize) -> Option<i32> {
    arr.get(index).copied()
}

// Retry mechanism
fn unreliable_operation(attempt: i32) -> Result<String, String> {
    if attempt < 3 {
        Err(format!("Simulated failure on attempt {}", attempt))
    } else {
        Ok("Success!".to_string())
    }
}

// Configuration loading with fallbacks
#[derive(Debug)]
struct Config {
    name: String,
    version: String,
}

impl Config {
    fn emergency_default() -> Self {
        Config {
            name: "Emergency Config".to_string(),
            version: "0.0.1".to_string(),
        }
    }
}

fn load_config() -> Result<Config, String> {
    Err("Config file not found".to_string())
}

fn load_default_config() -> Result<Config, String> {
    Err("Default config also failed".to_string())
}

// Logged operations
fn logged_operation(name: &str, should_succeed: bool) -> Result<String, String> {
    println!("  [LOG] Starting operation: {}", name);
    
    if should_succeed {
        println!("  [LOG] Operation {} completed successfully", name);
        Ok(format!("Result from {}", name))
    } else {
        println!("  [LOG] Operation {} failed", name);
        Err(format!("Operation {} failed", name))
    }
}

// Validation
struct UserInput {
    name: String,
    age: u32,
    email: String,
}

fn validate_user_input(input: &UserInput) -> Result<(), Vec<String>> {
    let mut errors = Vec::new();
    
    if input.name.trim().is_empty() {
        errors.push("Name cannot be empty".to_string());
    }
    
    if input.age < 18 {
        errors.push("Age must be 18 or older".to_string());
    }
    
    if !input.email.contains('@') {
        errors.push("Email must contain @".to_string());
    }
    
    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors)
    }
}

// External service simulation
fn call_external_service() -> Result<String, String> {
    // Simulate service failure
    Err("Service temporarily unavailable".to_string())
}

fn get_cached_data() -> String {
    "Cached data from previous successful call".to_string()
}
