; ModuleID = '11_advanced_features.19fa245b12374fe-cgu.0'
source_filename = "11_advanced_features.19fa245b12374fe-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%Message = type { i64, [2 x i64] }
%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@alloc_31365cfefba383c4d2bf6b6a04cc10aa = private unnamed_addr constant [17 x i8] c"capacity overflow", align 1
@alloc_11d257f5ed6cc7fc38feaa801053bac6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_31365cfefba383c4d2bf6b6a04cc10aa, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@anon.bf27c6c651a4deb7bc5a04d4d8800937.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h4d642c06691b3a34E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hbb5719c4a6bf21d3E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hbb5719c4a6bf21d3E" }>, align 8
@anon.bf27c6c651a4deb7bc5a04d4d8800937.1 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_dd79dfae92e8fdc23813c4c7a1b7cf72 = private unnamed_addr constant [228 x i8] c"unsafe precondition(s) violated: ptr::write_bytes requires that the destination pointer is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@vtable.1 = private unnamed_addr constant <{ [24 x i8], ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h824649dbc05b35eaE" }>, align 8
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@anon.bf27c6c651a4deb7bc5a04d4d8800937.2 = private unnamed_addr constant [16 x i8] c"\01\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00", align 8
@alloc_b3f4535eec761591e70a90f04eace3be = private unnamed_addr constant [90 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\exact_size.rs", align 1
@alloc_b9236782e79d68e69a864d7209dc159e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_b3f4535eec761591e70a90f04eace3be, [16 x i8] c"Z\00\00\00\00\00\00\00z\00\00\00\09\00\00\00" }>, align 8
@alloc_299852982c5db63901a556a4e2fe0f7e = private unnamed_addr constant [88 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\iterator.rs", align 1
@alloc_7277ca8d30122b7203dfd66509383767 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_299852982c5db63901a556a4e2fe0f7e, [16 x i8] c"X\00\00\00\00\00\00\00\C1\07\00\00\09\00\00\00" }>, align 8
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@anon.bf27c6c651a4deb7bc5a04d4d8800937.3 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] c"\01\00\00\00\00\00\00\80", [8 x i8] undef }>, align 8
@alloc_00e5a13bfec3eaffacf28cad02b1dee1 = private unnamed_addr constant [80 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\raw_vec\\mod.rs", align 1
@alloc_3d99a694a639f36d512dfe286335fb89 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_00e5a13bfec3eaffacf28cad02b1dee1, [16 x i8] c"P\00\00\00\00\00\00\00.\02\00\00\11\00\00\00" }>, align 8
@alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8 = private unnamed_addr constant [85 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\accum.rs", align 1
@alloc_2ff564f83739a041825038989c62f69d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8, [16 x i8] c"U\00\00\00\00\00\00\00\95\00\00\00\01\00\00\00" }>, align 8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_c10cf53f0ee0f754900a21ca81d4eda2 = private unnamed_addr constant [78 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\slice\\iter.rs", align 1
@alloc_abafb912297f657dc3b31c1b7ae4c4e5 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_c10cf53f0ee0f754900a21ca81d4eda2, [16 x i8] c"N\00\00\00\00\00\00\00\8E\00\00\00\01\00\00\00" }>, align 8
@alloc_d4583cf0b5046a288b871b001c5323ec = private unnamed_addr constant [38 x i8] c"=== Advanced Features Demo (Rust) ===\0A", align 1
@alloc_d95b7873c88797b3e97c1df5861c4f6f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d4583cf0b5046a288b871b001c5323ec, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9b18944505dd60a539e8ca1eef8309dc = private unnamed_addr constant [19 x i8] c"Match expressions:\0A", align 1
@alloc_3f2bfd9cdf44226e5fc1d6c09bca0aff = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9b18944505dd60a539e8ca1eef8309dc, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_e3976b92482fb4550470b093759847a6 = private unnamed_addr constant [9 x i8] c"Wednesday", align 1
@alloc_76cb799d8ab175bd043d6ed60470cdbd = private unnamed_addr constant [4 x i8] c"Day ", align 1
@alloc_c78a450e571c59bd9864ddd59ec6978c = private unnamed_addr constant [4 x i8] c" is ", align 1
@alloc_4bd9072c24f5c3baa5448cf37f29924e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_76cb799d8ab175bd043d6ed60470cdbd, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_c78a450e571c59bd9864ddd59ec6978c, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7a60d62387b70c16f2684a11b8b49eba = private unnamed_addr constant [25 x i8] c"Grade calculation match:\0A", align 1
@alloc_d1ddbe8f23419703c94bbe4cd90bf63f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7a60d62387b70c16f2684a11b8b49eba, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_4b372b42a7e59c4e87186c5d2ddb750d = private unnamed_addr constant [1 x i8] c"F", align 1
@alloc_650fee8799ff48e1b6ec6752e85c5de0 = private unnamed_addr constant [6 x i8] c"Failed", align 1
@alloc_fba4efe8e4f7fab8265f1b3a352c9317 = private unnamed_addr constant [1 x i8] c"D", align 1
@alloc_0c6ecae49f41ef7e73908e09a56ad40c = private unnamed_addr constant [13 x i8] c"Below average", align 1
@alloc_e57470275a219d8492d489e56910499e = private unnamed_addr constant [1 x i8] c"C", align 1
@alloc_c5b51882c377e9c817ac1a6aeb205676 = private unnamed_addr constant [19 x i8] c"Average performance", align 1
@alloc_d3bbdebcd7d668a59dc59a90afdc2fa1 = private unnamed_addr constant [1 x i8] c"B", align 1
@alloc_2b54c328cbbde02db9a83dfa81c4efd8 = private unnamed_addr constant [17 x i8] c"Good performance!", align 1
@alloc_e2ead6761956d440a2a6c3412b417ffa = private unnamed_addr constant [1 x i8] c"A", align 1
@alloc_21403cf1f221cae673edc7d8ebdeb7e2 = private unnamed_addr constant [22 x i8] c"Excellent performance!", align 1
@alloc_2e2f2e05479b7f96f9e0ace3eeea3d1d = private unnamed_addr constant [7 x i8] c"Score: ", align 1
@alloc_2e5386e7920bc4812fc6996afbc62454 = private unnamed_addr constant [9 x i8] c", Grade: ", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_c3417ee47490ee12b1af9bd1501db072 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2e2f2e05479b7f96f9e0ace3eeea3d1d, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_2e5386e7920bc4812fc6996afbc62454, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_89888e452b4e9c155dec2f7948adc7fc = private unnamed_addr constant [28 x i8] c"Error handling with Result:\0A", align 1
@alloc_6482d515921d1e84bda6c945bdc55428 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_89888e452b4e9c155dec2f7948adc7fc, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_2911f9d2c163869fda747b8ef563ce3f = private unnamed_addr constant [17 x i8] c"Division result: ", align 1
@alloc_eb9b4b67a77a57c0a57547dda75d76c5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2911f9d2c163869fda747b8ef563ce3f, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d18fb8cf1209cbe87459f8f714d25840 = private unnamed_addr constant [7 x i8] c"Error: ", align 1
@alloc_65a14c287e0a73af54985a3db7645c00 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d18fb8cf1209cbe87459f8f714d25840, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f35959cf01f3a190c734ef6e04d24547 = private unnamed_addr constant [17 x i8] c"Option handling:\0A", align 1
@alloc_5a58cc7a994f10d675cda38042f5f55b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f35959cf01f3a190c734ef6e04d24547, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_3a3539c51f57fffd3c34fe4eaefb5f52 = private unnamed_addr constant [23 x i8] c"11_advanced_features.rs", align 1
@alloc_cd1c7cc0951dc4db27745c1d87a79cda = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\007\00\00\00\13\00\00\00" }>, align 8
@alloc_f2dd52df6e5d8abc6baf179cc485f30b = private unnamed_addr constant [19 x i8] c"Number 3 not found\0A", align 1
@alloc_687bd6763d5d28b5ad99923878bb93ea = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f2dd52df6e5d8abc6baf179cc485f30b, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_40e47aab5aabb329021b17d6bd7fa39b = private unnamed_addr constant [17 x i8] c"Found 3 at index ", align 1
@alloc_dee56960b710635c6626f85dfda544d2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_40e47aab5aabb329021b17d6bd7fa39b, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6749d64b5578e581d3b7c418ee8407e8 = private unnamed_addr constant [20 x i8] c"Number 10 not found\0A", align 1
@alloc_cb0bb4cb60c5206550c227253c866203 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6749d64b5578e581d3b7c418ee8407e8, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_7bbd7857f911e651b7a530f0e0875223 = private unnamed_addr constant [18 x i8] c"Found 10 at index ", align 1
@alloc_e53fac3b6c03b3faca068335dee1e35f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7bbd7857f911e651b7a530f0e0875223, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2b51fc4ee15bfa3676ea1895c44eab9d = private unnamed_addr constant [37 x i8] c"Pattern matching with destructuring:\0A", align 1
@alloc_379906d5c2e22abd715edca2750793f1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2b51fc4ee15bfa3676ea1895c44eab9d, [8 x i8] c"%\00\00\00\00\00\00\00" }>, align 8
@alloc_a5a79f2447c22e8c64baa82231b62d27 = private unnamed_addr constant [13 x i8] c"Origin point\0A", align 1
@alloc_c2ab13edf99533dc761b1d39748dc01a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a5a79f2447c22e8c64baa82231b62d27, [8 x i8] c"\0D\00\00\00\00\00\00\00" }>, align 8
@alloc_f48a75c8ab026bde8deac26a6cef54a7 = private unnamed_addr constant [13 x i8] c"On Y-axis at ", align 1
@alloc_dbe3a2b4bebe9869fa2f95b0be39135e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f48a75c8ab026bde8deac26a6cef54a7, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7c836f719c3b07f45678ecc6e500cea9 = private unnamed_addr constant [13 x i8] c"On X-axis at ", align 1
@alloc_ce07cd6fb79bc33e157cd9eec2db1a29 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7c836f719c3b07f45678ecc6e500cea9, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_42fb3eb31ed5d990a14e21a39c45f798 = private unnamed_addr constant [10 x i8] c"Point at (", align 1
@alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b = private unnamed_addr constant [2 x i8] c")\0A", align 1
@alloc_7a78c9132da20336fbc72ada35ade4bd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_42fb3eb31ed5d990a14e21a39c45f798, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_be07c0d1cd22e7f71d6130971a13bcb3 = private unnamed_addr constant [38 x i8] c"Loop control with break and continue:\0A", align 1
@alloc_f941f3dcc13ef649ca6902d97bfb92c1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_be07c0d1cd22e7f71d6130971a13bcb3, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_8b7a9fe6835e03f55f4632b01ed9f687 = private unnamed_addr constant [9 x i8] c"Skipping ", align 1
@alloc_711012591040f0d9a9f8c5bc1491a916 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8b7a9fe6835e03f55f4632b01ed9f687, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_46021b73721b4b5cd42a235164b99216 = private unnamed_addr constant [12 x i8] c"Breaking at ", align 1
@alloc_8861b35c002520e58db630b9eb4ac7c8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_46021b73721b4b5cd42a235164b99216, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_774ef8c0ab74c4b03e3a75063b30b593 = private unnamed_addr constant [34 x i8] c"Nested loops with labeled breaks:\0A", align 1
@alloc_d1b4bf154dd7ebfb23a44891ca0b64fb = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_774ef8c0ab74c4b03e3a75063b30b593, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_dd1a548c59740205f43c56608984ffc0 = private unnamed_addr constant [15 x i8] c"Outer loop i = ", align 1
@alloc_d1cf28f64ece7698e78650aecdbea28a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_dd1a548c59740205f43c56608984ffc0, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_bd5df2fcf5618adc27ee51f30af5e148 = private unnamed_addr constant [17 x i8] c"  Inner loop j = ", align 1
@alloc_2a819f3db43106d9cd1ca4fd95f3e28c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bd5df2fcf5618adc27ee51f30af5e148, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_25f8897230464446fa4c413ec3df4352 = private unnamed_addr constant [26 x i8] c"  Breaking outer loop at (", align 1
@alloc_640a9d31705ea67aa71665ec0f6ad3e5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_25f8897230464446fa4c413ec3df4352, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_6a5d18ea3524652a80539a6d2a31be14 = private unnamed_addr constant [28 x i8] c"While let pattern matching:\0A", align 1
@alloc_5b9410d9839f8d1653e008ff2b4922db = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6a5d18ea3524652a80539a6d2a31be14, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_6850c1e8870e71e2873e74cec4a4cd10 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00n\00\00\00\15\00\00\00" }>, align 8
@alloc_d45626436190af9443b8499927db54b7 = private unnamed_addr constant [8 x i8] c"Popped: ", align 1
@alloc_5a5a9d2b50583c48057f5cf335935995 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d45626436190af9443b8499927db54b7, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5f357fd34fc37eb3a2bdb15539bd4345 = private unnamed_addr constant [15 x i8] c"Stack is empty\0A", align 1
@alloc_4ab3b78434807013697baab831df5cbd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5f357fd34fc37eb3a2bdb15539bd4345, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_755684d9956bb94554ee0a0625122531 = private unnamed_addr constant [25 x i8] c"If let pattern matching:\0A", align 1
@alloc_194efbb27be06150e6ed97df92b6f9cc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_755684d9956bb94554ee0a0625122531, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_0c163f72fb1c93a74b8162305f784748 = private unnamed_addr constant [12 x i8] c"Got number: ", align 1
@alloc_46ad37ebb913a091a0556d156eff58dd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0c163f72fb1c93a74b8162305f784748, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_58681ece09e9f1178e85317305a63d13 = private unnamed_addr constant [16 x i8] c"No number found\0A", align 1
@alloc_c69caa39a8fdbfb4b4197c3379dcc10f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_58681ece09e9f1178e85317305a63d13, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_047add81d360afde7801c9db950b0598 = private unnamed_addr constant [28 x i8] c"Advanced match with guards:\0A", align 1
@alloc_e9396b0f73ad0f184290b114db1dfaa8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_047add81d360afde7801c9db950b0598, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_2f320318ba2bb9119e0a03a5d802b4bc = private unnamed_addr constant [9 x i8] c" is zero\0A", align 1
@alloc_c5427c6923764723eba9feff02f43a1d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_2f320318ba2bb9119e0a03a5d802b4bc, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_e76b46e85ccffea9e99912ede5615430 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\85\00\00\00\0E\00\00\00" }>, align 8
@alloc_38f5228c4e5810a0c1d07ffeafd5af87 = private unnamed_addr constant [22 x i8] c" is positive and even\0A", align 1
@alloc_801fe18f9b3b363cf2faf6e860ca6b93 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_38f5228c4e5810a0c1d07ffeafd5af87, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_dceabf0edc2d73c487a7f2899f91f6fa = private unnamed_addr constant [21 x i8] c" is positive and odd\0A", align 1
@alloc_46a79ce96611ad4b486a2da732e191e1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_dceabf0edc2d73c487a7f2899f91f6fa, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_3b71843adc241811783c9bcc86ac6c66 = private unnamed_addr constant [13 x i8] c" is negative\0A", align 1
@alloc_8973458a6bea5a3ac0edc9fc59624c0d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_3b71843adc241811783c9bcc86ac6c66, [8 x i8] c"\0D\00\00\00\00\00\00\00" }>, align 8
@alloc_21e652805e71526fe7def2d4bf3c7ce2 = private unnamed_addr constant [18 x i8] c"Closure examples:\0A", align 1
@alloc_1e049b8719450bb928bf5c7092963580 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_21e652805e71526fe7def2d4bf3c7ce2, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_7300349b7be2d95ed15127d25d40823e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\8C\00\00\00\13\00\00\00" }>, align 8
@alloc_0371a86c633a9d4f525c073b1f6e087d = private unnamed_addr constant [9 x i8] c"Doubled: ", align 1
@alloc_c8ba73d273c0b252f5b4147bdfdc4a8a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0371a86c633a9d4f525c073b1f6e087d, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ac9c9fc18c2128602be58c16157115b4 = private unnamed_addr constant [7 x i8] c"Evens: ", align 1
@alloc_77cc6e4a047661fb3e5e6b0040f6dc84 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ac9c9fc18c2128602be58c16157115b4, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5b0102dc258292fd5e83bca8293a8d53 = private unnamed_addr constant [5 x i8] c"Sum: ", align 1
@alloc_fae7b78f3403b48b2870d7d78fcc4226 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5b0102dc258292fd5e83bca8293a8d53, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_027a81d1c5a19d35405ac7f087c785b2 = private unnamed_addr constant [19 x i8] c"Iterator chaining:\0A", align 1
@alloc_023c5ace34efd49fe59a2f1373515833 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_027a81d1c5a19d35405ac7f087c785b2, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_b0b1e414f8633a499943926ae39757ae = private unnamed_addr constant [24 x i8] c"Even squares from 1-10: ", align 1
@alloc_aed33c94cb45050d4d0f1fb9eed512f2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b0b1e414f8633a499943926ae39757ae, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4136ff7aeb3f3894857ca7c1f7fa5875 = private unnamed_addr constant [27 x i8] c"Enum with associated data:\0A", align 1
@alloc_6284f49c09bbc86b3693660fbfdaa60a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4136ff7aeb3f3894857ca7c1f7fa5875, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_3edef0b68cfa9c8c95e6d4fe1a68842b = private unnamed_addr constant [5 x i8] c"Hello", align 1
@alloc_17418fe1e2725972c923094ec36fb567 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\A3\00\00\00\14\00\00\00" }>, align 8
@alloc_85df74ba69548a9fe4f0630bc4ef809c = private unnamed_addr constant [28 x i8] c"Advanced error propagation:\0A", align 1
@alloc_6aaf3f3768fd6caa791f47e6081f4f51 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_85df74ba69548a9fe4f0630bc4ef809c, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_bb657bc06fc206007efa4b94cfff12b6 = private unnamed_addr constant [26 x i8] c"Complex operation result: ", align 1
@alloc_735da50cf165c4f36b10049b8526448f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bb657bc06fc206007efa4b94cfff12b6, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_330e193dc4806a2c5501c939ce6ef863 = private unnamed_addr constant [26 x i8] c"Complex operation failed: ", align 1
@alloc_b81dd633989cc95e6d5bb487bf79e870 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_330e193dc4806a2c5501c939ce6ef863, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_12f7e0203562251009b9333a168f81b6 = private unnamed_addr constant [38 x i8] c"=== End of Advanced Features Demo ===\0A", align 1
@alloc_2d9af10799b8ba4a64ac4d7e087abf63 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_12f7e0203562251009b9333a168f81b6, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_180b8264ca1098899d9c0e9af1dc90ff = private unnamed_addr constant [11 x i8] c"Processing ", align 1
@alloc_eaa2982728a46158796376bb2e9f6120 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_180b8264ca1098899d9c0e9af1dc90ff, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f782695604732cc93d77827976f2199e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\8E\00\00\004\00\00\00" }>, align 8
@alloc_034f5314d1276bc8624035dc7f2ccce9 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\91\00\00\007\00\00\00" }>, align 8
@alloc_24107041b0d41e40346c98860437e39e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\9B\00\00\00\16\00\00\00" }>, align 8
@alloc_eb7215b3588ada683685d858ba86c058 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\9C\00\00\00\12\00\00\00" }>, align 8
@alloc_36712b18fa80a356639965f9856729fb = private unnamed_addr constant [16 x i8] c"Division by zero", align 1
@alloc_6a83e622db22f0eec691a5ea76047efd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\BD\00\00\00\0C\00\00\00" }>, align 8
@alloc_44544d476d9ea0361b5f81ebb0ccf154 = private unnamed_addr constant [14 x i8] c"Text message: ", align 1
@alloc_67e443d542bcaab3dd76a71807774222 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_44544d476d9ea0361b5f81ebb0ccf154, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1815610b30091eb60a97d5d3cc0b8575 = private unnamed_addr constant [16 x i8] c"Number message: ", align 1
@alloc_fa86de3cf826d05860c2cdc447184c8f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1815610b30091eb60a97d5d3cc0b8575, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_20ab9c9559d8c122c92a94053fd87efd = private unnamed_addr constant [22 x i8] c"Quit message received\0A", align 1
@alloc_401f6fe8583d145640de68804cff6fdf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_20ab9c9559d8c122c92a94053fd87efd, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_9c9ec0e0f9012040139f57cd1c72b478 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\D7\00\00\00\08\00\00\00" }>, align 8
@alloc_46a16dc9ead633cefec9cfa661f86e5c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\D6\00\00\00\11\00\00\00" }>, align 8
@alloc_c08516871150497b81c8b396ff7efdd2 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3a3539c51f57fffd3c34fe4eaefb5f52, [16 x i8] c"\17\00\00\00\00\00\00\00\D5\00\00\00\11\00\00\00" }>, align 8

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: uwtable
define internal void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17he4648028d51ed991E"(ptr %self.0, ptr %self.1, ptr align 8 %g) unnamed_addr #0 {
start:
  %_5 = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_5, ptr align 8 %g, i64 24, i1 false)
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h595cbf4f8f6b7260E"(ptr %self.0, ptr %self.1, ptr align 8 %_5)
  ret void
}

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hdd5de5f45176c092E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %self1 = alloca [8 x i8], align 4
  %_0 = alloca [8 x i8], align 4
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
  %0 = call { i32, i32 } @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h83b4e650895975a2E"(ptr align 4 %self)
  %1 = extractvalue { i32, i32 } %0, 0
  %2 = extractvalue { i32, i32 } %0, 1
  store i32 %1, ptr %self1, align 4
  %3 = getelementptr inbounds i8, ptr %self1, i64 4
  store i32 %2, ptr %3, align 4
  %f = getelementptr inbounds i8, ptr %self, i64 12
  %4 = load i32, ptr %self1, align 4
  %5 = getelementptr inbounds i8, ptr %self1, i64 4
  %6 = load i32, ptr %5, align 4
  %_5 = zext i32 %4 to i64
  %7 = trunc nuw i64 %_5 to i1
  br i1 %7, label %bb5, label %bb4

bb5:                                              ; preds = %start
  %8 = getelementptr inbounds i8, ptr %self1, i64 4
  %x = load i32, ptr %8, align 4
; call _11_advanced_features::main::{{closure}}
  %_7 = call i32 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17h1c686b1e6fa9d427E"(ptr align 1 %f, i32 %x)
  %9 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_7, ptr %9, align 4
  store i32 1, ptr %_0, align 4
  br label %bb2

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb2

bb2:                                              ; preds = %bb5, %bb4
  %10 = load i32, ptr %_0, align 4
  %11 = getelementptr inbounds i8, ptr %_0, i64 4
  %12 = load i32, ptr %11, align 4
  %13 = insertvalue { i32, i32 } poison, i32 %10, 0
  %14 = insertvalue { i32, i32 } %13, i32 %12, 1
  ret { i32, i32 } %14

bb3:                                              ; No predecessors!
  unreachable
}

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h6c04afc52775f8a1E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h64baff096ef7aeb4E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self)
  ret void
}

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h8056a2e52437031aE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h607056622e1a8a4eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self)
  ret void
}

; <alloc::vec::into_iter::IntoIter<T,A> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal void @"_ZN103_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h6c378266e72a163eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_18 = alloca [24 x i8], align 8
  %_13 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  br label %bb4

bb4:                                              ; preds = %start
  %self1 = getelementptr inbounds i8, ptr %self, i64 8
  %_15 = getelementptr inbounds i8, ptr %self, i64 24
  %0 = load ptr, ptr %_15, align 8
  store ptr %0, ptr %_13, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %_23 = load ptr, ptr %1, align 8
  %_24 = load ptr, ptr %_13, align 8
  %_10 = icmp eq ptr %_23, %_24
  br i1 %_10, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  %old = load ptr, ptr %2, align 8
  %_25 = getelementptr inbounds nuw %Message, ptr %old, i64 1
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %_25, ptr %3, align 8
  store ptr %old, ptr %ptr, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store i64 -9223372036854775806, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %self2 = load ptr, ptr %ptr, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_18, ptr align 8 %self2, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_18, i64 24, i1 false)
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  ret void

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable
}

; <alloc::vec::into_iter::IntoIter<T,A> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN103_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17ha5e594e2876b133cE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [16 x i8], align 8
  %exact = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_10 = getelementptr inbounds i8, ptr %self, i64 24
  %_8 = load ptr, ptr %_10, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_11 = load ptr, ptr %0, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %1 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17hcd649773658a6bc3E"(ptr %_8, ptr %_11)
  store i64 %1, ptr %exact, align 8
  br label %bb4

bb4:                                              ; preds = %bb2
  %_12 = load i64, ptr %exact, align 8
  %_14 = load i64, ptr %exact, align 8
  %2 = getelementptr inbounds i8, ptr %_13, i64 8
  store i64 %_14, ptr %2, align 8
  store i64 1, ptr %_13, align 8
  store i64 %_12, ptr %_0, align 8
  %3 = load i64, ptr %_13, align 8
  %4 = getelementptr inbounds i8, ptr %_13, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 %5, ptr %7, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfa865311f4011dadE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 4
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 4 ptr @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha6541428188d913dE"(ptr align 8 %self)
  store ptr %0, ptr %self1, align 8
  %1 = load ptr, ptr %self1, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_4 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_4 to i1
  br i1 %4, label %bb5, label %bb4

bb5:                                              ; preds = %start
  %t = load ptr, ptr %self1, align 8
  %_0.i = load i32, ptr %t, align 4
  %5 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_0.i, ptr %5, align 4
  store i32 1, ptr %_0, align 4
  br label %bb2

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb2

bb2:                                              ; preds = %bb5, %bb4
  %6 = load i32, ptr %_0, align 4
  %7 = getelementptr inbounds i8, ptr %_0, i64 4
  %8 = load i32, ptr %7, align 4
  %9 = insertvalue { i32, i32 } poison, i32 %6, 0
  %10 = insertvalue { i32, i32 } %9, i32 %8, 1
  ret { i32, i32 } %10

bb3:                                              ; No predecessors!
  unreachable
}

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: uwtable
define internal void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17hc0b60653b92d3e32E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h380477fb729ad0a9E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self)
  ret void
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h206e23fd8e9e58e9E"(ptr align 4 %self, ptr align 1 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_36 = alloca [1 x i8], align 1
  %_35 = alloca [1 x i8], align 1
  %_25 = alloca [8 x i8], align 4
  %_14 = alloca [8 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %f = alloca [8 x i8], align 8
  store ptr %0, ptr %f, align 8
  store i8 1, ptr %_36, align 1
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %2 = load i8, ptr %1, align 4
  %_37 = trunc nuw i8 %2 to i1
  br i1 %_37, label %bb34, label %bb35

bb35:                                             ; preds = %start
  %_40 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_40, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  br label %bb36

bb34:                                             ; preds = %start
  br label %bb1

bb28:                                             ; preds = %funclet_bb28
  cleanupret from %cleanuppad unwind label %funclet_bb33

funclet_bb28:                                     ; preds = %bb1, %bb30, %bb31_cleanup_trampoline_bb28
  %cleanuppad = cleanuppad within none []
  br label %bb28

bb36:                                             ; preds = %bb35
  %_4 = xor i1 %_0.i, true
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb36
  store i8 0, ptr %_36, align 1
  store i8 1, ptr %_35, align 1
  br label %bb4

bb1:                                              ; preds = %bb34, %bb36
  store i8 0, ptr %_36, align 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
  %3 = invoke { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h10298756ec5c99ffE"()
          to label %bb2 unwind label %funclet_bb28

bb4:                                              ; preds = %bb11, %bb3
  %_9 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i5 = load i32, ptr %self, align 4
  %_4.i6 = load i32, ptr %_9, align 4
  %_0.i7 = icmp slt i32 %_3.i5, %_4.i6
  br label %bb5

bb31:                                             ; preds = %funclet_bb31
  %4 = load i8, ptr %_35, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb30, label %bb31_cleanup_trampoline_bb28

funclet_bb31:                                     ; preds = %bb12, %bb8, %bb7, %bb6, %bb20, %bb23, %bb17, %bb16, %bb14
  %cleanuppad1 = cleanuppad within none []
  br label %bb31

bb5:                                              ; preds = %bb4
  br i1 %_0.i7, label %bb6, label %bb14

bb14:                                             ; preds = %bb5
  %6 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %6, align 4
  %_24 = getelementptr inbounds i8, ptr %self, i64 4
; invoke core::cmp::impls::<impl core::cmp::PartialEq for i32>::eq
  %_22 = invoke zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$i32$GT$2eq17h86b1fd03d6780820E"(ptr align 4 %self, ptr align 4 %_24)
          to label %bb15 unwind label %funclet_bb31

bb6:                                              ; preds = %bb5
  %_11 = load i32, ptr %self, align 4
; invoke <i32 as core::iter::range::Step>::forward_unchecked
  %n = invoke i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hf6128236dae74b86E"(i32 %_11, i64 1)
          to label %bb7 unwind label %funclet_bb31

bb15:                                             ; preds = %bb14
  br i1 %_22, label %bb16, label %bb22

bb22:                                             ; preds = %bb15
  br label %bb23

bb16:                                             ; preds = %bb15
  store i8 0, ptr %_35, align 1
  %_30 = load i32, ptr %self, align 4
; invoke core::iter::traits::iterator::Iterator::find::check::{{closure}}
  %7 = invoke { i32, i32 } @"_ZN4core4iter6traits8iterator8Iterator4find5check28_$u7b$$u7b$closure$u7d$$u7d$17hb36cd056840c0ce9E"(ptr align 8 %f, i32 %_30)
          to label %bb17 unwind label %funclet_bb31

bb23:                                             ; preds = %bb19, %bb22
  store i8 0, ptr %_35, align 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
  %8 = invoke { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h10298756ec5c99ffE"()
          to label %bb24 unwind label %funclet_bb31

bb17:                                             ; preds = %bb16
  %_26.0 = extractvalue { i32, i32 } %7, 0
  %_26.1 = extractvalue { i32, i32 } %7, 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
  %9 = invoke { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h4b7c30ce07491c4cE"(i32 %_26.0, i32 %_26.1)
          to label %bb18 unwind label %funclet_bb31

bb18:                                             ; preds = %bb17
  %10 = extractvalue { i32, i32 } %9, 0
  %11 = extractvalue { i32, i32 } %9, 1
  store i32 %10, ptr %_25, align 4
  %12 = getelementptr inbounds i8, ptr %_25, i64 4
  store i32 %11, ptr %12, align 4
  %13 = load i32, ptr %_25, align 4
  %14 = getelementptr inbounds i8, ptr %_25, i64 4
  %15 = load i32, ptr %14, align 4
  %_31 = zext i32 %13 to i64
  %16 = trunc nuw i64 %_31 to i1
  br i1 %16, label %bb20, label %bb19

bb20:                                             ; preds = %bb18
  %17 = getelementptr inbounds i8, ptr %_25, i64 4
  %residual = load i32, ptr %17, align 4
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
  %18 = invoke { i32, i32 } @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17he21139f560841129E"(i32 %residual)
          to label %bb21 unwind label %funclet_bb31

bb19:                                             ; preds = %bb18
  br label %bb23

bb24:                                             ; preds = %bb23
  %19 = extractvalue { i32, i32 } %8, 0
  %20 = extractvalue { i32, i32 } %8, 1
  store i32 %19, ptr %_0, align 4
  %21 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %20, ptr %21, align 4
  br label %bb27

bb27:                                             ; preds = %bb26, %bb24
  %22 = load i32, ptr %_0, align 4
  %23 = getelementptr inbounds i8, ptr %_0, i64 4
  %24 = load i32, ptr %23, align 4
  %25 = insertvalue { i32, i32 } poison, i32 %22, 0
  %26 = insertvalue { i32, i32 } %25, i32 %24, 1
  ret { i32, i32 } %26

bb21:                                             ; preds = %bb20
  %27 = extractvalue { i32, i32 } %18, 0
  %28 = extractvalue { i32, i32 } %18, 1
  store i32 %27, ptr %_0, align 4
  %29 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %28, ptr %29, align 4
  br label %bb25

bb25:                                             ; preds = %bb13, %bb21
  br label %bb26

bb7:                                              ; preds = %bb6
  %n2 = load i32, ptr %self, align 4
  store i32 %n, ptr %self, align 4
  store i8 0, ptr %_35, align 1
; invoke core::iter::traits::iterator::Iterator::find::check::{{closure}}
  %30 = invoke { i32, i32 } @"_ZN4core4iter6traits8iterator8Iterator4find5check28_$u7b$$u7b$closure$u7d$$u7d$17hb36cd056840c0ce9E"(ptr align 8 %f, i32 %n2)
          to label %bb8 unwind label %funclet_bb31

bb8:                                              ; preds = %bb7
  %_15.0 = extractvalue { i32, i32 } %30, 0
  %_15.1 = extractvalue { i32, i32 } %30, 1
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
  %31 = invoke { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h4b7c30ce07491c4cE"(i32 %_15.0, i32 %_15.1)
          to label %bb9 unwind label %funclet_bb31

bb9:                                              ; preds = %bb8
  %32 = extractvalue { i32, i32 } %31, 0
  %33 = extractvalue { i32, i32 } %31, 1
  store i32 %32, ptr %_14, align 4
  %34 = getelementptr inbounds i8, ptr %_14, i64 4
  store i32 %33, ptr %34, align 4
  %35 = load i32, ptr %_14, align 4
  %36 = getelementptr inbounds i8, ptr %_14, i64 4
  %37 = load i32, ptr %36, align 4
  %_19 = zext i32 %35 to i64
  %38 = trunc nuw i64 %_19 to i1
  br i1 %38, label %bb12, label %bb11

bb12:                                             ; preds = %bb9
  %39 = getelementptr inbounds i8, ptr %_14, i64 4
  %residual3 = load i32, ptr %39, align 4
; invoke <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
  %40 = invoke { i32, i32 } @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17he21139f560841129E"(i32 %residual3)
          to label %bb13 unwind label %funclet_bb31

bb11:                                             ; preds = %bb9
  store i8 1, ptr %_35, align 1
  br label %bb4

bb13:                                             ; preds = %bb12
  %41 = extractvalue { i32, i32 } %40, 0
  %42 = extractvalue { i32, i32 } %40, 1
  store i32 %41, ptr %_0, align 4
  %43 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %42, ptr %43, align 4
  br label %bb25

bb26:                                             ; preds = %bb2, %bb25
  br label %bb27

bb10:                                             ; No predecessors!
  unreachable

bb31_cleanup_trampoline_bb28:                     ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb30:                                             ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb2:                                              ; preds = %bb1
  %44 = extractvalue { i32, i32 } %3, 0
  %45 = extractvalue { i32, i32 } %3, 1
  store i32 %44, ptr %_0, align 4
  %46 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %45, ptr %46, align 4
  br label %bb26

bb33:                                             ; preds = %funclet_bb33
  %47 = load i8, ptr %_36, align 1
  %48 = trunc nuw i8 %47 to i1
  br i1 %48, label %bb32, label %bb29

funclet_bb33:                                     ; preds = %bb28
  %cleanuppad4 = cleanuppad within none []
  br label %bb33

bb29:                                             ; preds = %bb32, %bb33
  cleanupret from %cleanuppad4 unwind to caller

bb32:                                             ; preds = %bb33
  br label %bb29
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hbe213a4a86387e21E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hf6128236dae74b86E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h83b4e650895975a2E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_3 = getelementptr inbounds i8, ptr %self, i64 12
; call core::iter::traits::iterator::Iterator::find
  %0 = call { i32, i32 } @_ZN4core4iter6traits8iterator8Iterator4find17hdf48acb074497b2cE(ptr align 4 %self, ptr align 1 %_3)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha6541428188d913dE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_3 = getelementptr inbounds i8, ptr %self, i64 16
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::find
  %_0 = call align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4find17h23c4be9d08bb81b8E"(ptr align 8 %self, ptr align 1 %_3)
  ret ptr %_0
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h380477fb729ad0a9E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h607056622e1a8a4eE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %upper.0 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %upper.1 = load i64, ptr %1, align 8
  store i64 0, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %upper.0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  store i64 %upper.1, ptr %3, align 8
  ret void
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h64baff096ef7aeb4E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::size_hint
  call void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$9size_hint17hdeb45718d3dff952E"(ptr sret([24 x i8]) align 8 %_3, ptr align 4 %self)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %upper.0 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %upper.1 = load i64, ptr %1, align 8
  store i64 0, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %upper.0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  store i64 %upper.1, ptr %3, align 8
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h392b8b5bc9e837f7E"(ptr sret([24 x i8]) align 8 %_0, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_12 = alloca [1 x i8], align 1
  %_8 = alloca [48 x i8], align 8
  %_3 = alloca [24 x i8], align 8
  %vector = alloca [24 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %3 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %3, align 8
  store i8 1, ptr %_12, align 1
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h8056a2e52437031aE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %iterator)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %4 = load i8, ptr %_12, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb6

funclet_bb8:                                      ; preds = %bb2, %bb5, %bb3, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  %6 = getelementptr inbounds i8, ptr %_3, i64 8
  %_5 = load i64, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = trunc nuw i64 %_5 to i1
  br i1 %9, label %bb3, label %bb2

bb3:                                              ; preds = %bb1
  %10 = getelementptr inbounds i8, ptr %_3, i64 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  %upper = load i64, ptr %11, align 8
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %12 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h795bf619d1c069c5E"(i64 %upper, i64 4, i64 4, ptr align 8 %2)
          to label %bb9 unwind label %funclet_bb8

bb2:                                              ; preds = %bb1
  store ptr @alloc_11d257f5ed6cc7fc38feaa801053bac6, ptr %_8, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %13, align 8
  %14 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %15 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %16 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %14, ptr %16, align 8
  %17 = getelementptr inbounds i8, ptr %16, i64 8
  store i64 %15, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %18, i64 8
  store i64 0, ptr %19, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 %2) #19
          to label %unreachable unwind label %funclet_bb8

bb9:                                              ; preds = %bb3
  %_16.0 = extractvalue { i64, ptr } %12, 0
  %_16.1 = extractvalue { i64, ptr } %12, 1
  store i64 %_16.0, ptr %vector, align 8
  %20 = getelementptr inbounds i8, ptr %vector, i64 8
  store ptr %_16.1, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %vector, i64 16
  store i64 0, ptr %21, align 8
  store i8 0, ptr %_12, align 1
  %_11.0 = load ptr, ptr %iterator, align 8
  %22 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_11.1 = load ptr, ptr %22, align 8
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h205ec1c42a5859b4E"(ptr align 8 %vector, ptr %_11.0, ptr %_11.1, ptr align 8 %2)
          to label %bb4 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %vector) #20 [ "funclet"(token %cleanuppad1) ]
  cleanupret from %cleanuppad1 unwind label %funclet_bb8

funclet_bb5:                                      ; preds = %bb9
  %cleanuppad1 = cleanuppad within none []
  br label %bb5

bb4:                                              ; preds = %bb9
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  ret void

unreachable:                                      ; preds = %bb2
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb6:                                              ; preds = %bb7, %bb8
  cleanupret from %cleanuppad unwind to caller

bb7:                                              ; preds = %bb8
  br label %bb6
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h4317ce575662e512E"(ptr sret([24 x i8]) align 8 %_0, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_20 = alloca [1 x i8], align 1
  %vector1 = alloca [24 x i8], align 8
  %_8 = alloca [24 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  %vector = alloca [24 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  store i8 1, ptr %_20, align 1
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
  %5 = invoke { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfa865311f4011dadE"(ptr align 8 %iterator)
          to label %bb1 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %6 = load i8, ptr %_20, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb10, label %bb8

funclet_bb11:                                     ; preds = %bb9, %bb7, %start
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb1:                                              ; preds = %start
  %8 = extractvalue { i32, i32 } %5, 0
  %9 = extractvalue { i32, i32 } %5, 1
  store i32 %8, ptr %_3, align 4
  %10 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %9, ptr %10, align 4
  %11 = load i32, ptr %_3, align 4
  %12 = getelementptr inbounds i8, ptr %_3, i64 4
  %13 = load i32, ptr %12, align 4
  %_5 = zext i32 %11 to i64
  %14 = trunc nuw i64 %_5 to i1
  br i1 %14, label %bb3, label %bb12

bb3:                                              ; preds = %bb1
  %15 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %15, align 4
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17hc0b60653b92d3e32E"(ptr sret([24 x i8]) align 8 %_8, ptr align 8 %iterator)
          to label %bb4 unwind label %funclet_bb9

bb12:                                             ; preds = %bb1
  store i64 0, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr getelementptr (i8, ptr null, i64 4), ptr %16, align 8
  %17 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %17, align 8
  br label %bb6

bb6:                                              ; preds = %bb5, %bb12
  ret void

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad2 unwind label %funclet_bb11

funclet_bb9:                                      ; preds = %bb14, %bb4, %bb3
  %cleanuppad2 = cleanuppad within none []
  br label %bb9

bb4:                                              ; preds = %bb3
  %lower = load i64, ptr %_8, align 8
  %18 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %18, ptr %3, align 8
  %v2 = load i64, ptr %3, align 8
; invoke core::cmp::Ord::max
  %initial_capacity = invoke i64 @_ZN4core3cmp3Ord3max17ha9c7785965ec8b2fE(i64 4, i64 %v2)
          to label %bb14 unwind label %funclet_bb9

bb14:                                             ; preds = %bb4
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %19 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h795bf619d1c069c5E"(i64 %initial_capacity, i64 4, i64 4, ptr align 8 %2)
          to label %bb15 unwind label %funclet_bb9

bb15:                                             ; preds = %bb14
  %_26.0 = extractvalue { i64, ptr } %19, 0
  %_26.1 = extractvalue { i64, ptr } %19, 1
  store i64 %_26.0, ptr %vector1, align 8
  %20 = getelementptr inbounds i8, ptr %vector1, i64 8
  store ptr %_26.1, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 0, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %vector1, i64 8
  %_27 = load ptr, ptr %22, align 8
  store i32 %element, ptr %_27, align 4
  %23 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 1, ptr %23, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %vector, ptr align 8 %vector1, i64 24, i1 false)
  store i8 0, ptr %_20, align 1
  %_19.0 = load ptr, ptr %iterator, align 8
  %24 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_19.1 = load ptr, ptr %24, align 8
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h1402ddaad8a14803E"(ptr align 8 %vector, ptr %_19.0, ptr %_19.1, ptr align 8 %2)
          to label %bb5 unwind label %funclet_bb7

bb7:                                              ; preds = %funclet_bb7
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %vector) #20 [ "funclet"(token %cleanuppad3) ]
  cleanupret from %cleanuppad3 unwind label %funclet_bb11

funclet_bb7:                                      ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb7

bb5:                                              ; preds = %bb15
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  br label %bb6

bb2:                                              ; No predecessors!
  unreachable

bb8:                                              ; preds = %bb10, %bb11
  cleanupret from %cleanuppad unwind to caller

bb10:                                             ; preds = %bb11
  br label %bb8
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h99972a45ffe39b86E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %1 = alloca [8 x i8], align 8
  %_20 = alloca [1 x i8], align 1
  %_19 = alloca [12 x i8], align 4
  %vector1 = alloca [24 x i8], align 8
  %_8 = alloca [24 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  %vector = alloca [24 x i8], align 8
  store i8 1, ptr %_20, align 1
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke { i32, i32 } @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hdd5de5f45176c092E"(ptr align 4 %iterator)
          to label %bb1 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %3 = load i8, ptr %_20, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb10, label %bb8

funclet_bb11:                                     ; preds = %bb9, %bb7, %start
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb1:                                              ; preds = %start
  %5 = extractvalue { i32, i32 } %2, 0
  %6 = extractvalue { i32, i32 } %2, 1
  store i32 %5, ptr %_3, align 4
  %7 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %6, ptr %7, align 4
  %8 = load i32, ptr %_3, align 4
  %9 = getelementptr inbounds i8, ptr %_3, i64 4
  %10 = load i32, ptr %9, align 4
  %_5 = zext i32 %8 to i64
  %11 = trunc nuw i64 %_5 to i1
  br i1 %11, label %bb3, label %bb12

bb3:                                              ; preds = %bb1
  %12 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %12, align 4
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h6c04afc52775f8a1E"(ptr sret([24 x i8]) align 8 %_8, ptr align 4 %iterator)
          to label %bb4 unwind label %funclet_bb9

bb12:                                             ; preds = %bb1
  store i64 0, ptr %_0, align 8
  %13 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr getelementptr (i8, ptr null, i64 4), ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %14, align 8
  br label %bb6

bb6:                                              ; preds = %bb5, %bb12
  ret void

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad2 unwind label %funclet_bb11

funclet_bb9:                                      ; preds = %bb14, %bb4, %bb3
  %cleanuppad2 = cleanuppad within none []
  br label %bb9

bb4:                                              ; preds = %bb3
  %lower = load i64, ptr %_8, align 8
  %15 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %15, ptr %1, align 8
  %v2 = load i64, ptr %1, align 8
; invoke core::cmp::Ord::max
  %initial_capacity = invoke i64 @_ZN4core3cmp3Ord3max17ha9c7785965ec8b2fE(i64 4, i64 %v2)
          to label %bb14 unwind label %funclet_bb9

bb14:                                             ; preds = %bb4
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %16 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h795bf619d1c069c5E"(i64 %initial_capacity, i64 4, i64 4, ptr align 8 %0)
          to label %bb15 unwind label %funclet_bb9

bb15:                                             ; preds = %bb14
  %_26.0 = extractvalue { i64, ptr } %16, 0
  %_26.1 = extractvalue { i64, ptr } %16, 1
  store i64 %_26.0, ptr %vector1, align 8
  %17 = getelementptr inbounds i8, ptr %vector1, i64 8
  store ptr %_26.1, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 0, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %vector1, i64 8
  %_27 = load ptr, ptr %19, align 8
  store i32 %element, ptr %_27, align 4
  %20 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 1, ptr %20, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %vector, ptr align 8 %vector1, i64 24, i1 false)
  store i8 0, ptr %_20, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_19, ptr align 4 %iterator, i64 12, i1 false)
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h5f9ed1ab9b79cac5E"(ptr align 8 %vector, ptr align 4 %_19, ptr align 8 %0)
          to label %bb5 unwind label %funclet_bb7

bb7:                                              ; preds = %funclet_bb7
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %vector) #20 [ "funclet"(token %cleanuppad3) ]
  cleanupret from %cleanuppad3 unwind label %funclet_bb11

funclet_bb7:                                      ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb7

bb5:                                              ; preds = %bb15
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  br label %bb6

bb2:                                              ; No predecessors!
  unreachable

bb8:                                              ; preds = %bb10, %bb11
  cleanupret from %cleanuppad unwind to caller

bb10:                                             ; preds = %bb11
  br label %bb8
}

; <core::result::Result<T,F> as core::ops::try_trait::FromResidual<core::result::Result<core::convert::Infallible,E>>>::from_residual
; Function Attrs: inlinehint uwtable
define internal void @"_ZN153_$LT$core..result..Result$LT$T$C$F$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..result..Result$LT$core..convert..Infallible$C$E$GT$$GT$$GT$13from_residual17h2381c7da409addfeE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %residual, ptr align 8 %0) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
  %e = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %e, ptr align 8 %residual, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_3, ptr align 8 %e, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 24, i1 false)
  ret void
}

; <<alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop::DropGuard<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN157_$LT$$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h464624ec833c4038E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %capacity = alloca [8 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %_7 = load ptr, ptr %self, align 8
  %slot = getelementptr inbounds i8, ptr %_7, i64 32
  %_8 = load ptr, ptr %self, align 8
  %ptr = load ptr, ptr %_8, align 8
  %_9 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %_9, i64 16
  %capacity1 = load i64, ptr %0, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store i64 %capacity1, ptr %capacity, align 8
  br label %bb2

bb2:                                              ; preds = %bb4
  %cap = load i64, ptr %capacity, align 8
  store i64 %cap, ptr %_4, align 8
  %1 = getelementptr inbounds i8, ptr %_4, i64 8
  store ptr %ptr, ptr %1, align 8
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<11_advanced_features::Message>>
  call void @"_ZN4core3ptr80drop_in_place$LT$alloc..raw_vec..RawVec$LT$11_advanced_features..Message$GT$$GT$17hd917aad64a962fa5E"(ptr align 8 %_4)
  ret void

bb3:                                              ; No predecessors!
  unreachable
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::FromResidual<core::ops::control_flow::ControlFlow<B,core::convert::Infallible>>>::from_residual
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN185_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..control_flow..ControlFlow$LT$B$C$core..convert..Infallible$GT$$GT$$GT$13from_residual17he21139f560841129E"(i32 %0) unnamed_addr #1 {
start:
  %_0 = alloca [8 x i8], align 4
  %residual = alloca [4 x i8], align 4
  store i32 %0, ptr %residual, align 4
  %_2 = load i32, ptr %residual, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_2, ptr %1, align 4
  store i32 1, ptr %_0, align 4
  %2 = load i32, ptr %_0, align 4
  %3 = getelementptr inbounds i8, ptr %_0, i64 4
  %4 = load i32, ptr %3, align 4
  %5 = insertvalue { i32, i32 } poison, i32 %2, 0
  %6 = insertvalue { i32, i32 } %5, i32 %4, 1
  ret { i32, i32 } %6
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hefcc0fce842277cbE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hbb5719c4a6bf21d3E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hf2dc46b16cf2ed90E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h8cd3dee2f7dbd2a5E"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hf2dc46b16cf2ed90E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h287446bd12621d27E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h824649dbc05b35eaE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::num::<impl core::fmt::Debug for i32>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17he2cc10859c08b5baE"(ptr align 4 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h114aba072d3db243E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <T as alloc::string::ToString>::to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17haaeace487c21f475E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call <str as alloc::string::SpecToString>::spec_to_string
  call void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17h16b1ac99954902baE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1)
  ret void
}

; <i32 as core::iter::range::Step>::steps_between
; Function Attrs: inlinehint uwtable
define internal void @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$13steps_between17h29fc7428a582f60dE"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %start1, ptr align 4 %end) unnamed_addr #1 {
start:
  %_12 = alloca [16 x i8], align 8
  %_4 = load i32, ptr %start1, align 4
  %_5 = load i32, ptr %end, align 4
  %_3 = icmp sle i32 %_4, %_5
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  %0 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %1 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb1:                                              ; preds = %start
  %_9 = load i32, ptr %end, align 4
  %self = sext i32 %_9 to i64
  %_11 = load i32, ptr %start1, align 4
  %rhs = sext i32 %_11 to i64
  %_7 = sub i64 %self, %rhs
  %4 = getelementptr inbounds i8, ptr %_12, i64 8
  store i64 %_7, ptr %4, align 8
  store i64 1, ptr %_12, align 8
  store i64 %_7, ptr %_0, align 8
  %5 = load i64, ptr %_12, align 8
  %6 = getelementptr inbounds i8, ptr %_12, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hf6128236dae74b86E"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.1, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.1, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h7f0a8c3c7e89a03dE() #21
  unreachable
}

; <[T] as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h4401fc56aa1310e0E"(ptr align 4 %self.0, i64 %self.1, ptr align 8 %f) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %_5 = alloca [16 x i8], align 8
; call core::fmt::Formatter::debug_list
  call void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8 %_5, ptr align 8 %f)
  br label %bb5

bb5:                                              ; preds = %start
  %_11 = getelementptr inbounds nuw i32, ptr %self.0, i64 %self.1
  store ptr %_11, ptr %end_or_len, align 8
  br label %bb6

bb6:                                              ; preds = %bb5
  %_13 = load ptr, ptr %end_or_len, align 8
; call core::fmt::builders::DebugList::entries
  %_3 = call align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h25643b51a79496dfE(ptr align 8 %_5, ptr %self.0, ptr %_13)
; call core::fmt::builders::DebugList::finish
  %_0 = call zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8 %_3)
  ret i1 %_0

bb4:                                              ; No predecessors!
  unreachable
}

; core::intrinsics::write_bytes::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics11write_bytes18precondition_check17he1e985ad79f2dd77E(ptr %addr, i64 %align, i1 zeroext %zero_size) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %zero_size, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_dd79dfae92e8fdc23813c4c7a1b7cf72, i64 228) #22
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #23 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17ha90d2a614c03fc4fE(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h456c64a24cd07128E(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #23 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #22
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17hb1ca0ac256ca9ab8E() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::Ord::max
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core3cmp3Ord3max17ha9c7785965ec8b2fE(i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %other = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  store i64 %0, ptr %self, align 8
  store i64 %1, ptr %other, align 8
  store i8 1, ptr %_6, align 1
  %_3.i = load i64, ptr %other, align 8
  %_4.i = load i64, ptr %self, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load i64, ptr %other, align 8
  store i64 %2, ptr %_0, align 8
  %3 = load i8, ptr %_6, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %5 = load i64, ptr %self, align 8
  store i64 %5, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load i64, ptr %_0, align 8
  ret i64 %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::cmp::impls::<impl core::cmp::PartialEq for i32>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$i32$GT$2eq17h86b1fd03d6780820E"(ptr align 4 %self, ptr align 4 %other) unnamed_addr #1 {
start:
  %_3 = load i32, ptr %self, align 4
  %_4 = load i32, ptr %other, align 4
  %_0 = icmp eq i32 %_3, %_4
  ret i1 %_0
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h141786a163beeda1E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h114aba072d3db243E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h5a3f64c052421b06E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hdcee5a0774ccf80cE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hb5ea6702297b0929E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_debug
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_debug17he8a22f98dffab20dE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h670961bd12dd27daE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::num::<impl core::fmt::Debug for i32>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17he2cc10859c08b5baE"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = getelementptr inbounds i8, ptr %f, i64 16
  %_4 = load i32, ptr %0, align 8
  %_3 = and i32 %_4, 33554432
  %1 = icmp eq i32 %_3, 0
  br i1 %1, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %f, i64 16
  %_6 = load i32, ptr %2, align 8
  %_5 = and i32 %_6, 67108864
  %3 = icmp eq i32 %_5, 0
  br i1 %3, label %bb4, label %bb3

bb1:                                              ; preds = %start
; call core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
  %4 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4 %self, ptr align 8 %f)
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb6

bb4:                                              ; preds = %bb2
; call core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
  %6 = call zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4 %self, ptr align 8 %f)
  %7 = zext i1 %6 to i8
  store i8 %7, ptr %_0, align 1
  br label %bb5

bb3:                                              ; preds = %bb2
; call core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
  %8 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4 %self, ptr align 8 %f)
  %9 = zext i1 %8 to i8
  store i8 %9, ptr %_0, align 1
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  br label %bb6

bb6:                                              ; preds = %bb1, %bb5
  %10 = load i8, ptr %_0, align 1
  %11 = trunc nuw i8 %10 to i1
  ret i1 %11
}

; core::fmt::builders::DebugList::entries
; Function Attrs: uwtable
define internal align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h25643b51a79496dfE(ptr align 8 %self, ptr %entries.0, ptr %entries.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %entry = alloca [8 x i8], align 8
  %_5 = alloca [8 x i8], align 8
  %iter = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h79fc13414bd2acf0E"(ptr %entries.0, ptr %entries.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
  store ptr %_3.0, ptr %iter, align 8
  %1 = getelementptr inbounds i8, ptr %iter, i64 8
  store ptr %_3.1, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %bb8, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h826e87a45bdaf675E"(ptr align 8 %iter)
          to label %bb3 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb10, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb3:                                              ; preds = %bb2
  store ptr %2, ptr %_5, align 8
  %3 = load ptr, ptr %_5, align 8
  %4 = ptrtoint ptr %3 to i64
  %5 = icmp eq i64 %4, 0
  %_7 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_7 to i1
  br i1 %6, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %7 = load ptr, ptr %_5, align 8
  store ptr %7, ptr %entry, align 8
; invoke core::fmt::builders::DebugList::entry
  %_9 = invoke align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8 %self, ptr align 1 %entry, ptr align 8 @vtable.1)
          to label %bb7 unwind label %funclet_bb10

bb6:                                              ; preds = %bb3
  ret ptr %self

bb10:                                             ; preds = %funclet_bb10
  cleanupret from %cleanuppad1 unwind label %funclet_bb11

funclet_bb10:                                     ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb10

bb7:                                              ; preds = %bb5
  br label %bb8

bb8:                                              ; preds = %bb7
  br label %bb2

bb4:                                              ; No predecessors!
  unreachable
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h1970af73f20cca4fE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h8e235922c73bde2dE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h0ada306c96cc4e7eE"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #22
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17hd6b277d9c3494e09E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #22
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf35903eb3007664eE"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #1 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h5115e003ff50ca8aE"(ptr align 8 %self, ptr align 8 %0) unnamed_addr #0 {
start:
  %args = alloca [8 x i8], align 8
  store ptr %0, ptr %args, align 8
  %_3 = load ptr, ptr %self, align 8
  %1 = load ptr, ptr %args, align 8
; call _11_advanced_features::main::{{closure}}
  %_0 = call zeroext i1 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17h6acf53c0769bec0dE"(ptr align 1 %_3, ptr align 8 %1)
  ret i1 %_0
}

; core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h5abbe346f4ba7635E"(ptr align 8 %self, ptr align 4 %0) unnamed_addr #0 {
start:
  %args = alloca [8 x i8], align 8
  store ptr %0, ptr %args, align 8
  %_3 = load ptr, ptr %self, align 8
  %1 = load ptr, ptr %args, align 8
; call _11_advanced_features::main::{{closure}}
  %_0 = call zeroext i1 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17hd6b9d2007b6ddf31E"(ptr align 1 %_3, ptr align 4 %1)
  ret i1 %_0
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h4d642c06691b3a34E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h44f80746a16dcf1cE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h287446bd12621d27E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h44f80746a16dcf1cE(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hbb5719c4a6bf21d3E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17h3ff7951c6f3bcfdfE(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #22
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #23 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<<alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop::DropGuard<11_advanced_features::Message,alloc::alloc::Global>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr183drop_in_place$LT$$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$11_advanced_features..Message$C$alloc..alloc..Global$GT$$GT$17h250e8b09876d3975E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <<alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop::DropGuard<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN157_$LT$$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h464624ec833c4038E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr230drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h2bf932a1704f5276E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::set_len_on_drop::SetLenOnDrop>
  call void @"_ZN4core3ptr62drop_in_place$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$GT$17hfd384507058f5e0dE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<&i32>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr28drop_in_place$LT$$RF$i32$GT$17h9fc0518f9d986ed1E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::drop_in_place<core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr327drop_in_place$LT$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h4dcd6ca46d6fd231E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>
  call void @"_ZN4core3ptr230drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h2bf932a1704f5276E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hda3991001d6c0dc8E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hda3991001d6c0dc8E"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h54583b80c46da701E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17hd5debed03f73209dE"(ptr align 8 %_1) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17hd5debed03f73209dE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9c97cdaf93931b37E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h8a7b0f3d28145570E"(ptr align 8 %_1) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h8a7b0f3d28145570E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::iter::adapters::map::map_fold<&i32,i32,(),11_advanced_features::main::{{closure}},core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr484drop_in_place$LT$core..iter..adapters..map..map_fold$LT$$RF$i32$C$i32$C$$LP$$RP$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hf272d9ea9eac737fE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>
  call void @"_ZN4core3ptr327drop_in_place$LT$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h4dcd6ca46d6fd231E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<11_advanced_features::Message>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr50drop_in_place$LT$11_advanced_features..Message$GT$17hda6685f014186321E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  %0 = load i64, ptr %_1, align 8
  %1 = sub i64 %0, -9223372036854775808
  %2 = icmp ule i64 %1, 1
  %3 = add i64 %1, 1
  %_2 = select i1 %2, i64 %3, i64 0
  %4 = icmp eq i64 %_2, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %_1)
  br label %bb1

bb1:                                              ; preds = %bb2, %start
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17hd5debed03f73209dE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hf8df6b229410e578E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h8a7b0f3d28145570E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17haa320c0e82b132e3E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<[11_advanced_features::Message]>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr60drop_in_place$LT$$u5b$11_advanced_features..Message$u5d$$GT$17h08e32bc0eaf2ea81E"(ptr align 8 %_1.0, i64 %_1.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_3 = alloca [8 x i8], align 8
  store i64 0, ptr %_3, align 8
  br label %bb6

bb6:                                              ; preds = %bb5, %start
  %0 = load i64, ptr %_3, align 8
  %_7 = icmp eq i64 %0, %_1.1
  br i1 %_7, label %bb1, label %bb5

bb5:                                              ; preds = %bb6
  %1 = load i64, ptr %_3, align 8
  %_6 = getelementptr inbounds nuw %Message, ptr %_1.0, i64 %1
  %2 = load i64, ptr %_3, align 8
  %3 = add i64 %2, 1
  store i64 %3, ptr %_3, align 8
; invoke core::ptr::drop_in_place<11_advanced_features::Message>
  invoke void @"_ZN4core3ptr50drop_in_place$LT$11_advanced_features..Message$GT$17hda6685f014186321E"(ptr align 8 %_6)
          to label %bb6 unwind label %funclet_bb4

bb1:                                              ; preds = %bb6
  ret void

bb4:                                              ; preds = %bb3, %funclet_bb4
  %4 = load i64, ptr %_3, align 8
  %_5 = icmp eq i64 %4, %_1.1
  br i1 %_5, label %bb2, label %bb3

funclet_bb4:                                      ; preds = %bb5
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb3:                                              ; preds = %bb4
  %5 = load i64, ptr %_3, align 8
  %_4 = getelementptr inbounds nuw %Message, ptr %_1.0, i64 %5
  %6 = load i64, ptr %_3, align 8
  %7 = add i64 %6, 1
  store i64 %7, ptr %_3, align 8
; call core::ptr::drop_in_place<11_advanced_features::Message>
  call void @"_ZN4core3ptr50drop_in_place$LT$11_advanced_features..Message$GT$17hda6685f014186321E"(ptr align 8 %_4) #20 [ "funclet"(token %cleanuppad) ]
  br label %bb4

bb2:                                              ; preds = %bb4
  cleanupret from %cleanuppad unwind to caller
}

; core::ptr::drop_in_place<alloc::vec::set_len_on_drop::SetLenOnDrop>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr62drop_in_place$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$GT$17hfd384507058f5e0dE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::vec::set_len_on_drop::SetLenOnDrop as core::ops::drop::Drop>::drop
  call void @"_ZN83_$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$u20$as$u20$core..ops..drop..Drop$GT$4drop17hbff6fc332a2b6947E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<11_advanced_features::Message>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr80drop_in_place$LT$alloc..raw_vec..RawVec$LT$11_advanced_features..Message$GT$$GT$17hd917aad64a962fa5E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hcdb01769256b3126E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h580464dcb215451eE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::drop_in_place<alloc::vec::into_iter::IntoIter<11_advanced_features::Message>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr89drop_in_place$LT$alloc..vec..into_iter..IntoIter$LT$11_advanced_features..Message$GT$$GT$17haba010042370dca3E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN86_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17he785bd7b1466bbacE"(ptr align 8 %_1)
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %ptr) unnamed_addr #3 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #22
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17habfe6d1f0d527829E"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h4c2d47beab7d5ae1E"(ptr %self, ptr %subtracted) #21
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 4
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #19
  unreachable
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17hcd649773658a6bc3E"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h4c2d47beab7d5ae1E"(ptr %self, ptr %subtracted) #21
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 24
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #19
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h4c2d47beab7d5ae1E"(ptr %this, ptr %origin) unnamed_addr #3 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #22
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) unnamed_addr #3 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #22
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17h7f0a8c3c7e89a03dE() unnamed_addr #3 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #22
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h929783076e4a423aE"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hbe213a4a86387e21E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::try_fold
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$8try_fold17h53475b5fede7c654E"(ptr align 4 %self, ptr align 1 %f) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h206e23fd8e9e58e9E"(ptr align 4 %self, ptr align 1 %f)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$9size_hint17hdeb45718d3dff952E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %hint = alloca [24 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %2 = load i8, ptr %1, align 4
  %_10 = trunc nuw i8 %2 to i1
  br i1 %_10, label %bb5, label %bb6

bb6:                                              ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb5:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb6
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
; call <i32 as core::iter::range::Step>::steps_between
  call void @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$13steps_between17h29fc7428a582f60dE"(ptr sret([24 x i8]) align 8 %hint, ptr align 4 %self, ptr align 4 %_5)
  %self2 = load i64, ptr %hint, align 8
  %3 = call i64 @llvm.uadd.sat.i64(i64 %self2, i64 1)
  store i64 %3, ptr %0, align 8
  %_6 = load i64, ptr %0, align 8
  %4 = getelementptr inbounds i8, ptr %hint, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %self1, align 8
  %8 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %7, ptr %8, align 8
  %_14 = load i64, ptr %self1, align 8
  %9 = getelementptr inbounds i8, ptr %self1, i64 8
  %10 = load i64, ptr %9, align 8
  %11 = trunc nuw i64 %_14 to i1
  br i1 %11, label %bb12, label %bb11

bb1:                                              ; preds = %bb5, %bb6
  store i64 0, ptr %_0, align 8
  %12 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.2, align 8
  %13 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.2, i64 8), align 8
  %14 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %12, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  store i64 %13, ptr %15, align 8
  br label %bb4

bb12:                                             ; preds = %bb2
  %16 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %16, align 8
  %17 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %x, i64 1)
  %_17.0 = extractvalue { i64, i1 } %17, 0
  %_17.1 = extractvalue { i64, i1 } %17, 1
  br i1 %_17.1, label %bb14, label %bb16

bb11:                                             ; preds = %bb2
  %18 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %19 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %18, ptr %_8, align 8
  %20 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %19, ptr %20, align 8
  br label %bb9

bb9:                                              ; preds = %bb13, %bb11
  store i64 %_6, ptr %_0, align 8
  %21 = load i64, ptr %_8, align 8
  %22 = getelementptr inbounds i8, ptr %_8, i64 8
  %23 = load i64, ptr %22, align 8
  %24 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %24, align 8
  %25 = getelementptr inbounds i8, ptr %24, i64 8
  store i64 %23, ptr %25, align 8
  br label %bb4

bb16:                                             ; preds = %bb12
  %_18 = add nuw i64 %x, 1
  %26 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %_18, ptr %26, align 8
  store i64 1, ptr %_8, align 8
  br label %bb13

bb14:                                             ; preds = %bb12
  %27 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %28 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %27, ptr %_8, align 8
  %29 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %28, ptr %29, align 8
  br label %bb13

bb13:                                             ; preds = %bb14, %bb16
  br label %bb9

bb4:                                              ; preds = %bb1, %bb9
  ret void

bb10:                                             ; No predecessors!
  unreachable
}

; core::iter::traits::exact_size::ExactSizeIterator::len
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core4iter6traits10exact_size17ExactSizeIterator3len17h85f117f1265b4418E(ptr align 8 %self) unnamed_addr #1 {
start:
  %_9 = alloca [48 x i8], align 8
  %_7 = alloca [1 x i8], align 1
  %_6 = alloca [16 x i8], align 8
  %_3 = alloca [24 x i8], align 8
  %upper = alloca [16 x i8], align 8
; call <alloc::vec::into_iter::IntoIter<T,A> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN103_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17ha5e594e2876b133cE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self)
  %lower = load i64, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = getelementptr inbounds i8, ptr %0, i64 8
  %3 = load i64, ptr %2, align 8
  store i64 %1, ptr %upper, align 8
  %4 = getelementptr inbounds i8, ptr %upper, i64 8
  store i64 %3, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %lower, ptr %5, align 8
  store i64 1, ptr %_6, align 8
  %_12 = load i64, ptr %upper, align 8
  %6 = getelementptr inbounds i8, ptr %upper, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = trunc nuw i64 %_12 to i1
  br i1 %8, label %bb6, label %bb7

bb6:                                              ; preds = %start
  %_10 = load i64, ptr %_6, align 8
  %9 = getelementptr inbounds i8, ptr %_6, i64 8
  %10 = load i64, ptr %9, align 8
  %11 = trunc nuw i64 %_10 to i1
  br i1 %11, label %bb9, label %bb8

bb7:                                              ; preds = %start
  %_11 = load i64, ptr %_6, align 8
  %12 = getelementptr inbounds i8, ptr %_6, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = icmp eq i64 %_11, 0
  %15 = zext i1 %14 to i8
  store i8 %15, ptr %_7, align 1
  br label %bb4

bb4:                                              ; preds = %bb9, %bb7
  %16 = load i8, ptr %_7, align 1
  %17 = trunc nuw i8 %16 to i1
  br i1 %17, label %bb2, label %bb3

bb9:                                              ; preds = %bb6
  %l = getelementptr inbounds i8, ptr %upper, i64 8
  %r = getelementptr inbounds i8, ptr %_6, i64 8
  %18 = getelementptr inbounds i8, ptr %upper, i64 8
  %_15 = load i64, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %_6, i64 8
  %_16 = load i64, ptr %19, align 8
  %20 = icmp eq i64 %_15, %_16
  %21 = zext i1 %20 to i8
  store i8 %21, ptr %_7, align 1
  br label %bb4

bb8:                                              ; preds = %bb6
  br label %bb3

bb3:                                              ; preds = %bb4, %bb8
  store ptr null, ptr %_9, align 8
; call core::panicking::assert_failed
  call void @_ZN4core9panicking13assert_failed17h364f5d38302fe36bE(i8 0, ptr align 8 %upper, ptr align 8 %_6, ptr align 8 %_9, ptr align 8 @alloc_b9236782e79d68e69a864d7209dc159e) #19
  unreachable

bb2:                                              ; preds = %bb4
  ret i64 %lower

bb5:                                              ; No predecessors!
  unreachable
}

; core::iter::traits::iterator::Iterator::map
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator3map17h2504ac3a1e07fb00E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::map
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator3map17h6ec6628ee06d548cE(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal i32 @_ZN4core4iter6traits8iterator8Iterator3sum17hdb3ab92ad2896615E(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
; call <i32 as core::iter::traits::accum::Sum<&i32>>::sum
  %_0 = call i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17hefda667590e8d22fE"(ptr %self.0, ptr %self.1)
  ret i32 %_0
}

; core::iter::traits::iterator::Iterator::find
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @_ZN4core4iter6traits8iterator8Iterator4find17hdf48acb074497b2cE(ptr align 4 %self, ptr align 1 %predicate) unnamed_addr #1 {
start:
  %self1 = alloca [8 x i8], align 4
  %_0 = alloca [8 x i8], align 4
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::try_fold
  %0 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$8try_fold17h53475b5fede7c654E"(ptr align 4 %self, ptr align 1 %predicate)
  %1 = extractvalue { i32, i32 } %0, 0
  %2 = extractvalue { i32, i32 } %0, 1
  store i32 %1, ptr %self1, align 4
  %3 = getelementptr inbounds i8, ptr %self1, i64 4
  store i32 %2, ptr %3, align 4
  %4 = load i32, ptr %self1, align 4
  %5 = getelementptr inbounds i8, ptr %self1, i64 4
  %6 = load i32, ptr %5, align 4
  %_5 = zext i32 %4 to i64
  %7 = trunc nuw i64 %_5 to i1
  br i1 %7, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %8 = getelementptr inbounds i8, ptr %self1, i64 4
  %x = load i32, ptr %8, align 4
  %9 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %x, ptr %9, align 4
  store i32 1, ptr %_0, align 4
  br label %bb7

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb7

bb7:                                              ; preds = %bb3, %bb4
  %10 = load i32, ptr %self1, align 4
  %11 = getelementptr inbounds i8, ptr %self1, i64 4
  %12 = load i32, ptr %11, align 4
  %_7 = zext i32 %10 to i64
  %13 = trunc nuw i64 %_7 to i1
  br i1 %13, label %bb5, label %bb6

bb5:                                              ; preds = %bb6, %bb7
  %14 = load i32, ptr %_0, align 4
  %15 = getelementptr inbounds i8, ptr %_0, i64 4
  %16 = load i32, ptr %15, align 4
  %17 = insertvalue { i32, i32 } poison, i32 %14, 0
  %18 = insertvalue { i32, i32 } %17, i32 %16, 1
  ret { i32, i32 } %18

bb6:                                              ; preds = %bb7
  br label %bb5

bb2:                                              ; No predecessors!
  unreachable
}

; core::iter::traits::iterator::Iterator::find::check::{{closure}}
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter6traits8iterator8Iterator4find5check28_$u7b$$u7b$closure$u7d$$u7d$17hb36cd056840c0ce9E"(ptr align 8 %_1, i32 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_0 = alloca [8 x i8], align 4
  %x = alloca [4 x i8], align 4
  store i32 %0, ptr %x, align 4
; invoke core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
  %_4 = invoke zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h5abbe346f4ba7635E"(ptr align 8 %_1, ptr align 4 %x)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind to caller

funclet_bb5:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_4, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  store i32 0, ptr %_0, align 4
  br label %bb4

bb2:                                              ; preds = %bb1
  %_8 = load i32, ptr %x, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_8, ptr %1, align 4
  store i32 1, ptr %_0, align 4
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %2 = load i32, ptr %_0, align 4
  %3 = getelementptr inbounds i8, ptr %_0, i64 4
  %4 = load i32, ptr %3, align 4
  %5 = insertvalue { i32, i32 } poison, i32 %2, 0
  %6 = insertvalue { i32, i32 } %5, i32 %4, 1
  ret { i32, i32 } %6
}

; core::iter::traits::iterator::Iterator::cloned
; Function Attrs: uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17hb6256425c7c73f1eE(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::filter
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator6filter17h1fc51651d854f0b8E(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; core::iter::traits::iterator::Iterator::filter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6filter17h321997cfcd534336E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17h3bae0b90aaaec23eE(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h0cd22d5662f53dd4E"(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17h88faa208b2460167E(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [12 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_6, ptr align 4 %self, i64 12, i1 false)
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17hdc98608e3c1be987E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %_6, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17had483c023c37a8e7E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17hf180f6e7d88e2d46E"(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::for_each
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator8for_each17hcbe47be8ec47888bE(ptr %self.0, ptr %self.1, ptr align 8 %f) unnamed_addr #1 {
start:
  %_4 = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_4, ptr align 8 %f, i64 24, i1 false)
; call <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
  call void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17he4648028d51ed991E"(ptr %self.0, ptr %self.1, ptr align 8 %_4)
  ret void
}

; core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h57e2b2a16f234362E"(ptr align 8 %_1, i32 %item) unnamed_addr #1 {
start:
; call alloc::vec::Vec<T,A>::extend_trusted::{{closure}}
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted28_$u7b$$u7b$closure$u7d$$u7d$17hd584654d53cd6a4eE"(ptr align 8 %_1, i32 %item)
  ret void
}

; core::iter::adapters::map::map_fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h675e1245fd7a490eE"(ptr align 8 %_1, ptr align 4 %elt) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  store i8 1, ptr %_10, align 1
  %_8 = getelementptr inbounds i8, ptr %_1, i64 24
; invoke _11_advanced_features::main::{{closure}}
  %_7 = invoke i32 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17hcb1382b96b46ef60E"(ptr align 1 %_8, ptr align 4 %elt)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  %0 = load i8, ptr %_10, align 1
  %1 = trunc nuw i8 %0 to i1
  br i1 %1, label %bb4, label %bb3

funclet_bb5:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
  invoke void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h57e2b2a16f234362E"(ptr align 8 %_1, i32 %_7)
          to label %bb2 unwind label %funclet_bb5

bb2:                                              ; preds = %bb1
  ret void

bb3:                                              ; preds = %bb4, %bb5
  cleanupret from %cleanuppad unwind to caller

bb4:                                              ; preds = %bb5
  br label %bb3
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h45e5b1be74942604E(ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hd88a9ce2014e706cE(i64 %size, i64 %align) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #23 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #22
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h90036ea58cd2d6f8E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hd88a9ce2014e706cE(i64 %new_size, i64 %_23) #21
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h45e5b1be74942604E(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9daa58a545a75da3E"(ptr align 4 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h7baec1e1e6e628e9E"(ptr align 4 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h48ce9fcb76390137E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #19
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #23 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #22
  unreachable
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h7baec1e1e6e628e9E"(ptr align 4 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h456c64a24cd07128E(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #1 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #22
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <str as alloc::string::SpecToString>::spec_to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17h16b1ac99954902baE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h3f1adc59298bbf73E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h8cd3dee2f7dbd2a5E"() unnamed_addr #1 {
start:
  ret i32 0
}

; alloc::vec::Vec<T,A>::extend_trusted
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted17hbb2edb81c5d48944E"(ptr align 8 %self, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_22 = alloca [1 x i8], align 1
  %_21 = alloca [48 x i8], align 8
  %_19 = alloca [24 x i8], align 8
  %_5 = alloca [24 x i8], align 8
  %high = alloca [16 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %3 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %3, align 8
  store i8 1, ptr %_22, align 1
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h8056a2e52437031aE"(ptr sret([24 x i8]) align 8 %_5, ptr align 8 %iterator)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %4 = load i8, ptr %_22, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb6

funclet_bb8:                                      ; preds = %bb5, %bb3, %bb2, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  %low = load i64, ptr %_5, align 8
  %6 = getelementptr inbounds i8, ptr %_5, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = getelementptr inbounds i8, ptr %6, i64 8
  %9 = load i64, ptr %8, align 8
  store i64 %7, ptr %high, align 8
  %10 = getelementptr inbounds i8, ptr %high, i64 8
  store i64 %9, ptr %10, align 8
  %_7 = load i64, ptr %high, align 8
  %11 = getelementptr inbounds i8, ptr %high, i64 8
  %12 = load i64, ptr %11, align 8
  %13 = trunc nuw i64 %_7 to i1
  br i1 %13, label %bb2, label %bb5

bb2:                                              ; preds = %bb1
  %14 = getelementptr inbounds i8, ptr %high, i64 8
  %additional = load i64, ptr %14, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h52547f034985a414E"(ptr align 8 %self, i64 %additional, ptr align 8 %2)
          to label %bb3 unwind label %funclet_bb8

bb5:                                              ; preds = %bb1
  store ptr @alloc_11d257f5ed6cc7fc38feaa801053bac6, ptr %_21, align 8
  %15 = getelementptr inbounds i8, ptr %_21, i64 8
  store i64 1, ptr %15, align 8
  %16 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %17 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %18 = getelementptr inbounds i8, ptr %_21, i64 32
  store ptr %16, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %18, i64 8
  store i64 %17, ptr %19, align 8
  %20 = getelementptr inbounds i8, ptr %_21, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %20, i64 8
  store i64 0, ptr %21, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_21, ptr align 8 %2) #19
          to label %unreachable unwind label %funclet_bb8

bb3:                                              ; preds = %bb2
  %22 = getelementptr inbounds i8, ptr %self, i64 8
  %_23 = load ptr, ptr %22, align 8
  %len = getelementptr inbounds i8, ptr %self, i64 16
  %_24 = load i64, ptr %len, align 8
  store i8 0, ptr %_22, align 1
  %_18.0 = load ptr, ptr %iterator, align 8
  %23 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_18.1 = load ptr, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %_19, i64 16
  store ptr %_23, ptr %24, align 8
  store ptr %len, ptr %_19, align 8
  %25 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %_24, ptr %25, align 8
; invoke core::iter::traits::iterator::Iterator::for_each
  invoke void @_ZN4core4iter6traits8iterator8Iterator8for_each17hcbe47be8ec47888bE(ptr %_18.0, ptr %_18.1, ptr align 8 %_19)
          to label %bb4 unwind label %funclet_bb8

bb4:                                              ; preds = %bb3
  ret void

unreachable:                                      ; preds = %bb5
  unreachable

bb9:                                              ; No predecessors!
  unreachable

bb6:                                              ; preds = %bb7, %bb8
  cleanupret from %cleanuppad unwind to caller

bb7:                                              ; preds = %bb8
  br label %bb6
}

; alloc::vec::Vec<T,A>::extend_trusted::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted28_$u7b$$u7b$closure$u7d$$u7d$17hd584654d53cd6a4eE"(ptr align 8 %_1, i32 %element) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_1, i64 16
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_1, i64 8
  %_5 = load i64, ptr %1, align 8
  %_3 = getelementptr inbounds nuw i32, ptr %_4, i64 %_5
  store i32 %element, ptr %_3, align 4
  %2 = getelementptr inbounds i8, ptr %_1, i64 8
  %3 = getelementptr inbounds i8, ptr %_1, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = add i64 %4, 1
  store i64 %5, ptr %2, align 8
  ret void
}

; alloc::vec::Vec<T,A>::extend_desugared
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17h6902ac6009b99630E"(ptr align 8 %self, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_11 = alloca [24 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  br label %bb1

bb1:                                              ; preds = %bb8, %start
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
  %5 = invoke { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hfa865311f4011dadE"(ptr align 8 %iterator)
          to label %bb2 unwind label %funclet_bb12

bb12:                                             ; preds = %funclet_bb12
  cleanupret from %cleanuppad unwind to caller

funclet_bb12:                                     ; preds = %bb14, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb12

bb2:                                              ; preds = %bb1
  %6 = extractvalue { i32, i32 } %5, 0
  %7 = extractvalue { i32, i32 } %5, 1
  store i32 %6, ptr %_3, align 4
  %8 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %7, ptr %8, align 4
  %9 = load i32, ptr %_3, align 4
  %10 = getelementptr inbounds i8, ptr %_3, i64 4
  %11 = load i32, ptr %10, align 4
  %_5 = zext i32 %9 to i64
  %12 = trunc nuw i64 %_5 to i1
  br i1 %12, label %bb3, label %bb9

bb3:                                              ; preds = %bb2
  %13 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %13, align 4
  %14 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %14, align 8
  %_19 = icmp ule i64 %len, 2305843009213693951
  br label %bb17

bb9:                                              ; preds = %bb2
  br label %bb10

bb17:                                             ; preds = %bb3
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_9, align 8
  br label %bb15

bb16:                                             ; No predecessors!
  store i64 -1, ptr %_9, align 8
  br label %bb15

bb15:                                             ; preds = %bb17, %bb16
  %15 = load i64, ptr %_9, align 8
  %_8 = icmp eq i64 %len, %15
  br i1 %_8, label %bb4, label %bb7

bb7:                                              ; preds = %bb15
  br label %bb8

bb4:                                              ; preds = %bb15
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17hc0b60653b92d3e32E"(ptr sret([24 x i8]) align 8 %_11, ptr align 8 %iterator)
          to label %bb5 unwind label %funclet_bb14

bb8:                                              ; preds = %bb6, %bb7
  %16 = getelementptr inbounds i8, ptr %self, i64 8
  %_24 = load ptr, ptr %16, align 8
  %dst = getelementptr inbounds nuw i32, ptr %_24, i64 %len
  store i32 %element, ptr %dst, align 4
  %new_len = add i64 %len, 1
  %17 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %new_len, ptr %17, align 8
  br label %bb1

bb14:                                             ; preds = %funclet_bb14
  cleanupret from %cleanuppad2 unwind label %funclet_bb12

funclet_bb14:                                     ; preds = %bb5, %bb4
  %cleanuppad2 = cleanuppad within none []
  br label %bb14

bb5:                                              ; preds = %bb4
  %lower = load i64, ptr %_11, align 8
  %18 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %18, ptr %3, align 8
  %_14 = load i64, ptr %3, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h52547f034985a414E"(ptr align 8 %self, i64 %_14, ptr align 8 %2)
          to label %bb6 unwind label %funclet_bb14

bb6:                                              ; preds = %bb5
  br label %bb8

bb10:                                             ; preds = %bb9
  ret void

bb19:                                             ; No predecessors!
  unreachable
}

; alloc::vec::Vec<T,A>::extend_desugared
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17hb4502db373e80a83E"(ptr align 8 %self, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %1 = alloca [8 x i8], align 8
  %_11 = alloca [24 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  br label %bb1

bb1:                                              ; preds = %bb8, %start
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke { i32, i32 } @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hdd5de5f45176c092E"(ptr align 4 %iterator)
          to label %bb2 unwind label %funclet_bb12

bb12:                                             ; preds = %funclet_bb12
  cleanupret from %cleanuppad unwind to caller

funclet_bb12:                                     ; preds = %bb14, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb12

bb2:                                              ; preds = %bb1
  %3 = extractvalue { i32, i32 } %2, 0
  %4 = extractvalue { i32, i32 } %2, 1
  store i32 %3, ptr %_3, align 4
  %5 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %4, ptr %5, align 4
  %6 = load i32, ptr %_3, align 4
  %7 = getelementptr inbounds i8, ptr %_3, i64 4
  %8 = load i32, ptr %7, align 4
  %_5 = zext i32 %6 to i64
  %9 = trunc nuw i64 %_5 to i1
  br i1 %9, label %bb3, label %bb9

bb3:                                              ; preds = %bb2
  %10 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %10, align 4
  %11 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %11, align 8
  %_19 = icmp ule i64 %len, 2305843009213693951
  br label %bb17

bb9:                                              ; preds = %bb2
  br label %bb10

bb17:                                             ; preds = %bb3
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_9, align 8
  br label %bb15

bb16:                                             ; No predecessors!
  store i64 -1, ptr %_9, align 8
  br label %bb15

bb15:                                             ; preds = %bb17, %bb16
  %12 = load i64, ptr %_9, align 8
  %_8 = icmp eq i64 %len, %12
  br i1 %_8, label %bb4, label %bb7

bb7:                                              ; preds = %bb15
  br label %bb8

bb4:                                              ; preds = %bb15
; invoke <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h6c04afc52775f8a1E"(ptr sret([24 x i8]) align 8 %_11, ptr align 4 %iterator)
          to label %bb5 unwind label %funclet_bb14

bb8:                                              ; preds = %bb6, %bb7
  %13 = getelementptr inbounds i8, ptr %self, i64 8
  %_24 = load ptr, ptr %13, align 8
  %dst = getelementptr inbounds nuw i32, ptr %_24, i64 %len
  store i32 %element, ptr %dst, align 4
  %new_len = add i64 %len, 1
  %14 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %new_len, ptr %14, align 8
  br label %bb1

bb14:                                             ; preds = %funclet_bb14
  cleanupret from %cleanuppad2 unwind label %funclet_bb12

funclet_bb14:                                     ; preds = %bb5, %bb4
  %cleanuppad2 = cleanuppad within none []
  br label %bb14

bb5:                                              ; preds = %bb4
  %lower = load i64, ptr %_11, align 8
  %15 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %15, ptr %1, align 8
  %_14 = load i64, ptr %1, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h52547f034985a414E"(ptr align 8 %self, i64 %_14, ptr align 8 %0)
          to label %bb6 unwind label %funclet_bb14

bb6:                                              ; preds = %bb5
  br label %bb8

bb10:                                             ; preds = %bb9
  ret void

bb19:                                             ; No predecessors!
  unreachable
}

; alloc::vec::Vec<T,A>::pop
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3pop17he3b5092ba99e3d44E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_5 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %_2 = load i64, ptr %0, align 8
  %1 = icmp eq i64 %_2, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb3

bb2:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %self, i64 16
  %3 = getelementptr inbounds i8, ptr %self, i64 16
  %4 = load i64, ptr %3, align 8
  %5 = sub i64 %4, 1
  store i64 %5, ptr %2, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  %_4 = load i64, ptr %6, align 8
  br label %bb6

bb3:                                              ; preds = %bb8, %bb1
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11

bb6:                                              ; preds = %bb2
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_5, align 8
  br label %bb4

bb5:                                              ; No predecessors!
  store i64 -1, ptr %_5, align 8
  br label %bb4

bb4:                                              ; preds = %bb6, %bb5
  %12 = load i64, ptr %_5, align 8
  %cond = icmp ult i64 %_4, %12
  br label %bb7

bb7:                                              ; preds = %bb4
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) #21
  br label %bb8

bb8:                                              ; preds = %bb7
  %13 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load ptr, ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %self, i64 16
  %count = load i64, ptr %14, align 8
  %_17 = icmp ule i64 %count, 2305843009213693951
  %src = getelementptr inbounds nuw i32, ptr %_16, i64 %count
  %_6 = load i32, ptr %src, align 4
  %15 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_6, ptr %15, align 4
  store i32 1, ptr %_0, align 4
  br label %bb3
}

; alloc::vec::Vec<T,A>::reserve
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h52547f034985a414E"(ptr align 8 %self, i64 %additional, ptr align 8 %0) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  store i64 4, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 4, ptr %2, align 8
  br label %bb6

bb6:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  store i64 %self2, ptr %self1, align 8
  br label %bb4

bb5:                                              ; No predecessors!
  store i64 -1, ptr %self1, align 8
  br label %bb4

bb4:                                              ; preds = %bb6, %bb5
  %3 = load i64, ptr %self1, align 8
  %_10 = sub i64 %3, %len
  %_7 = icmp ugt i64 %additional, %_10
  br i1 %_7, label %bb1, label %bb2

bb2:                                              ; preds = %bb4
  br label %bb3

bb1:                                              ; preds = %bb4
; call alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hf47d45f157f595c4E"(ptr align 8 %self, i64 %len, i64 %additional, i64 4, i64 4)
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; alloc::vec::Vec<T,A>::as_slice
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17heb145bbe2342f570E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h48ce9fcb76390137E(ptr %_4, i64 4, i64 4, i64 %len) #21
  br label %bb3

bb3:                                              ; preds = %bb1
  %2 = insertvalue { ptr, i64 } poison, ptr %_4, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17h6dd84f139c753509E(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h3ff7951c6f3bcfdfE(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #21
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #21
  ret ptr %_0
}

; alloc::alloc::exchange_malloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc15exchange_malloc17h9069f32671bf8c2aE(i64 %size, i64 %align) unnamed_addr #1 {
start:
  %_4 = alloca [16 x i8], align 8
  br label %bb4

bb4:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hd88a9ce2014e706cE(i64 %size, i64 %align) #21
  br label %bb5

bb5:                                              ; preds = %bb4
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 inttoptr (i64 1 to ptr), i64 %align, i64 %size, i1 zeroext false)
  %1 = extractvalue { ptr, i64 } %0, 0
  %2 = extractvalue { ptr, i64 } %0, 1
  store ptr %1, ptr %_4, align 8
  %3 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %2, ptr %3, align 8
  %4 = load ptr, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = ptrtoint ptr %4 to i64
  %8 = icmp eq i64 %7, 0
  %_5 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_5 to i1
  br i1 %9, label %bb2, label %bb3

bb2:                                              ; preds = %bb5
; call alloc::alloc::handle_alloc_error
  call void @_ZN5alloc5alloc18handle_alloc_error17h786143be1fde6527E(i64 %align, i64 %size) #19
  unreachable

bb3:                                              ; preds = %bb5
  %ptr.0 = load ptr, ptr %_4, align 8
  %10 = getelementptr inbounds i8, ptr %_4, i64 8
  %ptr.1 = load i64, ptr %10, align 8
  ret ptr %ptr.0

bb1:                                              ; No predecessors!
  unreachable
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h550903462d0ab95fE(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h3ff7951c6f3bcfdfE(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #21
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #21
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %_23) #21
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h550903462d0ab95fE(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17h6dd84f139c753509E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %ptr) #21
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %ptr4) #21
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::alloc::Global::grow_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17he54b42646a7a39beE(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1, i64 %2, i64 %3, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %layout5 = alloca [16 x i8], align 8
  %_60 = alloca [8 x i8], align 8
  %layout4 = alloca [16 x i8], align 8
  %self3 = alloca [16 x i8], align 8
  %_35 = alloca [16 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_25 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %old_size = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %old_layout, align 8
  %4 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %1, ptr %4, align 8
  store i64 %2, ptr %new_layout, align 8
  %5 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %3, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %7, ptr %old_size, align 8
  %8 = load i64, ptr %old_size, align 8
  %9 = icmp eq i64 %8, 0
  br i1 %9, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %10 = load i64, ptr %new_layout, align 8
  %11 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %12 = load i64, ptr %11, align 8
; call alloc::alloc::Global::alloc_impl
  %13 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 %self, i64 %10, i64 %12, i1 zeroext %zeroed)
  %14 = extractvalue { ptr, i64 } %13, 0
  %15 = extractvalue { ptr, i64 } %13, 1
  store ptr %14, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %15, ptr %16, align 8
  br label %bb9

bb1:                                              ; preds = %start
  %_43 = load i64, ptr %old_layout, align 8
  %_46 = icmp uge i64 %_43, 1
  %_47 = icmp ule i64 %_43, -9223372036854775808
  %_48 = and i1 %_46, %_47
  %_49 = load i64, ptr %new_layout, align 8
  %_52 = icmp uge i64 %_49, 1
  %_53 = icmp ule i64 %_49, -9223372036854775808
  %_54 = and i1 %_52, %_53
  %_11 = icmp eq i64 %_43, %_49
  br i1 %_11, label %bb3, label %bb4

bb9:                                              ; preds = %bb22, %bb30, %bb31, %bb37, %bb2
  %17 = load ptr, ptr %_0, align 8
  %18 = getelementptr inbounds i8, ptr %_0, i64 8
  %19 = load i64, ptr %18, align 8
  %20 = insertvalue { ptr, i64 } poison, ptr %17, 0
  %21 = insertvalue { ptr, i64 } %20, i64 %19, 1
  ret { ptr, i64 } %21

bb4:                                              ; preds = %bb1
  %22 = load i64, ptr %new_layout, align 8
  %23 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %24 = load i64, ptr %23, align 8
; call alloc::alloc::Global::alloc_impl
  %25 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 %self, i64 %22, i64 %24, i1 zeroext %zeroed)
  %26 = extractvalue { ptr, i64 } %25, 0
  %27 = extractvalue { ptr, i64 } %25, 1
  store ptr %26, ptr %self3, align 8
  %28 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %27, ptr %28, align 8
  %29 = load ptr, ptr %self3, align 8
  %30 = getelementptr inbounds i8, ptr %self3, i64 8
  %31 = load i64, ptr %30, align 8
  %32 = ptrtoint ptr %29 to i64
  %33 = icmp eq i64 %32, 0
  %_76 = select i1 %33, i64 1, i64 0
  %34 = trunc nuw i64 %_76 to i1
  br i1 %34, label %bb31, label %bb32

bb3:                                              ; preds = %bb1
  %35 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %new_size = load i64, ptr %35, align 8
  %36 = load i64, ptr %old_size, align 8
  %cond = icmp uge i64 %new_size, %36
  br label %bb10

bb31:                                             ; preds = %bb4
  %37 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %38 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store ptr %37, ptr %_0, align 8
  %39 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %38, ptr %39, align 8
  br label %bb9

bb32:                                             ; preds = %bb4
  %v.0 = load ptr, ptr %self3, align 8
  %40 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.1 = load i64, ptr %40, align 8
  store ptr %v.0, ptr %_35, align 8
  %41 = getelementptr inbounds i8, ptr %_35, i64 8
  store i64 %v.1, ptr %41, align 8
  %new_ptr.0 = load ptr, ptr %_35, align 8
  %42 = getelementptr inbounds i8, ptr %_35, i64 8
  %new_ptr.1 = load i64, ptr %42, align 8
  br label %bb33

bb33:                                             ; preds = %bb32
  %43 = load i64, ptr %old_size, align 8
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17ha90d2a614c03fc4fE(ptr %ptr, ptr %new_ptr.0, i64 1, i64 1, i64 %43) #21
  br label %bb35

bb35:                                             ; preds = %bb33
  %44 = load i64, ptr %old_size, align 8
  %45 = mul i64 %44, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %new_ptr.0, ptr align 1 %ptr, i64 %45, i1 false)
  %46 = load i64, ptr %old_layout, align 8
  %47 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %48 = load i64, ptr %47, align 8
  store i64 %46, ptr %layout4, align 8
  %49 = getelementptr inbounds i8, ptr %layout4, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr %old_size, align 8
  %51 = icmp eq i64 %50, 0
  br i1 %51, label %bb37, label %bb36

bb37:                                             ; preds = %bb36, %bb35
  store ptr %new_ptr.0, ptr %_0, align 8
  %52 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_ptr.1, ptr %52, align 8
  br label %bb9

bb36:                                             ; preds = %bb35
  %53 = load i64, ptr %old_layout, align 8
  %54 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %55 = load i64, ptr %54, align 8
  store i64 %53, ptr %layout5, align 8
  %56 = getelementptr inbounds i8, ptr %layout5, i64 8
  store i64 %55, ptr %56, align 8
  %57 = load i64, ptr %old_size, align 8
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %57, i64 %_43) #21
  br label %bb37

bb10:                                             ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) #21
  %58 = load i64, ptr %old_layout, align 8
  %59 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %60 = load i64, ptr %59, align 8
  store i64 %58, ptr %layout, align 8
  %61 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %60, ptr %61, align 8
  %62 = load i64, ptr %old_size, align 8
; call __rustc::__rust_realloc
  %63 = call ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr %ptr, i64 %62, i64 %_43, i64 %new_size) #21
  store ptr %63, ptr %raw_ptr, align 8
  %ptr6 = load ptr, ptr %raw_ptr, align 8
  %64 = load ptr, ptr %raw_ptr, align 8
  store ptr %64, ptr %_60, align 8
  %65 = load ptr, ptr %raw_ptr, align 8
  %_61 = ptrtoint ptr %65 to i64
  %66 = icmp eq i64 %_61, 0
  br i1 %66, label %bb14, label %bb41

bb14:                                             ; preds = %bb10
  store ptr null, ptr %self2, align 8
  br label %bb13

bb41:                                             ; preds = %bb10
  br label %bb16

bb13:                                             ; preds = %bb18, %bb14
  %67 = load ptr, ptr %self2, align 8
  %68 = ptrtoint ptr %67 to i64
  %69 = icmp eq i64 %68, 0
  %_64 = select i1 %69, i64 0, i64 1
  %70 = trunc nuw i64 %_64 to i1
  br i1 %70, label %bb21, label %bb20

bb16:                                             ; preds = %bb41
  %_63 = load ptr, ptr %raw_ptr, align 8
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %_63) #21
  br label %bb18

bb18:                                             ; preds = %bb16
  %_59 = load ptr, ptr %_60, align 8
  store ptr %_59, ptr %self2, align 8
  br label %bb13

bb21:                                             ; preds = %bb13
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  br label %bb19

bb20:                                             ; preds = %bb13
  store ptr null, ptr %self1, align 8
  br label %bb19

bb19:                                             ; preds = %bb21, %bb20
  %71 = load ptr, ptr %self1, align 8
  %72 = ptrtoint ptr %71 to i64
  %73 = icmp eq i64 %72, 0
  %_66 = select i1 %73, i64 1, i64 0
  %74 = trunc nuw i64 %_66 to i1
  br i1 %74, label %bb22, label %bb23

bb22:                                             ; preds = %bb19
  %75 = load ptr, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %76 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store ptr %75, ptr %_0, align 8
  %77 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %76, ptr %77, align 8
  br label %bb9

bb23:                                             ; preds = %bb19
  %v7 = load ptr, ptr %self1, align 8
  store ptr %v7, ptr %_25, align 8
  %ptr8 = load ptr, ptr %_25, align 8
  br i1 %zeroed, label %bb6, label %bb7

bb7:                                              ; preds = %bb27, %bb23
  br label %bb28

bb6:                                              ; preds = %bb23
  %self9 = load ptr, ptr %raw_ptr, align 8
  %78 = load ptr, ptr %raw_ptr, align 8
  %79 = load i64, ptr %old_size, align 8
  %self10 = getelementptr inbounds nuw i8, ptr %78, i64 %79
  %80 = load i64, ptr %old_size, align 8
  %count = sub i64 %new_size, %80
  br label %bb25

bb25:                                             ; preds = %bb6
  %_70 = icmp eq i64 %count, 0
; call core::intrinsics::write_bytes::precondition_check
  call void @_ZN4core10intrinsics11write_bytes18precondition_check17he1e985ad79f2dd77E(ptr %self10, i64 1, i1 zeroext %_70) #21
  br label %bb27

bb27:                                             ; preds = %bb25
  %81 = mul i64 1, %count
  call void @llvm.memset.p0.i64(ptr align 1 %self10, i8 0, i64 %81, i1 false)
  br label %bb7

bb28:                                             ; preds = %bb7
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %ptr8) #21
  br label %bb30

bb30:                                             ; preds = %bb28
  store ptr %ptr8, ptr %_0, align 8
  %82 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_size, ptr %82, align 8
  br label %bb9

bb5:                                              ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable

bb12:                                             ; No predecessors!
  unreachable

bb15:                                             ; No predecessors!
  unreachable
}

; alloc::slice::<impl [T]>::into_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h0f2b6cb3a0a7e2d1E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %capacity = alloca [8 x i8], align 8
  br label %bb3

bb3:                                              ; preds = %start
  store i64 %self.1, ptr %capacity, align 8
  br label %bb1

bb1:                                              ; preds = %bb3
  %cap = load i64, ptr %capacity, align 8
  br label %bb4

bb4:                                              ; preds = %bb1
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %self.0) #21
  br label %bb6

bb6:                                              ; preds = %bb4
  store i64 %cap, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %self.1, ptr %1, align 8
  ret void

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::slice::<impl [T]>::into_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17hd1c66c729c6206abE"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %capacity = alloca [8 x i8], align 8
  br label %bb3

bb3:                                              ; preds = %start
  store i64 %self.1, ptr %capacity, align 8
  br label %bb1

bb1:                                              ; preds = %bb3
  %cap = load i64, ptr %capacity, align 8
  br label %bb4

bb4:                                              ; preds = %bb1
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h2ed101965c90a61cE"(ptr %self.0) #21
  br label %bb6

bb6:                                              ; preds = %bb4
  store i64 %cap, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %self.1, ptr %1, align 8
  ret void

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::finish_grow
; Function Attrs: cold uwtable
define internal void @_ZN5alloc7raw_vec11finish_grow17h6f0d4c89233fcc60E(ptr sret([24 x i8]) align 8 %_0, i64 %0, i64 %1, ptr align 8 %current_memory, ptr align 1 %alloc) unnamed_addr #5 {
start:
  %_43 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  %memory = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %new_layout, align 8
  %2 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %alloc_size = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = icmp eq i64 %5, 0
  %_9 = select i1 %6, i64 0, i64 1
  %7 = trunc nuw i64 %_9 to i1
  br i1 %7, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %ptr = load ptr, ptr %current_memory, align 8
  %8 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = getelementptr inbounds i8, ptr %8, i64 8
  %11 = load i64, ptr %10, align 8
  store i64 %9, ptr %old_layout, align 8
  %12 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %11, ptr %12, align 8
  %_26 = load i64, ptr %old_layout, align 8
  %_29 = icmp uge i64 %_26, 1
  %_30 = icmp ule i64 %_26, -9223372036854775808
  %_31 = and i1 %_29, %_30
  %_32 = load i64, ptr %new_layout, align 8
  %_35 = icmp uge i64 %_32, 1
  %_36 = icmp ule i64 %_32, -9223372036854775808
  %_37 = and i1 %_35, %_36
  %cond = icmp eq i64 %_26, %_32
  br label %bb7

bb2:                                              ; preds = %start
  %13 = load i64, ptr %new_layout, align 8
  %14 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %15 = load i64, ptr %14, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %16 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h83970b8b3af92a20E"(ptr align 1 %alloc, i64 %13, i64 %15)
  %17 = extractvalue { ptr, i64 } %16, 0
  %18 = extractvalue { ptr, i64 } %16, 1
  store ptr %17, ptr %memory, align 8
  %19 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %18, ptr %19, align 8
  br label %bb6

bb7:                                              ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) #21
  br label %bb8

bb8:                                              ; preds = %bb7
  %20 = load i64, ptr %old_layout, align 8
  %21 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %22 = load i64, ptr %21, align 8
  %23 = load i64, ptr %new_layout, align 8
  %24 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %25 = load i64, ptr %24, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::grow
  %26 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17h2623c36686f0b010E"(ptr align 1 %alloc, ptr %ptr, i64 %20, i64 %22, i64 %23, i64 %25)
  %27 = extractvalue { ptr, i64 } %26, 0
  %28 = extractvalue { ptr, i64 } %26, 1
  store ptr %27, ptr %memory, align 8
  %29 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %28, ptr %29, align 8
  br label %bb6

bb6:                                              ; preds = %bb2, %bb8
  %30 = load ptr, ptr %memory, align 8
  %31 = getelementptr inbounds i8, ptr %memory, i64 8
  %32 = load i64, ptr %31, align 8
  store ptr %30, ptr %self, align 8
  %33 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %32, ptr %33, align 8
  %34 = load ptr, ptr %self, align 8
  %35 = getelementptr inbounds i8, ptr %self, i64 8
  %36 = load i64, ptr %35, align 8
  %37 = ptrtoint ptr %34 to i64
  %38 = icmp eq i64 %37, 0
  %_40 = select i1 %38, i64 1, i64 0
  %39 = trunc nuw i64 %_40 to i1
  br i1 %39, label %bb10, label %bb11

bb10:                                             ; preds = %bb6
  %_44.0 = load i64, ptr %new_layout, align 8
  %40 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %_44.1 = load i64, ptr %40, align 8
  store i64 %_44.0, ptr %_43, align 8
  %41 = getelementptr inbounds i8, ptr %_43, i64 8
  store i64 %_44.1, ptr %41, align 8
  %_42.0 = load i64, ptr %_43, align 8
  %42 = getelementptr inbounds i8, ptr %_43, i64 8
  %_42.1 = load i64, ptr %42, align 8
  %43 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_42.0, ptr %43, align 8
  %44 = getelementptr inbounds i8, ptr %43, i64 8
  store i64 %_42.1, ptr %44, align 8
  store i64 1, ptr %_0, align 8
  br label %bb9

bb11:                                             ; preds = %bb6
  %t.0 = load ptr, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %t.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %t.1, ptr %47, align 8
  store i64 0, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb10, %bb11
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h1a646460ea757233E"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h42c7557aa56a42d1E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17haa027d38a42f38acE"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h42c7557aa56a42d1E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17hd6b277d9c3494e09E"(i64 %self1, i64 %self3) #21
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hd88a9ce2014e706cE(i64 %8, i64 %9) #21
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::grow_amortized
; Function Attrs: uwtable
define internal { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17h54a00296951f4364E"(ptr align 8 %self, i64 %len, i64 %additional, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_56 = alloca [16 x i8], align 8
  %_51 = alloca [16 x i8], align 8
  %self9 = alloca [24 x i8], align 8
  %self8 = alloca [16 x i8], align 8
  %_38 = alloca [16 x i8], align 8
  %residual7 = alloca [16 x i8], align 8
  %_26 = alloca [24 x i8], align 8
  %self6 = alloca [24 x i8], align 8
  %_24 = alloca [24 x i8], align 8
  %residual5 = alloca [16 x i8], align 8
  %elem_layout4 = alloca [16 x i8], align 8
  %self3 = alloca [24 x i8], align 8
  %_19 = alloca [24 x i8], align 8
  %v1 = alloca [8 x i8], align 8
  %residual = alloca [16 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb1, label %bb2

bb1:                                              ; preds = %start
  %5 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %6 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %5, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %6, ptr %7, align 8
  br label %bb8

bb2:                                              ; preds = %start
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %len, i64 %additional)
  %_32.0 = extractvalue { i64, i1 } %8, 0
  %_32.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_32.1, label %bb9, label %bb11

bb8:                                              ; preds = %bb7, %bb24, %bb1
  %9 = load i64, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  %11 = load i64, ptr %10, align 8
  %12 = insertvalue { i64, i64 } poison, i64 %9, 0
  %13 = insertvalue { i64, i64 } %12, i64 %11, 1
  ret { i64, i64 } %13

bb11:                                             ; preds = %bb2
  %_33 = add nuw i64 %len, %additional
  %14 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %_33, ptr %14, align 8
  store i64 1, ptr %self2, align 8
  %15 = getelementptr inbounds i8, ptr %self2, i64 8
  %v = load i64, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %v, ptr %16, align 8
  store i64 -9223372036854775807, ptr %self1, align 8
  %17 = getelementptr inbounds i8, ptr %self1, i64 8
  %v10 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %v10, ptr %18, align 8
  store i64 -9223372036854775807, ptr %_7, align 8
  %19 = getelementptr inbounds i8, ptr %_7, i64 8
  %required_cap = load i64, ptr %19, align 8
  %self11 = load i64, ptr %self, align 8
  %v112 = mul i64 %self11, 2
; call core::cmp::Ord::max
  %cap = call i64 @_ZN4core3cmp3Ord3max17ha9c7785965ec8b2fE(i64 %v112, i64 %required_cap)
  %20 = icmp eq i64 %size, 1
  br i1 %20, label %bb14, label %bb15

bb9:                                              ; preds = %bb2
  %21 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %22 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %21, ptr %self2, align 8
  %23 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %22, ptr %23, align 8
  %24 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %25 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %24, ptr %self1, align 8
  %26 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %25, ptr %26, align 8
  %e.024 = load i64, ptr %self1, align 8
  %27 = getelementptr inbounds i8, ptr %self1, i64 8
  %e.125 = load i64, ptr %27, align 8
  store i64 %e.024, ptr %_38, align 8
  %28 = getelementptr inbounds i8, ptr %_38, i64 8
  store i64 %e.125, ptr %28, align 8
  %29 = load i64, ptr %_38, align 8
  %30 = getelementptr inbounds i8, ptr %_38, i64 8
  %31 = load i64, ptr %30, align 8
  store i64 %29, ptr %_7, align 8
  %32 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %31, ptr %32, align 8
  %33 = load i64, ptr %_7, align 8
  %34 = getelementptr inbounds i8, ptr %_7, i64 8
  %35 = load i64, ptr %34, align 8
  store i64 %33, ptr %residual, align 8
  %36 = getelementptr inbounds i8, ptr %residual, i64 8
  store i64 %35, ptr %36, align 8
  %e.026 = load i64, ptr %residual, align 8
  %37 = getelementptr inbounds i8, ptr %residual, i64 8
  %e.127 = load i64, ptr %37, align 8
  store i64 %e.026, ptr %_0, align 8
  %38 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.127, ptr %38, align 8
  br label %bb7

bb14:                                             ; preds = %bb11
  store i64 8, ptr %v1, align 8
  br label %bb13

bb15:                                             ; preds = %bb11
  %_41 = icmp ule i64 %size, 1024
  br i1 %_41, label %bb16, label %bb17

bb13:                                             ; preds = %bb18, %bb14
  %39 = load i64, ptr %v1, align 8
; call core::cmp::Ord::max
  %cap13 = call i64 @_ZN4core3cmp3Ord3max17ha9c7785965ec8b2fE(i64 %39, i64 %cap)
  %40 = load i64, ptr %elem_layout, align 8
  %41 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %42 = load i64, ptr %41, align 8
  store i64 %40, ptr %elem_layout4, align 8
  %43 = getelementptr inbounds i8, ptr %elem_layout4, i64 8
  store i64 %42, ptr %43, align 8
; call core::alloc::layout::Layout::repeat
  call void @_ZN4core5alloc6layout6Layout6repeat17h90036ea58cd2d6f8E(ptr sret([24 x i8]) align 8 %self9, ptr align 8 %elem_layout4, i64 %cap13)
  %44 = load i64, ptr %self9, align 8
  %45 = icmp eq i64 %44, 0
  %_45 = select i1 %45, i64 1, i64 0
  %46 = trunc nuw i64 %_45 to i1
  br i1 %46, label %bb21, label %bb22

bb17:                                             ; preds = %bb15
  store i64 1, ptr %v1, align 8
  br label %bb18

bb16:                                             ; preds = %bb15
  store i64 4, ptr %v1, align 8
  br label %bb18

bb18:                                             ; preds = %bb16, %bb17
  br label %bb13

bb21:                                             ; preds = %bb13
  %47 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %48 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %47, ptr %self8, align 8
  %49 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %51 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %52 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %52, i64 8
  store i64 %51, ptr %53, align 8
  store i64 1, ptr %self3, align 8
  %54 = getelementptr inbounds i8, ptr %self3, i64 8
  %e.020 = load i64, ptr %54, align 8
  %55 = getelementptr inbounds i8, ptr %54, i64 8
  %e.121 = load i64, ptr %55, align 8
  store i64 %e.020, ptr %_51, align 8
  %56 = getelementptr inbounds i8, ptr %_51, i64 8
  store i64 %e.121, ptr %56, align 8
  %57 = load i64, ptr %_51, align 8
  %58 = getelementptr inbounds i8, ptr %_51, i64 8
  %59 = load i64, ptr %58, align 8
  %60 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %57, ptr %60, align 8
  %61 = getelementptr inbounds i8, ptr %60, i64 8
  store i64 %59, ptr %61, align 8
  store i64 1, ptr %_19, align 8
  %62 = getelementptr inbounds i8, ptr %_19, i64 8
  %63 = load i64, ptr %62, align 8
  %64 = getelementptr inbounds i8, ptr %62, i64 8
  %65 = load i64, ptr %64, align 8
  store i64 %63, ptr %residual5, align 8
  %66 = getelementptr inbounds i8, ptr %residual5, i64 8
  store i64 %65, ptr %66, align 8
  %e.022 = load i64, ptr %residual5, align 8
  %67 = getelementptr inbounds i8, ptr %residual5, i64 8
  %e.123 = load i64, ptr %67, align 8
  store i64 %e.022, ptr %_0, align 8
  %68 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.123, ptr %68, align 8
  br label %bb6

bb22:                                             ; preds = %bb13
  %t.0 = load i64, ptr %self9, align 8
  %69 = getelementptr inbounds i8, ptr %self9, i64 8
  %t.1 = load i64, ptr %69, align 8
  %70 = getelementptr inbounds i8, ptr %self9, i64 16
  %t = load i64, ptr %70, align 8
  store i64 %t.0, ptr %self8, align 8
  %71 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %t.1, ptr %71, align 8
  %t.014 = load i64, ptr %self8, align 8
  %72 = getelementptr inbounds i8, ptr %self8, i64 8
  %t.115 = load i64, ptr %72, align 8
  %73 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %t.014, ptr %73, align 8
  %74 = getelementptr inbounds i8, ptr %73, i64 8
  store i64 %t.115, ptr %74, align 8
  store i64 0, ptr %self3, align 8
  %75 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.0 = load i64, ptr %75, align 8
  %76 = getelementptr inbounds i8, ptr %75, i64 8
  %v.1 = load i64, ptr %76, align 8
  %77 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %v.0, ptr %77, align 8
  %78 = getelementptr inbounds i8, ptr %77, i64 8
  store i64 %v.1, ptr %78, align 8
  store i64 0, ptr %_19, align 8
  %79 = getelementptr inbounds i8, ptr %_19, i64 8
  %new_layout.0 = load i64, ptr %79, align 8
  %80 = getelementptr inbounds i8, ptr %79, i64 8
  %new_layout.1 = load i64, ptr %80, align 8
  %81 = load i64, ptr %elem_layout, align 8
  %82 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %83 = load i64, ptr %82, align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h42c7557aa56a42d1E"(ptr sret([24 x i8]) align 8 %_26, ptr align 8 %self, i64 %81, i64 %83)
  %_28 = getelementptr inbounds i8, ptr %self, i64 16
; call alloc::raw_vec::finish_grow
  call void @_ZN5alloc7raw_vec11finish_grow17h6f0d4c89233fcc60E(ptr sret([24 x i8]) align 8 %self6, i64 %new_layout.0, i64 %new_layout.1, ptr align 8 %_26, ptr align 1 %_28)
  %_53 = load i64, ptr %self6, align 8
  %84 = trunc nuw i64 %_53 to i1
  br i1 %84, label %bb23, label %bb24

bb23:                                             ; preds = %bb22
  %85 = getelementptr inbounds i8, ptr %self6, i64 8
  %e.0 = load i64, ptr %85, align 8
  %86 = getelementptr inbounds i8, ptr %85, i64 8
  %e.1 = load i64, ptr %86, align 8
  store i64 %e.0, ptr %_56, align 8
  %87 = getelementptr inbounds i8, ptr %_56, i64 8
  store i64 %e.1, ptr %87, align 8
  %88 = load i64, ptr %_56, align 8
  %89 = getelementptr inbounds i8, ptr %_56, i64 8
  %90 = load i64, ptr %89, align 8
  %91 = getelementptr inbounds i8, ptr %_24, i64 8
  store i64 %88, ptr %91, align 8
  %92 = getelementptr inbounds i8, ptr %91, i64 8
  store i64 %90, ptr %92, align 8
  store i64 1, ptr %_24, align 8
  %93 = getelementptr inbounds i8, ptr %_24, i64 8
  %94 = load i64, ptr %93, align 8
  %95 = getelementptr inbounds i8, ptr %93, i64 8
  %96 = load i64, ptr %95, align 8
  store i64 %94, ptr %residual7, align 8
  %97 = getelementptr inbounds i8, ptr %residual7, i64 8
  store i64 %96, ptr %97, align 8
  %e.018 = load i64, ptr %residual7, align 8
  %98 = getelementptr inbounds i8, ptr %residual7, i64 8
  %e.119 = load i64, ptr %98, align 8
  store i64 %e.018, ptr %_0, align 8
  %99 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.119, ptr %99, align 8
  br label %bb6

bb24:                                             ; preds = %bb22
  %100 = getelementptr inbounds i8, ptr %self6, i64 8
  %v.016 = load ptr, ptr %100, align 8
  %101 = getelementptr inbounds i8, ptr %100, i64 8
  %v.117 = load i64, ptr %101, align 8
  %102 = getelementptr inbounds i8, ptr %_24, i64 8
  store ptr %v.016, ptr %102, align 8
  %103 = getelementptr inbounds i8, ptr %102, i64 8
  store i64 %v.117, ptr %103, align 8
  store i64 0, ptr %_24, align 8
  %104 = getelementptr inbounds i8, ptr %_24, i64 8
  %ptr.0 = load ptr, ptr %104, align 8
  %105 = getelementptr inbounds i8, ptr %104, i64 8
  %ptr.1 = load i64, ptr %105, align 8
  %106 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %ptr.0, ptr %106, align 8
  store i64 %cap13, ptr %self, align 8
  %107 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.3, align 8
  %108 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.3, i64 8), align 8
  store i64 %107, ptr %_0, align 8
  %109 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %108, ptr %109, align 8
  br label %bb8

bb6:                                              ; preds = %bb21, %bb23
  br label %bb7

bb7:                                              ; preds = %bb9, %bb6
  br label %bb8

bb3:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h0812776bfe393e8fE"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h90036ea58cd2d6f8E(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17hb84390020151c431E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h83970b8b3af92a20E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h795bf619d1c069c5E"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h0812776bfe393e8fE"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #19
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) #21
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
; Function Attrs: cold uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hf47d45f157f595c4E"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #5 {
start:
  %_5 = alloca [16 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::grow_amortized
  %0 = call { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17h54a00296951f4364E"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1)
  %1 = extractvalue { i64, i64 } %0, 0
  %2 = extractvalue { i64, i64 } %0, 1
  store i64 %1, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %2, ptr %3, align 8
  %4 = load i64, ptr %_5, align 8
  %5 = getelementptr inbounds i8, ptr %_5, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = icmp eq i64 %4, -9223372036854775807
  %_6 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_6 to i1
  br i1 %8, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %err.0 = load i64, ptr %_5, align 8
  %9 = getelementptr inbounds i8, ptr %_5, i64 8
  %err.1 = load i64, ptr %9, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 @alloc_3d99a694a639f36d512dfe286335fb89) #19
  unreachable

bb3:                                              ; preds = %start
  ret void

bb4:                                              ; No predecessors!
  unreachable
}

; <&i32 as core::ops::arith::Mul<i32>>::mul
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN60_$LT$$RF$i32$u20$as$u20$core..ops..arith..Mul$LT$i32$GT$$GT$3mul17h386fe3a6dc551df5E"(ptr align 4 %self, i32 %other, ptr align 8 %0) unnamed_addr #1 {
start:
  %self1 = load i32, ptr %self, align 4
  %1 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %self1, i32 %other)
  %_4.0 = extractvalue { i32, i1 } %1, 0
  %_4.1 = extractvalue { i32, i1 } %1, 1
  br i1 %_4.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_4.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 %0) #19
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hdcee5a0774ccf80cE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h48ce9fcb76390137E(ptr %_8, i64 1, i64 1, i64 %len) #21
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h072395550d287f23E"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1dd039910d510f0bE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h591157094937ac9fE"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h79fc13414bd2acf0E"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf6c97b35307792afE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17haa027d38a42f38acE"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #21
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17hb84390020151c431E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::grow
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17h2623c36686f0b010E"(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::grow_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17he54b42646a7a39beE(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h83970b8b3af92a20E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hef5cead5a76b6cafE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h670961bd12dd27daE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h48ce9fcb76390137E(ptr %_6, i64 4, i64 4, i64 %len) #21
  br label %bb4

bb4:                                              ; preds = %bb2
; call <[T] as core::fmt::Debug>::fmt
  %_0 = call zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h4401fc56aa1310e0E"(ptr align 4 %_6, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum
; Function Attrs: uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17hefda667590e8d22fE"(ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h87cfdaaa71eb7fe5E"(ptr %iter.0, ptr %iter.1, i32 0)
  ret i32 %_0
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17haeec51e549500d8cE"(ptr align 1 %_1, i32 %a, ptr align 4 %b) unnamed_addr #1 {
start:
  %other = load i32, ptr %b, align 4
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %a, i32 %other)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_5.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2ff564f83739a041825038989c62f69d) #19
  unreachable
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h54583b80c46da701E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9c97cdaf93931b37E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha0176726bdb08e61E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %ptr = load ptr, ptr %self, align 8
  store i64 72, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  store i64 8, ptr %0, align 8
  %align = load i64, ptr %0, align 8
  br label %bb6

bb6:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hd88a9ce2014e706cE(i64 %size, i64 %align) #21
  br label %bb7

bb7:                                              ; preds = %bb6
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %size, ptr %2, align 8
  store i64 %align, ptr %layout, align 8
  %3 = icmp eq i64 %size, 0
  br i1 %3, label %bb3, label %bb1

bb3:                                              ; preds = %bb1, %bb7
  ret void

bb1:                                              ; preds = %bb7
  %_7 = getelementptr inbounds i8, ptr %self, i64 8
  %4 = load i64, ptr %layout, align 8
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %6 = load i64, ptr %5, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17haa027d38a42f38acE"(ptr align 1 %_7, ptr %ptr, i64 %4, i64 %6)
  br label %bb3
}

; <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %self) unnamed_addr #1 {
start:
; call alloc::vec::Vec<T,A>::as_slice
  %0 = call { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17heb145bbe2342f570E"(ptr align 8 %self)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17haa320c0e82b132e3E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h1a646460ea757233E"(ptr align 8 %self, i64 4, i64 4)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hcdb01769256b3126E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h1a646460ea757233E"(ptr align 8 %self, i64 8, i64 24)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hf8df6b229410e578E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h1a646460ea757233E"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <core::result::Result<T,E> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal void @"_ZN79_$LT$core..result..Result$LT$T$C$E$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h5da1a59185789f7cE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_5 = alloca [24 x i8], align 8
  %e = alloca [24 x i8], align 8
  %0 = load i64, ptr %self, align 8
  %1 = icmp eq i64 %0, -9223372036854775808
  %_2 = select i1 %1, i64 0, i64 1
  %2 = trunc nuw i64 %_2 to i1
  br i1 %2, label %bb2, label %bb3

bb2:                                              ; preds = %start
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %e, ptr align 8 %self, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_5, ptr align 8 %e, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_5, i64 24, i1 false)
  br label %bb4

bb3:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %v = load i32, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 8
  store i32 %v, ptr %4, align 8
  store i64 -9223372036854775808, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <alloc::vec::set_len_on_drop::SetLenOnDrop as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN83_$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$u20$as$u20$core..ops..drop..Drop$GT$4drop17hbff6fc332a2b6947E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_2 = load i64, ptr %0, align 8
  %_3 = load ptr, ptr %self, align 8
  store i64 %_2, ptr %_3, align 8
  ret void
}

; <alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN86_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17he785bd7b1466bbacE"(ptr align 8 %self) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self1 = alloca [8 x i8], align 8
  %guard = alloca [8 x i8], align 8
  store ptr %self, ptr %guard, align 8
  %_6 = load ptr, ptr %guard, align 8
  store ptr %_6, ptr %self1, align 8
  %0 = getelementptr inbounds i8, ptr %_6, i64 8
  %self2 = load ptr, ptr %0, align 8
; invoke core::iter::traits::exact_size::ExactSizeIterator::len
  %len = invoke i64 @_ZN4core4iter6traits10exact_size17ExactSizeIterator3len17h85f117f1265b4418E(ptr align 8 %_6)
          to label %bb5 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<<alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop::DropGuard<11_advanced_features::Message,alloc::alloc::Global>>
  call void @"_ZN4core3ptr183drop_in_place$LT$$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$11_advanced_features..Message$C$alloc..alloc..Global$GT$$GT$17h250e8b09876d3975E"(ptr align 8 %guard) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb5:                                              ; preds = %start
; invoke core::ptr::drop_in_place<[11_advanced_features::Message]>
  invoke void @"_ZN4core3ptr60drop_in_place$LT$$u5b$11_advanced_features..Message$u5d$$GT$17h08e32bc0eaf2ea81E"(ptr align 8 %self2, i64 %len)
          to label %bb1 unwind label %funclet_bb3

bb1:                                              ; preds = %bb5
; call core::ptr::drop_in_place<<alloc::vec::into_iter::IntoIter<T,A> as core::ops::drop::Drop>::drop::DropGuard<11_advanced_features::Message,alloc::alloc::Global>>
  call void @"_ZN4core3ptr183drop_in_place$LT$$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$11_advanced_features..Message$C$alloc..alloc..Global$GT$$GT$17h250e8b09876d3975E"(ptr align 8 %guard)
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h3f1adc59298bbf73E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #1 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h795bf619d1c069c5E"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17ha90d2a614c03fc4fE(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #21
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; <alloc::vec::Vec<T,A> as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN90_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd286d096d4486eb1E"(ptr sret([32 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %cap = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %me = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %me, ptr align 8 %self, i64 24, i1 false)
  %src = getelementptr inbounds i8, ptr %me, i64 16
  %0 = getelementptr inbounds i8, ptr %me, i64 8
  %_27 = load ptr, ptr %0, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %me, i64 16
  %count = load i64, ptr %1, align 8
  %_34 = icmp ule i64 %count, 384307168202282325
  %_17 = getelementptr inbounds nuw %Message, ptr %_27, i64 %count
  store ptr %_17, ptr %end, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  br label %bb7

bb7:                                              ; preds = %bb3
  %self1 = load i64, ptr %me, align 8
  store i64 %self1, ptr %cap, align 8
  br label %bb5

bb6:                                              ; No predecessors!
  store i64 -1, ptr %cap, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %_24 = load i64, ptr %cap, align 8
  %_25 = load ptr, ptr %end, align 8
  store ptr %_27, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %_24, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %_27, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 24
  store ptr %_25, ptr %4, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable

bb4:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::find
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4find17h23c4be9d08bb81b8E"(ptr align 8 %self, ptr align 1 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %x = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %predicate = alloca [8 x i8], align 8
  store ptr %0, ptr %predicate, align 8
  br label %bb1

bb1:                                              ; preds = %bb6, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %1 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h826e87a45bdaf675E"(ptr align 8 %self)
          to label %bb2 unwind label %funclet_bb9

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad unwind to caller

funclet_bb9:                                      ; preds = %bb3, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb9

bb2:                                              ; preds = %bb1
  store ptr %1, ptr %_3, align 8
  %2 = load ptr, ptr %_3, align 8
  %3 = ptrtoint ptr %2 to i64
  %4 = icmp eq i64 %3, 0
  %_4 = select i1 %4, i64 0, i64 1
  %5 = trunc nuw i64 %_4 to i1
  br i1 %5, label %bb3, label %bb7

bb3:                                              ; preds = %bb2
  %6 = load ptr, ptr %_3, align 8
  store ptr %6, ptr %x, align 8
; invoke core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
  %_6 = invoke zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h5115e003ff50ca8aE"(ptr align 8 %predicate, ptr align 8 %x)
          to label %bb4 unwind label %funclet_bb9

bb7:                                              ; preds = %bb2
  store ptr null, ptr %_0, align 8
  br label %bb8

bb4:                                              ; preds = %bb3
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  br label %bb1

bb5:                                              ; preds = %bb4
  %7 = load ptr, ptr %x, align 8
  store ptr %7, ptr %_0, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb5
  %8 = load ptr, ptr %_0, align 8
  ret ptr %8

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h595cbf4f8f6b7260E"(ptr %0, ptr %1, ptr align 8 %f) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
; invoke core::ptr::drop_in_place<core::iter::adapters::map::map_fold<&i32,i32,(),11_advanced_features::main::{{closure}},core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>::{{closure}}>
  invoke void @"_ZN4core3ptr484drop_in_place$LT$core..iter..adapters..map..map_fold$LT$$RF$i32$C$i32$C$$LP$$RP$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hf272d9ea9eac737fE"(ptr align 8 %f)
          to label %bb14 unwind label %funclet_bb20

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17habfe6d1f0d527829E"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke core::iter::adapters::map::map_fold::{{closure}}
  invoke void @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h675e1245fd7a490eE"(ptr align 8 %f, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h0ada306c96cc4e7eE"(i64 %self2, i64 1) #21
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
; invoke core::ptr::drop_in_place<core::iter::adapters::map::map_fold<&i32,i32,(),11_advanced_features::main::{{closure}},core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>::{{closure}}>
  invoke void @"_ZN4core3ptr484drop_in_place$LT$core..iter..adapters..map..map_fold$LT$$RF$i32$C$i32$C$$LP$$RP$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hf272d9ea9eac737fE"(ptr align 8 %f)
          to label %bb14 unwind label %funclet_bb20

bb20:                                             ; preds = %funclet_bb20
  %12 = load i8, ptr %_34, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb4, %bb15, %bb12
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb14:                                             ; preds = %bb4, %bb12
  ret void

bb15:                                             ; preds = %bb17, %bb18
; call core::ptr::drop_in_place<core::iter::adapters::map::map_fold<&i32,i32,(),11_advanced_features::main::{{closure}},core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::iter::adapters::map::Map<core::slice::iter::Iter<i32>,11_advanced_features::main::{{closure}}>>::{{closure}}>::{{closure}}>::{{closure}}>
  call void @"_ZN4core3ptr484drop_in_place$LT$core..iter..adapters..map..map_fold$LT$$RF$i32$C$i32$C$$LP$$RP$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..iter..adapters..map..Map$LT$core..slice..iter..Iter$LT$i32$GT$$C$11_advanced_features..main..$u7b$$u7b$closure$u7d$$u7d$$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hf272d9ea9eac737fE"(ptr align 8 %f) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h87cfdaaa71eb7fe5E"(ptr %0, ptr %1, i32 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [4 x i8], align 4
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [4 x i8], align 4
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i32 %init, ptr %acc, align 4
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i32 %init, ptr %_0, align 4
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17habfe6d1f0d527829E"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load i32, ptr %acc, align 4
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
  %_19 = invoke i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17haeec51e549500d8cE"(ptr align 1 %f, i32 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store i32 %_19, ptr %acc, align 4
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h0ada306c96cc4e7eE"(i64 %self2, i64 1) #21
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load i32, ptr %acc, align 4
  store i32 %12, ptr %_0, align 4
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load i32, ptr %_0, align 4
  ret i32 %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h826e87a45bdaf675E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i32, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::position
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$8position17hfb102d45f8d88a18E"(ptr align 8 %self, ptr align 4 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %n = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %predicate = alloca [8 x i8], align 8
  store ptr %0, ptr %predicate, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %_7 = load ptr, ptr %1, align 8
  %_8 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %2 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17habfe6d1f0d527829E"(ptr %_7, ptr %_8)
          to label %bb3 unwind label %funclet_bb14

bb14:                                             ; preds = %funclet_bb14
  cleanupret from %cleanuppad unwind to caller

funclet_bb14:                                     ; preds = %panic, %bb7, %bb5, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb14

bb3:                                              ; preds = %bb2
  store i64 %2, ptr %n, align 8
  br label %bb4

bb4:                                              ; preds = %bb3
  store i64 0, ptr %i, align 8
  br label %bb5

bb5:                                              ; preds = %bb11, %bb4
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %3 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h826e87a45bdaf675E"(ptr align 8 %self)
          to label %bb6 unwind label %funclet_bb14

bb6:                                              ; preds = %bb5
  store ptr %3, ptr %_10, align 8
  %4 = load ptr, ptr %_10, align 8
  %5 = ptrtoint ptr %4 to i64
  %6 = icmp eq i64 %5, 0
  %_11 = select i1 %6, i64 0, i64 1
  %7 = trunc nuw i64 %_11 to i1
  br i1 %7, label %bb7, label %bb12

bb7:                                              ; preds = %bb6
  %x = load ptr, ptr %_10, align 8
; invoke _11_advanced_features::find_number::{{closure}}
  %_13 = invoke zeroext i1 @"_ZN21_11_advanced_features11find_number28_$u7b$$u7b$closure$u7d$$u7d$17ha05cf360fcd1b761E"(ptr align 8 %predicate, ptr align 4 %x)
          to label %bb8 unwind label %funclet_bb14

bb12:                                             ; preds = %bb6
  %8 = load i64, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bf27c6c651a4deb7bc5a04d4d8800937.0, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb13

bb8:                                              ; preds = %bb7
  br i1 %_13, label %bb9, label %bb10

bb10:                                             ; preds = %bb8
  %11 = load i64, ptr %i, align 8
  %12 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %11, i64 1)
  %_20.0 = extractvalue { i64, i1 } %12, 0
  %_20.1 = extractvalue { i64, i1 } %12, 1
  br i1 %_20.1, label %panic, label %bb11

bb9:                                              ; preds = %bb8
  %_17 = load i64, ptr %i, align 8
  %_18 = load i64, ptr %n, align 8
  %cond = icmp ult i64 %_17, %_18
  br label %bb16

bb11:                                             ; preds = %bb10
  store i64 %_20.0, ptr %i, align 8
  br label %bb5

panic:                                            ; preds = %bb10
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_abafb912297f657dc3b31c1b7ae4c4e5) #19
          to label %unreachable unwind label %funclet_bb14

unreachable:                                      ; preds = %panic
  unreachable

bb16:                                             ; preds = %bb9
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h3fc8a757cf9e840eE(i1 zeroext %cond) #21
  br label %bb17

bb17:                                             ; preds = %bb16
  %_19 = load i64, ptr %i, align 8
  %13 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_19, ptr %13, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb13:                                             ; preds = %bb12, %bb17
  %14 = load i64, ptr %_0, align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  %16 = load i64, ptr %15, align 8
  %17 = insertvalue { i64, i64 } poison, i64 %14, 0
  %18 = insertvalue { i64, i64 } %17, i64 %16, 1
  ret { i64, i64 } %18

bb18:                                             ; No predecessors!
  unreachable

bb1:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h607056622e1a8a4eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_9 = alloca [16 x i8], align 8
  %exact = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %_7 = load ptr, ptr %self, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %1 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17habfe6d1f0d527829E"(ptr %_6, ptr %_7)
  store i64 %1, ptr %exact, align 8
  br label %bb4

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %exact, align 8
  %_10 = load i64, ptr %exact, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_10, ptr %2, align 8
  store i64 1, ptr %_9, align 8
  store i64 %_8, ptr %_0, align 8
  %3 = load i64, ptr %_9, align 8
  %4 = getelementptr inbounds i8, ptr %_9, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 %5, ptr %7, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h0cd22d5662f53dd4E"(ptr sret([24 x i8]) align 8 %_0, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #1 {
start:
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %1 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h072395550d287f23E"(ptr %iter.0, ptr %iter.1)
  %_2.0 = extractvalue { ptr, ptr } %1, 0
  %_2.1 = extractvalue { ptr, ptr } %1, 1
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hf150c607b694f026E"(ptr sret([24 x i8]) align 8 %_0, ptr %_2.0, ptr %_2.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17hdc98608e3c1be987E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iter, ptr align 8 %0) unnamed_addr #1 {
start:
  %_2 = alloca [12 x i8], align 4
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1dd039910d510f0bE"(ptr sret([12 x i8]) align 4 %_2, ptr align 4 %iter)
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h66659f6106abc020E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %_2, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17hf180f6e7d88e2d46E"(ptr sret([24 x i8]) align 8 %_0, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #1 {
start:
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %1 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h591157094937ac9fE"(ptr %iter.0, ptr %iter.1)
  %_2.0 = extractvalue { ptr, ptr } %1, 0
  %_2.1 = extractvalue { ptr, ptr } %1, 1
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hd738f588413f9afcE"(ptr sret([24 x i8]) align 8 %_0, ptr %_2.0, ptr %_2.1, ptr align 8 %0)
  ret void
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::from_output
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h10298756ec5c99ffE"() unnamed_addr #1 {
start:
  %_0 = alloca [8 x i8], align 4
  store i32 0, ptr %_0, align 4
  %0 = load i32, ptr %_0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 4
  %2 = load i32, ptr %1, align 4
  %3 = insertvalue { i32, i32 } poison, i32 %0, 0
  %4 = insertvalue { i32, i32 } %3, i32 %2, 1
  ret { i32, i32 } %4
}

; <core::ops::control_flow::ControlFlow<B,C> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN95_$LT$core..ops..control_flow..ControlFlow$LT$B$C$C$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h4b7c30ce07491c4cE"(i32 %0, i32 %1) unnamed_addr #1 {
start:
  %_5 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %self = alloca [8 x i8], align 4
  store i32 %0, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  %4 = getelementptr inbounds i8, ptr %self, i64 4
  %5 = load i32, ptr %4, align 4
  %_2 = zext i32 %3 to i64
  %6 = trunc nuw i64 %_2 to i1
  br i1 %6, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %7 = getelementptr inbounds i8, ptr %self, i64 4
  %b = load i32, ptr %7, align 4
  store i32 %b, ptr %_5, align 4
  %8 = load i32, ptr %_5, align 4
  %9 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %8, ptr %9, align 4
  store i32 1, ptr %_0, align 4
  br label %bb4

bb3:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb4

bb4:                                              ; preds = %bb2, %bb3
  %10 = load i32, ptr %_0, align 4
  %11 = getelementptr inbounds i8, ptr %_0, i64 4
  %12 = load i32, ptr %11, align 4
  %13 = insertvalue { i32, i32 } poison, i32 %10, 0
  %14 = insertvalue { i32, i32 } %13, i32 %12, 1
  ret { i32, i32 } %14

bb1:                                              ; No predecessors!
  unreachable
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h1402ddaad8a14803E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_desugared
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17h6902ac6009b99630E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h205ec1c42a5859b4E"(ptr align 8 %self, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_trusted
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted17hbb2edb81c5d48944E"(ptr align 8 %self, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h5f9ed1ab9b79cac5E"(ptr align 8 %self, ptr align 4 %iter, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_desugared
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17hb4502db373e80a83E"(ptr align 8 %self, ptr align 4 %iter, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h66659f6106abc020E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h99972a45ffe39b86E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hd738f588413f9afcE"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h4317ce575662e512E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hf150c607b694f026E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h392b8b5bc9e837f7E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0)
  ret void
}

; _11_advanced_features::main
; Function Attrs: uwtable
define internal void @_ZN21_11_advanced_features4main17h191ecf3e3c08520bE() unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_464 = alloca [48 x i8], align 8
  %_461 = alloca [48 x i8], align 8
  %_458 = alloca [16 x i8], align 8
  %_457 = alloca [16 x i8], align 8
  %_454 = alloca [48 x i8], align 8
  %e = alloca [24 x i8], align 8
  %_450 = alloca [16 x i8], align 8
  %_449 = alloca [16 x i8], align 8
  %_446 = alloca [48 x i8], align 8
  %result16 = alloca [4 x i8], align 4
  %_442 = alloca [24 x i8], align 8
  %_440 = alloca [48 x i8], align 8
  %_437 = alloca [48 x i8], align 8
  %msg = alloca [24 x i8], align 8
  %_431 = alloca [24 x i8], align 8
  %iter15 = alloca [32 x i8], align 8
  %_429 = alloca [32 x i8], align 8
  %_428 = alloca [24 x i8], align 8
  %_427 = alloca [24 x i8], align 8
  %_425 = alloca [24 x i8], align 8
  %_424 = alloca [24 x i8], align 8
  %_423 = alloca [8 x i8], align 8
  %messages = alloca [24 x i8], align 8
  %_415 = alloca [48 x i8], align 8
  %_412 = alloca [48 x i8], align 8
  %_409 = alloca [16 x i8], align 8
  %_408 = alloca [16 x i8], align 8
  %_405 = alloca [48 x i8], align 8
  %_403 = alloca [12 x i8], align 4
  %_402 = alloca [12 x i8], align 4
  %_401 = alloca [12 x i8], align 4
  %result14 = alloca [24 x i8], align 8
  %_398 = alloca [48 x i8], align 8
  %_395 = alloca [48 x i8], align 8
  %_392 = alloca [16 x i8], align 8
  %_391 = alloca [16 x i8], align 8
  %_388 = alloca [48 x i8], align 8
  %sum = alloca [4 x i8], align 4
  %_381 = alloca [16 x i8], align 8
  %_380 = alloca [16 x i8], align 8
  %_377 = alloca [48 x i8], align 8
  %evens = alloca [24 x i8], align 8
  %_368 = alloca [16 x i8], align 8
  %_367 = alloca [16 x i8], align 8
  %_364 = alloca [48 x i8], align 8
  %doubled = alloca [24 x i8], align 8
  %numbers13 = alloca [24 x i8], align 8
  %_350 = alloca [48 x i8], align 8
  %_347 = alloca [48 x i8], align 8
  %_344 = alloca [16 x i8], align 8
  %_343 = alloca [16 x i8], align 8
  %_340 = alloca [48 x i8], align 8
  %n12 = alloca [4 x i8], align 4
  %_336 = alloca [16 x i8], align 8
  %_335 = alloca [16 x i8], align 8
  %_332 = alloca [48 x i8], align 8
  %n11 = alloca [4 x i8], align 4
  %_321 = alloca [16 x i8], align 8
  %_320 = alloca [16 x i8], align 8
  %_317 = alloca [48 x i8], align 8
  %n10 = alloca [4 x i8], align 4
  %_311 = alloca [16 x i8], align 8
  %_310 = alloca [16 x i8], align 8
  %_307 = alloca [48 x i8], align 8
  %n = alloca [4 x i8], align 4
  %number9 = alloca [4 x i8], align 4
  %_299 = alloca [48 x i8], align 8
  %_296 = alloca [48 x i8], align 8
  %_293 = alloca [48 x i8], align 8
  %_290 = alloca [16 x i8], align 8
  %_289 = alloca [16 x i8], align 8
  %_286 = alloca [48 x i8], align 8
  %number = alloca [4 x i8], align 4
  %maybe_number = alloca [8 x i8], align 4
  %_280 = alloca [48 x i8], align 8
  %_277 = alloca [48 x i8], align 8
  %_274 = alloca [48 x i8], align 8
  %_271 = alloca [16 x i8], align 8
  %_270 = alloca [16 x i8], align 8
  %_267 = alloca [48 x i8], align 8
  %value = alloca [4 x i8], align 4
  %_262 = alloca [8 x i8], align 4
  %stack = alloca [24 x i8], align 8
  %_254 = alloca [48 x i8], align 8
  %_251 = alloca [48 x i8], align 8
  %_248 = alloca [16 x i8], align 8
  %_246 = alloca [16 x i8], align 8
  %_245 = alloca [32 x i8], align 8
  %_242 = alloca [48 x i8], align 8
  %_239 = alloca [16 x i8], align 8
  %_238 = alloca [16 x i8], align 8
  %_235 = alloca [48 x i8], align 8
  %j = alloca [4 x i8], align 4
  %_230 = alloca [8 x i8], align 4
  %iter8 = alloca [12 x i8], align 4
  %_228 = alloca [12 x i8], align 4
  %_227 = alloca [12 x i8], align 4
  %_225 = alloca [16 x i8], align 8
  %_224 = alloca [16 x i8], align 8
  %_221 = alloca [48 x i8], align 8
  %i7 = alloca [4 x i8], align 4
  %_216 = alloca [8 x i8], align 4
  %iter6 = alloca [12 x i8], align 4
  %_214 = alloca [12 x i8], align 4
  %_213 = alloca [12 x i8], align 4
  %_211 = alloca [48 x i8], align 8
  %_208 = alloca [48 x i8], align 8
  %_205 = alloca [16 x i8], align 8
  %_204 = alloca [16 x i8], align 8
  %_201 = alloca [48 x i8], align 8
  %_198 = alloca [16 x i8], align 8
  %_197 = alloca [16 x i8], align 8
  %_194 = alloca [48 x i8], align 8
  %_191 = alloca [16 x i8], align 8
  %_190 = alloca [16 x i8], align 8
  %_187 = alloca [48 x i8], align 8
  %i = alloca [4 x i8], align 4
  %_182 = alloca [8 x i8], align 4
  %iter = alloca [12 x i8], align 4
  %_180 = alloca [12 x i8], align 4
  %_179 = alloca [12 x i8], align 4
  %_177 = alloca [48 x i8], align 8
  %_174 = alloca [48 x i8], align 8
  %_171 = alloca [16 x i8], align 8
  %_169 = alloca [16 x i8], align 8
  %_168 = alloca [32 x i8], align 8
  %_165 = alloca [48 x i8], align 8
  %y5 = alloca [4 x i8], align 4
  %x4 = alloca [4 x i8], align 4
  %_160 = alloca [16 x i8], align 8
  %_159 = alloca [16 x i8], align 8
  %_156 = alloca [48 x i8], align 8
  %x = alloca [4 x i8], align 4
  %_152 = alloca [16 x i8], align 8
  %_151 = alloca [16 x i8], align 8
  %_148 = alloca [48 x i8], align 8
  %y = alloca [4 x i8], align 4
  %_144 = alloca [48 x i8], align 8
  %_140 = alloca [48 x i8], align 8
  %_137 = alloca [48 x i8], align 8
  %_134 = alloca [48 x i8], align 8
  %_131 = alloca [16 x i8], align 8
  %_130 = alloca [16 x i8], align 8
  %_127 = alloca [48 x i8], align 8
  %index3 = alloca [8 x i8], align 8
  %_121 = alloca [16 x i8], align 8
  %_119 = alloca [48 x i8], align 8
  %_116 = alloca [16 x i8], align 8
  %_115 = alloca [16 x i8], align 8
  %_112 = alloca [48 x i8], align 8
  %index = alloca [8 x i8], align 8
  %_106 = alloca [16 x i8], align 8
  %numbers = alloca [24 x i8], align 8
  %_98 = alloca [48 x i8], align 8
  %_95 = alloca [48 x i8], align 8
  %_92 = alloca [16 x i8], align 8
  %_91 = alloca [16 x i8], align 8
  %_88 = alloca [48 x i8], align 8
  %error2 = alloca [24 x i8], align 8
  %_84 = alloca [16 x i8], align 8
  %_83 = alloca [16 x i8], align 8
  %_80 = alloca [48 x i8], align 8
  %result1 = alloca [4 x i8], align 4
  %_76 = alloca [24 x i8], align 8
  %_74 = alloca [16 x i8], align 8
  %_73 = alloca [16 x i8], align 8
  %_70 = alloca [48 x i8], align 8
  %error = alloca [24 x i8], align 8
  %_66 = alloca [16 x i8], align 8
  %_65 = alloca [16 x i8], align 8
  %_62 = alloca [48 x i8], align 8
  %result = alloca [4 x i8], align 4
  %_56 = alloca [24 x i8], align 8
  %_54 = alloca [48 x i8], align 8
  %_51 = alloca [48 x i8], align 8
  %_48 = alloca [16 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %_44 = alloca [16 x i8], align 8
  %_43 = alloca [48 x i8], align 8
  %_40 = alloca [48 x i8], align 8
  %_30 = alloca [32 x i8], align 8
  %message = alloca [16 x i8], align 8
  %grade = alloca [16 x i8], align 8
  %score = alloca [4 x i8], align 4
  %_25 = alloca [48 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_19 = alloca [16 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_16 = alloca [32 x i8], align 8
  %_13 = alloca [48 x i8], align 8
  %day_name = alloca [16 x i8], align 8
  %day_number = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_d95b7873c88797b3e97c1df5861c4f6f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_3f2bfd9cdf44226e5fc1d6c09bca0aff)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
  store i32 3, ptr %day_number, align 4
  %0 = load i32, ptr %day_number, align 4
  switch i32 %0, label %bb7 [
    i32 1, label %bb14
    i32 2, label %bb13
    i32 3, label %bb12
    i32 4, label %bb11
    i32 5, label %bb10
    i32 6, label %bb9
    i32 7, label %bb8
  ]

bb7:                                              ; preds = %start
  unreachable

bb14:                                             ; preds = %start
  unreachable

bb13:                                             ; preds = %start
  unreachable

bb12:                                             ; preds = %start
  store ptr @alloc_e3976b92482fb4550470b093759847a6, ptr %day_name, align 8
  %1 = getelementptr inbounds i8, ptr %day_name, i64 8
  store i64 9, ptr %1, align 8
  br label %bb15

bb11:                                             ; preds = %start
  unreachable

bb10:                                             ; preds = %start
  unreachable

bb9:                                              ; preds = %start
  unreachable

bb8:                                              ; preds = %start
  unreachable

bb15:                                             ; preds = %bb12
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_17, ptr align 4 %day_number)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h141786a163beeda1E(ptr sret([16 x i8]) align 8 %_19, ptr align 8 %day_name)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_17, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_19, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h1970af73f20cca4fE(ptr sret([48 x i8]) align 8 %_13, ptr align 8 @alloc_4bd9072c24f5c3baa5448cf37f29924e, ptr align 8 %_16)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_13)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_22, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_22)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_25, ptr align 8 @alloc_d1ddbe8f23419703c94bbe4cd90bf63f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_25)
  store i32 85, ptr %score, align 4
  %4 = load i32, ptr %score, align 4
  %_37 = icmp sle i32 90, %4
  br i1 %_37, label %bb35, label %bb26

bb26:                                             ; preds = %bb35, %bb15
  %5 = load i32, ptr %score, align 4
  %_35 = icmp sle i32 80, %5
  br i1 %_35, label %bb34, label %bb28

bb35:                                             ; preds = %bb15
  %6 = load i32, ptr %score, align 4
  %_38 = icmp sle i32 %6, 100
  br i1 %_38, label %bb25, label %bb26

bb25:                                             ; preds = %bb35
  store ptr @alloc_e2ead6761956d440a2a6c3412b417ffa, ptr %_30, align 8
  %7 = getelementptr inbounds i8, ptr %_30, i64 8
  store i64 1, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %_30, i64 16
  store ptr @alloc_21403cf1f221cae673edc7d8ebdeb7e2, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 22, ptr %9, align 8
  br label %bb36

bb28:                                             ; preds = %bb34, %bb26
  %10 = load i32, ptr %score, align 4
  %_33 = icmp sle i32 70, %10
  br i1 %_33, label %bb33, label %bb30

bb34:                                             ; preds = %bb26
  %11 = load i32, ptr %score, align 4
  %_36 = icmp sle i32 %11, 89
  br i1 %_36, label %bb27, label %bb28

bb27:                                             ; preds = %bb34
  store ptr @alloc_d3bbdebcd7d668a59dc59a90afdc2fa1, ptr %_30, align 8
  %12 = getelementptr inbounds i8, ptr %_30, i64 8
  store i64 1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_30, i64 16
  store ptr @alloc_2b54c328cbbde02db9a83dfa81c4efd8, ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %13, i64 8
  store i64 17, ptr %14, align 8
  br label %bb36

bb30:                                             ; preds = %bb33, %bb28
  %15 = load i32, ptr %score, align 4
  %_31 = icmp sle i32 60, %15
  br i1 %_31, label %bb32, label %bb24

bb33:                                             ; preds = %bb28
  %16 = load i32, ptr %score, align 4
  %_34 = icmp sle i32 %16, 79
  br i1 %_34, label %bb29, label %bb30

bb29:                                             ; preds = %bb33
  store ptr @alloc_e57470275a219d8492d489e56910499e, ptr %_30, align 8
  %17 = getelementptr inbounds i8, ptr %_30, i64 8
  store i64 1, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %_30, i64 16
  store ptr @alloc_c5b51882c377e9c817ac1a6aeb205676, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %18, i64 8
  store i64 19, ptr %19, align 8
  br label %bb36

bb24:                                             ; preds = %bb32, %bb30
  store ptr @alloc_4b372b42a7e59c4e87186c5d2ddb750d, ptr %_30, align 8
  %20 = getelementptr inbounds i8, ptr %_30, i64 8
  store i64 1, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_30, i64 16
  store ptr @alloc_650fee8799ff48e1b6ec6752e85c5de0, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 6, ptr %22, align 8
  br label %bb36

bb32:                                             ; preds = %bb30
  %23 = load i32, ptr %score, align 4
  %_32 = icmp sle i32 %23, 69
  br i1 %_32, label %bb31, label %bb24

bb31:                                             ; preds = %bb32
  store ptr @alloc_fba4efe8e4f7fab8265f1b3a352c9317, ptr %_30, align 8
  %24 = getelementptr inbounds i8, ptr %_30, i64 8
  store i64 1, ptr %24, align 8
  %25 = getelementptr inbounds i8, ptr %_30, i64 16
  store ptr @alloc_0c6ecae49f41ef7e73908e09a56ad40c, ptr %25, align 8
  %26 = getelementptr inbounds i8, ptr %25, i64 8
  store i64 13, ptr %26, align 8
  br label %bb36

bb36:                                             ; preds = %bb25, %bb27, %bb29, %bb31, %bb24
  %27 = load ptr, ptr %_30, align 8
  %28 = getelementptr inbounds i8, ptr %_30, i64 8
  %29 = load i64, ptr %28, align 8
  store ptr %27, ptr %grade, align 8
  %30 = getelementptr inbounds i8, ptr %grade, i64 8
  store i64 %29, ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %_30, i64 16
  %32 = load ptr, ptr %31, align 8
  %33 = getelementptr inbounds i8, ptr %31, i64 8
  %34 = load i64, ptr %33, align 8
  store ptr %32, ptr %message, align 8
  %35 = getelementptr inbounds i8, ptr %message, i64 8
  store i64 %34, ptr %35, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_44, ptr align 4 %score)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h141786a163beeda1E(ptr sret([16 x i8]) align 8 %_46, ptr align 8 %grade)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h141786a163beeda1E(ptr sret([16 x i8]) align 8 %_48, ptr align 8 %message)
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_43, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_44, i64 16, i1 false)
  %37 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_43, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %37, ptr align 8 %_46, i64 16, i1 false)
  %38 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_43, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %38, ptr align 8 %_48, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h8e235922c73bde2dE(ptr sret([48 x i8]) align 8 %_40, ptr align 8 @alloc_c3417ee47490ee12b1af9bd1501db072, ptr align 8 %_43)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_40)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_51, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_51)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_54, ptr align 8 @alloc_6482d515921d1e84bda6c945bdc55428)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_54)
; call _11_advanced_features::safe_divide
  call void @_ZN21_11_advanced_features11safe_divide17h23b2cf84e5049b76E(ptr sret([24 x i8]) align 8 %_56, i32 10, i32 0)
  %39 = load i64, ptr %_56, align 8
  %40 = icmp eq i64 %39, -9223372036854775808
  %_59 = select i1 %40, i64 0, i64 1
  %41 = trunc nuw i64 %_59 to i1
  br i1 %41, label %bb48, label %bb49

bb48:                                             ; preds = %bb36
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %error, ptr align 8 %_56, i64 24, i1 false)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5a3f64c052421b06E(ptr sret([16 x i8]) align 8 %_74, ptr align 8 %error)
          to label %bb52 unwind label %funclet_bb276

bb49:                                             ; preds = %bb36
  %42 = getelementptr inbounds i8, ptr %_56, i64 8
  %43 = load i32, ptr %42, align 8
  store i32 %43, ptr %result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_66, ptr align 4 %result)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_65, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_66, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_62, ptr align 8 @alloc_eb9b4b67a77a57c0a57547dda75d76c5, ptr align 8 %_65)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_62)
  br label %bb278

bb278:                                            ; preds = %bb54, %bb49
; call _11_advanced_features::safe_divide
  call void @_ZN21_11_advanced_features11safe_divide17h23b2cf84e5049b76E(ptr sret([24 x i8]) align 8 %_76, i32 10, i32 2)
  %45 = load i64, ptr %_76, align 8
  %46 = icmp eq i64 %45, -9223372036854775808
  %_77 = select i1 %46, i64 0, i64 1
  %47 = trunc nuw i64 %_77 to i1
  br i1 %47, label %bb56, label %bb57

bb276:                                            ; preds = %funclet_bb276
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %error) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb277

funclet_bb276:                                    ; preds = %bb53, %bb52, %bb48
  %cleanuppad = cleanuppad within none []
  br label %bb276

bb52:                                             ; preds = %bb48
  %48 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_73, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %48, ptr align 8 %_74, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_70, ptr align 8 @alloc_65a14c287e0a73af54985a3db7645c00, ptr align 8 %_73)
          to label %bb53 unwind label %funclet_bb276

bb53:                                             ; preds = %bb52
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_70)
          to label %bb54 unwind label %funclet_bb276

bb54:                                             ; preds = %bb53
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %error)
  br label %bb278

bb56:                                             ; preds = %bb278
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %error2, ptr align 8 %_76, i64 24, i1 false)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5a3f64c052421b06E(ptr sret([16 x i8]) align 8 %_92, ptr align 8 %error2)
          to label %bb60 unwind label %funclet_bb275

bb57:                                             ; preds = %bb278
  %49 = getelementptr inbounds i8, ptr %_76, i64 8
  %50 = load i32, ptr %49, align 8
  store i32 %50, ptr %result1, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_84, ptr align 4 %result1)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_83, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_84, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_80, ptr align 8 @alloc_eb9b4b67a77a57c0a57547dda75d76c5, ptr align 8 %_83)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_80)
  br label %bb279

bb279:                                            ; preds = %bb62, %bb57
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_95, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_95)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_98, ptr align 8 @alloc_5a58cc7a994f10d675cda38042f5f55b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_98)
; call alloc::alloc::exchange_malloc
  %_104 = call ptr @_ZN5alloc5alloc15exchange_malloc17h9069f32671bf8c2aE(i64 20, i64 4)
  %_491 = ptrtoint ptr %_104 to i64
  %_494 = and i64 %_491, 3
  %_495 = icmp eq i64 %_494, 0
  br i1 %_495, label %bb285, label %panic

bb275:                                            ; preds = %funclet_bb275
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %error2) #20 [ "funclet"(token %cleanuppad17) ]
  cleanupret from %cleanuppad17 unwind label %funclet_bb277

funclet_bb275:                                    ; preds = %bb61, %bb60, %bb56
  %cleanuppad17 = cleanuppad within none []
  br label %bb275

bb60:                                             ; preds = %bb56
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_91, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_92, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_88, ptr align 8 @alloc_65a14c287e0a73af54985a3db7645c00, ptr align 8 %_91)
          to label %bb61 unwind label %funclet_bb275

bb61:                                             ; preds = %bb60
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_88)
          to label %bb62 unwind label %funclet_bb275

bb62:                                             ; preds = %bb61
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %error2)
  br label %bb279

bb285:                                            ; preds = %bb279
  %_497 = ptrtoint ptr %_104 to i64
  %_500 = icmp eq i64 %_497, 0
  %_501 = and i1 %_500, true
  %_502 = xor i1 %_501, true
  br i1 %_502, label %bb286, label %panic18

panic:                                            ; preds = %bb279
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_491, ptr align 8 @alloc_cd1c7cc0951dc4db27745c1d87a79cda) #22
  unreachable

bb286:                                            ; preds = %bb285
  %53 = getelementptr inbounds nuw i32, ptr %_104, i64 0
  store i32 1, ptr %53, align 4
  %54 = getelementptr inbounds nuw i32, ptr %_104, i64 1
  store i32 2, ptr %54, align 4
  %55 = getelementptr inbounds nuw i32, ptr %_104, i64 2
  store i32 3, ptr %55, align 4
  %56 = getelementptr inbounds nuw i32, ptr %_104, i64 3
  store i32 4, ptr %56, align 4
  %57 = getelementptr inbounds nuw i32, ptr %_104, i64 4
  store i32 5, ptr %57, align 4
; call alloc::slice::<impl [T]>::into_vec
  call void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17hd1c66c729c6206abE"(ptr sret([24 x i8]) align 8 %numbers, ptr align 4 %_104, i64 5)
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %58 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %numbers)
          to label %bb69 unwind label %funclet_bb274

panic18:                                          ; preds = %bb285
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_cd1c7cc0951dc4db27745c1d87a79cda) #22
  unreachable

bb274:                                            ; preds = %funclet_bb274
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %numbers) #20 [ "funclet"(token %cleanuppad19) ]
  cleanupret from %cleanuppad19 unwind label %funclet_bb277

funclet_bb274:                                    ; preds = %bb122, %bb121, %bb118, %bb273, %bb264, %bb287, %bb153, %bb152, %bb151, %bb150, %bb149, %bb148, %bb147, %bb146, %bb145, %bb142, %bb141, %bb140, %bb138, %bb136, %bb135, %bb134, %bb133, %bb132, %bb130, %bb128, %bb127, %bb126, %bb125, %bb124, %bb123, %bb120, %bb119, %bb117, %bb115, %bb114, %bb113, %bb110, %bb108, %bb107, %bb106, %bb105, %bb104, %bb103, %bb102, %bb101, %bb100, %bb91, %bb99, %bb98, %bb92, %bb97, %bb96, %bb93, %bb95, %bb94, %bb87, %bb86, %bb85, %bb84, %bb82, %bb81, %bb80, %bb83, %bb79, %bb77, %bb76, %bb74, %bb73, %bb72, %bb75, %bb71, %bb69, %bb286
  %cleanuppad19 = cleanuppad within none []
  br label %bb274

bb69:                                             ; preds = %bb286
  %_107.0 = extractvalue { ptr, i64 } %58, 0
  %_107.1 = extractvalue { ptr, i64 } %58, 1
; invoke _11_advanced_features::find_number
  %59 = invoke { i64, i64 } @_ZN21_11_advanced_features11find_number17h4271184ac6c2448eE(ptr align 4 %_107.0, i64 %_107.1, i32 3)
          to label %bb70 unwind label %funclet_bb274

bb70:                                             ; preds = %bb69
  %60 = extractvalue { i64, i64 } %59, 0
  %61 = extractvalue { i64, i64 } %59, 1
  store i64 %60, ptr %_106, align 8
  %62 = getelementptr inbounds i8, ptr %_106, i64 8
  store i64 %61, ptr %62, align 8
  %_109 = load i64, ptr %_106, align 8
  %63 = getelementptr inbounds i8, ptr %_106, i64 8
  %64 = load i64, ptr %63, align 8
  %65 = trunc nuw i64 %_109 to i1
  br i1 %65, label %bb72, label %bb71

bb72:                                             ; preds = %bb70
  %66 = getelementptr inbounds i8, ptr %_106, i64 8
  %67 = load i64, ptr %66, align 8
  store i64 %67, ptr %index, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hb5ea6702297b0929E(ptr sret([16 x i8]) align 8 %_116, ptr align 8 %index)
          to label %bb73 unwind label %funclet_bb274

bb71:                                             ; preds = %bb70
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_119, ptr align 8 @alloc_687bd6763d5d28b5ad99923878bb93ea)
          to label %bb75 unwind label %funclet_bb274

bb75:                                             ; preds = %bb71
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_119)
          to label %bb291 unwind label %funclet_bb274

bb291:                                            ; preds = %bb75
  br label %bb76

bb76:                                             ; preds = %bb290, %bb291
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %68 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %numbers)
          to label %bb77 unwind label %funclet_bb274

bb73:                                             ; preds = %bb72
  %69 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_115, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %69, ptr align 8 %_116, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_112, ptr align 8 @alloc_dee56960b710635c6626f85dfda544d2, ptr align 8 %_115)
          to label %bb74 unwind label %funclet_bb274

bb74:                                             ; preds = %bb73
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_112)
          to label %bb290 unwind label %funclet_bb274

bb290:                                            ; preds = %bb74
  br label %bb76

bb77:                                             ; preds = %bb76
  %_122.0 = extractvalue { ptr, i64 } %68, 0
  %_122.1 = extractvalue { ptr, i64 } %68, 1
; invoke _11_advanced_features::find_number
  %70 = invoke { i64, i64 } @_ZN21_11_advanced_features11find_number17h4271184ac6c2448eE(ptr align 4 %_122.0, i64 %_122.1, i32 10)
          to label %bb78 unwind label %funclet_bb274

bb78:                                             ; preds = %bb77
  %71 = extractvalue { i64, i64 } %70, 0
  %72 = extractvalue { i64, i64 } %70, 1
  store i64 %71, ptr %_121, align 8
  %73 = getelementptr inbounds i8, ptr %_121, i64 8
  store i64 %72, ptr %73, align 8
  %_124 = load i64, ptr %_121, align 8
  %74 = getelementptr inbounds i8, ptr %_121, i64 8
  %75 = load i64, ptr %74, align 8
  %76 = trunc nuw i64 %_124 to i1
  br i1 %76, label %bb80, label %bb79

bb80:                                             ; preds = %bb78
  %77 = getelementptr inbounds i8, ptr %_121, i64 8
  %78 = load i64, ptr %77, align 8
  store i64 %78, ptr %index3, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hb5ea6702297b0929E(ptr sret([16 x i8]) align 8 %_131, ptr align 8 %index3)
          to label %bb81 unwind label %funclet_bb274

bb79:                                             ; preds = %bb78
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_134, ptr align 8 @alloc_cb0bb4cb60c5206550c227253c866203)
          to label %bb83 unwind label %funclet_bb274

bb83:                                             ; preds = %bb79
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_134)
          to label %bb293 unwind label %funclet_bb274

bb293:                                            ; preds = %bb83
  br label %bb84

bb84:                                             ; preds = %bb292, %bb293
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_137, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb85 unwind label %funclet_bb274

bb81:                                             ; preds = %bb80
  %79 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_130, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %79, ptr align 8 %_131, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_127, ptr align 8 @alloc_e53fac3b6c03b3faca068335dee1e35f, ptr align 8 %_130)
          to label %bb82 unwind label %funclet_bb274

bb82:                                             ; preds = %bb81
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_127)
          to label %bb292 unwind label %funclet_bb274

bb292:                                            ; preds = %bb82
  br label %bb84

bb85:                                             ; preds = %bb84
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_137)
          to label %bb86 unwind label %funclet_bb274

bb86:                                             ; preds = %bb85
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_140, ptr align 8 @alloc_379906d5c2e22abd715edca2750793f1)
          to label %bb87 unwind label %funclet_bb274

bb87:                                             ; preds = %bb86
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_140)
          to label %bb88 unwind label %funclet_bb274

bb88:                                             ; preds = %bb87
  br label %bb89

bb89:                                             ; preds = %bb88
  br label %bb91

bb90:                                             ; No predecessors!
  br label %bb93

bb93:                                             ; preds = %bb90
  store i32 4, ptr %y, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_152, ptr align 4 %y)
          to label %bb96 unwind label %funclet_bb274

bb94:                                             ; No predecessors!
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_144, ptr align 8 @alloc_c2ab13edf99533dc761b1d39748dc01a)
          to label %bb95 unwind label %funclet_bb274

bb95:                                             ; preds = %bb94
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_144)
          to label %bb294 unwind label %funclet_bb274

bb294:                                            ; preds = %bb95
  br label %bb103

bb103:                                            ; preds = %bb297, %bb296, %bb295, %bb294
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_174, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb104 unwind label %funclet_bb274

bb96:                                             ; preds = %bb93
  %80 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_151, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %80, ptr align 8 %_152, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_148, ptr align 8 @alloc_dbe3a2b4bebe9869fa2f95b0be39135e, ptr align 8 %_151)
          to label %bb97 unwind label %funclet_bb274

bb97:                                             ; preds = %bb96
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_148)
          to label %bb295 unwind label %funclet_bb274

bb295:                                            ; preds = %bb97
  br label %bb103

bb91:                                             ; preds = %bb89
  store i32 3, ptr %x4, align 4
  store i32 4, ptr %y5, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_169, ptr align 4 %x4)
          to label %bb100 unwind label %funclet_bb274

bb92:                                             ; No predecessors!
  store i32 3, ptr %x, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_160, ptr align 4 %x)
          to label %bb98 unwind label %funclet_bb274

bb98:                                             ; preds = %bb92
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_159, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_160, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_156, ptr align 8 @alloc_ce07cd6fb79bc33e157cd9eec2db1a29, ptr align 8 %_159)
          to label %bb99 unwind label %funclet_bb274

bb99:                                             ; preds = %bb98
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_156)
          to label %bb296 unwind label %funclet_bb274

bb296:                                            ; preds = %bb99
  br label %bb103

bb100:                                            ; preds = %bb91
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_171, ptr align 4 %y5)
          to label %bb101 unwind label %funclet_bb274

bb101:                                            ; preds = %bb100
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_168, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_169, i64 16, i1 false)
  %83 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_168, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %83, ptr align 8 %_171, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h1970af73f20cca4fE(ptr sret([48 x i8]) align 8 %_165, ptr align 8 @alloc_7a78c9132da20336fbc72ada35ade4bd, ptr align 8 %_168)
          to label %bb102 unwind label %funclet_bb274

bb102:                                            ; preds = %bb101
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_165)
          to label %bb297 unwind label %funclet_bb274

bb297:                                            ; preds = %bb102
  br label %bb103

bb104:                                            ; preds = %bb103
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_174)
          to label %bb105 unwind label %funclet_bb274

bb105:                                            ; preds = %bb104
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_177, ptr align 8 @alloc_f941f3dcc13ef649ca6902d97bfb92c1)
          to label %bb106 unwind label %funclet_bb274

bb106:                                            ; preds = %bb105
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_177)
          to label %bb107 unwind label %funclet_bb274

bb107:                                            ; preds = %bb106
; invoke core::ops::range::RangeInclusive<Idx>::new
  invoke void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf35903eb3007664eE"(ptr sret([12 x i8]) align 4 %_180, i32 1, i32 10)
          to label %bb108 unwind label %funclet_bb274

bb108:                                            ; preds = %bb107
; invoke <I as core::iter::traits::collect::IntoIterator>::into_iter
  invoke void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf6c97b35307792afE"(ptr sret([12 x i8]) align 4 %_179, ptr align 4 %_180)
          to label %bb109 unwind label %funclet_bb274

bb109:                                            ; preds = %bb108
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter, ptr align 4 %_179, i64 12, i1 false)
  br label %bb110

bb110:                                            ; preds = %bb300, %bb298, %bb109
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %84 = invoke { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h929783076e4a423aE"(ptr align 4 %iter)
          to label %bb111 unwind label %funclet_bb274

bb111:                                            ; preds = %bb110
  %85 = extractvalue { i32, i32 } %84, 0
  %86 = extractvalue { i32, i32 } %84, 1
  store i32 %85, ptr %_182, align 4
  %87 = getelementptr inbounds i8, ptr %_182, i64 4
  store i32 %86, ptr %87, align 4
  %88 = load i32, ptr %_182, align 4
  %89 = getelementptr inbounds i8, ptr %_182, i64 4
  %90 = load i32, ptr %89, align 4
  %_184 = zext i32 %88 to i64
  %91 = trunc nuw i64 %_184 to i1
  br i1 %91, label %bb112, label %bb123

bb112:                                            ; preds = %bb111
  %92 = getelementptr inbounds i8, ptr %_182, i64 4
  %93 = load i32, ptr %92, align 4
  store i32 %93, ptr %i, align 4
  %94 = load i32, ptr %i, align 4
  %95 = icmp eq i32 %94, 3
  br i1 %95, label %bb113, label %bb116

bb123:                                            ; preds = %bb299, %bb111
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_208, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb124 unwind label %funclet_bb274

bb113:                                            ; preds = %bb112
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_191, ptr align 4 %i)
          to label %bb114 unwind label %funclet_bb274

bb116:                                            ; preds = %bb112
  %96 = load i32, ptr %i, align 4
  %97 = icmp eq i32 %96, 7
  br i1 %97, label %bb117, label %bb118

bb114:                                            ; preds = %bb113
  %98 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_190, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %98, ptr align 8 %_191, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_187, ptr align 8 @alloc_711012591040f0d9a9f8c5bc1491a916, ptr align 8 %_190)
          to label %bb115 unwind label %funclet_bb274

bb115:                                            ; preds = %bb114
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_187)
          to label %bb298 unwind label %funclet_bb274

bb298:                                            ; preds = %bb115
  br label %bb110

bb117:                                            ; preds = %bb116
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_198, ptr align 4 %i)
          to label %bb119 unwind label %funclet_bb274

bb118:                                            ; preds = %bb116
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_205, ptr align 4 %i)
          to label %bb121 unwind label %funclet_bb274

bb119:                                            ; preds = %bb117
  %99 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_197, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %99, ptr align 8 %_198, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_194, ptr align 8 @alloc_8861b35c002520e58db630b9eb4ac7c8, ptr align 8 %_197)
          to label %bb120 unwind label %funclet_bb274

bb120:                                            ; preds = %bb119
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_194)
          to label %bb299 unwind label %funclet_bb274

bb299:                                            ; preds = %bb120
  br label %bb123

bb124:                                            ; preds = %bb123
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_208)
          to label %bb125 unwind label %funclet_bb274

bb125:                                            ; preds = %bb124
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_211, ptr align 8 @alloc_d1b4bf154dd7ebfb23a44891ca0b64fb)
          to label %bb126 unwind label %funclet_bb274

bb126:                                            ; preds = %bb125
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_211)
          to label %bb127 unwind label %funclet_bb274

bb127:                                            ; preds = %bb126
; invoke core::ops::range::RangeInclusive<Idx>::new
  invoke void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf35903eb3007664eE"(ptr sret([12 x i8]) align 4 %_214, i32 1, i32 3)
          to label %bb128 unwind label %funclet_bb274

bb128:                                            ; preds = %bb127
; invoke <I as core::iter::traits::collect::IntoIterator>::into_iter
  invoke void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf6c97b35307792afE"(ptr sret([12 x i8]) align 4 %_213, ptr align 4 %_214)
          to label %bb129 unwind label %funclet_bb274

bb129:                                            ; preds = %bb128
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter6, ptr align 4 %_213, i64 12, i1 false)
  br label %bb130

bb130:                                            ; preds = %bb139, %bb129
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %100 = invoke { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h929783076e4a423aE"(ptr align 4 %iter6)
          to label %bb131 unwind label %funclet_bb274

bb131:                                            ; preds = %bb130
  %101 = extractvalue { i32, i32 } %100, 0
  %102 = extractvalue { i32, i32 } %100, 1
  store i32 %101, ptr %_216, align 4
  %103 = getelementptr inbounds i8, ptr %_216, i64 4
  store i32 %102, ptr %103, align 4
  %104 = load i32, ptr %_216, align 4
  %105 = getelementptr inbounds i8, ptr %_216, i64 4
  %106 = load i32, ptr %105, align 4
  %_218 = zext i32 %104 to i64
  %107 = trunc nuw i64 %_218 to i1
  br i1 %107, label %bb132, label %bb149

bb132:                                            ; preds = %bb131
  %108 = getelementptr inbounds i8, ptr %_216, i64 4
  %109 = load i32, ptr %108, align 4
  store i32 %109, ptr %i7, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_225, ptr align 4 %i7)
          to label %bb133 unwind label %funclet_bb274

bb149:                                            ; preds = %bb301, %bb131
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_251, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb150 unwind label %funclet_bb274

bb133:                                            ; preds = %bb132
  %110 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_224, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %110, ptr align 8 %_225, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_221, ptr align 8 @alloc_d1cf28f64ece7698e78650aecdbea28a, ptr align 8 %_224)
          to label %bb134 unwind label %funclet_bb274

bb134:                                            ; preds = %bb133
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_221)
          to label %bb135 unwind label %funclet_bb274

bb135:                                            ; preds = %bb134
; invoke core::ops::range::RangeInclusive<Idx>::new
  invoke void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf35903eb3007664eE"(ptr sret([12 x i8]) align 4 %_228, i32 1, i32 3)
          to label %bb136 unwind label %funclet_bb274

bb136:                                            ; preds = %bb135
; invoke <I as core::iter::traits::collect::IntoIterator>::into_iter
  invoke void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf6c97b35307792afE"(ptr sret([12 x i8]) align 4 %_227, ptr align 4 %_228)
          to label %bb137 unwind label %funclet_bb274

bb137:                                            ; preds = %bb136
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter8, ptr align 4 %_227, i64 12, i1 false)
  br label %bb138

bb138:                                            ; preds = %bb144, %bb143, %bb137
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %111 = invoke { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h929783076e4a423aE"(ptr align 4 %iter8)
          to label %bb139 unwind label %funclet_bb274

bb139:                                            ; preds = %bb138
  %112 = extractvalue { i32, i32 } %111, 0
  %113 = extractvalue { i32, i32 } %111, 1
  store i32 %112, ptr %_230, align 4
  %114 = getelementptr inbounds i8, ptr %_230, i64 4
  store i32 %113, ptr %114, align 4
  %115 = load i32, ptr %_230, align 4
  %116 = getelementptr inbounds i8, ptr %_230, i64 4
  %117 = load i32, ptr %116, align 4
  %_232 = zext i32 %115 to i64
  %118 = trunc nuw i64 %_232 to i1
  br i1 %118, label %bb140, label %bb130

bb140:                                            ; preds = %bb139
  %119 = getelementptr inbounds i8, ptr %_230, i64 4
  %120 = load i32, ptr %119, align 4
  store i32 %120, ptr %j, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_239, ptr align 4 %j)
          to label %bb141 unwind label %funclet_bb274

bb141:                                            ; preds = %bb140
  %121 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_238, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %121, ptr align 8 %_239, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_235, ptr align 8 @alloc_2a819f3db43106d9cd1ca4fd95f3e28c, ptr align 8 %_238)
          to label %bb142 unwind label %funclet_bb274

bb142:                                            ; preds = %bb141
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_235)
          to label %bb143 unwind label %funclet_bb274

bb143:                                            ; preds = %bb142
  %122 = load i32, ptr %i7, align 4
  %123 = icmp eq i32 %122, 2
  br i1 %123, label %bb144, label %bb138

bb144:                                            ; preds = %bb143
  %124 = load i32, ptr %j, align 4
  %125 = icmp eq i32 %124, 2
  br i1 %125, label %bb145, label %bb138

bb145:                                            ; preds = %bb144
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_246, ptr align 4 %i7)
          to label %bb146 unwind label %funclet_bb274

bb146:                                            ; preds = %bb145
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_248, ptr align 4 %j)
          to label %bb147 unwind label %funclet_bb274

bb147:                                            ; preds = %bb146
  %126 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_245, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %126, ptr align 8 %_246, i64 16, i1 false)
  %127 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_245, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %127, ptr align 8 %_248, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h1970af73f20cca4fE(ptr sret([48 x i8]) align 8 %_242, ptr align 8 @alloc_640a9d31705ea67aa71665ec0f6ad3e5, ptr align 8 %_245)
          to label %bb148 unwind label %funclet_bb274

bb148:                                            ; preds = %bb147
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_242)
          to label %bb301 unwind label %funclet_bb274

bb301:                                            ; preds = %bb148
  br label %bb149

bb150:                                            ; preds = %bb149
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_251)
          to label %bb151 unwind label %funclet_bb274

bb151:                                            ; preds = %bb150
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_254, ptr align 8 @alloc_5b9410d9839f8d1653e008ff2b4922db)
          to label %bb152 unwind label %funclet_bb274

bb152:                                            ; preds = %bb151
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_254)
          to label %bb153 unwind label %funclet_bb274

bb153:                                            ; preds = %bb152
; invoke alloc::alloc::exchange_malloc
  %_260 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h9069f32671bf8c2aE(i64 20, i64 4)
          to label %bb154 unwind label %funclet_bb274

bb154:                                            ; preds = %bb153
  %_485 = ptrtoint ptr %_260 to i64
  %_488 = and i64 %_485, 3
  %_489 = icmp eq i64 %_488, 0
  br i1 %_489, label %bb284, label %panic20

bb284:                                            ; preds = %bb154
  %_504 = ptrtoint ptr %_260 to i64
  %_507 = icmp eq i64 %_504, 0
  %_508 = and i1 %_507, true
  %_509 = xor i1 %_508, true
  br i1 %_509, label %bb287, label %panic21

panic20:                                          ; preds = %bb154
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_485, ptr align 8 @alloc_6850c1e8870e71e2873e74cec4a4cd10) #22
  unreachable

bb287:                                            ; preds = %bb284
  %128 = getelementptr inbounds nuw i32, ptr %_260, i64 0
  store i32 1, ptr %128, align 4
  %129 = getelementptr inbounds nuw i32, ptr %_260, i64 1
  store i32 2, ptr %129, align 4
  %130 = getelementptr inbounds nuw i32, ptr %_260, i64 2
  store i32 3, ptr %130, align 4
  %131 = getelementptr inbounds nuw i32, ptr %_260, i64 3
  store i32 4, ptr %131, align 4
  %132 = getelementptr inbounds nuw i32, ptr %_260, i64 4
  store i32 5, ptr %132, align 4
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17hd1c66c729c6206abE"(ptr sret([24 x i8]) align 8 %stack, ptr align 4 %_260, i64 5)
          to label %bb312 unwind label %funclet_bb274

panic21:                                          ; preds = %bb284
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_6850c1e8870e71e2873e74cec4a4cd10) #22
  unreachable

bb312:                                            ; preds = %bb287
  br label %bb155

bb155:                                            ; preds = %bb302, %bb312
; invoke alloc::vec::Vec<T,A>::pop
  %133 = invoke { i32, i32 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3pop17he3b5092ba99e3d44E"(ptr align 8 %stack)
          to label %bb156 unwind label %funclet_bb273

bb273:                                            ; preds = %funclet_bb273
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %stack) #20 [ "funclet"(token %cleanuppad22) ]
  cleanupret from %cleanuppad22 unwind label %funclet_bb274

funclet_bb273:                                    ; preds = %bb272, %bb263, %bb288, %bb197, %bb196, %bb195, %bb194, %bb193, %bb180, %bb179, %bb177, %bb192, %bb191, %bb188, %bb190, %bb189, %bb187, %panic23, %bb184, %bb183, %bb181, %bb175, %bb174, %bb173, %bb172, %bb171, %bb170, %bb169, %bb168, %bb167, %bb165, %bb164, %bb163, %bb162, %bb161, %bb160, %bb159, %bb158, %bb157, %bb155
  %cleanuppad22 = cleanuppad within none []
  br label %bb273

bb156:                                            ; preds = %bb155
  %134 = extractvalue { i32, i32 } %133, 0
  %135 = extractvalue { i32, i32 } %133, 1
  store i32 %134, ptr %_262, align 4
  %136 = getelementptr inbounds i8, ptr %_262, i64 4
  store i32 %135, ptr %136, align 4
  %137 = load i32, ptr %_262, align 4
  %138 = getelementptr inbounds i8, ptr %_262, i64 4
  %139 = load i32, ptr %138, align 4
  %_264 = zext i32 %137 to i64
  %140 = trunc nuw i64 %_264 to i1
  br i1 %140, label %bb157, label %bb160

bb157:                                            ; preds = %bb156
  %141 = getelementptr inbounds i8, ptr %_262, i64 4
  %142 = load i32, ptr %141, align 4
  store i32 %142, ptr %value, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_271, ptr align 4 %value)
          to label %bb158 unwind label %funclet_bb273

bb160:                                            ; preds = %bb156
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_274, ptr align 8 @alloc_4ab3b78434807013697baab831df5cbd)
          to label %bb161 unwind label %funclet_bb273

bb158:                                            ; preds = %bb157
  %143 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_270, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %143, ptr align 8 %_271, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_267, ptr align 8 @alloc_5a5a9d2b50583c48057f5cf335935995, ptr align 8 %_270)
          to label %bb159 unwind label %funclet_bb273

bb159:                                            ; preds = %bb158
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_267)
          to label %bb302 unwind label %funclet_bb273

bb302:                                            ; preds = %bb159
  br label %bb155

bb161:                                            ; preds = %bb160
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_274)
          to label %bb162 unwind label %funclet_bb273

bb162:                                            ; preds = %bb161
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_277, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb163 unwind label %funclet_bb273

bb163:                                            ; preds = %bb162
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_277)
          to label %bb164 unwind label %funclet_bb273

bb164:                                            ; preds = %bb163
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_280, ptr align 8 @alloc_194efbb27be06150e6ed97df92b6f9cc)
          to label %bb165 unwind label %funclet_bb273

bb165:                                            ; preds = %bb164
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_280)
          to label %bb166 unwind label %funclet_bb273

bb166:                                            ; preds = %bb165
  %144 = getelementptr inbounds i8, ptr %maybe_number, i64 4
  store i32 42, ptr %144, align 4
  store i32 1, ptr %maybe_number, align 4
  %145 = load i32, ptr %maybe_number, align 4
  %146 = getelementptr inbounds i8, ptr %maybe_number, i64 4
  %147 = load i32, ptr %146, align 4
  %_283 = zext i32 %145 to i64
  %148 = trunc nuw i64 %_283 to i1
  br i1 %148, label %bb167, label %bb170

bb167:                                            ; preds = %bb166
  %149 = getelementptr inbounds i8, ptr %maybe_number, i64 4
  %150 = load i32, ptr %149, align 4
  store i32 %150, ptr %number, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_290, ptr align 4 %number)
          to label %bb168 unwind label %funclet_bb273

bb170:                                            ; preds = %bb166
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_293, ptr align 8 @alloc_c69caa39a8fdbfb4b4197c3379dcc10f)
          to label %bb171 unwind label %funclet_bb273

bb168:                                            ; preds = %bb167
  %151 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_289, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %151, ptr align 8 %_290, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_286, ptr align 8 @alloc_46ad37ebb913a091a0556d156eff58dd, ptr align 8 %_289)
          to label %bb169 unwind label %funclet_bb273

bb169:                                            ; preds = %bb168
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_286)
          to label %bb303 unwind label %funclet_bb273

bb303:                                            ; preds = %bb169
  br label %bb172

bb172:                                            ; preds = %bb304, %bb303
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_296, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb173 unwind label %funclet_bb273

bb171:                                            ; preds = %bb170
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_293)
          to label %bb304 unwind label %funclet_bb273

bb304:                                            ; preds = %bb171
  br label %bb172

bb173:                                            ; preds = %bb172
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_296)
          to label %bb174 unwind label %funclet_bb273

bb174:                                            ; preds = %bb173
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_299, ptr align 8 @alloc_e9396b0f73ad0f184290b114db1dfaa8)
          to label %bb175 unwind label %funclet_bb273

bb175:                                            ; preds = %bb174
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_299)
          to label %bb176 unwind label %funclet_bb273

bb176:                                            ; preds = %bb175
  store i32 15, ptr %number9, align 4
  %_305 = load i32, ptr %number9, align 4
  %_304 = icmp slt i32 %_305, 0
  br i1 %_304, label %bb177, label %bb178

bb178:                                            ; preds = %bb176
  %_315 = load i32, ptr %number9, align 4
  %152 = icmp eq i32 %_315, 0
  br i1 %152, label %bb181, label %bb182

bb177:                                            ; preds = %bb176
  %153 = load i32, ptr %number9, align 4
  store i32 %153, ptr %n, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_311, ptr align 4 %n)
          to label %bb179 unwind label %funclet_bb273

bb181:                                            ; preds = %bb178
  %154 = load i32, ptr %number9, align 4
  store i32 %154, ptr %n10, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_321, ptr align 4 %n10)
          to label %bb183 unwind label %funclet_bb273

bb182:                                            ; preds = %bb178
  %_326 = load i32, ptr %number9, align 4
  br label %bb185

bb183:                                            ; preds = %bb181
  %155 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_320, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %155, ptr align 8 %_321, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_317, ptr align 8 @alloc_c5427c6923764723eba9feff02f43a1d, ptr align 8 %_320)
          to label %bb184 unwind label %funclet_bb273

bb184:                                            ; preds = %bb183
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_317)
          to label %bb306 unwind label %funclet_bb273

bb306:                                            ; preds = %bb184
  br label %bb193

bb193:                                            ; preds = %bb305, %bb308, %bb307, %bb306
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_347, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb194 unwind label %funclet_bb273

bb185:                                            ; preds = %bb182
  %_329 = icmp eq i32 %_326, -2147483648
  %_330 = and i1 false, %_329
  br i1 %_330, label %panic23, label %bb186

bb186:                                            ; preds = %bb185
  %_325 = srem i32 %_326, 2
  %156 = icmp eq i32 %_325, 0
  br i1 %156, label %bb187, label %bb188

panic23:                                          ; preds = %bb185
; invoke core::panicking::panic_const::panic_const_rem_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_e76b46e85ccffea9e99912ede5615430) #19
          to label %unreachable unwind label %funclet_bb273

unreachable:                                      ; preds = %panic23
  unreachable

bb187:                                            ; preds = %bb186
  %157 = load i32, ptr %number9, align 4
  store i32 %157, ptr %n11, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_336, ptr align 4 %n11)
          to label %bb189 unwind label %funclet_bb273

bb188:                                            ; preds = %bb186
  %158 = load i32, ptr %number9, align 4
  store i32 %158, ptr %n12, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_344, ptr align 4 %n12)
          to label %bb191 unwind label %funclet_bb273

bb189:                                            ; preds = %bb187
  %159 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_335, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %159, ptr align 8 %_336, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_332, ptr align 8 @alloc_801fe18f9b3b363cf2faf6e860ca6b93, ptr align 8 %_335)
          to label %bb190 unwind label %funclet_bb273

bb190:                                            ; preds = %bb189
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_332)
          to label %bb307 unwind label %funclet_bb273

bb307:                                            ; preds = %bb190
  br label %bb193

bb191:                                            ; preds = %bb188
  %160 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_343, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %160, ptr align 8 %_344, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_340, ptr align 8 @alloc_46a79ce96611ad4b486a2da732e191e1, ptr align 8 %_343)
          to label %bb192 unwind label %funclet_bb273

bb192:                                            ; preds = %bb191
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_340)
          to label %bb308 unwind label %funclet_bb273

bb308:                                            ; preds = %bb192
  br label %bb193

bb179:                                            ; preds = %bb177
  %161 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_310, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %161, ptr align 8 %_311, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_307, ptr align 8 @alloc_8973458a6bea5a3ac0edc9fc59624c0d, ptr align 8 %_310)
          to label %bb180 unwind label %funclet_bb273

bb180:                                            ; preds = %bb179
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_307)
          to label %bb305 unwind label %funclet_bb273

bb305:                                            ; preds = %bb180
  br label %bb193

bb194:                                            ; preds = %bb193
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_347)
          to label %bb195 unwind label %funclet_bb273

bb195:                                            ; preds = %bb194
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_350, ptr align 8 @alloc_1e049b8719450bb928bf5c7092963580)
          to label %bb196 unwind label %funclet_bb273

bb196:                                            ; preds = %bb195
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_350)
          to label %bb197 unwind label %funclet_bb273

bb197:                                            ; preds = %bb196
; invoke alloc::alloc::exchange_malloc
  %_356 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h9069f32671bf8c2aE(i64 20, i64 4)
          to label %bb198 unwind label %funclet_bb273

bb198:                                            ; preds = %bb197
  %_479 = ptrtoint ptr %_356 to i64
  %_482 = and i64 %_479, 3
  %_483 = icmp eq i64 %_482, 0
  br i1 %_483, label %bb283, label %panic24

bb283:                                            ; preds = %bb198
  %_511 = ptrtoint ptr %_356 to i64
  %_514 = icmp eq i64 %_511, 0
  %_515 = and i1 %_514, true
  %_516 = xor i1 %_515, true
  br i1 %_516, label %bb288, label %panic25

panic24:                                          ; preds = %bb198
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_479, ptr align 8 @alloc_7300349b7be2d95ed15127d25d40823e) #22
  unreachable

bb288:                                            ; preds = %bb283
  %162 = getelementptr inbounds nuw i32, ptr %_356, i64 0
  store i32 1, ptr %162, align 4
  %163 = getelementptr inbounds nuw i32, ptr %_356, i64 1
  store i32 2, ptr %163, align 4
  %164 = getelementptr inbounds nuw i32, ptr %_356, i64 2
  store i32 3, ptr %164, align 4
  %165 = getelementptr inbounds nuw i32, ptr %_356, i64 3
  store i32 4, ptr %165, align 4
  %166 = getelementptr inbounds nuw i32, ptr %_356, i64 4
  store i32 5, ptr %166, align 4
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17hd1c66c729c6206abE"(ptr sret([24 x i8]) align 8 %numbers13, ptr align 4 %_356, i64 5)
          to label %bb199 unwind label %funclet_bb273

panic25:                                          ; preds = %bb283
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_7300349b7be2d95ed15127d25d40823e) #22
  unreachable

bb199:                                            ; preds = %bb288
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %167 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %numbers13)
          to label %bb200 unwind label %funclet_bb272

bb272:                                            ; preds = %funclet_bb272
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %numbers13) #20 [ "funclet"(token %cleanuppad26) ]
  cleanupret from %cleanuppad26 unwind label %funclet_bb273

funclet_bb272:                                    ; preds = %bb271, %bb262, %bb202, %bb201, %bb200, %bb199
  %cleanuppad26 = cleanuppad within none []
  br label %bb272

bb200:                                            ; preds = %bb199
  %_361.0 = extractvalue { ptr, i64 } %167, 0
  %_361.1 = extractvalue { ptr, i64 } %167, 1
; invoke core::slice::<impl [T]>::iter
  %168 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9daa58a545a75da3E"(ptr align 4 %_361.0, i64 %_361.1)
          to label %bb201 unwind label %funclet_bb272

bb201:                                            ; preds = %bb200
  %_360.0 = extractvalue { ptr, ptr } %168, 0
  %_360.1 = extractvalue { ptr, ptr } %168, 1
; invoke core::iter::traits::iterator::Iterator::map
  %169 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator3map17h2504ac3a1e07fb00E(ptr %_360.0, ptr %_360.1)
          to label %bb202 unwind label %funclet_bb272

bb202:                                            ; preds = %bb201
  %_359.0 = extractvalue { ptr, ptr } %169, 0
  %_359.1 = extractvalue { ptr, ptr } %169, 1
; invoke core::iter::traits::iterator::Iterator::collect
  invoke void @_ZN4core4iter6traits8iterator8Iterator7collect17h3bae0b90aaaec23eE(ptr sret([24 x i8]) align 8 %doubled, ptr %_359.0, ptr %_359.1)
          to label %bb203 unwind label %funclet_bb272

bb203:                                            ; preds = %bb202
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17he8a22f98dffab20dE(ptr sret([16 x i8]) align 8 %_368, ptr align 8 %doubled)
          to label %bb204 unwind label %funclet_bb271

bb271:                                            ; preds = %funclet_bb271
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %doubled) #20 [ "funclet"(token %cleanuppad27) ]
  cleanupret from %cleanuppad27 unwind label %funclet_bb272

funclet_bb271:                                    ; preds = %bb270, %bb261, %bb210, %bb209, %bb208, %bb207, %bb206, %bb205, %bb204, %bb203
  %cleanuppad27 = cleanuppad within none []
  br label %bb271

bb204:                                            ; preds = %bb203
  %170 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_367, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %170, ptr align 8 %_368, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_364, ptr align 8 @alloc_c8ba73d273c0b252f5b4147bdfdc4a8a, ptr align 8 %_367)
          to label %bb205 unwind label %funclet_bb271

bb205:                                            ; preds = %bb204
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_364)
          to label %bb206 unwind label %funclet_bb271

bb206:                                            ; preds = %bb205
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %171 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %numbers13)
          to label %bb207 unwind label %funclet_bb271

bb207:                                            ; preds = %bb206
  %_374.0 = extractvalue { ptr, i64 } %171, 0
  %_374.1 = extractvalue { ptr, i64 } %171, 1
; invoke core::slice::<impl [T]>::iter
  %172 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9daa58a545a75da3E"(ptr align 4 %_374.0, i64 %_374.1)
          to label %bb208 unwind label %funclet_bb271

bb208:                                            ; preds = %bb207
  %_373.0 = extractvalue { ptr, ptr } %172, 0
  %_373.1 = extractvalue { ptr, ptr } %172, 1
; invoke core::iter::traits::iterator::Iterator::filter
  %173 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6filter17h321997cfcd534336E(ptr %_373.0, ptr %_373.1)
          to label %bb209 unwind label %funclet_bb271

bb209:                                            ; preds = %bb208
  %_372.0 = extractvalue { ptr, ptr } %173, 0
  %_372.1 = extractvalue { ptr, ptr } %173, 1
; invoke core::iter::traits::iterator::Iterator::cloned
  %174 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17hb6256425c7c73f1eE(ptr %_372.0, ptr %_372.1)
          to label %bb210 unwind label %funclet_bb271

bb210:                                            ; preds = %bb209
  %_371.0 = extractvalue { ptr, ptr } %174, 0
  %_371.1 = extractvalue { ptr, ptr } %174, 1
; invoke core::iter::traits::iterator::Iterator::collect
  invoke void @_ZN4core4iter6traits8iterator8Iterator7collect17had483c023c37a8e7E(ptr sret([24 x i8]) align 8 %evens, ptr %_371.0, ptr %_371.1)
          to label %bb211 unwind label %funclet_bb271

bb211:                                            ; preds = %bb210
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17he8a22f98dffab20dE(ptr sret([16 x i8]) align 8 %_381, ptr align 8 %evens)
          to label %bb212 unwind label %funclet_bb270

bb270:                                            ; preds = %funclet_bb270
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %evens) #20 [ "funclet"(token %cleanuppad28) ]
  cleanupret from %cleanuppad28 unwind label %funclet_bb271

funclet_bb270:                                    ; preds = %bb269, %bb260, %bb227, %bb226, %bb225, %bb224, %bb223, %bb222, %bb221, %bb220, %bb219, %bb218, %bb217, %bb216, %bb215, %bb214, %bb213, %bb212, %bb211
  %cleanuppad28 = cleanuppad within none []
  br label %bb270

bb212:                                            ; preds = %bb211
  %175 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_380, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %175, ptr align 8 %_381, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_377, ptr align 8 @alloc_77cc6e4a047661fb3e5e6b0040f6dc84, ptr align 8 %_380)
          to label %bb213 unwind label %funclet_bb270

bb213:                                            ; preds = %bb212
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_377)
          to label %bb214 unwind label %funclet_bb270

bb214:                                            ; preds = %bb213
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %176 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h952596421417f4fdE"(ptr align 8 %numbers13)
          to label %bb215 unwind label %funclet_bb270

bb215:                                            ; preds = %bb214
  %_385.0 = extractvalue { ptr, i64 } %176, 0
  %_385.1 = extractvalue { ptr, i64 } %176, 1
; invoke core::slice::<impl [T]>::iter
  %177 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9daa58a545a75da3E"(ptr align 4 %_385.0, i64 %_385.1)
          to label %bb216 unwind label %funclet_bb270

bb216:                                            ; preds = %bb215
  %_384.0 = extractvalue { ptr, ptr } %177, 0
  %_384.1 = extractvalue { ptr, ptr } %177, 1
; invoke core::iter::traits::iterator::Iterator::sum
  %178 = invoke i32 @_ZN4core4iter6traits8iterator8Iterator3sum17hdb3ab92ad2896615E(ptr %_384.0, ptr %_384.1)
          to label %bb217 unwind label %funclet_bb270

bb217:                                            ; preds = %bb216
  store i32 %178, ptr %sum, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_392, ptr align 4 %sum)
          to label %bb218 unwind label %funclet_bb270

bb218:                                            ; preds = %bb217
  %179 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_391, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %179, ptr align 8 %_392, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_388, ptr align 8 @alloc_fae7b78f3403b48b2870d7d78fcc4226, ptr align 8 %_391)
          to label %bb219 unwind label %funclet_bb270

bb219:                                            ; preds = %bb218
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_388)
          to label %bb220 unwind label %funclet_bb270

bb220:                                            ; preds = %bb219
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_395, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb221 unwind label %funclet_bb270

bb221:                                            ; preds = %bb220
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_395)
          to label %bb222 unwind label %funclet_bb270

bb222:                                            ; preds = %bb221
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_398, ptr align 8 @alloc_023c5ace34efd49fe59a2f1373515833)
          to label %bb223 unwind label %funclet_bb270

bb223:                                            ; preds = %bb222
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_398)
          to label %bb224 unwind label %funclet_bb270

bb224:                                            ; preds = %bb223
; invoke core::ops::range::RangeInclusive<Idx>::new
  invoke void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf35903eb3007664eE"(ptr sret([12 x i8]) align 4 %_403, i32 1, i32 10)
          to label %bb225 unwind label %funclet_bb270

bb225:                                            ; preds = %bb224
; invoke core::iter::traits::iterator::Iterator::filter
  invoke void @_ZN4core4iter6traits8iterator8Iterator6filter17h1fc51651d854f0b8E(ptr sret([12 x i8]) align 4 %_402, ptr align 4 %_403)
          to label %bb226 unwind label %funclet_bb270

bb226:                                            ; preds = %bb225
; invoke core::iter::traits::iterator::Iterator::map
  invoke void @_ZN4core4iter6traits8iterator8Iterator3map17h6ec6628ee06d548cE(ptr sret([12 x i8]) align 4 %_401, ptr align 4 %_402)
          to label %bb227 unwind label %funclet_bb270

bb227:                                            ; preds = %bb226
; invoke core::iter::traits::iterator::Iterator::collect
  invoke void @_ZN4core4iter6traits8iterator8Iterator7collect17h88faa208b2460167E(ptr sret([24 x i8]) align 8 %result14, ptr align 4 %_401)
          to label %bb228 unwind label %funclet_bb270

bb228:                                            ; preds = %bb227
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17he8a22f98dffab20dE(ptr sret([16 x i8]) align 8 %_409, ptr align 8 %result14)
          to label %bb229 unwind label %funclet_bb269

bb269:                                            ; preds = %funclet_bb269
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %result14) #20 [ "funclet"(token %cleanuppad29) ]
  cleanupret from %cleanuppad29 unwind label %funclet_bb270

funclet_bb269:                                    ; preds = %bb281, %bb268, %bb267, %bb259, %bb258, %bb257, %bb280, %bb256, %bb253, %bb252, %bb251, %bb248, %bb247, %bb246, %bb245, %bb244, %bb243, %bb238, %bb289, %bb235, %bb234, %bb233, %bb232, %bb231, %bb230, %bb229, %bb228
  %cleanuppad29 = cleanuppad within none []
  br label %bb269

bb229:                                            ; preds = %bb228
  %180 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_408, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %180, ptr align 8 %_409, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_405, ptr align 8 @alloc_aed33c94cb45050d4d0f1fb9eed512f2, ptr align 8 %_408)
          to label %bb230 unwind label %funclet_bb269

bb230:                                            ; preds = %bb229
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_405)
          to label %bb231 unwind label %funclet_bb269

bb231:                                            ; preds = %bb230
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_412, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb232 unwind label %funclet_bb269

bb232:                                            ; preds = %bb231
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_412)
          to label %bb233 unwind label %funclet_bb269

bb233:                                            ; preds = %bb232
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_415, ptr align 8 @alloc_6284f49c09bbc86b3693660fbfdaa60a)
          to label %bb234 unwind label %funclet_bb269

bb234:                                            ; preds = %bb233
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_415)
          to label %bb235 unwind label %funclet_bb269

bb235:                                            ; preds = %bb234
; invoke alloc::alloc::exchange_malloc
  %_422 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h9069f32671bf8c2aE(i64 72, i64 8)
          to label %bb236 unwind label %funclet_bb269

bb236:                                            ; preds = %bb235
  store ptr %_422, ptr %_423, align 8
; invoke <T as alloc::string::ToString>::to_string
  invoke void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17haaeace487c21f475E"(ptr sret([24 x i8]) align 8 %_425, ptr align 1 @alloc_3edef0b68cfa9c8c95e6d4fe1a68842b, i64 5)
          to label %bb237 unwind label %funclet_bb281

bb281:                                            ; preds = %funclet_bb281
; call <alloc::boxed::Box<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha0176726bdb08e61E"(ptr align 8 %_423) #20 [ "funclet"(token %cleanuppad30) ]
  cleanupret from %cleanuppad30 unwind label %funclet_bb269

funclet_bb281:                                    ; preds = %bb236
  %cleanuppad30 = cleanuppad within none []
  br label %bb281

bb237:                                            ; preds = %bb236
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_424, ptr align 8 %_425, i64 24, i1 false)
  %181 = getelementptr inbounds i8, ptr %_427, i64 8
  store i32 42, ptr %181, align 8
  store i64 -9223372036854775808, ptr %_427, align 8
  store i64 -9223372036854775807, ptr %_428, align 8
  %_471 = load ptr, ptr %_423, align 8
  %_473 = ptrtoint ptr %_471 to i64
  %_476 = and i64 %_473, 7
  %_477 = icmp eq i64 %_476, 0
  br i1 %_477, label %bb282, label %panic31

bb282:                                            ; preds = %bb237
  %_518 = ptrtoint ptr %_471 to i64
  %_521 = icmp eq i64 %_518, 0
  %_522 = and i1 %_521, true
  %_523 = xor i1 %_522, true
  br i1 %_523, label %bb289, label %panic32

panic31:                                          ; preds = %bb237
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 8, i64 %_473, ptr align 8 @alloc_17418fe1e2725972c923094ec36fb567) #22
  unreachable

bb289:                                            ; preds = %bb282
  %182 = getelementptr inbounds nuw %Message, ptr %_471, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %182, ptr align 8 %_424, i64 24, i1 false)
  %183 = getelementptr inbounds nuw %Message, ptr %_471, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %183, ptr align 8 %_427, i64 24, i1 false)
  %184 = getelementptr inbounds nuw %Message, ptr %_471, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %184, ptr align 8 %_428, i64 24, i1 false)
  %_419 = load ptr, ptr %_423, align 8
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h0f2b6cb3a0a7e2d1E"(ptr sret([24 x i8]) align 8 %messages, ptr align 8 %_419, i64 3)
          to label %bb238 unwind label %funclet_bb269

panic32:                                          ; preds = %bb282
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_17418fe1e2725972c923094ec36fb567) #22
  unreachable

bb238:                                            ; preds = %bb289
; invoke <alloc::vec::Vec<T,A> as core::iter::traits::collect::IntoIterator>::into_iter
  invoke void @"_ZN90_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd286d096d4486eb1E"(ptr sret([32 x i8]) align 8 %_429, ptr align 8 %messages)
          to label %bb239 unwind label %funclet_bb269

bb239:                                            ; preds = %bb238
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter15, ptr align 8 %_429, i64 32, i1 false)
  br label %bb240

bb240:                                            ; preds = %bb309, %bb239
; invoke <alloc::vec::into_iter::IntoIter<T,A> as core::iter::traits::iterator::Iterator>::next
  invoke void @"_ZN103_$LT$alloc..vec..into_iter..IntoIter$LT$T$C$A$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h6c378266e72a163eE"(ptr sret([24 x i8]) align 8 %_431, ptr align 8 %iter15)
          to label %bb241 unwind label %funclet_bb268

bb268:                                            ; preds = %funclet_bb268
; call core::ptr::drop_in_place<alloc::vec::into_iter::IntoIter<11_advanced_features::Message>>
  call void @"_ZN4core3ptr89drop_in_place$LT$alloc..vec..into_iter..IntoIter$LT$11_advanced_features..Message$GT$$GT$17haba010042370dca3E"(ptr align 8 %iter15) #20 [ "funclet"(token %cleanuppad33) ]
  cleanupret from %cleanuppad33 unwind label %funclet_bb269

funclet_bb268:                                    ; preds = %bb242, %bb240
  %cleanuppad33 = cleanuppad within none []
  br label %bb268

bb241:                                            ; preds = %bb240
  %185 = load i64, ptr %_431, align 8
  %186 = icmp eq i64 %185, -9223372036854775806
  %_433 = select i1 %186, i64 0, i64 1
  %187 = trunc nuw i64 %_433 to i1
  br i1 %187, label %bb242, label %bb243

bb242:                                            ; preds = %bb241
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %msg, ptr align 8 %_431, i64 24, i1 false)
; invoke _11_advanced_features::process_message
  invoke void @_ZN21_11_advanced_features15process_message17h8e1fa084ffa6f388E(ptr align 8 %msg)
          to label %bb309 unwind label %funclet_bb268

bb243:                                            ; preds = %bb241
; invoke core::ptr::drop_in_place<alloc::vec::into_iter::IntoIter<11_advanced_features::Message>>
  invoke void @"_ZN4core3ptr89drop_in_place$LT$alloc..vec..into_iter..IntoIter$LT$11_advanced_features..Message$GT$$GT$17haba010042370dca3E"(ptr align 8 %iter15)
          to label %bb244 unwind label %funclet_bb269

bb244:                                            ; preds = %bb243
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_437, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb245 unwind label %funclet_bb269

bb245:                                            ; preds = %bb244
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_437)
          to label %bb246 unwind label %funclet_bb269

bb246:                                            ; preds = %bb245
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_440, ptr align 8 @alloc_6aaf3f3768fd6caa791f47e6081f4f51)
          to label %bb247 unwind label %funclet_bb269

bb247:                                            ; preds = %bb246
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_440)
          to label %bb248 unwind label %funclet_bb269

bb248:                                            ; preds = %bb247
; invoke _11_advanced_features::complex_operation
  invoke void @_ZN21_11_advanced_features17complex_operation17h597aacebad3952d7E(ptr sret([24 x i8]) align 8 %_442)
          to label %bb249 unwind label %funclet_bb269

bb249:                                            ; preds = %bb248
  %188 = load i64, ptr %_442, align 8
  %189 = icmp eq i64 %188, -9223372036854775808
  %_443 = select i1 %189, i64 0, i64 1
  %190 = trunc nuw i64 %_443 to i1
  br i1 %190, label %bb250, label %bb251

bb250:                                            ; preds = %bb249
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %e, ptr align 8 %_442, i64 24, i1 false)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5a3f64c052421b06E(ptr sret([16 x i8]) align 8 %_458, ptr align 8 %e)
          to label %bb254 unwind label %funclet_bb267

bb251:                                            ; preds = %bb249
  %191 = getelementptr inbounds i8, ptr %_442, i64 8
  %192 = load i32, ptr %191, align 8
  store i32 %192, ptr %result16, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_450, ptr align 4 %result16)
          to label %bb252 unwind label %funclet_bb269

bb252:                                            ; preds = %bb251
  %193 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_449, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %193, ptr align 8 %_450, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_446, ptr align 8 @alloc_735da50cf165c4f36b10049b8526448f, ptr align 8 %_449)
          to label %bb253 unwind label %funclet_bb269

bb253:                                            ; preds = %bb252
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_446)
          to label %bb310 unwind label %funclet_bb269

bb310:                                            ; preds = %bb253
  br label %bb280

bb280:                                            ; preds = %bb256, %bb310
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_461, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb257 unwind label %funclet_bb269

bb267:                                            ; preds = %funclet_bb267
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %e) #20 [ "funclet"(token %cleanuppad34) ]
  cleanupret from %cleanuppad34 unwind label %funclet_bb269

funclet_bb267:                                    ; preds = %bb255, %bb254, %bb250
  %cleanuppad34 = cleanuppad within none []
  br label %bb267

bb254:                                            ; preds = %bb250
  %194 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_457, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %194, ptr align 8 %_458, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_454, ptr align 8 @alloc_b81dd633989cc95e6d5bb487bf79e870, ptr align 8 %_457)
          to label %bb255 unwind label %funclet_bb267

bb255:                                            ; preds = %bb254
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_454)
          to label %bb256 unwind label %funclet_bb267

bb256:                                            ; preds = %bb255
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %e)
          to label %bb280 unwind label %funclet_bb269

bb257:                                            ; preds = %bb280
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_461)
          to label %bb258 unwind label %funclet_bb269

bb258:                                            ; preds = %bb257
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_464, ptr align 8 @alloc_2d9af10799b8ba4a64ac4d7e087abf63)
          to label %bb259 unwind label %funclet_bb269

bb259:                                            ; preds = %bb258
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_464)
          to label %bb260 unwind label %funclet_bb269

bb260:                                            ; preds = %bb259
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %result14)
          to label %bb261 unwind label %funclet_bb270

bb261:                                            ; preds = %bb260
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %evens)
          to label %bb262 unwind label %funclet_bb271

bb262:                                            ; preds = %bb261
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %doubled)
          to label %bb263 unwind label %funclet_bb272

bb263:                                            ; preds = %bb262
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %numbers13)
          to label %bb264 unwind label %funclet_bb273

bb264:                                            ; preds = %bb263
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %stack)
          to label %bb265 unwind label %funclet_bb274

bb265:                                            ; preds = %bb264
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17h545113c05285dd52E"(ptr align 8 %numbers)
  ret void

bb309:                                            ; preds = %bb242
  br label %bb240

bb121:                                            ; preds = %bb118
  %195 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_204, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %195, ptr align 8 %_205, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_201, ptr align 8 @alloc_eaa2982728a46158796376bb2e9f6120, ptr align 8 %_204)
          to label %bb122 unwind label %funclet_bb274

bb122:                                            ; preds = %bb121
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_201)
          to label %bb300 unwind label %funclet_bb274

bb300:                                            ; preds = %bb122
  br label %bb110

bb277:                                            ; preds = %funclet_bb277
  cleanupret from %cleanuppad35 unwind to caller

funclet_bb277:                                    ; preds = %bb276, %bb275, %bb274
  %cleanuppad35 = cleanuppad within none []
  br label %bb277

bb47:                                             ; No predecessors!
  unreachable
}

; _11_advanced_features::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17hcb1382b96b46ef60E"(ptr align 1 %_1, ptr align 4 %x) unnamed_addr #1 {
start:
; call <&i32 as core::ops::arith::Mul<i32>>::mul
  %_0 = call i32 @"_ZN60_$LT$$RF$i32$u20$as$u20$core..ops..arith..Mul$LT$i32$GT$$GT$3mul17h386fe3a6dc551df5E"(ptr align 4 %x, i32 2, ptr align 8 @alloc_f782695604732cc93d77827976f2199e)
  ret i32 %_0
}

; _11_advanced_features::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17h6acf53c0769bec0dE"(ptr align 1 %_1, ptr align 8 %_2) unnamed_addr #1 {
start:
  %_9 = load ptr, ptr %_2, align 8
  %x = load i32, ptr %_9, align 4
  %_7 = icmp eq i32 %x, -2147483648
  %_8 = and i1 false, %_7
  br i1 %_8, label %panic, label %bb2

bb2:                                              ; preds = %start
  %_4 = srem i32 %x, 2
  %_0 = icmp eq i32 %_4, 0
  ret i1 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_034f5314d1276bc8624035dc7f2ccce9) #19
  unreachable
}

; _11_advanced_features::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17hd6b9d2007b6ddf31E"(ptr align 1 %_1, ptr align 4 %_2) unnamed_addr #1 {
start:
  %x = load i32, ptr %_2, align 4
  %_7 = icmp eq i32 %x, -2147483648
  %_8 = and i1 false, %_7
  br i1 %_8, label %panic, label %bb2

bb2:                                              ; preds = %start
  %_4 = srem i32 %x, 2
  %_0 = icmp eq i32 %_4, 0
  ret i1 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_24107041b0d41e40346c98860437e39e) #19
  unreachable
}

; _11_advanced_features::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN21_11_advanced_features4main28_$u7b$$u7b$closure$u7d$$u7d$17h1c686b1e6fa9d427E"(ptr align 1 %_1, i32 %x) unnamed_addr #1 {
start:
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %x, i32 %x)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_eb7215b3588ada683685d858ba86c058) #19
  unreachable
}

; _11_advanced_features::safe_divide
; Function Attrs: uwtable
define internal void @_ZN21_11_advanced_features11safe_divide17h23b2cf84e5049b76E(ptr sret([24 x i8]) align 8 %_0, i32 %a, i32 %b) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
  %0 = icmp eq i32 %b, 0
  br i1 %0, label %bb1, label %bb3

bb1:                                              ; preds = %start
; call <T as alloc::string::ToString>::to_string
  call void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17haaeace487c21f475E"(ptr sret([24 x i8]) align 8 %_3, ptr align 1 @alloc_36712b18fa80a356639965f9856729fb, i64 16)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 24, i1 false)
  br label %bb6

bb3:                                              ; preds = %start
  %_6 = icmp eq i32 %b, 0
  br i1 %_6, label %panic, label %bb4

bb6:                                              ; preds = %bb5, %bb1
  ret void

bb4:                                              ; preds = %bb3
  %_7 = icmp eq i32 %b, -1
  %_8 = icmp eq i32 %a, -2147483648
  %_9 = and i1 %_7, %_8
  br i1 %_9, label %panic1, label %bb5

panic:                                            ; preds = %bb3
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_6a83e622db22f0eec691a5ea76047efd) #19
  unreachable

bb5:                                              ; preds = %bb4
  %_5 = sdiv i32 %a, %b
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i32 %_5, ptr %1, align 8
  store i64 -9223372036854775808, ptr %_0, align 8
  br label %bb6

panic1:                                           ; preds = %bb4
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_6a83e622db22f0eec691a5ea76047efd) #19
  unreachable
}

; _11_advanced_features::find_number
; Function Attrs: uwtable
define internal { i64, i64 } @_ZN21_11_advanced_features11find_number17h4271184ac6c2448eE(ptr align 4 %numbers.0, i64 %numbers.1, i32 %0) unnamed_addr #0 {
start:
  %_4 = alloca [16 x i8], align 8
  %target = alloca [4 x i8], align 4
  store i32 %0, ptr %target, align 4
; call core::slice::<impl [T]>::iter
  %1 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9daa58a545a75da3E"(ptr align 4 %numbers.0, i64 %numbers.1)
  %2 = extractvalue { ptr, ptr } %1, 0
  %3 = extractvalue { ptr, ptr } %1, 1
  store ptr %2, ptr %_4, align 8
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  store ptr %3, ptr %4, align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::position
  %5 = call { i64, i64 } @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$8position17hfb102d45f8d88a18E"(ptr align 8 %_4, ptr align 4 %target)
  %_0.0 = extractvalue { i64, i64 } %5, 0
  %_0.1 = extractvalue { i64, i64 } %5, 1
  %6 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %7 = insertvalue { i64, i64 } %6, i64 %_0.1, 1
  ret { i64, i64 } %7
}

; _11_advanced_features::find_number::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN21_11_advanced_features11find_number28_$u7b$$u7b$closure$u7d$$u7d$17ha05cf360fcd1b761E"(ptr align 8 %_1, ptr align 4 %_2) unnamed_addr #1 {
start:
  %x = load i32, ptr %_2, align 4
  %_5 = load ptr, ptr %_1, align 8
  %_4 = load i32, ptr %_5, align 4
  %_0 = icmp eq i32 %x, %_4
  ret i1 %_0
}

; _11_advanced_features::process_message
; Function Attrs: uwtable
define internal void @_ZN21_11_advanced_features15process_message17h8e1fa084ffa6f388E(ptr align 8 %msg) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_20 = alloca [48 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_16 = alloca [16 x i8], align 8
  %_13 = alloca [48 x i8], align 8
  %num = alloca [4 x i8], align 4
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %text = alloca [24 x i8], align 8
  %0 = load i64, ptr %msg, align 8
  %1 = sub i64 %0, -9223372036854775808
  %2 = icmp ule i64 %1, 1
  %3 = add i64 %1, 1
  %_2 = select i1 %2, i64 %3, i64 0
  switch i64 %_2, label %bb1 [
    i64 0, label %bb4
    i64 1, label %bb3
    i64 2, label %bb2
  ]

bb1:                                              ; preds = %start
  unreachable

bb4:                                              ; preds = %start
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %text, ptr align 8 %msg, i64 24, i1 false)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h5a3f64c052421b06E(ptr sret([16 x i8]) align 8 %_9, ptr align 8 %text)
          to label %bb5 unwind label %funclet_bb11

bb3:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %msg, i64 8
  %5 = load i32, ptr %4, align 8
  store i32 %5, ptr %num, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h898fdb559519e23cE(ptr sret([16 x i8]) align 8 %_17, ptr align 4 %num)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_17, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_13, ptr align 8 @alloc_fa86de3cf826d05860c2cdc447184c8f, ptr align 8 %_16)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_13)
  br label %bb12

bb2:                                              ; preds = %start
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17heceef0d88d0910b7E(ptr sret([48 x i8]) align 8 %_20, ptr align 8 @alloc_401f6fe8583d145640de68804cff6fdf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_20)
  br label %bb12

bb11:                                             ; preds = %funclet_bb11
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %text) #20 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb6, %bb5, %bb4
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb4
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_9, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117he99f596472c9ce41E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_67e443d542bcaab3dd76a71807774222, ptr align 8 %_8)
          to label %bb6 unwind label %funclet_bb11

bb6:                                              ; preds = %bb5
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
          to label %bb7 unwind label %funclet_bb11

bb7:                                              ; preds = %bb6
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h5fc9bd98073d1aa6E"(ptr align 8 %text)
  br label %bb12

bb12:                                             ; preds = %bb2, %bb3, %bb7
  ret void
}

; _11_advanced_features::complex_operation
; Function Attrs: uwtable
define internal void @_ZN21_11_advanced_features17complex_operation17h597aacebad3952d7E(ptr sret([24 x i8]) align 8 %_0) unnamed_addr #0 {
start:
  %residual1 = alloca [24 x i8], align 8
  %_7 = alloca [24 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %residual = alloca [24 x i8], align 8
  %_2 = alloca [24 x i8], align 8
  %_1 = alloca [24 x i8], align 8
; call _11_advanced_features::safe_divide
  call void @_ZN21_11_advanced_features11safe_divide17h23b2cf84e5049b76E(ptr sret([24 x i8]) align 8 %_2, i32 20, i32 4)
; call <core::result::Result<T,E> as core::ops::try_trait::Try>::branch
  call void @"_ZN79_$LT$core..result..Result$LT$T$C$E$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h5da1a59185789f7cE"(ptr sret([24 x i8]) align 8 %_1, ptr align 8 %_2)
  %0 = load i64, ptr %_1, align 8
  %1 = icmp eq i64 %0, -9223372036854775808
  %_3 = select i1 %1, i64 0, i64 1
  %2 = trunc nuw i64 %_3 to i1
  br i1 %2, label %bb5, label %bb4

bb5:                                              ; preds = %start
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %residual, ptr align 8 %_1, i64 24, i1 false)
; call <core::result::Result<T,F> as core::ops::try_trait::FromResidual<core::result::Result<core::convert::Infallible,E>>>::from_residual
  call void @"_ZN153_$LT$core..result..Result$LT$T$C$F$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..result..Result$LT$core..convert..Infallible$C$E$GT$$GT$$GT$13from_residual17h2381c7da409addfeE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %residual, ptr align 8 @alloc_c08516871150497b81c8b396ff7efdd2)
  br label %bb11

bb4:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %_1, i64 8
  %step1 = load i32, ptr %3, align 8
; call _11_advanced_features::safe_divide
  call void @_ZN21_11_advanced_features11safe_divide17h23b2cf84e5049b76E(ptr sret([24 x i8]) align 8 %_7, i32 %step1, i32 2)
; call <core::result::Result<T,E> as core::ops::try_trait::Try>::branch
  call void @"_ZN79_$LT$core..result..Result$LT$T$C$E$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h5da1a59185789f7cE"(ptr sret([24 x i8]) align 8 %_6, ptr align 8 %_7)
  %4 = load i64, ptr %_6, align 8
  %5 = icmp eq i64 %4, -9223372036854775808
  %_8 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_8 to i1
  br i1 %6, label %bb9, label %bb8

bb9:                                              ; preds = %bb4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %residual1, ptr align 8 %_6, i64 24, i1 false)
; call <core::result::Result<T,F> as core::ops::try_trait::FromResidual<core::result::Result<core::convert::Infallible,E>>>::from_residual
  call void @"_ZN153_$LT$core..result..Result$LT$T$C$F$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..result..Result$LT$core..convert..Infallible$C$E$GT$$GT$$GT$13from_residual17h2381c7da409addfeE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %residual1, ptr align 8 @alloc_46a16dc9ead633cefec9cfa661f86e5c)
  br label %bb11

bb8:                                              ; preds = %bb4
  %7 = getelementptr inbounds i8, ptr %_6, i64 8
  %step2 = load i32, ptr %7, align 8
  %8 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %step2, i32 3)
  %_12.0 = extractvalue { i32, i1 } %8, 0
  %_12.1 = extractvalue { i32, i1 } %8, 1
  br i1 %_12.1, label %panic, label %bb10

bb10:                                             ; preds = %bb8
  %9 = getelementptr inbounds i8, ptr %_0, i64 8
  store i32 %_12.0, ptr %9, align 8
  store i64 -9223372036854775808, ptr %_0, align 8
  br label %bb11

panic:                                            ; preds = %bb8
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_9c9ec0e0f9012040139f57cd1c72b478) #19
  unreachable

bb11:                                             ; preds = %bb5, %bb9, %bb10
  ret void

bb3:                                              ; No predecessors!
  unreachable
}

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #6

declare i32 @__CxxFrameHandler3(...) unnamed_addr #7

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.uadd.sat.i64(i64, i64) #9

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #9

; core::fmt::Formatter::debug_list
; Function Attrs: uwtable
declare void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::finish
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #9

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #10

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #11

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for usize>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE"(ptr align 8, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::entry
; Function Attrs: uwtable
declare align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8, ptr align 1, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #9

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; core::panicking::assert_failed
; Function Attrs: cold minsize noinline noreturn optsize uwtable
declare void @_ZN4core9panicking13assert_failed17h364f5d38302fe36bE(i8, ptr align 8, ptr align 8, ptr align 8, ptr align 8) unnamed_addr #12

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #0

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #13

; alloc::alloc::handle_alloc_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc5alloc18handle_alloc_error17h786143be1fde6527E(i64, i64) unnamed_addr #14

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #15

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #16

; __rustc::__rust_realloc
; Function Attrs: nounwind allockind("realloc,aligned") allocsize(3) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr allocptr, i64, i64 allocalign, i64) unnamed_addr #17

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: write)
declare void @llvm.memset.p0.i64(ptr nocapture writeonly, i8, i64, i1 immarg) #18

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #14

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; core::panicking::panic_misaligned_pointer_dereference
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64, i64, ptr align 8) unnamed_addr #11

; core::panicking::panic_null_pointer_dereference
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8) unnamed_addr #11

; core::panicking::panic_const::panic_const_rem_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #8

define i32 @main(i32 %0, ptr %1) unnamed_addr #7 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hefcc0fce842277cbE(ptr @_ZN21_11_advanced_features4main17h191ecf3e3c08520bE, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { cold uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #7 = { "target-cpu"="x86-64" }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #10 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #11 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { cold minsize noinline noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #16 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #17 = { nounwind allockind("realloc,aligned") allocsize(3) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #18 = { nocallback nofree nounwind willreturn memory(argmem: write) }
attributes #19 = { noreturn }
attributes #20 = { cold }
attributes #21 = { nounwind }
attributes #22 = { noreturn nounwind }
attributes #23 = { cold noreturn nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 16151005477036318}
