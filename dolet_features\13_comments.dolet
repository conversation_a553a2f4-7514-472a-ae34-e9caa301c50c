# 13. Comments - Code Documentation
# This file demonstrates comment usage in Dolet

print "=== Comments Demo ==="
print ""

# Single-line comments
print "Single-line Comments:"
# This is a single-line comment
print "Comments help explain code"

# Comments can be at the end of lines
int age = 25  # This stores the age
string name = "<PERSON>"  # This stores the name

print "age = " + age  # Print the age value
print "name = " + name  # Print the name value
print ""

# Multiple single-line comments
print "Multiple Comments:"
# This is the first comment
# This is the second comment  
# This is the third comment
print "Multiple comments shown above"
print ""

# Comments for code sections
print "Section Comments:"

# === VARIABLES SECTION ===
int x = 10
int y = 20

# === CALCULATIONS SECTION ===
int sum = x + y
int product = x * y

# === OUTPUT SECTION ===
print "x = " + x
print "y = " + y
print "sum = " + sum
print "product = " + product
print ""

# Comments explaining complex logic
print "Explanatory Comments:"

# Check if number is even or odd
int number = 17
if number % 2 == 0
    print number + " is even"  # Remainder is 0
else
    print number + " is odd"   # Remainder is 1
print ""

# Comments in loops
print "Comments in Loops:"

# Loop from 1 to 5
for i = 1 to 5
    # Print current iteration
    print "Iteration: " + i
    
    # Check if it's the middle iteration
    if i == 3
        print "  Middle iteration!"  # Special case
print ""

# Comments in functions
print "Comments in Functions:"

# Function to calculate area of rectangle
# Parameters: length and width
# Returns: area as integer
fun calculate_area(length, width) {
    # Multiply length by width
    int area = length * width
    
    # Return the calculated area
    return area
}

# Call the function with sample values
int room_area = calculate_area(12, 8)  # 12x8 room
print "Room area: " + room_area + " square units"
print ""

# TODO comments (planning)
print "TODO Comments:"
# TODO: Add input validation
# TODO: Implement error handling
# TODO: Add more mathematical functions
print "TODO items listed in comments above"
print ""

# Comments for debugging
print "Debug Comments:"
int debug_value = 42
print "debug_value = " + debug_value  # DEBUG: Check value
# DEBUG: The following line should print 84
print "double value = " + (debug_value * 2)
print ""

# Comments explaining algorithms
print "Algorithm Comments:"

# Bubble sort algorithm (simplified)
array int numbers = [64, 34, 25, 12, 22]
print "Original array: [64, 34, 25, 12, 22]"

# Outer loop for passes
for i = 0 to 3
    # Inner loop for comparisons
    for j = 0 to 3
        # Compare adjacent elements
        if numbers[j] > numbers[j + 1]
            # Swap elements if they're in wrong order
            int temp = numbers[j]
            set numbers[j] = numbers[j + 1]
            set numbers[j + 1] = temp

print "After sorting:"
for i = 0 to 4
    print numbers[i] + " "  # Print each sorted element
print ""

# Comments for constants and magic numbers
print "Constants and Magic Numbers:"
int MAX_USERS = 100        # Maximum number of users
int MIN_PASSWORD_LENGTH = 8 # Minimum password length
float PI = 3.14159         # Mathematical constant pi
float TAX_RATE = 0.15      # 15% tax rate

print "MAX_USERS = " + MAX_USERS
print "MIN_PASSWORD_LENGTH = " + MIN_PASSWORD_LENGTH
print "PI = " + PI
print "TAX_RATE = " + TAX_RATE
print ""

# Comments for error conditions
print "Error Condition Comments:"

int divisor = 0
if divisor == 0
    print "Error: Cannot divide by zero"  # Prevent division by zero
else
    int result = 10 / divisor
    print "Result: " + result

# Comments for performance notes
print "Performance Comments:"
# NOTE: This loop could be optimized for large arrays
for i = 1 to 1000
    # Perform some calculation (simplified)
    int calc = i * 2
    # Only print first few results to avoid spam
    if i <= 5
        print "calc(" + i + ") = " + calc
print "... (995 more calculations performed)"
print ""

# Comments for version history
print "Version History Comments:"
# Version 1.0: Basic functionality
# Version 1.1: Added error handling  
# Version 1.2: Improved performance
# Version 1.3: Added comments documentation
print "Version 1.3 - Comments Documentation"
print ""

print "=== End of Comments Demo ==="
