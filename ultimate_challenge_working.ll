; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 273 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"Admin\0A\00", align 1
@.str_2 = private unnamed_addr constant [7 x i8] c"Guest\0A\00", align 1
@.str_3 = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_4 = private unnamed_addr constant [49 x i8] c"This is a very long string for testing purposes\0A\00", align 1
@.str_5 = private unnamed_addr constant [6 x i8] c"\F0\9F\8E\89\0A\00", align 1
@.str_6 = private unnamed_addr constant [6 x i8] c"\F0\9F\9A\80\0A\00", align 1
@.str_7 = private unnamed_addr constant [6 x i8] c"\F0\9F\8E\89\0A\00", align 1
@.str_8 = private unnamed_addr constant [12 x i8] c"\D9\85\D8\B1\D8\AD\D8\A8\D8\A7\0A\00", align 1
@.str_9 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_score = global i32 0, align 4
@global_maxScore = global i32 0, align 4
@global_minScore = global i32 0, align 4
@global_passingGrade = global i32 0, align 4
@global_perfectScore = global i32 0, align 4
@global_pi = global float 0.0, align 4
@global_e = global float 0.0, align 4
@global_goldenRatio = global float 0.0, align 4
@global_zero = global float 0.0, align 4
@global_negativeOne = global float 0.0, align 4
@global_playerName = global i8* null, align 8
@global_adminName = global i8* null, align 8
@global_guestName = global i8* null, align 8
@global_emptyString = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_longString = global i8* null, align 8
@global_isActive = global i1 0, align 1
@global_isExpired = global i1 0, align 1
@global_hasPermission = global i1 0, align 1
@global_isGuest = global i1 0, align 1
@global_debugMode = global i1 0, align 1
@global_fib1 = global i32 0, align 4
@global_fib2 = global i32 0, align 4
@global_fib3 = global i32 0, align 4
@global_fib4 = global i32 0, align 4
@global_fib5 = global i32 0, align 4
@global_prime1 = global i32 0, align 4
@global_prime2 = global i32 0, align 4
@global_prime3 = global i32 0, align 4
@global_prime4 = global i32 0, align 4
@global_emoji1 = global i8* null, align 8
@global_emoji2 = global i8* null, align 8
@global_emoji3 = global i8* null, align 8
@global_arabic = global i8* null, align 8
@global_english = global i8* null, align 8
@global_arr1 = global i32 0, align 4
@global_arr2 = global i32 0, align 4
@global_arr3 = global i32 0, align 4
@global_arr4 = global i32 0, align 4
@global_arr5 = global i32 0, align 4
@global_large1 = global float 0.0, align 4
@global_large2 = global float 0.0, align 4
@global_small1 = global float 0.0, align 4
@global_small2 = global float 0.0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "🎯 === ULTIMATE COMPARISON CHALLENGE === 🎯" (inline)
  %tmp_1_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\AF === ULTIMATE COMPARISON CHALLENGE === \F0\9F\8E\AF\00", [48 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [48 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 Setting up test variables..." (inline)
  %tmp_3_str = alloca [34 x i8], align 1
  store [34 x i8] c"\F0\9F\93\8A Setting up test variables...\00", [34 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [34 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int score = 85
  store i32 85, i32* @global_score, align 4
  ; int maxScore = 100
  store i32 100, i32* @global_maxScore, align 4
  ; int minScore = 0
  store i32 0, i32* @global_minScore, align 4
  ; int passingGrade = 60
  store i32 60, i32* @global_passingGrade, align 4
  ; int perfectScore = 100
  store i32 100, i32* @global_perfectScore, align 4
  ; float pi = 3.14159
  %tmp_5 = bitcast i32 1078530000 to float
  store float %tmp_5, float* @global_pi, align 4
  ; float e = 2.71828
  %tmp_6 = bitcast i32 1076754509 to float
  store float %tmp_6, float* @global_e, align 4
  ; float goldenRatio = 1.61803
  %tmp_7 = bitcast i32 1070537627 to float
  store float %tmp_7, float* @global_goldenRatio, align 4
  ; float zero = 0.0
  %tmp_8 = bitcast i32 0 to float
  store float %tmp_8, float* @global_zero, align 4
  ; float negativeOne = -1.0
  %tmp_9 = bitcast i32 3212836864 to float
  store float %tmp_9, float* @global_negativeOne, align 4
  ; string playerName = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_playerName, align 8
  ; string adminName = "Admin"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_adminName, align 8
  ; string guestName = "Guest"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_2, i64 0, i64 0), i8** @global_guestName, align 8
  ; string emptyString = ""
  store i8* getelementptr inbounds ([1 x i8], [1 x i8]* @.str_3, i64 0, i64 0), i8** @global_emptyString, align 8
  ; string longString = "This is a very long string for testing purposes"
  store i8* getelementptr inbounds ([48 x i8], [48 x i8]* @.str_4, i64 0, i64 0), i8** @global_longString, align 8
  ; bool isActive = true
  store i1 1, i1* @global_isActive, align 1
  ; bool isExpired = false
  store i1 0, i1* @global_isExpired, align 1
  ; bool hasPermission = true
  store i1 1, i1* @global_hasPermission, align 1
  ; bool isGuest = false
  store i1 0, i1* @global_isGuest, align 1
  ; bool debugMode = true
  store i1 1, i1* @global_debugMode, align 1
  ; print "✅ Variables initialized successfully!" (inline)
  %tmp_10_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9C\85 Variables initialized successfully!\00", [40 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [40 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔢 === EXTREME INTEGER COMPARISONS === 🔢" (inline)
  %tmp_12_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\94\A2 === EXTREME INTEGER COMPARISONS === \F0\9F\94\A2\00", [46 x i8]* %tmp_12_str, align 1
  %tmp_13 = bitcast [46 x i8]* %tmp_12_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score + 15) > (maxScore - 10)
  %tmp_14 = load i32, i32* @global_score, align 4
  %tmp_15 = add i32 %tmp_14, 15
  %tmp_16 = load i32, i32* @global_maxScore, align 4
  %tmp_17 = sub i32 %tmp_16, 10
  %tmp_18 = icmp sgt i32 %tmp_15, %tmp_17
  br i1 %tmp_18, label %if_true_1, label %if_else_3
if_true_1:
  ; print "🎉 PASS: Complex addition comparison works!" (inline)
  %tmp_19_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Complex addition comparison works!\00", [46 x i8]* %tmp_19_str, align 1
  %tmp_20 = bitcast [46 x i8]* %tmp_19_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_else_3:
  ; print "❌ FAIL: Complex addition comparison failed!" (inline)
  %tmp_21_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Complex addition comparison failed!\00", [46 x i8]* %tmp_21_str, align 1
  %tmp_22 = bitcast [46 x i8]* %tmp_21_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_end_2:
  ; if (score * 2) >= (maxScore + passingGrade)
  %tmp_23 = load i32, i32* @global_score, align 4
  %tmp_24 = mul i32 %tmp_23, 2
  %tmp_25 = load i32, i32* @global_maxScore, align 4
  %tmp_26 = load i32, i32* @global_passingGrade, align 4
  %tmp_27 = add i32 %tmp_25, %tmp_26
  %tmp_28 = icmp sge i32 %tmp_24, %tmp_27
  br i1 %tmp_28, label %if_true_4, label %if_else_6
if_true_4:
  ; print "🎉 PASS: Complex multiplication comparison works!" (inline)
  %tmp_29_str = alloca [52 x i8], align 1
  store [52 x i8] c"\F0\9F\8E\89 PASS: Complex multiplication comparison works!\00", [52 x i8]* %tmp_29_str, align 1
  %tmp_30 = bitcast [52 x i8]* %tmp_29_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_else_6:
  ; print "❌ FAIL: Complex multiplication comparison failed!" (inline)
  %tmp_31_str = alloca [52 x i8], align 1
  store [52 x i8] c"\E2\9D\8C FAIL: Complex multiplication comparison failed!\00", [52 x i8]* %tmp_31_str, align 1
  %tmp_32 = bitcast [52 x i8]* %tmp_31_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_end_5:
  ; if ((score + passingGrade) / 2) > ((maxScore - minScore) / 3)
  %tmp_33 = load i32, i32* @global_score, align 4
  %tmp_34 = load i32, i32* @global_passingGrade, align 4
  %tmp_35 = add i32 %tmp_33, %tmp_34
  %tmp_36 = sdiv i32 %tmp_35, 2
  %tmp_37 = load i32, i32* @global_maxScore, align 4
  %tmp_38 = load i32, i32* @global_minScore, align 4
  %tmp_39 = sub i32 %tmp_37, %tmp_38
  %tmp_40 = sdiv i32 %tmp_39, 3
  %tmp_41 = icmp sgt i32 %tmp_36, %tmp_40
  br i1 %tmp_41, label %if_true_7, label %if_else_9
if_true_7:
  ; print "🎉 PASS: Triple nested division comparison works!" (inline)
  %tmp_42_str = alloca [52 x i8], align 1
  store [52 x i8] c"\F0\9F\8E\89 PASS: Triple nested division comparison works!\00", [52 x i8]* %tmp_42_str, align 1
  %tmp_43 = bitcast [52 x i8]* %tmp_42_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_else_9:
  ; print "❌ FAIL: Triple nested division comparison failed!" (inline)
  %tmp_44_str = alloca [52 x i8], align 1
  store [52 x i8] c"\E2\9D\8C FAIL: Triple nested division comparison failed!\00", [52 x i8]* %tmp_44_str, align 1
  %tmp_45 = bitcast [52 x i8]* %tmp_44_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_45)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_end_8:
  ; if (((score * 2) + (passingGrade * 3)) - (minScore * 5)) == ((maxScore * 2) + (perfectScore / 2))
  %tmp_46 = load i32, i32* @global_score, align 4
  %tmp_47 = mul i32 %tmp_46, 2
  %tmp_48 = load i32, i32* @global_passingGrade, align 4
  %tmp_49 = mul i32 %tmp_48, 3
  %tmp_50 = add i32 %tmp_47, %tmp_49
  %tmp_51 = load i32, i32* @global_minScore, align 4
  %tmp_52 = mul i32 %tmp_51, 5
  %tmp_53 = sub i32 %tmp_50, %tmp_52
  %tmp_54 = load i32, i32* @global_maxScore, align 4
  %tmp_55 = mul i32 %tmp_54, 2
  %tmp_56 = load i32, i32* @global_perfectScore, align 4
  %tmp_57 = sdiv i32 %tmp_56, 2
  %tmp_58 = add i32 %tmp_55, %tmp_57
  %tmp_59 = icmp eq i32 %tmp_53, %tmp_58
  br i1 %tmp_59, label %if_true_10, label %if_else_12
if_true_10:
  ; print "🎉 PASS: Extreme expression comparison works!" (inline)
  %tmp_60_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\89 PASS: Extreme expression comparison works!\00", [48 x i8]* %tmp_60_str, align 1
  %tmp_61 = bitcast [48 x i8]* %tmp_60_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_61)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_else_12:
  ; print "❌ FAIL: Extreme expression comparison failed!" (inline)
  %tmp_62_str = alloca [48 x i8], align 1
  store [48 x i8] c"\E2\9D\8C FAIL: Extreme expression comparison failed!\00", [48 x i8]* %tmp_62_str, align 1
  %tmp_63 = bitcast [48 x i8]* %tmp_62_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_63)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_end_11:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌊 === FLOATING POINT PRECISION TESTS === 🌊" (inline)
  %tmp_64_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\8C\8A === FLOATING POINT PRECISION TESTS === \F0\9F\8C\8A\00", [49 x i8]* %tmp_64_str, align 1
  %tmp_65 = bitcast [49 x i8]* %tmp_64_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_65)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if pi > e
  %tmp_66 = load float, float* @global_pi, align 4
  %tmp_67 = load float, float* @global_e, align 4
  %tmp_68 = fcmp ogt float %tmp_66, %tmp_67
  br i1 %tmp_68, label %if_true_13, label %if_else_15
if_true_13:
  ; print "🎉 PASS: π > e comparison works!" (inline)
  %tmp_69_str = alloca [36 x i8], align 1
  store [36 x i8] c"\F0\9F\8E\89 PASS: \CF\80 > e comparison works!\00", [36 x i8]* %tmp_69_str, align 1
  %tmp_70 = bitcast [36 x i8]* %tmp_69_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_70)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_else_15:
  ; print "❌ FAIL: π > e comparison failed!" (inline)
  %tmp_71_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9D\8C FAIL: \CF\80 > e comparison failed!\00", [36 x i8]* %tmp_71_str, align 1
  %tmp_72 = bitcast [36 x i8]* %tmp_71_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_72)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_end_14:
  ; if (pi * e) > (goldenRatio * 5)
  %tmp_73 = load float, float* @global_pi, align 4
  %tmp_74 = load float, float* @global_e, align 4
  %tmp_75 = fmul float %tmp_73, %tmp_74
  %tmp_76 = load float, float* @global_goldenRatio, align 4
  %tmp_77 = sitofp i32 5 to float
  %tmp_78 = fmul float %tmp_76, %tmp_77
  %tmp_79 = fcmp ogt float %tmp_75, %tmp_78
  br i1 %tmp_79, label %if_true_16, label %if_else_18
if_true_16:
  ; print "🎉 PASS: Complex float multiplication works!" (inline)
  %tmp_80_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\8E\89 PASS: Complex float multiplication works!\00", [47 x i8]* %tmp_80_str, align 1
  %tmp_81 = bitcast [47 x i8]* %tmp_80_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_81)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_else_18:
  ; print "❌ FAIL: Complex float multiplication failed!" (inline)
  %tmp_82_str = alloca [47 x i8], align 1
  store [47 x i8] c"\E2\9D\8C FAIL: Complex float multiplication failed!\00", [47 x i8]* %tmp_82_str, align 1
  %tmp_83 = bitcast [47 x i8]* %tmp_82_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_83)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_end_17:
  ; if (pi + e + goldenRatio) >= (zero + 7.5)
  %tmp_84 = load float, float* @global_pi, align 4
  %tmp_85 = load float, float* @global_e, align 4
  %tmp_86 = fadd float %tmp_84, %tmp_85
  %tmp_87 = load float, float* @global_goldenRatio, align 4
  %tmp_88 = fadd float %tmp_86, %tmp_87
  %tmp_89 = load float, float* @global_zero, align 4
  %tmp_90 = bitcast i32 1089470464 to float
  %tmp_91 = fadd float %tmp_89, %tmp_90
  %tmp_92 = fcmp oge float %tmp_88, %tmp_91
  br i1 %tmp_92, label %if_true_19, label %if_else_21
if_true_19:
  ; print "🎉 PASS: Multi-float addition comparison works!" (inline)
  %tmp_93_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Multi-float addition comparison works!\00", [50 x i8]* %tmp_93_str, align 1
  %tmp_94 = bitcast [50 x i8]* %tmp_93_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_else_21:
  ; print "❌ FAIL: Multi-float addition comparison failed!" (inline)
  %tmp_95_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Multi-float addition comparison failed!\00", [50 x i8]* %tmp_95_str, align 1
  %tmp_96 = bitcast [50 x i8]* %tmp_95_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_96)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_end_20:
  ; if negativeOne < zero
  %tmp_97 = load float, float* @global_negativeOne, align 4
  %tmp_98 = load float, float* @global_zero, align 4
  %tmp_99 = fcmp olt float %tmp_97, %tmp_98
  br i1 %tmp_99, label %if_true_22, label %if_else_24
if_true_22:
  ; print "🎉 PASS: Negative number comparison works!" (inline)
  %tmp_100_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Negative number comparison works!\00", [45 x i8]* %tmp_100_str, align 1
  %tmp_101 = bitcast [45 x i8]* %tmp_100_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_else_24:
  ; print "❌ FAIL: Negative number comparison failed!" (inline)
  %tmp_102_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Negative number comparison failed!\00", [45 x i8]* %tmp_102_str, align 1
  %tmp_103 = bitcast [45 x i8]* %tmp_102_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_103)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_end_23:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📝 === STRING COMPARISON MADNESS === 📝" (inline)
  %tmp_104_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\93\9D === STRING COMPARISON MADNESS === \F0\9F\93\9D\00", [44 x i8]* %tmp_104_str, align 1
  %tmp_105 = bitcast [44 x i8]* %tmp_104_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_105)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if playerName == "Ahmed"
  %tmp_106 = load i8*, i8** @global_playerName, align 8
  %tmp_107 = getelementptr inbounds [6 x i8], [6 x i8]* @str_0, i64 0, i64 0
  %tmp_109 = call i32 @strcmp(i8* %tmp_106, i8* %tmp_107)
  %tmp_108 = icmp eq i32 %tmp_109, 0
  br i1 %tmp_108, label %if_true_25, label %if_else_27
if_true_25:
  ; print "🎉 PASS: Basic string equality works!" (inline)
  %tmp_110_str = alloca [40 x i8], align 1
  store [40 x i8] c"\F0\9F\8E\89 PASS: Basic string equality works!\00", [40 x i8]* %tmp_110_str, align 1
  %tmp_111 = bitcast [40 x i8]* %tmp_110_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_111)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_else_27:
  ; print "❌ FAIL: Basic string equality failed!" (inline)
  %tmp_112_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9D\8C FAIL: Basic string equality failed!\00", [40 x i8]* %tmp_112_str, align 1
  %tmp_113 = bitcast [40 x i8]* %tmp_112_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_113)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_end_26:
  ; if playerName != adminName
  %tmp_114 = load i8*, i8** @global_playerName, align 8
  %tmp_115 = load i8*, i8** @global_adminName, align 8
  %tmp_117 = call i32 @strcmp(i8* %tmp_114, i8* %tmp_115)
  %tmp_116 = icmp ne i32 %tmp_117, 0
  br i1 %tmp_116, label %if_true_28, label %if_else_30
if_true_28:
  ; print "🎉 PASS: String inequality works!" (inline)
  %tmp_118_str = alloca [36 x i8], align 1
  store [36 x i8] c"\F0\9F\8E\89 PASS: String inequality works!\00", [36 x i8]* %tmp_118_str, align 1
  %tmp_119 = bitcast [36 x i8]* %tmp_118_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_119)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_else_30:
  ; print "❌ FAIL: String inequality failed!" (inline)
  %tmp_120_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9D\8C FAIL: String inequality failed!\00", [36 x i8]* %tmp_120_str, align 1
  %tmp_121 = bitcast [36 x i8]* %tmp_120_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_121)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_end_29:
  ; if emptyString == ""
  %tmp_122 = load i8*, i8** @global_emptyString, align 8
  %tmp_123 = getelementptr inbounds [1 x i8], [1 x i8]* @str_3, i64 0, i64 0
  %tmp_125 = call i32 @strcmp(i8* %tmp_122, i8* %tmp_123)
  %tmp_124 = icmp eq i32 %tmp_125, 0
  br i1 %tmp_124, label %if_true_31, label %if_else_33
if_true_31:
  ; print "🎉 PASS: Empty string comparison works!" (inline)
  %tmp_126_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Empty string comparison works!\00", [42 x i8]* %tmp_126_str, align 1
  %tmp_127 = bitcast [42 x i8]* %tmp_126_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_127)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_else_33:
  ; print "❌ FAIL: Empty string comparison failed!" (inline)
  %tmp_128_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Empty string comparison failed!\00", [42 x i8]* %tmp_128_str, align 1
  %tmp_129 = bitcast [42 x i8]* %tmp_128_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_end_32:
  ; if longString != emptyString
  %tmp_130 = load i8*, i8** @global_longString, align 8
  %tmp_131 = load i8*, i8** @global_emptyString, align 8
  %tmp_133 = call i32 @strcmp(i8* %tmp_130, i8* %tmp_131)
  %tmp_132 = icmp ne i32 %tmp_133, 0
  br i1 %tmp_132, label %if_true_34, label %if_else_36
if_true_34:
  ; print "🎉 PASS: Long vs empty string comparison works!" (inline)
  %tmp_134_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Long vs empty string comparison works!\00", [50 x i8]* %tmp_134_str, align 1
  %tmp_135 = bitcast [50 x i8]* %tmp_134_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_135)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_else_36:
  ; print "❌ FAIL: Long vs empty string comparison failed!" (inline)
  %tmp_136_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Long vs empty string comparison failed!\00", [50 x i8]* %tmp_136_str, align 1
  %tmp_137 = bitcast [50 x i8]* %tmp_136_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_137)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_end_35:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔘 === BOOLEAN LOGIC EXTREMES === 🔘" (inline)
  %tmp_138_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\94\98 === BOOLEAN LOGIC EXTREMES === \F0\9F\94\98\00", [41 x i8]* %tmp_138_str, align 1
  %tmp_139 = bitcast [41 x i8]* %tmp_138_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_139)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if isActive == true
  %tmp_140 = load i1, i1* @global_isActive, align 1
  %tmp_141 = zext i1 %tmp_140 to i32
  %tmp_142 = icmp eq i32 %tmp_141, 1
  br i1 %tmp_142, label %if_true_37, label %if_else_39
if_true_37:
  ; print "🎉 PASS: Boolean true comparison works!" (inline)
  %tmp_143_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Boolean true comparison works!\00", [42 x i8]* %tmp_143_str, align 1
  %tmp_144 = bitcast [42 x i8]* %tmp_143_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_144)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_else_39:
  ; print "❌ FAIL: Boolean true comparison failed!" (inline)
  %tmp_145_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Boolean true comparison failed!\00", [42 x i8]* %tmp_145_str, align 1
  %tmp_146 = bitcast [42 x i8]* %tmp_145_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_end_38:
  ; if isExpired == false
  %tmp_147 = load i1, i1* @global_isExpired, align 1
  %tmp_148 = zext i1 %tmp_147 to i32
  %tmp_149 = icmp eq i32 %tmp_148, 0
  br i1 %tmp_149, label %if_true_40, label %if_else_42
if_true_40:
  ; print "🎉 PASS: Boolean false comparison works!" (inline)
  %tmp_150_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\89 PASS: Boolean false comparison works!\00", [43 x i8]* %tmp_150_str, align 1
  %tmp_151 = bitcast [43 x i8]* %tmp_150_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_151)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_else_42:
  ; print "❌ FAIL: Boolean false comparison failed!" (inline)
  %tmp_152_str = alloca [43 x i8], align 1
  store [43 x i8] c"\E2\9D\8C FAIL: Boolean false comparison failed!\00", [43 x i8]* %tmp_152_str, align 1
  %tmp_153 = bitcast [43 x i8]* %tmp_152_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_153)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_end_41:
  ; if hasPermission != isGuest
  %tmp_154 = load i1, i1* @global_hasPermission, align 1
  %tmp_155 = zext i1 %tmp_154 to i32
  %tmp_156 = load i1, i1* @global_isGuest, align 1
  %tmp_157 = zext i1 %tmp_156 to i32
  %tmp_158 = icmp ne i32 %tmp_155, %tmp_157
  br i1 %tmp_158, label %if_true_43, label %if_else_45
if_true_43:
  ; print "🎉 PASS: Boolean inequality works!" (inline)
  %tmp_159_str = alloca [37 x i8], align 1
  store [37 x i8] c"\F0\9F\8E\89 PASS: Boolean inequality works!\00", [37 x i8]* %tmp_159_str, align 1
  %tmp_160 = bitcast [37 x i8]* %tmp_159_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_160)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_else_45:
  ; print "❌ FAIL: Boolean inequality failed!" (inline)
  %tmp_161_str = alloca [37 x i8], align 1
  store [37 x i8] c"\E2\9D\8C FAIL: Boolean inequality failed!\00", [37 x i8]* %tmp_161_str, align 1
  %tmp_162 = bitcast [37 x i8]* %tmp_161_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_162)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_end_44:
  ; if (isActive == hasPermission) == (debugMode == true)
  %tmp_163 = icmp eq i32 1, 1  ; Invalid comparison
  br i1 %tmp_163, label %if_true_46, label %if_else_48
if_true_46:
  ; print "🎉 PASS: Complex boolean comparison works!" (inline)
  %tmp_164_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Complex boolean comparison works!\00", [45 x i8]* %tmp_164_str, align 1
  %tmp_165 = bitcast [45 x i8]* %tmp_164_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_165)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_else_48:
  ; print "❌ FAIL: Complex boolean comparison failed!" (inline)
  %tmp_166_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Complex boolean comparison failed!\00", [45 x i8]* %tmp_166_str, align 1
  %tmp_167 = bitcast [45 x i8]* %tmp_166_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_167)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_end_47:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌈 === MIXED TYPE COMPARISON CHALLENGES === 🌈" (inline)
  %tmp_168_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\8C\88 === MIXED TYPE COMPARISON CHALLENGES === \F0\9F\8C\88\00", [51 x i8]* %tmp_168_str, align 1
  %tmp_169 = bitcast [51 x i8]* %tmp_168_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_169)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > pi
  %tmp_170 = load i32, i32* @global_score, align 4
  %tmp_171 = load float, float* @global_pi, align 4
  %tmp_172 = fcmp ogt float %tmp_170, %tmp_171
  br i1 %tmp_172, label %if_true_49, label %if_else_51
if_true_49:
  ; print "🎉 PASS: Integer vs Float comparison works!" (inline)
  %tmp_173_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Integer vs Float comparison works!\00", [46 x i8]* %tmp_173_str, align 1
  %tmp_174 = bitcast [46 x i8]* %tmp_173_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_174)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_else_51:
  ; print "❌ FAIL: Integer vs Float comparison failed!" (inline)
  %tmp_175_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Integer vs Float comparison failed!\00", [46 x i8]* %tmp_175_str, align 1
  %tmp_176 = bitcast [46 x i8]* %tmp_175_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_176)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_end_50:
  ; if (score + 15) > (pi * 25)
  %tmp_177 = load i32, i32* @global_score, align 4
  %tmp_178 = add i32 %tmp_177, 15
  %tmp_179 = load float, float* @global_pi, align 4
  %tmp_180 = sitofp i32 25 to float
  %tmp_181 = fmul float %tmp_179, %tmp_180
  %tmp_182 = fcmp ogt float %tmp_178, %tmp_181
  br i1 %tmp_182, label %if_true_52, label %if_else_54
if_true_52:
  ; print "🎉 PASS: Complex mixed type comparison works!" (inline)
  %tmp_183_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\89 PASS: Complex mixed type comparison works!\00", [48 x i8]* %tmp_183_str, align 1
  %tmp_184 = bitcast [48 x i8]* %tmp_183_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_184)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_else_54:
  ; print "❌ FAIL: Complex mixed type comparison failed!" (inline)
  %tmp_185_str = alloca [48 x i8], align 1
  store [48 x i8] c"\E2\9D\8C FAIL: Complex mixed type comparison failed!\00", [48 x i8]* %tmp_185_str, align 1
  %tmp_186 = bitcast [48 x i8]* %tmp_185_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_186)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_end_53:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "⚡ === EDGE CASE SCENARIOS === ⚡" (inline)
  %tmp_187_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9A\A1 === EDGE CASE SCENARIOS === \E2\9A\A1\00", [36 x i8]* %tmp_187_str, align 1
  %tmp_188 = bitcast [36 x i8]* %tmp_187_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_188)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if minScore == 0
  %tmp_189 = load i32, i32* @global_minScore, align 4
  %tmp_190 = icmp eq i32 %tmp_189, 0
  br i1 %tmp_190, label %if_true_55, label %if_else_57
if_true_55:
  ; print "🎉 PASS: Zero equality works!" (inline)
  %tmp_191_str = alloca [32 x i8], align 1
  store [32 x i8] c"\F0\9F\8E\89 PASS: Zero equality works!\00", [32 x i8]* %tmp_191_str, align 1
  %tmp_192 = bitcast [32 x i8]* %tmp_191_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_192)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_else_57:
  ; print "❌ FAIL: Zero equality failed!" (inline)
  %tmp_193_str = alloca [32 x i8], align 1
  store [32 x i8] c"\E2\9D\8C FAIL: Zero equality failed!\00", [32 x i8]* %tmp_193_str, align 1
  %tmp_194 = bitcast [32 x i8]* %tmp_193_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_194)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_end_56:
  ; if zero == 0.0
  %tmp_195 = load float, float* @global_zero, align 4
  %tmp_196 = bitcast i32 0 to float
  %tmp_197 = fcmp oeq float %tmp_195, %tmp_196
  br i1 %tmp_197, label %if_true_58, label %if_else_60
if_true_58:
  ; print "🎉 PASS: Float zero comparison works!" (inline)
  %tmp_198_str = alloca [40 x i8], align 1
  store [40 x i8] c"\F0\9F\8E\89 PASS: Float zero comparison works!\00", [40 x i8]* %tmp_198_str, align 1
  %tmp_199 = bitcast [40 x i8]* %tmp_198_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_199)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_59
if_else_60:
  ; print "❌ FAIL: Float zero comparison failed!" (inline)
  %tmp_200_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9D\8C FAIL: Float zero comparison failed!\00", [40 x i8]* %tmp_200_str, align 1
  %tmp_201 = bitcast [40 x i8]* %tmp_200_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_201)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_59
if_end_59:
  ; if maxScore >= perfectScore
  %tmp_202 = load i32, i32* @global_maxScore, align 4
  %tmp_203 = load i32, i32* @global_perfectScore, align 4
  %tmp_204 = icmp sge i32 %tmp_202, %tmp_203
  br i1 %tmp_204, label %if_true_61, label %if_else_63
if_true_61:
  ; print "🎉 PASS: Maximum value comparison works!" (inline)
  %tmp_205_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\89 PASS: Maximum value comparison works!\00", [43 x i8]* %tmp_205_str, align 1
  %tmp_206 = bitcast [43 x i8]* %tmp_205_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_206)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_else_63:
  ; print "❌ FAIL: Maximum value comparison failed!" (inline)
  %tmp_207_str = alloca [43 x i8], align 1
  store [43 x i8] c"\E2\9D\8C FAIL: Maximum value comparison failed!\00", [43 x i8]* %tmp_207_str, align 1
  %tmp_208 = bitcast [43 x i8]* %tmp_207_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_208)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_end_62:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏃‍♂️ === PERFORMANCE STRESS TEST === 🏃‍♂️" (inline)
  %tmp_209_str = alloca [60 x i8], align 1
  store [60 x i8] c"\F0\9F\8F\83\E2\80\8D\E2\99\82\EF\B8\8F === PERFORMANCE STRESS TEST === \F0\9F\8F\83\E2\80\8D\E2\99\82\EF\B8\8F\00", [60 x i8]* %tmp_209_str, align 1
  %tmp_210 = bitcast [60 x i8]* %tmp_209_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_210)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > passingGrade
  %tmp_211 = load i32, i32* @global_score, align 4
  %tmp_212 = load i32, i32* @global_passingGrade, align 4
  %tmp_213 = icmp sgt i32 %tmp_211, %tmp_212
  br i1 %tmp_213, label %if_true_64, label %if_else_66
if_true_64:
  ; if maxScore > score
  %tmp_214 = load i32, i32* @global_maxScore, align 4
  %tmp_215 = load i32, i32* @global_score, align 4
  %tmp_216 = icmp sgt i32 %tmp_214, %tmp_215
  br i1 %tmp_216, label %if_true_67, label %if_else_69
if_true_67:
  ; if pi > e
  %tmp_217 = load float, float* @global_pi, align 4
  %tmp_218 = load float, float* @global_e, align 4
  %tmp_219 = fcmp ogt float %tmp_217, %tmp_218
  br i1 %tmp_219, label %if_true_70, label %if_else_72
if_true_70:
  ; if playerName != adminName
  %tmp_220 = load i8*, i8** @global_playerName, align 8
  %tmp_221 = load i8*, i8** @global_adminName, align 8
  %tmp_223 = call i32 @strcmp(i8* %tmp_220, i8* %tmp_221)
  %tmp_222 = icmp ne i32 %tmp_223, 0
  br i1 %tmp_222, label %if_true_73, label %if_else_75
if_true_73:
  ; if isActive == true
  %tmp_224 = load i1, i1* @global_isActive, align 1
  %tmp_225 = zext i1 %tmp_224 to i32
  %tmp_226 = icmp eq i32 %tmp_225, 1
  br i1 %tmp_226, label %if_true_76, label %if_else_78
if_true_76:
  ; print "🎉 PASS: Nested comparison chain works!" (inline)
  %tmp_227_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Nested comparison chain works!\00", [42 x i8]* %tmp_227_str, align 1
  %tmp_228 = bitcast [42 x i8]* %tmp_227_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_228)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_77
if_else_78:
  ; print "❌ FAIL: Nested comparison chain failed at level 5!" (inline)
  %tmp_229_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 5!\00", [53 x i8]* %tmp_229_str, align 1
  %tmp_230 = bitcast [53 x i8]* %tmp_229_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_230)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_77
if_end_77:
  br label %if_end_74
if_else_75:
  ; print "❌ FAIL: Nested comparison chain failed at level 4!" (inline)
  %tmp_231_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 4!\00", [53 x i8]* %tmp_231_str, align 1
  %tmp_232 = bitcast [53 x i8]* %tmp_231_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_232)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_74
if_end_74:
  br label %if_end_71
if_else_72:
  ; print "❌ FAIL: Nested comparison chain failed at level 3!" (inline)
  %tmp_233_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 3!\00", [53 x i8]* %tmp_233_str, align 1
  %tmp_234 = bitcast [53 x i8]* %tmp_233_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_234)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_71
if_end_71:
  br label %if_end_68
if_else_69:
  ; print "❌ FAIL: Nested comparison chain failed at level 2!" (inline)
  %tmp_235_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 2!\00", [53 x i8]* %tmp_235_str, align 1
  %tmp_236 = bitcast [53 x i8]* %tmp_235_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_236)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_68
if_end_68:
  br label %if_end_65
if_else_66:
  ; print "❌ FAIL: Nested comparison chain failed at level 1!" (inline)
  %tmp_237_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 1!\00", [53 x i8]* %tmp_237_str, align 1
  %tmp_238 = bitcast [53 x i8]* %tmp_237_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_238)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_end_65:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 === MATHEMATICAL EXPRESSION OLYMPICS === 🏆" (inline)
  %tmp_239_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\8F\86 === MATHEMATICAL EXPRESSION OLYMPICS === \F0\9F\8F\86\00", [51 x i8]* %tmp_239_str, align 1
  %tmp_240 = bitcast [51 x i8]* %tmp_239_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_240)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int fib1 = 1
  store i32 1, i32* @global_fib1, align 4
  ; int fib2 = 1
  store i32 1, i32* @global_fib2, align 4
  ; int fib3 = 2
  store i32 2, i32* @global_fib3, align 4
  ; int fib4 = 3
  store i32 3, i32* @global_fib4, align 4
  ; int fib5 = 5
  store i32 5, i32* @global_fib5, align 4
  ; if (fib1 + fib2) == fib3
  %tmp_241 = load i32, i32* @global_fib1, align 4
  %tmp_242 = load i32, i32* @global_fib2, align 4
  %tmp_243 = add i32 %tmp_241, %tmp_242
  %tmp_244 = load i32, i32* @global_fib3, align 4
  %tmp_245 = icmp eq i32 %tmp_243, %tmp_244
  br i1 %tmp_245, label %if_true_79, label %if_else_81
if_true_79:
  ; print "🎉 PASS: Fibonacci sequence comparison 1 works!" (inline)
  %tmp_246_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Fibonacci sequence comparison 1 works!\00", [50 x i8]* %tmp_246_str, align 1
  %tmp_247 = bitcast [50 x i8]* %tmp_246_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_247)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_80
if_else_81:
  ; print "❌ FAIL: Fibonacci sequence comparison 1 failed!" (inline)
  %tmp_248_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Fibonacci sequence comparison 1 failed!\00", [50 x i8]* %tmp_248_str, align 1
  %tmp_249 = bitcast [50 x i8]* %tmp_248_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_249)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_80
if_end_80:
  ; if (fib3 + fib4) == (fib5 + 0)
  %tmp_250 = load i32, i32* @global_fib3, align 4
  %tmp_251 = load i32, i32* @global_fib4, align 4
  %tmp_252 = add i32 %tmp_250, %tmp_251
  %tmp_253 = load i32, i32* @global_fib5, align 4
  %tmp_254 = add i32 %tmp_253, 0
  %tmp_255 = icmp eq i32 %tmp_252, %tmp_254
  br i1 %tmp_255, label %if_true_82, label %if_else_84
if_true_82:
  ; print "🎉 PASS: Fibonacci sequence comparison 2 works!" (inline)
  %tmp_256_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Fibonacci sequence comparison 2 works!\00", [50 x i8]* %tmp_256_str, align 1
  %tmp_257 = bitcast [50 x i8]* %tmp_256_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_257)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_83
if_else_84:
  ; print "❌ FAIL: Fibonacci sequence comparison 2 failed!" (inline)
  %tmp_258_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Fibonacci sequence comparison 2 failed!\00", [50 x i8]* %tmp_258_str, align 1
  %tmp_259 = bitcast [50 x i8]* %tmp_258_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_259)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_83
if_end_83:
  ; int prime1 = 2
  store i32 2, i32* @global_prime1, align 4
  ; int prime2 = 3
  store i32 3, i32* @global_prime2, align 4
  ; int prime3 = 5
  store i32 5, i32* @global_prime3, align 4
  ; int prime4 = 7
  store i32 7, i32* @global_prime4, align 4
  ; if (prime1 * prime2) < (prime3 * prime4)
  %tmp_260 = load i32, i32* @global_prime1, align 4
  %tmp_261 = load i32, i32* @global_prime2, align 4
  %tmp_262 = mul i32 %tmp_260, %tmp_261
  %tmp_263 = load i32, i32* @global_prime3, align 4
  %tmp_264 = load i32, i32* @global_prime4, align 4
  %tmp_265 = mul i32 %tmp_263, %tmp_264
  %tmp_266 = icmp slt i32 %tmp_262, %tmp_265
  br i1 %tmp_266, label %if_true_85, label %if_else_87
if_true_85:
  ; print "🎉 PASS: Prime multiplication comparison works!" (inline)
  %tmp_267_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Prime multiplication comparison works!\00", [50 x i8]* %tmp_267_str, align 1
  %tmp_268 = bitcast [50 x i8]* %tmp_267_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_268)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_86
if_else_87:
  ; print "❌ FAIL: Prime multiplication comparison failed!" (inline)
  %tmp_269_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Prime multiplication comparison failed!\00", [50 x i8]* %tmp_269_str, align 1
  %tmp_270 = bitcast [50 x i8]* %tmp_269_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_270)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_86
if_end_86:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "👑 === FINAL BOSS CHALLENGE === 👑" (inline)
  %tmp_271_str = alloca [39 x i8], align 1
  store [39 x i8] c"\F0\9F\91\91 === FINAL BOSS CHALLENGE === \F0\9F\91\91\00", [39 x i8]* %tmp_271_str, align 1
  %tmp_272 = bitcast [39 x i8]* %tmp_271_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_272)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (((score * 2) + (fib5 * prime1)) >= ((maxScore + passingGrade) - (minScore * 2))) == (((pi + e) > (goldenRatio * 2)) == ((playerName != emptyString) == (isActive != isExpired)))
  %tmp_273 = load i32, i32* @global_score, align 4
  %tmp_274 = mul i32 %tmp_273, 2
  %tmp_275 = load i32, i32* @global_fib5, align 4
  %tmp_276 = load i32, i32* @global_prime1, align 4
  %tmp_277 = mul i32 %tmp_275, %tmp_276
  %tmp_279 = add i32 0, 0  ; complex expression fallback
  %tmp_280 = load i32, i32* @global_maxScore, align 4
  %tmp_281 = load i32, i32* @global_passingGrade, align 4
  %tmp_282 = add i32 %tmp_280, %tmp_281
  %tmp_283 = load i32, i32* @global_minScore, align 4
  %tmp_284 = mul i32 %tmp_283, 2
  %tmp_285 = sub i32 %tmp_282, %tmp_284
  %tmp_301 = add i32 0, 0  ; complex expression fallback
  %tmp_302 = icmp sge i32 %tmp_279, %tmp_301
  br i1 %tmp_302, label %if_true_88, label %if_else_90
if_true_88:
  ; print "🎉🎉🎉 ULTIMATE VICTORY! The most complex comparison ..." (inline)
  %tmp_303_str = alloca [96 x i8], align 1
  store [96 x i8] c"\F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89 ULTIMATE VICTORY! The most complex comparison in Dolet history works! \F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89\00", [96 x i8]* %tmp_303_str, align 1
  %tmp_304 = bitcast [96 x i8]* %tmp_303_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_304)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 Dolet has achieved LEGENDARY status in compariso..." (inline)
  %tmp_305_str = alloca [72 x i8], align 1
  store [72 x i8] c"\F0\9F\8F\86 Dolet has achieved LEGENDARY status in comparison operations! \F0\9F\8F\86\00", [72 x i8]* %tmp_305_str, align 1
  %tmp_306 = bitcast [72 x i8]* %tmp_305_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_306)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_89
if_else_90:
  ; print "💀 ULTIMATE DEFEAT! The final boss comparison faile..." (inline)
  %tmp_307_str = alloca [56 x i8], align 1
  store [56 x i8] c"\F0\9F\92\80 ULTIMATE DEFEAT! The final boss comparison failed!\00", [56 x i8]* %tmp_307_str, align 1
  %tmp_308 = bitcast [56 x i8]* %tmp_307_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_308)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔧 Time to debug the most complex expression ever c..." (inline)
  %tmp_309_str = alloca [61 x i8], align 1
  store [61 x i8] c"\F0\9F\94\A7 Time to debug the most complex expression ever created!\00", [61 x i8]* %tmp_309_str, align 1
  %tmp_310 = bitcast [61 x i8]* %tmp_309_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_310)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_89
if_end_89:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎯 === CHALLENGE COMPLETE === 🎯" (inline)
  %tmp_311_str = alloca [37 x i8], align 1
  store [37 x i8] c"\F0\9F\8E\AF === CHALLENGE COMPLETE === \F0\9F\8E\AF\00", [37 x i8]* %tmp_311_str, align 1
  %tmp_312 = bitcast [37 x i8]* %tmp_311_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_312)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 If you see this message, Dolet has survived the ..." (inline)
  %tmp_313_str = alloca [84 x i8], align 1
  store [84 x i8] c"\F0\9F\93\8A If you see this message, Dolet has survived the ultimate comparison challenge!\00", [84 x i8]* %tmp_313_str, align 1
  %tmp_314 = bitcast [84 x i8]* %tmp_313_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_314)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🚀 Ready for production use with confidence!" (inline)
  %tmp_315_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\9A\80 Ready for production use with confidence!\00", [47 x i8]* %tmp_315_str, align 1
  %tmp_316 = bitcast [47 x i8]* %tmp_315_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_316)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎉 Thank you for testing Dolet's comparison capabil..." (inline)
  %tmp_317_str = alloca [65 x i8], align 1
  store [65 x i8] c"\F0\9F\8E\89 Thank you for testing Dolet's comparison capabilities! \F0\9F\8E\89\00", [65 x i8]* %tmp_317_str, align 1
  %tmp_318 = bitcast [65 x i8]* %tmp_317_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_318)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌟 === BONUS: UNICODE & EMOJI TESTS === 🌟" (inline)
  %tmp_319_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\8C\9F === BONUS: UNICODE & EMOJI TESTS === \F0\9F\8C\9F\00", [47 x i8]* %tmp_319_str, align 1
  %tmp_320 = bitcast [47 x i8]* %tmp_319_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_320)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string emoji1 = "🎉"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_5, i64 0, i64 0), i8** @global_emoji1, align 8
  ; string emoji2 = "🚀"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_6, i64 0, i64 0), i8** @global_emoji2, align 8
  ; string emoji3 = "🎉"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_5, i64 0, i64 0), i8** @global_emoji3, align 8
  ; string arabic = "مرحبا"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_8, i64 0, i64 0), i8** @global_arabic, align 8
  ; string english = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_9, i64 0, i64 0), i8** @global_english, align 8
  ; if emoji1 == emoji3
  %tmp_321 = load i8*, i8** @global_emoji1, align 8
  %tmp_322 = load i8*, i8** @global_emoji3, align 8
  %tmp_324 = call i32 @strcmp(i8* %tmp_321, i8* %tmp_322)
  %tmp_323 = icmp eq i32 %tmp_324, 0
  br i1 %tmp_323, label %if_true_91, label %if_else_93
if_true_91:
  ; print "🎉 PASS: Emoji equality comparison works!" (inline)
  %tmp_325_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\8E\89 PASS: Emoji equality comparison works!\00", [44 x i8]* %tmp_325_str, align 1
  %tmp_326 = bitcast [44 x i8]* %tmp_325_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_326)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_92
if_else_93:
  ; print "❌ FAIL: Emoji equality comparison failed!" (inline)
  %tmp_327_str = alloca [44 x i8], align 1
  store [44 x i8] c"\E2\9D\8C FAIL: Emoji equality comparison failed!\00", [44 x i8]* %tmp_327_str, align 1
  %tmp_328 = bitcast [44 x i8]* %tmp_327_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_328)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_92
if_end_92:
  ; if emoji1 != emoji2
  %tmp_329 = load i8*, i8** @global_emoji1, align 8
  %tmp_330 = load i8*, i8** @global_emoji2, align 8
  %tmp_332 = call i32 @strcmp(i8* %tmp_329, i8* %tmp_330)
  %tmp_331 = icmp ne i32 %tmp_332, 0
  br i1 %tmp_331, label %if_true_94, label %if_else_96
if_true_94:
  ; print "🎉 PASS: Different emoji comparison works!" (inline)
  %tmp_333_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Different emoji comparison works!\00", [45 x i8]* %tmp_333_str, align 1
  %tmp_334 = bitcast [45 x i8]* %tmp_333_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_334)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_95
if_else_96:
  ; print "❌ FAIL: Different emoji comparison failed!" (inline)
  %tmp_335_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Different emoji comparison failed!\00", [45 x i8]* %tmp_335_str, align 1
  %tmp_336 = bitcast [45 x i8]* %tmp_335_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_336)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_95
if_end_95:
  ; if arabic != english
  %tmp_337 = load i8*, i8** @global_arabic, align 8
  %tmp_338 = load i8*, i8** @global_english, align 8
  %tmp_340 = call i32 @strcmp(i8* %tmp_337, i8* %tmp_338)
  %tmp_339 = icmp ne i32 %tmp_340, 0
  br i1 %tmp_339, label %if_true_97, label %if_else_99
if_true_97:
  ; print "🎉 PASS: Unicode language comparison works!" (inline)
  %tmp_341_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Unicode language comparison works!\00", [46 x i8]* %tmp_341_str, align 1
  %tmp_342 = bitcast [46 x i8]* %tmp_341_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_342)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_98
if_else_99:
  ; print "❌ FAIL: Unicode language comparison failed!" (inline)
  %tmp_343_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Unicode language comparison failed!\00", [46 x i8]* %tmp_343_str, align 1
  %tmp_344 = bitcast [46 x i8]* %tmp_343_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_344)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_98
if_end_98:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔥 === EXTREME NESTING CHALLENGE === 🔥" (inline)
  %tmp_345_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\94\A5 === EXTREME NESTING CHALLENGE === \F0\9F\94\A5\00", [44 x i8]* %tmp_345_str, align 1
  %tmp_346 = bitcast [44 x i8]* %tmp_345_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_346)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > 50
  %tmp_347 = load i32, i32* @global_score, align 4
  %tmp_348 = icmp sgt i32 %tmp_347, 50
  br i1 %tmp_348, label %if_true_100, label %if_else_102
if_true_100:
  ; if maxScore > 90
  %tmp_349 = load i32, i32* @global_maxScore, align 4
  %tmp_350 = icmp sgt i32 %tmp_349, 90
  br i1 %tmp_350, label %if_true_103, label %if_else_105
if_true_103:
  ; if pi > 3.0
  %tmp_351 = load float, float* @global_pi, align 4
  %tmp_352 = bitcast i32 1077936128 to float
  %tmp_353 = fcmp ogt float %tmp_351, %tmp_352
  br i1 %tmp_353, label %if_true_106, label %if_else_108
if_true_106:
  ; if playerName == "Ahmed"
  %tmp_354 = load i8*, i8** @global_playerName, align 8
  %tmp_355 = getelementptr inbounds [6 x i8], [6 x i8]* @str_0, i64 0, i64 0
  %tmp_357 = call i32 @strcmp(i8* %tmp_354, i8* %tmp_355)
  %tmp_356 = icmp eq i32 %tmp_357, 0
  br i1 %tmp_356, label %if_true_109, label %if_else_111
if_true_109:
  ; if isActive == true
  %tmp_358 = load i1, i1* @global_isActive, align 1
  %tmp_359 = zext i1 %tmp_358 to i32
  %tmp_360 = icmp eq i32 %tmp_359, 1
  br i1 %tmp_360, label %if_true_112, label %if_else_114
if_true_112:
  ; if fib5 == 5
  %tmp_361 = load i32, i32* @global_fib5, align 4
  %tmp_362 = icmp eq i32 %tmp_361, 5
  br i1 %tmp_362, label %if_true_115, label %if_else_117
if_true_115:
  ; if prime4 > prime3
  %tmp_363 = load i32, i32* @global_prime4, align 4
  %tmp_364 = load i32, i32* @global_prime3, align 4
  %tmp_365 = icmp sgt i32 %tmp_363, %tmp_364
  br i1 %tmp_365, label %if_true_118, label %if_else_120
if_true_118:
  ; if emoji1 == "🎉"
  %tmp_366 = load i8*, i8** @global_emoji1, align 8
  %tmp_367 = getelementptr inbounds [13 x i8], [13 x i8]* @str_5, i64 0, i64 0
  %tmp_369 = call i32 @strcmp(i8* %tmp_366, i8* %tmp_367)
  %tmp_368 = icmp eq i32 %tmp_369, 0
  br i1 %tmp_368, label %if_true_121, label %if_else_123
if_true_121:
  ; if (score + 10) > 90
  %tmp_370 = load i32, i32* @global_score, align 4
  %tmp_371 = add i32 %tmp_370, 10
  %tmp_372 = icmp sgt i32 %tmp_371, 90
  br i1 %tmp_372, label %if_true_124, label %if_else_126
if_true_124:
  ; if (pi * 2) > 6.0
  %tmp_373 = load float, float* @global_pi, align 4
  %tmp_374 = sitofp i32 2 to float
  %tmp_375 = fmul float %tmp_373, %tmp_374
  %tmp_376 = bitcast i32 1086324736 to float
  %tmp_377 = fcmp ogt float %tmp_375, %tmp_376
  br i1 %tmp_377, label %if_true_127, label %if_else_129
if_true_127:
  ; print "🔥🔥🔥 LEGENDARY! 10-level nested comparison SUCCESS!..." (inline)
  %tmp_378_str = alloca [73 x i8], align 1
  store [73 x i8] c"\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5 LEGENDARY! 10-level nested comparison SUCCESS! \F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\00", [73 x i8]* %tmp_378_str, align 1
  %tmp_379 = bitcast [73 x i8]* %tmp_378_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_379)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 Dolet has achieved GODLIKE comparison nesting! 🏆" (inline)
  %tmp_380_str = alloca [57 x i8], align 1
  store [57 x i8] c"\F0\9F\8F\86 Dolet has achieved GODLIKE comparison nesting! \F0\9F\8F\86\00", [57 x i8]* %tmp_380_str, align 1
  %tmp_381 = bitcast [57 x i8]* %tmp_380_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_381)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_128
if_else_129:
  ; print "💀 Failed at level 10: Float multiplication" (inline)
  %tmp_382_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\92\80 Failed at level 10: Float multiplication\00", [46 x i8]* %tmp_382_str, align 1
  %tmp_383 = bitcast [46 x i8]* %tmp_382_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_383)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_128
if_end_128:
  br label %if_end_125
if_else_126:
  ; print "💀 Failed at level 9: Integer addition" (inline)
  %tmp_384_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 9: Integer addition\00", [41 x i8]* %tmp_384_str, align 1
  %tmp_385 = bitcast [41 x i8]* %tmp_384_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_385)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_125
if_end_125:
  br label %if_end_122
if_else_123:
  ; print "💀 Failed at level 8: Emoji comparison" (inline)
  %tmp_386_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 8: Emoji comparison\00", [41 x i8]* %tmp_386_str, align 1
  %tmp_387 = bitcast [41 x i8]* %tmp_386_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_387)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_122
if_end_122:
  br label %if_end_119
if_else_120:
  ; print "💀 Failed at level 7: Prime comparison" (inline)
  %tmp_388_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 7: Prime comparison\00", [41 x i8]* %tmp_388_str, align 1
  %tmp_389 = bitcast [41 x i8]* %tmp_388_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_389)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_119
if_end_119:
  br label %if_end_116
if_else_117:
  ; print "💀 Failed at level 6: Fibonacci comparison" (inline)
  %tmp_390_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\92\80 Failed at level 6: Fibonacci comparison\00", [45 x i8]* %tmp_390_str, align 1
  %tmp_391 = bitcast [45 x i8]* %tmp_390_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_391)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_116
if_end_116:
  br label %if_end_113
if_else_114:
  ; print "💀 Failed at level 5: Boolean comparison" (inline)
  %tmp_392_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\92\80 Failed at level 5: Boolean comparison\00", [43 x i8]* %tmp_392_str, align 1
  %tmp_393 = bitcast [43 x i8]* %tmp_392_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_393)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_113
if_end_113:
  br label %if_end_110
if_else_111:
  ; print "💀 Failed at level 4: String comparison" (inline)
  %tmp_394_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\92\80 Failed at level 4: String comparison\00", [42 x i8]* %tmp_394_str, align 1
  %tmp_395 = bitcast [42 x i8]* %tmp_394_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_395)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_110
if_end_110:
  br label %if_end_107
if_else_108:
  ; print "💀 Failed at level 3: Pi comparison" (inline)
  %tmp_396_str = alloca [38 x i8], align 1
  store [38 x i8] c"\F0\9F\92\80 Failed at level 3: Pi comparison\00", [38 x i8]* %tmp_396_str, align 1
  %tmp_397 = bitcast [38 x i8]* %tmp_396_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_397)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_107
if_end_107:
  br label %if_end_104
if_else_105:
  ; print "💀 Failed at level 2: MaxScore comparison" (inline)
  %tmp_398_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\92\80 Failed at level 2: MaxScore comparison\00", [44 x i8]* %tmp_398_str, align 1
  %tmp_399 = bitcast [44 x i8]* %tmp_398_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_399)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_104
if_end_104:
  br label %if_end_101
if_else_102:
  ; print "💀 Failed at level 1: Score comparison" (inline)
  %tmp_400_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 1: Score comparison\00", [41 x i8]* %tmp_400_str, align 1
  %tmp_401 = bitcast [41 x i8]* %tmp_400_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_401)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_101
if_end_101:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 === ARRAY-LIKE COMPARISONS === 📊" (inline)
  %tmp_402_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\93\8A === ARRAY-LIKE COMPARISONS === \F0\9F\93\8A\00", [41 x i8]* %tmp_402_str, align 1
  %tmp_403 = bitcast [41 x i8]* %tmp_402_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_403)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int arr1 = 10
  store i32 10, i32* @global_arr1, align 4
  ; int arr2 = 20
  store i32 20, i32* @global_arr2, align 4
  ; int arr3 = 30
  store i32 30, i32* @global_arr3, align 4
  ; int arr4 = 40
  store i32 40, i32* @global_arr4, align 4
  ; int arr5 = 50
  store i32 50, i32* @global_arr5, align 4
  ; if arr1 < arr2
  %tmp_404 = load i32, i32* @global_arr1, align 4
  %tmp_405 = load i32, i32* @global_arr2, align 4
  %tmp_406 = icmp slt i32 %tmp_404, %tmp_405
  br i1 %tmp_406, label %if_true_130, label %if_else_132
if_true_130:
  ; if arr2 < arr3
  %tmp_407 = load i32, i32* @global_arr2, align 4
  %tmp_408 = load i32, i32* @global_arr3, align 4
  %tmp_409 = icmp slt i32 %tmp_407, %tmp_408
  br i1 %tmp_409, label %if_true_133, label %if_else_135
if_true_133:
  ; if arr3 < arr4
  %tmp_410 = load i32, i32* @global_arr3, align 4
  %tmp_411 = load i32, i32* @global_arr4, align 4
  %tmp_412 = icmp slt i32 %tmp_410, %tmp_411
  br i1 %tmp_412, label %if_true_136, label %if_else_138
if_true_136:
  ; if arr4 < arr5
  %tmp_413 = load i32, i32* @global_arr4, align 4
  %tmp_414 = load i32, i32* @global_arr5, align 4
  %tmp_415 = icmp slt i32 %tmp_413, %tmp_414
  br i1 %tmp_415, label %if_true_139, label %if_else_141
if_true_139:
  ; print "🎉 PASS: Sequential array comparison works!" (inline)
  %tmp_416_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Sequential array comparison works!\00", [46 x i8]* %tmp_416_str, align 1
  %tmp_417 = bitcast [46 x i8]* %tmp_416_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_417)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_140
if_else_141:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_418_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 4-5!\00", [62 x i8]* %tmp_418_str, align 1
  %tmp_419 = bitcast [62 x i8]* %tmp_418_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_419)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_140
if_end_140:
  br label %if_end_137
if_else_138:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_420_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 3-4!\00", [62 x i8]* %tmp_420_str, align 1
  %tmp_421 = bitcast [62 x i8]* %tmp_420_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_421)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_137
if_end_137:
  br label %if_end_134
if_else_135:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_422_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 2-3!\00", [62 x i8]* %tmp_422_str, align 1
  %tmp_423 = bitcast [62 x i8]* %tmp_422_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_423)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_134
if_end_134:
  br label %if_end_131
if_else_132:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_424_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 1-2!\00", [62 x i8]* %tmp_424_str, align 1
  %tmp_425 = bitcast [62 x i8]* %tmp_424_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_425)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_131
if_end_131:
  ; if (arr1 + arr2) < (arr4 + arr5)
  %tmp_426 = load i32, i32* @global_arr1, align 4
  %tmp_427 = load i32, i32* @global_arr2, align 4
  %tmp_428 = add i32 %tmp_426, %tmp_427
  %tmp_429 = load i32, i32* @global_arr4, align 4
  %tmp_430 = load i32, i32* @global_arr5, align 4
  %tmp_431 = add i32 %tmp_429, %tmp_430
  %tmp_432 = icmp slt i32 %tmp_428, %tmp_431
  br i1 %tmp_432, label %if_true_142, label %if_else_144
if_true_142:
  ; print "🎉 PASS: Array sum comparison works!" (inline)
  %tmp_433_str = alloca [39 x i8], align 1
  store [39 x i8] c"\F0\9F\8E\89 PASS: Array sum comparison works!\00", [39 x i8]* %tmp_433_str, align 1
  %tmp_434 = bitcast [39 x i8]* %tmp_433_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_434)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_143
if_else_144:
  ; print "❌ FAIL: Array sum comparison failed!" (inline)
  %tmp_435_str = alloca [39 x i8], align 1
  store [39 x i8] c"\E2\9D\8C FAIL: Array sum comparison failed!\00", [39 x i8]* %tmp_435_str, align 1
  %tmp_436 = bitcast [39 x i8]* %tmp_435_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_436)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_143
if_end_143:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔬 === SCIENTIFIC NOTATION SIMULATION === 🔬" (inline)
  %tmp_437_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\94\AC === SCIENTIFIC NOTATION SIMULATION === \F0\9F\94\AC\00", [49 x i8]* %tmp_437_str, align 1
  %tmp_438 = bitcast [49 x i8]* %tmp_437_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_438)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float large1 = 1000000.0
  %tmp_439 = bitcast i32 1232348160 to float
  store float %tmp_439, float* @global_large1, align 4
  ; float large2 = 2000000.0
  %tmp_440 = bitcast i32 1240736768 to float
  store float %tmp_440, float* @global_large2, align 4
  ; float small1 = 0.000001
  %tmp_441 = bitcast i32 897988541 to float
  store float %tmp_441, float* @global_small1, align 4
  ; float small2 = 0.000002
  %tmp_442 = bitcast i32 906377149 to float
  store float %tmp_442, float* @global_small2, align 4
  ; if large1 < large2
  %tmp_443 = load float, float* @global_large1, align 4
  %tmp_444 = load float, float* @global_large2, align 4
  %tmp_445 = fcmp olt float %tmp_443, %tmp_444
  br i1 %tmp_445, label %if_true_145, label %if_else_147
if_true_145:
  ; print "🎉 PASS: Large number comparison works!" (inline)
  %tmp_446_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Large number comparison works!\00", [42 x i8]* %tmp_446_str, align 1
  %tmp_447 = bitcast [42 x i8]* %tmp_446_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_447)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_146
if_else_147:
  ; print "❌ FAIL: Large number comparison failed!" (inline)
  %tmp_448_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Large number comparison failed!\00", [42 x i8]* %tmp_448_str, align 1
  %tmp_449 = bitcast [42 x i8]* %tmp_448_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_449)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_146
if_end_146:
  ; if small1 < small2
  %tmp_450 = load float, float* @global_small1, align 4
  %tmp_451 = load float, float* @global_small2, align 4
  %tmp_452 = fcmp olt float %tmp_450, %tmp_451
  br i1 %tmp_452, label %if_true_148, label %if_else_150
if_true_148:
  ; print "🎉 PASS: Small number comparison works!" (inline)
  %tmp_453_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Small number comparison works!\00", [42 x i8]* %tmp_453_str, align 1
  %tmp_454 = bitcast [42 x i8]* %tmp_453_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_454)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_149
if_else_150:
  ; print "❌ FAIL: Small number comparison failed!" (inline)
  %tmp_455_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Small number comparison failed!\00", [42 x i8]* %tmp_455_str, align 1
  %tmp_456 = bitcast [42 x i8]* %tmp_455_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_456)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_149
if_end_149:
  ; if (large1 * small1) > 0.5
  %tmp_457 = load float, float* @global_large1, align 4
  %tmp_458 = load float, float* @global_small1, align 4
  %tmp_459 = fmul float %tmp_457, %tmp_458
  %tmp_460 = bitcast i32 1056964608 to float
  %tmp_461 = fcmp ogt float %tmp_459, %tmp_460
  br i1 %tmp_461, label %if_true_151, label %if_else_153
if_true_151:
  ; print "🎉 PASS: Large × Small comparison works!" (inline)
  %tmp_462_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\8E\89 PASS: Large \C3\97 Small comparison works!\00", [44 x i8]* %tmp_462_str, align 1
  %tmp_463 = bitcast [44 x i8]* %tmp_462_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_463)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_152
if_else_153:
  ; print "❌ FAIL: Large × Small comparison failed!" (inline)
  %tmp_464_str = alloca [44 x i8], align 1
  store [44 x i8] c"\E2\9D\8C FAIL: Large \C3\97 Small comparison failed!\00", [44 x i8]* %tmp_464_str, align 1
  %tmp_465 = bitcast [44 x i8]* %tmp_464_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_465)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_152
if_end_152:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎊 === ALL CHALLENGES COMPLETED === 🎊" (inline)
  %tmp_466_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\8A === ALL CHALLENGES COMPLETED === \F0\9F\8E\8A\00", [43 x i8]* %tmp_466_str, align 1
  %tmp_467 = bitcast [43 x i8]* %tmp_466_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_467)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏅 If you see this, Dolet is OFFICIALLY AWESOME! 🏅" (inline)
  %tmp_468_str = alloca [56 x i8], align 1
  store [56 x i8] c"\F0\9F\8F\85 If you see this, Dolet is OFFICIALLY AWESOME! \F0\9F\8F\85\00", [56 x i8]* %tmp_468_str, align 1
  %tmp_469 = bitcast [56 x i8]* %tmp_468_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_469)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
