# 03. Arithmetic Operations - Mathematical Computations
# This file demonstrates all arithmetic operations in Dolet

print "=== Arithmetic Operations Demo ==="
print ""

# Basic arithmetic with integers
print "Basic Integer Arithmetic:"
int a = 15
int b = 4

print "a = " + a + ", b = " + b
print "Addition: " + a + " + " + b + " = " + (a + b)
print "Subtraction: " + a + " - " + b + " = " + (a - b)
print "Multiplication: " + a + " * " + b + " = " + (a * b)
print "Division: " + a + " / " + b + " = " + (a / b)
print "Modulo: " + a + " % " + b + " = " + (a % b)
print ""

# Arithmetic with floats
print "Float Arithmetic:"
float x = 10.5
float y = 3.2

print "x = " + x + ", y = " + y
print "Addition: " + x + " + " + y + " = " + (x + y)
print "Subtraction: " + x + " - " + y + " = " + (x - y)
print "Multiplication: " + x + " * " + y + " = " + (x * y)
print "Division: " + x + " / " + y + " = " + (x / y)
print ""

# Operations with parentheses
print "Operations with Parentheses:"
int result1 = (a + b) * 2
int result2 = a * (b + 5)
int result3 = (a * 2) + (b * 3)

print "(a + b) * 2 = " + result1
print "a * (b + 5) = " + result2
print "(a * 2) + (b * 3) = " + result3
print ""

# Complex nested expressions
print "Complex Nested Expressions:"
int complex1 = ((a + b) * 2) - (a / b)
int complex2 = (a * b) + ((a - b) * 2)
int complex3 = ((a + 5) * (b - 1)) / 2

print "((a + b) * 2) - (a / b) = " + complex1
print "(a * b) + ((a - b) * 2) = " + complex2
print "((a + 5) * (b - 1)) / 2 = " + complex3
print ""

# Negative numbers
print "Negative Number Operations:"
int neg1 = -5
int neg2 = -3

print "neg1 = " + neg1 + ", neg2 = " + neg2
print "(-5) + (-3) = " + (neg1 + neg2)
print "(-5) * (-3) = " + (neg1 * neg2)
print "(-5) - (-3) = " + (neg1 - neg2)
print ""

# Assignment with operations
print "Assignment with Operations:"
int counter = 0
print "Initial counter = " + counter

set counter = counter + 1
print "After increment: " + counter

set counter = counter * 2
print "After doubling: " + counter

set counter = counter - 5
print "After subtracting 5: " + counter
print ""

# Chain operations
print "Chain Operations:"
int chain_result = 1 + 2 + 3 + 4 + 5
print "1 + 2 + 3 + 4 + 5 = " + chain_result

int chain_mult = 2 * 3 * 4
print "2 * 3 * 4 = " + chain_mult
print ""

# Power operations (using multiplication)
print "Power Operations (using multiplication):"
int base = 2
int power2 = base * base
int power3 = base * base * base
int power4 = base * base * base * base

print "base = " + base
print "2^2 = " + power2
print "2^3 = " + power3
print "2^4 = " + power4
print ""

# Mixed type operations
print "Mixed Type Operations:"
int int_val = 10
float float_val = 2.5
print "int_val = " + int_val + ", float_val = " + float_val
print "Mixed operations require careful handling"
print ""

# Order of operations
print "Order of Operations:"
int order1 = 2 + 3 * 4
int order2 = (2 + 3) * 4
int order3 = 2 * 3 + 4
int order4 = 2 * (3 + 4)

print "2 + 3 * 4 = " + order1
print "(2 + 3) * 4 = " + order2
print "2 * 3 + 4 = " + order3
print "2 * (3 + 4) = " + order4
print ""

print "=== End of Arithmetic Operations Demo ==="
