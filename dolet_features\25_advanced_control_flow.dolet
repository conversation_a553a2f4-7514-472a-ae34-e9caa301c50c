# 25. Advanced Control Flow - Complex Flow Control Patterns
# This file demonstrates advanced control flow in Dolet

print "=== Advanced Control Flow Demo ==="
print ""

# Nested loops with break simulation
print "Nested Loops with Break Simulation:"

fun find_in_matrix() {
    array int row1 = [1, 2, 3, 4]
    array int row2 = [5, 6, 7, 8]
    array int row3 = [9, 10, 11, 12]
    
    int target = 7
    bool found = false
    
    print "Searching for " + target + " in matrix:"
    
    for i = 0 to 2
        print "Checking row " + i + ":"
        
        for j = 0 to 3
            int current_value = 0
            
            if i == 0
                set current_value = row1[j]
            elif i == 1
                set current_value = row2[j]
            else
                set current_value = row3[j]
            
            print "  Checking [" + i + "][" + j + "] = " + current_value
            
            if current_value == target
                print "  Found " + target + " at position [" + i + "][" + j + "]"
                set found = true
                # In real implementation, would break out of both loops
                return found
    
    if not found
        print "Value " + target + " not found in matrix"
    
    return found
}

bool search_result = find_in_matrix()
print ""

# Switch-case simulation using if-elif
print "Switch-Case Simulation:"

fun process_grade(letter_grade) {
    print "Processing grade: " + letter_grade
    
    if letter_grade == "A"
        print "  Excellent! GPA: 4.0"
        print "  Congratulations on outstanding performance"
    elif letter_grade == "B"
        print "  Very Good! GPA: 3.0"
        print "  Keep up the good work"
    elif letter_grade == "C"
        print "  Good! GPA: 2.0"
        print "  Room for improvement"
    elif letter_grade == "D"
        print "  Pass! GPA: 1.0"
        print "  Consider additional study"
    elif letter_grade == "F"
        print "  Fail! GPA: 0.0"
        print "  Retake required"
    else
        print "  Invalid grade: " + letter_grade
        print "  Please enter A, B, C, D, or F"
}

process_grade("A")
process_grade("C")
process_grade("X")
print ""

# State machine simulation
print "State Machine Simulation:"

fun traffic_light_controller() {
    string state = "RED"
    int timer = 0
    
    print "Traffic Light Controller:"
    
    for cycle = 1 to 10
        print "Cycle " + cycle + ": State = " + state + ", Timer = " + timer
        
        if state == "RED"
            set timer = timer + 1
            if timer >= 3
                set state = "GREEN"
                set timer = 0
                print "  Switching to GREEN"
        elif state == "GREEN"
            set timer = timer + 1
            if timer >= 3
                set state = "YELLOW"
                set timer = 0
                print "  Switching to YELLOW"
        elif state == "YELLOW"
            set timer = timer + 1
            if timer >= 1
                set state = "RED"
                set timer = 0
                print "  Switching to RED"
}

traffic_light_controller()
print ""

# Exception handling simulation
print "Exception Handling Simulation:"

fun safe_divide(a, b) {
    print "Attempting to divide " + a + " by " + b
    
    # Try block simulation
    if b == 0
        print "  ERROR: Division by zero detected"
        print "  Handling exception..."
        print "  Returning default value: 0"
        return 0
    else
        int result = a / b
        print "  Division successful: " + result
        return result
}

fun safe_array_access(arr, index, size) {
    print "Attempting to access array[" + index + "]"
    
    if index < 0 or index >= size
        print "  ERROR: Array index out of bounds"
        print "  Valid range: 0 to " + (size - 1)
        print "  Returning default value: -1"
        return -1
    else
        int value = arr[index]
        print "  Access successful: " + value
        return value
}

int div_result1 = safe_divide(10, 2)
int div_result2 = safe_divide(10, 0)

array int test_array = [10, 20, 30, 40, 50]
int access_result1 = safe_array_access(test_array, 2, 5)
int access_result2 = safe_array_access(test_array, 10, 5)
print ""

# Complex conditional logic
print "Complex Conditional Logic:"

fun evaluate_loan_application(age, income, credit_score, employment_years) {
    print "Evaluating loan application:"
    print "  Age: " + age
    print "  Income: $" + income
    print "  Credit Score: " + credit_score
    print "  Employment Years: " + employment_years
    
    bool approved = false
    string reason = ""
    
    if age < 18
        set reason = "Applicant too young"
    elif age > 75
        set reason = "Applicant too old"
    elif income < 30000
        set reason = "Income too low"
    elif credit_score < 600
        set reason = "Credit score too low"
    elif employment_years < 2
        set reason = "Insufficient employment history"
    else
        # Additional checks for approval level
        if credit_score >= 750 and income >= 50000
            set approved = true
            set reason = "Excellent candidate - approved"
        elif credit_score >= 700 and income >= 40000 and employment_years >= 3
            set approved = true
            set reason = "Good candidate - approved"
        elif credit_score >= 650 and income >= 35000 and employment_years >= 5
            set approved = true
            set reason = "Acceptable candidate - approved"
        else
            set reason = "Does not meet minimum requirements"
    
    print "  Decision: " + (approved ? "APPROVED" : "REJECTED")
    print "  Reason: " + reason
    
    return approved
}

bool app1 = evaluate_loan_application(30, 55000, 780, 5)
bool app2 = evaluate_loan_application(25, 25000, 650, 1)
bool app3 = evaluate_loan_application(45, 75000, 720, 10)
print ""

# Loop with multiple exit conditions
print "Loop with Multiple Exit Conditions:"

fun search_with_timeout() {
    print "Searching with timeout and multiple exit conditions:"
    
    int attempts = 0
    int max_attempts = 10
    bool found = false
    bool timeout = false
    
    while not found and not timeout and attempts < max_attempts
        set attempts = attempts + 1
        print "  Attempt " + attempts + ":"
        
        # Simulate search operation
        int random_result = attempts % 3  # Simplified random
        
        if random_result == 0
            print "    Search successful!"
            set found = true
        elif attempts >= 7
            print "    Timeout reached"
            set timeout = true
        else
            print "    Not found, continuing..."
    
    print "Search completed:"
    print "  Attempts: " + attempts
    print "  Found: " + found
    print "  Timeout: " + timeout
    
    if found
        return 1
    elif timeout
        return -1
    else
        return 0
}

int search_result = search_with_timeout()
print ""

# Goto simulation using flags
print "Goto Simulation using Flags:"

fun complex_processing() {
    print "Complex processing with goto-like behavior:"
    
    bool skip_section_a = false
    bool skip_section_b = false
    bool early_exit = false
    
    # Section A
    if not skip_section_a
        print "  Executing Section A"
        
        # Some condition that might skip Section B
        if true  # Simulated condition
            print "    Condition met - skipping Section B"
            set skip_section_b = true
    
    # Section B
    if not skip_section_b
        print "  Executing Section B"
        
        # Some condition that might cause early exit
        if false  # Simulated condition
            print "    Error detected - early exit"
            set early_exit = true
    
    # Section C (cleanup)
    if not early_exit
        print "  Executing Section C (cleanup)"
        print "  Processing completed successfully"
    else
        print "  Executing emergency cleanup"
        print "  Processing terminated early"
}

complex_processing()
print ""

# Pattern matching simulation
print "Pattern Matching Simulation:"

fun process_command(command, arg1, arg2) {
    print "Processing command: " + command + " with args: " + arg1 + ", " + arg2
    
    if command == "ADD"
        int result = arg1 + arg2
        print "  Addition result: " + result
    elif command == "SUB"
        int result = arg1 - arg2
        print "  Subtraction result: " + result
    elif command == "MUL"
        int result = arg1 * arg2
        print "  Multiplication result: " + result
    elif command == "DIV"
        if arg2 != 0
            int result = arg1 / arg2
            print "  Division result: " + result
        else
            print "  Error: Division by zero"
    elif command == "HELP"
        print "  Available commands: ADD, SUB, MUL, DIV, HELP, EXIT"
    elif command == "EXIT"
        print "  Exiting program..."
        return false
    else
        print "  Unknown command: " + command
        print "  Type HELP for available commands"
    
    return true
}

bool continue1 = process_command("ADD", 10, 5)
bool continue2 = process_command("DIV", 20, 0)
bool continue3 = process_command("HELP", 0, 0)
bool continue4 = process_command("EXIT", 0, 0)
print ""

print "=== End of Advanced Control Flow Demo ==="
