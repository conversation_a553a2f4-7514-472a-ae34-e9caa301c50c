// 4. Conditional Statements - if/elif/else, nested conditions (Rust version)
// This file demonstrates all conditional statement patterns in Rust for comparison with Dolet

fn main() {
    println!("=== Conditional Statements Demo (Rust) ===");
    println!();

    // Simple if statement
    let score = 85;
    println!("Simple if statement (score = 85):");
    if score >= 80 {
        println!("✓ Good score!");
    }
    println!();

    // if-else statement
    let age = 17;
    println!("if-else statement (age = 17):");
    if age >= 18 {
        println!("You are an adult");
    } else {
        println!("You are a minor");
    }
    println!();

    // if-elif-else chain (else if in Rust)
    let grade = 92;
    println!("if-else if-else chain (grade = 92):");
    if grade >= 90 {
        println!("Grade: A (Excellent!)");
    } else if grade >= 80 {
        println!("Grade: B (Good!)");
    } else if grade >= 70 {
        println!("Grade: C (Average)");
    } else if grade >= 60 {
        println!("Grade: D (Below Average)");
    } else {
        println!("Grade: F (Failed)");
    }
    println!();

    // Multiple else if statements
    let temperature = 25;
    println!("Multiple else if statements (temperature = 25°C):");
    if temperature > 35 {
        println!("It's very hot!");
    } else if temperature > 25 {
        println!("It's warm");
    } else if temperature > 15 {
        println!("It's mild");
    } else if temperature > 5 {
        println!("It's cool");
    } else if temperature > 0 {
        println!("It's cold");
    } else {
        println!("It's freezing!");
    }
    println!();

    // Nested if statements
    let x = 10;
    let y = 5;
    println!("Nested if statements (x=10, y=5):");
    if x > 0 {
        println!("x is positive");
        if y > 0 {
            println!("y is also positive");
            if x > y {
                println!("x is greater than y");
            } else {
                println!("y is greater than or equal to x");
            }
        } else {
            println!("y is not positive");
        }
    } else {
        println!("x is not positive");
    }
    println!();

    // Complex conditions with expressions
    let a = 15;
    let b = 8;
    println!("Complex conditions (a=15, b=8):");
    if (a + b) > 20 {
        println!("Sum is greater than 20");
        if (a * b) > 100 {
            println!("Product is greater than 100");
        } else {
            println!("Product is 100 or less");
        }
    } else {
        println!("Sum is 20 or less");
    }
    println!();

    // String conditions
    let username = "admin";
    let password = "123456";
    println!("String conditions:");
    if username == "admin" {
        println!("Username is correct");
        if password == "123456" {
            println!("✓ Login successful!");
        } else {
            println!("✗ Wrong password");
        }
    } else {
        println!("✗ Wrong username");
    }
    println!();

    // Boolean conditions
    let is_logged_in = true;
    let has_permission = false;
    println!("Boolean conditions:");
    if is_logged_in {
        println!("User is logged in");
        if has_permission {
            println!("✓ Access granted");
        } else {
            println!("✗ Access denied - no permission");
        }
    } else {
        println!("✗ Please log in first");
    }
    println!();

    // Multiple condition checks
    let number = 12;
    println!("Multiple condition checks (number = 12):");
    if number > 0 {
        println!("Number is positive");
        if number % 2 == 0 {
            println!("Number is even");
            if number > 10 {
                println!("Number is greater than 10");
            } else {
                println!("Number is 10 or less");
            }
        } else {
            println!("Number is odd");
        }
    } else {
        println!("Number is zero or negative");
    }
    println!();

    // Practical example: User validation
    let user_type = "premium";
    let account_balance = 150;
    println!("Practical example - User validation:");
    if user_type == "premium" {
        println!("Premium user detected");
        if account_balance > 100 {
            println!("✓ Transaction approved");
        } else {
            println!("✗ Insufficient balance");
        }
    } else if user_type == "standard" {
        println!("Standard user detected");
        if account_balance > 50 {
            println!("✓ Transaction approved");
        } else {
            println!("✗ Insufficient balance");
        }
    } else {
        println!("✗ Unknown user type");
    }
    println!();

    // Deeply nested conditions
    let level = 3;
    let points = 250;
    let premium = true;
    println!("Deeply nested conditions:");
    if level >= 1 {
        println!("Level 1+ user");
        if level >= 2 {
            println!("Level 2+ user");
            if level >= 3 {
                println!("Level 3+ user");
                if points > 200 {
                    println!("High points user");
                    if premium {
                        println!("✓ VIP status activated!");
                    } else {
                        println!("Consider upgrading to premium");
                    }
                } else {
                    println!("Need more points for VIP");
                }
            } else {
                println!("Level 3 required for advanced features");
            }
        } else {
            println!("Level 2 required for intermediate features");
        }
    } else {
        println!("New user - welcome!");
    }

    println!();
    println!("=== End of Conditional Statements Demo ===");
}
