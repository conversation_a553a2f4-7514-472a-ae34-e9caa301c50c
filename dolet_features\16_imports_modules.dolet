# 16. Imports and Modules - Code Organization and Reuse
# This file demonstrates import and module system in Dolet

print "=== Imports and Mo<PERSON>les Demo ==="
print ""

# Standard library imports (simulated)
print "Standard Library Imports:"
# import math
# import string
# import array
# import file
# import system

print "Importing standard modules:"
print "- math module (mathematical functions)"
print "- string module (string utilities)"
print "- array module (array operations)"
print "- file module (file operations)"
print "- system module (system utilities)"
print ""

# Using imported math functions
print "Using Math Module:"
float pi = 3.14159  # math.PI
float e = 2.71828   # math.E

float angle = 45.0
float sin_value = 0.707  # math.sin(angle)
float cos_value = 0.707  # math.cos(angle)
float sqrt_value = 7.071 # math.sqrt(50)

print "PI = " + pi
print "E = " + e
print "sin(45°) = " + sin_value
print "cos(45°) = " + cos_value
print "sqrt(50) = " + sqrt_value
print ""

# Using string module functions
print "Using String Module:"
string text = "Hello World"
int text_length = 11        # string.length(text)
string upper_text = "HELLO WORLD"  # string.upper(text)
string lower_text = "hello world"  # string.lower(text)
bool contains_hello = true  # string.contains(text, "Hello")

print "Original: '" + text + "'"
print "Length: " + text_length
print "Upper: '" + upper_text + "'"
print "Lower: '" + lower_text + "'"
print "Contains 'Hello': " + contains_hello
print ""

# Custom module simulation
print "Custom Module Simulation:"

# Simulating a custom math_utils module
print "Creating math_utils module:"

fun factorial(n) {
    if n <= 1
        return 1
    else
        return n * factorial(n - 1)
}

fun is_prime(n) {
    if n <= 1
        return false
    
    for i = 2 to n - 1
        if n % i == 0
            return false
    
    return true
}

fun gcd(a, b) {
    while b != 0
        int temp = b
        set b = a % b
        set a = temp
    return a
}

print "- factorial function defined"
print "- is_prime function defined"
print "- gcd function defined"
print ""

# Using custom module functions
print "Using Custom Math Utils:"
int fact5 = factorial(5)
bool is_17_prime = is_prime(17)
int gcd_result = gcd(48, 18)

print "factorial(5) = " + fact5
print "is_prime(17) = " + is_17_prime
print "gcd(48, 18) = " + gcd_result
print ""

# Simulating file module
print "File Module Simulation:"

fun file_exists(filename) {
    print "Checking if file '" + filename + "' exists"
    return true  # Simulated result
}

fun read_file(filename) {
    print "Reading file '" + filename + "'"
    return "File content here"  # Simulated content
}

fun write_file(filename, content) {
    print "Writing to file '" + filename + "': '" + content + "'"
    return true
}

bool exists = file_exists("data.txt")
string content = read_file("data.txt")
bool written = write_file("output.txt", "Hello, World!")

print "File exists: " + exists
print "File content: '" + content + "'"
print "Write successful: " + written
print ""

# Array utilities module
print "Array Utilities Module:"

fun array_sum(arr, size) {
    int total = 0
    for i = 0 to size - 1
        set total = total + arr[i]
    return total
}

fun array_max(arr, size) {
    int maximum = arr[0]
    for i = 1 to size - 1
        if arr[i] > maximum
            set maximum = arr[i]
    return maximum
}

fun array_reverse(arr, size) {
    print "Reversing array of size " + size
    # Actual reversal logic would be here
}

array int test_array = [5, 2, 8, 1, 9]
int sum_result = array_sum(test_array, 5)
int max_result = array_max(test_array, 5)

print "Array: [5, 2, 8, 1, 9]"
print "Sum: " + sum_result
print "Max: " + max_result
array_reverse(test_array, 5)
print ""

# System module simulation
print "System Module Simulation:"

fun get_current_time() {
    return "2024-01-15 14:30:00"  # Simulated timestamp
}

fun get_user_name() {
    return "current_user"  # Simulated username
}

fun get_os_info() {
    return "Windows 11"  # Simulated OS info
}

string current_time = get_current_time()
string username = get_user_name()
string os_info = get_os_info()

print "Current time: " + current_time
print "Username: " + username
print "OS: " + os_info
print ""

# Module namespacing simulation
print "Module Namespacing:"

# Simulating math.advanced namespace
fun advanced_power(base, exponent) {
    int result = 1
    for i = 1 to exponent
        set result = result * base
    return result
}

fun advanced_log(number, base) {
    print "Calculating log base " + base + " of " + number
    return 2.0  # Simulated result
}

# Simulating string.format namespace
fun format_currency(amount) {
    return "$" + amount
}

fun format_percentage(value) {
    return value + "%"
}

int power_result = advanced_power(2, 8)  # math.advanced.power(2, 8)
float log_result = advanced_log(100, 10)  # math.advanced.log(100, 10)
string currency = format_currency("1299.99")  # string.format.currency(1299.99)
string percentage = format_percentage("85")    # string.format.percentage(85)

print "2^8 = " + power_result
print "log10(100) = " + log_result
print "Currency: " + currency
print "Percentage: " + percentage
print ""

# Conditional imports simulation
print "Conditional Imports:"
bool debug_mode = true

if debug_mode
    print "Debug mode enabled - loading debug utilities"
    
    fun debug_print(message) {
        print "[DEBUG] " + message
    }
    
    fun debug_assert(condition, message) {
        if not condition
            print "[ASSERT FAILED] " + message
    }
    
    debug_print("Debug module loaded successfully")
    debug_assert(true, "This should not fail")
    debug_assert(false, "This assertion will fail")
else
    print "Production mode - debug utilities not loaded"
print ""

# Module version checking
print "Module Version Checking:"
string math_version = "1.2.0"
string string_version = "2.1.0"
string required_version = "1.0.0"

print "Math module version: " + math_version
print "String module version: " + string_version
print "Required version: " + required_version

if math_version >= required_version
    print "Math module version is compatible"
else
    print "Math module version is too old"
print ""

print "=== End of Imports and Modules Demo ==="
