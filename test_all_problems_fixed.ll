; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 82 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [24 x i8] c"She said \22Hello\22 to me\0A\00", align 1
@.str_1 = private unnamed_addr constant [32 x i8] c"\22Start\22 and \22End\22 and \22Middle\22\0A\00", align 1
@.str_2 = private unnamed_addr constant [45 x i8] c"Quote: \22Test\22 Symbol: @ Percent: %% Hash: #\0A\00", align 1
@.str_3 = private unnamed_addr constant [87 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\22,./<>?   \0A\00", align 1
@.str_4 = private unnamed_addr constant [21 x i8] c"\22Beginning and end\22\0A\00", align 1
@.str_5 = private unnamed_addr constant [16 x i8] c"Progress: 50%%\0A\00", align 1
@.str_6 = private unnamed_addr constant [43 x i8] c"100%% complete, 50%% done, 25%% remaining\0A\00", align 1
@.str_7 = private unnamed_addr constant [33 x i8] c"%%d %%s %%f %%c %%%% formatting\0A\00", align 1
@.str_8 = private unnamed_addr constant [26 x i8] c"Line1\0ALine2\09Tabbed\0ALine3\0A\00", align 1
@.str_9 = private unnamed_addr constant [30 x i8] c"Path: C:\\Users\\<USER>\\File.txt\0A\00", align 1
@.str_10 = private unnamed_addr constant [48 x i8] c"Quote:\22 Newline:\0A Tab:\09 Backslash:\\ Percent:%%\0A\00", align 1
@.str_11 = private unnamed_addr constant [32 x i8] c"!@#$%%^&*()_+-=[]{}|;':\22,./<>?\0A\00", align 1
@.str_12 = private unnamed_addr constant [22 x i8] c"123!@#456$%%^789&*()\0A\00", align 1
@.str_13 = private unnamed_addr constant [39 x i8] c"Caf\C3\A9 na\C3\AFve r\C3\A9sum\C3\A9\0A\00", align 1
@.str_14 = private unnamed_addr constant [35 x i8] c"   Leading and trailing spaces   \0A\00", align 1
@.str_15 = private unnamed_addr constant [27 x i8] c"Word1     Word2     Word3\0A\00", align 1
@.str_16 = private unnamed_addr constant [26 x i8] c" \09 Mixed \0A whitespace \09 \0A\00", align 1
@.str_17 = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_18 = private unnamed_addr constant [3 x i8] c"A\0A\00", align 1
@.str_19 = private unnamed_addr constant [3 x i8] c"\22\0A\00", align 1
@.str_20 = private unnamed_addr constant [4 x i8] c"%%\0A\00", align 1
@.str_21 = private unnamed_addr constant [425 x i8] c"This is a very long string that contains many different types of characters including quotes like \22hello\22 and percents like 100%% and symbols like !@#$%%^&*() and escape sequences like \0A newlines and \09 tabs and backslashes like \\ and should test the complete string handling system of the Dolet compiler to make sure everything works correctly even with very long content that might cause buffer issues or parsing problems.\0A\00", align 1
@.str_22 = private unnamed_addr constant [15 x i8] c"Start \22quote\22\0A\00", align 1
@.str_23 = private unnamed_addr constant [21 x i8] c" middle %% percent \0A\00", align 1
@.str_24 = private unnamed_addr constant [15 x i8] c"end \0A newline\0A\00", align 1
@.str_25 = private unnamed_addr constant [134 x i8] c"\22Ultimate test: 100%% complete with \0A newlines, \09 tabs, \\ backslashes, and all symbols !@#$%%^&*()_+-=[]{}|;':\22,./<>? in one string\22\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [15 x i8] c"Basic quotes: \00", align 1
@.str_expr_1 = private unnamed_addr constant [18 x i8] c"Multiple quotes: \00", align 1
@.str_expr_2 = private unnamed_addr constant [21 x i8] c"Quotes and special: \00", align 1
@.str_expr_3 = private unnamed_addr constant [19 x i8] c"Original problem: \00", align 1
@.str_expr_4 = private unnamed_addr constant [18 x i8] c"Quotes at edges: \00", align 1
@.str_expr_5 = private unnamed_addr constant [17 x i8] c"Single percent: \00", align 1
@.str_expr_6 = private unnamed_addr constant [20 x i8] c"Multiple percents: \00", align 1
@.str_expr_7 = private unnamed_addr constant [18 x i8] c"Percent special: \00", align 1
@.str_expr_8 = private unnamed_addr constant [20 x i8] c"Newlines and tabs: \00", align 1
@.str_expr_9 = private unnamed_addr constant [14 x i8] c"Backslashes: \00", align 1
@.str_expr_10 = private unnamed_addr constant [16 x i8] c"Mixed escapes: \00", align 1
@.str_expr_11 = private unnamed_addr constant [14 x i8] c"All symbols: \00", align 1
@.str_expr_12 = private unnamed_addr constant [22 x i8] c"Numbers and symbols: \00", align 1
@.str_expr_13 = private unnamed_addr constant [17 x i8] c"Extended chars: \00", align 1
@.str_expr_14 = private unnamed_addr constant [16 x i8] c"Spaces edges: '\00", align 1
@.str_expr_15 = private unnamed_addr constant [2 x i8] c"'\00", align 1
@.str_expr_16 = private unnamed_addr constant [19 x i8] c"Internal spaces: '\00", align 1
@.str_expr_17 = private unnamed_addr constant [20 x i8] c"Mixed whitespace: '\00", align 1
@.str_expr_18 = private unnamed_addr constant [16 x i8] c"Empty string: '\00", align 1
@.str_expr_19 = private unnamed_addr constant [14 x i8] c"Single char: \00", align 1
@.str_expr_20 = private unnamed_addr constant [15 x i8] c"Single quote: \00", align 1
@.str_expr_21 = private unnamed_addr constant [12 x i8] c"Very long: \00", align 1
@.str_expr_22 = private unnamed_addr constant [11 x i8] c"Combined: \00", align 1
@.str_expr_23 = private unnamed_addr constant [16 x i8] c"Ultimate test: \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1
@str_dyn_0 = private unnamed_addr constant [25 x i8] c"She said \\\22Hello\\\22 to me\00", align 1
@str_dyn_1 = private unnamed_addr constant [37 x i8] c"\\\22Start\\\22 and \\\22End\\\22 and \\\22Middle\\\22\00", align 1
@str_dyn_2 = private unnamed_addr constant [45 x i8] c"Quote: \\\22Test\\\22 Symbol: @ Percent: %% Hash: #\00", align 1
@str_dyn_3 = private unnamed_addr constant [86 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\\\22,./<>?   \00", align 1
@str_dyn_4 = private unnamed_addr constant [22 x i8] c"\\\22Beginning and end\\\22\00", align 1
@str_dyn_5 = private unnamed_addr constant [28 x i8] c"Line1\\nLine2\\tTabbed\\nLine3\00", align 1
@str_dyn_6 = private unnamed_addr constant [32 x i8] c"Path: C:\\\\Users\\\\<USER>\\\\File.txt\00", align 1
@str_dyn_7 = private unnamed_addr constant [50 x i8] c"Quote:\\\22 Newline:\\n Tab:\\t Backslash:\\\\ Percent:%%\00", align 1
@str_dyn_8 = private unnamed_addr constant [31 x i8] c"!@#$%%^&*()_+-=[]{}|;':\\\22,./<>?\00", align 1
@str_dyn_9 = private unnamed_addr constant [28 x i8] c" \\t Mixed \\n whitespace \\t \00", align 1
@str_dyn_10 = private unnamed_addr constant [3 x i8] c"\\\22\00", align 1
@str_dyn_11 = private unnamed_addr constant [427 x i8] c"This is a very long string that contains many different types of characters including quotes like \\\22hello\\\22 and percents like 100%% and symbols like !@#$%%^&*() and escape sequences like \\n newlines and \\t tabs and backslashes like \\\\ and should test the complete string handling system of the Dolet compiler to make sure everything works correctly even with very long content that might cause buffer issues or parsing problems.\00", align 1
@str_dyn_12 = private unnamed_addr constant [16 x i8] c"Start \\\22quote\\\22\00", align 1
@str_dyn_13 = private unnamed_addr constant [15 x i8] c"end \\n newline\00", align 1
@str_dyn_14 = private unnamed_addr constant [137 x i8] c"\\\22Ultimate test: 100%% complete with \\n newlines, \\t tabs, \\\\ backslashes, and all symbols !@#$%%^&*()_+-=[]{}|;':\\\22,./<>? in one string\\\22\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_basic_quotes = global i8* null, align 8
@global_multiple_quotes = global i8* null, align 8
@global_quotes_and_special = global i8* null, align 8
@global_original_problem = global i8* null, align 8
@global_quotes_edges = global i8* null, align 8
@global_single_percent = global i8* null, align 8
@global_multiple_percents = global i8* null, align 8
@global_percent_special = global i8* null, align 8
@global_newlines_tabs = global i8* null, align 8
@global_backslashes = global i8* null, align 8
@global_mixed_escapes = global i8* null, align 8
@global_all_symbols = global i8* null, align 8
@global_numbers_symbols = global i8* null, align 8
@global_extended_chars = global i8* null, align 8
@global_spaces_edges = global i8* null, align 8
@global_internal_spaces = global i8* null, align 8
@global_mixed_whitespace = global i8* null, align 8
@global_empty_str = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_single_char = global i8* null, align 8
@global_single_quote = global i8* null, align 8
@global_very_long = global i8* null, align 8
@global_part1 = global i8* null, align 8
@global_part2 = global i8* null, align 8
@global_part3 = global i8* null, align 8
@global_combined = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_ultimate_test = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== COMPREHENSIVE PROBLEM TEST ===" (inline)
  %tmp_1_str = alloca [35 x i8], align 1
  store [35 x i8] c"=== COMPREHENSIVE PROBLEM TEST ===\00", [35 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [35 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== STRING QUOTE ISSUES ===" (inline)
  %tmp_3_str = alloca [28 x i8], align 1
  store [28 x i8] c"=== STRING QUOTE ISSUES ===\00", [28 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [28 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string basic_quotes = "She said \"Hello\" to me"
  store i8* getelementptr inbounds ([25 x i8], [25 x i8]* @str_dyn_0, i64 0, i64 0), i8** @global_basic_quotes, align 8
  ; print expression: "Basic quotes: " + basic_quotes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_5 = load i8*, i8** @global_basic_quotes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_5)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string multiple_quotes = "\"Start\" and \"End\" and \"Middle\""
  store i8* getelementptr inbounds ([37 x i8], [37 x i8]* @str_dyn_1, i64 0, i64 0), i8** @global_multiple_quotes, align 8
  ; print expression: "Multiple quotes: " + multiple_quotes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_6 = load i8*, i8** @global_multiple_quotes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string quotes_and_special = "Quote: \"Test\" Symbol: @ Percent: % Hash: #"
  store i8* getelementptr inbounds ([45 x i8], [45 x i8]* @str_dyn_2, i64 0, i64 0), i8** @global_quotes_and_special, align 8
  ; print expression: "Quotes and special: " + quotes_and_special
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_7 = load i8*, i8** @global_quotes_and_special, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string original_problem = "   Multiple   Spaces   And   Special   Characters   !@#$%^&*()_+-=[]{}|;':\",./<>?   "
  store i8* getelementptr inbounds ([86 x i8], [86 x i8]* @str_dyn_3, i64 0, i64 0), i8** @global_original_problem, align 8
  ; print expression: "Original problem: " + original_problem
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_8 = load i8*, i8** @global_original_problem, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string quotes_edges = "\"Beginning and end\""
  store i8* getelementptr inbounds ([22 x i8], [22 x i8]* @str_dyn_4, i64 0, i64 0), i8** @global_quotes_edges, align 8
  ; print expression: "Quotes at edges: " + quotes_edges
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_9 = load i8*, i8** @global_quotes_edges, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PERCENT CHARACTER ISSUES ===" (inline)
  %tmp_10_str = alloca [33 x i8], align 1
  store [33 x i8] c"=== PERCENT CHARACTER ISSUES ===\00", [33 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [33 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string single_percent = "Progress: 50%"
  store i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_5, i64 0, i64 0), i8** @global_single_percent, align 8
  ; print expression: "Single percent: " + single_percent
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_12 = load i8*, i8** @global_single_percent, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string multiple_percents = "100% complete, 50% done, 25% remaining"
  store i8* getelementptr inbounds ([39 x i8], [39 x i8]* @.str_6, i64 0, i64 0), i8** @global_multiple_percents, align 8
  ; print expression: "Multiple percents: " + multiple_percents
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_13 = load i8*, i8** @global_multiple_percents, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string percent_special = "%d %s %f %c %% formatting"
  store i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_7, i64 0, i64 0), i8** @global_percent_special, align 8
  ; print expression: "Percent special: " + percent_special
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_14 = load i8*, i8** @global_percent_special, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_14)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== ESCAPE SEQUENCE ISSUES ===" (inline)
  %tmp_15_str = alloca [31 x i8], align 1
  store [31 x i8] c"=== ESCAPE SEQUENCE ISSUES ===\00", [31 x i8]* %tmp_15_str, align 1
  %tmp_16 = bitcast [31 x i8]* %tmp_15_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string newlines_tabs = "Line1\nLine2\tTabbed\nLine3"
  store i8* getelementptr inbounds ([28 x i8], [28 x i8]* @str_dyn_5, i64 0, i64 0), i8** @global_newlines_tabs, align 8
  ; print expression: "Newlines and tabs: " + newlines_tabs
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_17 = load i8*, i8** @global_newlines_tabs, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string backslashes = "Path: C:\\Users\\<USER>\\File.txt"
  store i8* getelementptr inbounds ([32 x i8], [32 x i8]* @str_dyn_6, i64 0, i64 0), i8** @global_backslashes, align 8
  ; print expression: "Backslashes: " + backslashes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_18 = load i8*, i8** @global_backslashes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string mixed_escapes = "Quote:\" Newline:\n Tab:\t Backslash:\\ Percent:%"
  store i8* getelementptr inbounds ([50 x i8], [50 x i8]* @str_dyn_7, i64 0, i64 0), i8** @global_mixed_escapes, align 8
  ; print expression: "Mixed escapes: " + mixed_escapes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_19 = load i8*, i8** @global_mixed_escapes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== SPECIAL CHARACTER COMBINATIONS ===" (inline)
  %tmp_20_str = alloca [39 x i8], align 1
  store [39 x i8] c"=== SPECIAL CHARACTER COMBINATIONS ===\00", [39 x i8]* %tmp_20_str, align 1
  %tmp_21 = bitcast [39 x i8]* %tmp_20_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_21)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string all_symbols = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
  store i8* getelementptr inbounds ([31 x i8], [31 x i8]* @str_dyn_8, i64 0, i64 0), i8** @global_all_symbols, align 8
  ; print expression: "All symbols: " + all_symbols
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_22 = load i8*, i8** @global_all_symbols, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string numbers_symbols = "123!@#456$%^789&*()"
  store i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_12, i64 0, i64 0), i8** @global_numbers_symbols, align 8
  ; print expression: "Numbers and symbols: " + numbers_symbols
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_23 = load i8*, i8** @global_numbers_symbols, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_23)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string extended_chars = "Café naïve résumé"
  store i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_13, i64 0, i64 0), i8** @global_extended_chars, align 8
  ; print expression: "Extended chars: " + extended_chars
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_24 = load i8*, i8** @global_extended_chars, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== WHITESPACE ISSUES ===" (inline)
  %tmp_25_str = alloca [26 x i8], align 1
  store [26 x i8] c"=== WHITESPACE ISSUES ===\00", [26 x i8]* %tmp_25_str, align 1
  %tmp_26 = bitcast [26 x i8]* %tmp_25_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string spaces_edges = "   Leading and trailing spaces   "
  store i8* getelementptr inbounds ([34 x i8], [34 x i8]* @.str_14, i64 0, i64 0), i8** @global_spaces_edges, align 8
  ; print expression: "Spaces edges: '" + spaces_edges + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_27 = load i8*, i8** @global_spaces_edges, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_27)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_15, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string internal_spaces = "Word1     Word2     Word3"
  store i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_15, i64 0, i64 0), i8** @global_internal_spaces, align 8
  ; print expression: "Internal spaces: '" + internal_spaces + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_28 = load i8*, i8** @global_internal_spaces, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_28)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_15, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string mixed_whitespace = " \t Mixed \n whitespace \t "
  store i8* getelementptr inbounds ([28 x i8], [28 x i8]* @str_dyn_9, i64 0, i64 0), i8** @global_mixed_whitespace, align 8
  ; print expression: "Mixed whitespace: '" + mixed_whitespace + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_29 = load i8*, i8** @global_mixed_whitespace, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_29)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_15, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EMPTY AND MINIMAL STRINGS ===" (inline)
  %tmp_30_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== EMPTY AND MINIMAL STRINGS ===\00", [34 x i8]* %tmp_30_str, align 1
  %tmp_31 = bitcast [34 x i8]* %tmp_30_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_31)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string empty_str = ""
  store i8* getelementptr inbounds ([1 x i8], [1 x i8]* @.str_17, i64 0, i64 0), i8** @global_empty_str, align 8
  ; print expression: "Empty string: '" + empty_str + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_32 = load i8*, i8** @global_empty_str, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_15, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string single_char = "A"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_18, i64 0, i64 0), i8** @global_single_char, align 8
  ; string single_quote = "\""
  store i8* getelementptr inbounds ([3 x i8], [3 x i8]* @str_dyn_10, i64 0, i64 0), i8** @global_single_quote, align 8
  ; string single_percent = "%"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_20, i64 0, i64 0), i8** @global_single_percent, align 8
  ; print expression: "Single char: " + single_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_33 = load i8*, i8** @global_single_char, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_33)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Single quote: " + single_quote
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_34 = load i8*, i8** @global_single_quote, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Single percent: " + single_percent
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_35 = load i8*, i8** @global_single_percent, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_35)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== VERY LONG STRINGS ===" (inline)
  %tmp_36_str = alloca [26 x i8], align 1
  store [26 x i8] c"=== VERY LONG STRINGS ===\00", [26 x i8]* %tmp_36_str, align 1
  %tmp_37 = bitcast [26 x i8]* %tmp_36_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_37)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string very_long = "This is a very long string that contains many different types of characters including quotes like \"hello\" and percents like 100% and symbols like !@#$%^&*() and escape sequences like \n newlines and \t tabs and backslashes like \\ and should test the complete string handling system of the Dolet compiler to make sure everything works correctly even with very long content that might cause buffer issues or parsing problems."
  store i8* getelementptr inbounds ([427 x i8], [427 x i8]* @str_dyn_11, i64 0, i64 0), i8** @global_very_long, align 8
  ; print expression: "Very long: " + very_long
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_38 = load i8*, i8** @global_very_long, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_38)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== CONCATENATION TESTS ===" (inline)
  %tmp_39_str = alloca [28 x i8], align 1
  store [28 x i8] c"=== CONCATENATION TESTS ===\00", [28 x i8]* %tmp_39_str, align 1
  %tmp_40 = bitcast [28 x i8]* %tmp_39_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string part1 = "Start \"quote\""
  store i8* getelementptr inbounds ([16 x i8], [16 x i8]* @str_dyn_12, i64 0, i64 0), i8** @global_part1, align 8
  ; string part2 = " middle % percent "
  store i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_23, i64 0, i64 0), i8** @global_part2, align 8
  ; string part3 = "end \n newline"
  store i8* getelementptr inbounds ([15 x i8], [15 x i8]* @str_dyn_13, i64 0, i64 0), i8** @global_part3, align 8
  ; string combined = part1 + part2 + part3
  ; string assignment from variable/expression: combined = part1 + part2 + part3
  ; print expression: "Combined: " + combined
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_41 = load i8*, i8** @global_combined, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_41)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== VARIABLE ASSIGNMENT TESTS ===" (inline)
  %tmp_42_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== VARIABLE ASSIGNMENT TESTS ===\00", [34 x i8]* %tmp_42_str, align 1
  %tmp_43 = bitcast [34 x i8]* %tmp_42_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set basic_quotes = "New \"quoted\" value"
  %tmp_44_str = alloca [19 x i8], align 1
  %tmp_45 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 0
  store i8 78, i8* %tmp_45, align 1
  %tmp_46 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 1
  store i8 101, i8* %tmp_46, align 1
  %tmp_47 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 2
  store i8 119, i8* %tmp_47, align 1
  %tmp_48 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 3
  store i8 32, i8* %tmp_48, align 1
  %tmp_49 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 4
  store i8 34, i8* %tmp_49, align 1
  %tmp_50 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 5
  store i8 113, i8* %tmp_50, align 1
  %tmp_51 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 6
  store i8 117, i8* %tmp_51, align 1
  %tmp_52 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 7
  store i8 111, i8* %tmp_52, align 1
  %tmp_53 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 8
  store i8 116, i8* %tmp_53, align 1
  %tmp_54 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 9
  store i8 101, i8* %tmp_54, align 1
  %tmp_55 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 10
  store i8 100, i8* %tmp_55, align 1
  %tmp_56 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 11
  store i8 34, i8* %tmp_56, align 1
  %tmp_57 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 12
  store i8 32, i8* %tmp_57, align 1
  %tmp_58 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 13
  store i8 118, i8* %tmp_58, align 1
  %tmp_59 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 14
  store i8 97, i8* %tmp_59, align 1
  %tmp_60 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 15
  store i8 108, i8* %tmp_60, align 1
  %tmp_61 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 16
  store i8 117, i8* %tmp_61, align 1
  %tmp_62 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 17
  store i8 101, i8* %tmp_62, align 1
  %tmp_63 = getelementptr inbounds [19 x i8], [19 x i8]* %tmp_44_str, i64 0, i64 18
  store i8 0, i8* %tmp_63, align 1
  %tmp_64 = bitcast [19 x i8]* %tmp_44_str to i8*
  store i8* %tmp_64, i8** @global_basic_quotes, align 8
  ; set single_percent = "New 100% value"
  %tmp_65_str = alloca [15 x i8], align 1
  %tmp_66 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 0
  store i8 78, i8* %tmp_66, align 1
  %tmp_67 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 1
  store i8 101, i8* %tmp_67, align 1
  %tmp_68 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 2
  store i8 119, i8* %tmp_68, align 1
  %tmp_69 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 3
  store i8 32, i8* %tmp_69, align 1
  %tmp_70 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 4
  store i8 49, i8* %tmp_70, align 1
  %tmp_71 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 5
  store i8 48, i8* %tmp_71, align 1
  %tmp_72 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 6
  store i8 48, i8* %tmp_72, align 1
  %tmp_73 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 7
  store i8 37, i8* %tmp_73, align 1
  %tmp_74 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 8
  store i8 32, i8* %tmp_74, align 1
  %tmp_75 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 9
  store i8 118, i8* %tmp_75, align 1
  %tmp_76 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 10
  store i8 97, i8* %tmp_76, align 1
  %tmp_77 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 11
  store i8 108, i8* %tmp_77, align 1
  %tmp_78 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 12
  store i8 117, i8* %tmp_78, align 1
  %tmp_79 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 13
  store i8 101, i8* %tmp_79, align 1
  %tmp_80 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_65_str, i64 0, i64 14
  store i8 0, i8* %tmp_80, align 1
  %tmp_81 = bitcast [15 x i8]* %tmp_65_str to i8*
  store i8* %tmp_81, i8** @global_single_percent, align 8
  ; set mixed_escapes = "New \n\t\\ value"
  %tmp_82_str = alloca [14 x i8], align 1
  %tmp_83 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 0
  store i8 78, i8* %tmp_83, align 1
  %tmp_84 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 1
  store i8 101, i8* %tmp_84, align 1
  %tmp_85 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 2
  store i8 119, i8* %tmp_85, align 1
  %tmp_86 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 3
  store i8 32, i8* %tmp_86, align 1
  %tmp_87 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 4
  store i8 10, i8* %tmp_87, align 1
  %tmp_88 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 5
  store i8 9, i8* %tmp_88, align 1
  %tmp_89 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 6
  store i8 92, i8* %tmp_89, align 1
  %tmp_90 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 7
  store i8 32, i8* %tmp_90, align 1
  %tmp_91 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 8
  store i8 118, i8* %tmp_91, align 1
  %tmp_92 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 9
  store i8 97, i8* %tmp_92, align 1
  %tmp_93 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 10
  store i8 108, i8* %tmp_93, align 1
  %tmp_94 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 11
  store i8 117, i8* %tmp_94, align 1
  %tmp_95 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 12
  store i8 101, i8* %tmp_95, align 1
  %tmp_96 = getelementptr inbounds [14 x i8], [14 x i8]* %tmp_82_str, i64 0, i64 13
  store i8 0, i8* %tmp_96, align 1
  %tmp_97 = bitcast [14 x i8]* %tmp_82_str to i8*
  store i8* %tmp_97, i8** @global_mixed_escapes, align 8
  ; print "After reassignment:" (inline)
  %tmp_98_str = alloca [20 x i8], align 1
  store [20 x i8] c"After reassignment:\00", [20 x i8]* %tmp_98_str, align 1
  %tmp_99 = bitcast [20 x i8]* %tmp_98_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_99)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Basic quotes: " + basic_quotes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_100 = load i8*, i8** @global_basic_quotes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_100)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Single percent: " + single_percent
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_101 = load i8*, i8** @global_single_percent, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Mixed escapes: " + mixed_escapes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_102 = load i8*, i8** @global_mixed_escapes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_102)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== FINAL STRESS TEST ===" (inline)
  %tmp_103_str = alloca [26 x i8], align 1
  store [26 x i8] c"=== FINAL STRESS TEST ===\00", [26 x i8]* %tmp_103_str, align 1
  %tmp_104 = bitcast [26 x i8]* %tmp_103_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_104)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string ultimate_test = "\"Ultimate test: 100% complete with \n newlines, \t tabs, \\ backslashes, and all symbols !@#$%^&*()_+-=[]{}|;':\",./<>? in one string\""
  store i8* getelementptr inbounds ([137 x i8], [137 x i8]* @str_dyn_14, i64 0, i64 0), i8** @global_ultimate_test, align 8
  ; print expression: "Ultimate test: " + ultimate_test
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_23, i32 0, i32 0))
  %tmp_105 = load i8*, i8** @global_ultimate_test, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_105)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_17, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== ALL TESTS COMPLETED ===" (inline)
  %tmp_106_str = alloca [28 x i8], align 1
  store [28 x i8] c"=== ALL TESTS COMPLETED ===\00", [28 x i8]* %tmp_106_str, align 1
  %tmp_107 = bitcast [28 x i8]* %tmp_106_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_107)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "If you can see this message, all string problems a..." (inline)
  %tmp_108_str = alloca [66 x i8], align 1
  store [66 x i8] c"If you can see this message, all string problems are SOLVED! \F0\9F\8E\89\00", [66 x i8]* %tmp_108_str, align 1
  %tmp_109 = bitcast [66 x i8]* %tmp_108_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_109)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
