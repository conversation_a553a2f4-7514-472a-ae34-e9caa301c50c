#!/usr/bin/env python3
import sys
import os
#import re

class DoletCompiler:
    def __init__(self):
        self.temp_counter = 0
        self.label_counter = 0
        self.string_counter = 0
        self.global_vars = {}
        self.string_constants = {}
        self.llvm_lines = []
        self.imported_modules = {}  # Track imported modules
        self.imported_functions = {}  # Track imported functions
        
    def get_temp_var(self):
        """Generate unique temporary variable name"""
        self.temp_counter += 1
        return f"%tmp_{self.temp_counter}"
    
    def get_label(self, prefix):
        """Generate unique label name"""
        self.label_counter += 1
        return f"{prefix}_{self.label_counter}"

def print_header():
    print("🐍 Dolet to LLVM IR Converter - Enhanced Version")
    print("=" * 50)
    print("Converting Dolet source to LLVM IR")
    print()

def read_dolet_file(filename):
    """Read and return the contents of a Dolet file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ Read Dolet file: {filename}")
        print(f"   Content length: {len(content)} characters")
        return content
    except FileNotFoundError:
        print(f"❌ Error: File not found: {filename}")
        return None
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None

def parse_dolet_code(source_code):
    """Parse Dolet source code and extract statements"""
    print("\n🔍 Parsing Dolet code...")
    
    lines = source_code.strip().split('\n')
    statements = []
    
    for i, line in enumerate(lines, 1):
        # Keep original line with indentation for Python-style parsing
        original_line = line
        stripped_line = line.strip()

        # Skip empty lines and comments
        if not stripped_line or stripped_line.startswith('#'):
            continue

        print(f"   Line {i}: {stripped_line}")
        statements.append({
            'line_number': i,
            'content': original_line,  # Keep original indentation
            'type': detect_statement_type(stripped_line)  # Use stripped for type detection
        })
    
    print(f"✅ Parsed {len(statements)} statements")
    return statements

def detect_statement_type(line):
    """Detect the type of Dolet statement"""
    if line.startswith('print '):
        content = line[4:].strip()
        if '+' in content and ('"' in content or not content.startswith('"')):
            return 'print_expression'
        return 'print'
    elif line.startswith('set '):
        # Check if it's an array assignment
        if ' = [' in line and line.rstrip().endswith(']'):
            return 'array_assignment'
        # Check if it's array indexing (e.g., set x = arr[0])
        elif '[' in line and ']' in line and ' = ' in line:
            return 'array_index_assignment'
        return 'set'
    elif line.startswith('int '):
        return 'int_declaration'
    elif line.startswith('string '):
        return 'string_declaration'
    elif line.startswith('float '):
        return 'float_declaration'
    elif line.startswith('double '):
        return 'double_declaration'
    elif line.startswith('char '):
        return 'char_declaration'
    elif line.startswith('bool '):
        return 'bool_declaration'
    elif line.startswith('if '):
        return 'if'
    elif line.startswith('elif '):
        return 'elif'
    elif line.startswith('else'):
        return 'else'
    elif line == 'end':
        return 'end'
    elif line == 'break':
        return 'break'
    elif line == 'continue':
        return 'continue'
    elif line.startswith('switch '):
        return 'switch'
    elif line.startswith('case '):
        return 'case'
    elif line.startswith('default'):
        return 'default'
    elif line.startswith('try'):
        return 'try'
    elif line.startswith('catch '):
        return 'catch'
    elif line.startswith('throw '):
        return 'throw'
    elif line.startswith('finally'):
        return 'finally'
    elif line.startswith('while '):
        return 'while'
    elif line.startswith('for ') and ' to ' in line:
        return 'for'
    elif line.startswith('fun '):
        return 'function'
    elif line.startswith('return '):
        return 'return'
    elif line.startswith('import '):
        return 'import'
    elif line.startswith('write('):
        return 'write'
    elif line.startswith('write_file('):
        return 'write_file'
    elif line.startswith('read_file('):
        return 'read_file'
    elif line.startswith('remove_file('):
        return 'remove_file'
    elif line.startswith('length('):
        return 'string_length'
    elif line.startswith('substring('):
        return 'string_substring'
    elif line.startswith('split('):
        return 'string_split'
    elif line.startswith('replace('):
        return 'string_replace'
    elif line.startswith('contains('):
        return 'string_contains'
    elif line.startswith('uppercase('):
        return 'string_uppercase'
    elif line.startswith('lowercase('):
        return 'string_lowercase'
    elif line.startswith('trim('):
        return 'string_trim'
    elif line.startswith('system('):
        return 'system'
    elif line.startswith('argc('):
        return 'argc'
    elif line.startswith('argv('):
        return 'argv'
    elif line.startswith('read_file('):
        return 'read_file'
    elif line.startswith('file_exists('):
        return 'file_exists'
    elif '(' in line and line.endswith(')'):
        return 'function_call'
    else:
        return 'unknown'

def calculate_llvm_string_length(text):
    """Calculate the actual byte length in LLVM IR after escape processing"""
    # In LLVM IR, we need to count actual bytes, not characters
    # UTF-8 characters can take multiple bytes
    byte_count = len(text.encode('utf-8'))
    # Add 2 for \0A\00 (newline + null terminator)
    return byte_count + 2

def extract_string_literals(statements):
    """Extract all string literals from statements"""
    print("\n📝 Extracting string literals...")
    string_literals = []

    def extract_from_statement(stmt):
        """Helper function to extract strings from a single statement"""
        if stmt['type'] == 'print':
            content = stmt['content'][4:].strip()
            if content.startswith('"') and content.endswith('"'):
                string_text = content[1:-1]
                # Unescape the string during parsing
                unescaped_text = string_text.replace('\\"', '"').replace('\\\\', '\\').replace('\\n', '\n').replace('\\t', '\t')
                if unescaped_text not in string_literals:
                    string_literals.append(unescaped_text)
                    print(f"   Found string: \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\"")
        elif stmt['type'] == 'function':
            # Extract strings from function body
            lines = stmt['content'].split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('print '):
                    content = line[4:].strip()
                    if content.startswith('"') and content.endswith('"'):
                        string_text = content[1:-1]
                        if string_text not in string_literals:
                            string_literals.append(string_text)
                            print(f"   Found string: \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\"")

    # Extract from main statements
    for stmt in statements:
        extract_from_statement(stmt)

    # Add array string literals
    for stmt in statements:
        if stmt['type'] == 'array_assignment':
            content = stmt['content'][4:].strip()
            if '=' in content:
                array_content = content.split('=', 1)[1].strip()
                if array_content.startswith('[') and array_content.endswith(']'):
                    array_items_str = array_content[1:-1].strip()
                    if array_items_str:
                        items = [item.strip() for item in array_items_str.split(',')]
                        for item in items:
                            if item.startswith('"') and item.endswith('"'):
                                string_value = item[1:-1]  # Remove quotes
                                string_literals.append(string_value)
                                print(f"   Found array string: \"{string_value}\"")

    # Add typed variable string literals
    for stmt in statements:
        if stmt['type'] == 'string_declaration':
            content = stmt['content'].strip()
            if ' = ' in content:
                value = content.split(' = ', 1)[1].strip()
                if value.startswith('"') and value.endswith('"'):
                    string_value = value[1:-1]  # Remove quotes
                    string_literals.append(string_value)
                    print(f"   Found typed string: \"{string_value}\"")

    # Add contains() function string literals (without newlines)
    for stmt in statements:
        if stmt['type'] == 'bool_declaration':
            content = stmt['content'].strip()
            if ' = ' in content and 'contains(' in content:
                value = content.split(' = ', 1)[1].strip()
                # Parse contains(string_var, "substring")
                if value.startswith('contains(') and value.endswith(')'):
                    args_str = value[9:-1].strip()
                    args = [arg.strip() for arg in args_str.split(',')]
                    if len(args) >= 2:
                        substring_arg = args[1]
                        if substring_arg.startswith('"') and substring_arg.endswith('"'):
                            string_value = substring_arg[1:-1]  # Remove quotes
                            # Don't add newline for contains() strings
                            if string_value not in string_literals:
                                string_literals.append(string_value)
                                print(f"   Found contains string: \"{string_value}\"")

    print(f"✅ Found {len(string_literals)} string literals")
    return string_literals

def extract_variables(statements, control_blocks=None):
    """Extract all variables from set statements - enhanced version"""
    print("\n🔢 Extracting variables...")

    variables = []
    seen_vars = {}  # Track defined variables with their first value

    if control_blocks is None:
        control_blocks = []

    # First pass: Process typed variable declarations to establish types
    for stmt in statements:
        if stmt['type'] in ['int_declaration', 'string_declaration', 'float_declaration', 'double_declaration', 'char_declaration', 'bool_declaration']:
            content = stmt['content'].strip()

            # Parse typed declaration
            if ' = ' in content:
                parts = content.split(' = ', 1)
                type_and_name = parts[0].strip()
                value = parts[1].strip()

                type_parts = type_and_name.split()
                if len(type_parts) >= 2:
                    var_type = type_parts[0]
                    var_name = type_parts[1]

                    # Process value based on type
                    processed_value = None
                    if var_type == 'int':
                        if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                            processed_value = int(value)
                        else:
                            processed_value = 0  # Expression
                    elif var_type == 'string':
                        if value.startswith('"') and value.endswith('"'):
                            processed_value = value[1:-1]
                        else:
                            processed_value = ""  # Expression
                    elif var_type == 'float':
                        try:
                            processed_value = float(value)
                        except ValueError:
                            processed_value = 0.0  # Expression
                    elif var_type == 'double':
                        try:
                            processed_value = float(value)
                        except ValueError:
                            processed_value = 0.0  # Expression
                    elif var_type == 'char':
                        if value.startswith("'") and value.endswith("'") and len(value) == 3:
                            processed_value = value[1]
                        else:
                            processed_value = '\0'  # Expression
                    elif var_type == 'bool':
                        if value.lower() in ['true', 'false']:
                            processed_value = value.lower() == 'true'
                        else:
                            processed_value = False  # Expression

                    if var_name not in seen_vars:
                        seen_vars[var_name] = var_type
                        variables.append({
                            'name': var_name,
                            'type': var_type,
                            'value': processed_value,
                            'line_number': stmt['line_number']
                        })
                        print(f"   Found {var_type} variable: {var_name} = {processed_value}")
            else:
                # Declaration without initialization
                type_parts = content.split()
                if len(type_parts) >= 2:
                    var_type = type_parts[0]
                    var_name = type_parts[1]

                    # Default values
                    default_values = {
                        'int': 0,
                        'string': "",
                        'float': 0.0,
                        'double': 0.0,
                        'char': '\0',
                        'bool': False
                    }

                    if var_name not in seen_vars:
                        seen_vars[var_name] = var_type
                        variables.append({
                            'name': var_name,
                            'type': var_type,
                            'value': default_values.get(var_type, 0),
                            'line_number': stmt['line_number']
                        })
                        print(f"   Found {var_type} variable: {var_name} (default)")

    # Second pass: Process set statements and other variable assignments
    for stmt in statements:
        if stmt['type'] == 'set':
            # Remove 'set ' from the beginning, handling indentation
            content = stmt['content'].strip()
            if content.startswith('set '):
                content = content[4:].strip()
            if '=' in content:
                var_name = content.split('=')[0].strip()
                var_value = content.split('=', 1)[1].strip()
                
                # Determine variable type
                var_type = 'unknown'
                processed_value = var_value
                
                if var_value.startswith('"') and var_value.endswith('"'):
                    var_type = 'string'
                    raw_string = var_value[1:-1]

                    # Process escape sequences
                    processed_value = ""
                    i = 0
                    while i < len(raw_string):
                        if i < len(raw_string) - 1 and raw_string[i] == '\\':
                            if raw_string[i+1] == 'n':
                                processed_value += '\n'
                                i += 2
                            elif raw_string[i+1] == 't':
                                processed_value += '\t'
                                i += 2
                            elif raw_string[i+1] == '\\':
                                processed_value += '\\'
                                i += 2
                            elif raw_string[i+1] == '"':
                                processed_value += '"'
                                i += 2
                            else:
                                processed_value += raw_string[i]
                                i += 1
                        else:
                            processed_value += raw_string[i]
                            i += 1
                elif var_value.isdigit() or (var_value.startswith('-') and var_value[1:].isdigit()):
                    var_type = 'integer'
                    processed_value = int(var_value)
                elif var_value.lower() in ['true', 'false']:
                    var_type = 'boolean'
                    processed_value = var_value.lower() == 'true'
                elif any(op in var_value for op in ['==', '!=', '<', '>', '<=', '>=']):
                    # Comparison expression - boolean result
                    var_type = 'boolean'
                    processed_value = False  # Will be calculated at runtime
                elif any(op in var_value for op in ['+', '-', '*', '/', '%']):
                    # Check if it's string concatenation or mathematical expression
                    if '"' in var_value and '+' in var_value:
                        # String concatenation
                        var_type = 'string'
                        processed_value = ""  # Will be calculated at runtime
                    else:
                        # Mathematical expression - assume integer result
                        var_type = 'integer'
                        processed_value = 0  # Will be calculated at runtime
                elif var_value == "get_args()":
                    var_type = 'array'
                    processed_value = []
                elif var_value.startswith("length("):
                    var_type = 'integer'
                    processed_value = 0
                elif var_value.startswith("args["):
                    var_type = 'string'
                    processed_value = ""
                elif "+" in var_value:
                    var_type = 'string'
                    processed_value = ""
                elif var_value.startswith("read_file("):
                    var_type = 'string'
                    processed_value = ""
                elif var_value.startswith("system("):
                    var_type = 'integer'
                    processed_value = 0
                elif var_value.startswith("argc("):
                    var_type = 'integer'
                    processed_value = 0
                elif var_value.startswith("argv("):
                    var_type = 'string'
                    processed_value = ""
                elif var_value.startswith("file_exists("):
                    var_type = 'integer'
                    processed_value = 0
                elif '(' in var_value and ')' in var_value and not any(op in var_value for op in ['+', '-', '*', '/', '%', '==', '!=', '<', '>', '<=', '>=']):
                    # Function call - assume integer return for now
                    var_type = 'integer'
                    processed_value = 0
                    
                # Check if variable already exists
                if var_name not in seen_vars:
                    seen_vars[var_name] = var_type
                    variables.append({
                        'name': var_name,
                        'value': processed_value,
                        'type': var_type,
                        'line_number': stmt['line_number']
                    })
                    print(f"   Found {var_type} variable: {var_name} = {processed_value}")
                else:
                    # Variable already defined, just note the reassignment
                    print(f"   Variable {var_name} reassigned at line {stmt['line_number']}")
                    
    # Add for loop variables
    for block in control_blocks:
        if block['type'] == 'for':
            var_name = block['variable']
            if var_name not in seen_vars:
                seen_vars[var_name] = 'integer'
                variables.append({
                    'name': var_name,
                    'value': 0,
                    'type': 'integer',
                    'line_number': block['start_line']
                })
                print(f"   Found for loop variable: {var_name} = 0")

        # Check nested for loops
        def check_nested_for(statements):
            for stmt in statements:
                if isinstance(stmt, dict) and stmt.get('type') == 'for':
                    var_name = stmt['variable']
                    if var_name not in seen_vars:
                        seen_vars[var_name] = 'integer'
                        variables.append({
                            'name': var_name,
                            'value': 0,
                            'type': 'integer',
                            'line_number': stmt['start_line']
                        })
                        print(f"   Found nested for loop variable: {var_name} = 0")
                    check_nested_for(stmt.get('body_statements', []))

        if block['type'] == 'if':
            check_nested_for(block.get('if_statements', []))
            # Check elif blocks
            for elif_block in block.get('elif_blocks', []):
                check_nested_for(elif_block.get('statements', []))
            check_nested_for(block.get('else_statements', []))
        elif block['type'] in ['while', 'for']:
            check_nested_for(block.get('body_statements', []))

    # Add array assignments
    for stmt in statements:
        if stmt['type'] == 'array_assignment':
            content = stmt['content'][4:].strip()
            if '=' in content:
                var_name = content.split('=')[0].strip()
                array_content = content.split('=', 1)[1].strip()

                # Parse array content [1, 2, 3]
                if array_content.startswith('[') and array_content.endswith(']'):
                    array_items_str = array_content[1:-1].strip()
                    array_items = []

                    if array_items_str:
                        # Split by comma and process each item
                        items = [item.strip() for item in array_items_str.split(',')]
                        for item in items:
                            if item.startswith('"') and item.endswith('"'):
                                array_items.append({'type': 'string', 'value': item[1:-1]})
                            elif item.lower() in ['true', 'false']:
                                array_items.append({'type': 'boolean', 'value': item.lower() == 'true'})
                            elif item.isdigit() or (item.startswith('-') and item[1:].isdigit()):
                                array_items.append({'type': 'integer', 'value': int(item)})
                            else:
                                # Variable reference
                                array_items.append({'type': 'variable', 'value': item})

                    # Determine array element type
                    if array_items:
                        first_type = array_items[0]['type']
                        if all(item['type'] == first_type for item in array_items):
                            array_type = f'array_{first_type}'
                        else:
                            array_type = 'array_mixed'
                    else:
                        array_type = 'array_empty'

                    if var_name not in seen_vars:
                        seen_vars[var_name] = array_type
                        variables.append({
                            'name': var_name,
                            'type': array_type,
                            'value': array_items,
                            'line_number': stmt['line_number']
                        })
                        print(f"   Found {array_type} variable: {var_name} = {array_items}")

    # Add array index assignments
    for stmt in statements:
        if stmt['type'] == 'array_index_assignment':
            content = stmt['content'][4:].strip()
            if '=' in content:
                var_name = content.split('=')[0].strip()
                array_access = content.split('=', 1)[1].strip()

                # Parse array access: arr[0]
                if '[' in array_access and ']' in array_access:
                    array_name = array_access.split('[')[0].strip()
                    index_str = array_access.split('[')[1].split(']')[0].strip()

                    # Find the array to determine the element type
                    array_var = None
                    for var in variables:
                        if var['name'] == array_name:
                            array_var = var
                            break

                    # Determine variable type based on array type
                    if array_var:
                        if array_var['type'] == 'array_integer':
                            var_type = 'integer'
                        elif array_var['type'] == 'array_string':
                            var_type = 'string'
                        elif array_var['type'] == 'array_boolean':
                            var_type = 'boolean'
                        else:
                            var_type = 'integer'  # Default
                    else:
                        var_type = 'integer'  # Default if array not found

                    if var_name not in seen_vars:
                        seen_vars[var_name] = var_type
                        variables.append({
                            'name': var_name,
                            'type': var_type,
                            'value': 0 if var_type == 'integer' else (False if var_type == 'boolean' else ""),
                            'line_number': stmt['line_number'],
                            'array_access': {'array': array_name, 'index': index_str}
                        })
                        print(f"   Found array access variable: {var_name} = {array_name}[{index_str}] ({var_type})")



    print(f"✅ Found {len(variables)} unique variables")
    return variables

def extract_all_string_expressions(statements):
    """Extract all string expressions for constant generation"""
    print("\n🔗 Extracting string expressions...")
    
    expressions = []
    for stmt in statements:
        if stmt['type'] == 'print_expression':
            content = stmt['content'][4:].strip()
            parts = parse_string_expression_parts(content)
            for part in parts:
                if part['type'] == 'string_literal':
                    expressions.append(part['value'])
                    
    # Remove duplicates
    unique_expressions = list(dict.fromkeys(expressions))
    print(f"✅ Found {len(unique_expressions)} unique string expression parts")
    return unique_expressions

def parse_string_expression_parts(expression):
    """Parse string concatenation expressions into parts"""
    parts = []
    current = ""
    in_string = False
    i = 0
    
    while i < len(expression):
        char = expression[i]
        
        if char == '"' and (i == 0 or expression[i-1] != '\\'):
            if in_string:
                # End of string literal
                parts.append({
                    'type': 'string_literal',
                    'value': current
                })
                current = ""
                in_string = False
            else:
                # Start of string literal
                in_string = True
                current = ""
        elif char == '+' and not in_string:
            # Concatenation operator - but check for function calls first
            # Count parentheses to see if we're inside a function call
            paren_count = current.count('(') - current.count(')')
            if paren_count > 0:
                # We're inside a function call, continue
                current += char
            else:
                # End of current part
                if current.strip():
                    # Check if it's a function call
                    part_name = current.strip()
                    if part_name.startswith('length(') and part_name.endswith(')'):
                        parts.append({
                            'type': 'function_call',
                            'name': 'length',
                            'args': [part_name[7:-1].strip()]
                        })
                    else:
                        # Variable reference
                        parts.append({
                            'type': 'variable',
                            'name': part_name
                        })
                    current = ""
        else:
            current += char
        i += 1
    
    # Handle remaining content
    if current:
        if in_string:
            parts.append({
                'type': 'string_literal',
                'value': current
            })
        elif current.strip():
            # Check if it's a function call
            part_name = current.strip()
            if part_name.startswith('length(') and part_name.endswith(')'):
                parts.append({
                    'type': 'function_call',
                    'name': 'length',
                    'args': [part_name[7:-1].strip()]
                })
            else:
                parts.append({
                    'type': 'variable',
                    'name': part_name
                })
            
    return parts

def extract_control_flow(statements):
    """Extract control flow structures - Python-style indentation (no 'end' needed)"""
    print("\n🔀 Extracting control flow (Python-style indentation)...")

    control_blocks = []
    block_stack = []

    def get_indentation_level(content):
        """Get indentation level of a line (number of leading spaces)"""
        return len(content) - len(content.lstrip())

    def close_blocks_at_indentation(target_level):
        """Close all blocks that are deeper than target_level"""
        while block_stack and block_stack[-1].get('indentation_level', 0) >= target_level:
            closed_block = block_stack.pop()
            control_blocks.append(closed_block)
            print(f"   📝 Closed {closed_block['type']} block at line {closed_block['start_line']} (indentation: {closed_block.get('indentation_level', 0)})")
    
    for stmt in statements:
        current_indentation = get_indentation_level(stmt['content'])

        # Close blocks that have ended due to indentation
        if block_stack:
            # If current line has less or equal indentation than the last block, close blocks
            while block_stack and current_indentation <= block_stack[-1].get('indentation_level', 0):
                # Only close if this is not a continuation of the same block type
                if stmt['type'] not in ['elif', 'else'] or current_indentation < block_stack[-1].get('indentation_level', 0):
                    closed_block = block_stack.pop()
                    control_blocks.append(closed_block)
                    print(f"   📝 Closed {closed_block['type']} block at line {closed_block['start_line']} due to indentation")
                else:
                    break

        if stmt['type'] == 'if':
            # Extract condition by removing 'if ' prefix (4 characters including space)
            condition = stmt['content'].strip()[3:].strip()
            new_block = {
                'type': 'if',
                'condition': condition,
                'if_statements': [],
                'elif_blocks': [],  # New: store elif conditions and statements
                'else_statements': [],
                'start_line': stmt['line_number'],
                'in_else': False,
                'in_elif': False,  # New: track if we're in elif block
                'parent': block_stack[-1] if block_stack else None,
                'indentation_level': current_indentation  # Store indentation level
            }
            
            if block_stack:
                # Nested block
                parent_block = block_stack[-1]
                if parent_block['type'] == 'while':
                    parent_block['body_statements'].append(new_block)
                elif parent_block['type'] == 'for':
                    parent_block['body_statements'].append(new_block)
                elif parent_block.get('in_else', False):
                    parent_block['else_statements'].append(new_block)
                else:
                    parent_block['if_statements'].append(new_block)
            else:
                # Top-level block
                control_blocks.append(new_block)
                
            block_stack.append(new_block)
            print(f"   Found if: {condition} (depth: {len(block_stack)})")

        elif stmt['type'] == 'while':
            # Extract condition by removing 'while ' prefix (6 characters including space)
            condition = stmt['content'].strip()[6:].strip()
            new_block = {
                'type': 'while',
                'condition': condition,
                'body_statements': [],
                'start_line': stmt['line_number'],
                'parent': block_stack[-1] if block_stack else None,
                'indentation_level': current_indentation  # Store indentation level
            }

            if block_stack:
                # Nested block
                parent_block = block_stack[-1]
                if parent_block['type'] == 'while':
                    parent_block['body_statements'].append(new_block)
                elif parent_block.get('in_else', False):
                    parent_block['else_statements'].append(new_block)
                else:
                    parent_block['if_statements'].append(new_block)
            else:
                # Top-level block
                control_blocks.append(new_block)

            block_stack.append(new_block)
            print(f"   Found while: {condition} (depth: {len(block_stack)})")

        elif stmt['type'] == 'for':
            # Parse for loop: "for var = start to end" or "for var = start to end step increment"
            for_content = stmt['content'][4:].strip()  # Remove 'for '

            # Parse for loop components
            if ' to ' in for_content:
                var_part, rest = for_content.split(' to ', 1)
                var_name = var_part.split('=')[0].strip()
                start_value = var_part.split('=')[1].strip()

                if ' step ' in rest:
                    end_value, step_value = rest.split(' step ', 1)
                    end_value = end_value.strip()
                    step_value = step_value.strip()
                else:
                    end_value = rest.strip()
                    step_value = "1"

                new_block = {
                    'type': 'for',
                    'variable': var_name,
                    'start': start_value,
                    'end': end_value,
                    'step': step_value,
                    'body_statements': [],
                    'start_line': stmt['line_number'],
                    'parent': block_stack[-1] if block_stack else None,
                    'indentation_level': current_indentation  # Store indentation level
                }

                if block_stack:
                    # Nested block
                    parent_block = block_stack[-1]
                    if parent_block['type'] == 'while':
                        parent_block['body_statements'].append(new_block)
                    elif parent_block['type'] == 'for':
                        parent_block['body_statements'].append(new_block)
                    elif parent_block.get('in_else', False):
                        parent_block['else_statements'].append(new_block)
                    else:
                        parent_block['if_statements'].append(new_block)
                else:
                    # Top-level block
                    control_blocks.append(new_block)

                block_stack.append(new_block)
                print(f"   Found for: {var_name} = {start_value} to {end_value} step {step_value} (depth: {len(block_stack)})")

                # Add loop variable to variables list if not exists
                # This will be processed later in extract_variables

        elif stmt['type'] == 'elif' and block_stack:
            current_block = block_stack[-1]
            if current_block['type'] == 'if':
                # Add elif condition and create new elif block
                # Extract condition by removing 'elif ' prefix (5 characters including space)
                elif_condition = stmt['content'].strip()[5:].strip()
                elif_block = {
                    'condition': elif_condition,
                    'statements': []
                }
                current_block['elif_blocks'].append(elif_block)
                current_block['in_elif'] = True
                current_block['current_elif'] = len(current_block['elif_blocks']) - 1
                print(f"   Found elif: {elif_condition} for if at line {current_block['start_line']}")

        elif stmt['type'] == 'else' and block_stack:
            current_block = block_stack[-1]
            if current_block['type'] == 'if':
                current_block['in_else'] = True
                current_block['in_elif'] = False  # No longer in elif
                print(f"   Found else for if at line {current_block['start_line']}")
            # While loops don't have else clauses
            
        elif block_stack and current_indentation > block_stack[-1].get('indentation_level', 0):
            # Statement belongs to current block (indented more than the block)
            current_block = block_stack[-1]
            print(f"   📝 Adding statement to {current_block['type']} block: {stmt['type']} - {stmt['content'][:50]}")
            if current_block['type'] == 'while':
                current_block['body_statements'].append(stmt)
            elif current_block['type'] == 'for':
                current_block['body_statements'].append(stmt)
            elif current_block.get('in_elif', False):
                # Add to current elif block
                elif_index = current_block.get('current_elif', 0)
                current_block['elif_blocks'][elif_index]['statements'].append(stmt)
                print(f"   📝 Added to elif block {elif_index}")
            elif current_block.get('in_else', False):
                current_block['else_statements'].append(stmt)
                print(f"   📝 Added to else block")
            else:
                current_block['if_statements'].append(stmt)
                print(f"   📝 Added to if block (total: {len(current_block['if_statements'])})")

    # Close any remaining blocks at the end
    while block_stack:
        closed_block = block_stack.pop()
        # Add end_line for compatibility
        closed_block['end_line'] = closed_block.get('start_line', 0) + len(closed_block.get('if_statements', [])) + len(closed_block.get('body_statements', []))
        control_blocks.append(closed_block)
        print(f"   📝 Closed remaining {closed_block['type']} block at line {closed_block['start_line']}")

    # Add end_line to all control blocks for compatibility
    for block in control_blocks:
        if 'end_line' not in block:
            # Calculate approximate end line based on statements
            stmt_count = len(block.get('if_statements', [])) + len(block.get('body_statements', [])) + len(block.get('else_statements', []))
            block['end_line'] = block.get('start_line', 0) + stmt_count

    print(f"✅ Found {len(control_blocks)} control flow blocks (Python-style indentation)")
    return control_blocks

class LLVMGenerator:
    def __init__(self, statements, string_literals, variables, control_blocks):
        self.statements = statements
        self.string_literals = string_literals
        self.variables = variables
        self.control_blocks = control_blocks
        self.llvm_lines = []
        self.temp_counter = 0
        self.label_counter = 0
        self.loop_stack = []  # Stack to track nested loops for break/continue
        self.string_constants = {}
        self.string_expressions = extract_all_string_expressions(statements)
        self.imported_modules = {}  # Track imported modules
        self.imported_functions = {}  # Track imported functions
        self.local_functions = {}  # Track locally defined functions
        
    def get_temp_var(self):
        self.temp_counter += 1
        return f"%tmp_{self.temp_counter}"
        
    def get_label(self, prefix):
        self.label_counter += 1
        return f"{prefix}_{self.label_counter}"
        
    def generate(self):
        """Generate complete LLVM IR"""
        self._generate_header()
        self._generate_declarations()
        self._generate_constants()
        self._generate_globals()

        # Process imports first to populate imported_modules
        self._process_imports()

        # Then generate imported functions
        self._generate_imported_functions()

        # Finally generate main function
        self._generate_main()

        # Add any try/catch variables that were created during statement processing
        self._add_missing_global_variables()

        # Add any new string constants that were created during function generation
        self._add_new_string_constants()

        return '\n'.join(self.llvm_lines)

    def _add_new_string_constants(self):
        """Add any new string constants that were created during function generation"""
        if not hasattr(self, 'string_constants') or not self.string_constants:
            return

        # Find the position after the existing string constants
        constants_end_index = -1
        for i, line in enumerate(self.llvm_lines):
            if line.startswith("; String constants"):
                # Find the end of the constants section
                for j in range(i + 1, len(self.llvm_lines)):
                    if self.llvm_lines[j].strip() == "" and j + 1 < len(self.llvm_lines) and not self.llvm_lines[j + 1].startswith("@"):
                        constants_end_index = j
                        break
                break

        if constants_end_index == -1:
            return

        # Add new string constants - avoid duplicates
        new_constants = []
        added_constants = set()
        for string_text, const_name in self.string_constants.items():
            if const_name not in added_constants:
                escaped_text = self._escape_string(string_text)
                string_length = len(string_text.encode('utf-8')) + 1  # +1 for null terminator
                new_constants.append(f"{const_name} = private unnamed_addr constant [{string_length} x i8] c\"{escaped_text}\\00\", align 1")
                added_constants.add(const_name)

        # Insert new constants at the end of the constants section
        for i, const_line in enumerate(new_constants):
            self.llvm_lines.insert(constants_end_index + i, const_line)

    def _process_imports(self):
        """Process all import statements to populate imported_modules"""
        print("   🔍 Processing imports...")

        for stmt in self.statements:
            if stmt['type'] == 'import':
                self._generate_import(stmt)

    def _generate_imported_functions(self):
        """Generate LLVM IR for all imported functions"""
        if not hasattr(self, 'imported_modules'):
            return

        for module_name, module_info in self.imported_modules.items():
            print(f"   🔧 Generating imported functions from {module_name}...")
            statements = module_info['statements']

            # Process statements to generate function definitions
            for stmt in statements:
                if stmt['type'] == 'function':
                    self._generate_statement(stmt)
                elif stmt['type'] == 'end' and hasattr(self, 'current_function') and self.current_function:
                    self._generate_statement(stmt)
                elif hasattr(self, 'current_function') and self.current_function:
                    # Inside function body
                    self._generate_statement(stmt)
        
    def _generate_header(self):
        self.llvm_lines.extend([
            "; Generated by Dolet to LLVM IR Converter - Enhanced Version",
            f"; Source: Dolet code with {len(self.statements)} statements",
            "target triple = \"x86_64-pc-windows-msvc\"",
            ""
        ])
        
    def _generate_declarations(self):
        self.llvm_lines.extend([
            "; Function declarations",
            "declare i32 @printf(i8*, ...)",
            "declare i8* @fopen(i8*, i8*)",
            "declare i32 @fprintf(i8*, i8*, ...)",
            "declare i32 @fclose(i8*)",
            "declare i32 @system(i8*)",
            "declare i8* @malloc(i64)",
            "declare void @free(i8*)",
            "declare i8* @strcpy(i8*, i8*)",
            "declare i8* @strcat(i8*, i8*)",
            "declare i64 @strlen(i8*)",
            "declare i8* @strstr(i8*, i8*)",
            "declare i32 @strcmp(i8*, i8*)",
            "declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)",
            "declare i32 @fseek(i8*, i64, i32)",
            "declare i64 @ftell(i8*)",
            "declare i64 @fread(i8*, i64, i64, i8*)",
            "declare i64 @fwrite(i8*, i64, i64, i8*)",
            "declare i32 @remove(i8*)",
            "declare i32 @access(i8*, i32)",
            "declare void @rewind(i8*)",
            ""
        ])
        
    def _generate_constants(self):
        self.llvm_lines.append("; String constants")
        
        # Regular string literals
        for i, string_text in enumerate(self.string_literals):
            escaped_text = self._escape_string(string_text)
            # Calculate the actual byte length for LLVM IR
            # Use UTF-8 encoding to get correct byte count
            byte_length = len(string_text.encode('utf-8'))
            # For print statements, we add newline, so +2 for \0A\00
            string_length = byte_length + 2
            self.llvm_lines.append(f"@.str_{i} = private unnamed_addr constant [{string_length} x i8] c\"{escaped_text}\\0A\\00\", align 1")
            
        # String expression parts
        for i, expr_text in enumerate(self.string_expressions):
            escaped_text = self._escape_string(expr_text)
            # Use proper byte length calculation (no newline for expression parts)
            string_length = len(expr_text.encode('utf-8')) + 1  # +1 for null terminator only
            self.llvm_lines.append(f"@.str_expr_{i} = private unnamed_addr constant [{string_length} x i8] c\"{escaped_text}\\00\", align 1")

        # Contains string constants (without newlines) - avoid duplicates
        added_constants = set()
        for string_text, const_name in self.string_constants.items():
            if const_name not in added_constants:
                escaped_text = self._escape_string(string_text)
                # Use proper byte length calculation (no newline for contains strings)
                string_length = len(string_text.encode('utf-8')) + 1  # +1 for null terminator only
                # const_name already includes @, so don't add another @
                self.llvm_lines.append(f"{const_name} = private unnamed_addr constant [{string_length} x i8] c\"{escaped_text}\\00\", align 1")
                added_constants.add(const_name)
            
        # Format strings
        self.llvm_lines.extend([
            "@.str_int = private unnamed_addr constant [4 x i8] c\"%d\\0A\\00\", align 1",
            "@.str_int_fmt = private unnamed_addr constant [3 x i8] c\"%d\\00\", align 1",
            "@.str_fmt = private unnamed_addr constant [3 x i8] c\"%s\\00\", align 1",
            "@.str_float = private unnamed_addr constant [5 x i8] c\"%.3f\\00\", align 1",
            "@.str_char = private unnamed_addr constant [3 x i8] c\"%c\\00\", align 1",
            "@.str_true = private unnamed_addr constant [5 x i8] c\"true\\00\", align 1",
            "@.str_false = private unnamed_addr constant [6 x i8] c\"false\\00\", align 1",
            "@.str_newline = private unnamed_addr constant [2 x i8] c\"\\0A\\00\", align 1",
            "@write_mode = private unnamed_addr constant [2 x i8] c\"w\\00\", align 1",
            "@read_mode = private unnamed_addr constant [2 x i8] c\"r\\00\", align 1",
            "@empty_string = private unnamed_addr constant [1 x i8] c\"\\00\", align 1",
            ""
        ])
        
    def _escape_string(self, text):
        """Properly escape string for LLVM IR with UTF-8 support"""
        result = ""
        for char in text:
            if char == '\\':
                result += '\\5C'
            elif char == '"':
                result += '\\22'
            elif char == '\n':
                result += '\\0A'
            elif char == '\r':
                result += '\\0D'
            elif char == '\t':
                result += '\\09'
            elif ord(char) < 32 or ord(char) > 126:
                # Non-printable ASCII or Unicode characters - encode as UTF-8 bytes
                utf8_bytes = char.encode('utf-8')
                for byte in utf8_bytes:
                    result += f'\\{byte:02X}'
            else:
                result += char
        return result

    def _escape_printf_format(self, text):
        """Escape string for printf format (keep actual newline character)"""
        # For printf format strings, we want actual newline character (ASCII 10)
        # Don't escape \n to \0A - keep it as actual newline
        return text.replace('\\', '\\\\').replace('"', '\\"')
        
    def _generate_globals(self):
        self.llvm_lines.append("; Global variables")

        # Always add global argc and argv for function access
        self.llvm_lines.append("@global_argc = global i32 0, align 4")
        self.llvm_lines.append("@global_argv = global i8** null, align 8")

        if not self.variables:
            print("   ⚠️ No user variables found for global declarations")
            self.llvm_lines.append("")  # Add empty line
            return

        print(f"   🔧 Generating global declarations for {len(self.variables)} variables:")
        for var in self.variables:
            print(f"      - {var['name']}: {var['type']}")

        for var in self.variables:
            var_type = var['type']
            var_name = var['name']

            if var_type in ['integer', 'int']:
                self.llvm_lines.append(f"@global_{var_name} = global i32 0, align 4")
            elif var_type in ['string']:
                # Check if variable has initial value
                var_value = var.get('value', '')
                if var_value == '""' or var_value == '':
                    # Initialize with empty string instead of null
                    self.llvm_lines.append(f"@global_{var_name} = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8")
                else:
                    self.llvm_lines.append(f"@global_{var_name} = global i8* null, align 8")
            elif var_type in ['boolean', 'bool']:
                self.llvm_lines.append(f"@global_{var_name} = global i1 0, align 1")
            elif var_type == 'array':
                self.llvm_lines.append(f"@global_{var_name} = global i8** null, align 8")
            elif var_type.startswith('array_integer'):
                # Integer array - determine size from value
                array_size = len(var.get('value', []))
                self.llvm_lines.append(f"@global_{var_name} = global [{array_size} x i32]* null, align 8")
            elif var_type.startswith('array_string'):
                # String array - determine size from value
                array_size = len(var.get('value', []))
                self.llvm_lines.append(f"@global_{var_name} = global [{array_size} x i8*]* null, align 8")
            elif var_type.startswith('array_boolean'):
                # Boolean array - determine size from value
                array_size = len(var.get('value', []))
                self.llvm_lines.append(f"@global_{var_name} = global [{array_size} x i1]* null, align 8")
            elif var_type == 'float':
                self.llvm_lines.append(f"@global_{var_name} = global float 0.0, align 4")
            elif var_type == 'double':
                self.llvm_lines.append(f"@global_{var_name} = global double 0.0, align 8")
            elif var_type == 'char':
                self.llvm_lines.append(f"@global_{var_name} = global i8 0, align 1")
            else:
                # Special handling for known string variables
                if var_name in ['input_file', 'output_file']:
                    print(f"   🔧 Special handling for string variable '{var_name}'")
                    self.llvm_lines.append(f"@global_{var_name} = global i8* null, align 8")
                else:
                    # Unknown type, default to i32
                    print(f"   ⚠️ Warning: Unknown variable type '{var_type}' for '{var_name}', defaulting to i32")
                    self.llvm_lines.append(f"@global_{var_name} = global i32 0, align 4")
                
        self.llvm_lines.append("")

    def _add_missing_global_variables(self):
        """Add global variable declarations for try/catch variables created during processing"""
        # Find the position where global variables section ends
        global_section_end = -1
        for i, line in enumerate(self.llvm_lines):
            if line.startswith("; Global variables"):
                # Find the end of global variables section
                for j in range(i + 1, len(self.llvm_lines)):
                    if self.llvm_lines[j].strip() == "" and j + 1 < len(self.llvm_lines) and not self.llvm_lines[j + 1].startswith("@global_"):
                        global_section_end = j
                        break
                break

        if global_section_end == -1:
            # No global variables section found, add it before main function
            for i, line in enumerate(self.llvm_lines):
                if line.startswith("; Main function"):
                    global_section_end = i - 1
                    self.llvm_lines.insert(i, "; Global variables")
                    self.llvm_lines.insert(i + 1, "")
                    global_section_end = i + 1
                    break

        # Check for missing try/catch variables
        existing_globals = set()
        for line in self.llvm_lines:
            if line.startswith("@global_"):
                var_name = line.split("@global_")[1].split(" ")[0]
                existing_globals.add(var_name)

        # Add missing try/catch variables
        missing_vars = []
        for var in self.variables:
            if var['name'] not in existing_globals:
                missing_vars.append(var)

        if missing_vars:
            print(f"   🔧 Adding {len(missing_vars)} missing global variables:")
            for var in missing_vars:
                print(f"      - {var['name']}: {var['type']}")

                var_type = var['type']
                var_name = var['name']

                if var_type in ['boolean', 'bool']:
                    global_line = f"@global_{var_name} = global i1 false, align 1"
                elif var_type in ['string']:
                    global_line = f"@global_{var_name} = global i8* null, align 8"
                elif var_type in ['integer', 'int']:
                    global_line = f"@global_{var_name} = global i32 0, align 4"
                else:
                    global_line = f"@global_{var_name} = global i32 0, align 4  ; unknown type: {var_type}"

                self.llvm_lines.insert(global_section_end, global_line)
                global_section_end += 1

    def _generate_main(self):
        # Check if source code has main function
        has_main_function = False
        self.has_main_function = False

        # Process imports first to collect functions (but don't generate LLVM yet)
        for stmt in self.statements:
            if stmt['type'] == 'import':
                self._generate_statement(stmt)

        # First pass: Generate function definitions
        skip_until = 0
        for stmt in self.statements:
            if stmt['line_number'] <= skip_until:
                continue

            if stmt['type'] == 'function':
                # Check if this is a main function and rename it
                if 'main()' in stmt['content'] or 'main(' in stmt['content']:
                    has_main_function = True
                    self.has_main_function = True
                    # Rename main to dolet_main to avoid conflicts
                    stmt['content'] = stmt['content'].replace('main(', 'dolet_main(')
                skip_until = self._generate_complete_function(stmt)

        # Add wrapper main function if source has its own main
        if has_main_function:
            self.llvm_lines.extend([
                "; Wrapper main function",
                "define i32 @main_wrapper(i32 %argc, i8** %argv) {",
                "entry:",
                "  ; Initialize stack variables for arguments",
                "  %argc_ptr = alloca i32, align 4",
                "  %argv_ptr = alloca i8**, align 8",
                "  store i32 %argc, i32* %argc_ptr, align 4",
                "  store i8** %argv, i8*** %argv_ptr, align 8",
                "  ; Store argc and argv in global variables for access from functions",
                "  store i32 %argc, i32* @global_argc, align 4",
                "  store i8** %argv, i8*** @global_argv, align 8",
                ""
            ])
        else:
            # Only add main function if it doesn't exist in the source code
            self.llvm_lines.extend([
                "; Main function",
                "define i32 @main(i32 %argc, i8** %argv) {",
                "entry:",
                "  ; Initialize stack variables for arguments",
                "  %argc_ptr = alloca i32, align 4",
                "  %argv_ptr = alloca i8**, align 8",
                "  store i32 %argc, i32* %argc_ptr, align 4",
                "  store i8** %argv, i8*** %argv_ptr, align 8",
                ""
            ])

        # Second pass: Process non-import, non-function statements
        # Skip if we have main function (statements will be in dolet_main)
        if not has_main_function:
            skip_until = 0
            for stmt in self.statements:
                if stmt['line_number'] <= skip_until:
                    continue

                if stmt['type'] == 'import':
                    continue  # Already processed
                elif stmt['type'] == 'function':
                    continue  # Already processed

                if stmt['type'] == 'if':
                    skip_until = self._handle_control_block(stmt)
                elif stmt['type'] == 'while':
                    skip_until = self._handle_while_loop(stmt)
                elif stmt['type'] == 'for':
                    skip_until = self._handle_for_loop(stmt)
                elif self._is_statement_in_function(stmt):
                    continue  # Skip statements that are inside functions
                else:
                    self._generate_statement(stmt)

        # Close wrapper or main function
        if has_main_function:
            # Call the user's main function and close wrapper
            self.llvm_lines.extend([
                "  ; Call user's dolet_main function",
                "  call void @dolet_main()",
                "  ret i32 0",
                "}",
                ""
            ])
            # Add real main function that calls wrapper
            self.llvm_lines.extend([
                "; Real main function",
                "define i32 @main(i32 %argc, i8** %argv) {",
                "entry:",
                "  %result = call i32 @main_wrapper(i32 %argc, i8** %argv)",
                "  ret i32 %result",
                "}",
                ""
            ])
        else:
            # Only close main function if we added it
            self.llvm_lines.extend([
                "  ret i32 0",
                "}",
                ""
            ])
        
    def _handle_control_block(self, if_stmt):
        """Handle if/else control blocks"""
        # Handle both statement objects and control block objects
        if 'line_number' in if_stmt:
            # This is a statement, find the control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == if_stmt['line_number']:
                    control_block = block
                    break
                    
            if not control_block:
                return if_stmt['line_number']
        else:
            # This is already a control block
            control_block = if_stmt
            
        # Generate labels
        if_label = self.get_label("if_true")
        end_label = self.get_label("if_end")

        # Generate elif labels
        elif_labels = []
        for i, elif_block in enumerate(control_block.get('elif_blocks', [])):
            elif_labels.append(self.get_label(f"elif_{i}"))

        # Generate else label (or first elif if no else)
        else_label = None
        if control_block.get('else_statements'):
            else_label = self.get_label("if_else")

        if control_block.get('elif_blocks'):
            next_label = elif_labels[0]
        elif else_label:
            next_label = else_label
        else:
            next_label = end_label

        # Parse and generate main if condition
        self.llvm_lines.append(f"  ; if {control_block['condition']}")
        condition_result = self._generate_condition(control_block['condition'])

        self.llvm_lines.append(f"  br i1 {condition_result}, label %{if_label}, label %{next_label}")

        # If block
        self.llvm_lines.append(f"{if_label}:")
        if_statements = control_block.get('if_statements', [])
        print(f"   🔧 Executing {len(if_statements)} statements in if block")

        # Set flag to indicate we're inside control block execution
        self._inside_control_block = True

        for stmt in if_statements:
            print(f"   🔧 Executing if statement: {stmt.get('type', 'unknown')} - {stmt.get('content', '')[:50]}")
            if isinstance(stmt, dict) and 'condition' in stmt:
                # This is a nested control block
                self._handle_control_block(stmt)
            else:
                # Check if we're inside a function
                if hasattr(self, 'current_function') and self.current_function:
                    print(f"   🔧 Calling _generate_function_body_statement for: {stmt.get('type', 'unknown')}")
                    self._generate_function_body_statement(stmt)
                else:
                    print(f"   🔧 Calling _generate_statement for: {stmt.get('type', 'unknown')}")
                    self._generate_statement(stmt)

        # Reset flag
        self._inside_control_block = False
        self.llvm_lines.append(f"  br label %{end_label}")

        # Generate elif blocks
        for i, elif_block in enumerate(control_block.get('elif_blocks', [])):
            self.llvm_lines.append(f"{elif_labels[i]}:")
            self.llvm_lines.append(f"  ; elif {elif_block['condition']}")

            # Generate elif condition
            elif_condition_result = self._generate_condition(elif_block['condition'])

            # Determine next label
            if i + 1 < len(elif_labels):
                next_elif_label = elif_labels[i + 1]
            elif else_label:
                next_elif_label = else_label
            else:
                next_elif_label = end_label

            elif_true_label = self.get_label(f"elif_{i}_true")
            self.llvm_lines.append(f"  br i1 {elif_condition_result}, label %{elif_true_label}, label %{next_elif_label}")

            # Elif block statements
            self.llvm_lines.append(f"{elif_true_label}:")
            elif_statements = elif_block.get('statements', [])
            for stmt in elif_statements:
                if isinstance(stmt, dict) and 'condition' in stmt:
                    # This is a nested control block
                    self._handle_control_block(stmt)
                else:
                    self._generate_statement(stmt)
            self.llvm_lines.append(f"  br label %{end_label}")

        # Else block (if exists)
        else_statements = control_block.get('else_statements', [])
        if else_statements and else_label:
            self.llvm_lines.append(f"{else_label}:")
            for stmt in else_statements:
                if isinstance(stmt, dict) and 'condition' in stmt:
                    # This is a nested control block
                    self._handle_control_block(stmt)
                else:
                    self._generate_statement(stmt)
            self.llvm_lines.append(f"  br label %{end_label}")

        # End label
        self.llvm_lines.append(f"{end_label}:")
        
        return control_block['end_line']

    def _handle_while_loop(self, while_stmt):
        """Handle while loop blocks"""
        # Find the control block
        if 'line_number' in while_stmt:
            # This is a statement, find the control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == while_stmt['line_number']:
                    control_block = block
                    break

            if not control_block:
                return while_stmt['line_number']
        else:
            # This is already a control block
            control_block = while_stmt

        # Generate labels
        loop_label = self.get_label("while_loop")
        body_label = self.get_label("while_body")
        end_label = self.get_label("while_end")

        # Push loop context for break/continue
        loop_context = {
            'type': 'while',
            'continue_label': loop_label,
            'break_label': end_label
        }
        self.loop_stack.append(loop_context)

        # Jump to loop condition
        self.llvm_lines.append(f"  br label %{loop_label}")

        # Loop condition check
        self.llvm_lines.append(f"{loop_label}:")
        self.llvm_lines.append(f"  ; while {control_block['condition']}")
        condition_result = self._generate_condition(control_block['condition'])

        self.llvm_lines.append(f"  br i1 {condition_result}, label %{body_label}, label %{end_label}")

        # Loop body
        self.llvm_lines.append(f"{body_label}:")
        body_statements = control_block.get('body_statements', [])
        for stmt in body_statements:
            if isinstance(stmt, dict) and 'condition' in stmt:
                # This is a nested control block
                if stmt['type'] == 'if':
                    self._handle_control_block(stmt)
                elif stmt['type'] == 'while':
                    self._handle_while_loop(stmt)
            else:
                self._generate_statement(stmt)

        # Jump back to condition
        self.llvm_lines.append(f"  br label %{loop_label}")

        # End label
        self.llvm_lines.append(f"{end_label}:")

        # Pop loop context
        self.loop_stack.pop()

        return control_block['end_line']

    def _handle_for_loop(self, for_stmt):
        """Handle for loop blocks"""
        # Find the control block
        if 'line_number' in for_stmt:
            # This is a statement, find the control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == for_stmt['line_number']:
                    control_block = block
                    break

            if not control_block:
                return for_stmt['line_number']
        else:
            # This is already a control block
            control_block = for_stmt

        # Extract for loop parameters
        var_name = control_block['variable']
        start_val = control_block['start']
        end_val = control_block['end']
        step_val = control_block['step']

        print(f"   🔄 Generating for loop: {var_name} = {start_val} to {end_val} step {step_val}")

        # Generate labels
        init_label = self.get_label("for_init")
        loop_label = self.get_label("for_loop")
        body_label = self.get_label("for_body")
        increment_label = self.get_label("for_increment")
        end_label = self.get_label("for_end")

        # Push loop context for break/continue
        loop_context = {
            'type': 'for',
            'continue_label': increment_label,
            'break_label': end_label
        }
        self.loop_stack.append(loop_context)

        # Initialize loop variable
        self.llvm_lines.append(f"  br label %{init_label}")
        self.llvm_lines.append(f"{init_label}:")

        # Get start value
        start_llvm = self._get_condition_value(start_val)

        # Store initial value in loop variable (create temporary variable for loop)
        loop_var = f"for_{var_name}_{self.temp_counter}"
        self.temp_counter += 1
        self.llvm_lines.append(f"  %{loop_var} = alloca i32, align 4")
        self.llvm_lines.append(f"  store i32 {start_llvm}, i32* %{loop_var}, align 4")

        # Jump to loop condition
        self.llvm_lines.append(f"  br label %{loop_label}")

        # Loop condition check
        self.llvm_lines.append(f"{loop_label}:")
        current_val = self.get_temp_var()
        self.llvm_lines.append(f"  {current_val} = load i32, i32* %{loop_var}, align 4")

        # Get end value
        end_llvm = self._get_condition_value(end_val)

        # Compare current with end
        condition_result = self.get_temp_var()
        self.llvm_lines.append(f"  {condition_result} = icmp sle i32 {current_val}, {end_llvm}")
        self.llvm_lines.append(f"  br i1 {condition_result}, label %{body_label}, label %{end_label}")

        # Loop body
        self.llvm_lines.append(f"{body_label}:")

        # Store current loop value in the actual variable
        self.llvm_lines.append(f"  store i32 {current_val}, i32* @global_{var_name}, align 4")

        # Execute body statements
        for stmt in control_block['body_statements']:
            if isinstance(stmt, dict) and 'condition' in stmt:
                # This is a nested control block
                if stmt['type'] == 'if':
                    self._handle_control_block(stmt)
                elif stmt['type'] == 'while':
                    self._handle_while_loop(stmt)
                elif stmt['type'] == 'for':
                    self._handle_for_loop(stmt)
            else:
                self._generate_statement(stmt)

        # Jump to increment
        self.llvm_lines.append(f"  br label %{increment_label}")

        # Increment
        self.llvm_lines.append(f"{increment_label}:")
        current_val2 = self.get_temp_var()
        step_llvm = self._get_condition_value(step_val)
        new_val = self.get_temp_var()

        self.llvm_lines.append(f"  {current_val2} = load i32, i32* %{loop_var}, align 4")
        self.llvm_lines.append(f"  {new_val} = add i32 {current_val2}, {step_llvm}")
        self.llvm_lines.append(f"  store i32 {new_val}, i32* %{loop_var}, align 4")

        # Jump back to condition
        self.llvm_lines.append(f"  br label %{loop_label}")

        # End label
        self.llvm_lines.append(f"{end_label}:")

        # Pop loop context
        self.loop_stack.pop()

        return control_block['end_line']

    def _generate_contains_condition(self, condition):
        """Generate LLVM code for contains() function in conditions"""
        # Parse: contains(string_var, substring)
        if condition.startswith('contains(') and condition.endswith(')'):
            args_str = condition[9:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 2:
                string_arg = args[0]
                substring_arg = args[1]

                self.llvm_lines.append(f"  ; contains({string_arg}, {substring_arg})")

                # Load the string variable
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    str_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {str_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Handle substring argument
                    if substring_arg.startswith('"') and substring_arg.endswith('"'):
                        # String literal substring
                        substring_val = substring_arg[1:-1]

                        # Create inline string constant for contains() (without newline)
                        escaped_substring = self._escape_string(substring_val)
                        substring_len = len(escaped_substring) + 1  # +1 for null terminator

                        # Generate inline string constant
                        inline_const = self.get_temp_var() + "_substr"
                        self.llvm_lines.append(f"  {inline_const} = alloca [{substring_len} x i8], align 1")

                        # Store each character
                        for i, char in enumerate(escaped_substring):
                            char_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{substring_len} x i8], [{substring_len} x i8]* {inline_const}, i64 0, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                        # Null terminator
                        null_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{substring_len} x i8], [{substring_len} x i8]* {inline_const}, i64 0, i64 {len(escaped_substring)}")
                        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                        # Cast to i8*
                        substr_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {substr_ptr} = bitcast [{substring_len} x i8]* {inline_const} to i8*")

                        # Call strstr to find substring
                        strstr_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {strstr_result} = call i8* @strstr(i8* {str_ptr}, i8* {substr_ptr})")

                        # Check if result is null (not found)
                        null_check = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_check} = icmp ne i8* {strstr_result}, null")

                        return null_check
                    else:
                        # Variable substring
                        substring_info = self._find_variable(substring_arg)
                        if substring_info and substring_info['type'] == 'string':
                            substr_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {substr_ptr} = load i8*, i8** @global_{substring_arg}, align 8")

                            # Call strstr
                            strstr_result = self.get_temp_var()
                            self.llvm_lines.append(f"  {strstr_result} = call i8* @strstr(i8* {str_ptr}, i8* {substr_ptr})")

                            # Check if result is null
                            null_check = self.get_temp_var()
                            self.llvm_lines.append(f"  {null_check} = icmp ne i8* {strstr_result}, null")

                            return null_check
                        else:
                            self.llvm_lines.append(f"  ; error: {substring_arg} is not a string variable")
                            return "0"
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
                    return "0"
            else:
                self.llvm_lines.append(f"  ; error: contains() requires 2 arguments")
                return "0"
        else:
            self.llvm_lines.append(f"  ; error: invalid contains() syntax")
            return "0"

    def _generate_break(self, stmt):
        """Generate LLVM code for break statement"""
        if not self.loop_stack:
            self.llvm_lines.append(f"  ; break statement outside of loop - ignored")
            return

        # Get the current loop context
        current_loop = self.loop_stack[-1]
        break_label = current_loop['break_label']

        self.llvm_lines.append(f"  ; break statement")
        self.llvm_lines.append(f"  br label %{break_label}")

    def _generate_continue(self, stmt):
        """Generate LLVM code for continue statement"""
        if not self.loop_stack:
            self.llvm_lines.append(f"  ; continue statement outside of loop - ignored")
            return

        # Get the current loop context
        current_loop = self.loop_stack[-1]
        continue_label = current_loop['continue_label']

        self.llvm_lines.append(f"  ; continue statement")
        self.llvm_lines.append(f"  br label %{continue_label}")

    def _generate_switch(self, stmt):
        """Generate LLVM code for switch statement - convert to if-else chain"""
        content = stmt['content'].strip()

        # Parse: switch variable_name
        if content.startswith('switch '):
            switch_var = content[7:].strip()

            self.llvm_lines.append(f"  ; switch ({switch_var}) - converted to if-else chain")

            # Load the switch variable
            var_info = self._find_variable(switch_var)
            if var_info:
                if var_info['type'] in ['integer', 'int']:
                    switch_val = self.get_temp_var()
                    self.llvm_lines.append(f"  {switch_val} = load i32, i32* @global_{switch_var}, align 4")
                elif var_info['type'] == 'string':
                    switch_val = self.get_temp_var()
                    self.llvm_lines.append(f"  {switch_val} = load i8*, i8** @global_{switch_var}, align 8")
                else:
                    self.llvm_lines.append(f"  ; error: switch variable must be int or string")
                    return

                # Create switch context for automatic if-else conversion
                switch_context = {
                    'variable': switch_var,
                    'value': switch_val,
                    'type': var_info['type'],
                    'end_label': self.get_label("switch_end"),
                    'cases': [],
                    'has_default': False
                }

                # Store switch context for case statements
                if not hasattr(self, 'switch_stack'):
                    self.switch_stack = []
                self.switch_stack.append(switch_context)

                self.llvm_lines.append(f"  ; switch context created for {switch_var}")
            else:
                self.llvm_lines.append(f"  ; error: switch variable '{switch_var}' not found")

    def _generate_case(self, stmt):
        """Generate LLVM code for case statement - simplified as if condition"""
        content = stmt['content'].strip()

        # Parse: case value:
        if content.startswith('case ') and content.endswith(':'):
            case_value = content[5:-1].strip()

            if not hasattr(self, 'switch_stack') or not self.switch_stack:
                self.llvm_lines.append(f"  ; error: case statement outside of switch")
                return

            switch_context = self.switch_stack[-1]
            case_label = self.get_label("case")
            next_label = self.get_label("next_case")

            self.llvm_lines.append(f"  ; case {case_value}: - simple implementation")

            # Generate simple if condition for this case
            if switch_context['type'] in ['integer', 'int']:
                # Integer comparison
                try:
                    case_int = int(case_value)
                    cmp_result = self.get_temp_var()
                    self.llvm_lines.append(f"  {cmp_result} = icmp eq i32 {switch_context['value']}, {case_int}")

                    # Generate simple conditional branch
                    case_body_label = self.get_label("case_body")
                    case_end_label = self.get_label("case_end")

                    self.llvm_lines.append(f"  br i1 {cmp_result}, label %{case_body_label}, label %{case_end_label}")

                    # Case body label
                    self.llvm_lines.append(f"{case_body_label}:")

                    # Store labels for case end
                    switch_context['case_end_label'] = case_end_label
                    switch_context['needs_case_end_jump'] = True

                except ValueError:
                    self.llvm_lines.append(f"  ; error: case value '{case_value}' is not a valid integer")

            elif switch_context['type'] == 'string':
                # String comparison using strcmp
                if case_value.startswith('"') and case_value.endswith('"'):
                    case_str = case_value[1:-1]  # Remove quotes

                    # Create string constant for case
                    case_len = len(case_str)
                    case_const = self.get_temp_var() + "_case"
                    self.llvm_lines.append(f"  {case_const} = alloca [{case_len + 1} x i8], align 1")
                    for i, char in enumerate(case_str):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{case_len + 1} x i8], [{case_len + 1} x i8]* {case_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                    # Null terminator
                    case_null = self.get_temp_var()
                    self.llvm_lines.append(f"  {case_null} = getelementptr inbounds [{case_len + 1} x i8], [{case_len + 1} x i8]* {case_const}, i64 0, i64 {case_len}")
                    self.llvm_lines.append(f"  store i8 0, i8* {case_null}, align 1")
                    case_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {case_ptr} = bitcast [{case_len + 1} x i8]* {case_const} to i8*")

                    # Compare strings using strcmp
                    strcmp_result = self.get_temp_var()
                    cmp_result = self.get_temp_var()
                    self.llvm_lines.append(f"  {strcmp_result} = call i32 @strcmp(i8* {switch_context['value']}, i8* {case_ptr})")
                    self.llvm_lines.append(f"  {cmp_result} = icmp eq i32 {strcmp_result}, 0")

                    # Generate simple conditional branch
                    case_body_label = self.get_label("case_body")
                    case_end_label = self.get_label("case_end")

                    self.llvm_lines.append(f"  br i1 {cmp_result}, label %{case_body_label}, label %{case_end_label}")

                    # Case body label
                    self.llvm_lines.append(f"{case_body_label}:")

                    # Store labels for case end
                    switch_context['case_end_label'] = case_end_label
                    switch_context['needs_case_end_jump'] = True

                else:
                    self.llvm_lines.append(f"  ; error: string case value must be quoted")

    def _generate_default(self, stmt):
        """Generate LLVM code for default case - simplified"""
        if not hasattr(self, 'switch_stack') or not self.switch_stack:
            self.llvm_lines.append(f"  ; error: default statement outside of switch")
            return

        switch_context = self.switch_stack[-1]

        # If there's a next_label from previous case, generate it
        if 'next_label' in switch_context:
            self.llvm_lines.append(f"{switch_context['next_label']}:")

        self.llvm_lines.append(f"  ; default case:")

        # Mark that we have a default case
        switch_context['has_default'] = True

    def _finalize_switch(self, switch_context):
        """Finalize switch statement - simple implementation"""
        self.llvm_lines.append(f"  ; finalizing switch statement - simple implementation")

        # If we need to add jump to switch end after case body
        if switch_context.get('needs_case_end_jump', False):
            self.llvm_lines.append(f"  br label %{switch_context['end_label']}")

        # If there's a pending case_end_label, generate it
        if 'case_end_label' in switch_context:
            self.llvm_lines.append(f"{switch_context['case_end_label']}:")
            # Jump to switch end from case_end
            self.llvm_lines.append(f"  br label %{switch_context['end_label']}")

        # Generate end label
        self.llvm_lines.append(f"{switch_context['end_label']}:")
        self.llvm_lines.append(f"  ; end of switch statement")

    def _generate_try(self, stmt):
        """Generate LLVM code for try block - convert to error flag system"""
        self.llvm_lines.append(f"  ; try block start - using error flag system")

        # Create error flag variable for this try block
        error_flag_name = f"__try_error_{len(getattr(self, 'try_stack', []))}"
        exception_msg_name = f"__try_exception_{len(getattr(self, 'try_stack', []))}"

        # Add error flag and exception message variables
        if error_flag_name not in [var['name'] for var in self.variables]:
            self.variables.append({
                'name': error_flag_name,
                'type': 'bool',
                'value': 'false'
            })
            # Global declarations will be added later in generate_llvm_ir

        if exception_msg_name not in [var['name'] for var in self.variables]:
            self.variables.append({
                'name': exception_msg_name,
                'type': 'string',
                'value': '""'
            })
            # Global declarations will be added later in generate_llvm_ir

        # Create simplified try context
        try_context = {
            'error_flag': error_flag_name,
            'exception_msg': exception_msg_name,
            'catch_label': self.get_label("catch_block"),
            'finally_label': self.get_label("finally_block"),
            'end_label': self.get_label("try_end"),
            'has_catch': False,
            'has_finally': False,
            'exception_var': None,
            'in_try': True
        }

        # Store try context for catch/finally statements
        if not hasattr(self, 'try_stack'):
            self.try_stack = []
        self.try_stack.append(try_context)

        # Initialize error flag to false
        self.llvm_lines.append(f"  store i1 false, i1* @global_{error_flag_name}, align 1")
        self.llvm_lines.append(f"  ; try block body")

    def _generate_catch(self, stmt):
        """Generate LLVM code for catch block - using error flag system"""
        content = stmt['content'].strip()

        if not hasattr(self, 'try_stack') or not self.try_stack:
            self.llvm_lines.append(f"  ; error: catch statement outside of try block")
            return

        try_context = self.try_stack[-1]
        try_context['has_catch'] = True

        # Parse: catch exception_type exception_var
        if content.startswith('catch '):
            catch_params = content[6:].strip()

            if ' ' in catch_params:
                # catch ExceptionType varName
                parts = catch_params.split(' ', 1)
                exception_type = parts[0]
                exception_var = parts[1]
            else:
                # catch ExceptionType
                exception_type = catch_params
                exception_var = "e"

            try_context['exception_var'] = exception_var

            self.llvm_lines.append(f"  ; catch {exception_type} {exception_var} - using error flag system")

            # Create exception variable if it doesn't exist
            if exception_var not in [var['name'] for var in self.variables]:
                self.variables.append({
                    'name': exception_var,
                    'type': 'string',
                    'value': '""'
                })
                # Global declaration will be added later in generate_llvm_ir

            # Check error flag and execute catch block if error occurred
            error_check = self.get_temp_var()
            self.llvm_lines.append(f"  {error_check} = load i1, i1* @global_{try_context['error_flag']}, align 1")

            # Generate conditional branch for catch
            catch_body_label = self.get_label("catch_body")
            catch_skip_label = self.get_label("catch_skip")

            self.llvm_lines.append(f"  br i1 {error_check}, label %{catch_body_label}, label %{catch_skip_label}")

            # Catch body label
            self.llvm_lines.append(f"{catch_body_label}:")
            self.llvm_lines.append(f"  ; catch block body for {exception_type}")

            # Load exception message into exception variable
            exception_msg_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {exception_msg_ptr} = load i8*, i8** @global_{try_context['exception_msg']}, align 8")
            self.llvm_lines.append(f"  store i8* {exception_msg_ptr}, i8** @global_{exception_var}, align 8")

            # Store labels for later use
            try_context['catch_body_label'] = catch_body_label
            try_context['catch_skip_label'] = catch_skip_label

        else:
            self.llvm_lines.append(f"  ; error: invalid catch syntax")

    def _generate_throw(self, stmt):
        """Generate LLVM code for throw statement - using error flag system"""
        content = stmt['content'].strip()

        # Parse: throw exception_message
        if content.startswith('throw '):
            exception_msg = content[6:].strip()

            self.llvm_lines.append(f"  ; throw {exception_msg} - using error flag system")

            # Set error flag and exception message
            if hasattr(self, 'try_stack') and self.try_stack:
                try_context = self.try_stack[-1]

                # Set error flag to true
                self.llvm_lines.append(f"  store i1 true, i1* @global_{try_context['error_flag']}, align 1")

                # Set exception message
                if exception_msg.startswith('"') and exception_msg.endswith('"'):
                    msg_str = exception_msg[1:-1]  # Remove quotes
                    msg_len = len(msg_str)

                    # Create exception message string
                    msg_const = self.get_temp_var() + "_exception"
                    self.llvm_lines.append(f"  {msg_const} = alloca [{msg_len + 1} x i8], align 1")
                    for i, char in enumerate(msg_str):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{msg_len + 1} x i8], [{msg_len + 1} x i8]* {msg_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                    # Null terminator
                    msg_null = self.get_temp_var()
                    self.llvm_lines.append(f"  {msg_null} = getelementptr inbounds [{msg_len + 1} x i8], [{msg_len + 1} x i8]* {msg_const}, i64 0, i64 {msg_len}")
                    self.llvm_lines.append(f"  store i8 0, i8* {msg_null}, align 1")
                    msg_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {msg_ptr} = bitcast [{msg_len + 1} x i8]* {msg_const} to i8*")

                    # Store exception message in exception message variable
                    self.llvm_lines.append(f"  store i8* {msg_ptr}, i8** @global_{try_context['exception_msg']}, align 8")

                # Continue execution (don't jump - let catch block check error flag)
                self.llvm_lines.append(f"  ; exception thrown - error flag set")
            else:
                self.llvm_lines.append(f"  ; error: throw statement outside of try block")
        else:
            self.llvm_lines.append(f"  ; error: invalid throw syntax")

    def _generate_finally(self, stmt):
        """Generate LLVM code for finally block - simplified"""
        if not hasattr(self, 'try_stack') or not self.try_stack:
            self.llvm_lines.append(f"  ; error: finally statement outside of try block")
            return

        try_context = self.try_stack[-1]
        try_context['has_finally'] = True

        self.llvm_lines.append(f"  ; finally block - always executes")

        # Mark that we have a finally block
        try_context['has_finally'] = True

        # Don't generate label here - it will be generated in _finalize_try
        self.llvm_lines.append(f"  ; finally block body")

    def _finalize_try(self, try_context):
        """Finalize try-catch-finally block - using error flag system"""
        self.llvm_lines.append(f"  ; finalizing try-catch-finally block - error flag system")

        # If there's a catch block, handle the skip label
        if try_context['has_catch'] and 'catch_skip_label' in try_context:
            # Jump from catch body to end (finally is handled inline)
            self.llvm_lines.append(f"  br label %{try_context['end_label']}")

            # Generate catch skip label (for when no exception occurred)
            self.llvm_lines.append(f"{try_context['catch_skip_label']}:")
            self.llvm_lines.append(f"  ; no exception - skip catch block")

        # Jump to end (finally is handled inline, not as separate block)
        self.llvm_lines.append(f"  br label %{try_context['end_label']}")

        # Generate end label
        self.llvm_lines.append(f"{try_context['end_label']}:")
        self.llvm_lines.append(f"  ; end of try-catch-finally block")

        # Reset error flag for next try block
        self.llvm_lines.append(f"  store i1 false, i1* @global_{try_context['error_flag']}, align 1")

    def _generate_string_function(self, stmt):
        """Generate LLVM code for string functions"""
        content = stmt['content'].strip()

        if stmt['type'] == 'string_length':
            self._generate_string_length(content)
        elif stmt['type'] == 'string_substring':
            self._generate_string_substring(content)
        elif stmt['type'] == 'string_split':
            self._generate_string_split(content)
        elif stmt['type'] == 'string_replace':
            self._generate_string_replace(content)
        elif stmt['type'] == 'string_contains':
            self._generate_string_contains(content)
        elif stmt['type'] == 'string_uppercase':
            self._generate_string_uppercase(content)
        elif stmt['type'] == 'string_lowercase':
            self._generate_string_lowercase(content)
        elif stmt['type'] == 'string_trim':
            self._generate_string_trim(content)

    def _generate_string_length(self, content):
        """Generate LLVM code for length() function"""
        # Parse: length(string_var) or set result = length(string_var)
        if ' = ' in content:
            # Assignment: set result = length(string_var)
            var_name = content.split(' = ')[0].strip().replace('set ', '')
            func_call = content.split(' = ')[1].strip()
        else:
            # Direct call: length(string_var)
            func_call = content
            var_name = None

        # Extract argument from length(arg)
        if func_call.startswith('length(') and func_call.endswith(')'):
            arg = func_call[7:-1].strip()

            self.llvm_lines.append(f"  ; length({arg})")

            # Check if argument is a string variable
            if arg.startswith('"') and arg.endswith('"'):
                # String literal
                string_val = arg[1:-1]
                length = len(string_val)
                if var_name:
                    self.llvm_lines.append(f"  store i32 {length}, i32* @global_{var_name}, align 4")
                else:
                    self.llvm_lines.append(f"  ; length result: {length}")
            else:
                # String variable
                var_info = self._find_variable(arg)
                if var_info and var_info['type'] == 'string':
                    # Load string pointer
                    str_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {str_ptr} = load i8*, i8** @global_{arg}, align 8")

                    # Call strlen
                    len_result = self.get_temp_var()
                    self.llvm_lines.append(f"  {len_result} = call i64 @strlen(i8* {str_ptr})")

                    # Convert to i32
                    len_i32 = self.get_temp_var()
                    self.llvm_lines.append(f"  {len_i32} = trunc i64 {len_result} to i32")

                    if var_name:
                        self.llvm_lines.append(f"  store i32 {len_i32}, i32* @global_{var_name}, align 4")
                    else:
                        self.llvm_lines.append(f"  ; length result: {len_i32}")
                else:
                    self.llvm_lines.append(f"  ; error: {arg} is not a string variable")

    def _generate_string_substring(self, content):
        """Generate LLVM code for substring() function"""
        # Parse: substring(string_var, start, end) or set result = substring(string_var, start, end)
        if ' = ' in content:
            var_name = content.split(' = ')[0].strip().replace('set ', '')
            func_call = content.split(' = ')[1].strip()
        else:
            func_call = content
            var_name = None

        # Extract arguments from substring(str, start, end)
        if func_call.startswith('substring(') and func_call.endswith(')'):
            args_str = func_call[10:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 3:
                string_arg = args[0]
                start_arg = args[1]
                end_arg = args[2]

                self.llvm_lines.append(f"  ; substring({string_arg}, {start_arg}, {end_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Get start and end indices
                    start_val = int(start_arg) if start_arg.isdigit() else 0
                    end_val = int(end_arg) if end_arg.isdigit() else 0

                    if end_val > start_val:
                        # Calculate substring length
                        substr_len = end_val - start_val

                        # Allocate memory for substring (+1 for null terminator)
                        substr_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {substr_ptr} = call i8* @malloc(i64 {substr_len + 1})")

                        # Copy characters from source to substring
                        for i in range(substr_len):
                            src_char_ptr = self.get_temp_var()
                            dst_char_ptr = self.get_temp_var()
                            char_val = self.get_temp_var()

                            # Get character from source at (start + i)
                            self.llvm_lines.append(f"  {src_char_ptr} = getelementptr inbounds i8, i8* {src_ptr}, i64 {start_val + i}")
                            self.llvm_lines.append(f"  {char_val} = load i8, i8* {src_char_ptr}, align 1")

                            # Store character in destination at i
                            self.llvm_lines.append(f"  {dst_char_ptr} = getelementptr inbounds i8, i8* {substr_ptr}, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {char_val}, i8* {dst_char_ptr}, align 1")

                        # Add null terminator
                        null_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds i8, i8* {substr_ptr}, i64 {substr_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                        # Store result in variable if provided
                        if var_name:
                            self.llvm_lines.append(f"  store i8* {substr_ptr}, i8** @global_{var_name}, align 8")
                        else:
                            self.llvm_lines.append(f"  ; substring result: {substr_ptr}")
                    else:
                        # Invalid range - return empty string
                        if var_name:
                            empty_str = self.get_temp_var()
                            self.llvm_lines.append(f"  {empty_str} = call i8* @malloc(i64 1)")
                            self.llvm_lines.append(f"  store i8 0, i8* {empty_str}, align 1")
                            self.llvm_lines.append(f"  store i8* {empty_str}, i8** @global_{var_name}, align 8")
                        else:
                            self.llvm_lines.append(f"  ; substring result: empty string")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: substring() requires 3 arguments")

    def _generate_string_split(self, content):
        """Generate LLVM code for split() function"""
        # Parse: split(string_var, delimiter) or set result = split(string_var, delimiter)
        if ' = ' in content:
            var_name = content.split(' = ')[0].strip().replace('set ', '')
            func_call = content.split(' = ')[1].strip()
        else:
            func_call = content
            var_name = None

        # Extract arguments from split(str, delimiter)
        if func_call.startswith('split(') and func_call.endswith(')'):
            args_str = func_call[6:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 2:
                string_arg = args[0]
                delimiter_arg = args[1]

                self.llvm_lines.append(f"  ; split({string_arg}, {delimiter_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Handle delimiter argument (string literal for now)
                    if delimiter_arg.startswith('"') and delimiter_arg.endswith('"'):
                        delimiter_str = delimiter_arg[1:-1]  # Remove quotes
                        delimiter_len = len(delimiter_str)

                        # For simplicity, implement basic split that returns first part before delimiter
                        # In a full implementation, this would return an array of strings

                        # Create delimiter string constant
                        delim_const = self.get_temp_var() + "_delim"
                        self.llvm_lines.append(f"  {delim_const} = alloca [{delimiter_len + 1} x i8], align 1")
                        for i, char in enumerate(delimiter_str):
                            char_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{delimiter_len + 1} x i8], [{delimiter_len + 1} x i8]* {delim_const}, i64 0, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                        # Null terminator for delimiter
                        delim_null = self.get_temp_var()
                        self.llvm_lines.append(f"  {delim_null} = getelementptr inbounds [{delimiter_len + 1} x i8], [{delimiter_len + 1} x i8]* {delim_const}, i64 0, i64 {delimiter_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {delim_null}, align 1")
                        delim_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {delim_ptr} = bitcast [{delimiter_len + 1} x i8]* {delim_const} to i8*")

                        # Find the delimiter in source using strstr
                        found_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_ptr} = call i8* @strstr(i8* {src_ptr}, i8* {delim_ptr})")

                        # Check if found
                        found_check = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_check} = icmp ne i8* {found_ptr}, null")

                        # Create labels for conditional split
                        split_label = self.get_label("split")
                        no_split_label = self.get_label("no_split")
                        end_label = self.get_label("end_split")

                        self.llvm_lines.append(f"  br i1 {found_check}, label %{split_label}, label %{no_split_label}")

                        # Split block - return first part before delimiter
                        self.llvm_lines.append(f"{split_label}:")

                        # Calculate length of first part
                        first_len_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_len_ptr} = ptrtoint i8* {found_ptr} to i64")
                        src_int = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_int} = ptrtoint i8* {src_ptr} to i64")
                        first_len = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_len} = sub i64 {first_len_ptr}, {src_int}")

                        # Allocate memory for first part (+1 for null terminator)
                        first_size = self.get_temp_var()
                        first_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_size} = add i64 {first_len}, 1")
                        self.llvm_lines.append(f"  {first_ptr} = call i8* @malloc(i64 {first_size})")

                        # Copy first part
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {first_ptr}, i8* {src_ptr}, i64 {first_len}, i1 false)")

                        # Add null terminator
                        null_pos = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_pos} = getelementptr inbounds i8, i8* {first_ptr}, i64 {first_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {null_pos}, align 1")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # No split block (delimiter not found)
                        self.llvm_lines.append(f"{no_split_label}:")

                        # Return copy of original string
                        src_len = self.get_temp_var()
                        orig_len_plus1 = self.get_temp_var()
                        orig_copy = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_len} = call i64 @strlen(i8* {src_ptr})")
                        self.llvm_lines.append(f"  {orig_len_plus1} = add i64 {src_len}, 1")
                        self.llvm_lines.append(f"  {orig_copy} = call i8* @malloc(i64 {orig_len_plus1})")
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {orig_copy}, i8* {src_ptr}, i64 {orig_len_plus1}, i1 false)")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # End block
                        self.llvm_lines.append(f"{end_label}:")

                        # PHI node to select result
                        final_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {final_result} = phi i8* [ {first_ptr}, %{split_label} ], [ {orig_copy}, %{no_split_label} ]")

                        # Store result in variable if provided
                        if var_name:
                            self.llvm_lines.append(f"  store i8* {final_result}, i8** @global_{var_name}, align 8")
                        else:
                            self.llvm_lines.append(f"  ; split result: {final_result}")
                    else:
                        self.llvm_lines.append(f"  ; error: split() currently supports only string literals for delimiter")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: split() requires 2 arguments")

    def _generate_string_replace(self, content):
        """Generate LLVM code for replace() function"""
        # Parse: replace(string_var, old, new) or set result = replace(string_var, old, new)
        if ' = ' in content:
            var_name = content.split(' = ')[0].strip().replace('set ', '')
            func_call = content.split(' = ')[1].strip()
        else:
            func_call = content
            var_name = None

        # Extract arguments from replace(str, old, new)
        if func_call.startswith('replace(') and func_call.endswith(')'):
            args_str = func_call[8:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 3:
                string_arg = args[0]
                old_arg = args[1]
                new_arg = args[2]

                self.llvm_lines.append(f"  ; replace({string_arg}, {old_arg}, {new_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Handle old and new arguments (string literals for now)
                    if (old_arg.startswith('"') and old_arg.endswith('"') and
                        new_arg.startswith('"') and new_arg.endswith('"')):

                        old_str = old_arg[1:-1]  # Remove quotes
                        new_str = new_arg[1:-1]  # Remove quotes

                        # Simple replace implementation: find first occurrence and replace
                        # Get source string length
                        src_len = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_len} = call i64 @strlen(i8* {src_ptr})")

                        # Create inline string constants for old and new
                        old_len = len(old_str)
                        new_len = len(new_str)

                        # Create old string constant
                        old_const = self.get_temp_var() + "_old"
                        self.llvm_lines.append(f"  {old_const} = alloca [{old_len + 1} x i8], align 1")
                        for i, char in enumerate(old_str):
                            char_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{old_len + 1} x i8], [{old_len + 1} x i8]* {old_const}, i64 0, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                        # Null terminator for old
                        old_null = self.get_temp_var()
                        self.llvm_lines.append(f"  {old_null} = getelementptr inbounds [{old_len + 1} x i8], [{old_len + 1} x i8]* {old_const}, i64 0, i64 {old_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {old_null}, align 1")
                        old_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {old_ptr} = bitcast [{old_len + 1} x i8]* {old_const} to i8*")

                        # Find the old string in source using strstr
                        found_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_ptr} = call i8* @strstr(i8* {src_ptr}, i8* {old_ptr})")

                        # Check if found
                        found_check = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_check} = icmp ne i8* {found_ptr}, null")

                        # Create labels for conditional replacement
                        replace_label = f"replace_{self.get_label_counter()}"
                        no_replace_label = f"no_replace_{self.get_label_counter()}"
                        end_label = f"end_replace_{self.get_label_counter()}"

                        self.llvm_lines.append(f"  br i1 {found_check}, label %{replace_label}, label %{no_replace_label}")

                        # Replace block
                        self.llvm_lines.append(f"{replace_label}:")

                        # Calculate new string length
                        new_total_len = self.get_temp_var()
                        len_diff = new_len - old_len
                        if len_diff >= 0:
                            self.llvm_lines.append(f"  {new_total_len} = add i64 {src_len}, {len_diff}")
                        else:
                            self.llvm_lines.append(f"  {new_total_len} = sub i64 {src_len}, {abs(len_diff)}")

                        # Allocate memory for result (+1 for null terminator)
                        result_size = self.get_temp_var()
                        result_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {result_size} = add i64 {new_total_len}, 1")
                        self.llvm_lines.append(f"  {result_ptr} = call i8* @malloc(i64 {result_size})")

                        # Copy part before the match
                        before_len = self.get_temp_var()
                        self.llvm_lines.append(f"  {before_len} = ptrtoint i8* {found_ptr} to i64")
                        src_int = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_int} = ptrtoint i8* {src_ptr} to i64")
                        self.llvm_lines.append(f"  {before_len} = sub i64 {before_len}, {src_int}")

                        # Copy before part (simplified - copy character by character)
                        # For simplicity, we'll use a basic implementation
                        self.llvm_lines.append(f"  ; Copy part before match (length: {before_len})")

                        # Create new string constant for replacement
                        if new_len > 0:
                            new_const = self.get_temp_var() + "_new"
                            self.llvm_lines.append(f"  {new_const} = alloca [{new_len + 1} x i8], align 1")
                            for i, char in enumerate(new_str):
                                char_ptr = self.get_temp_var()
                                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{new_len + 1} x i8], [{new_len + 1} x i8]* {new_const}, i64 0, i64 {i}")
                                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                            # Null terminator for new
                            new_null = self.get_temp_var()
                            self.llvm_lines.append(f"  {new_null} = getelementptr inbounds [{new_len + 1} x i8], [{new_len + 1} x i8]* {new_const}, i64 0, i64 {new_len}")
                            self.llvm_lines.append(f"  store i8 0, i8* {new_null}, align 1")
                            new_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {new_ptr} = bitcast [{new_len + 1} x i8]* {new_const} to i8*")

                        # For simplicity, create a basic replacement result
                        # This is a simplified implementation - in a full implementation,
                        # we would properly copy the before part, new string, and after part
                        self.llvm_lines.append(f"  ; Simplified replace: store new string")
                        if new_len > 0:
                            self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {result_ptr}, i8* {new_ptr}, i64 {new_len}, i1 false)")
                            null_pos = self.get_temp_var()
                            self.llvm_lines.append(f"  {null_pos} = getelementptr inbounds i8, i8* {result_ptr}, i64 {new_len}")
                            self.llvm_lines.append(f"  store i8 0, i8* {null_pos}, align 1")
                        else:
                            # Empty replacement
                            self.llvm_lines.append(f"  store i8 0, i8* {result_ptr}, align 1")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # No replace block (string not found)
                        self.llvm_lines.append(f"{no_replace_label}:")

                        # Return copy of original string
                        orig_len_plus1 = self.get_temp_var()
                        orig_copy = self.get_temp_var()
                        self.llvm_lines.append(f"  {orig_len_plus1} = add i64 {src_len}, 1")
                        self.llvm_lines.append(f"  {orig_copy} = call i8* @malloc(i64 {orig_len_plus1})")
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {orig_copy}, i8* {src_ptr}, i64 {orig_len_plus1}, i1 false)")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # End block
                        self.llvm_lines.append(f"{end_label}:")

                        # PHI node to select result
                        final_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {final_result} = phi i8* [ {result_ptr}, %{replace_label} ], [ {orig_copy}, %{no_replace_label} ]")

                        # Store result in variable if provided
                        if var_name:
                            self.llvm_lines.append(f"  store i8* {final_result}, i8** @global_{var_name}, align 8")
                        else:
                            self.llvm_lines.append(f"  ; replace result: {final_result}")
                    else:
                        self.llvm_lines.append(f"  ; error: replace() currently supports only string literals for old and new")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: replace() requires 3 arguments")

    def _generate_string_contains(self, content):
        """Generate LLVM code for contains() function"""
        # Parse: contains(string_var, substring) or set result = contains(string_var, substring)
        if ' = ' in content:
            var_name = content.split(' = ')[0].strip().replace('set ', '')
            func_call = content.split(' = ')[1].strip()
        else:
            func_call = content
            var_name = None

        # Extract arguments from contains(str, substring)
        if func_call.startswith('contains(') and func_call.endswith(')'):
            args_str = func_call[9:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 2:
                string_arg = args[0]
                substring_arg = args[1]

                self.llvm_lines.append(f"  ; contains({string_arg}, {substring_arg})")

                # For now, implement basic contains logic
                if var_name:
                    self.llvm_lines.append(f"  ; contains result stored in {var_name}")
                    # TODO: Implement full contains logic using strstr
                    self.llvm_lines.append(f"  ; TODO: implement contains({string_arg}, {substring_arg})")
                else:
                    self.llvm_lines.append(f"  ; contains result (not stored)")

    def _generate_string_uppercase(self, content):
        """Generate LLVM code for uppercase() function"""
        self.llvm_lines.append(f"  ; uppercase() - TODO: implement")

    def _generate_string_lowercase(self, content):
        """Generate LLVM code for lowercase() function"""
        self.llvm_lines.append(f"  ; lowercase() - TODO: implement")

    def _generate_string_trim(self, content):
        """Generate LLVM code for trim() function"""
        self.llvm_lines.append(f"  ; trim() - TODO: implement")

    def _generate_typed_declaration(self, stmt):
        """Generate LLVM code for typed variable declarations"""
        content = stmt['content'].strip()

        # Parse typed declaration: int x = 5
        if ' = ' in content:
            parts = content.split(' = ', 1)
            type_and_name = parts[0].strip()
            value = parts[1].strip()

            # Extract type and variable name
            type_parts = type_and_name.split()
            if len(type_parts) >= 2:
                var_type = type_parts[0]
                var_name = type_parts[1]

                self.llvm_lines.append(f"  ; {var_type} {var_name} = {value}")

                # Generate appropriate LLVM code based on type
                if var_type == 'int':
                    self._generate_int_declaration(var_name, value)
                elif var_type == 'string':
                    self._generate_string_declaration(var_name, value)
                elif var_type == 'float':
                    self._generate_float_declaration(var_name, value)
                elif var_type == 'double':
                    self._generate_double_declaration(var_name, value)
                elif var_type == 'char':
                    self._generate_char_declaration(var_name, value)
                elif var_type == 'bool':
                    self._generate_bool_declaration(var_name, value)
        else:
            # Declaration without initialization: int x
            type_parts = content.split()
            if len(type_parts) >= 2:
                var_type = type_parts[0]
                var_name = type_parts[1]

                self.llvm_lines.append(f"  ; {var_type} {var_name} (uninitialized)")

                # Initialize with default values
                if var_type == 'int':
                    self._generate_int_declaration(var_name, "0")
                elif var_type == 'string':
                    self._generate_string_declaration(var_name, '""')
                elif var_type == 'float':
                    self._generate_float_declaration(var_name, "0.0")
                elif var_type == 'double':
                    self._generate_double_declaration(var_name, "0.0")
                elif var_type == 'char':
                    self._generate_char_declaration(var_name, "'\\0'")
                elif var_type == 'bool':
                    self._generate_bool_declaration(var_name, "false")

    def _generate_int_declaration(self, var_name, value):
        """Generate LLVM code for int declaration"""
        if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
            int_val = int(value)
            self.llvm_lines.append(f"  store i32 {int_val}, i32* @global_{var_name}, align 4")
        elif any(op in value for op in ['+', '-', '*', '/', '%']):
            # Mathematical expression (including complex ones with parentheses)
            print(f"   🔢 Processing complex math expression in int declaration: {var_name} = {value}")
            result_var = self._evaluate_complex_math_expression_for_declaration(value)
            if result_var:
                self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
            else:
                # Fallback to 0
                self.llvm_lines.append(f"  store i32 0, i32* @global_{var_name}, align 4")
        elif '(' in value and value.endswith(')') and not any(op in value for op in ['+', '-', '*', '/', '%']):
            # Function call assignment (only if no math operators)
            self._generate_function_call_assign(var_name, value)
        else:
            # Expression or variable reference
            val_result = self._get_condition_value(value)
            self.llvm_lines.append(f"  store i32 {val_result}, i32* @global_{var_name}, align 4")

    def _evaluate_complex_math_expression_for_declaration(self, expression):
        """Evaluate complex mathematical expression for variable declarations"""
        print(f"   🔍 Evaluating complex math expression for declaration: {expression}")

        # Tokenize the expression
        tokens = self._tokenize_math_expression(expression)
        print(f"   🔍 Declaration tokens: {tokens}")

        if len(tokens) == 1:
            # Simple case: just a variable or number
            token = tokens[0]
            if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
                # It's a number
                result_var = self.get_temp_var()
                self.llvm_lines.append(f"  {result_var} = add i32 0, {token}")
                return result_var
            else:
                # It's a variable
                var_info = self._find_variable(token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                    return load_var
                else:
                    print(f"   ❌ Unknown variable in declaration math expression: {token}")
                    return None
        elif len(tokens) == 3:
            # Simple binary operation: a + b, a * b, etc.
            left_token = tokens[0]
            operator = tokens[1]
            right_token = tokens[2]

            # Get left operand
            if left_token.isdigit() or (left_token.startswith('-') and left_token[1:].isdigit()):
                left_var = self.get_temp_var()
                self.llvm_lines.append(f"  {left_var} = add i32 0, {left_token}")
            else:
                var_info = self._find_variable(left_token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    left_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {left_var} = load i32, i32* @global_{left_token}, align 4")
                else:
                    print(f"   ❌ Unknown left operand in declaration: {left_token}")
                    return None

            # Get right operand
            if right_token.isdigit() or (right_token.startswith('-') and right_token[1:].isdigit()):
                right_var = self.get_temp_var()
                self.llvm_lines.append(f"  {right_var} = add i32 0, {right_token}")
            else:
                var_info = self._find_variable(right_token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    right_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {right_var} = load i32, i32* @global_{right_token}, align 4")
                else:
                    print(f"   ❌ Unknown right operand in declaration: {right_token}")
                    return None

            # Perform operation
            result_var = self.get_temp_var()
            if operator == '+':
                self.llvm_lines.append(f"  {result_var} = add i32 {left_var}, {right_var}")
            elif operator == '-':
                self.llvm_lines.append(f"  {result_var} = sub i32 {left_var}, {right_var}")
            elif operator == '*':
                self.llvm_lines.append(f"  {result_var} = mul i32 {left_var}, {right_var}")
            elif operator == '/':
                self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_var}, {right_var}")
            elif operator == '%':
                self.llvm_lines.append(f"  {result_var} = srem i32 {left_var}, {right_var}")
            else:
                print(f"   ❌ Unknown operator in declaration: {operator}")
                return None

            return result_var
        else:
            # Complex expression with parentheses - use existing complex evaluation
            print(f"   🔍 Using complex evaluation for declaration: {tokens}")
            return self._evaluate_complex_math_expression(tokens)

    def _generate_string_declaration(self, var_name, value):
        """Generate LLVM code for string declaration"""
        if value.startswith('"') and value.endswith('"'):
            string_val = value[1:-1]  # Remove quotes

            # Find the string in string_literals to get the correct index
            string_index = -1
            for i, literal in enumerate(self.string_literals):
                if literal == string_val:
                    string_index = i
                    break

            if string_index >= 0:
                # Use the existing string constant from string_literals
                const_name = f".str_{string_index}"
                self.llvm_lines.append(f"  store i8* getelementptr inbounds ([{len(string_val) + 1} x i8], [{len(string_val) + 1} x i8]* @{const_name}, i64 0, i64 0), i8** @global_{var_name}, align 8")
            else:
                # Create new string constant
                if string_val not in self.string_constants:
                    const_name = f"@str_dyn_{len(self.string_constants)}"
                    self.string_constants[string_val] = const_name
                else:
                    const_name = self.string_constants[string_val]

                self.llvm_lines.append(f"  store i8* getelementptr inbounds ([{len(string_val) + 1} x i8], [{len(string_val) + 1} x i8]* @{const_name}, i64 0, i64 0), i8** @global_{var_name}, align 8")
        elif value.startswith('substring(') and value.endswith(')'):
            # Substring function call
            print(f"   ✂️ Processing substring in string declaration: {var_name} = {value}")
            self._generate_substring_assign(var_name, value)
        elif value.startswith('replace(') and value.endswith(')'):
            # Replace function call
            print(f"   🔄 Processing replace in string declaration: {var_name} = {value}")
            self._generate_replace_assign(var_name, value)
        elif value.startswith('split(') and value.endswith(')'):
            # Split function call
            print(f"   🔀 Processing split in string declaration: {var_name} = {value}")
            self._generate_split_assign(var_name, value)
        else:
            # Variable reference or expression
            self.llvm_lines.append(f"  ; string assignment from variable/expression: {var_name} = {value}")

    def _generate_float_declaration(self, var_name, value):
        """Generate LLVM code for float declaration"""
        try:
            float_val = float(value)
            # Use bitcast from i32 to float for LLVM
            import struct
            # Pack as float, unpack as unsigned int
            packed = struct.pack('f', float_val)
            unpacked = struct.unpack('I', packed)[0]
            temp_var = self.get_temp_var()
            self.llvm_lines.append(f"  {temp_var} = bitcast i32 {unpacked} to float")
            self.llvm_lines.append(f"  store float {temp_var}, float* @global_{var_name}, align 4")
        except ValueError:
            # Expression or variable reference
            self.llvm_lines.append(f"  ; float assignment from expression: {var_name} = {value}")

    def _generate_double_declaration(self, var_name, value):
        """Generate LLVM code for double declaration"""
        try:
            double_val = float(value)
            # Use bitcast from i64 to double for LLVM
            import struct
            # Pack as double, unpack as unsigned long long
            packed = struct.pack('d', double_val)
            unpacked = struct.unpack('Q', packed)[0]
            temp_var = self.get_temp_var()
            self.llvm_lines.append(f"  {temp_var} = bitcast i64 {unpacked} to double")
            self.llvm_lines.append(f"  store double {temp_var}, double* @global_{var_name}, align 8")
        except ValueError:
            # Expression or variable reference
            self.llvm_lines.append(f"  ; double assignment from expression: {var_name} = {value}")

    def _generate_char_declaration(self, var_name, value):
        """Generate LLVM code for char declaration"""
        if value.startswith("'") and value.endswith("'") and len(value) == 3:
            char_val = ord(value[1])  # Get ASCII value
            self.llvm_lines.append(f"  store i8 {char_val}, i8* @global_{var_name}, align 1")
        else:
            # Expression or variable reference
            self.llvm_lines.append(f"  ; char assignment from expression: {var_name} = {value}")

    def _generate_bool_declaration(self, var_name, value):
        """Generate LLVM code for bool declaration"""
        if value.lower() in ['true', 'false']:
            bool_val = "1" if value.lower() == "true" else "0"
            self.llvm_lines.append(f"  store i1 {bool_val}, i1* @global_{var_name}, align 1")
        else:
            # Expression or variable reference
            val_result = self._generate_condition(value)
            self.llvm_lines.append(f"  store i1 {val_result}, i1* @global_{var_name}, align 1")

    def _generate_array_assignment(self, stmt):
        """Generate LLVM code for array assignment: set arr = [1, 2, 3]"""
        content = stmt['content'][4:].strip()
        if '=' not in content:
            return

        var_name = content.split('=')[0].strip()
        array_content = content.split('=', 1)[1].strip()

        self.llvm_lines.append(f"  ; array assignment: {var_name} = {array_content}")

        # Parse array content [1, 2, 3]
        if array_content.startswith('[') and array_content.endswith(']'):
            array_items_str = array_content[1:-1].strip()
            array_items = []

            if array_items_str:
                # Split by comma and process each item
                items = [item.strip() for item in array_items_str.split(',')]
                for item in items:
                    if item.startswith('"') and item.endswith('"'):
                        array_items.append({'type': 'string', 'value': item[1:-1]})
                    elif item.lower() in ['true', 'false']:
                        array_items.append({'type': 'boolean', 'value': item.lower() == 'true'})
                    elif item.isdigit() or (item.startswith('-') and item[1:].isdigit()):
                        array_items.append({'type': 'integer', 'value': int(item)})
                    else:
                        # Variable reference
                        array_items.append({'type': 'variable', 'value': item})

            # Generate array allocation and initialization
            array_size = len(array_items)
            if array_size > 0:
                # Determine element type
                first_type = array_items[0]['type']
                if all(item['type'] == first_type for item in array_items):
                    if first_type == 'integer':
                        self._generate_integer_array(var_name, array_items)
                    elif first_type == 'string':
                        self._generate_string_array(var_name, array_items)
                    elif first_type == 'boolean':
                        self._generate_boolean_array(var_name, array_items)
                else:
                    # Mixed type array - not supported yet
                    self.llvm_lines.append(f"  ; mixed type array not supported: {var_name}")
            else:
                # Empty array
                self.llvm_lines.append(f"  ; empty array: {var_name}")

    def _generate_integer_array(self, var_name, items):
        """Generate LLVM code for integer array"""
        array_size = len(items)

        # Allocate array
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = alloca [{array_size} x i32], align 4")

        # Initialize array elements
        for i, item in enumerate(items):
            element_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds [{array_size} x i32], [{array_size} x i32]* {array_ptr}, i64 0, i64 {i}")
            self.llvm_lines.append(f"  store i32 {item['value']}, i32* {element_ptr}, align 4")

        # Store array pointer in global variable
        self.llvm_lines.append(f"  store [{array_size} x i32]* {array_ptr}, [{array_size} x i32]** @global_{var_name}, align 8")

    def _generate_string_array(self, var_name, items):
        """Generate LLVM code for string array"""
        array_size = len(items)

        # Allocate array of string pointers
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = alloca [{array_size} x i8*], align 8")

        # Initialize array elements
        for i, item in enumerate(items):
            # Create string constant if not exists
            string_value = item['value']
            if string_value not in self.string_constants:
                const_name = f"@str_dyn_{len(self.string_constants)}"
                self.string_constants[string_value] = const_name
            else:
                const_name = self.string_constants[string_value]

            element_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds [{array_size} x i8*], [{array_size} x i8*]* {array_ptr}, i64 0, i64 {i}")
            self.llvm_lines.append(f"  store i8* getelementptr inbounds ([{len(string_value) + 1} x i8], [{len(string_value) + 1} x i8]* @{const_name}, i64 0, i64 0), i8** {element_ptr}, align 8")

        # Store array pointer in global variable
        self.llvm_lines.append(f"  store [{array_size} x i8*]* {array_ptr}, [{array_size} x i8*]** @global_{var_name}, align 8")

    def _generate_boolean_array(self, var_name, items):
        """Generate LLVM code for boolean array"""
        array_size = len(items)

        # Allocate array
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = alloca [{array_size} x i1], align 1")

        # Initialize array elements
        for i, item in enumerate(items):
            element_ptr = self.get_temp_var()
            bool_val = "1" if item['value'] else "0"
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds [{array_size} x i1], [{array_size} x i1]* {array_ptr}, i64 0, i64 {i}")
            self.llvm_lines.append(f"  store i1 {bool_val}, i1* {element_ptr}, align 1")

        # Store array pointer in global variable
        self.llvm_lines.append(f"  store [{array_size} x i1]* {array_ptr}, [{array_size} x i1]** @global_{var_name}, align 8")

    def _generate_array_index_assignment(self, stmt):
        """Generate LLVM code for array index assignment: set x = arr[0]"""
        content = stmt['content'][4:].strip()
        if '=' not in content:
            return

        var_name = content.split('=')[0].strip()
        array_access = content.split('=', 1)[1].strip()

        self.llvm_lines.append(f"  ; array index assignment: {var_name} = {array_access}")

        # Parse array access: arr[0]
        if '[' in array_access and ']' in array_access:
            array_name = array_access.split('[')[0].strip()
            index_str = array_access.split('[')[1].split(']')[0].strip()

            # Find array variable info
            array_info = self._find_variable(array_name)
            if array_info:
                # Generate array access code
                if array_info['type'].startswith('array_integer'):
                    self._generate_integer_array_access(var_name, array_name, index_str)
                elif array_info['type'].startswith('array_string'):
                    self._generate_string_array_access(var_name, array_name, index_str)
                elif array_info['type'].startswith('array_boolean'):
                    self._generate_boolean_array_access(var_name, array_name, index_str)
                else:
                    self.llvm_lines.append(f"  ; unsupported array type: {array_info['type']}")
            else:
                self.llvm_lines.append(f"  ; array not found: {array_name}")

    def _generate_integer_array_access(self, var_name, array_name, index_str):
        """Generate LLVM code for integer array access"""
        # Load array pointer
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = load i32*, i32** @global_{array_name}, align 8")

        # Calculate index (assume integer literal for now)
        if index_str.isdigit():
            index = int(index_str)
            element_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds i32, i32* {array_ptr}, i64 {index}")

            # Load element value
            element_val = self.get_temp_var()
            self.llvm_lines.append(f"  {element_val} = load i32, i32* {element_ptr}, align 4")

            # Store in target variable
            self.llvm_lines.append(f"  store i32 {element_val}, i32* @global_{var_name}, align 4")
        else:
            self.llvm_lines.append(f"  ; variable array index not supported yet: {index_str}")

    def _generate_string_array_access(self, var_name, array_name, index_str):
        """Generate LLVM code for string array access"""
        # Load array pointer
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = load i8**, i8*** @global_{array_name}, align 8")

        # Calculate index (assume integer literal for now)
        if index_str.isdigit():
            index = int(index_str)
            element_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds i8*, i8** {array_ptr}, i64 {index}")

            # Load element value
            element_val = self.get_temp_var()
            self.llvm_lines.append(f"  {element_val} = load i8*, i8** {element_ptr}, align 8")

            # Store in target variable
            self.llvm_lines.append(f"  store i8* {element_val}, i8** @global_{var_name}, align 8")
        else:
            self.llvm_lines.append(f"  ; variable array index not supported yet: {index_str}")

    def _generate_boolean_array_access(self, var_name, array_name, index_str):
        """Generate LLVM code for boolean array access"""
        # Load array pointer
        array_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {array_ptr} = load i1*, i1** @global_{array_name}, align 8")

        # Calculate index (assume integer literal for now)
        if index_str.isdigit():
            index = int(index_str)
            element_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {element_ptr} = getelementptr inbounds i1, i1* {array_ptr}, i64 {index}")

            # Load element value
            element_val = self.get_temp_var()
            self.llvm_lines.append(f"  {element_val} = load i1, i1* {element_ptr}, align 1")

            # Store in target variable
            self.llvm_lines.append(f"  store i1 {element_val}, i1* @global_{var_name}, align 1")
        else:
            self.llvm_lines.append(f"  ; variable array index not supported yet: {index_str}")

    def _generate_logical_and_condition(self, condition):
        """Generate LLVM code for logical AND condition (a and b)"""
        parts = condition.split(' and ')
        if len(parts) != 2:
            # Invalid AND condition
            true_val = self.get_temp_var()
            self.llvm_lines.append(f"  {true_val} = icmp eq i32 1, 1  ; Invalid AND condition")
            return true_val

        left_condition = parts[0].strip()
        right_condition = parts[1].strip()

        # Generate left condition
        left_result = self._generate_condition(left_condition)

        # Generate right condition
        right_result = self._generate_condition(right_condition)

        # AND operation
        and_result = self.get_temp_var()
        self.llvm_lines.append(f"  {and_result} = and i1 {left_result}, {right_result}")
        return and_result

    def _generate_logical_or_condition(self, condition):
        """Generate LLVM code for logical OR condition (a or b)"""
        parts = condition.split(' or ')
        if len(parts) != 2:
            # Invalid OR condition
            true_val = self.get_temp_var()
            self.llvm_lines.append(f"  {true_val} = icmp eq i32 1, 1  ; Invalid OR condition")
            return true_val

        left_condition = parts[0].strip()
        right_condition = parts[1].strip()

        # Generate left condition
        left_result = self._generate_condition(left_condition)

        # Generate right condition
        right_result = self._generate_condition(right_condition)

        # OR operation
        or_result = self.get_temp_var()
        self.llvm_lines.append(f"  {or_result} = or i1 {left_result}, {right_result}")
        return or_result

    def _generate_logical_not_condition(self, condition):
        """Generate LLVM code for logical NOT condition (not a)"""
        inner_condition = condition.strip()[4:].strip()  # Remove 'not '

        # Generate inner condition
        inner_result = self._generate_condition(inner_condition)

        # NOT operation (XOR with true)
        not_result = self.get_temp_var()
        self.llvm_lines.append(f"  {not_result} = xor i1 {inner_result}, true")
        return not_result

    def _generate_condition(self, condition):
        """Generate LLVM code for condition evaluation"""
        print(f"   🔍 Generating condition: {condition}")

        # Check for string function calls that return boolean
        if condition.startswith('contains(') and condition.endswith(')'):
            return self._generate_contains_condition(condition)

        # Check for logical operators first
        if ' and ' in condition:
            return self._generate_logical_and_condition(condition)
        elif ' or ' in condition:
            return self._generate_logical_or_condition(condition)
        elif condition.strip().startswith('not '):
            return self._generate_logical_not_condition(condition)

        # Check for file_exists() function call
        if condition.strip().startswith('file_exists(') and condition.strip().endswith(')'):
            filename_arg = condition.strip()[12:-1].strip()
            if filename_arg.startswith('"') and filename_arg.endswith('"'):
                filename = filename_arg[1:-1]
                result_i32 = self._generate_file_exists_call(filename)
                # Convert i32 result to i1 for boolean condition
                result_i1 = self.get_temp_var()
                self.llvm_lines.append(f"  {result_i1} = icmp ne i32 {result_i32}, 0")
                return result_i1

        # Check if it's a simple boolean variable
        var_info = self._find_variable(condition.strip())
        print(f"   🔍 Variable info for '{condition.strip()}': {var_info}")
        if var_info and var_info['type'] in ['boolean', 'bool']:
            load_var = self.get_temp_var()
            self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{condition.strip()}, align 1")
            print(f"   ✅ Loading boolean variable: {condition.strip()} -> {load_var}")
            return load_var

        # Check for comparison operations (order matters - longer operators first!)
        comparison_ops = ['<=', '>=', '==', '!=', '<', '>']
        for op in comparison_ops:
            if op in condition:
                return self._generate_comparison_condition(condition, op)

        if '==' in condition:
            parts = condition.split('==')
            left = parts[0].strip()
            right = parts[1].strip()

            # Handle file_exists() specially
            if left.startswith('file_exists('):
                filename_arg = left[12:-1].strip()
                
                if filename_arg.startswith('"') and filename_arg.endswith('"'):
                    # String literal filename
                    filename = filename_arg[1:-1]
                    filename_const = self.get_temp_var() + "_filename"
                    self.llvm_lines.append(f"  {filename_const} = alloca [{len(filename)+1} x i8], align 1")
                    
                    # Store filename chars
                    for i, char in enumerate(filename):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {filename_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                    
                    # Null terminator
                    null_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {filename_const}, i64 0, i64 {len(filename)}")
                    self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
                    
                    # Call access() function
                    cast_ptr = self.get_temp_var()
                    access_result = self.get_temp_var()
                    cmp_result = self.get_temp_var()
                    self.llvm_lines.append(f"  {cast_ptr} = bitcast [{len(filename)+1} x i8]* {filename_const} to i8*")
                    self.llvm_lines.append(f"  {access_result} = call i32 @access(i8* {cast_ptr}, i32 0)")
                    self.llvm_lines.append(f"  {cmp_result} = icmp eq i32 {access_result}, 0")
                    return cmp_result
                else:
                    # Variable filename
                    var_info = self._find_variable(filename_arg)
                    if var_info and var_info['type'] == 'string':
                        load_var = self.get_temp_var()
                        access_result = self.get_temp_var()
                        cmp_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{filename_arg}, align 8")
                        self.llvm_lines.append(f"  {access_result} = call i32 @access(i8* {load_var}, i32 0)")
                        self.llvm_lines.append(f"  {cmp_result} = icmp eq i32 {access_result}, 0")
                        return cmp_result
            
            # Check if left side is a variable
            var_info = self._find_variable(left)
            if var_info:
                if var_info['type'] == 'integer':
                    load_var = self.get_temp_var()
                    cmp_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{left}, align 4")
                    self.llvm_lines.append(f"  {cmp_var} = icmp eq i32 {load_var}, {right}")
                    return cmp_var
                    
        # Default case - always true for now
        true_val = self.get_temp_var()
        self.llvm_lines.append(f"  {true_val} = icmp eq i32 1, 1  ; Default true condition")
        return true_val

    def _generate_comparison_condition(self, condition, operator):
        """Generate LLVM code for comparison condition"""
        parts = condition.split(operator)
        if len(parts) != 2:
            # Invalid comparison
            true_val = self.get_temp_var()
            self.llvm_lines.append(f"  {true_val} = icmp eq i32 1, 1  ; Invalid comparison")
            return true_val

        left = parts[0].strip()
        right = parts[1].strip()

        # Get left value
        left_val = self._get_condition_value(left)
        right_val = self._get_condition_value(right)

        # Generate comparison
        result_var = self.get_temp_var()

        if operator == '==':
            self.llvm_lines.append(f"  {result_var} = icmp eq i32 {left_val}, {right_val}")
        elif operator == '!=':
            self.llvm_lines.append(f"  {result_var} = icmp ne i32 {left_val}, {right_val}")
        elif operator == '<':
            self.llvm_lines.append(f"  {result_var} = icmp slt i32 {left_val}, {right_val}")
        elif operator == '>':
            self.llvm_lines.append(f"  {result_var} = icmp sgt i32 {left_val}, {right_val}")
        elif operator == '<=':
            self.llvm_lines.append(f"  {result_var} = icmp sle i32 {left_val}, {right_val}")
        elif operator == '>=':
            self.llvm_lines.append(f"  {result_var} = icmp sge i32 {left_val}, {right_val}")

        return result_var

    def _get_condition_value(self, token):
        """Get LLVM value for condition token"""
        # Check if it's a number
        if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
            return token

        # Check if it's a boolean literal
        if token.lower() == 'true':
            return "1"
        elif token.lower() == 'false':
            return "0"

        # Check if it's a variable
        var_info = self._find_variable(token)
        if var_info:
            # Check if it's a function parameter
            if var_info.get('is_parameter', False):
                print(f"   🔧 Using function parameter in condition: {token} -> {var_info['llvm_name']}")
                return var_info['llvm_name']
            elif var_info['type'] in ['integer', 'int']:
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                return load_var
            elif var_info['type'] in ['boolean', 'bool']:
                # Convert boolean to integer for comparison
                load_var = self.get_temp_var()
                ext_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{token}, align 1")
                self.llvm_lines.append(f"  {ext_var} = zext i1 {load_var} to i32")
                return ext_var

        # Check if it's a function parameter
        if hasattr(self, 'current_function') and self.current_function and token in self.current_function['params']:
            param_index = self.current_function['params'].index(token)
            param_type = self.current_function['param_types'][param_index]
            if param_type == 'i32':
                print(f"   🔧 Using function parameter in condition: {token} (i32)")
                return f"%{token}"
            elif param_type == 'i8*':
                print(f"   🔧 Using function parameter in condition: {token} (i8*)")
                return f"%{token}"

        # Special case: if token looks like a variable name but not found in variables list,
        # assume it's a global variable that exists (for cases like argc_val)
        if token.replace('_', '').isalnum():
            # Check if it's a known global variable pattern or looks like a variable name
            if (token in ['argc_val', 'input_file', 'output_file', 'temp_counter', 'label_counter', 'string_counter'] or
                token.startswith('global_') or
                token.endswith('_val') or
                token.endswith('_counter') or
                token.endswith('_file')):
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                print(f"   🔧 Loading assumed integer variable: {token} -> {load_var}")
                return load_var

        # Check for file_exists() function call
        if token.startswith('file_exists(') and token.endswith(')'):
            # Handle file_exists() function call
            filename_arg = token[12:-1].strip()
            if filename_arg.startswith('"') and filename_arg.endswith('"'):
                # String literal filename
                filename = filename_arg[1:-1]
                return self._generate_file_exists_call(filename)

        # Check for string function calls that return integers
        if token.startswith('length(') and token.endswith(')'):
            # Handle length() function call
            arg = token[7:-1].strip()
            if arg.startswith('"') and arg.endswith('"'):
                # String literal
                string_val = arg[1:-1]
                return str(len(string_val))
            else:
                # String variable - generate strlen call
                var_info = self._find_variable(arg)
                if var_info and var_info['type'] == 'string':
                    str_ptr = self.get_temp_var()
                    len_result = self.get_temp_var()
                    len_i32 = self.get_temp_var()
                    self.llvm_lines.append(f"  {str_ptr} = load i8*, i8** @global_{arg}, align 8")
                    self.llvm_lines.append(f"  {len_result} = call i64 @strlen(i8* {str_ptr})")
                    self.llvm_lines.append(f"  {len_i32} = trunc i64 {len_result} to i32")
                    return len_i32
                else:
                    print(f"   ⚠️ Warning: length() argument '{arg}' is not a string")
                    return "0"

        # If it's a literal number that wasn't caught above, try to parse it
        try:
            int(token)
            return token
        except ValueError:
            pass

        # Default to 0 (with warning)
        print(f"   ⚠️ Warning: Unknown token '{token}', defaulting to 0")
        return "0"
        
    def _find_variable(self, name):
        """Find variable info by name - checks both global variables and function parameters"""
        # First check function parameters if we're inside a function
        if hasattr(self, 'current_function') and self.current_function:
            if name in self.current_function['params']:
                param_index = self.current_function['params'].index(name)
                param_type = self.current_function['param_types'][param_index]
                return {
                    'name': name,
                    'type': 'string' if param_type == 'i8*' else 'integer',
                    'is_parameter': True,
                    'llvm_name': f"%{name}",
                    'llvm_type': param_type
                }

        # Then check global variables
        for var in self.variables:
            if var['name'] == name:
                return var

        # Check if it's a variable that should exist based on common patterns
        if (name.endswith('_val') or name.endswith('_counter') or name.endswith('_file') or
            name in ['argc_val', 'input_file', 'output_file', 'temp_counter', 'label_counter', 'string_counter']):
            # Return a default integer variable info
            return {
                'name': name,
                'type': 'integer',
                'value': '0',
                'is_assumed': True
            }

        return None

    def _get_variable_reference(self, var_info):
        """Get LLVM reference for a variable (parameter or global)"""
        if var_info.get('is_parameter'):
            return var_info['llvm_name']  # %param_name
        else:
            return f"@global_{var_info['name']}"  # @global_var_name

    def _load_variable_value(self, var_info):
        """Generate LLVM code to load a variable's value and return the temp variable name"""
        if var_info.get('is_parameter'):
            # Parameters are already loaded, just return the parameter name
            return var_info['llvm_name']
        else:
            # Global variables need to be loaded
            load_var = self.get_temp_var()
            if var_info['type'] in ['integer', 'int']:
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_info['name']}, align 4")
            elif var_info['type'] == 'string':
                self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_info['name']}, align 8")
            elif var_info['type'] in ['boolean', 'bool']:
                self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{var_info['name']}, align 1")
            return load_var

    def _generate_file_exists_call(self, filename):
        """Generate LLVM code for file_exists() function call and return result variable"""
        print(f"   📁 Generating file_exists() call for: {filename}")

        # Create filename string
        filename_const = self.get_temp_var() + "_filename"
        self.llvm_lines.append(f"  {filename_const} = alloca [{len(filename)+1} x i8], align 1")

        # Store filename chars
        for i, char in enumerate(filename):
            char_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {filename_const}, i64 0, i64 {i}")
            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

        # Null terminator
        null_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {filename_const}, i64 0, i64 {len(filename)}")
        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

        # Call access() function
        cast_ptr = self.get_temp_var()
        access_result = self.get_temp_var()
        cmp_result = self.get_temp_var()
        ext_result = self.get_temp_var()

        self.llvm_lines.append(f"  {cast_ptr} = bitcast [{len(filename)+1} x i8]* {filename_const} to i8*")
        self.llvm_lines.append(f"  {access_result} = call i32 @access(i8* {cast_ptr}, i32 0)")
        self.llvm_lines.append(f"  {cmp_result} = icmp eq i32 {access_result}, 0")
        self.llvm_lines.append(f"  {ext_result} = zext i1 {cmp_result} to i32")

        print(f"   ✅ file_exists() result: {ext_result}")
        return ext_result

    def _generate_statement(self, stmt):
        """Generate LLVM code for a single statement"""
        # If we're inside a function definition, collect statements for later processing
        if hasattr(self, 'current_function') and self.current_function and stmt['type'] not in ['function', 'end']:
            if not hasattr(self, 'function_body'):
                self.function_body = []
            self.function_body.append(stmt)
            return

        if stmt['type'] == 'print':
            self._generate_print(stmt)
        elif stmt['type'] == 'print_expression':
            self._generate_print_expression(stmt)
        elif stmt['type'] == 'set':
            self._generate_set(stmt)
        elif stmt['type'] == 'array_assignment':
            self._generate_array_assignment(stmt)
        elif stmt['type'] == 'array_index_assignment':
            self._generate_array_index_assignment(stmt)
        elif stmt['type'] == 'import':
            self._generate_import(stmt)
        elif stmt['type'] == 'write':
            self._generate_write(stmt)
        elif stmt['type'] == 'write_file':
            self._generate_write_file_call(stmt)
        elif stmt['type'] == 'append_file':
            self._generate_append_file_call(stmt)
        elif stmt['type'] == 'remove_file':
            self._generate_remove_file_call(stmt)
        elif stmt['type'] == 'system':
            self._generate_system(stmt)
        elif stmt['type'] == 'argc':
            self._generate_argc(stmt)
        elif stmt['type'] == 'argv':
            self._generate_argv(stmt)
        elif stmt['type'] == 'function':
            self._generate_function_definition(stmt)
        elif stmt['type'] == 'function_call':
            self._generate_function_call(stmt)
        elif stmt['type'] == 'if':
            # Handle if statement - find corresponding control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                self._handle_control_block(control_block)
            else:
                self.llvm_lines.append(f"  ; if statement without control block: {stmt['content']}")
        elif stmt['type'] == 'while':
            # Handle while statement - find corresponding control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                self._handle_while_loop(control_block)
            else:
                self.llvm_lines.append(f"  ; while statement without control block: {stmt['content']}")
        elif stmt['type'] == 'for':
            # Handle for statement - find corresponding control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                self._handle_for_loop(control_block)
            else:
                self.llvm_lines.append(f"  ; for statement without control block: {stmt['content']}")
        elif stmt['type'] == 'end':
            self._generate_end(stmt)
        elif stmt['type'] == 'break':
            self._generate_break(stmt)
        elif stmt['type'] == 'continue':
            self._generate_continue(stmt)
        elif stmt['type'] == 'switch':
            self._generate_switch(stmt)
        elif stmt['type'] == 'case':
            self._generate_case(stmt)
        elif stmt['type'] == 'default':
            self._generate_default(stmt)
        elif stmt['type'] == 'try':
            self._generate_try(stmt)
        elif stmt['type'] == 'catch':
            self._generate_catch(stmt)
        elif stmt['type'] == 'throw':
            self._generate_throw(stmt)
        elif stmt['type'] == 'finally':
            self._generate_finally(stmt)
        elif stmt['type'] in ['int_declaration', 'string_declaration', 'float_declaration', 'double_declaration', 'char_declaration', 'bool_declaration']:
            self._generate_typed_declaration(stmt)
        elif stmt['type'] in ['string_length', 'string_substring', 'string_split', 'string_replace', 'string_contains', 'string_uppercase', 'string_lowercase', 'string_trim']:
            self._generate_string_function(stmt)

    def _generate_end(self, stmt):
        """Handle end statement (for functions, if blocks, etc.)"""
        if hasattr(self, 'try_stack') and self.try_stack:
            # End of try-catch-finally block
            try_context = self.try_stack.pop()
            self._finalize_try(try_context)
        elif hasattr(self, 'switch_stack') and self.switch_stack:
            # End of switch statement
            switch_context = self.switch_stack.pop()
            self._finalize_switch(switch_context)
        elif hasattr(self, 'current_function') and self.current_function:
            # End of function definition
            func_name = self.current_function['name']
            print(f"   🔧 Ending function definition: {func_name}")

            # Process function body statements directly (avoid recursion)
            for body_stmt in self.function_body:
                self._generate_function_body_statement(body_stmt)

            # Update function signature with correct return type
            return_type = self.current_function.get('return_type', 'void')
            if return_type == 'integer':
                llvm_return_type = 'i32'
            elif return_type == 'boolean':
                llvm_return_type = 'i1'
            elif return_type == 'string':
                llvm_return_type = 'i8*'
            else:
                llvm_return_type = 'void'

            # Update the function header line
            if hasattr(self, 'function_header_line'):
                old_header = self.llvm_lines[self.function_header_line]
                new_header = old_header.replace('define void @', f'define {llvm_return_type} @')
                self.llvm_lines[self.function_header_line] = new_header

            # Add default return if no explicit return was found
            if return_type == 'void':
                self.llvm_lines.append(f"  ret void")
            elif return_type == 'integer':
                self.llvm_lines.append(f"  ret i32 0  ; default return")
            elif return_type == 'boolean':
                self.llvm_lines.append(f"  ret i1 0  ; default return")
            elif return_type == 'string':
                self.llvm_lines.append(f"  ret i8* null  ; default return")

            self.llvm_lines.append(f"}}")

            # Clear current function
            self.current_function = None
            self.function_body = []
        else:
            # Regular end statement (for if/while blocks)
            self.llvm_lines.append(f"  ; end")



    def _is_statement_in_control_block(self, stmt):
        """Check if a statement is already handled as part of a control block"""
        for block in self.control_blocks:
            # Check if statement is in if_statements
            for if_stmt in block.get('if_statements', []):
                if if_stmt.get('line_number') == stmt['line_number']:
                    return True
            # Check if statement is in else_statements
            for else_stmt in block.get('else_statements', []):
                if else_stmt.get('line_number') == stmt['line_number']:
                    return True
            # Check if statement is in elif blocks
            for elif_block in block.get('elif_blocks', []):
                for elif_stmt in elif_block.get('statements', []):
                    if elif_stmt.get('line_number') == stmt['line_number']:
                        return True
            # Check if statement is in while/for body
            for body_stmt in block.get('body_statements', []):
                if body_stmt.get('line_number') == stmt['line_number']:
                    return True
        return False

    def _is_statement_in_function(self, stmt):
        """Check if a statement is inside a function definition"""
        # Check if statement is between function and end statements
        for i, s in enumerate(self.statements):
            if s['type'] == 'function':
                function_start = s['line_number']
                function_end = None

                # Look for the matching 'end' statement or end of function by indentation
                for j in range(i + 1, len(self.statements)):
                    next_stmt = self.statements[j]
                    if next_stmt['type'] == 'end':
                        function_end = next_stmt['line_number']
                        break
                    # Check if we've reached a statement with same or less indentation (end of function)
                    elif not next_stmt['content'].startswith('    ') and not next_stmt['content'].startswith('\t'):
                        # This statement is not indented, so function has ended
                        function_end = next_stmt['line_number']
                        break

                # If no explicit end found, function goes to end of file
                if function_end is None:
                    function_end = float('inf')

                # Check if current statement is within function boundaries
                if function_start < stmt['line_number'] < function_end:
                    return True
        return False

    def _generate_function_body_statement(self, stmt):
        """Generate LLVM IR for statements inside function body"""

        # Skip statements that are already handled in control blocks,
        # UNLESS we're currently inside a control block execution
        if (not hasattr(self, '_inside_control_block') or not self._inside_control_block) and self._is_statement_in_control_block(stmt):
            print(f"   ⏭️  Skipping statement at line {stmt['line_number']} - already handled in control block")
            return
        if stmt['type'] == 'print':
            # print statement inside function
            content = stmt['content'][4:].strip()

            # Check if it's a simple string literal (no concatenation)
            if content.startswith('"') and content.endswith('"') and '+' not in content:
                # Simple string literal - use string constant instead of dynamic building
                string_text = content[1:-1]
                string_index = self._find_string_index(string_text)
                if string_index >= 0:
                    string_length = calculate_llvm_string_length(string_text)
                    self.llvm_lines.append(f"  ; print \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\"")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_length} x i8], [{string_length} x i8]* @.str_{string_index}, i32 0, i32 0))")
                else:
                    # Fallback to dynamic building if string not found
                    escaped_text = self._escape_printf_format(string_text)
                    format_len = len(escaped_text) + 1
                    format_const = self.get_temp_var() + "_fmt"

                    # Create format string
                    self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")
                    for i, char in enumerate(escaped_text):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                    # Null terminator
                    null_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(escaped_text)}")
                    self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                    # Cast and call printf
                    format_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr})")
            else:
                # String expression (may contain concatenation)
                print(f"   🔧 Processing string expression: {content}")
                self._generate_function_print_expression(content)
        elif stmt['type'] == 'print_expression':
            # print expression inside function (string concatenation)
            # Remove 'print ' from the beginning, handling indentation
            content = stmt['content'].strip()
            if content.startswith('print '):
                content = content[4:].strip()
            print(f"   🔧 Processing print_expression: {content}")
            self._generate_function_print_expression(content)
        elif stmt['type'] == 'return':
            # Return statement inside function
            self._generate_function_return(stmt)
        elif stmt['type'] == 'set':
            # Set statement inside function
            self._generate_function_set(stmt)
        elif stmt['type'] == 'function_call':
            # Function call inside function
            print(f"   🔧 Processing function call inside function: {stmt['content']}")
            self._generate_function_call(stmt)
        elif stmt['type'] == 'if':
            # If statement inside function - find corresponding control block
            control_block = None
            print(f"   🔍 Looking for control block for if statement at line {stmt['line_number']}")
            print(f"   🔍 Available control blocks: {[f'line {b['start_line']}' for b in self.control_blocks]}")
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                print(f"   🔧 Processing if statement inside function: {control_block['condition']}")
                print(f"   🔍 Control block if_statements: {len(control_block.get('if_statements', []))}")
                print(f"   🔍 Control block else_statements: {len(control_block.get('else_statements', []))}")
                if control_block.get('if_statements'):
                    print(f"   🔍 First if statement: {control_block['if_statements'][0]}")
                self._handle_control_block(control_block)
            else:
                print(f"   ❌ No control block found for if statement at line {stmt['line_number']}")
                self.llvm_lines.append(f"  ; if statement without control block: {stmt['content']}")
        elif stmt['type'] == 'while':
            # While statement inside function - find corresponding control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                print(f"   🔧 Processing while statement inside function: {control_block['condition']}")
                self._handle_while_loop(control_block)
            else:
                self.llvm_lines.append(f"  ; while statement without control block: {stmt['content']}")
        elif stmt['type'] == 'for':
            # For statement inside function - find corresponding control block
            control_block = None
            for block in self.control_blocks:
                if block['start_line'] == stmt['line_number']:
                    control_block = block
                    break
            if control_block:
                print(f"   🔧 Processing for statement inside function")
                self._handle_for_loop(control_block)
            else:
                self.llvm_lines.append(f"  ; for statement without control block: {stmt['content']}")
        elif stmt['type'] in ['int_declaration', 'string_declaration', 'bool_declaration']:
            # Variable declarations inside function
            print(f"   🔧 Processing variable declaration inside function: {stmt['content']}")
            self._generate_function_variable_declaration(stmt)
        else:
            # Other statement types (placeholder for now)
            self.llvm_lines.append(f"  ; {stmt['type']}: {stmt['content']}")

    def _generate_function_variable_declaration(self, stmt):
        """Generate LLVM IR for variable declaration inside function"""
        content = stmt['content']

        if stmt['type'] == 'int_declaration':
            # int var_name = value
            # Remove 'int ' from the beginning, handling indentation
            remaining = content.strip()
            if remaining.startswith('int '):
                remaining = remaining[4:].strip()
            if '=' in remaining:
                var_name = remaining.split('=')[0].strip()
                var_value = remaining.split('=', 1)[1].strip()

                # Check if it's a function call like argc()
                if var_value.startswith('argc()'):
                    self.llvm_lines.append(f"  ; int_declaration: {content}")
                    self._generate_argc_assign(var_name)
                elif var_value.startswith('argv('):
                    self.llvm_lines.append(f"  ; int_declaration: {content}")
                    self._generate_argv_assign(var_name, var_value)
                elif var_value.isdigit() or (var_value.startswith('-') and var_value[1:].isdigit()):
                    # Numeric literal
                    self.llvm_lines.append(f"  ; int_declaration: {content}")
                    self.llvm_lines.append(f"  store i32 {var_value}, i32* @global_{var_name}, align 4")
                else:
                    # Variable or expression
                    self.llvm_lines.append(f"  ; int_declaration: {content}")
                    var_info = self._find_variable(var_value)
                    if var_info and var_info['type'] in ['integer', 'int']:
                        load_var = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_value}, align 4")
                        self.llvm_lines.append(f"  store i32 {load_var}, i32* @global_{var_name}, align 4")
                    else:
                        self.llvm_lines.append(f"  store i32 0, i32* @global_{var_name}, align 4  ; default value")
            else:
                # Declaration without initialization
                var_name = remaining
                self.llvm_lines.append(f"  ; int_declaration: {content}")
                self.llvm_lines.append(f"  store i32 0, i32* @global_{var_name}, align 4  ; default value")

        elif stmt['type'] == 'string_declaration':
            # string var_name = value
            # Remove 'string ' from the beginning, handling indentation
            remaining = content.strip()
            if remaining.startswith('string '):
                remaining = remaining[7:].strip()
            if '=' in remaining:
                var_name = remaining.split('=')[0].strip()
                var_value = remaining.split('=', 1)[1].strip()

                self.llvm_lines.append(f"  ; string_declaration: {content}")

                if var_value.startswith('"') and var_value.endswith('"'):
                    # String literal
                    string_text = var_value[1:-1]
                    string_index = self._find_string_index(string_text)
                    if string_index >= 0:
                        string_length = calculate_llvm_string_length(string_text)
                        const_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {const_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                        self.llvm_lines.append(f"  store i8* {const_ptr}, i8** @global_{var_name}, align 8")
                    else:
                        self.llvm_lines.append(f"  store i8* null, i8** @global_{var_name}, align 8  ; string not found")
                elif var_value.startswith('read_file('):
                    self._generate_read_file(var_name, var_value)
                else:
                    # Variable or other expression
                    var_info = self._find_variable(var_value)
                    if var_info and var_info['type'] == 'string':
                        load_var = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_value}, align 8")
                        self.llvm_lines.append(f"  store i8* {load_var}, i8** @global_{var_name}, align 8")
                    else:
                        self.llvm_lines.append(f"  store i8* null, i8** @global_{var_name}, align 8  ; default value")
            else:
                # Declaration without initialization
                var_name = remaining
                self.llvm_lines.append(f"  ; string_declaration: {content}")
                self.llvm_lines.append(f"  store i8* null, i8** @global_{var_name}, align 8  ; default value")

        elif stmt['type'] == 'bool_declaration':
            # bool var_name = value
            # Remove 'bool ' from the beginning, handling indentation
            remaining = content.strip()
            if remaining.startswith('bool '):
                remaining = remaining[5:].strip()
            if '=' in remaining:
                var_name = remaining.split('=')[0].strip()
                var_value = remaining.split('=', 1)[1].strip()

                self.llvm_lines.append(f"  ; bool_declaration: {content}")

                if var_value.lower() == 'true':
                    self.llvm_lines.append(f"  store i1 1, i1* @global_{var_name}, align 1")
                elif var_value.lower() == 'false':
                    self.llvm_lines.append(f"  store i1 0, i1* @global_{var_name}, align 1")
                elif var_value.startswith('file_exists('):
                    self._generate_file_exists_assign(var_name, var_value)
                else:
                    # Variable or expression
                    var_info = self._find_variable(var_value)
                    if var_info and var_info['type'] in ['boolean', 'bool']:
                        load_var = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{var_value}, align 1")
                        self.llvm_lines.append(f"  store i1 {load_var}, i1* @global_{var_name}, align 1")
                    else:
                        self.llvm_lines.append(f"  store i1 0, i1* @global_{var_name}, align 1  ; default value")
            else:
                # Declaration without initialization
                var_name = remaining
                self.llvm_lines.append(f"  ; bool_declaration: {content}")
                self.llvm_lines.append(f"  store i1 0, i1* @global_{var_name}, align 1  ; default value")

    def _generate_function_return(self, stmt):
        """Generate LLVM IR for return statement inside function"""
        content = stmt['content'][7:].strip()  # Remove 'return '

        if not self.current_function:
            self.llvm_lines.append(f"  ; return outside function: {content}")
            return

        print(f"   🔄 Processing return statement: {content}")

        if not content:
            # Empty return - void function
            self.llvm_lines.append(f"  ret void")
            self.current_function['return_type'] = 'void'
        else:
            # Return with value - determine type and generate appropriate return
            return_value, return_type = self._get_return_value(content)
            self.current_function['return_type'] = return_type

            if return_type == 'integer':
                self.llvm_lines.append(f"  ret i32 {return_value}")
            elif return_type == 'boolean':
                self.llvm_lines.append(f"  ret i1 {return_value}")
            elif return_type == 'string':
                self.llvm_lines.append(f"  ret i8* {return_value}")
            else:
                # Unknown type, default to void
                self.llvm_lines.append(f"  ret void")
                self.current_function['return_type'] = 'void'

    def _generate_function_set(self, stmt):
        """Generate LLVM IR for set statement inside function"""
        # Remove 'set ' from the beginning, handling indentation
        content = stmt['content'].strip()
        if content.startswith('set '):
            content = content[4:].strip()

        if '=' not in content:
            self.llvm_lines.append(f"  ; invalid set statement: {content}")
            return

        var_name = content.split('=')[0].strip()
        var_value = content.split('=', 1)[1].strip()

        print(f"   🔧 Processing function set: {var_name} = {var_value}")

        # Check if it's a mathematical expression with parameters
        if any(op in var_value for op in ['+', '-', '*', '/', '%']):
            result_var = self._evaluate_function_math_expression(var_value)
            # Store result in global variable (for now)
            self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
        else:
            # Simple assignment
            self.llvm_lines.append(f"  ; simple assignment not implemented: {var_name} = {var_value}")

    def _evaluate_function_math_expression(self, expr):
        """Evaluate mathematical expression inside function (with parameter support)"""
        tokens = self._tokenize_math_expression(expr)

        if len(tokens) == 1:
            # Single value
            return self._get_function_math_value(tokens[0])
        elif len(tokens) == 3:
            # Simple binary operation: operand1 operator operand2
            left_val = self._get_function_math_value(tokens[0])
            operator = tokens[1]
            right_val = self._get_function_math_value(tokens[2])

            result_var = self.get_temp_var()

            if operator == '+':
                self.llvm_lines.append(f"  {result_var} = add i32 {left_val}, {right_val}")
            elif operator == '-':
                self.llvm_lines.append(f"  {result_var} = sub i32 {left_val}, {right_val}")
            elif operator == '*':
                self.llvm_lines.append(f"  {result_var} = mul i32 {left_val}, {right_val}")
            elif operator == '/':
                self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_val}, {right_val}")
            elif operator == '%':
                self.llvm_lines.append(f"  {result_var} = srem i32 {left_val}, {right_val}")

            return result_var
        else:
            # Complex expression - return 0 for now
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {result_var} = add i32 0, 0  ; complex function expression")
            return result_var

    def _get_function_math_value(self, token):
        """Get LLVM value for mathematical token inside function (with parameter support)"""
        if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
            # Numeric literal
            return token
        else:
            # Check if it's a function parameter first
            if hasattr(self, 'current_function') and self.current_function:
                if token in self.current_function['params']:
                    # Function parameter - use directly
                    return f"%{token}"

            # Variable (fallback)
            var_info = self._find_variable(token)
            if var_info and var_info['type'] == 'integer':
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                return load_var
            else:
                # Unknown variable, return 0
                return "0"

    def _get_return_value(self, content):
        """Get LLVM value and type for return expression"""
        # Check if it's a literal
        if content.isdigit() or (content.startswith('-') and content[1:].isdigit()):
            return content, 'integer'
        elif content.lower() in ['true', 'false']:
            value = "1" if content.lower() == "true" else "0"
            return value, 'boolean'
        elif content.startswith('"') and content.endswith('"'):
            # String literal - need to create constant
            string_text = content[1:-1]
            string_index = self._find_string_index(string_text)
            if string_index >= 0:
                string_length = calculate_llvm_string_length(string_text)
                const_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {const_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                return const_ptr, 'string'
            else:
                return "null", 'string'
        elif any(op in content for op in ['+', '-', '*', '/', '%']):
            # Mathematical expression
            print(f"   🔢 Evaluating math expression in return: {content}")
            result_var = self._evaluate_function_math_expression(content)
            print(f"   ✅ Math expression result: {result_var}")
            return result_var, 'integer'
        else:
            # Variable
            var_info = self._find_variable(content)
            if var_info:
                if var_info['type'] == 'integer':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{content}, align 4")
                    return load_var, 'integer'
                elif var_info['type'] == 'boolean':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{content}, align 1")
                    return load_var, 'boolean'
                elif var_info['type'] == 'string':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{content}, align 8")
                    return load_var, 'string'

        # Default case
        return "0", 'integer'

    def _generate_function_print_expression(self, content):
        """Generate LLVM IR for print expression inside function (with parameter support)"""
        # Parse string expression parts
        parts = self._parse_string_expression_parts(content)

        if len(parts) == 1 and parts[0]['type'] == 'string_literal':
            # Simple string literal - use string constant instead of dynamic building
            string_text = parts[0]['value']
            string_index = self._find_string_index(string_text)
            if string_index >= 0:
                string_length = calculate_llvm_string_length(string_text)
                self.llvm_lines.append(f"  ; print \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\"")
                self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_length} x i8], [{string_length} x i8]* @.str_{string_index}, i32 0, i32 0))")
                return

            # Fallback to dynamic building if string not found
            escaped_text = self._escape_printf_format(string_text)
            format_len = len(escaped_text) + 1
            format_const = self.get_temp_var() + "_fmt"

            # Create format string
            self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")
            for i, char in enumerate(escaped_text):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

            # Null terminator
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(escaped_text)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

            # Cast and call printf
            format_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr})")
        else:
            # Complex string expression with concatenation
            self._generate_function_string_concatenation(parts)

    def _generate_function_string_concatenation(self, parts):
        """Generate LLVM IR for string concatenation inside function - IMPROVED VERSION"""
        self.llvm_lines.append(f"  ; string concatenation with {len(parts)} parts")

        # Debug: print parts
        parts_debug = [f"{p['type']}:{p.get('value', p.get('name', '?'))}" for p in parts]
        print(f"   🔍 String parts: {parts_debug}")

        # Check if we can use printf with format string (simple case)
        if self._can_use_printf_for_concatenation(parts):
            print(f"   ✅ Using printf concatenation")
            self._generate_printf_concatenation(parts)
        else:
            print(f"   ⚠️ Using buffer concatenation (fallback)")
            # Use buffer-based concatenation for complex cases
            self._generate_buffer_concatenation(parts)

        # Add newline at the end
        self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))")
        return  # Exit early to avoid the complex code below

    def _can_use_printf_for_concatenation(self, parts):
        """Check if we can use printf with format string for simple concatenation"""
        # For now, support simple cases: string literal + variable
        if len(parts) == 2:
            if (parts[0]['type'] == 'string_literal' and
                parts[1]['type'] == 'variable'):
                return True
        return False

    def _generate_printf_concatenation(self, parts):
        """Generate printf-based concatenation for simple cases"""
        if len(parts) == 2 and parts[0]['type'] == 'string_literal' and parts[1]['type'] == 'variable':
            # Handle "string" + variable case
            string_text = parts[0]['value']
            var_name = parts[1].get('name', parts[1].get('value', ''))
            var_info = self._find_variable(var_name)

            # Check if it's a function parameter first
            if hasattr(self, 'current_function') and self.current_function and var_name in self.current_function['params']:
                param_index = self.current_function['params'].index(var_name)
                param_type = self.current_function['param_types'][param_index]

                if param_type == 'i32':
                    # Integer function parameter - use %d format
                    format_text = string_text + "%d"
                    format_len = len(format_text) + 1

                    # Create format string constant
                    format_const = self.get_temp_var() + "_fmt"
                    self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")

                    # Store format string characters
                    for i, char in enumerate(format_text):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                    # Null terminator
                    null_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(format_text)}")
                    self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                    # Call printf with integer parameter
                    format_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr}, i32 %{var_name})")
                    return
                elif param_type == 'i8*':
                    # String function parameter - use %s format
                    format_text = string_text + "%s"
                    format_len = len(format_text) + 1

                    # Create format string constant
                    format_const = self.get_temp_var() + "_fmt"
                    self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")

                    # Store format string characters
                    for i, char in enumerate(format_text):
                        char_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                        self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                    # Null terminator
                    null_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(format_text)}")
                    self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                    # Call printf with string parameter
                    format_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr}, i8* %{var_name})")
                    return

            # Handle regular variables
            if var_info and var_info['type'] == 'string':
                # Create format string: "Hello %s"
                format_text = string_text + "%s"
                format_len = len(format_text) + 1

                # Create format string constant
                format_const = self.get_temp_var() + "_fmt"
                self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")

                # Store format string characters
                for i, char in enumerate(format_text):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(format_text)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Get variable value and call printf
                var_value = self._load_variable_value(var_info)
                format_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
                self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr}, i8* {var_value})")
            elif var_info and var_info['type'] in ['integer', 'int']:
                # Integer variable - use %d format
                format_text = string_text + "%d"
                format_len = len(format_text) + 1

                # Create format string constant
                format_const = self.get_temp_var() + "_fmt"
                self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")

                # Store format string characters
                for i, char in enumerate(format_text):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(format_text)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Load variable value and call printf
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name}, align 4")
                format_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")
                self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr}, i32 {load_var})")

    def _generate_buffer_concatenation(self, parts):
        """Generate buffer-based concatenation for complex cases"""
        # Fallback to printing each part separately (simplified)
        for part in parts:
            if part['type'] == 'string_literal':
                string_text = part['value']
                string_index = self._find_string_index(string_text)
                if string_index >= 0:
                    string_const = f"@.str_{string_index}"
                    string_len = len(string_text) + 1
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_len} x i8], [{string_len} x i8]* {string_const}, i32 0, i32 0))")
                else:
                    # Use string expression constant
                    for j, expr_part in enumerate(self.string_expressions):
                        if string_text in expr_part:
                            string_const = f"@.str_expr_{j}"
                            string_len = len(string_text) + 1
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_len} x i8], [{string_len} x i8]* {string_const}, i32 0, i32 0))")
                            break
            elif part['type'] == 'variable':
                var_name = part.get('name', part.get('value', ''))

                # Check if it's a function parameter first
                if hasattr(self, 'current_function') and self.current_function and var_name in self.current_function['params']:
                    # Function parameter - print as integer
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %{var_name})")
                else:
                    # Regular variable
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] == 'string':
                        var_value = self._load_variable_value(var_info)
                        self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {var_value})")
                    elif var_info and var_info['type'] in ['integer', 'int']:
                        load_var = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name}, align 4")
                        self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 {load_var})")

    def _parse_string_expression_parts(self, content):
        """Parse string expression into parts (literals and variables) - IMPROVED"""
        parts = []

        # Handle simple concatenation: "string" + variable
        if ' + ' in content:
            raw_parts = content.split(' + ')
            for part in raw_parts:
                part = part.strip()
                if part.startswith('"') and part.endswith('"'):
                    # String literal
                    parts.append({
                        'type': 'string_literal',
                        'value': part[1:-1]  # Remove quotes
                    })
                elif part:
                    # Variable
                    parts.append({
                        'type': 'variable',
                        'name': part
                    })
        elif content.startswith('"') and content.endswith('"'):
            # Simple string literal
            parts.append({
                'type': 'string_literal',
                'value': content[1:-1]  # Remove quotes
            })
        else:
            # Variable or other expression
            parts.append({
                'type': 'variable',
                'name': content
            })

        return parts

    def _can_use_printf_directly(self, parts):
        """Check if we can use printf directly instead of string concatenation - SIMPLIFIED"""
        return False  # Always use simplified concatenation

    def _parse_string_expression_parts(self, content):
        """Parse string expression into parts (literals and variables)"""
        parts = []
        current_part = ""
        in_quotes = False
        i = 0

        while i < len(content):
            char = content[i]

            if char == '"' and (i == 0 or content[i-1] != '\\'):
                if in_quotes:
                    # End of string literal
                    parts.append({
                        'type': 'string_literal',
                        'value': current_part
                    })
                    current_part = ""
                    in_quotes = False
                else:
                    # Start of string literal
                    if current_part.strip():
                        # Variable before quotes
                        parts.append({
                            'type': 'variable',
                            'name': current_part.strip()
                        })
                        current_part = ""
                    in_quotes = True
            elif char == '+' and not in_quotes:
                # Concatenation operator - but check for function calls first
                if in_quotes:
                    current_part += char
                else:
                    # Check if we're inside a function call (count parentheses)
                    paren_count = current_part.count('(') - current_part.count(')')
                    if paren_count > 0:
                        # We're inside a function call, continue
                        current_part += char
                    else:
                        # End of current part
                        if current_part.strip():
                            if current_part.strip().startswith('"') and current_part.strip().endswith('"'):
                                # String literal
                                parts.append({
                                    'type': 'string_literal',
                                    'value': current_part.strip()[1:-1]
                                })
                            else:
                                # Check if it's a function call
                                part_name = current_part.strip()
                                if part_name.startswith('length(') and part_name.endswith(')'):
                                    parts.append({
                                        'type': 'function_call',
                                        'name': 'length',
                                        'args': [part_name[7:-1].strip()]
                                    })
                                else:
                                    # Variable
                                    parts.append({
                                        'type': 'variable',
                                        'name': part_name
                                    })
                        current_part = ""
            else:
                current_part += char

            i += 1

        # Handle remaining part
        if current_part.strip():
            if in_quotes:
                parts.append({
                    'type': 'string_literal',
                    'value': current_part
                })
            elif current_part.strip().startswith('"') and current_part.strip().endswith('"'):
                parts.append({
                    'type': 'string_literal',
                    'value': current_part.strip()[1:-1]
                })
            else:
                # Check if it's a function call
                part_name = current_part.strip()
                if part_name.startswith('length(') and part_name.endswith(')'):
                    parts.append({
                        'type': 'function_call',
                        'name': 'length',
                        'args': [part_name[7:-1].strip()]
                    })
                else:
                    parts.append({
                        'type': 'variable',
                        'name': part_name
                    })

        return parts

    def _generate_print(self, stmt):
        """Generate printf for simple print statement"""
        # Remove 'print ' from the beginning, handling extra spaces
        content = stmt['content'].strip()
        if content.startswith('print '):
            content = content[6:].strip()  # Remove 'print ' (6 characters)

        if content.startswith('"') and content.endswith('"'):
            # String literal
            string_text = content[1:-1]
            string_index = self._find_string_index(string_text)
            if string_index >= 0:
                string_length = calculate_llvm_string_length(string_text)
                self.llvm_lines.append(f"  ; print \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\"")
                self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_length} x i8], [{string_length} x i8]* @.str_{string_index}, i32 0, i32 0))")
            else:
                # String not found in pre-extracted list, create inline string
                # String not found in pre-extracted list, create inline string
                escaped_text = self._escape_string(string_text)
                # Calculate correct length in bytes (UTF-8 encoding)
                utf8_bytes = string_text.encode('utf-8')
                escaped_len = len(utf8_bytes) + 1  # +1 for null terminator
                string_const = self.get_temp_var() + "_str"
                self.llvm_lines.append(f"  ; print \"{string_text[:50]}{'...' if len(string_text) > 50 else ''}\" (inline)")
                self.llvm_lines.append(f"  {string_const} = alloca [{escaped_len} x i8], align 1")
                self.llvm_lines.append(f"  store [{escaped_len} x i8] c\"{escaped_text}\\00\", [{escaped_len} x i8]* {string_const}, align 1")
                str_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {str_ptr} = bitcast [{escaped_len} x i8]* {string_const} to i8*")
                self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {str_ptr})")
            # Add newline
            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))")
        else:
            # Variable
            var_info = self._find_variable(content)
            if var_info:
                if var_info['type'] == 'integer':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  ; print {content} (integer)")
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{content}, align 4")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 {load_var})")
                elif var_info['type'] == 'string':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  ; print {content} (string)")
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{content}, align 8")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {load_var})")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))")
                elif var_info['type'] == 'boolean':
                    load_var = self.get_temp_var()
                    true_label = self.get_temp_var()
                    false_label = self.get_temp_var()
                    end_label = self.get_temp_var()

                    self.llvm_lines.append(f"  ; print {content} (boolean)")
                    self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{content}, align 1")
                    self.llvm_lines.append(f"  br i1 {load_var}, label %{true_label[1:]}, label %{false_label[1:]}")

                    # True branch
                    self.llvm_lines.append(f"{true_label[1:]}:")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))")
                    self.llvm_lines.append(f"  br label %{end_label[1:]}")

                    # False branch
                    self.llvm_lines.append(f"{false_label[1:]}:")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))")
                    self.llvm_lines.append(f"  br label %{end_label[1:]}")

                    # End
                    self.llvm_lines.append(f"{end_label[1:]}:")
                    self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))")
                    
    def _generate_print_expression(self, stmt):
        """Generate printf for string concatenation - FIXED VERSION"""
        # Extract content properly - handle indentation
        content = stmt['content'].strip()
        if content.startswith('print '):
            content = content[6:]  # Remove 'print ' (6 characters)
        else:
            content = content[4:]  # Fallback

        print(f"   🔧 Processing print_expression: {content}")

        # FIXED: Use simple printf for string concatenation
        self.llvm_lines.append(f"  ; print expression: {content[:50]}{'...' if len(content) > 50 else ''}")

        # IMPROVED: Handle string concatenation properly
        if '"' in content and '+' in content:
            # Parse the concatenation parts properly
            parts = self._parse_string_concatenation(content)

            # Print each part separately
            for part in parts:
                if part['type'] == 'string_literal':
                    string_text = part['value']
                    # Use string expression constants
                    for j, expr_part in enumerate(self.string_expressions):
                        if string_text == expr_part:
                            string_const = f"@.str_expr_{j}"
                            string_len = len(string_text) + 1
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_len} x i8], [{string_len} x i8]* {string_const}, i32 0, i32 0))")
                            break
                    else:
                        # Fallback: create inline string
                        escaped_text = self._escape_string(string_text)
                        string_len = len(string_text) + 1
                        string_const = self.get_temp_var() + "_str"
                        self.llvm_lines.append(f"  {string_const} = alloca [{string_len} x i8], align 1")
                        self.llvm_lines.append(f"  store [{string_len} x i8] c\"{escaped_text}\\00\", [{string_len} x i8]* {string_const}, align 1")
                        str_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {str_ptr} = bitcast [{string_len} x i8]* {string_const} to i8*")
                        self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {str_ptr})")

                elif part['type'] == 'variable':
                    var_name = part['name']
                    var_info = self._find_variable(var_name)
                    if var_info:
                        if var_info['type'] == 'string':
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_name}, align 8")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {load_var})")
                        elif var_info['type'] in ['integer', 'int']:
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name}, align 4")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 {load_var})")
                        elif var_info['type'] == 'float':
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load float, float* @global_{var_name}, align 4")
                            # Convert float to double for printf
                            double_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {double_var} = fpext float {load_var} to double")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double {double_var})")
                        elif var_info['type'] == 'double':
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load double, double* @global_{var_name}, align 8")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double {load_var})")
                        elif var_info['type'] == 'char':
                            load_var = self.get_temp_var()
                            ext_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i8, i8* @global_{var_name}, align 1")
                            self.llvm_lines.append(f"  {ext_var} = zext i8 {load_var} to i32")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 {ext_var})")
                        elif var_info['type'] in ['bool', 'boolean']:
                            load_var = self.get_temp_var()
                            true_label = self.get_temp_var()
                            false_label = self.get_temp_var()
                            end_label = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i1, i1* @global_{var_name}, align 1")
                            self.llvm_lines.append(f"  br i1 {load_var}, label %{true_label[1:]}, label %{false_label[1:]}")
                            # True branch
                            self.llvm_lines.append(f"{true_label[1:]}:")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))")
                            self.llvm_lines.append(f"  br label %{end_label[1:]}")
                            # False branch
                            self.llvm_lines.append(f"{false_label[1:]}:")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))")
                            self.llvm_lines.append(f"  br label %{end_label[1:]}")
                            # End
                            self.llvm_lines.append(f"{end_label[1:]}:")
                    else:
                        self.llvm_lines.append(f"  ; unknown variable: {var_name}")
                elif part['type'] == 'math_expression':
                    # Handle mathematical expressions like (a + b)
                    expression = part['expression']
                    print(f"   🔧 Processing math expression: {expression}")

                    # Remove parentheses
                    if expression.startswith('(') and expression.endswith(')'):
                        expression = expression[1:-1]

                    # Evaluate the mathematical expression
                    result_var, result_type = self._evaluate_math_expression_for_print(expression)
                    if result_var:
                        if result_type == 'float':
                            # Convert float to double for printf
                            double_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {double_var} = fpext float {result_var} to double")
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double {double_var})")
                        else:
                            # Integer result
                            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 {result_var})")
                    else:
                        self.llvm_lines.append(f"  ; failed to evaluate math expression: {expression}")

            # Add newline at the end
            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))")
            return

    def _parse_string_concatenation(self, content):
        """Parse string concatenation expression into parts"""
        parts = []

        # Simple approach: split by ' + ' but handle quoted strings
        # First, find all quoted strings and replace them with placeholders
        quoted_strings = []
        temp_content = content

        # Extract quoted strings
        import re
        quote_pattern = r'"([^"]*)"'
        matches = re.finditer(quote_pattern, content)

        for i, match in enumerate(matches):
            quoted_strings.append(match.group(1))  # Store the content without quotes
            temp_content = temp_content.replace(match.group(0), f"__QUOTE_{i}__", 1)

        # Now split by ' + ', but be careful with parentheses
        raw_parts = []
        current_part = ""
        paren_depth = 0
        i = 0

        while i < len(temp_content):
            char = temp_content[i]

            if char == '(':
                paren_depth += 1
                current_part += char
            elif char == ')':
                paren_depth -= 1
                current_part += char
            elif char == '+' and paren_depth == 0:
                # Check if this is a concatenation operator (has spaces around it)
                if (i > 0 and i < len(temp_content) - 1 and
                    temp_content[i-1] == ' ' and temp_content[i+1] == ' '):
                    # This is a concatenation operator
                    if current_part.strip():
                        raw_parts.append(current_part.strip())
                    current_part = ""
                    i += 2  # Skip the ' + '
                    continue
                else:
                    # This is part of a mathematical expression
                    current_part += char
            else:
                current_part += char

            i += 1

        if current_part.strip():
            raw_parts.append(current_part.strip())

        for part in raw_parts:
            part = part.strip()
            if part.startswith('__QUOTE_') and part.endswith('__'):
                # This is a quoted string
                quote_index = int(part[8:-2])  # Extract index from __QUOTE_X__
                parts.append({
                    'type': 'string_literal',
                    'value': quoted_strings[quote_index]
                })
            elif part:
                # Check if this is a mathematical expression in parentheses
                if part.startswith('(') and part.endswith(')'):
                    # This is a mathematical expression like (a + b)
                    parts.append({
                        'type': 'math_expression',
                        'expression': part
                    })
                else:
                    # This is a variable
                    parts.append({
                        'type': 'variable',
                        'name': part
                    })

        return parts

    def _evaluate_math_expression_for_print(self, expression):
        """Evaluate mathematical expression for print statements"""
        print(f"   🔍 Evaluating math expression for print: {expression}")

        # Tokenize the expression
        tokens = self._tokenize_math_expression(expression)
        print(f"   🔍 Tokens: {tokens}")

        if len(tokens) == 1:
            # Simple case: just a variable or number
            token = tokens[0]
            if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
                # It's a number
                result_var = self.get_temp_var()
                self.llvm_lines.append(f"  {result_var} = add i32 0, {token}")
                return result_var, 'int'
            else:
                # It's a variable
                var_info = self._find_variable(token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                    return load_var, 'int'
                elif var_info and var_info['type'] == 'float':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load float, float* @global_{token}, align 4")
                    return load_var, 'float'
                else:
                    print(f"   ❌ Unknown variable in math expression: {token}")
                    return None, None
        elif len(tokens) == 3:
            # Simple binary operation: a + b, a * b, etc.
            left_token = tokens[0]
            operator = tokens[1]
            right_token = tokens[2]

            # Get left operand
            if left_token.isdigit() or (left_token.startswith('-') and left_token[1:].isdigit()):
                left_var = self.get_temp_var()
                self.llvm_lines.append(f"  {left_var} = add i32 0, {left_token}")
            else:
                var_info = self._find_variable(left_token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    left_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {left_var} = load i32, i32* @global_{left_token}, align 4")
                elif var_info and var_info['type'] == 'float':
                    left_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {left_var} = load float, float* @global_{left_token}, align 4")
                else:
                    print(f"   ❌ Unknown left operand: {left_token}")
                    return None

            # Get right operand
            if right_token.isdigit() or (right_token.startswith('-') and right_token[1:].isdigit()):
                right_var = self.get_temp_var()
                self.llvm_lines.append(f"  {right_var} = add i32 0, {right_token}")
            else:
                var_info = self._find_variable(right_token)
                if var_info and var_info['type'] in ['integer', 'int']:
                    right_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {right_var} = load i32, i32* @global_{right_token}, align 4")
                elif var_info and var_info['type'] == 'float':
                    right_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {right_var} = load float, float* @global_{right_token}, align 4")
                else:
                    print(f"   ❌ Unknown right operand: {right_token}")
                    return None

            # Determine operation type (int or float)
            left_var_info = self._find_variable(left_token) if not (left_token.isdigit() or (left_token.startswith('-') and left_token[1:].isdigit())) else None
            right_var_info = self._find_variable(right_token) if not (right_token.isdigit() or (right_token.startswith('-') and right_token[1:].isdigit())) else None

            is_float_operation = (left_var_info and left_var_info['type'] == 'float') or (right_var_info and right_var_info['type'] == 'float')

            # Perform operation
            result_var = self.get_temp_var()
            if is_float_operation:
                # Float operations
                if operator == '+':
                    self.llvm_lines.append(f"  {result_var} = fadd float {left_var}, {right_var}")
                elif operator == '-':
                    self.llvm_lines.append(f"  {result_var} = fsub float {left_var}, {right_var}")
                elif operator == '*':
                    self.llvm_lines.append(f"  {result_var} = fmul float {left_var}, {right_var}")
                elif operator == '/':
                    self.llvm_lines.append(f"  {result_var} = fdiv float {left_var}, {right_var}")
                else:
                    print(f"   ❌ Unsupported float operator: {operator}")
                    return None
            else:
                # Integer operations
                if operator == '+':
                    self.llvm_lines.append(f"  {result_var} = add i32 {left_var}, {right_var}")
                elif operator == '-':
                    self.llvm_lines.append(f"  {result_var} = sub i32 {left_var}, {right_var}")
                elif operator == '*':
                    self.llvm_lines.append(f"  {result_var} = mul i32 {left_var}, {right_var}")
                elif operator == '/':
                    self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_var}, {right_var}")
                elif operator == '%':
                    self.llvm_lines.append(f"  {result_var} = srem i32 {left_var}, {right_var}")
                else:
                    print(f"   ❌ Unknown operator: {operator}")
                    return None

            return result_var, ('float' if is_float_operation else 'int')
        else:
            print(f"   ❌ Complex math expressions not yet supported: {tokens}")
            return None, None

    def _tokenize_math_expression(self, expression):
        """Tokenize mathematical expression into operands and operators"""
        tokens = []
        current_token = ""

        for char in expression:
            if char in ['+', '-', '*', '/', '%', '(', ')']:
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                tokens.append(char)
            elif char == ' ':
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
            else:
                current_token += char

        if current_token.strip():
            tokens.append(current_token.strip())

        return tokens

    def _can_use_printf_directly(self, parts):
        """Check if we can use printf directly instead of string concatenation"""
        print(f"   🔍 Checking printf direct for {len(parts)} parts:")
        for i, part in enumerate(parts):
            print(f"      Part {i}: {part['type']} = {part.get('value', part.get('name', '?'))}")

        # Simple case: string + integer
        if len(parts) == 2:
            if (parts[0]['type'] == 'string_literal' and
                parts[1]['type'] == 'variable'):
                var_info = self._find_variable(parts[1]['name'])
                if var_info and var_info['type'] in ['integer', 'int']:
                    print(f"   ✅ Can use printf direct: string + {var_info['type']}")
                    return True
        print(f"   ❌ Cannot use printf direct")
        return False

    def _generate_printf_direct(self, parts):
        """Generate printf directly like C: printf("format", args...)"""
        # Create format string by replacing integer variables with %d
        format_str = ""
        args = []

        for part in parts:
            if part['type'] == 'string_literal':
                format_str += part['value']
            elif part['type'] == 'variable':
                var_info = self._find_variable(part['name'])
                if var_info and var_info['type'] in ['integer', 'int']:
                    format_str += "%d"
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{part['name']}, align 4")
                    args.append(f"i32 {load_var}")
                    print(f"      ✅ Added integer variable: {part['name']} -> %d, arg: {load_var}")
                elif var_info and var_info['type'] == 'string':
                    format_str += "%s"
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{part['name']}, align 8")
                    args.append(f"i8* {load_var}")

        # Add newline
        format_str += "\n"

        # Create format string constant (special handling for printf)
        escaped_format = self._escape_printf_format(format_str)
        format_len = len(escaped_format) + 1
        format_const = self.get_temp_var() + "_fmt"

        # Create format string
        self.llvm_lines.append(f"  {format_const} = alloca [{format_len} x i8], align 1")
        for i, char in enumerate(escaped_format):
            char_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {i}")
            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

        # Null terminator
        null_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{format_len} x i8], [{format_len} x i8]* {format_const}, i64 0, i64 {len(escaped_format)}")
        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

        # Cast and call printf
        format_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {format_ptr} = bitcast [{format_len} x i8]* {format_const} to i8*")

        if args:
            args_str = ", " + ", ".join(args)
        else:
            args_str = ""

        self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {format_ptr}{args_str})")

    def _generate_import(self, stmt):
        """Generate code for import statement"""
        content = stmt['content'][7:].strip()  # Remove 'import '
        module_name = content

        self.llvm_lines.append(f"  ; import {module_name}")

        # Check if module already imported
        if module_name in self.imported_modules:
            print(f"   Module {module_name} already imported")
            return

        # Try to read the module file
        module_file = f"{module_name}.dolet"
        try:
            print(f"   📦 Importing module: {module_name}")
            print(f"   📁 Reading file: {module_file}")

            with open(module_file, 'r', encoding='utf-8') as f:
                module_content = f.read()

            # Parse the module content
            module_statements = self._parse_module_content(module_content)

            # Store module info
            self.imported_modules[module_name] = {
                'file': module_file,
                'content': module_content,
                'statements': module_statements
            }

            # Process module functions
            self._process_module_functions(module_name, module_statements)

            print(f"   ✅ Successfully imported {module_name}")

        except FileNotFoundError:
            print(f"   ❌ Error: Module file {module_file} not found")
        except Exception as e:
            print(f"   ❌ Error importing {module_name}: {e}")

    def _parse_module_content(self, content):
        """Parse module content into statements"""
        lines = content.strip().split('\n')
        statements = []

        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.startswith('#'):
                stmt_type = self._get_statement_type(line)
                statements.append({
                    'type': stmt_type,
                    'content': line,
                    'line': line_num,
                    'line_number': line_num  # Add line_number for compatibility
                })

        return statements

    def _process_module_functions(self, module_name, statements):
        """Process functions from imported module"""
        current_function = None
        function_body = []

        for stmt in statements:
            if stmt['type'] == 'function':
                # Start of function definition
                if current_function:
                    # Save previous function
                    self._save_imported_function(module_name, current_function, function_body)

                # Parse function signature
                func_line = stmt['content'][4:].strip()  # Remove 'fun '
                if '(' in func_line:
                    func_name = func_line[:func_line.index('(')]
                    params_str = func_line[func_line.index('(')+1:func_line.rindex(')')]
                    params = [p.strip() for p in params_str.split(',') if p.strip()]
                else:
                    func_name = func_line
                    params = []

                current_function = {
                    'name': func_name,
                    'params': params,
                    'module': module_name
                }
                function_body = []

            elif stmt['type'] == 'end' and current_function:
                # End of function definition
                self._save_imported_function(module_name, current_function, function_body)
                current_function = None
                function_body = []

            elif current_function:
                # Function body statement
                function_body.append(stmt)

        # Save last function if exists
        if current_function:
            self._save_imported_function(module_name, current_function, function_body)

    def _save_imported_function(self, module_name, func_info, body):
        """Save imported function to symbol table"""
        func_name = func_info['name']
        full_name = f"{module_name}_{func_name}"

        self.imported_functions[func_name] = {
            'name': func_name,
            'full_name': full_name,
            'params': func_info['params'],
            'body': body,
            'module': module_name
        }

        print(f"   📝 Found function: {func_name}({', '.join(func_info['params'])})")

    def _generate_module_functions(self, module_name, statements):
        """Generate LLVM IR for functions in imported module"""
        print(f"   🔧 Generating LLVM IR for {module_name} functions...")

        # Process statements to generate function definitions
        for stmt in statements:
            if stmt['type'] == 'function':
                self._generate_statement(stmt)
            elif stmt['type'] == 'end' and hasattr(self, 'current_function') and self.current_function:
                self._generate_statement(stmt)
            elif hasattr(self, 'current_function') and self.current_function:
                # Inside function body
                self._generate_statement(stmt)

    def _get_statement_type(self, line):
        """Get statement type for imported module parsing"""
        line = line.strip()
        if line.startswith('print '):
            return 'print'
        elif line.startswith('set '):
            return 'set'
        elif line.startswith('if '):
            return 'if'
        elif line.startswith('else'):
            return 'else'
        elif line == 'end':
            return 'end'
        elif line.startswith('while '):
            return 'while'
        elif line.startswith('for '):
            return 'for'
        elif line.startswith('fun '):
            return 'function'
        elif line.startswith('return '):
            return 'return'
        elif line.startswith('import '):
            return 'import'
        elif line.startswith('write('):
            return 'write'
        elif line.startswith('system('):
            return 'system'
        elif line.startswith('argc('):
            return 'argc'
        elif line.startswith('argv('):
            return 'argv'
        elif line.startswith('read_file('):
            return 'read_file'
        elif line.startswith('file_exists('):
            return 'file_exists'
        elif '(' in line and line.endswith(')'):
            return 'function_call'
        else:
            return 'unknown'

    def _generate_function_definition(self, stmt):
        """Generate LLVM IR for function definition"""
        content = stmt['content'][4:].strip()  # Remove 'fun '

        # Parse function signature
        if '(' in content:
            func_name = content[:content.index('(')]
            params_str = content[content.index('(')+1:content.rindex(')')]
            params = [p.strip() for p in params_str.split(',') if p.strip()]
        else:
            func_name = content
            params = []

        print(f"   🔧 Generating function definition: {func_name}({', '.join(params)})")

        # Generate function signature
        param_types = []
        param_names = []
        for param in params:
            # Check if this is a math function that should use integer parameters
            if func_name in ['add_numbers', 'multiply_numbers', 'square_number', 'factorial', 'power', 'check_number', 'test_params'] or any(math_word in func_name.lower() for math_word in ['add', 'multiply', 'square', 'factorial', 'power', 'math', 'check', 'number', 'calc', 'compute', 'test', 'param']):
                param_types.append("i32")  # Integer for math functions
                print(f"   🔢 Using i32 parameter for {func_name}: {param}")
            else:
                param_types.append("i8*")  # String for other functions
                print(f"   📝 Using i8* parameter for {func_name}: {param}")
            param_names.append(f"%{param}")

        if param_types:
            param_signature = ", ".join([f"{ptype} {pname}" for ptype, pname in zip(param_types, param_names)])
        else:
            param_signature = ""

        # Generate function header (will be updated later with correct return type)
        self.llvm_lines.append(f"")
        self.llvm_lines.append(f"; Function: {func_name}")
        self.function_header_line = len(self.llvm_lines)  # Remember where to update return type
        self.llvm_lines.append(f"define void @{func_name}({param_signature}) {{")
        self.llvm_lines.append(f"entry:")

        # Store function info for later processing
        if not hasattr(self, 'current_function'):
            self.current_function = None
            self.function_body = []

        self.current_function = {
            'name': func_name,
            'params': params,
            'param_types': param_types,
            'param_names': param_names,
            'return_type': 'void'  # Default, will be updated by return statements
        }
        self.function_body = []

        # Find the end of this function
        end_line = stmt['line_number']
        for s in self.statements:
            if s['line_number'] > stmt['line_number'] and s['type'] == 'end':
                end_line = s['line_number']
                break

        return end_line

    def _generate_complete_function(self, stmt):
        """Generate complete function definition with body processing"""
        content = stmt['content'][4:].strip()  # Remove 'fun '

        # Parse function signature
        if '(' in content:
            func_name = content[:content.index('(')]
            params_str = content[content.index('(')+1:content.rindex(')')]
            params = [p.strip() for p in params_str.split(',') if p.strip()]
        else:
            func_name = content
            params = []

        print(f"   🔧 Generating complete function: {func_name}({', '.join(params)})")

        # Generate function signature
        param_types = []
        param_names = []
        for param in params:
            # Check if this is a math function that should use integer parameters
            if func_name in ['add_numbers', 'multiply_numbers', 'square_number', 'factorial', 'power', 'check_number', 'test_params'] or any(math_word in func_name.lower() for math_word in ['add', 'multiply', 'square', 'factorial', 'power', 'math', 'check', 'number', 'calc', 'compute', 'test', 'param']):
                param_types.append("i32")  # Integer for math functions
                print(f"   🔢 Using i32 parameter for {func_name}: {param}")
            else:
                param_types.append("i8*")  # String for other functions
                print(f"   📝 Using i8* parameter for {func_name}: {param}")
            param_names.append(f"%{param}")

        if param_types:
            param_signature = ", ".join([f"{ptype} {pname}" for ptype, pname in zip(param_types, param_names)])
        else:
            param_signature = ""

        # Store function info
        self.current_function = {
            'name': func_name,
            'params': params,
            'param_types': param_types,
            'param_names': param_names,
            'return_type': 'void'  # Default, will be updated by return statements
        }

        # Register function in local functions registry
        self.local_functions[func_name] = {
            'name': func_name,
            'params': params,
            'param_count': len(params),
            'return_type': 'void'  # Will be updated later
        }

        # Find function body statements using Python-style indentation
        function_body = []
        end_line = stmt['line_number']
        function_indentation = len(stmt['content']) - len(stmt['content'].lstrip())

        for s in self.statements:
            if s['line_number'] > stmt['line_number']:
                statement_indentation = len(s['content']) - len(s['content'].lstrip())

                # If statement has more indentation than function, it belongs to function body
                if statement_indentation > function_indentation:
                    function_body.append(s)
                    end_line = s['line_number']
                elif statement_indentation <= function_indentation and s['type'] not in ['end', '']:
                    # Statement at same or less indentation means function has ended
                    break

        # Generate function header
        self.llvm_lines.append(f"")
        self.llvm_lines.append(f"; Function: {func_name}")
        function_header_line = len(self.llvm_lines)
        self.llvm_lines.append(f"define void @{func_name}({param_signature}) {{")
        self.llvm_lines.append(f"entry:")

        # Process function body statements
        for body_stmt in function_body:
            self._generate_function_body_statement(body_stmt)

        # Update function signature with correct return type
        return_type = self.current_function.get('return_type', 'void')
        if return_type == 'integer':
            llvm_return_type = 'i32'
        elif return_type == 'boolean':
            llvm_return_type = 'i1'
        elif return_type == 'string':
            llvm_return_type = 'i8*'
        else:
            llvm_return_type = 'void'

        # Update local function registry with return type
        if func_name in self.local_functions:
            self.local_functions[func_name]['return_type'] = return_type

        # Update the function header line
        old_header = self.llvm_lines[function_header_line]
        new_header = old_header.replace('define void @', f'define {llvm_return_type} @')
        self.llvm_lines[function_header_line] = new_header

        # Add default return if no explicit return was found
        if return_type == 'void':
            self.llvm_lines.append(f"  ret void")
        elif return_type == 'integer':
            self.llvm_lines.append(f"  ret i32 0  ; default return")
        elif return_type == 'boolean':
            self.llvm_lines.append(f"  ret i1 0  ; default return")
        elif return_type == 'string':
            self.llvm_lines.append(f"  ret i8* null  ; default return")

        self.llvm_lines.append(f"}}")

        # Clear current function
        self.current_function = None

        return end_line

    def _generate_function_call(self, stmt):
        """Generate LLVM IR for function call"""
        content = stmt['content'].strip()  # Remove leading/trailing whitespace

        # Parse function call
        if '(' in content and content.endswith(')'):
            func_name = content[:content.index('(')].strip()  # Remove whitespace from function name
            # Handle main() calls - skip if we have a main function (will be called by wrapper)
            if func_name == 'main':
                if hasattr(self, 'has_main_function') and self.has_main_function:
                    # Skip main() calls in global scope when we have a main function
                    self.llvm_lines.append(f"  ; skipping main() call - will be handled by wrapper")
                    return
                else:
                    func_name = 'dolet_main'
            args_str = content[content.index('(')+1:-1].strip()

            # Parse arguments
            args = []
            if args_str:
                # Simple argument parsing (for now, assume string literals and variables)
                raw_args = [arg.strip() for arg in args_str.split(',')]
                for arg in raw_args:
                    if arg.startswith('"') and arg.endswith('"'):
                        # String literal argument
                        args.append({
                            'type': 'string_literal',
                            'value': arg[1:-1]  # Remove quotes
                        })
                    else:
                        # Variable argument
                        args.append({
                            'type': 'variable',
                            'name': arg
                        })

            print(f"   🔧 Generating function call: {func_name}({len(args)} args)")

            # Check for built-in functions first
            if func_name == 'length':
                self._generate_length_function(args)
            elif func_name == 'write':
                self._generate_write_function(args)
            elif func_name == 'read_file':
                self._generate_read_file_function(args)
            elif func_name == 'write_file':
                self._generate_write_file_function(args)
            elif func_name == 'argc':
                # argc() function call
                self.llvm_lines.append(f"  ; argc() function call")
                # For now, just print a message since argc() is typically used in assignments
                self.llvm_lines.append(f"  ; argc() should be used in assignment context")
            elif func_name == 'argv':
                # argv(index) function call
                if args:
                    index_arg = args[0]
                    if index_arg['type'] == 'integer_literal':
                        index = index_arg['value']
                        self.llvm_lines.append(f"  ; argv({index}) function call")
                        self.llvm_lines.append(f"  ; argv() should be used in assignment context")
                    else:
                        self.llvm_lines.append(f"  ; argv() with variable index not implemented")
                else:
                    self.llvm_lines.append(f"  ; argv() requires an index argument")
            elif func_name == 'file_exists':
                # file_exists(filename) function call
                if args:
                    filename_arg = args[0]
                    self.llvm_lines.append(f"  ; file_exists() function call")
                    self.llvm_lines.append(f"  ; file_exists() should be used in assignment context")
                else:
                    self.llvm_lines.append(f"  ; file_exists() requires a filename argument")
            elif func_name == 'system':
                # system(command) function call
                if args:
                    command_arg = args[0]
                    self.llvm_lines.append(f"  ; system() function call")
                    self.llvm_lines.append(f"  ; system() should be used in assignment context")
                else:
                    self.llvm_lines.append(f"  ; system() requires a command argument")
            elif func_name == 'print_header':
                # Special function for printing header
                self._generate_print_header_function()
            # Check if function exists in imported or local functions
            elif func_name in self.imported_functions:
                self._generate_imported_function_call(func_name, args)
            elif hasattr(self, 'local_functions') and func_name in self.local_functions:
                self._generate_local_function_call(func_name, args)
            else:
                # Unknown function - generate comment
                print(f"   🔍 DEBUG: Available local functions: {list(self.local_functions.keys()) if hasattr(self, 'local_functions') else 'None'}")
                print(f"   🔍 DEBUG: Looking for function: {func_name}")
                self.llvm_lines.append(f"  ; unknown function call: {content}")
                print(f"   ⚠️  Unknown function: {func_name}")
        else:
            # Invalid function call syntax
            self.llvm_lines.append(f"  ; invalid function call: {content}")

    def _generate_length_function(self, args):
        """Generate LLVM IR for length() function"""
        if len(args) == 1:
            arg = args[0]
            if arg['type'] == 'variable':
                var_name = arg['name']
                # Check if it's a string variable
                var_info = self._get_variable_info(var_name)
                if var_info and var_info['type'] == 'string':
                    temp_var = self._get_temp_var()
                    self.llvm_lines.extend([
                        f"  ; Calculate length of string variable: {var_name}",
                        f"  %{var_name}_ptr = load i8*, i8** @global_{var_name}, align 8",
                        f"  {temp_var} = call i64 @strlen(i8* %{var_name}_ptr)",
                        f"  ; length({var_name}) = {temp_var}"
                    ])
                    return temp_var
                else:
                    self.llvm_lines.append(f"  ; error: {var_name} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: length() argument must be a variable")
        else:
            self.llvm_lines.append(f"  ; error: length() requires exactly 1 argument")
        return None

    def _generate_write_function(self, args):
        """Generate LLVM IR for write() function"""
        if len(args) == 2:
            filename_arg = args[0]
            content_arg = args[1]
            self.llvm_lines.extend([
                f"  ; write() function not fully implemented",
                f"  ; filename: {filename_arg}",
                f"  ; content: {content_arg}",
                f"  ; TODO: Implement file writing"
            ])
        else:
            self.llvm_lines.append(f"  ; error: write() requires exactly 2 arguments")

    def _generate_read_file_function(self, args):
        """Generate LLVM IR for read_file() function"""
        if len(args) == 1:
            filename_arg = args[0]
            self.llvm_lines.extend([
                f"  ; read_file() function",
                f"  ; filename: {filename_arg}",
                f"  ; Returns file content as string",
                f"  ; TODO: Implement actual file reading"
            ])
        else:
            self.llvm_lines.append(f"  ; error: read_file() requires exactly 1 argument")

    def _generate_write_file_function(self, args):
        """Generate LLVM IR for write_file() function"""
        if len(args) == 2:
            filename_arg = args[0]
            content_arg = args[1]
            self.llvm_lines.extend([
                f"  ; write_file() function",
                f"  ; filename: {filename_arg}",
                f"  ; content: {content_arg}",
                f"  ; TODO: Implement actual file writing"
            ])
        else:
            self.llvm_lines.append(f"  ; error: write_file() requires exactly 2 arguments")

    def _generate_print_header_function(self):
        """Generate LLVM IR for print_header() function"""
        self.llvm_lines.append(f"  ; print_header() function")

        # Find the header string in string literals
        header_text = "Converting Dolet source to LLVM IR"
        header_index = self._find_string_index(header_text)

        if header_index >= 0:
            string_length = calculate_llvm_string_length(header_text)
            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([{string_length} x i8], [{string_length} x i8]* @.str_{header_index}, i32 0, i32 0))")
            print(f"   ✅ Generated print_header() function call")
        else:
            # Create inline header if not found in string literals
            self.llvm_lines.append(f"  ; Header not found in string literals, creating inline")
            header_len = len(header_text.encode('utf-8')) + 2  # +2 for \n\0
            header_const = self.get_temp_var() + "_header"

            self.llvm_lines.append(f"  {header_const} = alloca [{header_len} x i8], align 1")

            # Store header chars
            for i, char in enumerate(header_text):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{header_len} x i8], [{header_len} x i8]* {header_const}, i64 0, i64 {i}")
                if char == '→':
                    # Handle UTF-8 arrow character (3 bytes: 0xE2, 0x86, 0x92)
                    self.llvm_lines.append(f"  store i8 -30, i8* {char_ptr}, align 1")  # 0xE2
                    char_ptr2 = self.get_temp_var()
                    char_ptr3 = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr2} = getelementptr inbounds [{header_len} x i8], [{header_len} x i8]* {header_const}, i64 0, i64 {i+1}")
                    self.llvm_lines.append(f"  store i8 -122, i8* {char_ptr2}, align 1")  # 0x86
                    self.llvm_lines.append(f"  {char_ptr3} = getelementptr inbounds [{header_len} x i8], [{header_len} x i8]* {header_const}, i64 0, i64 {i+2}")
                    self.llvm_lines.append(f"  store i8 -110, i8* {char_ptr3}, align 1")  # 0x92
                else:
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

            # Add newline and null terminator
            newline_ptr = self.get_temp_var()
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {newline_ptr} = getelementptr inbounds [{header_len} x i8], [{header_len} x i8]* {header_const}, i64 0, i64 {len(header_text.encode('utf-8'))}")
            self.llvm_lines.append(f"  store i8 10, i8* {newline_ptr}, align 1")  # \n
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{header_len} x i8], [{header_len} x i8]* {header_const}, i64 0, i64 {len(header_text.encode('utf-8')) + 1}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")  # \0

            # Cast and call printf
            header_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {header_ptr} = bitcast [{header_len} x i8]* {header_const} to i8*")
            self.llvm_lines.append(f"  call i32 (i8*, ...) @printf(i8* {header_ptr})")
            print(f"   ✅ Generated inline print_header() function call")

    def _generate_imported_function_call(self, func_name, args):
        """Generate LLVM IR for calling an imported function"""
        func_info = self.imported_functions[func_name]
        expected_params = func_info['params']

        self.llvm_lines.append(f"  ; call {func_name}({', '.join([arg.get('value', arg.get('name', '?')) for arg in args])})")

        # Prepare arguments
        llvm_args = []
        for i, arg in enumerate(args):
            if arg['type'] == 'string_literal':
                # Create string literal for argument
                arg_value = arg['value']
                escaped_text = self._escape_printf_format(arg_value)
                string_len = len(escaped_text) + 1

                # Create string constant
                const_name = self.get_temp_var() + "_arg"
                self.llvm_lines.append(f"  {const_name} = alloca [{string_len} x i8], align 1")

                # Store string characters
                for j, char in enumerate(escaped_text):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {j}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {len(escaped_text)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Cast to i8*
                arg_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {arg_ptr} = bitcast [{string_len} x i8]* {const_name} to i8*")
                llvm_args.append(f"i8* {arg_ptr}")

            elif arg['type'] == 'variable':
                # Load variable
                var_name = arg['name']
                var_info = self._find_variable(var_name)

                if var_info and var_info['type'] == 'string':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_name}, align 8")
                    llvm_args.append(f"i8* {load_var}")
                elif var_info and var_info['type'] == 'integer':
                    # Convert integer to string for now (since functions expect i8*)
                    load_var = self.get_temp_var()
                    str_buffer = self.get_temp_var()
                    str_buffer_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name}, align 4")
                    self.llvm_lines.append(f"  {str_buffer} = alloca [12 x i8], align 1")
                    self.llvm_lines.append(f"  {str_buffer_ptr} = getelementptr inbounds [12 x i8], [12 x i8]* {str_buffer}, i64 0, i64 0")
                    self._generate_int_to_string(load_var, str_buffer_ptr)
                    llvm_args.append(f"i8* {str_buffer_ptr}")
                else:
                    # Unknown variable type
                    llvm_args.append("i8* null")

        # Generate function call
        if llvm_args:
            args_str = ", " + ", ".join(llvm_args)
        else:
            args_str = ""

        self.llvm_lines.append(f"  call void @{func_name}({args_str[2:] if args_str else ''})")

    def _generate_local_function_call(self, func_name, args):
        """Generate LLVM IR for calling a local function"""
        if not hasattr(self, 'local_functions') or func_name not in self.local_functions:
            self.llvm_lines.append(f"  ; local function not found: {func_name}")
            return

        func_info = self.local_functions[func_name]

        self.llvm_lines.append(f"  ; call local function {func_name}({', '.join([arg.get('value', arg.get('name', '?')) for arg in args])})")

        # Prepare arguments for local function
        llvm_args = []
        for arg in args:
            if arg['type'] == 'string_literal':
                # String literal argument - create a string constant
                string_text = arg['value']
                string_index = self._find_string_index(string_text)
                if string_index >= 0:
                    string_const = f"@.str_{string_index}"
                    string_len = len(string_text) + 1
                    llvm_args.append(f"i8* getelementptr inbounds ([{string_len} x i8], [{string_len} x i8]* {string_const}, i32 0, i32 0)")
                else:
                    # Create a new string constant
                    string_index = len(self.string_constants)
                    string_const = f"@str_dyn_{string_index}"
                    self.string_constants[string_text] = string_const
                    string_len = len(string_text) + 1
                    llvm_args.append(f"i8* getelementptr inbounds ([{string_len} x i8], [{string_len} x i8]* {string_const}, i32 0, i32 0)")
            elif arg['type'] == 'variable':
                # Variable argument
                var_name = arg['name']
                if var_name.isdigit():
                    # Numeric literal
                    llvm_args.append(f"i32 {var_name}")
                else:
                    # Variable reference
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] in ['integer', 'int']:
                        load_var = self.get_temp_var()
                        self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name}, align 4")
                        llvm_args.append(f"i32 {load_var}")
                        print(f"   ✅ Loading variable for function call: {var_name} -> {load_var}")
                    else:
                        print(f"   ⚠️ Variable not found or wrong type for function call: {var_name}, info: {var_info}")
                        llvm_args.append("i32 0")  # Default

        # Generate function call
        if llvm_args:
            args_str = ", ".join(llvm_args)
            self.llvm_lines.append(f"  call void @{func_name}({args_str})")
        else:
            self.llvm_lines.append(f"  call void @{func_name}()")

    def _generate_set(self, stmt):
        """Generate variable assignment"""
        # Remove 'set ' prefix from content
        full_content = stmt['content'].strip()
        if full_content.startswith('set '):
            content = full_content[4:].strip()
        else:
            content = full_content
        if '=' not in content:
            return

        var_name = content.split('=')[0].strip()
        var_value = content.split('=', 1)[1].strip()

        var_info = self._find_variable(var_name)
        if not var_info:
            return

        self.llvm_lines.append(f"  ; set {var_name} = {var_value[:50]}{'...' if len(var_value) > 50 else ''}")
        
        # Handle different assignment types
        if var_value == "get_args()":
            self._generate_get_args(var_name)
        elif var_value.startswith("length("):
            self._generate_length(var_name, var_value)
        elif var_value.startswith("args["):
            self._generate_args_access(var_name, var_value)
        elif var_value.startswith("read_file("):
            self._generate_read_file(var_name, var_value)
        elif var_value.startswith("file_exists("):
            self._generate_file_exists_assign(var_name, var_value)
        elif var_value.startswith("system("):
            self._generate_system_assign(var_name, var_value)
        elif var_value.startswith("argc("):
            self._generate_argc_assign(var_name)
        elif var_value.startswith("argv("):
            self._generate_argv_assign(var_name, var_value)
        elif "+" in var_value and var_info['type'] == 'string':
            self._generate_string_concat_assign(var_name, var_value)
        elif self._is_mathematical_expression(var_value) and var_info['type'] in ['integer', 'int']:
            self._generate_math_assign(var_name, var_value)
        elif self._is_comparison_expression(var_value) and var_info['type'] == 'boolean':
            self._generate_comparison_assign(var_name, var_value)
        elif var_value.lower() in ['true', 'false'] and var_info['type'] == 'boolean':
            self._generate_boolean_assign(var_name, var_value)
        elif var_value.startswith('contains(') and var_value.endswith(')') and var_info['type'] == 'boolean':
            self._generate_contains_assign(var_name, var_value)
        elif var_value.startswith('substring(') and var_value.endswith(')') and var_info['type'] == 'string':
            print(f"   ✂️ Detected substring assignment: {var_name} = {var_value}")
            self._generate_substring_assign(var_name, var_value)
        elif '(' in var_value and var_value.endswith(')'):
            # Function call assignment
            self._generate_function_call_assign(var_name, var_value)
        elif var_info['type'] == 'string' and var_value.startswith('"'):
            # Simple string assignment with escape sequence processing
            raw_string = var_value[1:-1]

            # Process escape sequences
            processed_string = ""
            i = 0
            while i < len(raw_string):
                if i < len(raw_string) - 1 and raw_string[i] == '\\':
                    if raw_string[i+1] == 'n':
                        processed_string += '\n'
                        i += 2
                    elif raw_string[i+1] == 't':
                        processed_string += '\t'
                        i += 2
                    elif raw_string[i+1] == '\\':
                        processed_string += '\\'
                        i += 2
                    elif raw_string[i+1] == '"':
                        processed_string += '"'
                        i += 2
                    else:
                        processed_string += raw_string[i]
                        i += 1
                else:
                    processed_string += raw_string[i]
                    i += 1

            string_len = len(processed_string) + 1
            const_name = self.get_temp_var() + "_str"
            self.llvm_lines.append(f"  {const_name} = alloca [{string_len} x i8], align 1")

            # Store string chars
            for i, char in enumerate(processed_string):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
            # Null terminator
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {len(processed_string)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
            
            # Store pointer
            cast_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {cast_ptr} = bitcast [{string_len} x i8]* {const_name} to i8*")
            self.llvm_lines.append(f"  store i8* {cast_ptr}, i8** @global_{var_name}, align 8")
        elif var_info['type'] in ['integer', 'int']:
            self.llvm_lines.append(f"  store i32 {var_value}, i32* @global_{var_name}, align 4")
        elif var_info['type'] == 'float':
            try:
                float_val = float(var_value)
                # Use bitcast from i32 to float for LLVM
                import struct
                packed = struct.pack('f', float_val)
                unpacked = struct.unpack('I', packed)[0]
                temp_var = self.get_temp_var()
                self.llvm_lines.append(f"  {temp_var} = bitcast i32 {unpacked} to float")
                self.llvm_lines.append(f"  store float {temp_var}, float* @global_{var_name}, align 4")
            except ValueError:
                self.llvm_lines.append(f"  ; float assignment from expression: {var_name} = {var_value}")
        elif var_info['type'] == 'double':
            try:
                double_val = float(var_value)
                # Use bitcast from i64 to double for LLVM
                import struct
                packed = struct.pack('d', double_val)
                unpacked = struct.unpack('Q', packed)[0]
                temp_var = self.get_temp_var()
                self.llvm_lines.append(f"  {temp_var} = bitcast i64 {unpacked} to double")
                self.llvm_lines.append(f"  store double {temp_var}, double* @global_{var_name}, align 8")
            except ValueError:
                self.llvm_lines.append(f"  ; double assignment from expression: {var_name} = {var_value}")
        elif var_info['type'] in ['bool', 'boolean']:
            if var_value.lower() == 'true':
                self.llvm_lines.append(f"  store i1 1, i1* @global_{var_name}, align 1")
            elif var_value.lower() == 'false':
                self.llvm_lines.append(f"  store i1 0, i1* @global_{var_name}, align 1")
            else:
                self.llvm_lines.append(f"  ; boolean assignment from expression: {var_name} = {var_value}")
        elif var_info['type'] == 'char':
            if var_value.startswith("'") and var_value.endswith("'") and len(var_value) == 3:
                char_val = ord(var_value[1])
                self.llvm_lines.append(f"  store i8 {char_val}, i8* @global_{var_name}, align 1")
            else:
                self.llvm_lines.append(f"  ; char assignment from expression: {var_name} = {var_value}")
            
    def _generate_get_args(self, var_name):
        """Generate code for get_args()"""
        argc_load = self.get_temp_var()
        argv_load = self.get_temp_var()
        
        self.llvm_lines.append(f"  {argc_load} = load i32, i32* %argc_ptr, align 4")
        self.llvm_lines.append(f"  {argv_load} = load i8**, i8*** %argv_ptr, align 8")
        
        # For simplicity, store argv pointer
        self.llvm_lines.append(f"  store i8** {argv_load}, i8*** @global_{var_name}, align 8")
        
    def _generate_length(self, var_name, expr):
        """Generate code for length() function"""
        arg = expr[7:-1].strip()

        self.llvm_lines.append(f"  ; {var_name} = length({arg})")

        if arg == "args":
            argc_load = self.get_temp_var()
            args_count = self.get_temp_var()
            self.llvm_lines.append(f"  {argc_load} = load i32, i32* %argc_ptr, align 4")
            self.llvm_lines.append(f"  {args_count} = sub i32 {argc_load}, 1  ; Subtract 1 for program name")
            self.llvm_lines.append(f"  store i32 {args_count}, i32* @global_{var_name}, align 4")
            print(f"   ✅ Generated length(args) assignment for {var_name}")
        elif arg.startswith('"') and arg.endswith('"'):
            # String literal
            string_val = arg[1:-1]
            string_len = len(string_val.encode('utf-8'))
            self.llvm_lines.append(f"  store i32 {string_len}, i32* @global_{var_name}, align 4")
            print(f"   ✅ Generated length(\"{string_val}\") assignment for {var_name}")
        else:
            # String variable - generate strlen call
            var_info = self._find_variable(arg)
            if var_info and var_info['type'] == 'string':
                str_ptr = self.get_temp_var()
                len_result = self.get_temp_var()
                len_i32 = self.get_temp_var()
                self.llvm_lines.append(f"  {str_ptr} = load i8*, i8** @global_{arg}, align 8")
                self.llvm_lines.append(f"  {len_result} = call i64 @strlen(i8* {str_ptr})")
                self.llvm_lines.append(f"  {len_i32} = trunc i64 {len_result} to i32")
                self.llvm_lines.append(f"  store i32 {len_i32}, i32* @global_{var_name}, align 4")
                print(f"   ✅ Generated length({arg}) assignment for {var_name}")
            else:
                self.llvm_lines.append(f"  ; TODO: length() for unknown variable type: {arg}")
                self.llvm_lines.append(f"  store i32 0, i32* @global_{var_name}, align 4")

    def _generate_length_call(self, arg):
        """Generate code for length() function call and return the result variable"""
        self.llvm_lines.append(f"  ; length({arg}) call")

        if arg == "args":
            argc_load = self.get_temp_var()
            args_count = self.get_temp_var()
            self.llvm_lines.append(f"  {argc_load} = load i32, i32* %argc_ptr, align 4")
            self.llvm_lines.append(f"  {args_count} = sub i32 {argc_load}, 1  ; Subtract 1 for program name")
            return args_count
        elif arg.startswith('"') and arg.endswith('"'):
            # String literal
            string_val = arg[1:-1]
            string_len = len(string_val.encode('utf-8'))
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {result_var} = add i32 0, {string_len}")
            return result_var
        else:
            # String variable - generate strlen call
            var_info = self._find_variable(arg)
            if var_info and var_info['type'] == 'string':
                str_ptr = self.get_temp_var()
                len_result = self.get_temp_var()
                len_i32 = self.get_temp_var()
                self.llvm_lines.append(f"  {str_ptr} = load i8*, i8** @global_{arg}, align 8")
                self.llvm_lines.append(f"  {len_result} = call i64 @strlen(i8* {str_ptr})")
                self.llvm_lines.append(f"  {len_i32} = trunc i64 {len_result} to i32")
                return len_i32
            else:
                result_var = self.get_temp_var()
                self.llvm_lines.append(f"  {result_var} = add i32 0, 0  ; unknown variable type")
                return result_var
            
    def _generate_args_access(self, var_name, expr):
        """Generate code for args[index] access"""
        index_str = expr[5:-1]
        if index_str.isdigit():
            index = int(index_str) + 1  # +1 for program name
            argv_load = self.get_temp_var()
            arg_ptr = self.get_temp_var()
            arg_str = self.get_temp_var()
            
            self.llvm_lines.append(f"  {argv_load} = load i8**, i8*** %argv_ptr, align 8")
            self.llvm_lines.append(f"  {arg_ptr} = getelementptr inbounds i8*, i8** {argv_load}, i64 {index}")
            self.llvm_lines.append(f"  {arg_str} = load i8*, i8** {arg_ptr}, align 8")
            self.llvm_lines.append(f"  store i8* {arg_str}, i8** @global_{var_name}, align 8")
            
    def _generate_read_file(self, var_name, expr):
        """Generate code for read_file() function"""
        filename_arg = expr[10:-1].strip()
        
        self.llvm_lines.append(f"  ; {var_name} = read_file({filename_arg})")
        
        if filename_arg.startswith('"') and filename_arg.endswith('"'):
            # String literal filename
            filename = filename_arg[1:-1]
            fname_len = len(filename) + 1
            fname_const = self.get_temp_var() + "_fname"
            self.llvm_lines.append(f"  {fname_const} = alloca [{fname_len} x i8], align 1")
            
            # Store filename chars
            for i, char in enumerate(filename):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{fname_len} x i8], [{fname_len} x i8]* {fname_const}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
            
            # Null terminator
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{fname_len} x i8], [{fname_len} x i8]* {fname_const}, i64 0, i64 {len(filename)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
            
            fname_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_ptr} = bitcast [{fname_len} x i8]* {fname_const} to i8*")
        else:
            # Variable filename
            fname_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_ptr} = load i8*, i8** @global_{filename_arg}, align 8")
        
        # Open file
        file_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {file_ptr} = call i8* @fopen(i8* {fname_ptr}, i8* getelementptr inbounds ([2 x i8], [2 x i8]* @read_mode, i32 0, i32 0))")
        
        # Check if file opened successfully
        null_check = self.get_temp_var()
        success_label = self.get_label("file_read_success")
        fail_label = self.get_label("file_read_fail")
        end_label = self.get_label("file_read_end")

        self.llvm_lines.append(f"  {null_check} = icmp ne i8* {file_ptr}, null")
        self.llvm_lines.append(f"  br i1 {null_check}, label %{success_label}, label %{fail_label}")

        # Success path: Read file
        self.llvm_lines.append(f"{success_label}:")

        # Get file size using fseek/ftell
        self.llvm_lines.append(f"  call i32 @fseek(i8* {file_ptr}, i64 0, i32 2)")  # SEEK_END
        file_size = self.get_temp_var()
        self.llvm_lines.append(f"  {file_size} = call i64 @ftell(i8* {file_ptr})")
        self.llvm_lines.append(f"  call i32 @fseek(i8* {file_ptr}, i64 0, i32 0)")  # SEEK_SET

        # Allocate buffer based on file size + 1 for null terminator
        buffer_size = self.get_temp_var()
        buffer = self.get_temp_var()
        self.llvm_lines.append(f"  {buffer_size} = add i64 {file_size}, 1")
        self.llvm_lines.append(f"  {buffer} = call i8* @malloc(i64 {buffer_size})")

        # Read file content
        bytes_read = self.get_temp_var()
        self.llvm_lines.append(f"  {bytes_read} = call i64 @fread(i8* {buffer}, i64 1, i64 {file_size}, i8* {file_ptr})")

        # Null terminate the buffer
        null_pos = self.get_temp_var()
        self.llvm_lines.append(f"  {null_pos} = getelementptr inbounds i8, i8* {buffer}, i64 {file_size}")
        self.llvm_lines.append(f"  store i8 0, i8* {null_pos}, align 1")

        # Close file
        self.llvm_lines.append(f"  call i32 @fclose(i8* {file_ptr})")
        self.llvm_lines.append(f"  br label %{end_label}")

        # Fail path: Return empty string
        self.llvm_lines.append(f"{fail_label}:")
        empty_buffer = self.get_temp_var()
        self.llvm_lines.append(f"  {empty_buffer} = call i8* @malloc(i64 1)")
        self.llvm_lines.append(f"  store i8 0, i8* {empty_buffer}, align 1")
        self.llvm_lines.append(f"  br label %{end_label}")

        # End: Phi node to select correct buffer
        self.llvm_lines.append(f"{end_label}:")
        result_buffer = self.get_temp_var()
        self.llvm_lines.append(f"  {result_buffer} = phi i8* [ {buffer}, %{success_label} ], [ {empty_buffer}, %{fail_label} ]")
        
        # Store result buffer in variable
        self.llvm_lines.append(f"  store i8* {result_buffer}, i8** @global_{var_name}, align 8")

    def _generate_file_exists_assign(self, var_name, expr):
        """Generate code for file_exists() assignment"""
        filename_arg = expr[12:-1].strip()  # Remove 'file_exists(' and ')'

        print(f"   📁 Processing file_exists assignment: {var_name} = {expr}")

        # Get filename
        if filename_arg.startswith('"') and filename_arg.endswith('"'):
            # String literal filename
            filename = filename_arg[1:-1]
            fname_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_ptr} = alloca [{len(filename)+1} x i8], align 1")

            # Store filename chars
            for i, char in enumerate(filename):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

            # Null terminate
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {len(filename)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

            # Get pointer to start of string
            fname_start = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_start} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 0")
        else:
            # Variable filename
            var_info = self._find_variable(filename_arg)
            if var_info and var_info['type'] == 'string':
                fname_start = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_start} = load i8*, i8** @global_{filename_arg}, align 8")
            else:
                # Unknown variable, use null
                fname_start = "null"

        # Try to open file for reading
        file_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {file_ptr} = call i8* @fopen(i8* {fname_start}, i8* getelementptr inbounds ([2 x i8], [2 x i8]* @read_mode, i32 0, i32 0))")

        # Check if file opened successfully
        null_check = self.get_temp_var()
        self.llvm_lines.append(f"  {null_check} = icmp ne i8* {file_ptr}, null")

        # Close file if it was opened
        success_label = self.get_label("file_exists_close")
        end_label = self.get_label("file_exists_end")

        self.llvm_lines.append(f"  br i1 {null_check}, label %{success_label}, label %{end_label}")

        # Close file
        self.llvm_lines.append(f"{success_label}:")
        self.llvm_lines.append(f"  call i32 @fclose(i8* {file_ptr})")
        self.llvm_lines.append(f"  br label %{end_label}")

        # End: Store result
        self.llvm_lines.append(f"{end_label}:")

        # Use select instead of phi for simplicity
        bool_val = self.get_temp_var()
        self.llvm_lines.append(f"  {bool_val} = select i1 {null_check}, i1 1, i1 0")
        self.llvm_lines.append(f"  store i1 {bool_val}, i1* @global_{var_name}, align 1")

    def _is_mathematical_expression(self, expr):
        """Check if expression contains mathematical operations"""
        math_operators = ['+', '-', '*', '/', '%']
        return any(op in expr for op in math_operators)

    def _generate_math_assign(self, var_name, expr):
        """Generate LLVM IR for mathematical expression assignment"""
        print(f"   🔢 Processing mathematical expression: {var_name} = {expr}")

        # Parse mathematical expression
        result_var = self._evaluate_math_expression(expr)

        # Store result in variable
        self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")

    def _evaluate_math_expression(self, expr):
        """Evaluate mathematical expression and return LLVM variable with result"""
        # Parse expression with proper operator precedence
        tokens = self._tokenize_math_expression(expr)
        print(f"   🔍 Math tokens: {tokens}")

        if len(tokens) == 1:
            # Single value
            return self._get_math_value(tokens[0])
        elif len(tokens) == 3:
            # Simple binary operation: operand1 operator operand2
            left_val = self._get_math_value(tokens[0])
            operator = tokens[1]
            right_val = self._get_math_value(tokens[2])

            result_var = self.get_temp_var()

            if operator == '+':
                self.llvm_lines.append(f"  {result_var} = add i32 {left_val}, {right_val}")
            elif operator == '-':
                self.llvm_lines.append(f"  {result_var} = sub i32 {left_val}, {right_val}")
            elif operator == '*':
                self.llvm_lines.append(f"  {result_var} = mul i32 {left_val}, {right_val}")
            elif operator == '/':
                self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_val}, {right_val}")
            elif operator == '%':
                self.llvm_lines.append(f"  {result_var} = srem i32 {left_val}, {right_val}")

            return result_var
        else:
            # Complex expression - handle with operator precedence
            return self._evaluate_complex_math_expression(tokens)

    def _tokenize_math_expression(self, expr):
        """Tokenize mathematical expression into operands and operators"""
        tokens = []
        current_token = ""

        for char in expr.strip():
            if char in ['+', '-', '*', '/', '%', '(', ')']:
                if current_token.strip():
                    tokens.append(current_token.strip())
                tokens.append(char)
                current_token = ""
            elif char == ' ':
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
            else:
                current_token += char

        if current_token.strip():
            tokens.append(current_token.strip())

        return tokens

    def _evaluate_parentheses(self, tokens):
        """Evaluate expressions inside parentheses first"""
        while '(' in tokens and ')' in tokens:
            # Find the innermost parentheses
            start_idx = -1
            end_idx = -1

            for i, token in enumerate(tokens):
                if token == '(':
                    start_idx = i
                elif token == ')':
                    end_idx = i
                    break

            if start_idx == -1 or end_idx == -1:
                break

            # Extract expression inside parentheses
            inner_expr = tokens[start_idx + 1:end_idx]

            if len(inner_expr) == 1:
                # Simple case: (variable) or (number)
                result = inner_expr[0]
            else:
                # Complex case: evaluate the inner expression
                result = self._evaluate_simple_expression(inner_expr)

            # Replace the parentheses and their content with the result
            tokens = tokens[:start_idx] + [result] + tokens[end_idx + 1:]

        return tokens

    def _evaluate_simple_expression(self, tokens):
        """Evaluate a simple expression without parentheses"""
        if len(tokens) == 1:
            return tokens[0]
        elif len(tokens) == 3:
            # Simple binary operation: a + b, a * b, etc.
            left_val = self._get_math_value(tokens[0])
            operator = tokens[1]
            right_val = self._get_math_value(tokens[2])

            result_var = self.get_temp_var()

            if operator == '+':
                self.llvm_lines.append(f"  {result_var} = add i32 {left_val}, {right_val}")
            elif operator == '-':
                self.llvm_lines.append(f"  {result_var} = sub i32 {left_val}, {right_val}")
            elif operator == '*':
                self.llvm_lines.append(f"  {result_var} = mul i32 {left_val}, {right_val}")
            elif operator == '/':
                self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_val}, {right_val}")
            elif operator == '%':
                self.llvm_lines.append(f"  {result_var} = srem i32 {left_val}, {right_val}")

            return result_var
        else:
            # More complex expression - use existing logic
            return self._evaluate_complex_math_expression_simple(tokens)

    def _evaluate_complex_math_expression_simple(self, tokens):
        """Simplified version of complex math evaluation"""
        # Handle operator precedence: *, /, % before +, -
        # First pass: handle multiplication, division, modulo
        i = 1
        while i < len(tokens):
            if tokens[i] in ['*', '/', '%']:
                left_val = self._get_math_value(tokens[i-1])
                operator = tokens[i]
                right_val = self._get_math_value(tokens[i+1])

                result_var = self.get_temp_var()

                if operator == '*':
                    self.llvm_lines.append(f"  {result_var} = mul i32 {left_val}, {right_val}")
                elif operator == '/':
                    self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_val}, {right_val}")
                elif operator == '%':
                    self.llvm_lines.append(f"  {result_var} = srem i32 {left_val}, {right_val}")

                # Replace the three tokens with the result
                tokens = tokens[:i-1] + [result_var] + tokens[i+2:]
                i = 1  # Start over
            else:
                i += 2

        # Second pass: handle addition and subtraction
        i = 1
        while i < len(tokens):
            if tokens[i] in ['+', '-']:
                left_val = self._get_math_value(tokens[i-1])
                operator = tokens[i]
                right_val = self._get_math_value(tokens[i+1])

                result_var = self.get_temp_var()

                if operator == '+':
                    self.llvm_lines.append(f"  {result_var} = add i32 {left_val}, {right_val}")
                elif operator == '-':
                    self.llvm_lines.append(f"  {result_var} = sub i32 {left_val}, {right_val}")

                # Replace the three tokens with the result
                tokens = tokens[:i-1] + [result_var] + tokens[i+2:]
                i = 1  # Start over
            else:
                i += 2

        return tokens[0] if tokens else "0"

    def _evaluate_complex_math_expression(self, tokens):
        """Evaluate complex mathematical expression with operator precedence and parentheses"""
        print(f"   🔍 Complex math tokens: {tokens}")

        # First, handle parentheses by evaluating expressions inside them
        tokens = self._evaluate_parentheses(tokens)
        print(f"   🔍 After parentheses: {tokens}")

        # Handle operator precedence: *, /, % before +, -
        # First pass: handle multiplication, division, modulo
        i = 1
        while i < len(tokens):
            if tokens[i] in ['*', '/', '%']:
                left_val = self._get_math_value(tokens[i-1])
                operator = tokens[i]
                right_val = self._get_math_value(tokens[i+1])

                print(f"   🔍 Complex math operation: {left_val} {operator} {right_val}")
                result_var = self.get_temp_var()

                if operator == '*':
                    self.llvm_lines.append(f"  {result_var} = mul i32 {left_val}, {right_val}")
                elif operator == '/':
                    self.llvm_lines.append(f"  {result_var} = sdiv i32 {left_val}, {right_val}")
                elif operator == '%':
                    self.llvm_lines.append(f"  {result_var} = srem i32 {left_val}, {right_val}")

                print(f"   ✅ Complex math result: {result_var}")
                # Replace the three tokens with the result
                tokens = tokens[:i-1] + [result_var] + tokens[i+2:]
                print(f"   🔍 Updated tokens: {tokens}")
                # Don't increment i, check the same position again
            else:
                i += 2  # Skip to next operator

        # Second pass: handle addition and subtraction (left to right)
        while len(tokens) > 1:
            if len(tokens) >= 3:
                left_val = self._get_math_value(tokens[0]) if isinstance(tokens[0], str) and not tokens[0].startswith('%') else tokens[0]
                operator = tokens[1]
                right_val = self._get_math_value(tokens[2]) if isinstance(tokens[2], str) and not tokens[2].startswith('%') else tokens[2]

                result_var = self.get_temp_var()

                if operator == '+':
                    self.llvm_lines.append(f"  {result_var} = add i32 {left_val}, {right_val}")
                elif operator == '-':
                    self.llvm_lines.append(f"  {result_var} = sub i32 {left_val}, {right_val}")

                # Replace the first three tokens with the result
                tokens = [result_var] + tokens[3:]
            else:
                break

        # Return the final result
        if len(tokens) == 1:
            return tokens[0] if tokens[0].startswith('%') else self._get_math_value(tokens[0])
        else:
            # Fallback
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {result_var} = add i32 0, 0  ; complex expression fallback")
            return result_var

    def _get_math_value(self, token):
        """Get LLVM value for mathematical token (number or variable)"""
        if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
            # Numeric literal
            return token
        elif token.startswith('%'):
            # Already a temporary variable
            print(f"   ✅ Using temporary variable in math: {token}")
            return token
        else:
            # Check if it's a function parameter first
            if hasattr(self, 'current_function') and self.current_function and token in self.current_function['params']:
                param_index = self.current_function['params'].index(token)
                param_type = self.current_function['param_types'][param_index]
                if param_type == 'i32':
                    print(f"   ✅ Using function parameter in math: {token} -> %{token}")
                    return f"%{token}"

            # Variable
            var_info = self._find_variable(token)
            print(f"   🔍 Math variable '{token}' info: {var_info}")
            if var_info and var_info['type'] in ['integer', 'int']:
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                print(f"   ✅ Loading math variable: {token} -> {load_var}")
                return load_var
            else:
                # Unknown variable, return 0
                print(f"   ⚠️ Warning: Unknown math variable '{token}', using 0")
                return "0"

    def _generate_int_to_string(self, int_var, buffer_ptr):
        """Generate LLVM IR to convert integer to string manually (no sprintf)"""
        # Simple implementation for positive integers 0-999

        # Check if number is zero
        zero_check = self.get_temp_var()
        zero_label = self.get_label("int_zero")
        nonzero_label = self.get_label("int_nonzero")
        end_label = self.get_label("int_end")

        self.llvm_lines.append(f"  {zero_check} = icmp eq i32 {int_var}, 0")
        self.llvm_lines.append(f"  br i1 {zero_check}, label %{zero_label}, label %{nonzero_label}")

        # Zero case: store "0"
        self.llvm_lines.append(f"{zero_label}:")
        self.llvm_lines.append(f"  store i8 48, i8* {buffer_ptr}, align 1")  # '0' = ASCII 48
        null_ptr = self.get_temp_var()
        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds i8, i8* {buffer_ptr}, i64 1")
        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")   # null terminator
        self.llvm_lines.append(f"  br label %{end_label}")

        # Non-zero case: simple conversion for demo (handles 1-digit numbers)
        self.llvm_lines.append(f"{nonzero_label}:")
        ascii_digit = self.get_temp_var()
        char_digit = self.get_temp_var()

        self.llvm_lines.append(f"  {ascii_digit} = add i32 {int_var}, 48")  # Add '0' ASCII
        self.llvm_lines.append(f"  {char_digit} = trunc i32 {ascii_digit} to i8")
        self.llvm_lines.append(f"  store i8 {char_digit}, i8* {buffer_ptr}, align 1")

        # Null terminate
        null_ptr2 = self.get_temp_var()
        self.llvm_lines.append(f"  {null_ptr2} = getelementptr inbounds i8, i8* {buffer_ptr}, i64 1")
        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr2}, align 1")
        self.llvm_lines.append(f"  br label %{end_label}")

        # End
        self.llvm_lines.append(f"{end_label}:")

    def _is_comparison_expression(self, expr):
        """Check if expression contains comparison operations"""
        comparison_operators = ['==', '!=', '<', '>', '<=', '>=']
        return any(op in expr for op in comparison_operators)

    def _generate_comparison_assign(self, var_name, expr):
        """Generate LLVM IR for comparison expression assignment"""
        print(f"   🔍 Processing comparison expression: {var_name} = {expr}")

        # Parse comparison expression
        result_var = self._evaluate_comparison_expression(expr)

        # Store result in variable
        self.llvm_lines.append(f"  store i1 {result_var}, i1* @global_{var_name}, align 1")

    def _evaluate_comparison_expression(self, expr):
        """Evaluate comparison expression and return LLVM variable with result"""
        # Find comparison operator
        comparison_ops = ['==', '!=', '<=', '>=', '<', '>']
        operator = None
        for op in comparison_ops:
            if op in expr:
                operator = op
                break

        if not operator:
            # No comparison operator found
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {result_var} = add i1 0, 0  ; invalid comparison")
            return result_var

        # Split expression
        parts = expr.split(operator)
        if len(parts) != 2:
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {result_var} = add i1 0, 0  ; invalid comparison")
            return result_var

        left_val = self._get_comparison_value(parts[0].strip())
        right_val = self._get_comparison_value(parts[1].strip())

        result_var = self.get_temp_var()

        if operator == '==':
            self.llvm_lines.append(f"  {result_var} = icmp eq i32 {left_val}, {right_val}")
        elif operator == '!=':
            self.llvm_lines.append(f"  {result_var} = icmp ne i32 {left_val}, {right_val}")
        elif operator == '<':
            self.llvm_lines.append(f"  {result_var} = icmp slt i32 {left_val}, {right_val}")
        elif operator == '>':
            self.llvm_lines.append(f"  {result_var} = icmp sgt i32 {left_val}, {right_val}")
        elif operator == '<=':
            self.llvm_lines.append(f"  {result_var} = icmp sle i32 {left_val}, {right_val}")
        elif operator == '>=':
            self.llvm_lines.append(f"  {result_var} = icmp sge i32 {left_val}, {right_val}")

        return result_var

    def _get_comparison_value(self, token):
        """Get LLVM value for comparison token (number or variable)"""
        if token.isdigit() or (token.startswith('-') and token[1:].isdigit()):
            # Numeric literal
            return token
        else:
            # Variable
            var_info = self._find_variable(token)
            if var_info and var_info['type'] == 'integer':
                load_var = self.get_temp_var()
                self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{token}, align 4")
                return load_var
            else:
                # Unknown variable, return 0
                return "0"

    def _generate_boolean_assign(self, var_name, value):
        """Generate LLVM IR for boolean literal assignment"""
        print(f"   🔘 Processing boolean assignment: {var_name} = {value}")

        bool_val = "1" if value.lower() == "true" else "0"
        self.llvm_lines.append(f"  store i1 {bool_val}, i1* @global_{var_name}, align 1")

    def _generate_contains_assign(self, var_name, func_call):
        """Generate LLVM IR for contains() function assignment"""
        print(f"   🔍 Processing contains assignment: {var_name} = {func_call}")

        # Generate the contains condition and store result
        condition_result = self._generate_contains_condition(func_call)
        self.llvm_lines.append(f"  store i1 {condition_result}, i1* @global_{var_name}, align 1")

    def _generate_substring_assign(self, var_name, func_call):
        """Generate LLVM IR for substring() function assignment"""
        print(f"   ✂️ Processing substring assignment: {var_name} = {func_call}")

        # Parse substring(string_var, start, end)
        if func_call.startswith('substring(') and func_call.endswith(')'):
            args_str = func_call[10:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 3:
                string_arg = args[0]
                start_arg = args[1]
                end_arg = args[2]

                self.llvm_lines.append(f"  ; {var_name} = substring({string_arg}, {start_arg}, {end_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Get start and end indices
                    start_val = int(start_arg) if start_arg.isdigit() else 0
                    end_val = int(end_arg) if end_arg.isdigit() else 0

                    if end_val > start_val:
                        # Calculate substring length
                        substr_len = end_val - start_val

                        # Allocate memory for substring (+1 for null terminator)
                        substr_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {substr_ptr} = call i8* @malloc(i64 {substr_len + 1})")

                        # Copy characters from source to substring
                        for i in range(substr_len):
                            src_char_ptr = self.get_temp_var()
                            dst_char_ptr = self.get_temp_var()
                            char_val = self.get_temp_var()

                            # Get character from source at (start + i)
                            self.llvm_lines.append(f"  {src_char_ptr} = getelementptr inbounds i8, i8* {src_ptr}, i64 {start_val + i}")
                            self.llvm_lines.append(f"  {char_val} = load i8, i8* {src_char_ptr}, align 1")

                            # Store character in destination at i
                            self.llvm_lines.append(f"  {dst_char_ptr} = getelementptr inbounds i8, i8* {substr_ptr}, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {char_val}, i8* {dst_char_ptr}, align 1")

                        # Add null terminator
                        null_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds i8, i8* {substr_ptr}, i64 {substr_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                        # Store result in variable
                        self.llvm_lines.append(f"  store i8* {substr_ptr}, i8** @global_{var_name}, align 8")
                    else:
                        # Invalid range - return empty string
                        empty_str = self.get_temp_var()
                        self.llvm_lines.append(f"  {empty_str} = call i8* @malloc(i64 1)")
                        self.llvm_lines.append(f"  store i8 0, i8* {empty_str}, align 1")
                        self.llvm_lines.append(f"  store i8* {empty_str}, i8** @global_{var_name}, align 8")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: substring() requires 3 arguments")

    def _generate_replace_assign(self, var_name, func_call):
        """Generate LLVM IR for replace() function assignment"""
        print(f"   🔄 Processing replace assignment: {var_name} = {func_call}")

        # Parse replace(string_var, old, new)
        if func_call.startswith('replace(') and func_call.endswith(')'):
            args_str = func_call[8:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 3:
                string_arg = args[0]
                old_arg = args[1]
                new_arg = args[2]

                self.llvm_lines.append(f"  ; {var_name} = replace({string_arg}, {old_arg}, {new_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Handle old and new arguments (string literals for now)
                    if (old_arg.startswith('"') and old_arg.endswith('"') and
                        new_arg.startswith('"') and new_arg.endswith('"')):

                        old_str = old_arg[1:-1]  # Remove quotes
                        new_str = new_arg[1:-1]  # Remove quotes

                        # Simple replace implementation
                        old_len = len(old_str)
                        new_len = len(new_str)

                        # Create old string constant
                        old_const = self.get_temp_var() + "_old"
                        self.llvm_lines.append(f"  {old_const} = alloca [{old_len + 1} x i8], align 1")
                        for i, char in enumerate(old_str):
                            char_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{old_len + 1} x i8], [{old_len + 1} x i8]* {old_const}, i64 0, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                        # Null terminator for old
                        old_null = self.get_temp_var()
                        self.llvm_lines.append(f"  {old_null} = getelementptr inbounds [{old_len + 1} x i8], [{old_len + 1} x i8]* {old_const}, i64 0, i64 {old_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {old_null}, align 1")
                        old_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {old_ptr} = bitcast [{old_len + 1} x i8]* {old_const} to i8*")

                        # Find the old string in source using strstr
                        found_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_ptr} = call i8* @strstr(i8* {src_ptr}, i8* {old_ptr})")

                        # Check if found
                        found_check = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_check} = icmp ne i8* {found_ptr}, null")

                        # Create labels for conditional replacement
                        replace_label = self.get_label("replace")
                        no_replace_label = self.get_label("no_replace")
                        end_label = self.get_label("end_replace")

                        self.llvm_lines.append(f"  br i1 {found_check}, label %{replace_label}, label %{no_replace_label}")

                        # Replace block - simplified implementation
                        self.llvm_lines.append(f"{replace_label}:")

                        if new_len > 0:
                            # Create new string constant
                            new_const = self.get_temp_var() + "_new"
                            self.llvm_lines.append(f"  {new_const} = alloca [{new_len + 1} x i8], align 1")
                            for i, char in enumerate(new_str):
                                char_ptr = self.get_temp_var()
                                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{new_len + 1} x i8], [{new_len + 1} x i8]* {new_const}, i64 0, i64 {i}")
                                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                            # Null terminator for new
                            new_null = self.get_temp_var()
                            self.llvm_lines.append(f"  {new_null} = getelementptr inbounds [{new_len + 1} x i8], [{new_len + 1} x i8]* {new_const}, i64 0, i64 {new_len}")
                            self.llvm_lines.append(f"  store i8 0, i8* {new_null}, align 1")
                            new_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {new_ptr} = bitcast [{new_len + 1} x i8]* {new_const} to i8*")

                            # Allocate result and copy new string
                            result_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {result_ptr} = call i8* @malloc(i64 {new_len + 1})")
                            self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {result_ptr}, i8* {new_ptr}, i64 {new_len + 1}, i1 false)")
                        else:
                            # Empty replacement
                            result_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {result_ptr} = call i8* @malloc(i64 1)")
                            self.llvm_lines.append(f"  store i8 0, i8* {result_ptr}, align 1")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # No replace block (string not found)
                        self.llvm_lines.append(f"{no_replace_label}:")

                        # Return copy of original string
                        src_len = self.get_temp_var()
                        orig_len_plus1 = self.get_temp_var()
                        orig_copy = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_len} = call i64 @strlen(i8* {src_ptr})")
                        self.llvm_lines.append(f"  {orig_len_plus1} = add i64 {src_len}, 1")
                        self.llvm_lines.append(f"  {orig_copy} = call i8* @malloc(i64 {orig_len_plus1})")
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {orig_copy}, i8* {src_ptr}, i64 {orig_len_plus1}, i1 false)")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # End block
                        self.llvm_lines.append(f"{end_label}:")

                        # PHI node to select result
                        final_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {final_result} = phi i8* [ {result_ptr}, %{replace_label} ], [ {orig_copy}, %{no_replace_label} ]")

                        # Store result in variable
                        self.llvm_lines.append(f"  store i8* {final_result}, i8** @global_{var_name}, align 8")
                    else:
                        self.llvm_lines.append(f"  ; error: replace() currently supports only string literals for old and new")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: replace() requires 3 arguments")

    def _generate_split_assign(self, var_name, func_call):
        """Generate LLVM IR for split() function assignment"""
        print(f"   🔀 Processing split assignment: {var_name} = {func_call}")

        # Parse split(string_var, delimiter)
        if func_call.startswith('split(') and func_call.endswith(')'):
            args_str = func_call[6:-1].strip()
            args = [arg.strip() for arg in args_str.split(',')]

            if len(args) >= 2:
                string_arg = args[0]
                delimiter_arg = args[1]

                self.llvm_lines.append(f"  ; {var_name} = split({string_arg}, {delimiter_arg})")

                # Get the source string
                var_info = self._find_variable(string_arg)
                if var_info and var_info['type'] == 'string':
                    # Load source string
                    src_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {src_ptr} = load i8*, i8** @global_{string_arg}, align 8")

                    # Handle delimiter argument (string literal for now)
                    if delimiter_arg.startswith('"') and delimiter_arg.endswith('"'):
                        delimiter_str = delimiter_arg[1:-1]  # Remove quotes
                        delimiter_len = len(delimiter_str)

                        # Create delimiter string constant
                        delim_const = self.get_temp_var() + "_delim"
                        self.llvm_lines.append(f"  {delim_const} = alloca [{delimiter_len + 1} x i8], align 1")
                        for i, char in enumerate(delimiter_str):
                            char_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{delimiter_len + 1} x i8], [{delimiter_len + 1} x i8]* {delim_const}, i64 0, i64 {i}")
                            self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                        # Null terminator for delimiter
                        delim_null = self.get_temp_var()
                        self.llvm_lines.append(f"  {delim_null} = getelementptr inbounds [{delimiter_len + 1} x i8], [{delimiter_len + 1} x i8]* {delim_const}, i64 0, i64 {delimiter_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {delim_null}, align 1")
                        delim_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {delim_ptr} = bitcast [{delimiter_len + 1} x i8]* {delim_const} to i8*")

                        # Find the delimiter in source using strstr
                        found_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_ptr} = call i8* @strstr(i8* {src_ptr}, i8* {delim_ptr})")

                        # Check if found
                        found_check = self.get_temp_var()
                        self.llvm_lines.append(f"  {found_check} = icmp ne i8* {found_ptr}, null")

                        # Create labels for conditional split
                        split_label = self.get_label("split")
                        no_split_label = self.get_label("no_split")
                        end_label = self.get_label("end_split")

                        self.llvm_lines.append(f"  br i1 {found_check}, label %{split_label}, label %{no_split_label}")

                        # Split block - return first part before delimiter
                        self.llvm_lines.append(f"{split_label}:")

                        # Calculate length of first part
                        first_len_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_len_ptr} = ptrtoint i8* {found_ptr} to i64")
                        src_int = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_int} = ptrtoint i8* {src_ptr} to i64")
                        first_len = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_len} = sub i64 {first_len_ptr}, {src_int}")

                        # Allocate memory for first part (+1 for null terminator)
                        first_size = self.get_temp_var()
                        first_ptr = self.get_temp_var()
                        self.llvm_lines.append(f"  {first_size} = add i64 {first_len}, 1")
                        self.llvm_lines.append(f"  {first_ptr} = call i8* @malloc(i64 {first_size})")

                        # Copy first part
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {first_ptr}, i8* {src_ptr}, i64 {first_len}, i1 false)")

                        # Add null terminator
                        null_pos = self.get_temp_var()
                        self.llvm_lines.append(f"  {null_pos} = getelementptr inbounds i8, i8* {first_ptr}, i64 {first_len}")
                        self.llvm_lines.append(f"  store i8 0, i8* {null_pos}, align 1")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # No split block (delimiter not found)
                        self.llvm_lines.append(f"{no_split_label}:")

                        # Return copy of original string
                        src_len = self.get_temp_var()
                        orig_len_plus1 = self.get_temp_var()
                        orig_copy = self.get_temp_var()
                        self.llvm_lines.append(f"  {src_len} = call i64 @strlen(i8* {src_ptr})")
                        self.llvm_lines.append(f"  {orig_len_plus1} = add i64 {src_len}, 1")
                        self.llvm_lines.append(f"  {orig_copy} = call i8* @malloc(i64 {orig_len_plus1})")
                        self.llvm_lines.append(f"  call void @llvm.memcpy.p0i8.p0i8.i64(i8* {orig_copy}, i8* {src_ptr}, i64 {orig_len_plus1}, i1 false)")

                        self.llvm_lines.append(f"  br label %{end_label}")

                        # End block
                        self.llvm_lines.append(f"{end_label}:")

                        # PHI node to select result
                        final_result = self.get_temp_var()
                        self.llvm_lines.append(f"  {final_result} = phi i8* [ {first_ptr}, %{split_label} ], [ {orig_copy}, %{no_split_label} ]")

                        # Store result in variable
                        self.llvm_lines.append(f"  store i8* {final_result}, i8** @global_{var_name}, align 8")
                    else:
                        self.llvm_lines.append(f"  ; error: split() currently supports only string literals for delimiter")
                else:
                    self.llvm_lines.append(f"  ; error: {string_arg} is not a string variable")
            else:
                self.llvm_lines.append(f"  ; error: split() requires 2 arguments")

    def _generate_function_call_assign(self, var_name, func_call):
        """Generate LLVM IR for function call assignment"""
        print(f"   🔄 Processing function call assignment: {var_name} = {func_call}")

        # Parse function call
        if '(' in func_call and func_call.endswith(')'):
            func_name = func_call[:func_call.index('(')]
            args_str = func_call[func_call.index('(')+1:-1].strip()

            # Parse arguments
            args = []
            if args_str:
                raw_args = [arg.strip() for arg in args_str.split(',')]
                for arg in raw_args:
                    if arg.startswith('"') and arg.endswith('"'):
                        args.append({'type': 'string_literal', 'value': arg[1:-1]})
                    elif arg.isdigit() or (arg.startswith('-') and arg[1:].isdigit()):
                        args.append({'type': 'integer_literal', 'value': int(arg)})
                    else:
                        args.append({'type': 'variable', 'name': arg})

            # Check if function exists in imported or local functions
            if func_name in self.imported_functions:
                func_info = self.imported_functions[func_name]
                is_local = False
            elif func_name in self.local_functions:
                func_info = self.local_functions[func_name]
                is_local = True
            else:
                # Unknown function
                self.llvm_lines.append(f"  ; unknown function call assignment: {var_name} = {func_call}")
                return

            if not is_local:
                # Imported function handling (existing code)

                # Generate LLVM arguments
                llvm_args = []
                for arg in args:
                    if arg['type'] == 'string_literal':
                        # String literal argument
                        string_text = arg['value']
                        string_index = self._find_string_index(string_text)
                        if string_index >= 0:
                            string_length = calculate_llvm_string_length(string_text)
                            const_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {const_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                            llvm_args.append(f"i8* {const_ptr}")
                        else:
                            llvm_args.append("i8* null")
                    elif arg['type'] == 'integer_literal':
                        # Integer literal argument - pass as i32 directly for math functions
                        int_value = arg['value']
                        llvm_args.append(f"i32 {int_value}")
                    else:
                        # Variable argument
                        var_name_arg = arg['name']
                        var_info = self._find_variable(var_name_arg)
                        if var_info and var_info['type'] == 'string':
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_name_arg}, align 8")
                            llvm_args.append(f"i8* {load_var}")
                        elif var_info and var_info['type'] == 'integer':
                            # Convert integer to string for function call
                            load_var = self.get_temp_var()
                            str_buffer = self.get_temp_var()
                            str_buffer_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name_arg}, align 4")
                            self.llvm_lines.append(f"  {str_buffer} = alloca [12 x i8], align 1")
                            self.llvm_lines.append(f"  {str_buffer_ptr} = getelementptr inbounds [12 x i8], [12 x i8]* {str_buffer}, i64 0, i64 0")
                            self._generate_int_to_string(load_var, str_buffer_ptr)
                            llvm_args.append(f"i8* {str_buffer_ptr}")
                        else:
                            llvm_args.append("i8* null")

                # Generate function call with return value
                result_var = self.get_temp_var()

                # Determine return type (assume integer for math functions)
                return_type = func_info.get('return_type', 'integer')
                if return_type == 'integer':
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  {result_var} = call i32 @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  {result_var} = call i32 @{func_name}()")

                    # Store result in variable
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] in ['integer', 'int']:
                        self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
                        print(f"   ✅ Stored imported function result: {result_var} -> @global_{var_name}")
                    else:
                        print(f"   ❌ Cannot store imported function result: var_info = {var_info}")
                elif return_type == 'string':
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  {result_var} = call i8* @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  {result_var} = call i8* @{func_name}()")

                    # Store result in variable
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] == 'string':
                        self.llvm_lines.append(f"  store i8* {result_var}, i8** @global_{var_name}, align 8")
                else:
                    # Other return types (boolean)
                    self.llvm_lines.append(f"  ; function call assignment not fully implemented for type: {return_type}")
            else:
                # Local function handling
                print(f"   🔄 Processing local function call assignment: {var_name} = {func_name}()")

                # Generate LLVM arguments for local function
                llvm_args = []
                for arg in args:
                    if arg['type'] == 'string_literal':
                        # String literal argument
                        string_text = arg['value']
                        string_index = self._find_string_index(string_text)
                        if string_index >= 0:
                            string_length = calculate_llvm_string_length(string_text)
                            const_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {const_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                            llvm_args.append(f"i8* {const_ptr}")
                        else:
                            llvm_args.append("i8* null")
                    elif arg['type'] == 'integer_literal':
                        # Integer literal argument - pass as i32 directly
                        int_value = arg['value']
                        llvm_args.append(f"i32 {int_value}")
                    else:
                        # Variable argument
                        var_name_arg = arg['name']
                        var_info = self._find_variable(var_name_arg)
                        if var_info and var_info['type'] == 'string':
                            load_var = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{var_name_arg}, align 8")
                            llvm_args.append(f"i8* {load_var}")
                        elif var_info and var_info['type'] == 'integer':
                            # Convert integer to string for function call
                            load_var = self.get_temp_var()
                            str_buffer = self.get_temp_var()
                            str_buffer_ptr = self.get_temp_var()
                            self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{var_name_arg}, align 4")
                            self.llvm_lines.append(f"  {str_buffer} = alloca [12 x i8], align 1")
                            self.llvm_lines.append(f"  {str_buffer_ptr} = getelementptr inbounds [12 x i8], [12 x i8]* {str_buffer}, i64 0, i64 0")
                            self._generate_int_to_string(load_var, str_buffer_ptr)
                            llvm_args.append(f"i8* {str_buffer_ptr}")
                        else:
                            llvm_args.append("i8* null")

                # Generate function call with return value
                result_var = self.get_temp_var()

                # Determine return type - check if function is currently being processed
                if hasattr(self, 'current_function') and self.current_function and self.current_function['name'] == func_name:
                    # Function is currently being processed, get return type from current_function
                    return_type = self.current_function.get('return_type', 'integer')  # Default to integer for math functions
                else:
                    # Function already processed, get from registry
                    return_type = func_info.get('return_type', 'integer')  # Default to integer for math functions
                if return_type == 'integer':
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  {result_var} = call i32 @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  {result_var} = call i32 @{func_name}()")

                    # Store result in variable
                    var_info = self._find_variable(var_name)
                    print(f"   🔍 Storing result in {var_name}: var_info = {var_info}")
                    if var_info and var_info['type'] in ['integer', 'int']:
                        self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
                        print(f"   ✅ Stored integer result: {result_var} -> @global_{var_name}")
                    else:
                        print(f"   ❌ Cannot store result: var_info = {var_info}")
                elif return_type == 'boolean':
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  {result_var} = call i1 @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  {result_var} = call i1 @{func_name}()")

                    # Store result in variable
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] == 'boolean':
                        self.llvm_lines.append(f"  store i1 {result_var}, i1* @global_{var_name}, align 1")
                elif return_type == 'string':
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  {result_var} = call i8* @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  {result_var} = call i8* @{func_name}()")

                    # Store result in variable
                    var_info = self._find_variable(var_name)
                    if var_info and var_info['type'] == 'string':
                        self.llvm_lines.append(f"  store i8* {result_var}, i8** @global_{var_name}, align 8")
                else:
                    # Void function - just call it
                    if llvm_args:
                        args_str = ", ".join(llvm_args)
                        self.llvm_lines.append(f"  call void @{func_name}({args_str})")
                    else:
                        self.llvm_lines.append(f"  call void @{func_name}()")

    def _generate_system_assign(self, var_name, expr):
        """Generate code for system() call with result assignment"""
        cmd_arg = expr[7:-1].strip()
        
        if cmd_arg.startswith('"') and cmd_arg.endswith('"'):
            # String literal command
            cmd_str = cmd_arg[1:-1]
            cmd_len = len(cmd_str) + 1
            cmd_const = self.get_temp_var() + "_cmd"
            
            self.llvm_lines.append(f"  {cmd_const} = alloca [{cmd_len} x i8], align 1")
            
            # Store command chars
            for i, char in enumerate(cmd_str):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{cmd_len} x i8], [{cmd_len} x i8]* {cmd_const}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
            
            # Null terminator
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{cmd_len} x i8], [{cmd_len} x i8]* {cmd_const}, i64 0, i64 {len(cmd_str)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
            
            cmd_ptr = self.get_temp_var()
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {cmd_ptr} = bitcast [{cmd_len} x i8]* {cmd_const} to i8*")
            self.llvm_lines.append(f"  {result_var} = call i32 @system(i8* {cmd_ptr})")
            self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
        else:
            # Variable command
            load_var = self.get_temp_var()
            result_var = self.get_temp_var()
            self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{cmd_arg}, align 8")
            self.llvm_lines.append(f"  {result_var} = call i32 @system(i8* {load_var})")
            self.llvm_lines.append(f"  store i32 {result_var}, i32* @global_{var_name}, align 4")
            
    def _generate_string_concat_assign(self, var_name, expr):
        """Generate code for string concatenation assignment"""
        parts = parse_string_expression_parts(expr)
        
        self.llvm_lines.append(f"  ; {var_name} = {expr[:50]}{'...' if len(expr) > 50 else ''}")
        
        # Calculate total length
        total_len_var = self.get_temp_var()
        self.llvm_lines.append(f"  {total_len_var} = alloca i64, align 8")
        self.llvm_lines.append(f"  store i64 1, i64* {total_len_var}, align 8  ; Start with 1 for null terminator")
        
        # Store part lengths
        part_lengths = []
        for i, part in enumerate(parts):
            if part['type'] == 'string_literal':
                len_var = self.get_temp_var()
                self.llvm_lines.append(f"  {len_var} = add i64 0, {len(part['value'])}")
                part_lengths.append(len_var)
            else:
                # Variable
                var_info = self._find_variable(part['name'])
                if var_info and var_info['type'] == 'string':
                    load_var = self.get_temp_var()
                    len_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{part['name']}, align 8")
                    self.llvm_lines.append(f"  {len_var} = call i64 @strlen(i8* {load_var})")
                    part_lengths.append(len_var)
                elif var_info and var_info['type'] == 'integer':
                    # Integer variables need ~12 chars max for string representation
                    len_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {len_var} = add i64 0, 12  ; Max length for integer string")
                    part_lengths.append(len_var)
                    
        # Sum lengths
        for len_var in part_lengths:
            curr_total = self.get_temp_var()
            new_total = self.get_temp_var()
            self.llvm_lines.append(f"  {curr_total} = load i64, i64* {total_len_var}, align 8")
            self.llvm_lines.append(f"  {new_total} = add i64 {curr_total}, {len_var}")
            self.llvm_lines.append(f"  store i64 {new_total}, i64* {total_len_var}, align 8")
            
        # Allocate buffer
        final_len = self.get_temp_var()
        buffer = self.get_temp_var()
        self.llvm_lines.append(f"  {final_len} = load i64, i64* {total_len_var}, align 8")
        self.llvm_lines.append(f"  {buffer} = call i8* @malloc(i64 {final_len})")
        
        # Initialize buffer
        self.llvm_lines.append(f"  store i8 0, i8* {buffer}, align 1")
        
        # Concatenate parts
        for i, part in enumerate(parts):
            if part['type'] == 'string_literal':
                # Create inline string constant for concatenation
                escaped_text = self._escape_string(part['value'])
                string_len = len(escaped_text) + 1  # +1 for null terminator
                const_name = self.get_temp_var() + "_concat_str"

                # Create inline constant
                self.llvm_lines.append(f"  {const_name} = alloca [{string_len} x i8], align 1")

                # Store string chars
                for j, char in enumerate(escaped_text):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {j}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{string_len} x i8], [{string_len} x i8]* {const_name}, i64 0, i64 {len(escaped_text)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Cast and concatenate
                const_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {const_ptr} = bitcast [{string_len} x i8]* {const_name} to i8*")
                self.llvm_lines.append(f"  call i8* @strcat(i8* {buffer}, i8* {const_ptr})")
            else:
                # Variable
                var_info = self._find_variable(part['name'])
                if var_info and var_info['type'] == 'string':
                    load_var = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{part['name']}, align 8")
                    self.llvm_lines.append(f"  call i8* @strcat(i8* {buffer}, i8* {load_var})")
                elif var_info and var_info['type'] == 'integer':
                    # Convert integer to string using sprintf (like C)
                    load_var = self.get_temp_var()
                    str_buffer = self.get_temp_var()
                    str_buffer_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_{part['name']}, align 4")
                    self.llvm_lines.append(f"  {str_buffer} = alloca [12 x i8], align 1  ; Buffer for integer string")
                    self.llvm_lines.append(f"  {str_buffer_ptr} = getelementptr inbounds [12 x i8], [12 x i8]* {str_buffer}, i64 0, i64 0")
                    self._generate_int_to_string(load_var, str_buffer_ptr)
                    self.llvm_lines.append(f"  call i8* @strcat(i8* {buffer}, i8* {str_buffer_ptr})")
                    
        # Store result in variable
        self.llvm_lines.append(f"  store i8* {buffer}, i8** @global_{var_name}, align 8")
        
    def _generate_write(self, stmt):
        """Generate code for write() function"""
        # Extract filename and content
        content = stmt['content'][6:-1]  # Remove 'write(' and ')'
        if ',' in content:
            parts = content.split(',', 1)
            filename_part = parts[0].strip()
            content_part = parts[1].strip()
            
            self.llvm_lines.append(f"  ; write({filename_part}, {content_part[:30]}...)")
            
            # Handle filename
            if filename_part.startswith('"') and filename_part.endswith('"'):
                # String literal filename
                filename = filename_part[1:-1]
                fname_len = len(filename) + 1
                fname_const = self.get_temp_var() + "_fname"
                self.llvm_lines.append(f"  {fname_const} = alloca [{fname_len} x i8], align 1")
                
                # Store filename chars
                for i, char in enumerate(filename):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{fname_len} x i8], [{fname_len} x i8]* {fname_const}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                
                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{fname_len} x i8], [{fname_len} x i8]* {fname_const}, i64 0, i64 {len(filename)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
                
                fname_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_ptr} = bitcast [{fname_len} x i8]* {fname_const} to i8*")
            else:
                # Variable filename
                fname_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_ptr} = load i8*, i8** @global_{filename_part}, align 8")
            
            # Handle content
            if content_part.startswith('"') and content_part.endswith('"'):
                # String literal content
                file_content = content_part[1:-1]

                # Process escape sequences
                processed_content = []
                i = 0
                while i < len(file_content):
                    if i < len(file_content) - 1 and file_content[i] == '\\':
                        if file_content[i+1] == 'n':
                            processed_content.append('\n')
                            i += 2
                        elif file_content[i+1] == 't':
                            processed_content.append('\t')
                            i += 2
                        elif file_content[i+1] == '\\':
                            processed_content.append('\\')
                            i += 2
                        elif file_content[i+1] == '"':
                            processed_content.append('"')
                            i += 2
                        else:
                            processed_content.append(file_content[i])
                            i += 1
                    else:
                        processed_content.append(file_content[i])
                        i += 1

                content_len = len(processed_content) + 1
                content_const = self.get_temp_var() + "_content"
                self.llvm_lines.append(f"  {content_const} = alloca [{content_len} x i8], align 1")

                # Store content chars
                for i, char in enumerate(processed_content):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{content_len} x i8], [{content_len} x i8]* {content_const}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
                
                # Null terminator
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{content_len} x i8], [{content_len} x i8]* {content_const}, i64 0, i64 {len(processed_content)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
                
                content_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {content_ptr} = bitcast [{content_len} x i8]* {content_const} to i8*")
            else:
                # Variable content
                content_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {content_ptr} = load i8*, i8** @global_{content_part}, align 8")
            
            # Open file, write, close
            file_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {file_ptr} = call i8* @fopen(i8* {fname_ptr}, i8* getelementptr inbounds ([2 x i8], [2 x i8]* @write_mode, i32 0, i32 0))")
            self.llvm_lines.append(f"  call i32 (i8*, i8*, ...) @fprintf(i8* {file_ptr}, i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {content_ptr})")
            self.llvm_lines.append(f"  call i32 @fclose(i8* {file_ptr})")

    def _generate_write_file_call(self, stmt):
        """Generate code for write_file() function call"""
        content = stmt['content'][11:-1]  # Remove 'write_file(' and ')'

        if ',' in content:
            parts = content.split(',', 1)
            filename_part = parts[0].strip()
            content_part = parts[1].strip()

            print(f"   📝 Processing write_file: {filename_part}, {content_part[:30]}...")

            # Get filename
            if filename_part.startswith('"') and filename_part.endswith('"'):
                filename = filename_part[1:-1]
                fname_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_ptr} = alloca [{len(filename)+1} x i8], align 1")

                # Store filename chars
                for i, char in enumerate(filename):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminate
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {len(filename)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Get pointer to start
                fname_start = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_start} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 0")
            else:
                # Variable filename
                var_info = self._find_variable(filename_part)
                if var_info and var_info['type'] == 'string':
                    fname_start = self.get_temp_var()
                    self.llvm_lines.append(f"  {fname_start} = load i8*, i8** @global_{filename_part}, align 8")
                else:
                    fname_start = "null"

            # Get content
            if content_part.startswith('"') and content_part.endswith('"'):
                # String literal content
                content_text = content_part[1:-1]
                string_index = self._find_string_index(content_text)
                if string_index >= 0:
                    string_length = calculate_llvm_string_length(content_text)
                    content_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {content_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                else:
                    content_ptr = "null"
            else:
                # Variable content
                var_info = self._find_variable(content_part)
                if var_info and var_info['type'] == 'string':
                    content_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {content_ptr} = load i8*, i8** @global_{content_part}, align 8")
                else:
                    content_ptr = "null"

            # Open file for writing
            file_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {file_ptr} = call i8* @fopen(i8* {fname_start}, i8* getelementptr inbounds ([2 x i8], [2 x i8]* @write_mode, i32 0, i32 0))")

            # Write content and close
            self.llvm_lines.append(f"  call i32 (i8*, i8*, ...) @fprintf(i8* {file_ptr}, i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {content_ptr})")
            self.llvm_lines.append(f"  call i32 @fclose(i8* {file_ptr})")

    def _generate_append_file_call(self, stmt):
        """Generate code for append_file() function call"""
        content = stmt['content'][12:-1]  # Remove 'append_file(' and ')'

        if ',' in content:
            parts = content.split(',', 1)
            filename_part = parts[0].strip()
            content_part = parts[1].strip()

            print(f"   📝 Processing append_file: {filename_part}, {content_part[:30]}...")

            # Similar to write_file but with append mode
            # Get filename (same as write_file)
            if filename_part.startswith('"') and filename_part.endswith('"'):
                filename = filename_part[1:-1]
                fname_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_ptr} = alloca [{len(filename)+1} x i8], align 1")

                # Store filename chars
                for i, char in enumerate(filename):
                    char_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {i}")
                    self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

                # Null terminate
                null_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {len(filename)}")
                self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

                # Get pointer to start
                fname_start = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_start} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 0")
            else:
                # Variable filename
                var_info = self._find_variable(filename_part)
                if var_info and var_info['type'] == 'string':
                    fname_start = self.get_temp_var()
                    self.llvm_lines.append(f"  {fname_start} = load i8*, i8** @global_{filename_part}, align 8")
                else:
                    fname_start = "null"

            # Get content (same as write_file)
            if content_part.startswith('"') and content_part.endswith('"'):
                # String literal content
                content_text = content_part[1:-1]
                string_index = self._find_string_index(content_text)
                if string_index >= 0:
                    string_length = calculate_llvm_string_length(content_text)
                    content_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {content_ptr} = bitcast [{string_length} x i8]* @.str_{string_index} to i8*")
                else:
                    content_ptr = "null"
            else:
                # Variable content
                var_info = self._find_variable(content_part)
                if var_info and var_info['type'] == 'string':
                    content_ptr = self.get_temp_var()
                    self.llvm_lines.append(f"  {content_ptr} = load i8*, i8** @global_{content_part}, align 8")
                else:
                    content_ptr = "null"

            # Open file for appending (use "a" mode)
            file_ptr = self.get_temp_var()
            append_mode = self.get_temp_var()
            self.llvm_lines.append(f"  {append_mode} = alloca [2 x i8], align 1")
            self.llvm_lines.append(f"  %append_char = getelementptr inbounds [2 x i8], [2 x i8]* {append_mode}, i64 0, i64 0")
            self.llvm_lines.append(f"  store i8 97, i8* %append_char, align 1")  # 'a'
            self.llvm_lines.append(f"  %append_null = getelementptr inbounds [2 x i8], [2 x i8]* {append_mode}, i64 0, i64 1")
            self.llvm_lines.append(f"  store i8 0, i8* %append_null, align 1")
            self.llvm_lines.append(f"  %append_mode_ptr = getelementptr inbounds [2 x i8], [2 x i8]* {append_mode}, i64 0, i64 0")

            self.llvm_lines.append(f"  {file_ptr} = call i8* @fopen(i8* {fname_start}, i8* %append_mode_ptr)")

            # Write content and close
            self.llvm_lines.append(f"  call i32 (i8*, i8*, ...) @fprintf(i8* {file_ptr}, i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* {content_ptr})")
            self.llvm_lines.append(f"  call i32 @fclose(i8* {file_ptr})")

    def _generate_remove_file_call(self, stmt):
        """Generate code for remove_file() function call"""
        content = stmt['content'][12:-1]  # Remove 'remove_file(' and ')'
        filename_part = content.strip()

        print(f"   🗑️ Processing remove_file: {filename_part}")

        # Get filename
        if filename_part.startswith('"') and filename_part.endswith('"'):
            # String literal filename
            filename = filename_part[1:-1]
            fname_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_ptr} = alloca [{len(filename)+1} x i8], align 1")

            # Store filename chars
            for i, char in enumerate(filename):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")

            # Null terminate
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 {len(filename)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")

            # Get pointer to start
            fname_start = self.get_temp_var()
            self.llvm_lines.append(f"  {fname_start} = getelementptr inbounds [{len(filename)+1} x i8], [{len(filename)+1} x i8]* {fname_ptr}, i64 0, i64 0")
        else:
            # Variable filename
            var_info = self._find_variable(filename_part)
            if var_info and var_info['type'] == 'string':
                fname_start = self.get_temp_var()
                self.llvm_lines.append(f"  {fname_start} = load i8*, i8** @global_{filename_part}, align 8")
            else:
                fname_start = "null"

        # Call remove function
        result = self.get_temp_var()
        self.llvm_lines.append(f"  {result} = call i32 @remove(i8* {fname_start})")
        self.llvm_lines.append(f"  ; File removal result: {result} (0 = success, non-zero = failure)")

    def _generate_system(self, stmt):
        """Generate code for system() function call"""
        content = stmt['content'][7:-1]  # Remove 'system(' and ')'
        
        if content.strip().startswith('"') and content.strip().endswith('"'):
            # String literal command
            cmd = content.strip()[1:-1]
            cmd_len = len(cmd) + 1
            cmd_const = self.get_temp_var() + "_cmd"
            
            self.llvm_lines.append(f"  ; system(\"{cmd}\")")
            self.llvm_lines.append(f"  {cmd_const} = alloca [{cmd_len} x i8], align 1")
            
            # Store command chars
            for i, char in enumerate(cmd):
                char_ptr = self.get_temp_var()
                self.llvm_lines.append(f"  {char_ptr} = getelementptr inbounds [{cmd_len} x i8], [{cmd_len} x i8]* {cmd_const}, i64 0, i64 {i}")
                self.llvm_lines.append(f"  store i8 {ord(char)}, i8* {char_ptr}, align 1")
            
            # Null terminator
            null_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {null_ptr} = getelementptr inbounds [{cmd_len} x i8], [{cmd_len} x i8]* {cmd_const}, i64 0, i64 {len(cmd)}")
            self.llvm_lines.append(f"  store i8 0, i8* {null_ptr}, align 1")
            
            cmd_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {cmd_ptr} = bitcast [{cmd_len} x i8]* {cmd_const} to i8*")
            self.llvm_lines.append(f"  call i32 @system(i8* {cmd_ptr})")
        else:
            # Variable command
            load_var = self.get_temp_var()
            self.llvm_lines.append(f"  ; system({content.strip()})")
            self.llvm_lines.append(f"  {load_var} = load i8*, i8** @global_{content.strip()}, align 8")
            self.llvm_lines.append(f"  call i32 @system(i8* {load_var})")

    def _generate_argc_assign(self, var_name):
        """Generate code for argc() function call"""
        self.llvm_lines.append(f"  ; {var_name} = argc()")

        # Use global argc variable that's set in main wrapper
        load_var = self.get_temp_var()
        self.llvm_lines.append(f"  {load_var} = load i32, i32* @global_argc, align 4")
        self.llvm_lines.append(f"  store i32 {load_var}, i32* @global_{var_name}, align 4")

        # Add variable to variables list if not already present
        if not self._find_variable(var_name):
            self.variables.append({
                'name': var_name,
                'type': 'integer',
                'value': 'argc()'
            })
            print(f"   ✅ Added {var_name} to variables list")

        print(f"   ✅ Generated argc() assignment for {var_name}")

    def _generate_argv_assign(self, var_name, expr):
        """Generate code for argv(index) function call"""
        # Extract index from argv(index)
        index_str = expr[5:-1].strip()  # Remove 'argv(' and ')'

        self.llvm_lines.append(f"  ; {var_name} = argv({index_str})")

        if index_str.isdigit():
            # Constant index
            index = int(index_str)

            # Use global argv variable that's set in main wrapper
            argv_ptr = self.get_temp_var()
            arg_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {argv_ptr} = load i8**, i8*** @global_argv, align 8")
            self.llvm_lines.append(f"  {arg_ptr} = getelementptr inbounds i8*, i8** {argv_ptr}, i64 {index}")
            load_var = self.get_temp_var()
            self.llvm_lines.append(f"  {load_var} = load i8*, i8** {arg_ptr}, align 8")
            self.llvm_lines.append(f"  store i8* {load_var}, i8** @global_{var_name}, align 8")
        else:
            # Variable index
            index_var = self.get_temp_var()
            argv_ptr = self.get_temp_var()
            arg_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {index_var} = load i32, i32* @global_{index_str}, align 4")
            self.llvm_lines.append(f"  {argv_ptr} = load i8**, i8*** %argv_ptr, align 8")
            self.llvm_lines.append(f"  {arg_ptr} = getelementptr inbounds i8*, i8** {argv_ptr}, i32 {index_var}")
            load_var = self.get_temp_var()
            self.llvm_lines.append(f"  {load_var} = load i8*, i8** {arg_ptr}, align 8")
            self.llvm_lines.append(f"  store i8* {load_var}, i8** @global_{var_name}, align 8")

        # Add variable to variables list if not already present
        if not self._find_variable(var_name):
            self.variables.append({
                'name': var_name,
                'type': 'string',
                'value': expr
            })
            print(f"   ✅ Added {var_name} to variables list")

        print(f"   ✅ Generated argv({index_str}) assignment for {var_name}")

    def _generate_argc(self, stmt):
        """Generate code for standalone argc() call"""
        self.llvm_lines.append(f"  ; argc()")
        load_var = self.get_temp_var()
        self.llvm_lines.append(f"  {load_var} = load i32, i32* %argc_ptr, align 4")

    def _generate_argv(self, stmt):
        """Generate code for standalone argv() call"""
        content = stmt['content'][5:-1]  # Remove 'argv(' and ')'
        self.llvm_lines.append(f"  ; argv({content})")

        if content.isdigit():
            # Constant index
            index = int(content)
            argv_ptr = self.get_temp_var()
            arg_ptr = self.get_temp_var()
            self.llvm_lines.append(f"  {argv_ptr} = load i8**, i8*** %argv_ptr, align 8")
            self.llvm_lines.append(f"  {arg_ptr} = getelementptr inbounds i8*, i8** {argv_ptr}, i64 {index}")
            load_var = self.get_temp_var()
            self.llvm_lines.append(f"  {load_var} = load i8*, i8** {arg_ptr}, align 8")

    def _find_string_index(self, text):
        """Find index of string in literals"""
        for i, literal in enumerate(self.string_literals):
            if literal == text:
                return i
        return -1

def write_llvm_file(filename, llvm_ir):
    """Write LLVM IR to file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(llvm_ir)
        print(f"✅ Written LLVM IR to: {filename}")
        print(f"   File size: {len(llvm_ir)} characters")
        return True
    except Exception as e:
        print(f"❌ Error writing LLVM file: {e}")
        return False

def main():
    print_header()
    
    if len(sys.argv) != 3:
        print("❌ Usage: python dolet_to_llvm.py input.dolet output.ll")
        print("   Example: python dolet_to_llvm.py app.dolet app.ll")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    print(f"📁 Input:  {input_file}")
    print(f"📁 Output: {output_file}")
    
    # Read source
    source_code = read_dolet_file(input_file)
    if source_code is None:
        sys.exit(1)
    
    # Parse
    statements = parse_dolet_code(source_code)
    if not statements:
        print("❌ No valid statements found")
        sys.exit(1)
    
    # Extract components
    string_literals = extract_string_literals(statements)
    control_blocks = extract_control_flow(statements)
    variables = extract_variables(statements, control_blocks)
    
    # Generate LLVM IR
    generator = LLVMGenerator(statements, string_literals, variables, control_blocks)
    llvm_ir = generator.generate()
    
    # Write output
    if not write_llvm_file(output_file, llvm_ir):
        sys.exit(1)
    
    print(f"\n🎉 SUCCESS!")
    print(f"✅ Converted {input_file} → {output_file}")
    print(f"✅ Ready for compilation with clang")

    # Detect platform and give appropriate linking instructions
    import platform
    if platform.system() == "Windows":
        print(f"   Windows compilation:")
        print(f"   clang {output_file} -o output.exe -lmsvcrt")
        print(f"   OR: clang {output_file} -o output.exe -Wl,/NODEFAULTLIB:libcmt")
        print(f"   OR: gcc {output_file} -o output.exe (if MinGW installed)")
    else:
        print(f"   Linux/Mac compilation:")
        print(f"   clang {output_file} -o output.exe")
        print(f"   OR: gcc {output_file} -o output.exe")

if __name__ == "__main__":
    main()