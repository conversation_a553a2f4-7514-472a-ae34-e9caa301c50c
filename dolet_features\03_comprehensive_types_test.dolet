# Comprehensive Data Types Test for Dolet
# Testing all possible scenarios and edge cases

print "=== COMPREHENSIVE DATA TYPES TEST ==="
print ""

# ========================================
# INTEGER TESTS
# ========================================
print "=== INTEGER TESTS ==="

# Basic integers
int zero = 0
int positive = 42
int negative = -123
int large = 2147483647
int small_negative = -2147483648

print "Basic integers:"
print "zero = " + zero
print "positive = " + positive  
print "negative = " + negative
print "large = " + large
print "small_negative = " + small_negative
print ""

# Integer modifications
print "Integer modifications:"
set zero = 100
set positive = -50
set negative = 999
print "After modification:"
print "zero = " + zero
print "positive = " + positive
print "negative = " + negative
print ""

# ========================================
# FLOAT TESTS  
# ========================================
print "=== FLOAT TESTS ==="

# Basic floats
float zero_float = 0.0
float positive_float = 3.14159
float negative_float = -2.718
float small_decimal = 0.001
float large_decimal = 999.999
float scientific_small = 1.23e-5
float scientific_large = 1.23e5

print "Basic floats:"
print "zero_float = " + zero_float
print "positive_float = " + positive_float
print "negative_float = " + negative_float
print "small_decimal = " + small_decimal
print "large_decimal = " + large_decimal
print "scientific_small = " + scientific_small
print "scientific_large = " + scientific_large
print ""

# Float modifications
print "Float modifications:"
set zero_float = 1.5
set positive_float = -9.99
set negative_float = 0.0001
print "After modification:"
print "zero_float = " + zero_float
print "positive_float = " + positive_float
print "negative_float = " + negative_float
print ""

# ========================================
# DOUBLE TESTS
# ========================================
print "=== DOUBLE TESTS ==="

# Basic doubles
double zero_double = 0.0
double pi_precise = 3.141592653589793
double negative_double = -123.456789012345
double very_small = 1.23e-15
double very_large = 1.23e15

print "Basic doubles:"
print "zero_double = " + zero_double
print "pi_precise = " + pi_precise
print "negative_double = " + negative_double
print "very_small = " + very_small
print "very_large = " + very_large
print ""

# Double modifications
print "Double modifications:"
set zero_double = 2.718281828459045
set pi_precise = -1.414213562373095
print "After modification:"
print "zero_double = " + zero_double
print "pi_precise = " + pi_precise
print ""

# ========================================
# STRING TESTS
# ========================================
print "=== STRING TESTS ==="

# Basic strings
string empty = ""
string simple = "Hello"
string with_spaces = "Hello World"
string with_numbers = "Test123"
string with_symbols = "!@#$%^&*()"
string with_quotes = "She said \"Hello\"" # issue with this also 
string multiline = "Line1\nLine2\nLine3"
string with_tabs = "Col1\tCol2\tCol3"

print "Basic strings:"
print "empty = '" + empty + "'"
print "simple = " + simple
print "with_spaces = " + with_spaces
print "with_numbers = " + with_numbers
print "with_symbols = " + with_symbols
print "with_quotes = " + with_quotes
print "multiline = " + multiline
print "with_tabs = " + with_tabs
print ""

# String modifications
print "String modifications:"
set empty = "Not empty anymore"
set simple = "Modified"
set with_spaces = ""
print "After modification:"
print "empty = " + empty
print "simple = " + simple
print "with_spaces = '" + with_spaces + "'"
print ""

# ========================================
# CHARACTER TESTS
# ========================================
print "=== CHARACTER TESTS ==="

# Basic characters
char letter_upper = 'A'
char letter_lower = 'z'
char digit_char = '9'
char space_char = ' '
char symbol_char = '@'
char newline_char = '\n'
char tab_char = '\t'
char quote_char = '\''
char backslash_char = '\\'

print "Basic characters:"
print "letter_upper = " + letter_upper
print "letter_lower = " + letter_lower
print "digit_char = " + digit_char
print "space_char = '" + space_char + "'"
print "symbol_char = " + symbol_char
print "quote_char = " + quote_char
print "backslash_char = " + backslash_char
print ""

# Character modifications
print "Character modifications:"
set letter_upper = 'B'
set letter_lower = 'Y'
set digit_char = '0'
print "After modification:"
print "letter_upper = " + letter_upper
print "letter_lower = " + letter_lower
print "digit_char = " + digit_char
print ""

# ========================================
# BOOLEAN TESTS
# ========================================
print "=== BOOLEAN TESTS ==="

# Basic booleans
bool true_val = true
bool false_val = false
bool another_true = true
bool another_false = false

print "Basic booleans:"
print "true_val = " + true_val
print "false_val = " + false_val
print "another_true = " + another_true
print "another_false = " + another_false
print ""

# Boolean modifications
print "Boolean modifications:"
set true_val = false
set false_val = true
set another_true = false
set another_false = true
print "After modification:"
print "true_val = " + true_val
print "false_val = " + false_val
print "another_true = " + another_true
print "another_false = " + another_false
print ""

# ========================================
# MIXED TYPE CONCATENATIONS
# ========================================
print "=== MIXED TYPE CONCATENATIONS ==="

int test_int = 42
float test_float = 3.14
string test_string = "Value"
char test_char = 'X'
bool test_bool = true

print "Mixed concatenations:"
print "Int + String: " + test_int + " is the answer"
print "Float + String: " + test_float + " is pi"
print "String + Char: " + test_string + " " + test_char
print "Bool + String: " + test_bool + " or false"
print "Multiple: " + test_string + " " + test_int + " " + test_float + " " + test_char + " " + test_bool
print ""

# ========================================
# EDGE CASES
# ========================================
print "=== EDGE CASES ==="

# Very long string
string long_string = "This is a very long string that contains many words and should test the string handling capabilities of the Dolet compiler"

# Multiple spaces and special characters
string special_string = "   Multiple   Spaces   And   Special   Characters   !@#$%^&*()_+-=[]{}|;':\",./<>?   "

# Numbers as strings
string number_string = "12345"
string float_string = "3.14159"

print "Edge cases:"
print "long_string = " + long_string
print "special_string = " + special_string
print "number_string = " + number_string
print "float_string = " + float_string
print ""

# ========================================
# FINAL SUMMARY
# ========================================
print "=== FINAL SUMMARY ==="
print "All data types tested successfully!"
print "Integer: " + test_int
print "Float: " + test_float
print "String: " + test_string
print "Character: " + test_char
print "Boolean: " + test_bool
print ""
print "=== END OF COMPREHENSIVE TEST ==="
