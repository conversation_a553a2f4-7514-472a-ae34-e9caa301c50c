// 18. Array Operations - sorting, searching, manipulation (Rust version)
// This file demonstrates array operations in Rust for comparison with Dolet

fn main() {
    println!("=== Array Operations Demo (Rust) ===");
    println!();

    // Basic array creation and access
    println!("Basic array creation and access:");
    let numbers = [5, 2, 8, 1, 9, 3];
    println!("Original array: {:?}", numbers);
    println!("Length: {}", numbers.len());
    println!("First element: {}", numbers[0]);
    println!("Last element: {}", numbers[numbers.len() - 1]);
    println!();

    // Array sorting
    println!("Array sorting:");
    let mut sortable = [64, 34, 25, 12, 22, 11, 90];
    println!("Before sorting: {:?}", sortable);
    sortable.sort();
    println!("After sorting: {:?}", sortable);
    
    let mut desc_sortable = [64, 34, 25, 12, 22, 11, 90];
    desc_sortable.sort_by(|a, b| b.cmp(a));
    println!("Descending sort: {:?}", desc_sortable);
    println!();

    // Array searching
    println!("Array searching:");
    let search_array = [10, 20, 30, 40, 50, 60, 70];
    let search_targets = [30, 45, 70, 5];
    
    println!("Search array: {:?}", search_array);
    for target in search_targets {
        match linear_search(&search_array, target) {
            Some(index) => println!("Linear search for {}: found at index {}", target, index),
            None => println!("Linear search for {}: not found", target),
        }
    }
    println!();

    // Binary search (on sorted array)
    println!("Binary search:");
    let sorted_array = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
    println!("Sorted array: {:?}", sorted_array);
    
    for target in [7, 15, 2, 19, 20] {
        match sorted_array.binary_search(&target) {
            Ok(index) => println!("Binary search for {}: found at index {}", target, index),
            Err(_) => println!("Binary search for {}: not found", target),
        }
    }
    println!();

    // Array filtering
    println!("Array filtering:");
    let data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let evens: Vec<i32> = data.iter().filter(|&&x| x % 2 == 0).cloned().collect();
    let odds: Vec<i32> = data.iter().filter(|&&x| x % 2 != 0).cloned().collect();
    let greater_than_5: Vec<i32> = data.iter().filter(|&&x| x > 5).cloned().collect();
    
    println!("Original: {:?}", data);
    println!("Even numbers: {:?}", evens);
    println!("Odd numbers: {:?}", odds);
    println!("Greater than 5: {:?}", greater_than_5);
    println!();

    // Array mapping/transformation
    println!("Array mapping/transformation:");
    let original = [1, 2, 3, 4, 5];
    let doubled: Vec<i32> = original.iter().map(|x| x * 2).collect();
    let squared: Vec<i32> = original.iter().map(|x| x * x).collect();
    let plus_ten: Vec<i32> = original.iter().map(|x| x + 10).collect();
    
    println!("Original: {:?}", original);
    println!("Doubled: {:?}", doubled);
    println!("Squared: {:?}", squared);
    println!("Plus 10: {:?}", plus_ten);
    println!();

    // Array reduction operations
    println!("Array reduction operations:");
    let values = [10, 20, 30, 40, 50];
    let sum: i32 = values.iter().sum();
    let product: i32 = values.iter().product();
    let max_val = *values.iter().max().unwrap();
    let min_val = *values.iter().min().unwrap();
    let average = sum as f64 / values.len() as f64;
    
    println!("Values: {:?}", values);
    println!("Sum: {}", sum);
    println!("Product: {}", product);
    println!("Maximum: {}", max_val);
    println!("Minimum: {}", min_val);
    println!("Average: {:.2}", average);
    println!();

    // Array reversal
    println!("Array reversal:");
    let mut reversible = [1, 2, 3, 4, 5, 6];
    println!("Before reversal: {:?}", reversible);
    reversible.reverse();
    println!("After reversal: {:?}", reversible);
    println!();

    // Array rotation
    println!("Array rotation:");
    let mut rotatable = vec![1, 2, 3, 4, 5];
    println!("Original: {:?}", rotatable);
    
    rotatable.rotate_left(2);
    println!("Rotate left by 2: {:?}", rotatable);
    
    rotatable.rotate_right(3);
    println!("Rotate right by 3: {:?}", rotatable);
    println!();

    // Array concatenation
    println!("Array concatenation:");
    let arr1 = [1, 2, 3];
    let arr2 = [4, 5, 6];
    let arr3 = [7, 8, 9];
    
    let mut concatenated = Vec::new();
    concatenated.extend_from_slice(&arr1);
    concatenated.extend_from_slice(&arr2);
    concatenated.extend_from_slice(&arr3);
    
    println!("Array 1: {:?}", arr1);
    println!("Array 2: {:?}", arr2);
    println!("Array 3: {:?}", arr3);
    println!("Concatenated: {:?}", concatenated);
    println!();

    // Array slicing
    println!("Array slicing:");
    let full_array = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    let slice1 = &full_array[2..6];
    let slice2 = &full_array[..4];
    let slice3 = &full_array[6..];
    
    println!("Full array: {:?}", full_array);
    println!("Slice [2..6]: {:?}", slice1);
    println!("Slice [..4]: {:?}", slice2);
    println!("Slice [6..]: {:?}", slice3);
    println!();

    // Array comparison
    println!("Array comparison:");
    let array_a = [1, 2, 3, 4, 5];
    let array_b = [1, 2, 3, 4, 5];
    let array_c = [1, 2, 3, 4, 6];
    
    println!("Array A: {:?}", array_a);
    println!("Array B: {:?}", array_b);
    println!("Array C: {:?}", array_c);
    println!("A == B: {}", array_a == array_b);
    println!("A == C: {}", array_a == array_c);
    println!("B == C: {}", array_b == array_c);
    println!();

    // Array deduplication
    println!("Array deduplication:");
    let mut with_duplicates = vec![1, 2, 2, 3, 3, 3, 4, 4, 5];
    println!("With duplicates: {:?}", with_duplicates);
    
    with_duplicates.sort();
    with_duplicates.dedup();
    println!("After deduplication: {:?}", with_duplicates);
    println!();

    // Array partitioning
    println!("Array partitioning:");
    let mixed = vec![1, 4, 2, 7, 3, 8, 5, 9, 6];
    let (evens, odds): (Vec<i32>, Vec<i32>) = mixed.iter().partition(|&&x| x % 2 == 0);
    
    println!("Mixed array: {:?}", mixed);
    println!("Even partition: {:?}", evens);
    println!("Odd partition: {:?}", odds);
    println!();

    // Array chunking
    println!("Array chunking:");
    let long_array = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
    println!("Long array: {:?}", long_array);
    
    println!("Chunks of 3:");
    for (i, chunk) in long_array.chunks(3).enumerate() {
        println!("  Chunk {}: {:?}", i + 1, chunk);
    }
    println!();

    // Array windowing
    println!("Array windowing:");
    let sequence = [1, 2, 3, 4, 5, 6, 7];
    println!("Sequence: {:?}", sequence);
    
    println!("Windows of size 3:");
    for (i, window) in sequence.windows(3).enumerate() {
        println!("  Window {}: {:?}", i + 1, window);
    }
    println!();

    // Custom array operations
    println!("Custom array operations:");
    let test_array = [3, 1, 4, 1, 5, 9, 2, 6, 5, 3];
    
    println!("Test array: {:?}", test_array);
    println!("Count of 1s: {}", count_occurrences(&test_array, 1));
    println!("Count of 5s: {}", count_occurrences(&test_array, 5));
    
    let second_largest = find_second_largest(&test_array);
    match second_largest {
        Some(val) => println!("Second largest: {}", val),
        None => println!("No second largest found"),
    }
    
    let is_sorted = is_array_sorted(&test_array);
    println!("Is sorted: {}", is_sorted);
    
    let mut sortable_copy = test_array;
    sortable_copy.sort();
    let is_sorted_after = is_array_sorted(&sortable_copy);
    println!("Is sorted after sorting: {}", is_sorted_after);
    println!();

    // Array statistics
    println!("Array statistics:");
    let stats_data = [85, 92, 78, 96, 88, 73, 89, 94, 81, 87];
    let stats = calculate_statistics(&stats_data);
    
    println!("Data: {:?}", stats_data);
    println!("Mean: {:.2}", stats.mean);
    println!("Median: {:.2}", stats.median);
    println!("Mode: {:?}", stats.mode);
    println!("Range: {}", stats.range);
    println!("Standard deviation: {:.2}", stats.std_dev);
    println!();

    println!("=== End of Array Operations Demo ===");
}

// Linear search implementation
fn linear_search(arr: &[i32], target: i32) -> Option<usize> {
    for (index, &value) in arr.iter().enumerate() {
        if value == target {
            return Some(index);
        }
    }
    None
}

// Count occurrences of a value
fn count_occurrences(arr: &[i32], target: i32) -> usize {
    arr.iter().filter(|&&x| x == target).count()
}

// Find second largest element
fn find_second_largest(arr: &[i32]) -> Option<i32> {
    if arr.len() < 2 {
        return None;
    }
    
    let mut unique_values: Vec<i32> = arr.iter().cloned().collect();
    unique_values.sort_unstable();
    unique_values.dedup();
    
    if unique_values.len() < 2 {
        None
    } else {
        Some(unique_values[unique_values.len() - 2])
    }
}

// Check if array is sorted
fn is_array_sorted(arr: &[i32]) -> bool {
    arr.windows(2).all(|w| w[0] <= w[1])
}

// Statistics structure
struct Statistics {
    mean: f64,
    median: f64,
    mode: Option<i32>,
    range: i32,
    std_dev: f64,
}

// Calculate array statistics
fn calculate_statistics(arr: &[i32]) -> Statistics {
    let sum: i32 = arr.iter().sum();
    let mean = sum as f64 / arr.len() as f64;
    
    let mut sorted = arr.to_vec();
    sorted.sort();
    let median = if sorted.len() % 2 == 0 {
        let mid = sorted.len() / 2;
        (sorted[mid - 1] + sorted[mid]) as f64 / 2.0
    } else {
        sorted[sorted.len() / 2] as f64
    };
    
    let max_val = *arr.iter().max().unwrap();
    let min_val = *arr.iter().min().unwrap();
    let range = max_val - min_val;
    
    // Calculate standard deviation
    let variance: f64 = arr.iter()
        .map(|&x| (x as f64 - mean).powi(2))
        .sum::<f64>() / arr.len() as f64;
    let std_dev = variance.sqrt();
    
    // Find mode (most frequent value)
    let mut frequency_map = std::collections::HashMap::new();
    for &value in arr {
        *frequency_map.entry(value).or_insert(0) += 1;
    }
    
    let mode = frequency_map.iter()
        .max_by_key(|(_, &count)| count)
        .map(|(&value, _)| value);
    
    Statistics {
        mean,
        median,
        mode,
        range,
        std_dev,
    }
}
