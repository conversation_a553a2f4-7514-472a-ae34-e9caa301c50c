# 11. Built-in Functions - Standard Library Functions
# This file demonstrates built-in functions in Dolet

print "=== Built-in Functions Demo ==="
print ""

# Mathematical functions
print "Mathematical Functions:"

# Basic math
int a = 16
int b = -7
float x = 3.14159
float y = 2.71828

print "abs(" + b + ") = " + abs(b)
print "sqrt(" + a + ") = " + sqrt(a)
print "pow(2, 3) = " + pow(2, 3)
print "pow(" + x + ", 2) = " + pow(x, 2)
print ""

# Trigonometric functions
print "Trigonometric Functions:"
float angle = 45.0  # degrees
print "sin(" + angle + "°) = " + sin(angle)
print "cos(" + angle + "°) = " + cos(angle)
print "tan(" + angle + "°) = " + tan(angle)
print ""

# Logarithmic functions
print "Logarithmic Functions:"
float num = 10.0
print "log(" + num + ") = " + log(num)
print "log10(" + num + ") = " + log10(num)
print "exp(1) = " + exp(1)
print ""

# Rounding functions
print "Rounding Functions:"
float decimal = 3.7
print "floor(" + decimal + ") = " + floor(decimal)
print "ceil(" + decimal + ") = " + ceil(decimal)
print "round(" + decimal + ") = " + round(decimal)
print ""

# String functions
print "String Functions:"
string text = "Hello World"
string lower_text = "hello world"
string upper_text = "HELLO WORLD"

print "Original: '" + text + "'"
print "length('" + text + "') = " + length(text)
print "upper('" + text + "') = " + upper(text)
print "lower('" + text + "') = " + lower(text)
print ""

# String search and manipulation
print "String Search and Manipulation:"
string sentence = "The quick brown fox jumps"
string search_word = "fox"
string old_word = "quick"
string new_word = "slow"

print "Text: '" + sentence + "'"
print "contains('" + sentence + "', '" + search_word + "') = " + contains(sentence, search_word)
print "index_of('" + sentence + "', '" + search_word + "') = " + index_of(sentence, search_word)
print "replace('" + sentence + "', '" + old_word + "', '" + new_word + "') = " + replace(sentence, old_word, new_word)
print ""

# String extraction
print "String Extraction:"
string full_text = "Programming is fun"
print "Original: '" + full_text + "'"
print "substring('" + full_text + "', 0, 11) = '" + substring(full_text, 0, 11) + "'"
print "left('" + full_text + "', 11) = '" + left(full_text, 11) + "'"
print "right('" + full_text + "', 7) = '" + right(full_text, 7) + "'"
print ""

# Array functions
print "Array Functions:"
array int numbers = [5, 2, 8, 1, 9, 3]
print "Array: [5, 2, 8, 1, 9, 3]"
print "size(numbers) = " + size(numbers)
print "max(numbers) = " + max(numbers)
print "min(numbers) = " + min(numbers)
print "sum(numbers) = " + sum(numbers)
print "average(numbers) = " + average(numbers)
print ""

# Array manipulation
print "Array Manipulation:"
array string fruits = ["apple", "banana", "orange"]
print "Original fruits: " + join(fruits, ", ")
push(fruits, "grape")
print "After push('grape'): " + join(fruits, ", ")
string removed = pop(fruits)
print "After pop(): " + join(fruits, ", ") + " (removed: " + removed + ")"
print ""

# Type conversion functions
print "Type Conversion Functions:"
string num_str = "123"
string float_str = "45.67"
int int_val = 789
float float_val = 12.34

print "to_int('" + num_str + "') = " + to_int(num_str)
print "to_float('" + float_str + "') = " + to_float(float_str)
print "to_string(" + int_val + ") = '" + to_string(int_val) + "'"
print "to_string(" + float_val + ") = '" + to_string(float_val) + "'"
print ""

# Date and time functions
print "Date and Time Functions:"
print "now() = " + now()
print "today() = " + today()
print "year() = " + year()
print "month() = " + month()
print "day() = " + day()
print "hour() = " + hour()
print "minute() = " + minute()
print "second() = " + second()
print ""

# Random functions
print "Random Functions:"
print "random() = " + random()
print "random_int(1, 10) = " + random_int(1, 10)
print "random_float(0.0, 1.0) = " + random_float(0.0, 1.0)
print ""

# File functions
print "File Functions:"
string filename = "test.txt"
print "file_exists('" + filename + "') = " + file_exists(filename)
print "file_size('" + filename + "') = " + file_size(filename)
print "Note: File operations would interact with actual filesystem"
print ""

# System functions
print "System Functions:"
print "get_env('PATH') = " + get_env("PATH")
print "get_user() = " + get_user()
print "get_os() = " + get_os()
print ""

# Validation functions
print "Validation Functions:"
string email = "<EMAIL>"
string phone = "************"
string url = "https://www.example.com"

print "is_email('" + email + "') = " + is_email(email)
print "is_phone('" + phone + "') = " + is_phone(phone)
print "is_url('" + url + "') = " + is_url(url)
print "is_numeric('123') = " + is_numeric("123")
print "is_alpha('ABC') = " + is_alpha("ABC")
print ""

# Utility functions
print "Utility Functions:"
print "sleep(1) - pauses execution for 1 second"
sleep(1)
print "Resumed after sleep"

print "print_debug('Debug message') - prints debug info"
print_debug("This is a debug message")
print ""

# Custom function using built-ins
fun analyze_text(text) {
    print "Analyzing text: '" + text + "'"
    print "Length: " + length(text)
    print "Uppercase: " + upper(text)
    print "Lowercase: " + lower(text)
    print "Word count: " + count_words(text)
    print "Contains 'the': " + contains(lower(text), "the")
}

print "Text Analysis Function:"
analyze_text("The Quick Brown Fox")
print ""

print "=== End of Built-in Functions Demo ==="
