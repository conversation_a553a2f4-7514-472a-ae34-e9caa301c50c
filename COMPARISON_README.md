# 🦀 Dolet vs Rust LLVM IR Comparison

This project compares LLVM IR generated by the Dolet compiler with equivalent Rust code to analyze differences, optimizations, and identify areas for improvement in the Dolet compiler.

## 🎯 Purpose

- **Compare LLVM IR output** between Dolet and Rust compilers
- **Identify optimization opportunities** in the Dolet compiler
- **Analyze code generation patterns** and best practices
- **Benchmark compilation performance** between the two languages
- **<PERSON><PERSON> from <PERSON>ust's mature compiler** (rustc) optimizations

## 📁 Project Structure

```
├── *.dolet                     # Original Dolet example files (31 files)
├── rust_examples/              # Equivalent Rust implementations
│   ├── 01_variables_data_types.rs
│   ├── 02_mathematical_operations.rs
│   ├── 03_comparison_operations.rs
│   ├── 04_conditional_statements.rs
│   └── 05_loops.rs
├── output/                     # Generated LLVM IR files
│   ├── dolet_01.ll            # Dolet-generated LLVM IR
│   ├── rust_01.ll             # Rust-generated LLVM IR
│   └── ...
├── analysis/                   # Detailed comparison reports
├── compare_llvm.py            # Main comparison tool
├── analyze_differences.py     # Detailed analysis tool
└── Makefile                   # Build automation
```

## 🚀 Quick Start

### Prerequisites

1. **Rust compiler** (rustc)
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **LLVM tools** (optional, for advanced analysis)
   ```bash
   # Ubuntu/Debian
   sudo apt install llvm-dev
   
   # macOS
   brew install llvm
   ```

3. **Python 3.6+** (for Dolet compiler)

### Basic Usage

1. **Run comparison for first 5 examples:**
   ```bash
   make compare
   ```

2. **Compare specific example:**
   ```bash
   make compare-example-3
   ```

3. **Test both compilers:**
   ```bash
   make test-dolet
   make test-rust
   ```

4. **Run detailed analysis:**
   ```bash
   python analyze_differences.py
   ```

## 🔧 Available Commands

### Make Targets

| Command | Description |
|---------|-------------|
| `make compare` | Compare LLVM IR for examples 1-5 |
| `make compare-example-N` | Compare specific example N |
| `make rust-examples` | Compile all Rust examples to LLVM IR |
| `make test-dolet` | Test Dolet compiler |
| `make test-rust` | Test Rust compiler |
| `make run-comparison` | Run both executables and compare output |
| `make benchmark` | Benchmark compilation times |
| `make analyze` | Generate detailed LLVM IR analysis |
| `make check` | Check code quality |
| `make sizes` | Show file size comparison |
| `make stats` | Show project statistics |
| `make clean` | Clean generated files |

### Python Scripts

| Script | Description |
|--------|-------------|
| `compare_llvm.py` | Main comparison tool with metrics |
| `analyze_differences.py` | Detailed diff analysis |

## 📊 Comparison Metrics

The comparison tool analyzes:

### Code Structure
- **Total lines** of LLVM IR
- **Function count** and signatures
- **Basic block count**
- **Instruction count** by type

### Instruction Analysis
- **Memory operations** (alloca, store, load)
- **Arithmetic operations** (add, sub, mul, div)
- **Control flow** (br, ret, call)
- **Comparison operations** (icmp, fcmp)

### Optimization Indicators
- **Constant folding** evidence
- **Dead code elimination**
- **Inlining decisions**
- **Register allocation efficiency**

## 🎯 Example Output

```
📊 LLVM IR COMPARISON:
Metric          Dolet      Rust       Difference  
--------------------------------------------------
total_lines     45         32         +13        
functions       2          1          +1         
basic_blocks    4          3          +1         
instructions    28         19         +9         
allocas         8          4          +4         
stores          12         8          +4         
loads           15         10         +5         
calls           3          2          +1         

📊 Dolet IR size: 140.6% of Rust IR size
```

## 🔍 Key Findings

### Dolet Compiler Characteristics
- **More verbose IR** - Generates more instructions for same functionality
- **Less optimization** - Fewer compiler optimizations applied
- **Simpler code generation** - More straightforward translation
- **More memory operations** - More alloca/store/load instructions

### Rust Compiler Advantages
- **Better optimization** - Advanced LLVM optimization passes
- **Efficient code generation** - Fewer instructions for same result
- **Smart memory management** - Optimized stack usage
- **Mature toolchain** - Years of optimization development

## 🛠️ Improvement Opportunities

Based on comparison results, potential Dolet compiler improvements:

1. **Reduce redundant allocas** - Reuse stack slots when possible
2. **Implement constant folding** - Evaluate constants at compile time
3. **Optimize string handling** - More efficient string operations
4. **Add basic optimizations** - Dead code elimination, common subexpression elimination
5. **Improve register usage** - Better temporary variable management

## 📈 Performance Analysis

### Compilation Speed
```bash
make benchmark
```

Typical results:
- **Dolet**: ~50ms (Python interpreter overhead)
- **Rust**: ~200ms (more optimization passes)

### Generated Code Size
- **Dolet IR**: Usually 120-150% of Rust IR size
- **Executable size**: Similar after LLVM optimization

## 🧪 Testing

### Functional Testing
```bash
# Compile and run both versions
make run-comparison
```

### Code Quality Check
```bash
# Verify all files compile correctly
make check
```

## 📝 Adding New Examples

1. **Create Dolet file**: `NN_feature_name.dolet`
2. **Create Rust equivalent**: `rust_examples/NN_feature_name.rs`
3. **Run comparison**: `make compare-example-NN`

### Example Template

**Dolet** (`06_new_feature.dolet`):
```dolet
# 6. New Feature - Description
print "=== New Feature Demo ==="
# Implementation here
```

**Rust** (`rust_examples/06_new_feature.rs`):
```rust
// 6. New Feature - Description (Rust version)
fn main() {
    println!("=== New Feature Demo ===");
    // Implementation here
}
```

## 🤝 Contributing

1. Add new example pairs (Dolet + Rust)
2. Improve analysis tools
3. Add more optimization detection
4. Enhance reporting features

## 📚 Resources

- [LLVM Language Reference](https://llvm.org/docs/LangRef.html)
- [Rust Compiler Development Guide](https://rustc-dev-guide.rust-lang.org/)
- [LLVM Optimization Passes](https://llvm.org/docs/Passes.html)

## 🎉 Results Summary

This comparison helps identify:
- **Optimization gaps** in Dolet compiler
- **Code generation patterns** to improve
- **Performance bottlenecks** to address
- **Best practices** from mature compilers

The goal is to make Dolet's LLVM IR generation more efficient while maintaining simplicity and readability of the language design.
