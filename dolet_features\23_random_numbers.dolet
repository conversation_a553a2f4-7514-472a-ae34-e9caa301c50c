# 23. Random Numbers - Random Number Generation and Applications
# This file demonstrates random number generation in Dolet

print "=== Random Numbers Demo ==="
print ""

# Basic random number generation (simulation)
print "Basic Random Number Generation:"

# Linear Congruential Generator implementation
int random_seed = 12345

fun next_random() {
    set random_seed = (random_seed * 1664525 + 1013904223) % 2147483647
    return random_seed
}

fun random_int(min, max) {
    int rand_val = next_random()
    return min + (rand_val % (max - min + 1))
}

fun random_float(min, max) {
    int rand_val = next_random()
    float normalized = (rand_val % 10000) / 10000.0
    return min + normalized * (max - min)
}

print "Random integers (1-100):"
for i = 1 to 10
    int rand_num = random_int(1, 100)
    print "Random[" + i + "] = " + rand_num

print ""
print "Random floats (0.0-1.0):"
for i = 1 to 5
    float rand_float = random_float(0.0, 1.0)
    print "Random[" + i + "] = " + rand_float
print ""

# Random array shuffling
print "Random Array Shuffling:"

fun shuffle_array(arr, size) {
    print "Shuffling array of size " + size
    
    for i = size - 1 to 1 step -1
        int j = random_int(0, i)
        print "  Swapping arr[" + i + "] with arr[" + j + "]"
        
        int temp = arr[i]
        set arr[i] = arr[j]
        set arr[j] = temp
}

array int deck = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
print "Original array: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
shuffle_array(deck, 10)

print "Shuffled array: ["
for i = 0 to 9
    if i == 9
        print deck[i] + "]"
    else
        print deck[i] + ", "
print ""

# Random sampling
print "Random Sampling:"

fun random_sample(arr, size, sample_size) {
    print "Taking random sample of " + sample_size + " items from " + size + " items"
    array int sample = [0, 0, 0, 0, 0]  # Max sample size
    
    for i = 0 to sample_size - 1
        int random_index = random_int(0, size - 1)
        set sample[i] = arr[random_index]
        print "  Sample[" + i + "] = arr[" + random_index + "] = " + sample[i]
    
    return sample
}

array int population = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
print "Population: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
array int sample = random_sample(population, 10, 5)
print ""

# Random password generation
print "Random Password Generation:"

fun generate_password(length) {
    print "Generating password of length " + length
    string password = ""
    
    # Simulated character sets
    array string chars = ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j"]
    array string numbers = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
    array string symbols = ["!", "@", "#", "$", "%", "&", "*", "+", "=", "?"]
    
    for i = 1 to length
        int char_type = random_int(1, 3)
        
        if char_type == 1
            int char_index = random_int(0, 9)
            set password = password + chars[char_index]
        elif char_type == 2
            int num_index = random_int(0, 9)
            set password = password + numbers[num_index]
        else
            int sym_index = random_int(0, 9)
            set password = password + symbols[sym_index]
    
    return password
}

string pwd1 = generate_password(8)
string pwd2 = generate_password(12)
print "8-character password: " + pwd1
print "12-character password: " + pwd2
print ""

# Random color generation
print "Random Color Generation:"

fun generate_random_color() {
    int red = random_int(0, 255)
    int green = random_int(0, 255)
    int blue = random_int(0, 255)
    
    print "RGB(" + red + ", " + green + ", " + blue + ")"
    return red * 65536 + green * 256 + blue  # Convert to single integer
}

print "Random colors:"
for i = 1 to 5
    int color = generate_random_color()
print ""

# Monte Carlo simulation
print "Monte Carlo Simulation (Pi Estimation):"

fun estimate_pi(iterations) {
    print "Estimating Pi using " + iterations + " random points"
    int inside_circle = 0
    
    for i = 1 to iterations
        float x = random_float(-1.0, 1.0)
        float y = random_float(-1.0, 1.0)
        
        float distance = (x * x) + (y * y)
        if distance <= 1.0
            set inside_circle = inside_circle + 1
        
        if i % 1000 == 0
            float current_pi = 4.0 * inside_circle / i
            print "  After " + i + " iterations: Pi ≈ " + current_pi
    
    float final_pi = 4.0 * inside_circle / iterations
    print "Final estimate: Pi ≈ " + final_pi
    return final_pi
}

float pi_estimate = estimate_pi(10000)
print ""

# Random walk simulation
print "Random Walk Simulation:"

fun random_walk(steps) {
    print "Performing random walk with " + steps + " steps"
    int x = 0
    int y = 0
    
    print "Starting position: (" + x + ", " + y + ")"
    
    for i = 1 to steps
        int direction = random_int(1, 4)
        
        if direction == 1
            set y = y + 1  # North
            print "  Step " + i + ": North -> (" + x + ", " + y + ")"
        elif direction == 2
            set x = x + 1  # East
            print "  Step " + i + ": East -> (" + x + ", " + y + ")"
        elif direction == 3
            set y = y - 1  # South
            print "  Step " + i + ": South -> (" + x + ", " + y + ")"
        else
            set x = x - 1  # West
            print "  Step " + i + ": West -> (" + x + ", " + y + ")"
    
    int distance = (x * x) + (y * y)  # Squared distance from origin
    print "Final position: (" + x + ", " + y + ")"
    print "Distance from origin: " + distance + " (squared)"
}

random_walk(10)
print ""

# Random quiz generator
print "Random Quiz Generator:"

fun generate_quiz_question() {
    int num1 = random_int(1, 20)
    int num2 = random_int(1, 20)
    int operation = random_int(1, 4)
    
    if operation == 1
        print "Question: " + num1 + " + " + num2 + " = ?"
        print "Answer: " + (num1 + num2)
    elif operation == 2
        print "Question: " + num1 + " - " + num2 + " = ?"
        print "Answer: " + (num1 - num2)
    elif operation == 3
        print "Question: " + num1 + " × " + num2 + " = ?"
        print "Answer: " + (num1 * num2)
    else
        print "Question: " + (num1 * num2) + " ÷ " + num1 + " = ?"
        print "Answer: " + num2
}

print "Random math quiz questions:"
for i = 1 to 5
    print "Question " + i + ":"
    generate_quiz_question()
    print ""

# Random data generation for testing
print "Random Test Data Generation:"

fun generate_test_user() {
    array string first_names = ["Ahmed", "Sara", "Ali", "Fatima", "Omar"]
    array string last_names = ["Hassan", "Mohamed", "Ali", "Ibrahim", "Mahmoud"]
    array string cities = ["Cairo", "Alexandria", "Giza", "Luxor", "Aswan"]
    
    int first_index = random_int(0, 4)
    int last_index = random_int(0, 4)
    int city_index = random_int(0, 4)
    int age = random_int(18, 65)
    
    print "Generated user:"
    print "  Name: " + first_names[first_index] + " " + last_names[last_index]
    print "  Age: " + age
    print "  City: " + cities[city_index]
}

print "Random test users:"
for i = 1 to 3
    print "User " + i + ":"
    generate_test_user()
    print ""

print "=== End of Random Numbers Demo ==="
