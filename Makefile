# Makefile for Do<PERSON> vs Rust LLVM IR Comparison
# Usage: make compare, make rust-examples, make clean

.PHONY: all compare rust-examples clean test-dolet test-rust help

# Default target
all: help

# Compare LLVM IR between Dolet and Rust
compare: rust-examples
	@echo "🔍 Starting LLVM IR comparison..."
	python compare_llvm.py 1 2 3 4 5

# Compare specific examples
compare-example-%:
	@echo "🔍 Comparing example $*..."
	python compare_llvm.py $*

# Compile all Rust examples
rust-examples:
	@echo "🦀 Compiling Rust examples..."
	@mkdir -p output
	@for file in rust_examples/*.rs; do \
		if [ -f "$$file" ]; then \
			echo "Compiling $$file..."; \
			rustc --emit=llvm-ir -O "$$file" -o "output/$$(basename $$file .rs).ll" || true; \
		fi \
	done

# Test Dolet compiler with first example
test-dolet:
	@echo "🐍 Testing Dolet compiler..."
	python dolet_to_llvm.py 01_variables_data_types.dolet output/test_dolet.ll
	@if [ -f output/test_dolet.ll ]; then \
		echo "✅ Dolet compilation successful"; \
		clang output/test_dolet.ll -o output/test_dolet.exe || true; \
	else \
		echo "❌ Dolet compilation failed"; \
	fi

# Test Rust compiler with first example
test-rust:
	@echo "🦀 Testing Rust compiler..."
	rustc rust_examples/01_variables_data_types.rs -o output/test_rust.exe
	@if [ -f output/test_rust.exe ]; then \
		echo "✅ Rust compilation successful"; \
	else \
		echo "❌ Rust compilation failed"; \
	fi

# Run both executables for comparison
run-comparison: test-dolet test-rust
	@echo "🏃 Running Dolet executable:"
	@./output/test_dolet.exe || echo "Failed to run Dolet executable"
	@echo ""
	@echo "🏃 Running Rust executable:"
	@./output/test_rust.exe || echo "Failed to run Rust executable"

# Generate detailed LLVM IR analysis
analyze:
	@echo "📊 Analyzing LLVM IR files..."
	@mkdir -p analysis
	@for file in output/*.ll; do \
		if [ -f "$$file" ]; then \
			echo "Analyzing $$file..."; \
			llvm-dis "$$file" -o "analysis/$$(basename $$file .ll).readable.ll" 2>/dev/null || true; \
		fi \
	done

# Create all Rust examples (for development)
create-rust-examples:
	@echo "📝 Creating remaining Rust examples..."
	@mkdir -p rust_examples
	@echo "// TODO: Implement remaining Rust examples" > rust_examples/06_functions.rs
	@echo "// TODO: Implement remaining Rust examples" > rust_examples/07_arrays.rs
	@echo "// TODO: Implement remaining Rust examples" > rust_examples/08_string_operations.rs

# Benchmark compilation times
benchmark:
	@echo "⏱️  Benchmarking compilation times..."
	@echo "Dolet compilation time:"
	@time python dolet_to_llvm.py 01_variables_data_types.dolet output/benchmark_dolet.ll
	@echo ""
	@echo "Rust compilation time:"
	@time rustc --emit=llvm-ir rust_examples/01_variables_data_types.rs -o output/benchmark_rust.ll

# Check code quality
check:
	@echo "🔍 Checking code quality..."
	@echo "Checking Rust code:"
	@for file in rust_examples/*.rs; do \
		if [ -f "$$file" ]; then \
			rustc --emit=metadata "$$file" -o /dev/null 2>/dev/null && echo "✅ $$file" || echo "❌ $$file"; \
		fi \
	done
	@echo ""
	@echo "Checking Dolet code:"
	@for file in *.dolet; do \
		if [ -f "$$file" ]; then \
			python dolet_to_llvm.py "$$file" /dev/null >/dev/null 2>&1 && echo "✅ $$file" || echo "❌ $$file"; \
		fi \
	done

# Clean generated files
clean:
	@echo "🧹 Cleaning generated files..."
	rm -rf output/
	rm -rf analysis/
	rm -f *.exe
	rm -f rust_examples/*.exe
	rm -f *.ll

# Show file sizes comparison
sizes:
	@echo "📏 File size comparison:"
	@echo "Dolet source files:"
	@du -h *.dolet | head -5
	@echo ""
	@echo "Rust source files:"
	@du -h rust_examples/*.rs | head -5
	@echo ""
	@if [ -d output ]; then \
		echo "Generated LLVM IR files:"; \
		du -h output/*.ll | head -10; \
	fi

# Show statistics
stats:
	@echo "📊 Project Statistics:"
	@echo "Dolet files: $$(ls -1 *.dolet 2>/dev/null | wc -l)"
	@echo "Rust files: $$(ls -1 rust_examples/*.rs 2>/dev/null | wc -l)"
	@echo "Total Dolet lines: $$(cat *.dolet 2>/dev/null | wc -l)"
	@echo "Total Rust lines: $$(cat rust_examples/*.rs 2>/dev/null | wc -l)"
	@if [ -d output ]; then \
		echo "Generated LLVM files: $$(ls -1 output/*.ll 2>/dev/null | wc -l)"; \
	fi

# Help target
help:
	@echo "🚀 Dolet vs Rust LLVM IR Comparison Tool"
	@echo "========================================"
	@echo ""
	@echo "Available targets:"
	@echo "  compare              - Compare LLVM IR for examples 1-5"
	@echo "  compare-example-N    - Compare specific example N"
	@echo "  rust-examples        - Compile all Rust examples to LLVM IR"
	@echo "  test-dolet          - Test Dolet compiler"
	@echo "  test-rust           - Test Rust compiler"
	@echo "  run-comparison      - Run both executables and compare output"
	@echo "  analyze             - Generate detailed LLVM IR analysis"
	@echo "  benchmark           - Benchmark compilation times"
	@echo "  check               - Check code quality"
	@echo "  sizes               - Show file size comparison"
	@echo "  stats               - Show project statistics"
	@echo "  clean               - Clean generated files"
	@echo "  help                - Show this help"
	@echo ""
	@echo "Examples:"
	@echo "  make compare                 # Compare first 5 examples"
	@echo "  make compare-example-3       # Compare only example 3"
	@echo "  make benchmark              # Time compilation speed"
	@echo "  make run-comparison         # Run and compare outputs"
