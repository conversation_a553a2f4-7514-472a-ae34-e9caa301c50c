# 20. String Manipulation - Advanced String Processing
# This file demonstrates advanced string manipulation in Dolet

print "=== String Manipulation Demo ==="
print ""

# String length and basic operations
print "String Length and Basic Operations:"
string text1 = "Hello World"
string text2 = "Programming"
string empty = ""

print "text1 = '" + text1 + "'"
print "text2 = '" + text2 + "'"
print "empty = '" + empty + "'"
print "Length simulation (would use built-in length function):"
print "length(text1) would return 11"
print "length(text2) would return 11"
print "length(empty) would return 0"
print ""

# String concatenation and building
print "String Concatenation and Building:"
string first_name = "Ahmed"
string last_name = "<PERSON>"
string title = "Dr."
string full_name = title + " " + first_name + " " + last_name

print "Building full name:"
print "first_name = '" + first_name + "'"
print "last_name = '" + last_name + "'"
print "title = '" + title + "'"
print "full_name = '" + full_name + "'"
print ""

# String repetition simulation
print "String Repetition Simulation:"
string pattern = "Hello "
string repeated = ""

for i = 1 to 5
    set repeated = repeated + pattern

print "pattern = '" + pattern + "'"
print "repeated 5 times = '" + repeated + "'"
print ""

# String comparison
print "String Comparison:"
string password1 = "secret123"
string password2 = "secret123"
string password3 = "different"

print "password1 = '" + password1 + "'"
print "password2 = '" + password2 + "'"
print "password3 = '" + password3 + "'"

if password1 == password2
    print "password1 equals password2: TRUE"
else
    print "password1 equals password2: FALSE"

if password1 == password3
    print "password1 equals password3: TRUE"
else
    print "password1 equals password3: FALSE"
print ""

# String search simulation
print "String Search Simulation:"
string haystack = "The quick brown fox jumps over the lazy dog"
string needle1 = "fox"
string needle2 = "cat"

print "Text: '" + haystack + "'"
print "Searching for '" + needle1 + "': (would use contains() function)"
print "Searching for '" + needle2 + "': (would use contains() function)"
print "Note: Actual implementation would return true/false"
print ""

# String case conversion simulation
print "String Case Conversion Simulation:"
string mixed_case = "Hello World Programming"
print "Original: '" + mixed_case + "'"
print "Uppercase: 'HELLO WORLD PROGRAMMING' (would use upper() function)"
print "Lowercase: 'hello world programming' (would use lower() function)"
print "Title Case: 'Hello World Programming' (would use title() function)"
print ""

# String splitting simulation
print "String Splitting Simulation:"
string csv_data = "Ahmed,25,Cairo,Engineer"
print "CSV data: '" + csv_data + "'"
print "Split by comma would produce:"
print "  [0] = 'Ahmed'"
print "  [1] = '25'"
print "  [2] = 'Cairo'"
print "  [3] = 'Engineer'"

# Manual parsing simulation
string name = ""
string age = ""
string city = ""
string job = ""

# Simplified parsing (in real implementation would use split function)
name = "Ahmed"
age = "25"
city = "Cairo"
job = "Engineer"

print "Parsed values:"
print "Name: " + name
print "Age: " + age
print "City: " + city
print "Job: " + job
print ""

# String replacement simulation
print "String Replacement Simulation:"
string template = "Hello NAME, welcome to PLACE!"
string personalized = template  # In real implementation: replace(template, "NAME", "Ahmed")
print "Template: '" + template + "'"
print "After replacement: 'Hello Ahmed, welcome to Cairo!'"
print "Note: Would use replace() function in actual implementation"
print ""

# String trimming simulation
print "String Trimming Simulation:"
string padded = "   Hello World   "
print "Original: '" + padded + "'"
print "Trimmed: 'Hello World' (would use trim() function)"
print "Left trim: 'Hello World   ' (would use ltrim() function)"
print "Right trim: '   Hello World' (would use rtrim() function)"
print ""

# String validation
print "String Validation:"

fun is_valid_email(email) {
    print "Validating email: '" + email + "'"
    # Simplified validation
    if email == ""
        print "  Empty email - invalid"
        return false
    
    # Check for @ symbol (simplified)
    bool has_at = true  # Would use contains(email, "@")
    bool has_dot = true # Would use contains(email, ".")
    
    if has_at and has_dot
        print "  Email appears valid"
        return true
    else
        print "  Email missing @ or . - invalid"
        return false
}

bool email1_valid = is_valid_email("<EMAIL>")
bool email2_valid = is_valid_email("invalid-email")
bool email3_valid = is_valid_email("")
print ""

# String formatting
print "String Formatting:"
string name_fmt = "Alice"
int age_fmt = 30
float salary_fmt = 5000.50

string formatted = "Employee: " + name_fmt + ", Age: " + age_fmt + ", Salary: $" + salary_fmt
print "Formatted string: '" + formatted + "'"

# Template-based formatting
string template_fmt = "Name: {name}, Age: {age}, Salary: ${salary}"
print "Template: '" + template_fmt + "'"
print "Would be formatted to: 'Name: Alice, Age: 30, Salary: $5000.50'"
print ""

# String reversal
print "String Reversal:"

fun reverse_string_manual(str) {
    print "Reversing string: '" + str + "'"
    # In actual implementation, would reverse character by character
    print "Reversed result would be calculated here"
    return str  # Simplified return
}

string original = "Programming"
string reversed = reverse_string_manual(original)
print "Original: '" + original + "'"
print "Reversed: (would be 'gnimmargorP')"
print ""

# Palindrome check
print "Palindrome Check:"

fun is_palindrome(str) {
    print "Checking if '" + str + "' is a palindrome"
    # Simplified check (would compare str with its reverse)
    if str == "radar" or str == "level" or str == "madam"
        print "  '" + str + "' is a palindrome"
        return true
    else
        print "  '" + str + "' is not a palindrome"
        return false
}

bool pal1 = is_palindrome("radar")
bool pal2 = is_palindrome("hello")
bool pal3 = is_palindrome("level")
print ""

# String encoding/decoding simulation
print "String Encoding/Decoding Simulation:"
string message = "Hello World"
print "Original message: '" + message + "'"
print "Base64 encoded: 'SGVsbG8gV29ybGQ=' (simulated)"
print "URL encoded: 'Hello%20World' (simulated)"
print "HTML encoded: 'Hello World' (no special chars)"
print ""

# String statistics
print "String Statistics:"

fun analyze_string(str) {
    print "Analyzing string: '" + str + "'"
    
    int length = 13  # Simulated length
    int vowels = 4   # Simulated vowel count
    int consonants = 7  # Simulated consonant count
    int spaces = 2   # Simulated space count
    
    print "  Length: " + length
    print "  Vowels: " + vowels
    print "  Consonants: " + consonants
    print "  Spaces: " + spaces
    print "  Other characters: " + (length - vowels - consonants - spaces)
}

analyze_string("Hello World!")
print ""

# String array operations
print "String Array Operations:"
array string words = ["apple", "banana", "cherry", "date", "elderberry"]

print "Word list:"
for i = 0 to 4
    print "  [" + i + "] = '" + words[i] + "'"

# Join operation simulation
string joined = ""
for i = 0 to 4
    set joined = joined + words[i]
    if i < 4
        set joined = joined + ", "

print "Joined with commas: '" + joined + "'"

# Find longest word
string longest = words[0]
for i = 1 to 4
    # In real implementation, would compare lengths
    if words[i] == "elderberry"  # Simulated longest
        set longest = words[i]

print "Longest word: '" + longest + "'"
print ""

print "=== End of String Manipulation Demo ==="
