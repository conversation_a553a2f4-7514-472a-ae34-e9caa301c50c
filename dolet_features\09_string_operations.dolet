# 09. String Operations - Text Manipulation
# This file demonstrates string operations in Dolet

print "=== String Operations Demo ==="
print ""

# Basic string operations
print "Basic String Operations:"
string first_name = "<PERSON>"
string last_name = "<PERSON>"
string full_name = first_name + " " + last_name

print "First name: " + first_name
print "Last name: " + last_name
print "Full name: " + full_name
print ""

# String concatenation
print "String Concatenation:"
string greeting = "Hello"
string target = "World"
string punctuation = "!"
string message = greeting + ", " + target + punctuation

print "Parts: '" + greeting + "', '" + target + "', '" + punctuation + "'"
print "Combined: " + message
print ""

# String with numbers
print "String with Numbers:"
string text = "The answer is "
int number = 42
string result = text + number

print "Text: " + text
print "Number: " + number
print "Combined: " + result
print ""

# Multi-line string simulation
print "Multi-line String Simulation:"
string line1 = "This is line 1"
string line2 = "This is line 2"
string line3 = "This is line 3"

print line1
print line2
print line3
print ""

# String comparison
print "String Comparison:"
string password1 = "secret123"
string password2 = "secret123"
string password3 = "different"

if password1 == password2
    print "Passwords match!"
else
    print "Passwords don't match"

if password1 == password3
    print "Passwords match!"
else
    print "Passwords don't match"
print ""

# String length simulation
print "String Length Simulation:"
string short_text = "Hi"
string medium_text = "Hello World"
string long_text = "This is a very long string with many characters"

print "Short: '" + short_text + "'"
print "Medium: '" + medium_text + "'"
print "Long: '" + long_text + "'"
print ""

# String case operations (simulation)
print "String Case Operations (Simulation):"
string original = "Hello World"
print "Original: " + original
print "Note: Case conversion would be implemented in built-in functions"
print ""

# String search simulation
print "String Search Simulation:"
string haystack = "The quick brown fox jumps over the lazy dog"
string needle = "fox"

print "Text: " + haystack
print "Searching for: " + needle
print "Note: Search functionality would be implemented in built-in functions"
print ""

# String replacement simulation
print "String Replacement Simulation:"
string template = "Hello NAME, welcome to PLACE!"
string name = "Ahmed"
string place = "Cairo"

print "Template: " + template
print "Name: " + name
print "Place: " + place
print "Note: Replacement would be done with built-in functions"
print ""

# String splitting simulation
print "String Splitting Simulation:"
string csv_data = "Ahmed,25,Cairo,Engineer"
print "CSV Data: " + csv_data
print "Note: Splitting would create an array of strings"
print ""

# String formatting with variables
print "String Formatting with Variables:"
string name = "Sara"
int age = 28
string city = "Alexandria"
float salary = 5000.50

string info = "Name: " + name + ", Age: " + age + ", City: " + city + ", Salary: " + salary
print info
print ""

# String building with loops
print "String Building with Loops:"
string repeated = ""
for i = 1 to 5
    set repeated = repeated + "Hello "
print "Repeated string: " + repeated

string numbers_string = ""
for i = 1 to 10
    set numbers_string = numbers_string + i + " "
print "Numbers: " + numbers_string
print ""

# String validation
print "String Validation:"
string email = "<EMAIL>"
string phone = "************"
string empty = ""

print "Email: " + email
if email != ""
    print "Email is not empty"

print "Phone: " + phone
if phone != ""
    print "Phone is not empty"

print "Empty: '" + empty + "'"
if empty == ""
    print "String is empty"
print ""

# String arrays for complex operations
print "String Arrays for Complex Operations:"
array string words = ["Hello", "beautiful", "world", "of", "programming"]

print "Words array:"
for i = 0 to 4
    print "Word " + i + ": " + words[i]

string sentence = ""
for i = 0 to 4
    set sentence = sentence + words[i]
    if i < 4
        set sentence = sentence + " "

print "Sentence: " + sentence
print ""

# String functions simulation
fun reverse_string(str) {
    print "Reversing: " + str
    print "Note: Actual reversal would be implemented"
    return str
}

fun count_characters(str) {
    print "Counting characters in: " + str
    print "Note: Character counting would be implemented"
    return 0
}

print "String Functions Simulation:"
string test_string = "Programming"
reverse_string(test_string)
count_characters(test_string)
print ""

# String constants
print "String Constants:"
string COMPANY_NAME = "TechCorp"
string VERSION = "1.0.0"
string COPYRIGHT = "2024 All Rights Reserved"

print "Company: " + COMPANY_NAME
print "Version: " + VERSION
print "Copyright: " + COPYRIGHT
print ""

print "=== End of String Operations Demo ==="
