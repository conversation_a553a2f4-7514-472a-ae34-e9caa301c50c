; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 258 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_expr_0 = private unnamed_addr constant [22 x i8] c"Integer variables: a=\00", align 1
@.str_expr_1 = private unnamed_addr constant [5 x i8] c", b=\00", align 1
@.str_expr_2 = private unnamed_addr constant [5 x i8] c", c=\00", align 1
@.str_expr_3 = private unnamed_addr constant [5 x i8] c", d=\00", align 1
@.str_expr_4 = private unnamed_addr constant [20 x i8] c"Float variables: x=\00", align 1
@.str_expr_5 = private unnamed_addr constant [5 x i8] c", y=\00", align 1
@.str_expr_6 = private unnamed_addr constant [5 x i8] c", z=\00", align 1
@.str_expr_7 = private unnamed_addr constant [26 x i8] c"Special values: negative=\00", align 1
@.str_expr_8 = private unnamed_addr constant [8 x i8] c", zero=\00", align 1
@.str_expr_9 = private unnamed_addr constant [9 x i8] c", large=\00", align 1
@.str_expr_10 = private unnamed_addr constant [11 x i8] c"Addition: \00", align 1
@.str_expr_11 = private unnamed_addr constant [4 x i8] c" + \00", align 1
@.str_expr_12 = private unnamed_addr constant [4 x i8] c" = \00", align 1
@.str_expr_13 = private unnamed_addr constant [14 x i8] c"Subtraction: \00", align 1
@.str_expr_14 = private unnamed_addr constant [4 x i8] c" - \00", align 1
@.str_expr_15 = private unnamed_addr constant [17 x i8] c"Multiplication: \00", align 1
@.str_expr_16 = private unnamed_addr constant [4 x i8] c" * \00", align 1
@.str_expr_17 = private unnamed_addr constant [11 x i8] c"Division: \00", align 1
@.str_expr_18 = private unnamed_addr constant [4 x i8] c" / \00", align 1
@.str_expr_19 = private unnamed_addr constant [9 x i8] c"Modulo: \00", align 1
@.str_expr_20 = private unnamed_addr constant [5 x i8] c" %% \00", align 1
@.str_expr_21 = private unnamed_addr constant [17 x i8] c"Float Addition: \00", align 1
@.str_expr_22 = private unnamed_addr constant [20 x i8] c"Float Subtraction: \00", align 1
@.str_expr_23 = private unnamed_addr constant [23 x i8] c"Float Multiplication: \00", align 1
@.str_expr_24 = private unnamed_addr constant [17 x i8] c"Float Division: \00", align 1
@.str_expr_25 = private unnamed_addr constant [14 x i8] c"Float + Int: \00", align 1
@.str_expr_26 = private unnamed_addr constant [14 x i8] c"Float * Int: \00", align 1
@.str_expr_27 = private unnamed_addr constant [14 x i8] c"Float / Int: \00", align 1
@.str_expr_28 = private unnamed_addr constant [14 x i8] c"Int * Float: \00", align 1
@.str_expr_29 = private unnamed_addr constant [16 x i8] c"(a + b) * c = (\00", align 1
@.str_expr_30 = private unnamed_addr constant [5 x i8] c") * \00", align 1
@.str_expr_31 = private unnamed_addr constant [15 x i8] c"a * (b + c) = \00", align 1
@.str_expr_32 = private unnamed_addr constant [5 x i8] c" * (\00", align 1
@.str_expr_33 = private unnamed_addr constant [5 x i8] c") = \00", align 1
@.str_expr_34 = private unnamed_addr constant [22 x i8] c"(a - b) + (c * d) = (\00", align 1
@.str_expr_35 = private unnamed_addr constant [6 x i8] c") + (\00", align 1
@.str_expr_36 = private unnamed_addr constant [22 x i8] c"(a + b) - (c - d) = (\00", align 1
@.str_expr_37 = private unnamed_addr constant [6 x i8] c") - (\00", align 1
@.str_expr_38 = private unnamed_addr constant [16 x i8] c"(x + y) * z = (\00", align 1
@.str_expr_39 = private unnamed_addr constant [15 x i8] c"x * (y + z) = \00", align 1
@.str_expr_40 = private unnamed_addr constant [23 x i8] c"((a + b) * c) - d = ((\00", align 1
@.str_expr_41 = private unnamed_addr constant [5 x i8] c") - \00", align 1
@.str_expr_42 = private unnamed_addr constant [21 x i8] c"a + ((b * c) - d) = \00", align 1
@.str_expr_43 = private unnamed_addr constant [6 x i8] c" + ((\00", align 1
@.str_expr_44 = private unnamed_addr constant [22 x i8] c"(a * (b + c)) / d = (\00", align 1
@.str_expr_45 = private unnamed_addr constant [6 x i8] c")) / \00", align 1
@.str_expr_46 = private unnamed_addr constant [25 x i8] c"((x + y) / z) * 2.0 = ((\00", align 1
@.str_expr_47 = private unnamed_addr constant [5 x i8] c") / \00", align 1
@.str_expr_48 = private unnamed_addr constant [11 x i8] c") * 2.0 = \00", align 1
@.str_expr_49 = private unnamed_addr constant [23 x i8] c"x + ((y * z) - 1.0) = \00", align 1
@.str_expr_50 = private unnamed_addr constant [12 x i8] c") - 1.0) = \00", align 1
@.str_expr_51 = private unnamed_addr constant [13 x i8] c"a + b * c = \00", align 1
@.str_expr_52 = private unnamed_addr constant [24 x i8] c" (multiplication first)\00", align 1
@.str_expr_53 = private unnamed_addr constant [13 x i8] c"a * b + c = \00", align 1
@.str_expr_54 = private unnamed_addr constant [17 x i8] c"a + b * c - d = \00", align 1
@.str_expr_55 = private unnamed_addr constant [17 x i8] c"a - b * c + d = \00", align 1
@.str_expr_56 = private unnamed_addr constant [13 x i8] c"x + y * z = \00", align 1
@.str_expr_57 = private unnamed_addr constant [20 x i8] c" (float precedence)\00", align 1
@.str_expr_58 = private unnamed_addr constant [13 x i8] c"x * y + z = \00", align 1
@.str_expr_59 = private unnamed_addr constant [18 x i8] c" (addition first)\00", align 1
@.str_expr_60 = private unnamed_addr constant [22 x i8] c"(a + b) * (c - d) = (\00", align 1
@.str_expr_61 = private unnamed_addr constant [6 x i8] c") * (\00", align 1
@.str_expr_62 = private unnamed_addr constant [18 x i8] c" (float override)\00", align 1
@.str_expr_63 = private unnamed_addr constant [17 x i8] c"a + b + c + d = \00", align 1
@.str_expr_64 = private unnamed_addr constant [17 x i8] c"a - b - c - d = \00", align 1
@.str_expr_65 = private unnamed_addr constant [17 x i8] c"a * b * c * d = \00", align 1
@.str_expr_66 = private unnamed_addr constant [19 x i8] c"x + y + z + 1.0 = \00", align 1
@.str_expr_67 = private unnamed_addr constant [10 x i8] c" + 1.0 = \00", align 1
@.str_expr_68 = private unnamed_addr constant [19 x i8] c"x * y * z * 2.0 = \00", align 1
@.str_expr_69 = private unnamed_addr constant [10 x i8] c" * 2.0 = \00", align 1
@.str_expr_70 = private unnamed_addr constant [23 x i8] c"negative + negative = \00", align 1
@.str_expr_71 = private unnamed_addr constant [23 x i8] c"negative * negative = \00", align 1
@.str_expr_72 = private unnamed_addr constant [23 x i8] c"negative - negative = \00", align 1
@.str_expr_73 = private unnamed_addr constant [16 x i8] c"a + negative = \00", align 1
@.str_expr_74 = private unnamed_addr constant [16 x i8] c"a * negative = \00", align 1
@.str_expr_75 = private unnamed_addr constant [12 x i8] c"zero + a = \00", align 1
@.str_expr_76 = private unnamed_addr constant [12 x i8] c"zero * a = \00", align 1
@.str_expr_77 = private unnamed_addr constant [12 x i8] c"a + zero = \00", align 1
@.str_expr_78 = private unnamed_addr constant [12 x i8] c"a * zero = \00", align 1
@.str_expr_79 = private unnamed_addr constant [12 x i8] c"zero - a = \00", align 1
@.str_expr_80 = private unnamed_addr constant [12 x i8] c"a - zero = \00", align 1
@.str_expr_81 = private unnamed_addr constant [17 x i8] c"large + large = \00", align 1
@.str_expr_82 = private unnamed_addr constant [13 x i8] c"large * a = \00", align 1
@.str_expr_83 = private unnamed_addr constant [13 x i8] c"large / a = \00", align 1
@.str_expr_84 = private unnamed_addr constant [14 x i8] c"large %% a = \00", align 1
@.str_expr_85 = private unnamed_addr constant [17 x i8] c"Direct: a + b = \00", align 1
@.str_expr_86 = private unnamed_addr constant [23 x i8] c"Direct: (a + b) * c = \00", align 1
@.str_expr_87 = private unnamed_addr constant [29 x i8] c"Direct: ((a + b) * c) - d = \00", align 1
@.str_expr_88 = private unnamed_addr constant [29 x i8] c"Direct: (a * b) + (c - d) = \00", align 1
@.str_expr_89 = private unnamed_addr constant [29 x i8] c"Direct float: (x + y) * z = \00", align 1
@.str_expr_90 = private unnamed_addr constant [23 x i8] c"Direct mixed: a + x = \00", align 1
@.str_expr_91 = private unnamed_addr constant [19 x i8] c"Initial counter = \00", align 1
@.str_expr_92 = private unnamed_addr constant [21 x i8] c"After +1: counter = \00", align 1
@.str_expr_93 = private unnamed_addr constant [21 x i8] c"After *3: counter = \00", align 1
@.str_expr_94 = private unnamed_addr constant [21 x i8] c"After -5: counter = \00", align 1
@.str_expr_95 = private unnamed_addr constant [21 x i8] c"After /2: counter = \00", align 1
@.str_expr_96 = private unnamed_addr constant [37 x i8] c"After (counter + 10) * 2: counter = \00", align 1
@.str_expr_97 = private unnamed_addr constant [42 x i8] c"After ((counter - 5) * 3) + 1: counter = \00", align 1
@.str_expr_98 = private unnamed_addr constant [26 x i8] c"Rectangle area (a * b) = \00", align 1
@.str_expr_99 = private unnamed_addr constant [37 x i8] c"Rectangle perimeter ((a + b) * 2) = \00", align 1
@.str_expr_100 = private unnamed_addr constant [31 x i8] c"Triangle area ((a * b) / 2) = \00", align 1
@.str_expr_101 = private unnamed_addr constant [27 x i8] c"Pythagorean (a^2 + b^2) = \00", align 1
@.str_expr_102 = private unnamed_addr constant [26 x i8] c"Circle area (pi * r^2) = \00", align 1
@.str_expr_103 = private unnamed_addr constant [23 x i8] c"Complex calculation = \00", align 1
@.str_expr_104 = private unnamed_addr constant [38 x i8] c"Extreme 1: (((a + b) * c) - d) + 1 = \00", align 1
@.str_expr_105 = private unnamed_addr constant [38 x i8] c"Extreme 2: a + (((b * c) - d) * 2) = \00", align 1
@.str_expr_106 = private unnamed_addr constant [44 x i8] c"Extreme 3: ((a + (b * c)) - (d + 1)) * 2 = \00", align 1
@.str_expr_107 = private unnamed_addr constant [54 x i8] c"Extreme 4: (((x + y) * z) - ((x - y) / 2.0)) + 1.0 = \00", align 1
@.str_expr_108 = private unnamed_addr constant [56 x i8] c"Extreme 5: ((a + b) * (c + d)) - ((a - b) * (c - d)) = \00", align 1
@.str_expr_109 = private unnamed_addr constant [28 x i8] c"Quadratic (a^2 + ba + c) = \00", align 1
@.str_expr_110 = private unnamed_addr constant [26 x i8] c"Distance^2 (x^2 + y^2) = \00", align 1
@.str_expr_111 = private unnamed_addr constant [31 x i8] c"Factorial 4! approx 1*2*3*4 = \00", align 1
@.str_expr_112 = private unnamed_addr constant [21 x i8] c"Average (x+y+z)/3 = \00", align 1
@.str_expr_113 = private unnamed_addr constant [32 x i8] c"Sum of squares (a^2+b^2+c^2) = \00", align 1
@.str_expr_114 = private unnamed_addr constant [11 x i8] c"Sum 1-10: \00", align 1
@.str_expr_115 = private unnamed_addr constant [19 x i8] c"Nested additions: \00", align 1
@.str_expr_116 = private unnamed_addr constant [14 x i8] c"Float * 0.0: \00", align 1
@.str_expr_117 = private unnamed_addr constant [14 x i8] c"Float / 1.0: \00", align 1
@.str_expr_118 = private unnamed_addr constant [10 x i8] c"Int * 1: \00", align 1
@.str_expr_119 = private unnamed_addr constant [16 x i8] c"Stress test 1: \00", align 1
@.str_expr_120 = private unnamed_addr constant [16 x i8] c"Stress test 2: \00", align 1
@.str_expr_121 = private unnamed_addr constant [16 x i8] c"Stress test 3: \00", align 1
@.str_expr_122 = private unnamed_addr constant [16 x i8] c"Stress test 4: \00", align 1
@.str_expr_123 = private unnamed_addr constant [23 x i8] c"Final integer result: \00", align 1
@.str_expr_124 = private unnamed_addr constant [21 x i8] c"Final float result: \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4
@global_b = global i32 0, align 4
@global_c = global i32 0, align 4
@global_d = global i32 0, align 4
@global_x = global float 0.0, align 4
@global_y = global float 0.0, align 4
@global_z = global float 0.0, align 4
@global_negative = global i32 0, align 4
@global_zero = global i32 0, align 4
@global_large = global i32 0, align 4
@global_add_result = global i32 0, align 4
@global_sub_result = global i32 0, align 4
@global_mul_result = global i32 0, align 4
@global_div_result = global i32 0, align 4
@global_mod_result = global i32 0, align 4
@global_float_add = global float 0.0, align 4
@global_float_sub = global float 0.0, align 4
@global_float_mul = global float 0.0, align 4
@global_float_div = global float 0.0, align 4
@global_mixed1 = global float 0.0, align 4
@global_mixed2 = global float 0.0, align 4
@global_mixed3 = global float 0.0, align 4
@global_mixed4 = global float 0.0, align 4
@global_paren1 = global i32 0, align 4
@global_paren2 = global i32 0, align 4
@global_paren3 = global i32 0, align 4
@global_paren4 = global i32 0, align 4
@global_paren5 = global float 0.0, align 4
@global_paren6 = global float 0.0, align 4
@global_nested1 = global i32 0, align 4
@global_nested2 = global i32 0, align 4
@global_nested3 = global i32 0, align 4
@global_nested4 = global float 0.0, align 4
@global_nested5 = global float 0.0, align 4
@global_prec1 = global i32 0, align 4
@global_prec2 = global i32 0, align 4
@global_prec3 = global i32 0, align 4
@global_prec4 = global i32 0, align 4
@global_prec5 = global float 0.0, align 4
@global_prec6 = global float 0.0, align 4
@global_override1 = global i32 0, align 4
@global_override2 = global i32 0, align 4
@global_override3 = global i32 0, align 4
@global_override4 = global float 0.0, align 4
@global_override5 = global float 0.0, align 4
@global_chain1 = global i32 0, align 4
@global_chain2 = global i32 0, align 4
@global_chain3 = global i32 0, align 4
@global_chain4 = global float 0.0, align 4
@global_chain5 = global float 0.0, align 4
@global_neg1 = global i32 0, align 4
@global_neg2 = global i32 0, align 4
@global_neg3 = global i32 0, align 4
@global_neg4 = global i32 0, align 4
@global_neg5 = global i32 0, align 4
@global_zero1 = global i32 0, align 4
@global_zero2 = global i32 0, align 4
@global_zero3 = global i32 0, align 4
@global_zero4 = global i32 0, align 4
@global_zero5 = global i32 0, align 4
@global_zero6 = global i32 0, align 4
@global_large1 = global i32 0, align 4
@global_large2 = global i32 0, align 4
@global_large3 = global i32 0, align 4
@global_large4 = global i32 0, align 4
@global_counter = global i32 0, align 4
@global_area_rectangle = global i32 0, align 4
@global_perimeter_rectangle = global i32 0, align 4
@global_area_triangle = global i32 0, align 4
@global_pythagorean = global i32 0, align 4
@global_circle_area = global float 0.0, align 4
@global_compound_calc = global float 0.0, align 4
@global_extreme1 = global i32 0, align 4
@global_extreme2 = global i32 0, align 4
@global_extreme3 = global i32 0, align 4
@global_extreme4 = global float 0.0, align 4
@global_extreme5 = global i32 0, align 4
@global_quadratic = global i32 0, align 4
@global_distance = global float 0.0, align 4
@global_factorial_approx = global i32 0, align 4
@global_average = global float 0.0, align 4
@global_sum_of_squares = global i32 0, align 4
@global_edge1 = global i32 0, align 4
@global_edge2 = global i32 0, align 4
@global_edge3 = global float 0.0, align 4
@global_edge4 = global float 0.0, align 4
@global_edge5 = global i32 0, align 4
@global_stress1 = global i32 0, align 4
@global_stress2 = global i32 0, align 4
@global_stress3 = global float 0.0, align 4
@global_stress4 = global i32 0, align 4
@global_final_int = global i32 0, align 4
@global_final_float = global float 0.0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== COMPREHENSIVE ARITHMETIC OPERATIONS - FINAL VE..." (inline)
  %tmp_1_str = alloca [60 x i8], align 1
  store [60 x i8] c"=== COMPREHENSIVE ARITHMETIC OPERATIONS - FINAL VERSION ===\00", [60 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [60 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_3_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [1 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== VARIABLE DECLARATIONS ===" (inline)
  %tmp_5_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== VARIABLE DECLARATIONS ===\00", [30 x i8]* %tmp_5_str, align 1
  %tmp_6 = bitcast [30 x i8]* %tmp_5_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int a = 15
  store i32 15, i32* @global_a, align 4
  ; int b = 4
  store i32 4, i32* @global_b, align 4
  ; int c = 7
  store i32 7, i32* @global_c, align 4
  ; int d = 2
  store i32 2, i32* @global_d, align 4
  ; float x = 10.5
  %tmp_7 = bitcast i32 1093140480 to float
  store float %tmp_7, float* @global_x, align 4
  ; float y = 3.2
  %tmp_8 = bitcast i32 1078774989 to float
  store float %tmp_8, float* @global_y, align 4
  ; float z = 2.0
  %tmp_9 = bitcast i32 1073741824 to float
  store float %tmp_9, float* @global_z, align 4
  ; int negative = -8
  store i32 -8, i32* @global_negative, align 4
  ; int zero = 0
  store i32 0, i32* @global_zero, align 4
  ; int large = 1000
  store i32 1000, i32* @global_large, align 4
  ; print expression: "Integer variables: a=" + a + ", b=" + b + ", c=" ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_10 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_11 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_12 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_13 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float variables: x=" + x + ", y=" + y + ", z=" + ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_14 = load float, float* @global_x, align 4
  %tmp_15 = fpext float %tmp_14 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_16 = load float, float* @global_y, align 4
  %tmp_17 = fpext float %tmp_16 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_18 = load float, float* @global_z, align 4
  %tmp_19 = fpext float %tmp_18 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Special values: negative=" + negative + ", zero="...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_20 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_21 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_21)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_22 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_23_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_23_str, align 1
  %tmp_24 = bitcast [1 x i8]* %tmp_23_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== BASIC INTEGER ARITHMETIC ===" (inline)
  %tmp_25_str = alloca [33 x i8], align 1
  store [33 x i8] c"=== BASIC INTEGER ARITHMETIC ===\00", [33 x i8]* %tmp_25_str, align 1
  %tmp_26 = bitcast [33 x i8]* %tmp_25_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int add_result = a + b
  %tmp_27 = load i32, i32* @global_a, align 4
  %tmp_28 = load i32, i32* @global_b, align 4
  %tmp_29 = add i32 %tmp_27, %tmp_28
  store i32 %tmp_29, i32* @global_add_result, align 4
  ; int sub_result = a - b
  %tmp_30 = load i32, i32* @global_a, align 4
  %tmp_31 = load i32, i32* @global_b, align 4
  %tmp_32 = sub i32 %tmp_30, %tmp_31
  store i32 %tmp_32, i32* @global_sub_result, align 4
  ; int mul_result = a * b
  %tmp_33 = load i32, i32* @global_a, align 4
  %tmp_34 = load i32, i32* @global_b, align 4
  %tmp_35 = mul i32 %tmp_33, %tmp_34
  store i32 %tmp_35, i32* @global_mul_result, align 4
  ; int div_result = a / b
  %tmp_36 = load i32, i32* @global_a, align 4
  %tmp_37 = load i32, i32* @global_b, align 4
  %tmp_38 = sdiv i32 %tmp_36, %tmp_37
  store i32 %tmp_38, i32* @global_div_result, align 4
  ; int mod_result = a % b
  %tmp_39 = load i32, i32* @global_a, align 4
  %tmp_40 = load i32, i32* @global_b, align 4
  %tmp_41 = srem i32 %tmp_39, %tmp_40
  store i32 %tmp_41, i32* @global_mod_result, align 4
  ; print expression: "Addition: " + a + " + " + b + " = " + add_result
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_42 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_43 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_44 = load i32, i32* @global_add_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_44)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Subtraction: " + a + " - " + b + " = " + sub_resu...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_45 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_45)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_46 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_46)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_47 = load i32, i32* @global_sub_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_47)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Multiplication: " + a + " * " + b + " = " + mul_r...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_48 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_48)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_49 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_49)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_50 = load i32, i32* @global_mul_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_50)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Division: " + a + " / " + b + " = " + div_result
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_51 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_51)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_52 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_52)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_53 = load i32, i32* @global_div_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_53)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Modulo: " + a + " % " + b + " = " + mod_result
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_54 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_54)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_55 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_55)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_56 = load i32, i32* @global_mod_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_56)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_57_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_57_str, align 1
  %tmp_58 = bitcast [1 x i8]* %tmp_57_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_58)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== BASIC FLOAT ARITHMETIC ===" (inline)
  %tmp_59_str = alloca [31 x i8], align 1
  store [31 x i8] c"=== BASIC FLOAT ARITHMETIC ===\00", [31 x i8]* %tmp_59_str, align 1
  %tmp_60 = bitcast [31 x i8]* %tmp_59_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_60)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float float_add = x + y
  %tmp_61 = load float, float* @global_x, align 4
  %tmp_62 = load float, float* @global_y, align 4
  %tmp_63 = fadd float %tmp_61, %tmp_62
  store float %tmp_63, float* @global_float_add, align 4
  ; float float_sub = x - y
  %tmp_64 = load float, float* @global_x, align 4
  %tmp_65 = load float, float* @global_y, align 4
  %tmp_66 = fsub float %tmp_64, %tmp_65
  store float %tmp_66, float* @global_float_sub, align 4
  ; float float_mul = x * y
  %tmp_67 = load float, float* @global_x, align 4
  %tmp_68 = load float, float* @global_y, align 4
  %tmp_69 = fmul float %tmp_67, %tmp_68
  store float %tmp_69, float* @global_float_mul, align 4
  ; float float_div = x / y
  %tmp_70 = load float, float* @global_x, align 4
  %tmp_71 = load float, float* @global_y, align 4
  %tmp_72 = fdiv float %tmp_70, %tmp_71
  store float %tmp_72, float* @global_float_div, align 4
  ; print expression: "Float Addition: " + x + " + " + y + " = " + float...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_73 = load float, float* @global_x, align 4
  %tmp_74 = fpext float %tmp_73 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_74)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_75 = load float, float* @global_y, align 4
  %tmp_76 = fpext float %tmp_75 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_76)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_77 = load float, float* @global_float_add, align 4
  %tmp_78 = fpext float %tmp_77 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_78)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float Subtraction: " + x + " - " + y + " = " + fl...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_79 = load float, float* @global_x, align 4
  %tmp_80 = fpext float %tmp_79 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_80)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_81 = load float, float* @global_y, align 4
  %tmp_82 = fpext float %tmp_81 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_82)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_83 = load float, float* @global_float_sub, align 4
  %tmp_84 = fpext float %tmp_83 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float Multiplication: " + x + " * " + y + " = " +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_23, i32 0, i32 0))
  %tmp_85 = load float, float* @global_x, align 4
  %tmp_86 = fpext float %tmp_85 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_87 = load float, float* @global_y, align 4
  %tmp_88 = fpext float %tmp_87 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_88)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_89 = load float, float* @global_float_mul, align 4
  %tmp_90 = fpext float %tmp_89 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float Division: " + x + " / " + y + " = " + float...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_91 = load float, float* @global_x, align 4
  %tmp_92 = fpext float %tmp_91 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_92)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_93 = load float, float* @global_y, align 4
  %tmp_94 = fpext float %tmp_93 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_95 = load float, float* @global_float_div, align 4
  %tmp_96 = fpext float %tmp_95 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_96)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_97_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_97_str, align 1
  %tmp_98 = bitcast [1 x i8]* %tmp_97_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_98)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== MIXED TYPE OPERATIONS ===" (inline)
  %tmp_99_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== MIXED TYPE OPERATIONS ===\00", [30 x i8]* %tmp_99_str, align 1
  %tmp_100 = bitcast [30 x i8]* %tmp_99_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_100)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float mixed1 = x + a
  %tmp_101 = load float, float* @global_x, align 4
  %tmp_102 = load i32, i32* @global_a, align 4
  %tmp_103 = sitofp i32 %tmp_102 to float
  %tmp_104 = fadd float %tmp_101, %tmp_103
  store float %tmp_104, float* @global_mixed1, align 4
  ; float mixed2 = y * b
  %tmp_105 = load float, float* @global_y, align 4
  %tmp_106 = load i32, i32* @global_b, align 4
  %tmp_107 = sitofp i32 %tmp_106 to float
  %tmp_108 = fmul float %tmp_105, %tmp_107
  store float %tmp_108, float* @global_mixed2, align 4
  ; float mixed3 = x / b
  %tmp_109 = load float, float* @global_x, align 4
  %tmp_110 = load i32, i32* @global_b, align 4
  %tmp_111 = sitofp i32 %tmp_110 to float
  %tmp_112 = fdiv float %tmp_109, %tmp_111
  store float %tmp_112, float* @global_mixed3, align 4
  ; float mixed4 = a * z
  %tmp_113 = load i32, i32* @global_a, align 4
  %tmp_114 = sitofp i32 %tmp_113 to float
  %tmp_115 = load float, float* @global_z, align 4
  %tmp_116 = fmul float %tmp_114, %tmp_115
  store float %tmp_116, float* @global_mixed4, align 4
  ; print expression: "Float + Int: " + x + " + " + a + " = " + mixed1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_25, i32 0, i32 0))
  %tmp_117 = load float, float* @global_x, align 4
  %tmp_118 = fpext float %tmp_117 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_118)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_119 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_119)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_120 = load float, float* @global_mixed1, align 4
  %tmp_121 = fpext float %tmp_120 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_121)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float * Int: " + y + " * " + b + " = " + mixed2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_26, i32 0, i32 0))
  %tmp_122 = load float, float* @global_y, align 4
  %tmp_123 = fpext float %tmp_122 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_123)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_124 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_124)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_125 = load float, float* @global_mixed2, align 4
  %tmp_126 = fpext float %tmp_125 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_126)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float / Int: " + x + " / " + b + " = " + mixed3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_27, i32 0, i32 0))
  %tmp_127 = load float, float* @global_x, align 4
  %tmp_128 = fpext float %tmp_127 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_128)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_129 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_130 = load float, float* @global_mixed3, align 4
  %tmp_131 = fpext float %tmp_130 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Int * Float: " + a + " * " + z + " = " + mixed4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_132 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_132)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_133 = load float, float* @global_z, align 4
  %tmp_134 = fpext float %tmp_133 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_134)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_135 = load float, float* @global_mixed4, align 4
  %tmp_136 = fpext float %tmp_135 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_136)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_137_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_137_str, align 1
  %tmp_138 = bitcast [1 x i8]* %tmp_137_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_138)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PARENTHESES OPERATIONS ===" (inline)
  %tmp_139_str = alloca [31 x i8], align 1
  store [31 x i8] c"=== PARENTHESES OPERATIONS ===\00", [31 x i8]* %tmp_139_str, align 1
  %tmp_140 = bitcast [31 x i8]* %tmp_139_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_140)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int paren1 = (a + b) * c
  %tmp_141 = load i32, i32* @global_a, align 4
  %tmp_142 = load i32, i32* @global_b, align 4
  %tmp_143 = add i32 %tmp_141, %tmp_142
  %tmp_144 = load i32, i32* @global_c, align 4
  %tmp_145 = mul i32 %tmp_143, %tmp_144
  store i32 %tmp_145, i32* @global_paren1, align 4
  ; int paren2 = a * (b + c)
  %tmp_146 = load i32, i32* @global_b, align 4
  %tmp_147 = load i32, i32* @global_c, align 4
  %tmp_148 = add i32 %tmp_146, %tmp_147
  %tmp_149 = load i32, i32* @global_a, align 4
  %tmp_150 = mul i32 %tmp_149, %tmp_148
  store i32 %tmp_150, i32* @global_paren2, align 4
  ; int paren3 = (a - b) + (c * d)
  %tmp_151 = load i32, i32* @global_a, align 4
  %tmp_152 = load i32, i32* @global_b, align 4
  %tmp_153 = sub i32 %tmp_151, %tmp_152
  %tmp_154 = load i32, i32* @global_c, align 4
  %tmp_155 = load i32, i32* @global_d, align 4
  %tmp_156 = mul i32 %tmp_154, %tmp_155
  %tmp_157 = add i32 %tmp_153, %tmp_156
  store i32 %tmp_157, i32* @global_paren3, align 4
  ; int paren4 = (a + b) - (c - d)
  %tmp_158 = load i32, i32* @global_a, align 4
  %tmp_159 = load i32, i32* @global_b, align 4
  %tmp_160 = add i32 %tmp_158, %tmp_159
  %tmp_161 = load i32, i32* @global_c, align 4
  %tmp_162 = load i32, i32* @global_d, align 4
  %tmp_163 = sub i32 %tmp_161, %tmp_162
  %tmp_164 = sub i32 %tmp_160, %tmp_163
  store i32 %tmp_164, i32* @global_paren4, align 4
  ; float paren5 = (x + y) * z
  %tmp_165 = load float, float* @global_x, align 4
  %tmp_166 = load float, float* @global_y, align 4
  %tmp_167 = fadd float %tmp_165, %tmp_166
  %tmp_168 = load float, float* @global_z, align 4
  %tmp_169 = fmul float %tmp_167, %tmp_168
  store float %tmp_169, float* @global_paren5, align 4
  ; float paren6 = x * (y + z)
  %tmp_170 = load float, float* @global_y, align 4
  %tmp_171 = load float, float* @global_z, align 4
  %tmp_172 = fadd float %tmp_170, %tmp_171
  %tmp_173 = load float, float* @global_x, align 4
  %tmp_174 = fmul float %tmp_173, %tmp_172
  store float %tmp_174, float* @global_paren6, align 4
  ; print expression: "(a + b) * c = (" + a + " + " + b + ") * " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_175 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_175)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_176 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_176)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_177 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_177)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_178 = load i32, i32* @global_paren1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_178)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * (b + c) = " + a + " * (" + b + " + " + c + ")...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_179 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_179)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_180 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_180)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_181 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_181)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_182 = load i32, i32* @global_paren2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_182)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a - b) + (c * d) = (" + a + " - " + b + ") + (" ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_34, i32 0, i32 0))
  %tmp_183 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_183)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_184 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_184)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_35, i32 0, i32 0))
  %tmp_185 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_185)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_186 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_186)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_187 = load i32, i32* @global_paren3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_187)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) - (c - d) = (" + a + " + " + b + ") - (" ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_36, i32 0, i32 0))
  %tmp_188 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_188)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_189 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_189)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_37, i32 0, i32 0))
  %tmp_190 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_190)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_191 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_191)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_192 = load i32, i32* @global_paren4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_192)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(x + y) * z = (" + x + " + " + y + ") * " + z + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_193 = load float, float* @global_x, align 4
  %tmp_194 = fpext float %tmp_193 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_194)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_195 = load float, float* @global_y, align 4
  %tmp_196 = fpext float %tmp_195 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_196)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_197 = load float, float* @global_z, align 4
  %tmp_198 = fpext float %tmp_197 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_198)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_199 = load float, float* @global_paren5, align 4
  %tmp_200 = fpext float %tmp_199 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_200)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x * (y + z) = " + x + " * (" + y + " + " + z + ")...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_39, i32 0, i32 0))
  %tmp_201 = load float, float* @global_x, align 4
  %tmp_202 = fpext float %tmp_201 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_202)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_203 = load float, float* @global_y, align 4
  %tmp_204 = fpext float %tmp_203 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_204)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_205 = load float, float* @global_z, align 4
  %tmp_206 = fpext float %tmp_205 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_206)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_207 = load float, float* @global_paren6, align 4
  %tmp_208 = fpext float %tmp_207 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_208)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_209_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_209_str, align 1
  %tmp_210 = bitcast [1 x i8]* %tmp_209_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_210)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== NESTED PARENTHESES ===" (inline)
  %tmp_211_str = alloca [27 x i8], align 1
  store [27 x i8] c"=== NESTED PARENTHESES ===\00", [27 x i8]* %tmp_211_str, align 1
  %tmp_212 = bitcast [27 x i8]* %tmp_211_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_212)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int nested1 = ((a + b) * c) - d
  %tmp_213 = load i32, i32* @global_a, align 4
  %tmp_214 = load i32, i32* @global_b, align 4
  %tmp_215 = add i32 %tmp_213, %tmp_214
  %tmp_216 = load i32, i32* @global_c, align 4
  %tmp_217 = mul i32 %tmp_215, %tmp_216
  %tmp_218 = load i32, i32* @global_d, align 4
  %tmp_219 = sub i32 %tmp_217, %tmp_218
  store i32 %tmp_219, i32* @global_nested1, align 4
  ; int nested2 = a + ((b * c) - d)
  %tmp_220 = load i32, i32* @global_b, align 4
  %tmp_221 = load i32, i32* @global_c, align 4
  %tmp_222 = mul i32 %tmp_220, %tmp_221
  %tmp_223 = load i32, i32* @global_d, align 4
  %tmp_224 = sub i32 %tmp_222, %tmp_223
  %tmp_225 = load i32, i32* @global_a, align 4
  %tmp_226 = add i32 %tmp_225, %tmp_224
  store i32 %tmp_226, i32* @global_nested2, align 4
  ; int nested3 = (a * (b + c)) / d
  %tmp_227 = load i32, i32* @global_b, align 4
  %tmp_228 = load i32, i32* @global_c, align 4
  %tmp_229 = add i32 %tmp_227, %tmp_228
  %tmp_230 = load i32, i32* @global_a, align 4
  %tmp_231 = mul i32 %tmp_230, %tmp_229
  %tmp_232 = load i32, i32* @global_d, align 4
  %tmp_233 = sdiv i32 %tmp_231, %tmp_232
  store i32 %tmp_233, i32* @global_nested3, align 4
  ; float nested4 = ((x + y) / z) * 2.0
  %tmp_234 = load float, float* @global_x, align 4
  %tmp_235 = load float, float* @global_y, align 4
  %tmp_236 = fadd float %tmp_234, %tmp_235
  %tmp_237 = load float, float* @global_z, align 4
  %tmp_238 = fdiv float %tmp_236, %tmp_237
  %tmp_239 = bitcast i32 1073741824 to float
  %tmp_240 = fmul float %tmp_238, %tmp_239
  store float %tmp_240, float* @global_nested4, align 4
  ; float nested5 = x + ((y * z) - 1.0)
  %tmp_241 = load float, float* @global_y, align 4
  %tmp_242 = load float, float* @global_z, align 4
  %tmp_243 = fmul float %tmp_241, %tmp_242
  %tmp_244 = bitcast i32 1065353216 to float
  %tmp_245 = fsub float %tmp_243, %tmp_244
  %tmp_246 = load float, float* @global_x, align 4
  %tmp_247 = fadd float %tmp_246, %tmp_245
  store float %tmp_247, float* @global_nested5, align 4
  ; print expression: "((a + b) * c) - d = ((" + a + " + " + b + ") * " ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_40, i32 0, i32 0))
  %tmp_248 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_248)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_249 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_249)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_250 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_250)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_41, i32 0, i32 0))
  %tmp_251 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_251)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_252 = load i32, i32* @global_nested1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_252)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + ((b * c) - d) = " + a + " + ((" + b + " * " +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_42, i32 0, i32 0))
  %tmp_253 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_253)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_43, i32 0, i32 0))
  %tmp_254 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_254)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_255 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_255)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_41, i32 0, i32 0))
  %tmp_256 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_256)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_257 = load i32, i32* @global_nested2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_257)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * (b + c)) / d = (" + a + " * (" + b + " + " +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_258 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_258)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_259 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_259)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_260 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_260)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_45, i32 0, i32 0))
  %tmp_261 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_261)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_262 = load i32, i32* @global_nested3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_262)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((x + y) / z) * 2.0 = ((" + x + " + " + y + ") / ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([25 x i8], [25 x i8]* @.str_expr_46, i32 0, i32 0))
  %tmp_263 = load float, float* @global_x, align 4
  %tmp_264 = fpext float %tmp_263 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_264)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_265 = load float, float* @global_y, align 4
  %tmp_266 = fpext float %tmp_265 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_266)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_47, i32 0, i32 0))
  %tmp_267 = load float, float* @global_z, align 4
  %tmp_268 = fpext float %tmp_267 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_268)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_48, i32 0, i32 0))
  %tmp_269 = load float, float* @global_nested4, align 4
  %tmp_270 = fpext float %tmp_269 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_270)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + ((y * z) - 1.0) = " + x + " + ((" + y + " * "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_49, i32 0, i32 0))
  %tmp_271 = load float, float* @global_x, align 4
  %tmp_272 = fpext float %tmp_271 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_272)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_43, i32 0, i32 0))
  %tmp_273 = load float, float* @global_y, align 4
  %tmp_274 = fpext float %tmp_273 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_274)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_275 = load float, float* @global_z, align 4
  %tmp_276 = fpext float %tmp_275 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_276)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_50, i32 0, i32 0))
  %tmp_277 = load float, float* @global_nested5, align 4
  %tmp_278 = fpext float %tmp_277 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_278)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_279_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_279_str, align 1
  %tmp_280 = bitcast [1 x i8]* %tmp_279_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_280)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== OPERATOR PRECEDENCE ===" (inline)
  %tmp_281_str = alloca [28 x i8], align 1
  store [28 x i8] c"=== OPERATOR PRECEDENCE ===\00", [28 x i8]* %tmp_281_str, align 1
  %tmp_282 = bitcast [28 x i8]* %tmp_281_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_282)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int prec1 = a + b * c
  %tmp_283 = load i32, i32* @global_b, align 4
  %tmp_284 = load i32, i32* @global_c, align 4
  %tmp_285 = mul i32 %tmp_283, %tmp_284
  %tmp_286 = load i32, i32* @global_a, align 4
  %tmp_287 = add i32 %tmp_286, %tmp_285
  store i32 %tmp_287, i32* @global_prec1, align 4
  ; int prec2 = a * b + c
  %tmp_288 = load i32, i32* @global_a, align 4
  %tmp_289 = load i32, i32* @global_b, align 4
  %tmp_290 = mul i32 %tmp_288, %tmp_289
  %tmp_291 = load i32, i32* @global_c, align 4
  %tmp_292 = add i32 %tmp_290, %tmp_291
  store i32 %tmp_292, i32* @global_prec2, align 4
  ; int prec3 = a + b * c - d
  %tmp_293 = load i32, i32* @global_b, align 4
  %tmp_294 = load i32, i32* @global_c, align 4
  %tmp_295 = mul i32 %tmp_293, %tmp_294
  %tmp_296 = load i32, i32* @global_a, align 4
  %tmp_297 = add i32 %tmp_296, %tmp_295
  %tmp_298 = load i32, i32* @global_d, align 4
  %tmp_299 = sub i32 %tmp_297, %tmp_298
  store i32 %tmp_299, i32* @global_prec3, align 4
  ; int prec4 = a - b * c + d
  %tmp_300 = load i32, i32* @global_b, align 4
  %tmp_301 = load i32, i32* @global_c, align 4
  %tmp_302 = mul i32 %tmp_300, %tmp_301
  %tmp_303 = load i32, i32* @global_a, align 4
  %tmp_304 = sub i32 %tmp_303, %tmp_302
  %tmp_305 = load i32, i32* @global_d, align 4
  %tmp_306 = add i32 %tmp_304, %tmp_305
  store i32 %tmp_306, i32* @global_prec4, align 4
  ; float prec5 = x + y * z
  %tmp_307 = load float, float* @global_y, align 4
  %tmp_308 = load float, float* @global_z, align 4
  %tmp_309 = fmul float %tmp_307, %tmp_308
  %tmp_310 = load float, float* @global_x, align 4
  %tmp_311 = fadd float %tmp_310, %tmp_309
  store float %tmp_311, float* @global_prec5, align 4
  ; float prec6 = x * y + z
  %tmp_312 = load float, float* @global_x, align 4
  %tmp_313 = load float, float* @global_y, align 4
  %tmp_314 = fmul float %tmp_312, %tmp_313
  %tmp_315 = load float, float* @global_z, align 4
  %tmp_316 = fadd float %tmp_314, %tmp_315
  store float %tmp_316, float* @global_prec6, align 4
  ; print expression: "a + b * c = " + a + " + " + b + " * " + c + " = "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_51, i32 0, i32 0))
  %tmp_317 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_317)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_318 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_318)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_319 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_319)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_320 = load i32, i32* @global_prec1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_320)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_expr_52, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b + c = " + a + " * " + b + " + " + c + " = "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_53, i32 0, i32 0))
  %tmp_321 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_321)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_322 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_322)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_323 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_323)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_324 = load i32, i32* @global_prec2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_324)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([24 x i8], [24 x i8]* @.str_expr_52, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + b * c - d = " + a + " + " + b + " * " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_54, i32 0, i32 0))
  %tmp_325 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_325)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_326 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_326)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_327 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_327)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_328 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_328)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_329 = load i32, i32* @global_prec3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_329)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - b * c + d = " + a + " - " + b + " * " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_55, i32 0, i32 0))
  %tmp_330 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_330)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_331 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_331)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_332 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_332)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_333 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_333)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_334 = load i32, i32* @global_prec4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_334)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + y * z = " + x + " + " + y + " * " + z + " = "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_56, i32 0, i32 0))
  %tmp_335 = load float, float* @global_x, align 4
  %tmp_336 = fpext float %tmp_335 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_336)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_337 = load float, float* @global_y, align 4
  %tmp_338 = fpext float %tmp_337 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_338)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_339 = load float, float* @global_z, align 4
  %tmp_340 = fpext float %tmp_339 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_340)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_341 = load float, float* @global_prec5, align 4
  %tmp_342 = fpext float %tmp_341 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_342)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_57, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x * y + z = " + x + " * " + y + " + " + z + " = "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_58, i32 0, i32 0))
  %tmp_343 = load float, float* @global_x, align 4
  %tmp_344 = fpext float %tmp_343 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_344)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_345 = load float, float* @global_y, align 4
  %tmp_346 = fpext float %tmp_345 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_346)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_347 = load float, float* @global_z, align 4
  %tmp_348 = fpext float %tmp_347 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_348)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_349 = load float, float* @global_prec6, align 4
  %tmp_350 = fpext float %tmp_349 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_350)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_57, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_351_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_351_str, align 1
  %tmp_352 = bitcast [1 x i8]* %tmp_351_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_352)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PARENTHESES OVERRIDE PRECEDENCE ===" (inline)
  %tmp_353_str = alloca [40 x i8], align 1
  store [40 x i8] c"=== PARENTHESES OVERRIDE PRECEDENCE ===\00", [40 x i8]* %tmp_353_str, align 1
  %tmp_354 = bitcast [40 x i8]* %tmp_353_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_354)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int override1 = (a + b) * c
  %tmp_355 = load i32, i32* @global_a, align 4
  %tmp_356 = load i32, i32* @global_b, align 4
  %tmp_357 = add i32 %tmp_355, %tmp_356
  %tmp_358 = load i32, i32* @global_c, align 4
  %tmp_359 = mul i32 %tmp_357, %tmp_358
  store i32 %tmp_359, i32* @global_override1, align 4
  ; int override2 = a * (b + c)
  %tmp_360 = load i32, i32* @global_b, align 4
  %tmp_361 = load i32, i32* @global_c, align 4
  %tmp_362 = add i32 %tmp_360, %tmp_361
  %tmp_363 = load i32, i32* @global_a, align 4
  %tmp_364 = mul i32 %tmp_363, %tmp_362
  store i32 %tmp_364, i32* @global_override2, align 4
  ; int override3 = (a + b) * (c - d)
  %tmp_365 = load i32, i32* @global_a, align 4
  %tmp_366 = load i32, i32* @global_b, align 4
  %tmp_367 = add i32 %tmp_365, %tmp_366
  %tmp_368 = load i32, i32* @global_c, align 4
  %tmp_369 = load i32, i32* @global_d, align 4
  %tmp_370 = sub i32 %tmp_368, %tmp_369
  %tmp_371 = mul i32 %tmp_367, %tmp_370
  store i32 %tmp_371, i32* @global_override3, align 4
  ; float override4 = (x + y) * z
  %tmp_372 = load float, float* @global_x, align 4
  %tmp_373 = load float, float* @global_y, align 4
  %tmp_374 = fadd float %tmp_372, %tmp_373
  %tmp_375 = load float, float* @global_z, align 4
  %tmp_376 = fmul float %tmp_374, %tmp_375
  store float %tmp_376, float* @global_override4, align 4
  ; float override5 = x * (y + z)
  %tmp_377 = load float, float* @global_y, align 4
  %tmp_378 = load float, float* @global_z, align 4
  %tmp_379 = fadd float %tmp_377, %tmp_378
  %tmp_380 = load float, float* @global_x, align 4
  %tmp_381 = fmul float %tmp_380, %tmp_379
  store float %tmp_381, float* @global_override5, align 4
  ; print expression: "(a + b) * c = (" + a + " + " + b + ") * " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_382 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_382)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_383 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_383)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_384 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_384)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_385 = load i32, i32* @global_override1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_385)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_59, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * (b + c) = " + a + " * (" + b + " + " + c + ")...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_386 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_386)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_387 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_387)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_388 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_388)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_389 = load i32, i32* @global_override2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_389)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_59, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a + b) * (c - d) = (" + a + " + " + b + ") * (" ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_60, i32 0, i32 0))
  %tmp_390 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_390)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_391 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_391)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_expr_61, i32 0, i32 0))
  %tmp_392 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_392)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_393 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_393)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_394 = load i32, i32* @global_override3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_394)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(x + y) * z = (" + x + " + " + y + ") * " + z + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_395 = load float, float* @global_x, align 4
  %tmp_396 = fpext float %tmp_395 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_396)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_397 = load float, float* @global_y, align 4
  %tmp_398 = fpext float %tmp_397 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_398)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_399 = load float, float* @global_z, align 4
  %tmp_400 = fpext float %tmp_399 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_400)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_401 = load float, float* @global_override4, align 4
  %tmp_402 = fpext float %tmp_401 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_402)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_62, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x * (y + z) = " + x + " * (" + y + " + " + z + ")...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_39, i32 0, i32 0))
  %tmp_403 = load float, float* @global_x, align 4
  %tmp_404 = fpext float %tmp_403 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_404)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_405 = load float, float* @global_y, align 4
  %tmp_406 = fpext float %tmp_405 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_406)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_407 = load float, float* @global_z, align 4
  %tmp_408 = fpext float %tmp_407 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_408)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_409 = load float, float* @global_override5, align 4
  %tmp_410 = fpext float %tmp_409 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_410)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_62, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_411_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_411_str, align 1
  %tmp_412 = bitcast [1 x i8]* %tmp_411_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_412)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== CHAIN OPERATIONS ===" (inline)
  %tmp_413_str = alloca [25 x i8], align 1
  store [25 x i8] c"=== CHAIN OPERATIONS ===\00", [25 x i8]* %tmp_413_str, align 1
  %tmp_414 = bitcast [25 x i8]* %tmp_413_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_414)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int chain1 = a + b + c + d
  %tmp_415 = load i32, i32* @global_a, align 4
  %tmp_416 = load i32, i32* @global_b, align 4
  %tmp_417 = add i32 %tmp_415, %tmp_416
  %tmp_418 = load i32, i32* @global_c, align 4
  %tmp_419 = add i32 %tmp_417, %tmp_418
  %tmp_420 = load i32, i32* @global_d, align 4
  %tmp_421 = add i32 %tmp_419, %tmp_420
  store i32 %tmp_421, i32* @global_chain1, align 4
  ; int chain2 = a - b - c - d
  %tmp_422 = load i32, i32* @global_a, align 4
  %tmp_423 = load i32, i32* @global_b, align 4
  %tmp_424 = sub i32 %tmp_422, %tmp_423
  %tmp_425 = load i32, i32* @global_c, align 4
  %tmp_426 = sub i32 %tmp_424, %tmp_425
  %tmp_427 = load i32, i32* @global_d, align 4
  %tmp_428 = sub i32 %tmp_426, %tmp_427
  store i32 %tmp_428, i32* @global_chain2, align 4
  ; int chain3 = a * b * c * d
  %tmp_429 = load i32, i32* @global_a, align 4
  %tmp_430 = load i32, i32* @global_b, align 4
  %tmp_431 = mul i32 %tmp_429, %tmp_430
  %tmp_432 = load i32, i32* @global_c, align 4
  %tmp_433 = mul i32 %tmp_431, %tmp_432
  %tmp_434 = load i32, i32* @global_d, align 4
  %tmp_435 = mul i32 %tmp_433, %tmp_434
  store i32 %tmp_435, i32* @global_chain3, align 4
  ; float chain4 = x + y + z + 1.0
  %tmp_436 = load float, float* @global_x, align 4
  %tmp_437 = load float, float* @global_y, align 4
  %tmp_438 = fadd float %tmp_436, %tmp_437
  %tmp_439 = load float, float* @global_z, align 4
  %tmp_440 = fadd float %tmp_438, %tmp_439
  %tmp_441 = bitcast i32 1065353216 to float
  %tmp_442 = fadd float %tmp_440, %tmp_441
  store float %tmp_442, float* @global_chain4, align 4
  ; float chain5 = x * y * z * 2.0
  %tmp_443 = load float, float* @global_x, align 4
  %tmp_444 = load float, float* @global_y, align 4
  %tmp_445 = fmul float %tmp_443, %tmp_444
  %tmp_446 = load float, float* @global_z, align 4
  %tmp_447 = fmul float %tmp_445, %tmp_446
  %tmp_448 = bitcast i32 1073741824 to float
  %tmp_449 = fmul float %tmp_447, %tmp_448
  store float %tmp_449, float* @global_chain5, align 4
  ; print expression: "a + b + c + d = " + a + " + " + b + " + " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_63, i32 0, i32 0))
  %tmp_450 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_450)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_451 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_451)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_452 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_452)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_453 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_453)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_454 = load i32, i32* @global_chain1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_454)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - b - c - d = " + a + " - " + b + " - " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_64, i32 0, i32 0))
  %tmp_455 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_455)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_456 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_456)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_457 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_457)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_458 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_458)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_459 = load i32, i32* @global_chain2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_459)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * b * c * d = " + a + " * " + b + " * " + c + "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_65, i32 0, i32 0))
  %tmp_460 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_460)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_461 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_461)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_462 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_462)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_463 = load i32, i32* @global_d, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_463)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_464 = load i32, i32* @global_chain3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_464)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x + y + z + 1.0 = " + x + " + " + y + " + " + z +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_66, i32 0, i32 0))
  %tmp_465 = load float, float* @global_x, align 4
  %tmp_466 = fpext float %tmp_465 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_466)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_467 = load float, float* @global_y, align 4
  %tmp_468 = fpext float %tmp_467 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_468)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_469 = load float, float* @global_z, align 4
  %tmp_470 = fpext float %tmp_469 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_470)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_67, i32 0, i32 0))
  %tmp_471 = load float, float* @global_chain4, align 4
  %tmp_472 = fpext float %tmp_471 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_472)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "x * y * z * 2.0 = " + x + " * " + y + " * " + z +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_68, i32 0, i32 0))
  %tmp_473 = load float, float* @global_x, align 4
  %tmp_474 = fpext float %tmp_473 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_474)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_475 = load float, float* @global_y, align 4
  %tmp_476 = fpext float %tmp_475 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_476)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_477 = load float, float* @global_z, align 4
  %tmp_478 = fpext float %tmp_477 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_478)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_69, i32 0, i32 0))
  %tmp_479 = load float, float* @global_chain5, align 4
  %tmp_480 = fpext float %tmp_479 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_480)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_481_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_481_str, align 1
  %tmp_482 = bitcast [1 x i8]* %tmp_481_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_482)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== NEGATIVE NUMBER OPERATIONS ===" (inline)
  %tmp_483_str = alloca [35 x i8], align 1
  store [35 x i8] c"=== NEGATIVE NUMBER OPERATIONS ===\00", [35 x i8]* %tmp_483_str, align 1
  %tmp_484 = bitcast [35 x i8]* %tmp_483_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_484)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int neg1 = negative + negative
  %tmp_485 = load i32, i32* @global_negative, align 4
  %tmp_486 = load i32, i32* @global_negative, align 4
  %tmp_487 = add i32 %tmp_485, %tmp_486
  store i32 %tmp_487, i32* @global_neg1, align 4
  ; int neg2 = negative * negative
  %tmp_488 = load i32, i32* @global_negative, align 4
  %tmp_489 = load i32, i32* @global_negative, align 4
  %tmp_490 = mul i32 %tmp_488, %tmp_489
  store i32 %tmp_490, i32* @global_neg2, align 4
  ; int neg3 = negative - negative
  %tmp_491 = load i32, i32* @global_negative, align 4
  %tmp_492 = load i32, i32* @global_negative, align 4
  %tmp_493 = sub i32 %tmp_491, %tmp_492
  store i32 %tmp_493, i32* @global_neg3, align 4
  ; int neg4 = a + negative
  %tmp_494 = load i32, i32* @global_a, align 4
  %tmp_495 = load i32, i32* @global_negative, align 4
  %tmp_496 = add i32 %tmp_494, %tmp_495
  store i32 %tmp_496, i32* @global_neg4, align 4
  ; int neg5 = a * negative
  %tmp_497 = load i32, i32* @global_a, align 4
  %tmp_498 = load i32, i32* @global_negative, align 4
  %tmp_499 = mul i32 %tmp_497, %tmp_498
  store i32 %tmp_499, i32* @global_neg5, align 4
  ; print expression: "negative + negative = " + negative + " + " + nega...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_70, i32 0, i32 0))
  %tmp_500 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_500)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_501 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_501)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_502 = load i32, i32* @global_neg1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_502)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative * negative = " + negative + " * " + nega...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_71, i32 0, i32 0))
  %tmp_503 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_503)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_504 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_504)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_505 = load i32, i32* @global_neg2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_505)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative - negative = " + negative + " - " + nega...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_72, i32 0, i32 0))
  %tmp_506 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_506)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_507 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_507)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_508 = load i32, i32* @global_neg3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_508)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + negative = " + a + " + " + negative + " = " +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_73, i32 0, i32 0))
  %tmp_509 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_509)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_510 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_510)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_511 = load i32, i32* @global_neg4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_511)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * negative = " + a + " * " + negative + " = " +...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_74, i32 0, i32 0))
  %tmp_512 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_512)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_513 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_513)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_514 = load i32, i32* @global_neg5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_514)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_515_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_515_str, align 1
  %tmp_516 = bitcast [1 x i8]* %tmp_515_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_516)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== ZERO OPERATIONS ===" (inline)
  %tmp_517_str = alloca [24 x i8], align 1
  store [24 x i8] c"=== ZERO OPERATIONS ===\00", [24 x i8]* %tmp_517_str, align 1
  %tmp_518 = bitcast [24 x i8]* %tmp_517_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_518)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int zero1 = zero + a
  %tmp_519 = load i32, i32* @global_zero, align 4
  %tmp_520 = load i32, i32* @global_a, align 4
  %tmp_521 = add i32 %tmp_519, %tmp_520
  store i32 %tmp_521, i32* @global_zero1, align 4
  ; int zero2 = zero * a
  %tmp_522 = load i32, i32* @global_zero, align 4
  %tmp_523 = load i32, i32* @global_a, align 4
  %tmp_524 = mul i32 %tmp_522, %tmp_523
  store i32 %tmp_524, i32* @global_zero2, align 4
  ; int zero3 = a + zero
  %tmp_525 = load i32, i32* @global_a, align 4
  %tmp_526 = load i32, i32* @global_zero, align 4
  %tmp_527 = add i32 %tmp_525, %tmp_526
  store i32 %tmp_527, i32* @global_zero3, align 4
  ; int zero4 = a * zero
  %tmp_528 = load i32, i32* @global_a, align 4
  %tmp_529 = load i32, i32* @global_zero, align 4
  %tmp_530 = mul i32 %tmp_528, %tmp_529
  store i32 %tmp_530, i32* @global_zero4, align 4
  ; int zero5 = zero - a
  %tmp_531 = load i32, i32* @global_zero, align 4
  %tmp_532 = load i32, i32* @global_a, align 4
  %tmp_533 = sub i32 %tmp_531, %tmp_532
  store i32 %tmp_533, i32* @global_zero5, align 4
  ; int zero6 = a - zero
  %tmp_534 = load i32, i32* @global_a, align 4
  %tmp_535 = load i32, i32* @global_zero, align 4
  %tmp_536 = sub i32 %tmp_534, %tmp_535
  store i32 %tmp_536, i32* @global_zero6, align 4
  ; print expression: "zero + a = " + zero + " + " + a + " = " + zero1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_75, i32 0, i32 0))
  %tmp_537 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_537)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_538 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_538)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_539 = load i32, i32* @global_zero1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_539)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero * a = " + zero + " * " + a + " = " + zero2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_76, i32 0, i32 0))
  %tmp_540 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_540)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_541 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_541)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_542 = load i32, i32* @global_zero2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_542)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a + zero = " + a + " + " + zero + " = " + zero3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_77, i32 0, i32 0))
  %tmp_543 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_543)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_544 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_544)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_545 = load i32, i32* @global_zero3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_545)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * zero = " + a + " * " + zero + " = " + zero4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_78, i32 0, i32 0))
  %tmp_546 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_546)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_547 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_547)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_548 = load i32, i32* @global_zero4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_548)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero - a = " + zero + " - " + a + " = " + zero5
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_79, i32 0, i32 0))
  %tmp_549 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_549)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_550 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_550)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_551 = load i32, i32* @global_zero5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_551)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a - zero = " + a + " - " + zero + " = " + zero6
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_80, i32 0, i32 0))
  %tmp_552 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_552)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_553 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_553)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_554 = load i32, i32* @global_zero6, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_554)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_555_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_555_str, align 1
  %tmp_556 = bitcast [1 x i8]* %tmp_555_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_556)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== LARGE NUMBER OPERATIONS ===" (inline)
  %tmp_557_str = alloca [32 x i8], align 1
  store [32 x i8] c"=== LARGE NUMBER OPERATIONS ===\00", [32 x i8]* %tmp_557_str, align 1
  %tmp_558 = bitcast [32 x i8]* %tmp_557_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_558)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int large1 = large + large
  %tmp_559 = load i32, i32* @global_large, align 4
  %tmp_560 = load i32, i32* @global_large, align 4
  %tmp_561 = add i32 %tmp_559, %tmp_560
  store i32 %tmp_561, i32* @global_large1, align 4
  ; int large2 = large * a
  %tmp_562 = load i32, i32* @global_large, align 4
  %tmp_563 = load i32, i32* @global_a, align 4
  %tmp_564 = mul i32 %tmp_562, %tmp_563
  store i32 %tmp_564, i32* @global_large2, align 4
  ; int large3 = large / a
  %tmp_565 = load i32, i32* @global_large, align 4
  %tmp_566 = load i32, i32* @global_a, align 4
  %tmp_567 = sdiv i32 %tmp_565, %tmp_566
  store i32 %tmp_567, i32* @global_large3, align 4
  ; int large4 = large % a
  %tmp_568 = load i32, i32* @global_large, align 4
  %tmp_569 = load i32, i32* @global_a, align 4
  %tmp_570 = srem i32 %tmp_568, %tmp_569
  store i32 %tmp_570, i32* @global_large4, align 4
  ; print expression: "large + large = " + large + " + " + large + " = "...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_81, i32 0, i32 0))
  %tmp_571 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_571)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_572 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_572)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_573 = load i32, i32* @global_large1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_573)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large * a = " + large + " * " + a + " = " + large...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_82, i32 0, i32 0))
  %tmp_574 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_574)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_575 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_575)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_576 = load i32, i32* @global_large2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_576)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large / a = " + large + " / " + a + " = " + large...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_83, i32 0, i32 0))
  %tmp_577 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_577)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_578 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_578)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_579 = load i32, i32* @global_large3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_579)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large % a = " + large + " % " + a + " = " + large...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_84, i32 0, i32 0))
  %tmp_580 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_580)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_581 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_581)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_582 = load i32, i32* @global_large4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_582)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_583_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_583_str, align 1
  %tmp_584 = bitcast [1 x i8]* %tmp_583_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_584)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== DIRECT CALCULATIONS IN PRINT ===" (inline)
  %tmp_585_str = alloca [37 x i8], align 1
  store [37 x i8] c"=== DIRECT CALCULATIONS IN PRINT ===\00", [37 x i8]* %tmp_585_str, align 1
  %tmp_586 = bitcast [37 x i8]* %tmp_585_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_586)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct: a + b = " + (a + b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_85, i32 0, i32 0))
  %tmp_587 = load i32, i32* @global_a, align 4
  %tmp_588 = load i32, i32* @global_b, align 4
  %tmp_589 = add i32 %tmp_587, %tmp_588
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_589)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct: (a + b) * c = " + ((a + b) * c)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_86, i32 0, i32 0))
  %tmp_590 = load i32, i32* @global_a, align 4
  %tmp_591 = load i32, i32* @global_b, align 4
  %tmp_592 = add i32 %tmp_590, %tmp_591
  %tmp_593 = load i32, i32* @global_c, align 4
  %tmp_594 = mul i32 %tmp_592, %tmp_593
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_594)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct: ((a + b) * c) - d = " + (((a + b) * c) - ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_87, i32 0, i32 0))
  %tmp_595 = load i32, i32* @global_a, align 4
  %tmp_596 = load i32, i32* @global_b, align 4
  %tmp_597 = add i32 %tmp_595, %tmp_596
  %tmp_598 = load i32, i32* @global_c, align 4
  %tmp_599 = mul i32 %tmp_597, %tmp_598
  %tmp_600 = load i32, i32* @global_d, align 4
  %tmp_601 = sub i32 %tmp_599, %tmp_600
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_601)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct: (a * b) + (c - d) = " + ((a * b) + (c - d...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_88, i32 0, i32 0))
  %tmp_602 = load i32, i32* @global_a, align 4
  %tmp_603 = load i32, i32* @global_b, align 4
  %tmp_604 = mul i32 %tmp_602, %tmp_603
  %tmp_605 = load i32, i32* @global_c, align 4
  %tmp_606 = load i32, i32* @global_d, align 4
  %tmp_607 = sub i32 %tmp_605, %tmp_606
  %tmp_608 = add i32 %tmp_604, %tmp_607
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_608)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct float: (x + y) * z = " + ((x + y) * z)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_89, i32 0, i32 0))
  %tmp_609 = load float, float* @global_x, align 4
  %tmp_610 = load float, float* @global_y, align 4
  %tmp_611 = fadd float %tmp_609, %tmp_610
  %tmp_612 = load float, float* @global_z, align 4
  %tmp_613 = fmul float %tmp_611, %tmp_612
  %tmp_614 = fpext float %tmp_613 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_614)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Direct mixed: a + x = " + (a + x)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_90, i32 0, i32 0))
  %tmp_615 = load i32, i32* @global_a, align 4
  %tmp_616 = load float, float* @global_x, align 4
  %tmp_618 = sitofp i32 %tmp_615 to float
  %tmp_617 = fadd float %tmp_618, %tmp_616
  %tmp_619 = fpext float %tmp_617 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_619)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_620_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_620_str, align 1
  %tmp_621 = bitcast [1 x i8]* %tmp_620_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_621)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== ASSIGNMENT OPERATIONS ===" (inline)
  %tmp_622_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== ASSIGNMENT OPERATIONS ===\00", [30 x i8]* %tmp_622_str, align 1
  %tmp_623 = bitcast [30 x i8]* %tmp_622_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_623)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int counter = 0
  store i32 0, i32* @global_counter, align 4
  ; print expression: "Initial counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_91, i32 0, i32 0))
  %tmp_624 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_624)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter + 1
  %tmp_625 = load i32, i32* @global_counter, align 4
  %tmp_626 = add i32 %tmp_625, 1
  store i32 %tmp_626, i32* @global_counter, align 4
  ; print expression: "After +1: counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_92, i32 0, i32 0))
  %tmp_627 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_627)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter * 3
  %tmp_628 = load i32, i32* @global_counter, align 4
  %tmp_629 = mul i32 %tmp_628, 3
  store i32 %tmp_629, i32* @global_counter, align 4
  ; print expression: "After *3: counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_93, i32 0, i32 0))
  %tmp_630 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_630)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter - 5
  %tmp_631 = load i32, i32* @global_counter, align 4
  %tmp_632 = sub i32 %tmp_631, 5
  store i32 %tmp_632, i32* @global_counter, align 4
  ; print expression: "After -5: counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_94, i32 0, i32 0))
  %tmp_633 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_633)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter / 2
  %tmp_634 = load i32, i32* @global_counter, align 4
  %tmp_635 = sdiv i32 %tmp_634, 2
  store i32 %tmp_635, i32* @global_counter, align 4
  ; print expression: "After /2: counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_95, i32 0, i32 0))
  %tmp_636 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_636)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = (counter + 10) * 2
  %tmp_637 = load i32, i32* @global_counter, align 4
  %tmp_638 = add i32 %tmp_637, 10
  %tmp_639 = mul i32 %tmp_638, 2
  store i32 %tmp_639, i32* @global_counter, align 4
  ; print expression: "After (counter + 10) * 2: counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([37 x i8], [37 x i8]* @.str_expr_96, i32 0, i32 0))
  %tmp_640 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_640)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = ((counter - 5) * 3) + 1
  %tmp_641 = load i32, i32* @global_counter, align 4
  %tmp_642 = sub i32 %tmp_641, 5
  %tmp_643 = mul i32 %tmp_642, 3
  %tmp_644 = add i32 %tmp_643, 1
  store i32 %tmp_644, i32* @global_counter, align 4
  ; print expression: "After ((counter - 5) * 3) + 1: counter = " + coun...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([42 x i8], [42 x i8]* @.str_expr_97, i32 0, i32 0))
  %tmp_645 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_645)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_646_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_646_str, align 1
  %tmp_647 = bitcast [1 x i8]* %tmp_646_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_647)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== COMPLEX REAL-WORLD SCENARIOS ===" (inline)
  %tmp_648_str = alloca [37 x i8], align 1
  store [37 x i8] c"=== COMPLEX REAL-WORLD SCENARIOS ===\00", [37 x i8]* %tmp_648_str, align 1
  %tmp_649 = bitcast [37 x i8]* %tmp_648_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_649)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int area_rectangle = a * b
  %tmp_650 = load i32, i32* @global_a, align 4
  %tmp_651 = load i32, i32* @global_b, align 4
  %tmp_652 = mul i32 %tmp_650, %tmp_651
  store i32 %tmp_652, i32* @global_area_rectangle, align 4
  ; int perimeter_rectangle = (a + b) * 2
  %tmp_653 = load i32, i32* @global_a, align 4
  %tmp_654 = load i32, i32* @global_b, align 4
  %tmp_655 = add i32 %tmp_653, %tmp_654
  %tmp_656 = mul i32 %tmp_655, 2
  store i32 %tmp_656, i32* @global_perimeter_rectangle, align 4
  ; int area_triangle = (a * b) / 2
  %tmp_657 = load i32, i32* @global_a, align 4
  %tmp_658 = load i32, i32* @global_b, align 4
  %tmp_659 = mul i32 %tmp_657, %tmp_658
  %tmp_660 = sdiv i32 %tmp_659, 2
  store i32 %tmp_660, i32* @global_area_triangle, align 4
  ; int pythagorean = (a * a) + (b * b)
  %tmp_661 = load i32, i32* @global_a, align 4
  %tmp_662 = load i32, i32* @global_a, align 4
  %tmp_663 = mul i32 %tmp_661, %tmp_662
  %tmp_664 = load i32, i32* @global_b, align 4
  %tmp_665 = load i32, i32* @global_b, align 4
  %tmp_666 = mul i32 %tmp_664, %tmp_665
  %tmp_667 = add i32 %tmp_663, %tmp_666
  store i32 %tmp_667, i32* @global_pythagorean, align 4
  ; float circle_area = 3.14 * (z * z)
  %tmp_668 = load float, float* @global_z, align 4
  %tmp_669 = load float, float* @global_z, align 4
  %tmp_670 = fmul float %tmp_668, %tmp_669
  %tmp_671 = bitcast i32 1078523331 to float
  %tmp_672 = fmul float %tmp_671, %tmp_670
  store float %tmp_672, float* @global_circle_area, align 4
  ; float compound_calc = ((x + y) * z) - ((x - y) / 2.0)
  %tmp_673 = load float, float* @global_x, align 4
  %tmp_674 = load float, float* @global_y, align 4
  %tmp_675 = fadd float %tmp_673, %tmp_674
  %tmp_676 = load float, float* @global_z, align 4
  %tmp_677 = fmul float %tmp_675, %tmp_676
  %tmp_678 = load float, float* @global_x, align 4
  %tmp_679 = load float, float* @global_y, align 4
  %tmp_680 = fsub float %tmp_678, %tmp_679
  %tmp_681 = bitcast i32 1073741824 to float
  %tmp_682 = fdiv float %tmp_680, %tmp_681
  %tmp_683 = fsub float %tmp_677, %tmp_682
  store float %tmp_683, float* @global_compound_calc, align 4
  ; print expression: "Rectangle area (a * b) = " + area_rectangle
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_98, i32 0, i32 0))
  %tmp_684 = load i32, i32* @global_area_rectangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_684)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Rectangle perimeter ((a + b) * 2) = " + perimeter...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([37 x i8], [37 x i8]* @.str_expr_99, i32 0, i32 0))
  %tmp_685 = load i32, i32* @global_perimeter_rectangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_685)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Triangle area ((a * b) / 2) = " + area_triangle
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_100, i32 0, i32 0))
  %tmp_686 = load i32, i32* @global_area_triangle, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_686)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Pythagorean (a^2 + b^2) = " + pythagorean
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_101, i32 0, i32 0))
  %tmp_687 = load i32, i32* @global_pythagorean, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_687)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Circle area (pi * r^2) = " + circle_area
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_102, i32 0, i32 0))
  %tmp_688 = load float, float* @global_circle_area, align 4
  %tmp_689 = fpext float %tmp_688 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_689)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Complex calculation = " + compound_calc
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_103, i32 0, i32 0))
  %tmp_690 = load float, float* @global_compound_calc, align 4
  %tmp_691 = fpext float %tmp_690 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_691)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_692_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_692_str, align 1
  %tmp_693 = bitcast [1 x i8]* %tmp_692_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_693)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EXTREME NESTING TESTS ===" (inline)
  %tmp_694_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== EXTREME NESTING TESTS ===\00", [30 x i8]* %tmp_694_str, align 1
  %tmp_695 = bitcast [30 x i8]* %tmp_694_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_695)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int extreme1 = (((a + b) * c) - d) + 1
  %tmp_696 = load i32, i32* @global_a, align 4
  %tmp_697 = load i32, i32* @global_b, align 4
  %tmp_698 = add i32 %tmp_696, %tmp_697
  %tmp_699 = load i32, i32* @global_c, align 4
  %tmp_700 = mul i32 %tmp_698, %tmp_699
  %tmp_701 = load i32, i32* @global_d, align 4
  %tmp_702 = sub i32 %tmp_700, %tmp_701
  %tmp_703 = add i32 %tmp_702, 1
  store i32 %tmp_703, i32* @global_extreme1, align 4
  ; int extreme2 = a + (((b * c) - d) * 2)
  %tmp_704 = load i32, i32* @global_b, align 4
  %tmp_705 = load i32, i32* @global_c, align 4
  %tmp_706 = mul i32 %tmp_704, %tmp_705
  %tmp_707 = load i32, i32* @global_d, align 4
  %tmp_708 = sub i32 %tmp_706, %tmp_707
  %tmp_709 = mul i32 %tmp_708, 2
  %tmp_710 = load i32, i32* @global_a, align 4
  %tmp_711 = add i32 %tmp_710, %tmp_709
  store i32 %tmp_711, i32* @global_extreme2, align 4
  ; int extreme3 = ((a + (b * c)) - (d + 1)) * 2
  %tmp_712 = load i32, i32* @global_b, align 4
  %tmp_713 = load i32, i32* @global_c, align 4
  %tmp_714 = mul i32 %tmp_712, %tmp_713
  %tmp_715 = load i32, i32* @global_a, align 4
  %tmp_716 = add i32 %tmp_715, %tmp_714
  %tmp_717 = load i32, i32* @global_d, align 4
  %tmp_718 = add i32 %tmp_717, 1
  %tmp_719 = sub i32 %tmp_716, %tmp_718
  %tmp_720 = mul i32 %tmp_719, 2
  store i32 %tmp_720, i32* @global_extreme3, align 4
  ; float extreme4 = (((x + y) * z) - ((x - y) / 2.0)) + 1.0
  %tmp_721 = load float, float* @global_x, align 4
  %tmp_722 = load float, float* @global_y, align 4
  %tmp_723 = fadd float %tmp_721, %tmp_722
  %tmp_724 = load float, float* @global_z, align 4
  %tmp_725 = fmul float %tmp_723, %tmp_724
  %tmp_726 = load float, float* @global_x, align 4
  %tmp_727 = load float, float* @global_y, align 4
  %tmp_728 = fsub float %tmp_726, %tmp_727
  %tmp_729 = bitcast i32 1073741824 to float
  %tmp_730 = fdiv float %tmp_728, %tmp_729
  %tmp_731 = fsub float %tmp_725, %tmp_730
  %tmp_732 = bitcast i32 1065353216 to float
  %tmp_733 = fadd float %tmp_731, %tmp_732
  store float %tmp_733, float* @global_extreme4, align 4
  ; int extreme5 = ((a + b) * (c + d)) - ((a - b) * (c - d))
  %tmp_734 = load i32, i32* @global_a, align 4
  %tmp_735 = load i32, i32* @global_b, align 4
  %tmp_736 = add i32 %tmp_734, %tmp_735
  %tmp_737 = load i32, i32* @global_c, align 4
  %tmp_738 = load i32, i32* @global_d, align 4
  %tmp_739 = add i32 %tmp_737, %tmp_738
  %tmp_740 = mul i32 %tmp_736, %tmp_739
  %tmp_741 = load i32, i32* @global_a, align 4
  %tmp_742 = load i32, i32* @global_b, align 4
  %tmp_743 = sub i32 %tmp_741, %tmp_742
  %tmp_744 = load i32, i32* @global_c, align 4
  %tmp_745 = load i32, i32* @global_d, align 4
  %tmp_746 = sub i32 %tmp_744, %tmp_745
  %tmp_747 = mul i32 %tmp_743, %tmp_746
  %tmp_748 = sub i32 %tmp_740, %tmp_747
  store i32 %tmp_748, i32* @global_extreme5, align 4
  ; print expression: "Extreme 1: (((a + b) * c) - d) + 1 = " + extreme1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([38 x i8], [38 x i8]* @.str_expr_104, i32 0, i32 0))
  %tmp_749 = load i32, i32* @global_extreme1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_749)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Extreme 2: a + (((b * c) - d) * 2) = " + extreme2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([38 x i8], [38 x i8]* @.str_expr_105, i32 0, i32 0))
  %tmp_750 = load i32, i32* @global_extreme2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_750)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Extreme 3: ((a + (b * c)) - (d + 1)) * 2 = " + ex...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([44 x i8], [44 x i8]* @.str_expr_106, i32 0, i32 0))
  %tmp_751 = load i32, i32* @global_extreme3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_751)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Extreme 4: (((x + y) * z) - ((x - y) / 2.0)) + 1....
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([54 x i8], [54 x i8]* @.str_expr_107, i32 0, i32 0))
  %tmp_752 = load float, float* @global_extreme4, align 4
  %tmp_753 = fpext float %tmp_752 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_753)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Extreme 5: ((a + b) * (c + d)) - ((a - b) * (c - ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([56 x i8], [56 x i8]* @.str_expr_108, i32 0, i32 0))
  %tmp_754 = load i32, i32* @global_extreme5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_754)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_755_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_755_str, align 1
  %tmp_756 = bitcast [1 x i8]* %tmp_755_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_756)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== MATHEMATICAL FORMULAS ===" (inline)
  %tmp_757_str = alloca [30 x i8], align 1
  store [30 x i8] c"=== MATHEMATICAL FORMULAS ===\00", [30 x i8]* %tmp_757_str, align 1
  %tmp_758 = bitcast [30 x i8]* %tmp_757_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_758)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int quadratic = (a * a) + (b * a) + c
  %tmp_759 = load i32, i32* @global_a, align 4
  %tmp_760 = load i32, i32* @global_a, align 4
  %tmp_761 = mul i32 %tmp_759, %tmp_760
  %tmp_762 = load i32, i32* @global_b, align 4
  %tmp_763 = load i32, i32* @global_a, align 4
  %tmp_764 = mul i32 %tmp_762, %tmp_763
  %tmp_765 = add i32 %tmp_761, %tmp_764
  %tmp_766 = load i32, i32* @global_c, align 4
  %tmp_767 = add i32 %tmp_765, %tmp_766
  store i32 %tmp_767, i32* @global_quadratic, align 4
  ; float distance = ((x * x) + (y * y))
  %tmp_768 = load float, float* @global_x, align 4
  %tmp_769 = load float, float* @global_x, align 4
  %tmp_770 = fmul float %tmp_768, %tmp_769
  %tmp_771 = load float, float* @global_y, align 4
  %tmp_772 = load float, float* @global_y, align 4
  %tmp_773 = fmul float %tmp_771, %tmp_772
  %tmp_774 = fadd float %tmp_770, %tmp_773
  store float %tmp_774, float* @global_distance, align 4
  ; int factorial_approx = 1 * 2 * 3 * 4
  %tmp_775 = mul i32 1, 2
  %tmp_776 = mul i32 %tmp_775, 3
  %tmp_777 = mul i32 %tmp_776, 4
  store i32 %tmp_777, i32* @global_factorial_approx, align 4
  ; float average = (x + y + z) / 3.0
  %tmp_778 = load float, float* @global_x, align 4
  %tmp_779 = load float, float* @global_y, align 4
  %tmp_780 = fadd float %tmp_778, %tmp_779
  %tmp_781 = load float, float* @global_z, align 4
  %tmp_782 = fadd float %tmp_780, %tmp_781
  %tmp_783 = bitcast i32 1077936128 to float
  %tmp_784 = fdiv float %tmp_782, %tmp_783
  store float %tmp_784, float* @global_average, align 4
  ; int sum_of_squares = (a * a) + (b * b) + (c * c)
  %tmp_785 = load i32, i32* @global_a, align 4
  %tmp_786 = load i32, i32* @global_a, align 4
  %tmp_787 = mul i32 %tmp_785, %tmp_786
  %tmp_788 = load i32, i32* @global_b, align 4
  %tmp_789 = load i32, i32* @global_b, align 4
  %tmp_790 = mul i32 %tmp_788, %tmp_789
  %tmp_791 = load i32, i32* @global_c, align 4
  %tmp_792 = load i32, i32* @global_c, align 4
  %tmp_793 = mul i32 %tmp_791, %tmp_792
  %tmp_794 = add i32 %tmp_787, %tmp_790
  %tmp_795 = add i32 %tmp_794, %tmp_793
  store i32 %tmp_795, i32* @global_sum_of_squares, align 4
  ; print expression: "Quadratic (a^2 + ba + c) = " + quadratic
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_109, i32 0, i32 0))
  %tmp_796 = load i32, i32* @global_quadratic, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_796)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Distance^2 (x^2 + y^2) = " + distance
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([26 x i8], [26 x i8]* @.str_expr_110, i32 0, i32 0))
  %tmp_797 = load float, float* @global_distance, align 4
  %tmp_798 = fpext float %tmp_797 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_798)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Factorial 4! approx 1*2*3*4 = " + factorial_appro...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([31 x i8], [31 x i8]* @.str_expr_111, i32 0, i32 0))
  %tmp_799 = load i32, i32* @global_factorial_approx, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_799)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Average (x+y+z)/3 = " + average
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_112, i32 0, i32 0))
  %tmp_800 = load float, float* @global_average, align 4
  %tmp_801 = fpext float %tmp_800 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_801)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Sum of squares (a^2+b^2+c^2) = " + sum_of_squares
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([32 x i8], [32 x i8]* @.str_expr_113, i32 0, i32 0))
  %tmp_802 = load i32, i32* @global_sum_of_squares, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_802)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_803_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_803_str, align 1
  %tmp_804 = bitcast [1 x i8]* %tmp_803_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_804)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EDGE CASES ===" (inline)
  %tmp_805_str = alloca [19 x i8], align 1
  store [19 x i8] c"=== EDGE CASES ===\00", [19 x i8]* %tmp_805_str, align 1
  %tmp_806 = bitcast [19 x i8]* %tmp_805_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_806)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int edge1 = 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10
  %tmp_807 = add i32 1, 2
  %tmp_808 = add i32 %tmp_807, 3
  %tmp_809 = add i32 %tmp_808, 4
  %tmp_810 = add i32 %tmp_809, 5
  %tmp_811 = add i32 %tmp_810, 6
  %tmp_812 = add i32 %tmp_811, 7
  %tmp_813 = add i32 %tmp_812, 8
  %tmp_814 = add i32 %tmp_813, 9
  %tmp_815 = add i32 %tmp_814, 10
  store i32 %tmp_815, i32* @global_edge1, align 4
  ; int edge2 = ((((a + 1) + 1) + 1) + 1) + 1
  %tmp_816 = load i32, i32* @global_a, align 4
  %tmp_817 = add i32 %tmp_816, 1
  %tmp_818 = add i32 %tmp_817, 1
  %tmp_819 = add i32 %tmp_818, 1
  %tmp_820 = add i32 %tmp_819, 1
  %tmp_821 = add i32 %tmp_820, 1
  store i32 %tmp_821, i32* @global_edge2, align 4
  ; float edge3 = x * 0.0
  %tmp_822 = load float, float* @global_x, align 4
  %tmp_823 = bitcast i32 0 to float
  %tmp_824 = fmul float %tmp_822, %tmp_823
  store float %tmp_824, float* @global_edge3, align 4
  ; float edge4 = y / 1.0
  %tmp_825 = load float, float* @global_y, align 4
  %tmp_826 = bitcast i32 1065353216 to float
  %tmp_827 = fdiv float %tmp_825, %tmp_826
  store float %tmp_827, float* @global_edge4, align 4
  ; int edge5 = a * 1
  %tmp_828 = load i32, i32* @global_a, align 4
  %tmp_829 = add i32 0, 1
  %tmp_830 = mul i32 %tmp_828, %tmp_829
  store i32 %tmp_830, i32* @global_edge5, align 4
  ; print expression: "Sum 1-10: " + edge1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_114, i32 0, i32 0))
  %tmp_831 = load i32, i32* @global_edge1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_831)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Nested additions: " + edge2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_115, i32 0, i32 0))
  %tmp_832 = load i32, i32* @global_edge2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_832)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float * 0.0: " + edge3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_116, i32 0, i32 0))
  %tmp_833 = load float, float* @global_edge3, align 4
  %tmp_834 = fpext float %tmp_833 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_834)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float / 1.0: " + edge4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_117, i32 0, i32 0))
  %tmp_835 = load float, float* @global_edge4, align 4
  %tmp_836 = fpext float %tmp_835 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_836)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Int * 1: " + edge5
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_118, i32 0, i32 0))
  %tmp_837 = load i32, i32* @global_edge5, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_837)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_838_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_838_str, align 1
  %tmp_839 = bitcast [1 x i8]* %tmp_838_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_839)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== PERFORMANCE STRESS TEST ===" (inline)
  %tmp_840_str = alloca [32 x i8], align 1
  store [32 x i8] c"=== PERFORMANCE STRESS TEST ===\00", [32 x i8]* %tmp_840_str, align 1
  %tmp_841 = bitcast [32 x i8]* %tmp_840_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_841)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int stress1 = ((((((a + b) * c) - d) + a) * b) - c) + d
  %tmp_842 = load i32, i32* @global_a, align 4
  %tmp_843 = load i32, i32* @global_b, align 4
  %tmp_844 = add i32 %tmp_842, %tmp_843
  %tmp_845 = load i32, i32* @global_c, align 4
  %tmp_846 = mul i32 %tmp_844, %tmp_845
  %tmp_847 = load i32, i32* @global_d, align 4
  %tmp_848 = sub i32 %tmp_846, %tmp_847
  %tmp_849 = load i32, i32* @global_a, align 4
  %tmp_850 = add i32 %tmp_848, %tmp_849
  %tmp_851 = load i32, i32* @global_b, align 4
  %tmp_852 = mul i32 %tmp_850, %tmp_851
  %tmp_853 = load i32, i32* @global_c, align 4
  %tmp_854 = sub i32 %tmp_852, %tmp_853
  %tmp_855 = load i32, i32* @global_d, align 4
  %tmp_856 = add i32 %tmp_854, %tmp_855
  store i32 %tmp_856, i32* @global_stress1, align 4
  ; int stress2 = (a * b * c * d) + (a + b + c + d) - (a - b - c - d)
  %tmp_857 = load i32, i32* @global_a, align 4
  %tmp_858 = load i32, i32* @global_b, align 4
  %tmp_859 = mul i32 %tmp_857, %tmp_858
  %tmp_860 = load i32, i32* @global_c, align 4
  %tmp_861 = mul i32 %tmp_859, %tmp_860
  %tmp_862 = load i32, i32* @global_d, align 4
  %tmp_863 = mul i32 %tmp_861, %tmp_862
  %tmp_864 = load i32, i32* @global_a, align 4
  %tmp_865 = load i32, i32* @global_b, align 4
  %tmp_866 = add i32 %tmp_864, %tmp_865
  %tmp_867 = load i32, i32* @global_c, align 4
  %tmp_868 = add i32 %tmp_866, %tmp_867
  %tmp_869 = load i32, i32* @global_d, align 4
  %tmp_870 = add i32 %tmp_868, %tmp_869
  %tmp_871 = load i32, i32* @global_a, align 4
  %tmp_872 = load i32, i32* @global_b, align 4
  %tmp_873 = sub i32 %tmp_871, %tmp_872
  %tmp_874 = load i32, i32* @global_c, align 4
  %tmp_875 = sub i32 %tmp_873, %tmp_874
  %tmp_876 = load i32, i32* @global_d, align 4
  %tmp_877 = sub i32 %tmp_875, %tmp_876
  %tmp_878 = add i32 %tmp_863, %tmp_870
  %tmp_879 = sub i32 %tmp_878, %tmp_877
  store i32 %tmp_879, i32* @global_stress2, align 4
  ; float stress3 = ((((x + y) * z) / 2.0) + x) - ((y * z) / 3.0)
  %tmp_880 = load float, float* @global_x, align 4
  %tmp_881 = load float, float* @global_y, align 4
  %tmp_882 = fadd float %tmp_880, %tmp_881
  %tmp_883 = load float, float* @global_z, align 4
  %tmp_884 = fmul float %tmp_882, %tmp_883
  %tmp_885 = bitcast i32 1073741824 to float
  %tmp_886 = fdiv float %tmp_884, %tmp_885
  %tmp_887 = load float, float* @global_x, align 4
  %tmp_888 = fadd float %tmp_886, %tmp_887
  %tmp_889 = load float, float* @global_y, align 4
  %tmp_890 = load float, float* @global_z, align 4
  %tmp_891 = fmul float %tmp_889, %tmp_890
  %tmp_892 = bitcast i32 1077936128 to float
  %tmp_893 = fdiv float %tmp_891, %tmp_892
  %tmp_894 = fsub float %tmp_888, %tmp_893
  store float %tmp_894, float* @global_stress3, align 4
  ; int stress4 = (((a + b) * (c + d)) + ((a - b) * (c - d))) / 2
  %tmp_895 = load i32, i32* @global_a, align 4
  %tmp_896 = load i32, i32* @global_b, align 4
  %tmp_897 = add i32 %tmp_895, %tmp_896
  %tmp_898 = load i32, i32* @global_c, align 4
  %tmp_899 = load i32, i32* @global_d, align 4
  %tmp_900 = add i32 %tmp_898, %tmp_899
  %tmp_901 = mul i32 %tmp_897, %tmp_900
  %tmp_902 = load i32, i32* @global_a, align 4
  %tmp_903 = load i32, i32* @global_b, align 4
  %tmp_904 = sub i32 %tmp_902, %tmp_903
  %tmp_905 = load i32, i32* @global_c, align 4
  %tmp_906 = load i32, i32* @global_d, align 4
  %tmp_907 = sub i32 %tmp_905, %tmp_906
  %tmp_908 = mul i32 %tmp_904, %tmp_907
  %tmp_909 = add i32 %tmp_901, %tmp_908
  %tmp_910 = sdiv i32 %tmp_909, 2
  store i32 %tmp_910, i32* @global_stress4, align 4
  ; print expression: "Stress test 1: " + stress1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_119, i32 0, i32 0))
  %tmp_911 = load i32, i32* @global_stress1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_911)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Stress test 2: " + stress2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_120, i32 0, i32 0))
  %tmp_912 = load i32, i32* @global_stress2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_912)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Stress test 3: " + stress3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_121, i32 0, i32 0))
  %tmp_913 = load float, float* @global_stress3, align 4
  %tmp_914 = fpext float %tmp_913 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_914)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Stress test 4: " + stress4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_122, i32 0, i32 0))
  %tmp_915 = load i32, i32* @global_stress4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_915)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_916_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_916_str, align 1
  %tmp_917 = bitcast [1 x i8]* %tmp_916_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_917)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== FINAL VERIFICATION ===" (inline)
  %tmp_918_str = alloca [27 x i8], align 1
  store [27 x i8] c"=== FINAL VERIFICATION ===\00", [27 x i8]* %tmp_918_str, align 1
  %tmp_919 = bitcast [27 x i8]* %tmp_918_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_919)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Testing all operation types in one expression:" (inline)
  %tmp_920_str = alloca [47 x i8], align 1
  store [47 x i8] c"Testing all operation types in one expression:\00", [47 x i8]* %tmp_920_str, align 1
  %tmp_921 = bitcast [47 x i8]* %tmp_920_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_921)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int final_int = ((a + b) * c) - ((d * 2) + 1) + (a % b)
  %tmp_922 = load i32, i32* @global_a, align 4
  %tmp_923 = load i32, i32* @global_b, align 4
  %tmp_924 = add i32 %tmp_922, %tmp_923
  %tmp_925 = load i32, i32* @global_c, align 4
  %tmp_926 = mul i32 %tmp_924, %tmp_925
  %tmp_927 = load i32, i32* @global_d, align 4
  %tmp_928 = mul i32 %tmp_927, 2
  %tmp_929 = add i32 %tmp_928, 1
  %tmp_930 = load i32, i32* @global_a, align 4
  %tmp_931 = load i32, i32* @global_b, align 4
  %tmp_932 = srem i32 %tmp_930, %tmp_931
  %tmp_933 = sub i32 %tmp_926, %tmp_929
  %tmp_934 = add i32 %tmp_933, %tmp_932
  store i32 %tmp_934, i32* @global_final_int, align 4
  ; float final_float = ((x + y) * z) - ((x - y) / 2.0) + (y * 0.5)
  %tmp_935 = load float, float* @global_x, align 4
  %tmp_936 = load float, float* @global_y, align 4
  %tmp_937 = fadd float %tmp_935, %tmp_936
  %tmp_938 = load float, float* @global_z, align 4
  %tmp_939 = fmul float %tmp_937, %tmp_938
  %tmp_940 = load float, float* @global_x, align 4
  %tmp_941 = load float, float* @global_y, align 4
  %tmp_942 = fsub float %tmp_940, %tmp_941
  %tmp_943 = bitcast i32 1073741824 to float
  %tmp_944 = fdiv float %tmp_942, %tmp_943
  %tmp_945 = load float, float* @global_y, align 4
  %tmp_946 = bitcast i32 1056964608 to float
  %tmp_947 = fmul float %tmp_945, %tmp_946
  %tmp_948 = fsub float %tmp_939, %tmp_944
  %tmp_949 = fadd float %tmp_948, %tmp_947
  store float %tmp_949, float* @global_final_float, align 4
  ; print expression: "Final integer result: " + final_int
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([23 x i8], [23 x i8]* @.str_expr_123, i32 0, i32 0))
  %tmp_950 = load i32, i32* @global_final_int, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_950)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Final float result: " + final_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_124, i32 0, i32 0))
  %tmp_951 = load float, float* @global_final_float, align 4
  %tmp_952 = fpext float %tmp_951 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_952)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_953_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_953_str, align 1
  %tmp_954 = bitcast [1 x i8]* %tmp_953_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_954)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== END OF COMPREHENSIVE ARITHMETIC OPERATIONS ===" (inline)
  %tmp_955_str = alloca [51 x i8], align 1
  store [51 x i8] c"=== END OF COMPREHENSIVE ARITHMETIC OPERATIONS ===\00", [51 x i8]* %tmp_955_str, align 1
  %tmp_956 = bitcast [51 x i8]* %tmp_955_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_956)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "All arithmetic operations tested and verified!" (inline)
  %tmp_957_str = alloca [47 x i8], align 1
  store [47 x i8] c"All arithmetic operations tested and verified!\00", [47 x i8]* %tmp_957_str, align 1
  %tmp_958 = bitcast [47 x i8]* %tmp_957_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_958)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Integer operations: WORKING" (inline)
  %tmp_959_str = alloca [33 x i8], align 1
  store [33 x i8] c"[OK] Integer operations: WORKING\00", [33 x i8]* %tmp_959_str, align 1
  %tmp_960 = bitcast [33 x i8]* %tmp_959_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_960)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Float operations: WORKING" (inline)
  %tmp_961_str = alloca [31 x i8], align 1
  store [31 x i8] c"[OK] Float operations: WORKING\00", [31 x i8]* %tmp_961_str, align 1
  %tmp_962 = bitcast [31 x i8]* %tmp_961_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_962)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Mixed operations: WORKING" (inline)
  %tmp_963_str = alloca [31 x i8], align 1
  store [31 x i8] c"[OK] Mixed operations: WORKING\00", [31 x i8]* %tmp_963_str, align 1
  %tmp_964 = bitcast [31 x i8]* %tmp_963_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_964)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Parentheses: WORKING" (inline)
  %tmp_965_str = alloca [26 x i8], align 1
  store [26 x i8] c"[OK] Parentheses: WORKING\00", [26 x i8]* %tmp_965_str, align 1
  %tmp_966 = bitcast [26 x i8]* %tmp_965_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_966)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Nested expressions: WORKING" (inline)
  %tmp_967_str = alloca [33 x i8], align 1
  store [33 x i8] c"[OK] Nested expressions: WORKING\00", [33 x i8]* %tmp_967_str, align 1
  %tmp_968 = bitcast [33 x i8]* %tmp_967_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_968)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Operator precedence: WORKING" (inline)
  %tmp_969_str = alloca [34 x i8], align 1
  store [34 x i8] c"[OK] Operator precedence: WORKING\00", [34 x i8]* %tmp_969_str, align 1
  %tmp_970 = bitcast [34 x i8]* %tmp_969_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_970)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Chain operations: WORKING" (inline)
  %tmp_971_str = alloca [31 x i8], align 1
  store [31 x i8] c"[OK] Chain operations: WORKING\00", [31 x i8]* %tmp_971_str, align 1
  %tmp_972 = bitcast [31 x i8]* %tmp_971_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_972)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Assignment operations: WORKING" (inline)
  %tmp_973_str = alloca [36 x i8], align 1
  store [36 x i8] c"[OK] Assignment operations: WORKING\00", [36 x i8]* %tmp_973_str, align 1
  %tmp_974 = bitcast [36 x i8]* %tmp_973_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_974)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Direct print calculations: WORKING" (inline)
  %tmp_975_str = alloca [40 x i8], align 1
  store [40 x i8] c"[OK] Direct print calculations: WORKING\00", [40 x i8]* %tmp_975_str, align 1
  %tmp_976 = bitcast [40 x i8]* %tmp_975_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_976)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Complex real-world scenarios: WORKING" (inline)
  %tmp_977_str = alloca [43 x i8], align 1
  store [43 x i8] c"[OK] Complex real-world scenarios: WORKING\00", [43 x i8]* %tmp_977_str, align 1
  %tmp_978 = bitcast [43 x i8]* %tmp_977_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_978)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Extreme nesting: WORKING" (inline)
  %tmp_979_str = alloca [30 x i8], align 1
  store [30 x i8] c"[OK] Extreme nesting: WORKING\00", [30 x i8]* %tmp_979_str, align 1
  %tmp_980 = bitcast [30 x i8]* %tmp_979_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_980)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Mathematical formulas: WORKING" (inline)
  %tmp_981_str = alloca [36 x i8], align 1
  store [36 x i8] c"[OK] Mathematical formulas: WORKING\00", [36 x i8]* %tmp_981_str, align 1
  %tmp_982 = bitcast [36 x i8]* %tmp_981_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_982)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Edge cases: WORKING" (inline)
  %tmp_983_str = alloca [25 x i8], align 1
  store [25 x i8] c"[OK] Edge cases: WORKING\00", [25 x i8]* %tmp_983_str, align 1
  %tmp_984 = bitcast [25 x i8]* %tmp_983_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_984)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ Performance stress tests: WORKING" (inline)
  %tmp_985_str = alloca [39 x i8], align 1
  store [39 x i8] c"[OK] Performance stress tests: WORKING\00", [39 x i8]* %tmp_985_str, align 1
  %tmp_986 = bitcast [39 x i8]* %tmp_985_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_986)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_987_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_987_str, align 1
  %tmp_988 = bitcast [1 x i8]* %tmp_987_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_988)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎉 DOLET ARITHMETIC SYSTEM: FULLY OPERATIONAL! 🎉" (inline)
  %tmp_989_str = alloca [64 x i8], align 1
  store [64 x i8] c"[SUCCESS] DOLET ARITHMETIC SYSTEM: FULLY OPERATIONAL! [SUCCESS]\00", [64 x i8]* %tmp_989_str, align 1
  %tmp_990 = bitcast [64 x i8]* %tmp_989_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_990)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
