# 30. Data Structures - Advanced Data Organization
# This file demonstrates data structures implementation in Dolet

print "=== Data Structures Demo ==="
print ""

# Stack implementation
print "Stack Data Structure:"

array int stack_data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # Max 10 elements
int stack_top = -1

fun stack_push(value) {
    print "Push(" + value + "):"
    
    if stack_top >= 9
        print "  Error: Stack overflow"
        return false
    
    set stack_top = stack_top + 1
    set stack_data[stack_top] = value
    print "  Pushed " + value + " at index " + stack_top
    
    return true
}

fun stack_pop() {
    print "Pop():"
    
    if stack_top < 0
        print "  Error: Stack underflow"
        return -1
    
    int value = stack_data[stack_top]
    set stack_top = stack_top - 1
    print "  Popped " + value + " from index " + (stack_top + 1)
    
    return value
}

fun stack_peek() {
    if stack_top < 0
        print "  Stack is empty"
        return -1
    
    print "  Top element: " + stack_data[stack_top]
    return stack_data[stack_top]
}

print "Stack operations:"
bool push1 = stack_push(10)
bool push2 = stack_push(20)
bool push3 = stack_push(30)
int peek1 = stack_peek()
int pop1 = stack_pop()
int pop2 = stack_pop()
int peek2 = stack_peek()
print ""

# Queue implementation
print "Queue Data Structure:"

array int queue_data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # Max 10 elements
int queue_front = 0
int queue_rear = -1
int queue_size = 0

fun queue_enqueue(value) {
    print "Enqueue(" + value + "):"
    
    if queue_size >= 10
        print "  Error: Queue overflow"
        return false
    
    set queue_rear = (queue_rear + 1) % 10
    set queue_data[queue_rear] = value
    set queue_size = queue_size + 1
    
    print "  Enqueued " + value + " at rear index " + queue_rear
    print "  Queue size: " + queue_size
    
    return true
}

fun queue_dequeue() {
    print "Dequeue():"
    
    if queue_size <= 0
        print "  Error: Queue underflow"
        return -1
    
    int value = queue_data[queue_front]
    set queue_front = (queue_front + 1) % 10
    set queue_size = queue_size - 1
    
    print "  Dequeued " + value + " from front"
    print "  Queue size: " + queue_size
    
    return value
}

print "Queue operations:"
bool enq1 = queue_enqueue(100)
bool enq2 = queue_enqueue(200)
bool enq3 = queue_enqueue(300)
int deq1 = queue_dequeue()
bool enq4 = queue_enqueue(400)
int deq2 = queue_dequeue()
print ""

# Linked List simulation
print "Linked List Data Structure:"

# Simulated linked list using arrays
array int list_values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
array int list_next = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
int list_head = -1
int list_free = 0

fun list_insert(value) {
    print "Insert(" + value + "):"
    
    if list_free >= 10
        print "  Error: No free nodes"
        return false
    
    int new_node = list_free
    set list_free = list_free + 1
    
    set list_values[new_node] = value
    set list_next[new_node] = list_head
    set list_head = new_node
    
    print "  Inserted " + value + " as new head (node " + new_node + ")"
    return true
}

fun list_display() {
    print "List contents:"
    
    if list_head == -1
        print "  List is empty"
        return
    
    int current = list_head
    int position = 0
    
    while current != -1 and position < 10
        print "  Node " + position + ": " + list_values[current]
        set current = list_next[current]
        set position = position + 1
    
    print "  Total nodes: " + position
}

fun list_search(value) {
    print "Search(" + value + "):"
    
    int current = list_head
    int position = 0
    
    while current != -1 and position < 10
        if list_values[current] == value
            print "  Found " + value + " at position " + position
            return position
        
        set current = list_next[current]
        set position = position + 1
    
    print "  " + value + " not found in list"
    return -1
}

print "Linked List operations:"
bool ins1 = list_insert(50)
bool ins2 = list_insert(60)
bool ins3 = list_insert(70)
list_display()
int search1 = list_search(60)
int search2 = list_search(99)
print ""

# Binary Tree simulation
print "Binary Tree Data Structure:"

# Simulated binary tree using arrays
array int tree_values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
array int tree_left = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
array int tree_right = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
int tree_root = -1
int tree_free = 0

fun tree_insert(value) {
    print "Tree Insert(" + value + "):"
    
    if tree_free >= 10
        print "  Error: No free nodes"
        return false
    
    int new_node = tree_free
    set tree_free = tree_free + 1
    set tree_values[new_node] = value
    
    if tree_root == -1
        set tree_root = new_node
        print "  Inserted " + value + " as root (node " + new_node + ")"
        return true
    
    # Find insertion point
    int current = tree_root
    
    while true
        if value < tree_values[current]
            if tree_left[current] == -1
                set tree_left[current] = new_node
                print "  Inserted " + value + " as left child of " + tree_values[current]
                return true
            else
                set current = tree_left[current]
        else
            if tree_right[current] == -1
                set tree_right[current] = new_node
                print "  Inserted " + value + " as right child of " + tree_values[current]
                return true
            else
                set current = tree_right[current]
    
    return false
}

fun tree_inorder(node) {
    if node == -1
        return
    
    tree_inorder(tree_left[node])
    print "  " + tree_values[node]
    tree_inorder(tree_right[node])
}

fun tree_search(value) {
    print "Tree Search(" + value + "):"
    
    int current = tree_root
    int depth = 0
    
    while current != -1 and depth < 10
        print "  Checking node at depth " + depth + ": " + tree_values[current]
        
        if tree_values[current] == value
            print "  Found " + value + " at depth " + depth
            return depth
        elif value < tree_values[current]
            set current = tree_left[current]
        else
            set current = tree_right[current]
        
        set depth = depth + 1
    
    print "  " + value + " not found in tree"
    return -1
}

print "Binary Tree operations:"
bool tree_ins1 = tree_insert(50)
bool tree_ins2 = tree_insert(30)
bool tree_ins3 = tree_insert(70)
bool tree_ins4 = tree_insert(20)
bool tree_ins5 = tree_insert(40)

print "Inorder traversal:"
tree_inorder(tree_root)

int tree_search1 = tree_search(40)
int tree_search2 = tree_search(99)
print ""

# Hash Table simulation
print "Hash Table Data Structure:"

array int hash_keys = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
array int hash_values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
int hash_size = 10

fun hash_function(key) {
    return key % hash_size
}

fun hash_insert(key, value) {
    print "Hash Insert(" + key + ", " + value + "):"
    
    int index = hash_function(key)
    print "  Hash function: " + key + " % " + hash_size + " = " + index
    
    # Linear probing for collision resolution
    int original_index = index
    
    while hash_keys[index] != -1
        if hash_keys[index] == key
            print "  Key " + key + " already exists, updating value"
            set hash_values[index] = value
            return true
        
        set index = (index + 1) % hash_size
        
        if index == original_index
            print "  Error: Hash table is full"
            return false
        
        print "  Collision at index " + (index - 1) + ", trying index " + index
    
    set hash_keys[index] = key
    set hash_values[index] = value
    print "  Inserted (" + key + ", " + value + ") at index " + index
    
    return true
}

fun hash_get(key) {
    print "Hash Get(" + key + "):"
    
    int index = hash_function(key)
    int original_index = index
    
    while hash_keys[index] != -1
        if hash_keys[index] == key
            print "  Found key " + key + " at index " + index + " with value " + hash_values[index]
            return hash_values[index]
        
        set index = (index + 1) % hash_size
        
        if index == original_index
            break
    
    print "  Key " + key + " not found"
    return -1
}

print "Hash Table operations:"
bool hash_ins1 = hash_insert(15, 150)
bool hash_ins2 = hash_insert(25, 250)  # Will collide with 15
bool hash_ins3 = hash_insert(35, 350)  # Will collide with 15 and 25
bool hash_ins4 = hash_insert(5, 50)

int hash_get1 = hash_get(25)
int hash_get2 = hash_get(99)
print ""

print "=== End of Data Structures Demo ==="
