; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 126 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [5 x i8] c"Ali\0A\00", align 1
@.str_2 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [18 x i8] c"Test values: a = \00", align 1
@.str_expr_1 = private unnamed_addr constant [7 x i8] c", b = \00", align 1
@.str_expr_2 = private unnamed_addr constant [7 x i8] c", c = \00", align 1
@.str_expr_3 = private unnamed_addr constant [5 x i8] c"\E2\9C\93 \00", align 1
@.str_expr_4 = private unnamed_addr constant [4 x i8] c" > \00", align 1
@.str_expr_5 = private unnamed_addr constant [9 x i8] c" is TRUE\00", align 1
@.str_expr_6 = private unnamed_addr constant [5 x i8] c"\E2\9C\97 \00", align 1
@.str_expr_7 = private unnamed_addr constant [10 x i8] c" is FALSE\00", align 1
@.str_expr_8 = private unnamed_addr constant [5 x i8] c" >= \00", align 1
@.str_expr_9 = private unnamed_addr constant [4 x i8] c" < \00", align 1
@.str_expr_10 = private unnamed_addr constant [5 x i8] c" <= \00", align 1
@.str_expr_11 = private unnamed_addr constant [5 x i8] c" == \00", align 1
@.str_expr_12 = private unnamed_addr constant [5 x i8] c" != \00", align 1
@.str_expr_13 = private unnamed_addr constant [19 x i8] c"Strings: name1 = '\00", align 1
@.str_expr_14 = private unnamed_addr constant [13 x i8] c"', name2 = '\00", align 1
@.str_expr_15 = private unnamed_addr constant [13 x i8] c"', name3 = '\00", align 1
@.str_expr_16 = private unnamed_addr constant [2 x i8] c"'\00", align 1
@.str_expr_17 = private unnamed_addr constant [19 x i8] c"Booleans: flag1 = \00", align 1
@.str_expr_18 = private unnamed_addr constant [11 x i8] c", flag2 = \00", align 1
@.str_expr_19 = private unnamed_addr constant [11 x i8] c", flag3 = \00", align 1
@.str_expr_20 = private unnamed_addr constant [13 x i8] c"Floats: x = \00", align 1
@.str_expr_21 = private unnamed_addr constant [7 x i8] c", y = \00", align 1
@.str_expr_22 = private unnamed_addr constant [7 x i8] c", z = \00", align 1
@.str_expr_23 = private unnamed_addr constant [30 x i8] c"\E2\9C\93 (a + b) > (c * 2) is TRUE\00", align 1
@.str_expr_24 = private unnamed_addr constant [31 x i8] c"\E2\9C\97 (a + b) > (c * 2) is FALSE\00", align 1
@.str_expr_25 = private unnamed_addr constant [31 x i8] c"\E2\9C\93 (a * 2) == (c + a) is TRUE\00", align 1
@.str_expr_26 = private unnamed_addr constant [32 x i8] c"\E2\9C\97 (a * 2) == (c + a) is FALSE\00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4
@global_b = global i32 0, align 4
@global_c = global i32 0, align 4
@global_name1 = global i8* null, align 8
@global_name2 = global i8* null, align 8
@global_name3 = global i8* null, align 8
@global_flag1 = global i1 0, align 1
@global_flag2 = global i1 0, align 1
@global_flag3 = global i1 0, align 1
@global_x = global float 0.0, align 4
@global_y = global float 0.0, align 4
@global_z = global float 0.0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== Comparison Operators Demo ===" (inline)
  %tmp_1_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== Comparison Operators Demo ===\00", [34 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [34 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_3_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [1 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int a = 10
  store i32 10, i32* @global_a, align 4
  ; int b = 5
  store i32 5, i32* @global_b, align 4
  ; int c = 10
  store i32 10, i32* @global_c, align 4
  ; print expression: "Test values: a = " + a + ", b = " + b + ", c = " ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_5 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_5)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_6 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_7 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_8_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_8_str, align 1
  %tmp_9 = bitcast [1 x i8]* %tmp_8_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Greater Than (>) Tests:" (inline)
  %tmp_10_str = alloca [24 x i8], align 1
  store [24 x i8] c"Greater Than (>) Tests:\00", [24 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [24 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a > b
  %tmp_12 = load i32, i32* @global_a, align 4
  %tmp_13 = load i32, i32* @global_b, align 4
  %tmp_14 = icmp sgt i32 %tmp_12, %tmp_13
  br i1 %tmp_14, label %if_true_1, label %if_else_3
if_true_1:
  ; print expression: "✓ " + a + " > " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_15 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_16 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_else_3:
  ; print expression: "✗ " + a + " > " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_17 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_18 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_end_2:
  ; if b > a
  %tmp_19 = load i32, i32* @global_b, align 4
  %tmp_20 = load i32, i32* @global_a, align 4
  %tmp_21 = icmp sgt i32 %tmp_19, %tmp_20
  br i1 %tmp_21, label %if_true_4, label %if_else_6
if_true_4:
  ; print expression: "✓ " + b + " > " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_22 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_23 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_23)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_else_6:
  ; print expression: "✗ " + b + " > " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_24 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_25 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_25)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_end_5:
  ; print "" (inline)
  %tmp_26_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_26_str, align 1
  %tmp_27 = bitcast [1 x i8]* %tmp_26_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_27)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Greater Than or Equal (>=) Tests:" (inline)
  %tmp_28_str = alloca [34 x i8], align 1
  store [34 x i8] c"Greater Than or Equal (>=) Tests:\00", [34 x i8]* %tmp_28_str, align 1
  %tmp_29 = bitcast [34 x i8]* %tmp_28_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_29)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a >= c
  %tmp_30 = load i32, i32* @global_a, align 4
  %tmp_31 = load i32, i32* @global_c, align 4
  %tmp_32 = icmp sge i32 %tmp_30, %tmp_31
  br i1 %tmp_32, label %if_true_7, label %if_else_9
if_true_7:
  ; print expression: "✓ " + a + " >= " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_33 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_33)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_34 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_else_9:
  ; print expression: "✗ " + a + " >= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_35 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_35)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_36 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_36)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_end_8:
  ; if a >= b
  %tmp_37 = load i32, i32* @global_a, align 4
  %tmp_38 = load i32, i32* @global_b, align 4
  %tmp_39 = icmp sge i32 %tmp_37, %tmp_38
  br i1 %tmp_39, label %if_true_10, label %if_else_12
if_true_10:
  ; print expression: "✓ " + a + " >= " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_40 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_41 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_41)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_else_12:
  ; print expression: "✗ " + a + " >= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_42 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_43 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_end_11:
  ; if b >= a
  %tmp_44 = load i32, i32* @global_b, align 4
  %tmp_45 = load i32, i32* @global_a, align 4
  %tmp_46 = icmp sge i32 %tmp_44, %tmp_45
  br i1 %tmp_46, label %if_true_13, label %if_else_15
if_true_13:
  ; print expression: "✓ " + b + " >= " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_47 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_47)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_48 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_48)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_else_15:
  ; print expression: "✗ " + b + " >= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_49 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_49)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_50 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_50)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_end_14:
  ; print "" (inline)
  %tmp_51_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_51_str, align 1
  %tmp_52 = bitcast [1 x i8]* %tmp_51_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_52)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Less Than (<) Tests:" (inline)
  %tmp_53_str = alloca [21 x i8], align 1
  store [21 x i8] c"Less Than (<) Tests:\00", [21 x i8]* %tmp_53_str, align 1
  %tmp_54 = bitcast [21 x i8]* %tmp_53_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_54)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if b < a
  %tmp_55 = load i32, i32* @global_b, align 4
  %tmp_56 = load i32, i32* @global_a, align 4
  %tmp_57 = icmp slt i32 %tmp_55, %tmp_56
  br i1 %tmp_57, label %if_true_16, label %if_else_18
if_true_16:
  ; print expression: "✓ " + b + " < " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_58 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_58)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_59 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_59)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_else_18:
  ; print expression: "✗ " + b + " < " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_60 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_60)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_61 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_61)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_end_17:
  ; if a < b
  %tmp_62 = load i32, i32* @global_a, align 4
  %tmp_63 = load i32, i32* @global_b, align 4
  %tmp_64 = icmp slt i32 %tmp_62, %tmp_63
  br i1 %tmp_64, label %if_true_19, label %if_else_21
if_true_19:
  ; print expression: "✓ " + a + " < " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_65 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_65)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_66 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_66)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_else_21:
  ; print expression: "✗ " + a + " < " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_67 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_67)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_68 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_68)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_end_20:
  ; print "" (inline)
  %tmp_69_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_69_str, align 1
  %tmp_70 = bitcast [1 x i8]* %tmp_69_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_70)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Less Than or Equal (<=) Tests:" (inline)
  %tmp_71_str = alloca [31 x i8], align 1
  store [31 x i8] c"Less Than or Equal (<=) Tests:\00", [31 x i8]* %tmp_71_str, align 1
  %tmp_72 = bitcast [31 x i8]* %tmp_71_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_72)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a <= c
  %tmp_73 = load i32, i32* @global_a, align 4
  %tmp_74 = load i32, i32* @global_c, align 4
  %tmp_75 = icmp sle i32 %tmp_73, %tmp_74
  br i1 %tmp_75, label %if_true_22, label %if_else_24
if_true_22:
  ; print expression: "✓ " + a + " <= " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_76 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_76)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_77 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_77)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_else_24:
  ; print expression: "✗ " + a + " <= " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_78 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_78)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_79 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_79)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_end_23:
  ; if b <= a
  %tmp_80 = load i32, i32* @global_b, align 4
  %tmp_81 = load i32, i32* @global_a, align 4
  %tmp_82 = icmp sle i32 %tmp_80, %tmp_81
  br i1 %tmp_82, label %if_true_25, label %if_else_27
if_true_25:
  ; print expression: "✓ " + b + " <= " + a + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_83 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_83)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_84 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_else_27:
  ; print expression: "✗ " + b + " <= " + a + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_85 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_85)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_86 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_end_26:
  ; if a <= b
  %tmp_87 = load i32, i32* @global_a, align 4
  %tmp_88 = load i32, i32* @global_b, align 4
  %tmp_89 = icmp sle i32 %tmp_87, %tmp_88
  br i1 %tmp_89, label %if_true_28, label %if_else_30
if_true_28:
  ; print expression: "✓ " + a + " <= " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_90 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_91 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_91)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_else_30:
  ; print expression: "✗ " + a + " <= " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_92 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_92)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_93 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_end_29:
  ; print "" (inline)
  %tmp_94_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_94_str, align 1
  %tmp_95 = bitcast [1 x i8]* %tmp_94_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_95)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Equal To (==) Tests:" (inline)
  %tmp_96_str = alloca [21 x i8], align 1
  store [21 x i8] c"Equal To (==) Tests:\00", [21 x i8]* %tmp_96_str, align 1
  %tmp_97 = bitcast [21 x i8]* %tmp_96_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_97)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a == c
  %tmp_98 = load i32, i32* @global_a, align 4
  %tmp_99 = load i32, i32* @global_c, align 4
  %tmp_100 = icmp eq i32 %tmp_98, %tmp_99
  br i1 %tmp_100, label %if_true_31, label %if_else_33
if_true_31:
  ; print expression: "✓ " + a + " == " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_101 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_102 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_102)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_else_33:
  ; print expression: "✗ " + a + " == " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_103 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_103)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_104 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_104)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_end_32:
  ; if a == b
  %tmp_105 = load i32, i32* @global_a, align 4
  %tmp_106 = load i32, i32* @global_b, align 4
  %tmp_107 = icmp eq i32 %tmp_105, %tmp_106
  br i1 %tmp_107, label %if_true_34, label %if_else_36
if_true_34:
  ; print expression: "✓ " + a + " == " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_108 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_108)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_109 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_109)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_else_36:
  ; print expression: "✗ " + a + " == " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_110 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_110)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_111 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_111)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_end_35:
  ; print "" (inline)
  %tmp_112_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_112_str, align 1
  %tmp_113 = bitcast [1 x i8]* %tmp_112_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_113)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Not Equal To (!=) Tests:" (inline)
  %tmp_114_str = alloca [25 x i8], align 1
  store [25 x i8] c"Not Equal To (!=) Tests:\00", [25 x i8]* %tmp_114_str, align 1
  %tmp_115 = bitcast [25 x i8]* %tmp_114_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_115)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if a != b
  %tmp_116 = load i32, i32* @global_a, align 4
  %tmp_117 = load i32, i32* @global_b, align 4
  %tmp_118 = icmp ne i32 %tmp_116, %tmp_117
  br i1 %tmp_118, label %if_true_37, label %if_else_39
if_true_37:
  ; print expression: "✓ " + a + " != " + b + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_119 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_119)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_120 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_120)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_else_39:
  ; print expression: "✗ " + a + " != " + b + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_121 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_121)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_122 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_122)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_end_38:
  ; if a != c
  %tmp_123 = load i32, i32* @global_a, align 4
  %tmp_124 = load i32, i32* @global_c, align 4
  %tmp_125 = icmp ne i32 %tmp_123, %tmp_124
  br i1 %tmp_125, label %if_true_40, label %if_else_42
if_true_40:
  ; print expression: "✓ " + a + " != " + c + " is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_126 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_126)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_127 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_127)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_5, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_else_42:
  ; print expression: "✗ " + a + " != " + c + " is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_128 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_128)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_129 = load i32, i32* @global_c, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_7, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_end_41:
  ; print "" (inline)
  %tmp_130_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_130_str, align 1
  %tmp_131 = bitcast [1 x i8]* %tmp_130_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "String Comparison Tests:" (inline)
  %tmp_132_str = alloca [25 x i8], align 1
  store [25 x i8] c"String Comparison Tests:\00", [25 x i8]* %tmp_132_str, align 1
  %tmp_133 = bitcast [25 x i8]* %tmp_132_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_133)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string name1 = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_name1, align 8
  ; string name2 = "Ali"
  store i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_1, i64 0, i64 0), i8** @global_name2, align 8
  ; string name3 = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_name3, align 8
  ; print expression: "Strings: name1 = '" + name1 + "', name2 = '" + na...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_134 = load i8*, i8** @global_name1, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_134)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_135 = load i8*, i8** @global_name2, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_135)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_136 = load i8*, i8** @global_name3, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_136)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_16, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if name1 == name3
  %tmp_137 = icmp eq i32 0, 0
  br i1 %tmp_137, label %if_true_43, label %if_else_45
if_true_43:
  ; print "✓ name1 == name3 is TRUE" (inline)
  %tmp_138_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 name1 == name3 is TRUE\00", [27 x i8]* %tmp_138_str, align 1
  %tmp_139 = bitcast [27 x i8]* %tmp_138_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_139)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_else_45:
  ; print "✗ name1 == name3 is FALSE" (inline)
  %tmp_140_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 == name3 is FALSE\00", [28 x i8]* %tmp_140_str, align 1
  %tmp_141 = bitcast [28 x i8]* %tmp_140_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_141)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_end_44:
  ; if name1 != name2
  %tmp_142 = icmp ne i32 0, 0
  br i1 %tmp_142, label %if_true_46, label %if_else_48
if_true_46:
  ; print "✓ name1 != name2 is TRUE" (inline)
  %tmp_143_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 name1 != name2 is TRUE\00", [27 x i8]* %tmp_143_str, align 1
  %tmp_144 = bitcast [27 x i8]* %tmp_143_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_144)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_else_48:
  ; print "✗ name1 != name2 is FALSE" (inline)
  %tmp_145_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 name1 != name2 is FALSE\00", [28 x i8]* %tmp_145_str, align 1
  %tmp_146 = bitcast [28 x i8]* %tmp_145_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_end_47:
  ; print "" (inline)
  %tmp_147_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_147_str, align 1
  %tmp_148 = bitcast [1 x i8]* %tmp_147_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_148)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Boolean Comparison Tests:" (inline)
  %tmp_149_str = alloca [26 x i8], align 1
  store [26 x i8] c"Boolean Comparison Tests:\00", [26 x i8]* %tmp_149_str, align 1
  %tmp_150 = bitcast [26 x i8]* %tmp_149_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_150)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; bool flag1 = true
  store i1 1, i1* @global_flag1, align 1
  ; bool flag2 = false
  store i1 0, i1* @global_flag2, align 1
  ; bool flag3 = true
  store i1 1, i1* @global_flag3, align 1
  ; print expression: "Booleans: flag1 = " + flag1 + ", flag2 = " + flag...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_151 = load i1, i1* @global_flag1, align 1
  br i1 %tmp_151, label %tmp_152, label %tmp_153
tmp_152:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_154
tmp_153:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_154
tmp_154:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_155 = load i1, i1* @global_flag2, align 1
  br i1 %tmp_155, label %tmp_156, label %tmp_157
tmp_156:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_158
tmp_157:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_158
tmp_158:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_159 = load i1, i1* @global_flag3, align 1
  br i1 %tmp_159, label %tmp_160, label %tmp_161
tmp_160:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_162
tmp_161:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_162
tmp_162:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if flag1 == flag3
  %tmp_163 = load i1, i1* @global_flag1, align 1
  %tmp_164 = zext i1 %tmp_163 to i32
  %tmp_165 = load i1, i1* @global_flag3, align 1
  %tmp_166 = zext i1 %tmp_165 to i32
  %tmp_167 = icmp eq i32 %tmp_164, %tmp_166
  br i1 %tmp_167, label %if_true_49, label %if_else_51
if_true_49:
  ; print "✓ flag1 == flag3 is TRUE" (inline)
  %tmp_168_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 flag1 == flag3 is TRUE\00", [27 x i8]* %tmp_168_str, align 1
  %tmp_169 = bitcast [27 x i8]* %tmp_168_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_169)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_else_51:
  ; print "✗ flag1 == flag3 is FALSE" (inline)
  %tmp_170_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 == flag3 is FALSE\00", [28 x i8]* %tmp_170_str, align 1
  %tmp_171 = bitcast [28 x i8]* %tmp_170_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_171)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_end_50:
  ; if flag1 != flag2
  %tmp_172 = load i1, i1* @global_flag1, align 1
  %tmp_173 = zext i1 %tmp_172 to i32
  %tmp_174 = load i1, i1* @global_flag2, align 1
  %tmp_175 = zext i1 %tmp_174 to i32
  %tmp_176 = icmp ne i32 %tmp_173, %tmp_175
  br i1 %tmp_176, label %if_true_52, label %if_else_54
if_true_52:
  ; print "✓ flag1 != flag2 is TRUE" (inline)
  %tmp_177_str = alloca [27 x i8], align 1
  store [27 x i8] c"\E2\9C\93 flag1 != flag2 is TRUE\00", [27 x i8]* %tmp_177_str, align 1
  %tmp_178 = bitcast [27 x i8]* %tmp_177_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_178)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_else_54:
  ; print "✗ flag1 != flag2 is FALSE" (inline)
  %tmp_179_str = alloca [28 x i8], align 1
  store [28 x i8] c"\E2\9C\97 flag1 != flag2 is FALSE\00", [28 x i8]* %tmp_179_str, align 1
  %tmp_180 = bitcast [28 x i8]* %tmp_179_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_180)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_end_53:
  ; print "" (inline)
  %tmp_181_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_181_str, align 1
  %tmp_182 = bitcast [1 x i8]* %tmp_181_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_182)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Float Comparison Tests:" (inline)
  %tmp_183_str = alloca [24 x i8], align 1
  store [24 x i8] c"Float Comparison Tests:\00", [24 x i8]* %tmp_183_str, align 1
  %tmp_184 = bitcast [24 x i8]* %tmp_183_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_184)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float x = 5.5
  %tmp_185 = bitcast i32 1085276160 to float
  store float %tmp_185, float* @global_x, align 4
  ; float y = 3.2
  %tmp_186 = bitcast i32 1078774989 to float
  store float %tmp_186, float* @global_y, align 4
  ; float z = 5.5
  %tmp_187 = bitcast i32 1085276160 to float
  store float %tmp_187, float* @global_z, align 4
  ; print expression: "Floats: x = " + x + ", y = " + y + ", z = " + z
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_188 = load float, float* @global_x, align 4
  %tmp_189 = fpext float %tmp_188 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_189)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_190 = load float, float* @global_y, align 4
  %tmp_191 = fpext float %tmp_190 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_191)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_192 = load float, float* @global_z, align 4
  %tmp_193 = fpext float %tmp_192 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_193)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if x > y
  %tmp_194 = icmp sgt i32 0, 0
  br i1 %tmp_194, label %if_true_55, label %if_end_56
if_true_55:
  ; print "✓ x > y is TRUE" (inline)
  %tmp_195_str = alloca [18 x i8], align 1
  store [18 x i8] c"\E2\9C\93 x > y is TRUE\00", [18 x i8]* %tmp_195_str, align 1
  %tmp_196 = bitcast [18 x i8]* %tmp_195_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_196)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_end_56:
  ; if x == z
  %tmp_197 = icmp eq i32 0, 0
  br i1 %tmp_197, label %if_true_57, label %if_end_58
if_true_57:
  ; print "✓ x == z is TRUE" (inline)
  %tmp_198_str = alloca [19 x i8], align 1
  store [19 x i8] c"\E2\9C\93 x == z is TRUE\00", [19 x i8]* %tmp_198_str, align 1
  %tmp_199 = bitcast [19 x i8]* %tmp_198_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_199)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_58
if_end_58:
  ; if y < x
  %tmp_200 = icmp slt i32 0, 0
  br i1 %tmp_200, label %if_true_59, label %if_end_60
if_true_59:
  ; print "✓ y < x is TRUE" (inline)
  %tmp_201_str = alloca [18 x i8], align 1
  store [18 x i8] c"\E2\9C\93 y < x is TRUE\00", [18 x i8]* %tmp_201_str, align 1
  %tmp_202 = bitcast [18 x i8]* %tmp_201_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_202)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_60
if_end_60:
  ; print "" (inline)
  %tmp_203_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_203_str, align 1
  %tmp_204 = bitcast [1 x i8]* %tmp_203_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_204)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Complex Expression Comparisons:" (inline)
  %tmp_205_str = alloca [32 x i8], align 1
  store [32 x i8] c"Complex Expression Comparisons:\00", [32 x i8]* %tmp_205_str, align 1
  %tmp_206 = bitcast [32 x i8]* %tmp_205_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_206)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (a + b) > (c * 2)
  %tmp_207 = icmp sgt i32 0, 0
  br i1 %tmp_207, label %if_true_61, label %if_else_63
if_true_61:
  ; print expression: "✓ (a + b) > (c * 2) is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([28 x i8], [28 x i8]* @.str_expr_23, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_else_63:
  ; print expression: "✗ (a + b) > (c * 2) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_24, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_end_62:
  ; if (a * 2) == (c + a)
  %tmp_208 = icmp eq i32 0, 0
  br i1 %tmp_208, label %if_true_64, label %if_else_66
if_true_64:
  ; print expression: "✓ (a * 2) == (c + a) is TRUE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([29 x i8], [29 x i8]* @.str_expr_25, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_else_66:
  ; print expression: "✗ (a * 2) == (c + a) is FALSE"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([30 x i8], [30 x i8]* @.str_expr_26, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_end_65:
  ; print "" (inline)
  %tmp_209_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_209_str, align 1
  %tmp_210 = bitcast [1 x i8]* %tmp_209_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_210)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== End of Comparison Operators Demo ===" (inline)
  %tmp_211_str = alloca [41 x i8], align 1
  store [41 x i8] c"=== End of Comparison Operators Demo ===\00", [41 x i8]* %tmp_211_str, align 1
  %tmp_212 = bitcast [41 x i8]* %tmp_211_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_212)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
