// 21. Mathematical Functions - trigonometry, logarithms, advanced math (Rust version)
// This file demonstrates mathematical functions in Rust for comparison with Dolet

use std::f64::consts::{PI, E};

fn main() {
    println!("=== Mathematical Functions Demo (Rust) ===");
    println!();

    // Basic mathematical constants
    println!("Mathematical constants:");
    println!("π (PI) = {:.10}", PI);
    println!("e (<PERSON><PERSON><PERSON>'s number) = {:.10}", E);
    println!("√2 = {:.10}", std::f64::consts::SQRT_2);
    println!("ln(2) = {:.10}", std::f64::consts::LN_2);
    println!("ln(10) = {:.10}", std::f64::consts::LN_10);
    println!("log₂(e) = {:.10}", std::f64::consts::LOG2_E);
    println!("log₁₀(e) = {:.10}", std::f64::consts::LOG10_E);
    println!();

    // Basic arithmetic functions
    println!("Basic arithmetic functions:");
    let a = 16.0_f64;
    let b = 4.0_f64;
    let c = -7.5_f64;
    
    println!("Numbers: a = {}, b = {}, c = {}", a, b, c);
    println!("abs(c) = {}", c.abs());
    println!("sqrt(a) = {:.6}", a.sqrt());
    println!("cbrt(a) = {:.6}", a.cbrt());
    println!("pow(b, 3) = {}", b.powi(3));
    println!("pow(a, 0.5) = {:.6}", a.powf(0.5));
    println!("exp(1) = {:.6}", 1.0_f64.exp());
    println!("ln(a) = {:.6}", a.ln());
    println!("log₂(a) = {:.6}", a.log2());
    println!("log₁₀(a) = {:.6}", a.log10());
    println!();

    // Trigonometric functions (angles in radians)
    println!("Trigonometric functions (radians):");
    let angles_rad = [0.0, PI/6.0, PI/4.0, PI/3.0, PI/2.0, PI];
    let angle_names = ["0", "π/6", "π/4", "π/3", "π/2", "π"];
    
    for (i, &angle) in angles_rad.iter().enumerate() {
        println!("Angle: {} ({:.6} rad)", angle_names[i], angle);
        println!("  sin({}) = {:.6}", angle_names[i], angle.sin());
        println!("  cos({}) = {:.6}", angle_names[i], angle.cos());
        println!("  tan({}) = {:.6}", angle_names[i], angle.tan());
    }
    println!();

    // Trigonometric functions (angles in degrees)
    println!("Trigonometric functions (degrees):");
    let angles_deg = [0.0_f64, 30.0, 45.0, 60.0, 90.0, 180.0];
    
    for &angle_deg in &angles_deg {
        let angle_rad = angle_deg.to_radians();
        println!("Angle: {}° ({:.6} rad)", angle_deg, angle_rad);
        println!("  sin({}°) = {:.6}", angle_deg, angle_rad.sin());
        println!("  cos({}°) = {:.6}", angle_deg, angle_rad.cos());
        if angle_deg != 90.0 && angle_deg != 270.0 {
            println!("  tan({}°) = {:.6}", angle_deg, angle_rad.tan());
        } else {
            println!("  tan({}°) = undefined", angle_deg);
        }
    }
    println!();

    // Inverse trigonometric functions
    println!("Inverse trigonometric functions:");
    let values = [0.0_f64, 0.5, 0.707, 0.866, 1.0];
    
    for &val in &values {
        if val >= -1.0 && val <= 1.0 {
            println!("Value: {}", val);
            println!("  arcsin({}) = {:.6} rad = {:.2}°", val, val.asin(), val.asin().to_degrees());
            println!("  arccos({}) = {:.6} rad = {:.2}°", val, val.acos(), val.acos().to_degrees());
            println!("  arctan({}) = {:.6} rad = {:.2}°", val, val.atan(), val.atan().to_degrees());
        }
    }
    println!();

    // Hyperbolic functions
    println!("Hyperbolic functions:");
    let hyp_values = [0.0_f64, 0.5, 1.0, 1.5, 2.0];
    
    for &val in &hyp_values {
        println!("Value: {}", val);
        println!("  sinh({}) = {:.6}", val, val.sinh());
        println!("  cosh({}) = {:.6}", val, val.cosh());
        println!("  tanh({}) = {:.6}", val, val.tanh());
    }
    println!();

    // Rounding and ceiling/floor functions
    println!("Rounding and ceiling/floor functions:");
    let decimal_values = [3.2_f64, 3.7, -2.3, -2.8, 5.5, -5.5];
    
    for &val in &decimal_values {
        println!("Value: {}", val);
        println!("  floor({}) = {}", val, val.floor());
        println!("  ceil({}) = {}", val, val.ceil());
        println!("  round({}) = {}", val, val.round());
        println!("  trunc({}) = {}", val, val.trunc());
        println!("  fract({}) = {:.6}", val, val.fract());
    }
    println!();

    // Statistical functions
    println!("Statistical functions:");
    let data = [2.5, 4.1, 3.8, 5.2, 1.9, 6.3, 4.7, 3.1, 5.8, 2.4];
    
    println!("Data: {:?}", data);
    let sum: f64 = data.iter().sum();
    let mean = sum / data.len() as f64;
    let min_val = data.iter().fold(f64::INFINITY, |a, &b| a.min(b));
    let max_val = data.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
    
    // Calculate variance and standard deviation
    let variance = data.iter()
        .map(|&x| (x - mean).powi(2))
        .sum::<f64>() / data.len() as f64;
    let std_dev = variance.sqrt();
    
    println!("Sum: {:.2}", sum);
    println!("Mean: {:.2}", mean);
    println!("Min: {:.2}", min_val);
    println!("Max: {:.2}", max_val);
    println!("Range: {:.2}", max_val - min_val);
    println!("Variance: {:.2}", variance);
    println!("Standard deviation: {:.2}", std_dev);
    println!();

    // Geometric calculations
    println!("Geometric calculations:");

    // Circle calculations
    let radius = 5.0_f64;
    let circle_area = PI * radius * radius;
    let circle_circumference = 2.0 * PI * radius;

    println!("Circle (radius = {}):", radius);
    println!("  Area = π × r² = {:.2}", circle_area);
    println!("  Circumference = 2π × r = {:.2}", circle_circumference);

    // Rectangle calculations
    let length = 8.0_f64;
    let width = 6.0_f64;
    let rect_area = length * width;
    let rect_perimeter = 2.0 * (length + width);
    let rect_diagonal = (length * length + width * width).sqrt();
    
    println!("Rectangle ({}×{}):", length, width);
    println!("  Area = {:.2}", rect_area);
    println!("  Perimeter = {:.2}", rect_perimeter);
    println!("  Diagonal = {:.2}", rect_diagonal);
    
    // Triangle calculations (using Heron's formula)
    let side_a = 3.0_f64;
    let side_b = 4.0_f64;
    let side_c = 5.0_f64;
    let semi_perimeter = (side_a + side_b + side_c) / 2.0;
    let triangle_area = (semi_perimeter * (semi_perimeter - side_a) * 
                        (semi_perimeter - side_b) * (semi_perimeter - side_c)).sqrt();
    
    println!("Triangle (sides: {}, {}, {}):", side_a, side_b, side_c);
    println!("  Area (Heron's formula) = {:.2}", triangle_area);
    println!();

    // Distance and coordinate calculations
    println!("Distance and coordinate calculations:");
    let point1 = (1.0_f64, 2.0_f64);
    let point2 = (4.0_f64, 6.0_f64);
    let distance = ((point2.0 - point1.0).powi(2) + (point2.1 - point1.1).powi(2)).sqrt();
    let midpoint = ((point1.0 + point2.0) / 2.0, (point1.1 + point2.1) / 2.0);
    
    println!("Point 1: ({}, {})", point1.0, point1.1);
    println!("Point 2: ({}, {})", point2.0, point2.1);
    println!("Distance: {:.2}", distance);
    println!("Midpoint: ({:.1}, {:.1})", midpoint.0, midpoint.1);
    println!();

    // Factorial and combinatorics
    println!("Factorial and combinatorics:");
    for n in 0..=10 {
        let fact = factorial(n);
        println!("{}! = {}", n, fact);
    }
    
    println!("Combinations and permutations:");
    let n = 10;
    let r = 3;
    let combinations = factorial(n) / (factorial(r) * factorial(n - r));
    let permutations = factorial(n) / factorial(n - r);
    
    println!("C({}, {}) = {}", n, r, combinations);
    println!("P({}, {}) = {}", n, r, permutations);
    println!();

    // Number theory functions
    println!("Number theory functions:");
    let num1 = 48;
    let num2 = 18;
    let gcd_result = gcd(num1, num2);
    let lcm_result = (num1 * num2) / gcd_result;
    
    println!("GCD({}, {}) = {}", num1, num2, gcd_result);
    println!("LCM({}, {}) = {}", num1, num2, lcm_result);
    
    // Prime checking
    let test_numbers = [2, 3, 4, 17, 25, 29, 100, 101];
    println!("Prime number checking:");
    for &num in &test_numbers {
        println!("{} is prime: {}", num, is_prime(num));
    }
    println!();

    // Fibonacci sequence
    println!("Fibonacci sequence:");
    print!("First 15 Fibonacci numbers: ");
    for i in 0..15 {
        print!("{} ", fibonacci(i));
    }
    println!();
    println!();

    // Complex number simulation (using tuples)
    println!("Complex number operations:");
    let complex1 = (3.0_f64, 4.0_f64); // 3 + 4i
    let complex2 = (1.0_f64, 2.0_f64); // 1 + 2i
    
    let sum = (complex1.0 + complex2.0, complex1.1 + complex2.1);
    let product = (complex1.0 * complex2.0 - complex1.1 * complex2.1,
                   complex1.0 * complex2.1 + complex1.1 * complex2.0);
    let magnitude1 = (complex1.0 * complex1.0 + complex1.1 * complex1.1).sqrt();
    
    println!("Complex 1: {} + {}i", complex1.0, complex1.1);
    println!("Complex 2: {} + {}i", complex2.0, complex2.1);
    println!("Sum: {} + {}i", sum.0, sum.1);
    println!("Product: {:.2} + {:.2}i", product.0, product.1);
    println!("Magnitude of Complex 1: {:.2}", magnitude1);
    println!();

    // Polynomial evaluation
    println!("Polynomial evaluation:");
    let coefficients = [1.0, -3.0, 2.0]; // x² - 3x + 2
    let x_values = [0.0, 1.0, 2.0, 3.0, 4.0];
    
    println!("Polynomial: x² - 3x + 2");
    for &x in &x_values {
        let result = evaluate_polynomial(&coefficients, x);
        println!("f({}) = {:.2}", x, result);
    }
    println!();

    println!("=== End of Mathematical Functions Demo ===");
}

// Helper functions

fn factorial(n: u64) -> u64 {
    if n <= 1 {
        1
    } else {
        (1..=n).product()
    }
}

fn gcd(mut a: u64, mut b: u64) -> u64 {
    while b != 0 {
        let temp = b;
        b = a % b;
        a = temp;
    }
    a
}

fn is_prime(n: u64) -> bool {
    if n < 2 {
        return false;
    }
    if n == 2 {
        return true;
    }
    if n % 2 == 0 {
        return false;
    }
    
    let sqrt_n = (n as f64).sqrt() as u64;
    for i in (3..=sqrt_n).step_by(2) {
        if n % i == 0 {
            return false;
        }
    }
    true
}

fn fibonacci(n: u32) -> u64 {
    match n {
        0 => 0,
        1 => 1,
        _ => {
            let mut a = 0;
            let mut b = 1;
            for _ in 2..=n {
                let temp = a + b;
                a = b;
                b = temp;
            }
            b
        }
    }
}

fn evaluate_polynomial(coefficients: &[f64], x: f64) -> f64 {
    coefficients.iter()
        .enumerate()
        .map(|(i, &coeff)| coeff * x.powi(coefficients.len() as i32 - 1 - i as i32))
        .sum()
}
