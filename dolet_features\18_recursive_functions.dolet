# 18. Recursive Functions - Functions That Call Themselves
# This file demonstrates recursive functions in Dolet

print "=== Recursive Functions Demo ==="
print ""

# Basic recursion - Factorial
print "Factorial Recursion:"

fun factorial(n) {
    print "factorial(" + n + ") called"
    
    if n <= 1
        print "  Base case reached: returning 1"
        return 1
    else
        print "  Recursive case: " + n + " * factorial(" + (n - 1) + ")"
        int result = n * factorial(n - 1)
        print "  factorial(" + n + ") = " + result
        return result
}

print "Calculating 5!:"
int fact5 = factorial(5)
print "Final result: 5! = " + fact5
print ""

print "Calculating 4!:"
int fact4 = factorial(4)
print "Final result: 4! = " + fact4
print ""

# Fibonacci sequence
print "Fibonacci Recursion:"

fun fibonacci(n) {
    print "fibonacci(" + n + ") called"
    
    if n <= 1
        print "  Base case: fibonacci(" + n + ") = " + n
        return n
    else
        print "  Recursive case: fibonacci(" + (n-1) + ") + fibonacci(" + (n-2) + ")"
        int fib1 = fibonacci(n - 1)
        int fib2 = fibonacci(n - 2)
        int result = fib1 + fib2
        print "  fibonacci(" + n + ") = " + fib1 + " + " + fib2 + " = " + result
        return result
}

print "Calculating fibonacci(5):"
int fib5 = fibonacci(5)
print "Final result: fibonacci(5) = " + fib5
print ""

# Power function using recursion
print "Power Function Recursion:"

fun power(base, exponent) {
    print "power(" + base + ", " + exponent + ") called"
    
    if exponent == 0
        print "  Base case: any number to power 0 = 1"
        return 1
    elif exponent == 1
        print "  Base case: " + base + " to power 1 = " + base
        return base
    else
        print "  Recursive case: " + base + " * power(" + base + ", " + (exponent - 1) + ")"
        int result = base * power(base, exponent - 1)
        print "  power(" + base + ", " + exponent + ") = " + result
        return result
}

print "Calculating 2^4:"
int power_result = power(2, 4)
print "Final result: 2^4 = " + power_result
print ""

# Sum of digits
print "Sum of Digits Recursion:"

fun sum_of_digits(number) {
    print "sum_of_digits(" + number + ") called"
    
    if number < 10
        print "  Base case: single digit " + number
        return number
    else
        int last_digit = number % 10
        int remaining = number / 10
        print "  Recursive case: " + last_digit + " + sum_of_digits(" + remaining + ")"
        int result = last_digit + sum_of_digits(remaining)
        print "  sum_of_digits(" + number + ") = " + result
        return result
}

print "Calculating sum of digits of 1234:"
int digit_sum = sum_of_digits(1234)
print "Final result: sum of digits of 1234 = " + digit_sum
print ""

# Greatest Common Divisor (GCD)
print "GCD Recursion (Euclidean Algorithm):"

fun gcd(a, b) {
    print "gcd(" + a + ", " + b + ") called"
    
    if b == 0
        print "  Base case: gcd(" + a + ", 0) = " + a
        return a
    else
        int remainder = a % b
        print "  Recursive case: gcd(" + b + ", " + remainder + ")"
        int result = gcd(b, remainder)
        print "  gcd(" + a + ", " + b + ") = " + result
        return result
}

print "Calculating GCD of 48 and 18:"
int gcd_result = gcd(48, 18)
print "Final result: GCD(48, 18) = " + gcd_result
print ""

# Binary search (recursive)
print "Binary Search Recursion:"

array int sorted_array = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]

fun binary_search(arr, target, left, right) {
    print "binary_search(target=" + target + ", left=" + left + ", right=" + right + ")"
    
    if left > right
        print "  Base case: target not found"
        return -1
    
    int mid = (left + right) / 2
    print "  Checking middle index " + mid + " (value = " + arr[mid] + ")"
    
    if arr[mid] == target
        print "  Base case: target found at index " + mid
        return mid
    elif arr[mid] > target
        print "  Target is smaller, searching left half"
        return binary_search(arr, target, left, mid - 1)
    else
        print "  Target is larger, searching right half"
        return binary_search(arr, target, mid + 1, right)
}

print "Array: [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]"
print "Searching for 7:"
int search_result = binary_search(sorted_array, 7, 0, 9)
if search_result != -1
    print "Found 7 at index " + search_result
else
    print "7 not found in array"
print ""

# Tower of Hanoi
print "Tower of Hanoi Recursion:"

fun hanoi(n, source, destination, auxiliary) {
    if n == 1
        print "Move disk 1 from " + source + " to " + destination
    else
        print "hanoi(" + n + "): Moving " + (n-1) + " disks from " + source + " to " + auxiliary
        hanoi(n - 1, source, auxiliary, destination)
        
        print "Move disk " + n + " from " + source + " to " + destination
        
        print "hanoi(" + n + "): Moving " + (n-1) + " disks from " + auxiliary + " to " + destination
        hanoi(n - 1, auxiliary, destination, source)
}

print "Solving Tower of Hanoi with 3 disks:"
hanoi(3, "A", "C", "B")
print ""

# Palindrome check
print "Palindrome Check Recursion:"

fun is_palindrome_recursive(text, start, end) {
    print "is_palindrome_recursive('" + text + "', " + start + ", " + end + ")"
    
    if start >= end
        print "  Base case: single character or empty, returning true"
        return true
    
    # Simulated character comparison (in real implementation, would compare text[start] with text[end])
    bool chars_equal = true  # Simplified for demo
    print "  Comparing characters at positions " + start + " and " + end
    
    if chars_equal
        print "  Characters match, checking inner substring"
        return is_palindrome_recursive(text, start + 1, end - 1)
    else
        print "  Characters don't match, returning false"
        return false
}

string test_word = "radar"
print "Checking if '" + test_word + "' is a palindrome:"
bool is_palindrome = is_palindrome_recursive(test_word, 0, 4)
print "Result: '" + test_word + "' is palindrome: " + is_palindrome
print ""

# Recursive array sum
print "Recursive Array Sum:"

fun array_sum_recursive(arr, index, size) {
    print "array_sum_recursive(index=" + index + ", size=" + size + ")"
    
    if index >= size
        print "  Base case: reached end of array, returning 0"
        return 0
    else
        print "  Adding arr[" + index + "] = " + arr[index] + " to sum of rest"
        int rest_sum = array_sum_recursive(arr, index + 1, size)
        int total = arr[index] + rest_sum
        print "  Sum from index " + index + " = " + total
        return total
}

array int numbers = [5, 3, 8, 2, 7]
print "Array: [5, 3, 8, 2, 7]"
print "Calculating recursive sum:"
int recursive_sum = array_sum_recursive(numbers, 0, 5)
print "Final result: recursive sum = " + recursive_sum
print ""

print "=== End of Recursive Functions Demo ==="
