# 06. Loops - Iteration and Repetition
# This file demonstrates all loop types in Dolet

print "=== Loops Demo ==="
print ""

# Simple for loop
print "Simple for Loop:"
for i = 1 to 5
    print "Iteration " + i
print ""

# For loop with step
print "For Loop with Step:"
for i = 0 to 10 step 2
    print "Even number: " + i
print ""

# For loop counting down
print "For Loop Counting Down:"
for i = 5 to 1 step -1
    print "Countdown: " + i
print "Blast off!"
print ""

# While loop
print "While Loop:"
int counter = 1
while counter <= 5
    print "Counter: " + counter
    set counter = counter + 1
print ""

# While loop with condition
print "While Loop with Condition:"
int number = 1
while number < 100
    print "Number: " + number
    set number = number * 2
print ""

# Nested for loops
print "Nested for Loops:"
for i = 1 to 3
    for j = 1 to 3
        print "i=" + i + ", j=" + j
print ""

# Loop with array-like behavior
print "Loop with Array-like Behavior:"
for index = 0 to 4
    if index == 0
        print "First item"
    elif index == 1
        print "Second item"
    elif index == 2
        print "Third item"
    elif index == 3
        print "Fourth item"
    else
        print "Last item"
print ""

# Loop with break simulation
print "Loop with Break Simulation:"
for i = 1 to 10
    if i == 5
        print "Breaking at " + i
        # break simulation - would exit loop
    else
        print "Processing " + i
print ""

# Loop with continue simulation
print "Loop with Continue Simulation:"
for i = 1 to 10
    if i % 2 == 0
        # continue simulation - would skip to next iteration
        print "Skipping even number " + i
    else
        print "Processing odd number " + i
print ""

# While loop with complex condition
print "While Loop with Complex Condition:"
int x = 1
int y = 10
while x < y
    print "x=" + x + ", y=" + y
    set x = x + 1
    set y = y - 1
print "x and y met at the middle"
print ""

# Loop for calculations
print "Loop for Calculations:"
int sum = 0
for i = 1 to 10
    set sum = sum + i
    print "Adding " + i + ", sum = " + sum
print "Final sum: " + sum
print ""

# Loop for factorial calculation
print "Loop for Factorial Calculation:"
int factorial = 1
int n = 5
for i = 1 to n
    set factorial = factorial * i
    print i + "! = " + factorial
print n + " factorial = " + factorial
print ""

# Loop with string operations
print "Loop with String Operations:"
string message = ""
for i = 1 to 5
    set message = message + "Hello "
print "Final message: " + message
print ""

# Multiplication table using loops
print "Multiplication Table:"
int table_num = 3
for i = 1 to 10
    int result = table_num * i
    print table_num + " x " + i + " = " + result
print ""

# Pattern printing with loops
print "Pattern Printing:"
for i = 1 to 5
    string pattern = ""
    for j = 1 to i
        set pattern = pattern + "*"
    print pattern
print ""

# Loop with conditional logic
print "Loop with Conditional Logic:"
for i = 1 to 20
    if i % 3 == 0 and i % 5 == 0
        print i + ": FizzBuzz"
    elif i % 3 == 0
        print i + ": Fizz"
    elif i % 5 == 0
        print i + ": Buzz"
    else
        print i + ": " + i
print ""

# Infinite loop simulation (with safety)
print "Infinite Loop Simulation:"
int safety_counter = 0
bool condition = true
while condition == true
    print "Loop iteration " + safety_counter
    set safety_counter = safety_counter + 1
    if safety_counter >= 5
        set condition = false
        print "Safety exit activated"
print ""

print "=== End of Loops Demo ==="
