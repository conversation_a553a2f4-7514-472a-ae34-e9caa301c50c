; ModuleID = '04_conditional_statements.55ec4d296be4a272-cgu.0'
source_filename = "04_conditional_statements.55ec4d296be4a272-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h7a5a6344d85efc0eE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h03d93596123dbd2cE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h03d93596123dbd2cE" }>, align 8
@anon.d12f44bc24e478e1874583811c9a5f62.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_e4aff5178404219470dbeca992759aa4 = private unnamed_addr constant [43 x i8] c"=== Conditional Statements Demo (Rust) ===\0A", align 1
@alloc_f86105c76f9380b78b07402ee8cc424d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e4aff5178404219470dbeca992759aa4, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b8fbc72d0698cac26d38e20d2038b619 = private unnamed_addr constant [34 x i8] c"Simple if statement (score = 85):\0A", align 1
@alloc_8daf906fefea4c98a8be166ee9433daf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b8fbc72d0698cac26d38e20d2038b619, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_74079ffa2369ff2ed8e95dfe3365557a = private unnamed_addr constant [16 x i8] c"\E2\9C\93 Good score!\0A", align 1
@alloc_525c2040f6f1502c10c7b83620ac4bf0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_74079ffa2369ff2ed8e95dfe3365557a, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_970bbd5c7cf6e8346af021582369aa6a = private unnamed_addr constant [30 x i8] c"if-else statement (age = 17):\0A", align 1
@alloc_d27c69d8abf62b21d8b2cdeb0568cf3f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_970bbd5c7cf6e8346af021582369aa6a, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_1249d12d2856112ab2962b9b214699a3 = private unnamed_addr constant [16 x i8] c"You are a minor\0A", align 1
@alloc_fd950fe31639541b5a6f68a08ecd88bd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1249d12d2856112ab2962b9b214699a3, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_0a6d4a8dd1fb371a2986926ec868cebb = private unnamed_addr constant [17 x i8] c"You are an adult\0A", align 1
@alloc_99e44f0c502056206671b19f10bf41a1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0a6d4a8dd1fb371a2986926ec868cebb, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_6dcbee6924ce7e68af8b4528249ea675 = private unnamed_addr constant [36 x i8] c"if-else if-else chain (grade = 92):\0A", align 1
@alloc_6655e66435ff12887a10966b87d418ce = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6dcbee6924ce7e68af8b4528249ea675, [8 x i8] c"$\00\00\00\00\00\00\00" }>, align 8
@alloc_54ab464787ae0caca5353d93c0b34796 = private unnamed_addr constant [18 x i8] c"Grade: F (Failed)\0A", align 1
@alloc_86c76d745292d3636de6d7979ede66a6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_54ab464787ae0caca5353d93c0b34796, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_aba04d8701b729810f3a4fc70b6d5907 = private unnamed_addr constant [25 x i8] c"Grade: D (Below Average)\0A", align 1
@alloc_07322d9824ec4d35e74eb9033c8d2e87 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_aba04d8701b729810f3a4fc70b6d5907, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_2081804bad2c2286dd0a6e6b4df50e80 = private unnamed_addr constant [19 x i8] c"Grade: C (Average)\0A", align 1
@alloc_3e1dc8319c2e9f7badc1123d1aa42be1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2081804bad2c2286dd0a6e6b4df50e80, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_3a1a2f1f55be505d0ef751d323293db4 = private unnamed_addr constant [17 x i8] c"Grade: B (Good!)\0A", align 1
@alloc_9ca807bc7b0c325c3b66af24529e8b64 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3a1a2f1f55be505d0ef751d323293db4, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_8df4afbcd639246c8bc5d0a81202d461 = private unnamed_addr constant [22 x i8] c"Grade: A (Excellent!)\0A", align 1
@alloc_713f5727e4262be8416e528b55da1cd7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8df4afbcd639246c8bc5d0a81202d461, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_7b49028e7a78065717ac0b8337a52060 = private unnamed_addr constant [51 x i8] c"Multiple else if statements (temperature = 25\C2\B0C):\0A", align 1
@alloc_2b167f7ac482b41d83cfb4f697db1125 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7b49028e7a78065717ac0b8337a52060, [8 x i8] c"3\00\00\00\00\00\00\00" }>, align 8
@alloc_322ef2f6cde7d0f6187a2db3e2181370 = private unnamed_addr constant [15 x i8] c"It's freezing!\0A", align 1
@alloc_f79d9a13b32afdff846b49bbba80e637 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_322ef2f6cde7d0f6187a2db3e2181370, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_37f482d356122c94764a4befd13c5f1d = private unnamed_addr constant [10 x i8] c"It's cold\0A", align 1
@alloc_0c7e29fef24828dd20d35f4af0e523b1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_37f482d356122c94764a4befd13c5f1d, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_05617cdedeaddcda4193f197d016d677 = private unnamed_addr constant [10 x i8] c"It's cool\0A", align 1
@alloc_4a495105947d39c5f45b20726d40c48a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_05617cdedeaddcda4193f197d016d677, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_06f810db97e0ec7697af04aa74d840b1 = private unnamed_addr constant [10 x i8] c"It's mild\0A", align 1
@alloc_a8a7eaf10e77a22d0e7aea7445fb60df = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_06f810db97e0ec7697af04aa74d840b1, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_3aa79049170c6c9cef0a951a5e350f27 = private unnamed_addr constant [10 x i8] c"It's warm\0A", align 1
@alloc_f29eda1ab2e9567e57965c529178d222 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3aa79049170c6c9cef0a951a5e350f27, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_a4341870411cbd07b682e0fbe6ef5b3d = private unnamed_addr constant [15 x i8] c"It's very hot!\0A", align 1
@alloc_c94991417afdbbadb50d366ee35c7984 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a4341870411cbd07b682e0fbe6ef5b3d, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_9b1f5bfdc3c137e004999607f5783dbb = private unnamed_addr constant [34 x i8] c"Nested if statements (x=10, y=5):\0A", align 1
@alloc_16c9e033425a8875dfb48bd03d47b312 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9b1f5bfdc3c137e004999607f5783dbb, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_4d8f916c82c883d35898e649d46561af = private unnamed_addr constant [18 x i8] c"x is not positive\0A", align 1
@alloc_4f1d2cf956dc52d971639accfe9b1252 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4d8f916c82c883d35898e649d46561af, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_b5b92b937b8bda964f4003401af73038 = private unnamed_addr constant [14 x i8] c"x is positive\0A", align 1
@alloc_27a3c993d67bb7e720866c550af5e707 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b5b92b937b8bda964f4003401af73038, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_30e5286651069c39fca7ef790d2642d2 = private unnamed_addr constant [18 x i8] c"y is not positive\0A", align 1
@alloc_64cad4df76c0eab3999cc34ab205c545 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_30e5286651069c39fca7ef790d2642d2, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_742b6053612b89572c471c079bdf50a0 = private unnamed_addr constant [19 x i8] c"y is also positive\0A", align 1
@alloc_3fccf7082f805eac6432bbee391f5fbc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_742b6053612b89572c471c079bdf50a0, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_5cd8fc1288dfa436c593966d13ea1e04 = private unnamed_addr constant [32 x i8] c"y is greater than or equal to x\0A", align 1
@alloc_2e86b1813d5b040d0f0028d3fd7b1dbf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5cd8fc1288dfa436c593966d13ea1e04, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_850eb7aac7b52c2dba9c969dfc562a6e = private unnamed_addr constant [20 x i8] c"x is greater than y\0A", align 1
@alloc_9c19e1257e0244b68c1a18622e785727 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_850eb7aac7b52c2dba9c969dfc562a6e, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_f1908d8033ecad32c38d9548fcf892bf = private unnamed_addr constant [32 x i8] c"Complex conditions (a=15, b=8):\0A", align 1
@alloc_d28663ab237cf81cea01275c9caafa93 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f1908d8033ecad32c38d9548fcf892bf, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_4985c0c8cfcfba0c409fe1a01b7bc05a = private unnamed_addr constant [28 x i8] c"04_conditional_statements.rs", align 1
@alloc_321e6638256a33b6cbf9daf4671b6a50 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_4985c0c8cfcfba0c409fe1a01b7bc05a, [16 x i8] c"\1C\00\00\00\00\00\00\00U\00\00\00\08\00\00\00" }>, align 8
@alloc_7519acac2871207ea3aad123230536b0 = private unnamed_addr constant [18 x i8] c"Sum is 20 or less\0A", align 1
@alloc_8892dcb11bded092141e2967f612c320 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7519acac2871207ea3aad123230536b0, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_0326046786a9200c6a349db2d3e3f0dc = private unnamed_addr constant [23 x i8] c"Sum is greater than 20\0A", align 1
@alloc_208808db0f04c42b606e6e8c495666e8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0326046786a9200c6a349db2d3e3f0dc, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_3d98172fb21f469b151472f5fb7c4219 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_4985c0c8cfcfba0c409fe1a01b7bc05a, [16 x i8] c"\1C\00\00\00\00\00\00\00W\00\00\00\0C\00\00\00" }>, align 8
@alloc_69b8b131c6e7ff6ecca17468ae98ed55 = private unnamed_addr constant [23 x i8] c"Product is 100 or less\0A", align 1
@alloc_41407d408d7ba57b2395c58c9f13542c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_69b8b131c6e7ff6ecca17468ae98ed55, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_4fca91b5682926170edcde01c0ea361f = private unnamed_addr constant [28 x i8] c"Product is greater than 100\0A", align 1
@alloc_d582be7f67d9b7adc6d802e89388cb9b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4fca91b5682926170edcde01c0ea361f, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_f876b28ff895ac67fe3c6bfc02a403c9 = private unnamed_addr constant [5 x i8] c"admin", align 1
@alloc_168497518f8053a7520b8608bc6592ba = private unnamed_addr constant [6 x i8] c"123456", align 1
@alloc_2fcde696e880c3cce5b82f0e536e65ae = private unnamed_addr constant [19 x i8] c"String conditions:\0A", align 1
@alloc_3243b23bcbb67b1e009edfabc5430107 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2fcde696e880c3cce5b82f0e536e65ae, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_c834d1e9fe7dbe611e3801cfa8bead5a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f876b28ff895ac67fe3c6bfc02a403c9, [8 x i8] c"\05\00\00\00\00\00\00\00" }>, align 8
@alloc_6b9fd531b3c5956a719a490d507b2cc1 = private unnamed_addr constant [19 x i8] c"\E2\9C\97 Wrong username\0A", align 1
@alloc_b3672b9aed9045a78efb76e547757f39 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6b9fd531b3c5956a719a490d507b2cc1, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_d32e6b02ca3b864fa490d239ab966624 = private unnamed_addr constant [20 x i8] c"Username is correct\0A", align 1
@alloc_8426b94a2b204868b64645593c54257b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d32e6b02ca3b864fa490d239ab966624, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_3d565027cf54b65252cd64f5b8c74f31 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_168497518f8053a7520b8608bc6592ba, [8 x i8] c"\06\00\00\00\00\00\00\00" }>, align 8
@alloc_6402b300d92f3217d60f96bfa9f90225 = private unnamed_addr constant [19 x i8] c"\E2\9C\97 Wrong password\0A", align 1
@alloc_0b9b97c6455e39800e7ec123f9b6acee = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6402b300d92f3217d60f96bfa9f90225, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_09fa1eda9c881cfa76b2db662a0a114a = private unnamed_addr constant [22 x i8] c"\E2\9C\93 Login successful!\0A", align 1
@alloc_df204f334522be14dea01b08fe3c481f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09fa1eda9c881cfa76b2db662a0a114a, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_09fed954f538640bb8d440ab5be059d1 = private unnamed_addr constant [20 x i8] c"Boolean conditions:\0A", align 1
@alloc_fb6bd5cdb2d54574f5820dac3211c1cf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09fed954f538640bb8d440ab5be059d1, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_b6b72391336fd20de536994f56c7d7bd = private unnamed_addr constant [18 x i8] c"User is logged in\0A", align 1
@alloc_2ee82d7d8f9008346876eb90df522a59 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b6b72391336fd20de536994f56c7d7bd, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_8b15ab19ab86a1bd2ebad2be7546f3cb = private unnamed_addr constant [34 x i8] c"\E2\9C\97 Access denied - no permission\0A", align 1
@alloc_fb41a68986d886a401b23e6015c062a0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8b15ab19ab86a1bd2ebad2be7546f3cb, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_ef29fb173b9a35326fe57542d19f5f8f = private unnamed_addr constant [41 x i8] c"Multiple condition checks (number = 12):\0A", align 1
@alloc_fbce9edd9b98b11e13ab671deeea0f7a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ef29fb173b9a35326fe57542d19f5f8f, [8 x i8] c")\00\00\00\00\00\00\00" }>, align 8
@alloc_c05990a7af500b3ebbb2d98cd6f9c919 = private unnamed_addr constant [27 x i8] c"Number is zero or negative\0A", align 1
@alloc_22e03ecf1da6bf834534c43ff53edad7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c05990a7af500b3ebbb2d98cd6f9c919, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_3b59e43cd1b63e7351cef854d9358826 = private unnamed_addr constant [19 x i8] c"Number is positive\0A", align 1
@alloc_0c2d609f01ae9d7ae3c07a5ab0facda0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3b59e43cd1b63e7351cef854d9358826, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_3097e9b5333cb8026c6e0ac5bd236585 = private unnamed_addr constant [15 x i8] c"Number is even\0A", align 1
@alloc_479a4c6954dea4ed2d17404c0556b9ac = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3097e9b5333cb8026c6e0ac5bd236585, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_97c75c04b37db49198c804e18a9830c4 = private unnamed_addr constant [21 x i8] c"Number is 10 or less\0A", align 1
@alloc_f04d3b29b3946a2bc8fbebe00e23fe92 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_97c75c04b37db49198c804e18a9830c4, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_583118cf45147a5f03868aaee8ab6ee5 = private unnamed_addr constant [26 x i8] c"Number is greater than 10\0A", align 1
@alloc_254a1deacd6d88a158569fb0ade74df0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_583118cf45147a5f03868aaee8ab6ee5, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_b0d3d9c9380aab56f651c9f55959fe7f = private unnamed_addr constant [14 x i8] c"Number is odd\0A", align 1
@alloc_1aecef1f35060d3cd48c0da5bbca1884 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b0d3d9c9380aab56f651c9f55959fe7f, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_54859d9f6611ace4cc4da7c3503d1cdb = private unnamed_addr constant [7 x i8] c"premium", align 1
@alloc_f7db8ebff5a55299f84ded1cf5a6f2cb = private unnamed_addr constant [37 x i8] c"Practical example - User validation:\0A", align 1
@alloc_3af847e88795a0598f6dc5cd0718d66a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f7db8ebff5a55299f84ded1cf5a6f2cb, [8 x i8] c"%\00\00\00\00\00\00\00" }>, align 8
@alloc_613fee5b526c6e3585464fb752dece48 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_54859d9f6611ace4cc4da7c3503d1cdb, [8 x i8] c"\07\00\00\00\00\00\00\00" }>, align 8
@alloc_d7158cf47b79abc3159ae16f41bc15d4 = private unnamed_addr constant [8 x i8] c"standard", align 1
@alloc_7b61518fc6b1b40082d64cddae5bfc45 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d7158cf47b79abc3159ae16f41bc15d4, [8 x i8] c"\08\00\00\00\00\00\00\00" }>, align 8
@alloc_31a04b7ade07cd01986330b7e2aa106f = private unnamed_addr constant [22 x i8] c"\E2\9C\97 Unknown user type\0A", align 1
@alloc_760728207ffe9deb95f6c2dfa8965458 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_31a04b7ade07cd01986330b7e2aa106f, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_6b3f4571c73f7fda34119bbaaa21ac25 = private unnamed_addr constant [23 x i8] c"Standard user detected\0A", align 1
@alloc_05d289ab8f4643d167bf2207c7a4c01b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6b3f4571c73f7fda34119bbaaa21ac25, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_d0f832a2fd31383d64712c144f79cda0 = private unnamed_addr constant [25 x i8] c"\E2\9C\97 Insufficient balance\0A", align 1
@alloc_fc86686b75319f97b795ca5cbf384087 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d0f832a2fd31383d64712c144f79cda0, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_63af026828a8c54f19c161a972519a75 = private unnamed_addr constant [25 x i8] c"\E2\9C\93 Transaction approved\0A", align 1
@alloc_56d07caa0e71dfc3e616e031d6700f30 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_63af026828a8c54f19c161a972519a75, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_7a7aad7767de6e36e11036b8f33ad62b = private unnamed_addr constant [22 x i8] c"Premium user detected\0A", align 1
@alloc_0c3e167cc7bc3656c6632863e9d8a5bd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7a7aad7767de6e36e11036b8f33ad62b, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_cfd929b384a7431050c7e8506d45068c = private unnamed_addr constant [26 x i8] c"Deeply nested conditions:\0A", align 1
@alloc_33235a1260bbf001098884583fc4c319 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cfd929b384a7431050c7e8506d45068c, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_ea45e0e4cfd5d523bbcfef52c2e1a080 = private unnamed_addr constant [20 x i8] c"New user - welcome!\0A", align 1
@alloc_bf9827446abb3dd2cae842a167b17f82 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ea45e0e4cfd5d523bbcfef52c2e1a080, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_c887808a6786ceecd45cdc948bf98d51 = private unnamed_addr constant [14 x i8] c"Level 1+ user\0A", align 1
@alloc_3e0d5a93ebad2eb860aefbc891a3f361 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c887808a6786ceecd45cdc948bf98d51, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_9189a22b203f039be575348b53d7e30e = private unnamed_addr constant [43 x i8] c"Level 2 required for intermediate features\0A", align 1
@alloc_62f621a1a7f3826eee0f200d30b02897 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9189a22b203f039be575348b53d7e30e, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8
@alloc_4f26b02b6bcf9ab7adfd0c2f37ee0b9e = private unnamed_addr constant [14 x i8] c"Level 2+ user\0A", align 1
@alloc_4552f69195f6c0159a4c84c1de96079e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4f26b02b6bcf9ab7adfd0c2f37ee0b9e, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_86e8e2e30d4b5f1581841ef09d5b73e2 = private unnamed_addr constant [39 x i8] c"Level 3 required for advanced features\0A", align 1
@alloc_02061f6a1785083f91883e184359f977 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_86e8e2e30d4b5f1581841ef09d5b73e2, [8 x i8] c"'\00\00\00\00\00\00\00" }>, align 8
@alloc_d130010b2392af54034b82c81b345cfb = private unnamed_addr constant [14 x i8] c"Level 3+ user\0A", align 1
@alloc_1104276dca1242b2e895c133dbeed46d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d130010b2392af54034b82c81b345cfb, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_4cec7f4a3bd2a8bcea0db1f79b150be1 = private unnamed_addr constant [25 x i8] c"Need more points for VIP\0A", align 1
@alloc_cf624bafdddb36103c2e013154fcd77b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4cec7f4a3bd2a8bcea0db1f79b150be1, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_3e6096f27c4222c2023227aad0d6ab77 = private unnamed_addr constant [17 x i8] c"High points user\0A", align 1
@alloc_8fe83814a51150db1cd247e4e7f5d256 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3e6096f27c4222c2023227aad0d6ab77, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_7199d937393412bdad2a4c769b5b56fe = private unnamed_addr constant [26 x i8] c"\E2\9C\93 VIP status activated!\0A", align 1
@alloc_cc019b17e41023a1afff7bee5e606fb0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7199d937393412bdad2a4c769b5b56fe, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_4eb2365db9c772ee0e075a2a8cb8994b = private unnamed_addr constant [43 x i8] c"=== End of Conditional Statements Demo ===\0A", align 1
@alloc_cab04e4f8eb886d2016c1702210d4b32 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4eb2365db9c772ee0e075a2a8cb8994b, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h412aaeae532548efE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h03d93596123dbd2cE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h6c1023d93e6f9451E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17he53cdb78c2ffd29eE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h6c1023d93e6f9451E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17he5295d110339151bE(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h47cd952780cbbb8eE"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
  %_4.0 = load ptr, ptr %other, align 8
  %1 = getelementptr inbounds i8, ptr %other, i64 8
  %_4.1 = load i64, ptr %1, align 8
; call core::str::traits::<impl core::cmp::PartialEq for str>::eq
  %_0 = call zeroext i1 @"_ZN4core3str6traits54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$str$GT$2eq17h5a30b2ff02d3e563E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 1 %_4.0, i64 %_4.1)
  ret i1 %_0
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.d12f44bc24e478e1874583811c9a5f62.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d12f44bc24e478e1874583811c9a5f62.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h7a5a6344d85efc0eE"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h0bc58d741c3c7bb2E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h0bc58d741c3c7bb2E(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h03d93596123dbd2cE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17he5295d110339151bE(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h2ed06637c8be94b5E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::str::traits::<impl core::cmp::PartialEq for str>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str6traits54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$str$GT$2eq17h5a30b2ff02d3e563E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #1 {
start:
  %other = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %self.0, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %self.1, ptr %0, align 8
  store ptr %other.0, ptr %other, align 8
  %1 = getelementptr inbounds i8, ptr %other, i64 8
  store i64 %other.1, ptr %1, align 8
  %2 = load ptr, ptr %self, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = load ptr, ptr %other, align 8
  %6 = getelementptr inbounds i8, ptr %other, i64 8
  %7 = load i64, ptr %6, align 8
; call <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
  %_0 = call zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17h22ccd7b76504fb63E"(ptr align 1 %2, i64 %4, ptr align 1 %5, i64 %7)
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17he53cdb78c2ffd29eE"() unnamed_addr #1 {
start:
  ret i32 0
}

; <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17h22ccd7b76504fb63E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #0 {
start:
  %0 = alloca [4 x i8], align 4
  %1 = alloca [8 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %_3 = icmp ne i64 %self.1, %other.1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %2 = mul nuw nsw i64 %self.1, 1
  store i64 %2, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  %3 = call i32 @memcmp(ptr %self.0, ptr %other.0, i64 %size)
  store i32 %3, ptr %0, align 4
  %_7 = load i32, ptr %0, align 4
  %4 = icmp eq i32 %_7, 0
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb4

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb4:                                              ; preds = %bb1, %bb2
  %6 = load i8, ptr %_0, align 1
  %7 = trunc nuw i8 %6 to i1
  ret i1 %7
}

; _04_conditional_statements::main
; Function Attrs: uwtable
define internal void @_ZN26_04_conditional_statements4main17h4ea320495afcc401E() unnamed_addr #0 {
start:
  %_302 = alloca [48 x i8], align 8
  %_299 = alloca [48 x i8], align 8
  %_296 = alloca [48 x i8], align 8
  %_293 = alloca [48 x i8], align 8
  %_290 = alloca [48 x i8], align 8
  %_287 = alloca [48 x i8], align 8
  %_281 = alloca [48 x i8], align 8
  %_277 = alloca [48 x i8], align 8
  %_272 = alloca [48 x i8], align 8
  %_268 = alloca [48 x i8], align 8
  %_264 = alloca [48 x i8], align 8
  %_260 = alloca [48 x i8], align 8
  %_256 = alloca [48 x i8], align 8
  %_253 = alloca [48 x i8], align 8
  %_250 = alloca [48 x i8], align 8
  %_247 = alloca [48 x i8], align 8
  %_243 = alloca [48 x i8], align 8
  %_237 = alloca [48 x i8], align 8
  %_234 = alloca [48 x i8], align 8
  %_230 = alloca [48 x i8], align 8
  %_224 = alloca [48 x i8], align 8
  %user_type = alloca [16 x i8], align 8
  %_219 = alloca [48 x i8], align 8
  %_216 = alloca [48 x i8], align 8
  %_213 = alloca [48 x i8], align 8
  %_210 = alloca [48 x i8], align 8
  %_207 = alloca [48 x i8], align 8
  %_203 = alloca [48 x i8], align 8
  %_195 = alloca [48 x i8], align 8
  %_191 = alloca [48 x i8], align 8
  %_187 = alloca [48 x i8], align 8
  %_181 = alloca [48 x i8], align 8
  %_174 = alloca [48 x i8], align 8
  %_170 = alloca [48 x i8], align 8
  %_167 = alloca [48 x i8], align 8
  %_164 = alloca [48 x i8], align 8
  %_161 = alloca [48 x i8], align 8
  %_158 = alloca [48 x i8], align 8
  %_152 = alloca [48 x i8], align 8
  %_146 = alloca [48 x i8], align 8
  %password = alloca [16 x i8], align 8
  %username = alloca [16 x i8], align 8
  %_141 = alloca [48 x i8], align 8
  %_138 = alloca [48 x i8], align 8
  %_135 = alloca [48 x i8], align 8
  %_132 = alloca [48 x i8], align 8
  %_126 = alloca [48 x i8], align 8
  %_120 = alloca [48 x i8], align 8
  %_115 = alloca [48 x i8], align 8
  %_112 = alloca [48 x i8], align 8
  %_109 = alloca [48 x i8], align 8
  %_106 = alloca [48 x i8], align 8
  %_103 = alloca [48 x i8], align 8
  %_99 = alloca [48 x i8], align 8
  %_95 = alloca [48 x i8], align 8
  %_91 = alloca [48 x i8], align 8
  %_86 = alloca [48 x i8], align 8
  %_83 = alloca [48 x i8], align 8
  %_80 = alloca [48 x i8], align 8
  %_76 = alloca [48 x i8], align 8
  %_72 = alloca [48 x i8], align 8
  %_68 = alloca [48 x i8], align 8
  %_64 = alloca [48 x i8], align 8
  %_60 = alloca [48 x i8], align 8
  %_56 = alloca [48 x i8], align 8
  %_53 = alloca [48 x i8], align 8
  %_50 = alloca [48 x i8], align 8
  %_46 = alloca [48 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %_38 = alloca [48 x i8], align 8
  %_34 = alloca [48 x i8], align 8
  %_30 = alloca [48 x i8], align 8
  %_27 = alloca [48 x i8], align 8
  %_24 = alloca [48 x i8], align 8
  %_19 = alloca [48 x i8], align 8
  %_16 = alloca [48 x i8], align 8
  %_13 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_f86105c76f9380b78b07402ee8cc424d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_8daf906fefea4c98a8be166ee9433daf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
  br label %bb7

bb7:                                              ; preds = %start
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_13, ptr align 8 @alloc_525c2040f6f1502c10c7b83620ac4bf0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_13)
  br label %bb9

bb9:                                              ; preds = %bb7
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_16, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_16)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_19, ptr align 8 @alloc_d27c69d8abf62b21d8b2cdeb0568cf3f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_19)
  br label %bb16

bb16:                                             ; preds = %bb9
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_27, ptr align 8 @alloc_fd950fe31639541b5a6f68a08ecd88bd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_27)
  br label %bb18

bb18:                                             ; preds = %bb14, %bb16
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_30, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_30)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_34, ptr align 8 @alloc_6655e66435ff12887a10966b87d418ce)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_34)
  br label %bb23

bb14:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_24, ptr align 8 @alloc_99e44f0c502056206671b19f10bf41a1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_24)
  br label %bb18

bb23:                                             ; preds = %bb18
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_38, ptr align 8 @alloc_713f5727e4262be8416e528b55da1cd7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_38)
  br label %bb36

bb25:                                             ; No predecessors!
  br label %bb26

bb26:                                             ; preds = %bb25
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_42, ptr align 8 @alloc_9ca807bc7b0c325c3b66af24529e8b64)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_42)
  br label %bb36

bb28:                                             ; No predecessors!
  br label %bb29

bb29:                                             ; preds = %bb28
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_46, ptr align 8 @alloc_3e1dc8319c2e9f7badc1123d1aa42be1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_46)
  br label %bb36

bb31:                                             ; No predecessors!
  br label %bb32

bb32:                                             ; preds = %bb31
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_50, ptr align 8 @alloc_07322d9824ec4d35e74eb9033c8d2e87)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_50)
  br label %bb36

bb34:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_53, ptr align 8 @alloc_86c76d745292d3636de6d7979ede66a6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_53)
  br label %bb36

bb36:                                             ; preds = %bb23, %bb26, %bb29, %bb32, %bb34
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_56, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_56)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_60, ptr align 8 @alloc_2b167f7ac482b41d83cfb4f697db1125)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_60)
  br label %bb43

bb43:                                             ; preds = %bb36
  br label %bb46

bb46:                                             ; preds = %bb43
  br label %bb47

bb47:                                             ; preds = %bb46
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_72, ptr align 8 @alloc_a8a7eaf10e77a22d0e7aea7445fb60df)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_72)
  br label %bb57

bb49:                                             ; No predecessors!
  br label %bb50

bb50:                                             ; preds = %bb49
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_76, ptr align 8 @alloc_4a495105947d39c5f45b20726d40c48a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_76)
  br label %bb57

bb52:                                             ; No predecessors!
  br label %bb53

bb53:                                             ; preds = %bb52
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_80, ptr align 8 @alloc_0c7e29fef24828dd20d35f4af0e523b1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_80)
  br label %bb57

bb55:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_83, ptr align 8 @alloc_f79d9a13b32afdff846b49bbba80e637)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_83)
  br label %bb57

bb57:                                             ; preds = %bb41, %bb44, %bb47, %bb50, %bb53, %bb55
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_86, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_86)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_91, ptr align 8 @alloc_16c9e033425a8875dfb48bd03d47b312)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_91)
  br label %bb62

bb44:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_68, ptr align 8 @alloc_f29eda1ab2e9567e57965c529178d222)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_68)
  br label %bb57

bb41:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_64, ptr align 8 @alloc_c94991417afdbbadb50d366ee35c7984)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_64)
  br label %bb57

bb62:                                             ; preds = %bb57
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_95, ptr align 8 @alloc_27a3c993d67bb7e720866c550af5e707)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_95)
  br label %bb65

bb74:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_112, ptr align 8 @alloc_4f1d2cf956dc52d971639accfe9b1252)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_112)
  br label %bb76

bb76:                                             ; preds = %bb68, %bb70, %bb72, %bb74
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_115, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_115)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_120, ptr align 8 @alloc_d28663ab237cf81cea01275c9caafa93)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_120)
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 15, i32 8)
  %_124.0 = extractvalue { i32, i1 } %0, 0
  %_124.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_124.1, label %panic, label %bb81

bb65:                                             ; preds = %bb62
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_99, ptr align 8 @alloc_3fccf7082f805eac6432bbee391f5fbc)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_99)
  br label %bb68

bb72:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_109, ptr align 8 @alloc_64cad4df76c0eab3999cc34ab205c545)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_109)
  br label %bb76

bb68:                                             ; preds = %bb65
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_103, ptr align 8 @alloc_9c19e1257e0244b68c1a18622e785727)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_103)
  br label %bb76

bb70:                                             ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_106, ptr align 8 @alloc_2e86b1813d5b040d0f0028d3fd7b1dbf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_106)
  br label %bb76

bb81:                                             ; preds = %bb76
  %_122 = icmp sgt i32 %_124.0, 20
  br i1 %_122, label %bb82, label %bb90

panic:                                            ; preds = %bb76
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_321e6638256a33b6cbf9daf4671b6a50) #6
  unreachable

bb90:                                             ; preds = %bb81
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_138, ptr align 8 @alloc_8892dcb11bded092141e2967f612c320)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_138)
  br label %bb92

bb82:                                             ; preds = %bb81
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_126, ptr align 8 @alloc_208808db0f04c42b606e6e8c495666e8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_126)
  %1 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 15, i32 8)
  %_130.0 = extractvalue { i32, i1 } %1, 0
  %_130.1 = extractvalue { i32, i1 } %1, 1
  br i1 %_130.1, label %panic1, label %bb85

bb92:                                             ; preds = %bb86, %bb88, %bb90
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_141, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_141)
  store ptr @alloc_f876b28ff895ac67fe3c6bfc02a403c9, ptr %username, align 8
  %2 = getelementptr inbounds i8, ptr %username, i64 8
  store i64 5, ptr %2, align 8
  store ptr @alloc_168497518f8053a7520b8608bc6592ba, ptr %password, align 8
  %3 = getelementptr inbounds i8, ptr %password, i64 8
  store i64 6, ptr %3, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_146, ptr align 8 @alloc_3243b23bcbb67b1e009edfabc5430107)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_146)
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
  %_148 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h47cd952780cbbb8eE"(ptr align 8 %username, ptr align 8 @alloc_c834d1e9fe7dbe611e3801cfa8bead5a)
  br i1 %_148, label %bb98, label %bb106

bb85:                                             ; preds = %bb82
  %_128 = icmp sgt i32 %_130.0, 100
  br i1 %_128, label %bb86, label %bb88

panic1:                                           ; preds = %bb82
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_3d98172fb21f469b151472f5fb7c4219) #6
  unreachable

bb88:                                             ; preds = %bb85
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_135, ptr align 8 @alloc_41407d408d7ba57b2395c58c9f13542c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_135)
  br label %bb92

bb86:                                             ; preds = %bb85
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_132, ptr align 8 @alloc_d582be7f67d9b7adc6d802e89388cb9b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_132)
  br label %bb92

bb106:                                            ; preds = %bb92
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_164, ptr align 8 @alloc_b3672b9aed9045a78efb76e547757f39)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_164)
  br label %bb108

bb98:                                             ; preds = %bb92
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_152, ptr align 8 @alloc_8426b94a2b204868b64645593c54257b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_152)
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
  %_154 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h47cd952780cbbb8eE"(ptr align 8 %password, ptr align 8 @alloc_3d565027cf54b65252cd64f5b8c74f31)
  br i1 %_154, label %bb102, label %bb104

bb108:                                            ; preds = %bb102, %bb104, %bb106
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_167, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_167)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_170, ptr align 8 @alloc_fb6bd5cdb2d54574f5820dac3211c1cf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_170)
  br label %bb113

bb104:                                            ; preds = %bb98
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_161, ptr align 8 @alloc_0b9b97c6455e39800e7ec123f9b6acee)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_161)
  br label %bb108

bb102:                                            ; preds = %bb98
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_158, ptr align 8 @alloc_df204f334522be14dea01b08fe3c481f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_158)
  br label %bb108

bb113:                                            ; preds = %bb108
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_174, ptr align 8 @alloc_2ee82d7d8f9008346876eb90df522a59)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_174)
  br label %bb118

bb118:                                            ; preds = %bb113
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_181, ptr align 8 @alloc_fb41a68986d886a401b23e6015c062a0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_181)
  br label %bb122

bb122:                                            ; preds = %bb118
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_187, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_187)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_191, ptr align 8 @alloc_fbce9edd9b98b11e13ab671deeea0f7a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_191)
  br label %bb127

bb127:                                            ; preds = %bb122
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_195, ptr align 8 @alloc_0c2d609f01ae9d7ae3c07a5ab0facda0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_195)
  br label %bb132

bb141:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_216, ptr align 8 @alloc_22e03ecf1da6bf834534c43ff53edad7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_216)
  br label %bb143

bb143:                                            ; preds = %bb139, %bb135, %bb137, %bb141
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_219, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_219)
  store ptr @alloc_54859d9f6611ace4cc4da7c3503d1cdb, ptr %user_type, align 8
  %4 = getelementptr inbounds i8, ptr %user_type, i64 8
  store i64 7, ptr %4, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_224, ptr align 8 @alloc_3af847e88795a0598f6dc5cd0718d66a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_224)
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
  %_226 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h47cd952780cbbb8eE"(ptr align 8 %user_type, ptr align 8 @alloc_613fee5b526c6e3585464fb752dece48)
  br i1 %_226, label %bb149, label %bb156

bb132:                                            ; preds = %bb127
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_203, ptr align 8 @alloc_479a4c6954dea4ed2d17404c0556b9ac)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_203)
  br label %bb135

bb135:                                            ; preds = %bb132
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_207, ptr align 8 @alloc_254a1deacd6d88a158569fb0ade74df0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_207)
  br label %bb143

bb137:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_210, ptr align 8 @alloc_f04d3b29b3946a2bc8fbebe00e23fe92)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_210)
  br label %bb143

bb139:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_213, ptr align 8 @alloc_1aecef1f35060d3cd48c0da5bbca1884)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_213)
  br label %bb143

bb156:                                            ; preds = %bb143
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
  %_239 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h47cd952780cbbb8eE"(ptr align 8 %user_type, ptr align 8 @alloc_7b61518fc6b1b40082d64cddae5bfc45)
  br i1 %_239, label %bb158, label %bb165

bb149:                                            ; preds = %bb143
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_230, ptr align 8 @alloc_0c3e167cc7bc3656c6632863e9d8a5bd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_230)
  br label %bb152

bb165:                                            ; preds = %bb156
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_253, ptr align 8 @alloc_760728207ffe9deb95f6c2dfa8965458)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_253)
  br label %bb167

bb158:                                            ; preds = %bb156
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_243, ptr align 8 @alloc_05d289ab8f4643d167bf2207c7a4c01b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_243)
  br label %bb161

bb167:                                            ; preds = %bb152, %bb154, %bb161, %bb163, %bb165
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_256, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_256)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_260, ptr align 8 @alloc_33235a1260bbf001098884583fc4c319)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_260)
  br label %bb172

bb161:                                            ; preds = %bb158
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_247, ptr align 8 @alloc_56d07caa0e71dfc3e616e031d6700f30)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_247)
  br label %bb167

bb163:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_250, ptr align 8 @alloc_fc86686b75319f97b795ca5cbf384087)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_250)
  br label %bb167

bb152:                                            ; preds = %bb149
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_234, ptr align 8 @alloc_56d07caa0e71dfc3e616e031d6700f30)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_234)
  br label %bb167

bb154:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_237, ptr align 8 @alloc_fc86686b75319f97b795ca5cbf384087)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_237)
  br label %bb167

bb172:                                            ; preds = %bb167
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_264, ptr align 8 @alloc_3e0d5a93ebad2eb860aefbc891a3f361)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_264)
  br label %bb175

bb194:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_296, ptr align 8 @alloc_bf9827446abb3dd2cae842a167b17f82)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_296)
  br label %bb196

bb196:                                            ; preds = %bb184, %bb188, %bb190, %bb192, %bb194
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_299, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_299)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_302, ptr align 8 @alloc_cab04e4f8eb886d2016c1702210d4b32)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_302)
  ret void

bb175:                                            ; preds = %bb172
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_268, ptr align 8 @alloc_4552f69195f6c0159a4c84c1de96079e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_268)
  br label %bb178

bb192:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_293, ptr align 8 @alloc_62f621a1a7f3826eee0f200d30b02897)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_293)
  br label %bb196

bb178:                                            ; preds = %bb175
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_272, ptr align 8 @alloc_1104276dca1242b2e895c133dbeed46d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_272)
  br label %bb181

bb190:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_290, ptr align 8 @alloc_02061f6a1785083f91883e184359f977)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_290)
  br label %bb196

bb181:                                            ; preds = %bb178
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_277, ptr align 8 @alloc_8fe83814a51150db1cd247e4e7f5d256)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_277)
  br label %bb184

bb188:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_287, ptr align 8 @alloc_cf624bafdddb36103c2e013154fcd77b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_287)
  br label %bb196

bb184:                                            ; preds = %bb181
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hb493a7b6832010fdE(ptr sret([48 x i8]) align 8 %_281, ptr align 8 @alloc_cc019b17e41023a1afff7bee5e606fb0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_281)
  br label %bb196

bb116:                                            ; No predecessors!
  unreachable

bb117:                                            ; No predecessors!
  unreachable

bb120:                                            ; No predecessors!
  unreachable

bb121:                                            ; No predecessors!
  unreachable

bb186:                                            ; No predecessors!
  unreachable

bb187:                                            ; No predecessors!
  unreachable
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

declare i32 @__CxxFrameHandler3(...) unnamed_addr #3

declare i32 @memcmp(ptr, ptr, i64)

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #4

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #5

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #4

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #5

define i32 @main(i32 %0, ptr %1) unnamed_addr #3 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h412aaeae532548efE(ptr @_ZN26_04_conditional_statements4main17h4ea320495afcc401E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { "target-cpu"="x86-64" }
attributes #4 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #5 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { noreturn }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 9899925389388747}
