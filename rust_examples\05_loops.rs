// 5. Loops - while, for, nested loops (Rust version)
// This file demonstrates all loop patterns in Rust for comparison with Dolet

fn main() {
    println!("=== Loops Demo (Rust) ===");
    println!();

    // Simple while loop
    println!("Simple while loop (counting 1 to 5):");
    let mut counter = 1;
    while counter <= 5 {
        println!("Count: {}", counter);
        counter += 1;
    }
    println!();

    // While loop with condition
    println!("While loop with condition (countdown from 10):");
    let mut countdown = 10;
    while countdown > 0 {
        println!("Countdown: {}", countdown);
        countdown -= 1;
    }
    println!("Blast off!");
    println!();

    // For loop with range
    println!("For loop with range (0 to 4):");
    for i in 0..5 {
        println!("Iteration: {}", i);
    }
    println!();

    // For loop with different range
    println!("For loop with range (1 to 10):");
    for num in 1..=10 {  // inclusive range
        println!("Number: {}", num);
    }
    println!();

    // For loop with step
    println!("For loop counting by 2s (2, 4, 6, 8, 10):");
    for i in (2..=10).step_by(2) {
        println!("Value: {}", i);
    }
    println!();

    // Nested while loops
    println!("Nested while loops (multiplication table 1-3):");
    let mut outer = 1;
    while outer <= 3 {
        println!("Table for {}:", outer);
        let mut inner = 1;
        while inner <= 5 {
            let result = outer * inner;
            println!("  {} x {} = {}", outer, inner, result);
            inner += 1;
        }
        outer += 1;
        println!();
    }

    // Nested for loops
    println!("Nested for loops (grid pattern):");
    for row in 1..=3 {
        for col in 1..=3 {
            print!("({},{}) ", row, col);
        }
        println!();
    }
    println!();

    // While loop with complex condition
    println!("While loop with complex condition:");
    let mut x = 1;
    let mut sum = 0;
    while x <= 10 && sum < 30 {
        sum += x;
        println!("x={}, sum={}", x, sum);
        x += 1;
    }
    println!("Final sum: {}", sum);
    println!();

    // For loop with calculations
    println!("For loop with calculations (squares):");
    for i in 1..=5 {
        let square = i * i;
        println!("{} squared = {}", i, square);
    }
    println!();

    // While loop for search simulation
    println!("While loop simulation (finding target):");
    let target = 7;
    let mut guess = 1;
    while guess != target {
        println!("Guess: {} (not the target)", guess);
        guess += 1;
    }
    println!("Found target: {}", guess);
    println!();

    // Nested loops with conditions
    println!("Nested loops with conditions:");
    for i in 1..=3 {
        println!("Outer loop i={}", i);
        for j in 1..=3 {
            if i == j {
                println!("  i equals j: {}", i);
            } else {
                println!("  i={}, j={}", i, j);
            }
        }
    }
    println!();

    // While loop with break
    println!("While loop with early exit (break):");
    let mut search = 1;
    let mut found_at = 0;
    while search <= 10 {
        if search == 6 {
            found_at = search;
            println!("Found target at position: {}", found_at);
            break;
        } else {
            println!("Searching position: {}", search);
            search += 1;
        }
    }
    println!();

    // For loop with accumulation
    println!("For loop with accumulation (factorial of 5):");
    let mut factorial = 1;
    for i in 1..=5 {
        factorial *= i;
        println!("Step {}: factorial = {}", i, factorial);
    }
    println!("Final factorial of 5: {}", factorial);
    println!();

    // Complex nested loop example
    println!("Complex nested loop (pattern printing):");
    for i in 1..=3 {
        for _j in 1..=i {
            print!("*");
        }
        println!();
    }
    println!();

    // While loop with multiple variables
    println!("While loop with multiple variables:");
    let mut a = 1;
    let mut b = 10;
    while a < b {
        println!("a={}, b={}, difference={}", a, b, b - a);
        a += 1;
        b -= 1;
    }
    println!("Final: a={}, b={}", a, b);
    println!();

    // For loop with string operations
    println!("For loop with string operations:");
    let base = "Hello";
    for i in 1..=3 {
        let repeated = format!("{} {}", base, i);
        println!("{}", repeated);
    }
    println!();

    // Iterator methods (Rust-specific)
    println!("Iterator methods (Rust-specific):");
    let numbers: Vec<i32> = (1..=10).collect();
    let sum: i32 = numbers.iter().sum();
    println!("Sum of 1-10: {}", sum);
    
    let evens: Vec<i32> = numbers.iter().filter(|&x| x % 2 == 0).cloned().collect();
    println!("Even numbers: {:?}", evens);
    println!();

    println!("=== End of Loops Demo ===");
}
