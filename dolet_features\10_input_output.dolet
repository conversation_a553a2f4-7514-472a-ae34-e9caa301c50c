# 10. Input/Output Operations - User Interaction
# This file demonstrates input/output operations in Dolet

print "=== Input/Output Operations Demo ==="
print ""

# Basic output
print "Basic Output Operations:"
print "This is a simple print statement"
print "Multiple lines can be printed"
print "Each print creates a new line"
print ""

# Output with variables
print "Output with Variables:"
string name = "<PERSON>"
int age = 25
float height = 175.5

print "Name: " + name
print "Age: " + age
print "Height: " + height + " cm"
print ""

# Formatted output simulation
print "Formatted Output Simulation:"
string product = "Laptop"
float price = 1299.99
int quantity = 3
float total = price * quantity

print "Product: " + product
print "Price: $" + price
print "Quantity: " + quantity
print "Total: $" + total
print ""

# Input operations (simulation)
print "Input Operations (Simulation):"
print "Note: Actual input would use 'input' keyword"

# Simulated input
print "Enter your name: "
string user_name = "John"  # This would be: input string user_name
print "Hello, " + user_name + "!"

print "Enter your age: "
int user_age = 30  # This would be: input int user_age
print "You are " + user_age + " years old"
print ""

# Input validation simulation
print "Input Validation Simulation:"
print "Enter a number between 1 and 10: "
int user_number = 5  # Simulated input

if user_number >= 1 and user_number <= 10
    print "Valid input: " + user_number
else
    print "Invalid input. Please enter a number between 1 and 10"
print ""

# Multiple inputs
print "Multiple Inputs:"
print "Enter your details:"
print "First name: "
string first_name = "Alice"  # Simulated

print "Last name: "
string last_name = "Smith"  # Simulated

print "Email: "
string email = "<EMAIL>"  # Simulated

print ""
print "Summary of entered information:"
print "Full name: " + first_name + " " + last_name
print "Email: " + email
print ""

# Input with different data types
print "Input with Different Data Types:"
print "Enter an integer: "
int int_input = 42

print "Enter a float: "
float float_input = 3.14

print "Enter a boolean (true/false): "
bool bool_input = true

print "Enter a character: "
char char_input = 'A'

print ""
print "You entered:"
print "Integer: " + int_input
print "Float: " + float_input
print "Boolean: " + bool_input
print "Character: " + char_input
print ""

# Interactive menu simulation
print "Interactive Menu Simulation:"
print "=== Main Menu ==="
print "1. View Profile"
print "2. Edit Settings"
print "3. Help"
print "4. Exit"
print "Enter your choice (1-4): "

int choice = 2  # Simulated input

if choice == 1
    print "Displaying profile..."
elif choice == 2
    print "Opening settings..."
elif choice == 3
    print "Showing help..."
elif choice == 4
    print "Goodbye!"
else
    print "Invalid choice"
print ""

# Input loop simulation
print "Input Loop Simulation:"
print "Enter numbers (0 to stop):"

int sum = 0
int count = 0

# Simulated input loop
array int inputs = [5, 10, 15, 20, 0]
for i = 0 to 4
    int current = inputs[i]
    print "Input: " + current
    
    if current == 0
        print "Stopping..."
    else
        set sum = sum + current
        set count = count + 1

if count > 0
    float average = sum / count
    print "Sum: " + sum
    print "Count: " + count
    print "Average: " + average
print ""

# File output simulation
print "File Output Simulation:"
print "Writing to file 'output.txt':"
string file_content = "This is line 1\nThis is line 2\nThis is line 3"
print "Content to write:"
print file_content
print "Note: Actual file writing would use file operations"
print ""

# Error handling for input
print "Error Handling for Input:"
print "Enter a positive number: "
int number = -5  # Simulated problematic input

if number > 0
    print "Valid positive number: " + number
else
    print "Error: Please enter a positive number"
    print "You entered: " + number
print ""

# Input with prompts
fun get_user_info() {
    print "=== User Registration ==="
    
    print "Enter username: "
    string username = "user123"  # Simulated
    
    print "Enter password: "
    string password = "secret"   # Simulated
    
    print "Enter email: "
    string email = "<EMAIL>"  # Simulated
    
    print ""
    print "Registration complete!"
    print "Username: " + username
    print "Email: " + email
    print "Password: [HIDDEN]"
}

get_user_info()
print ""

# Batch output
print "Batch Output:"
array string messages = [
    "Processing file 1...",
    "Processing file 2...",
    "Processing file 3...",
    "All files processed!",
    "Operation complete."
]

for i = 0 to 4
    print messages[i]
print ""

# Output formatting with alignment simulation
print "Output Formatting Simulation:"
print "Product Report:"
print "Name          Price    Qty   Total"
print "--------------------------------"
print "Laptop        $1299    2     $2598"
print "Mouse         $25      5     $125"
print "Keyboard      $75      3     $225"
print "--------------------------------"
print "Grand Total:                $2948"
print ""

print "=== End of Input/Output Demo ==="
