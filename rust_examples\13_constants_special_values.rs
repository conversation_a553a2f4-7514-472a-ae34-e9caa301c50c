// 13. Constants and Special Values (Rust version)
// This file demonstrates constants and special values in Rust for comparison with Dolet

use std::f64;

// Global constants
const PI: f64 = 3.14159265359;
const MAX_USERS: i32 = 1000;
const APP_NAME: &str = "Rust Demo App";
const VERSION: &str = "1.0.0";

fn main() {
    println!("=== Constants and Special Values Demo (Rust) ===");
    println!();

    // Using global constants
    println!("Global constants:");
    println!("PI = {}", PI);
    println!("MAX_USERS = {}", MAX_USERS);
    println!("APP_NAME = {}", APP_NAME);
    println!("VERSION = {}", VERSION);
    println!();

    // Mathematical constants
    println!("Mathematical constants:");
    println!("π (PI) = {:.10}", PI);
    println!("e (<PERSON><PERSON><PERSON>'s number) = {:.10}", f64::consts::E);
    println!("√2 = {:.10}", f64::consts::SQRT_2);
    println!("ln(2) = {:.10}", f64::consts::LN_2);
    println!("ln(10) = {:.10}", f64::consts::LN_10);
    println!();

    // Boolean constants
    println!("Boolean constants:");
    const IS_ACTIVE: bool = true;
    const IS_DEBUG: bool = false;
    println!("IS_ACTIVE = {}", IS_ACTIVE);
    println!("IS_DEBUG = {}", IS_DEBUG);
    println!();

    // Numeric constants
    println!("Numeric constants:");
    const ZERO: i32 = 0;
    const ONE: i32 = 1;
    const NEGATIVE_ONE: i32 = -1;
    const MAX_SCORE: i32 = 100;
    const MIN_SCORE: i32 = 0;
    
    println!("ZERO = {}", ZERO);
    println!("ONE = {}", ONE);
    println!("NEGATIVE_ONE = {}", NEGATIVE_ONE);
    println!("MAX_SCORE = {}", MAX_SCORE);
    println!("MIN_SCORE = {}", MIN_SCORE);
    println!();

    // String constants
    println!("String constants:");
    const EMPTY_STRING: &str = "";
    const SPACE: &str = " ";
    const NEWLINE: &str = "\n";
    const TAB: &str = "\t";
    const GREETING: &str = "Hello, World!";
    
    println!("EMPTY_STRING = '{}'", EMPTY_STRING);
    println!("SPACE = '{}'", SPACE);
    println!("GREETING = '{}'", GREETING);
    println!("String with TAB: 'Before{}After'", TAB);
    println!();

    // Array constants
    println!("Array constants:");
    const DAYS_IN_WEEK: i32 = 7;
    const MONTHS_IN_YEAR: i32 = 12;
    const HOURS_IN_DAY: i32 = 24;
    const MINUTES_IN_HOUR: i32 = 60;
    const SECONDS_IN_MINUTE: i32 = 60;
    
    println!("DAYS_IN_WEEK = {}", DAYS_IN_WEEK);
    println!("MONTHS_IN_YEAR = {}", MONTHS_IN_YEAR);
    println!("HOURS_IN_DAY = {}", HOURS_IN_DAY);
    println!("MINUTES_IN_HOUR = {}", MINUTES_IN_HOUR);
    println!("SECONDS_IN_MINUTE = {}", SECONDS_IN_MINUTE);
    println!();

    // Special numeric values
    println!("Special numeric values:");
    let positive_infinity = f64::INFINITY;
    let negative_infinity = f64::NEG_INFINITY;
    let not_a_number = f64::NAN;
    
    println!("Positive infinity: {}", positive_infinity);
    println!("Negative infinity: {}", negative_infinity);
    println!("Not a number (NaN): {}", not_a_number);
    println!("Is NaN actually NaN? {}", not_a_number.is_nan());
    println!();

    // Integer limits
    println!("Integer limits:");
    println!("i32 MIN: {}", i32::MIN);
    println!("i32 MAX: {}", i32::MAX);
    println!("u32 MIN: {}", u32::MIN);
    println!("u32 MAX: {}", u32::MAX);
    println!();

    // Float limits
    println!("Float limits:");
    println!("f64 MIN: {}", f64::MIN);
    println!("f64 MAX: {}", f64::MAX);
    println!("f64 EPSILON: {}", f64::EPSILON);
    println!();

    // Using constants in calculations
    println!("Using constants in calculations:");
    let radius = 5.0;
    let area = PI * radius * radius;
    let circumference = 2.0 * PI * radius;
    
    println!("Circle with radius {}:", radius);
    println!("Area = π × r² = {} × {}² = {:.2}", PI, radius, area);
    println!("Circumference = 2π × r = 2 × {} × {} = {:.2}", PI, radius, circumference);
    println!();

    // Time calculations using constants
    println!("Time calculations:");
    let total_seconds = 3661; // 1 hour, 1 minute, 1 second
    let hours = total_seconds / (MINUTES_IN_HOUR * SECONDS_IN_MINUTE);
    let remaining_after_hours = total_seconds % (MINUTES_IN_HOUR * SECONDS_IN_MINUTE);
    let minutes = remaining_after_hours / SECONDS_IN_MINUTE;
    let seconds = remaining_after_hours % SECONDS_IN_MINUTE;
    
    println!("Converting {} seconds:", total_seconds);
    println!("Hours: {}", hours);
    println!("Minutes: {}", minutes);
    println!("Seconds: {}", seconds);
    println!("Format: {}:{}:{}", hours, minutes, seconds);
    println!();

    // Configuration constants
    println!("Configuration constants:");
    const DEFAULT_PORT: u16 = 8080;
    const DEFAULT_HOST: &str = "localhost";
    const MAX_CONNECTIONS: i32 = 100;
    const TIMEOUT_SECONDS: i32 = 30;
    const BUFFER_SIZE: usize = 1024;
    
    println!("Server configuration:");
    println!("  Host: {}", DEFAULT_HOST);
    println!("  Port: {}", DEFAULT_PORT);
    println!("  Max connections: {}", MAX_CONNECTIONS);
    println!("  Timeout: {} seconds", TIMEOUT_SECONDS);
    println!("  Buffer size: {} bytes", BUFFER_SIZE);
    println!();

    // Status constants
    println!("Status constants:");
    const STATUS_OK: i32 = 200;
    const STATUS_NOT_FOUND: i32 = 404;
    const STATUS_ERROR: i32 = 500;
    
    let current_status = STATUS_OK;
    match current_status {
        STATUS_OK => println!("Status: OK ({})", STATUS_OK),
        STATUS_NOT_FOUND => println!("Status: Not Found ({})", STATUS_NOT_FOUND),
        STATUS_ERROR => println!("Status: Error ({})", STATUS_ERROR),
        _ => println!("Status: Unknown ({})", current_status),
    }
    println!();

    // Color constants (as RGB values)
    println!("Color constants:");
    const RED: (u8, u8, u8) = (255, 0, 0);
    const GREEN: (u8, u8, u8) = (0, 255, 0);
    const BLUE: (u8, u8, u8) = (0, 0, 255);
    const WHITE: (u8, u8, u8) = (255, 255, 255);
    const BLACK: (u8, u8, u8) = (0, 0, 0);
    
    println!("RED = RGB({}, {}, {})", RED.0, RED.1, RED.2);
    println!("GREEN = RGB({}, {}, {})", GREEN.0, GREEN.1, GREEN.2);
    println!("BLUE = RGB({}, {}, {})", BLUE.0, BLUE.1, BLUE.2);
    println!("WHITE = RGB({}, {}, {})", WHITE.0, WHITE.1, WHITE.2);
    println!("BLACK = RGB({}, {}, {})", BLACK.0, BLACK.1, BLACK.2);
    println!();

    // File system constants
    println!("File system constants:");
    const MAX_FILENAME_LENGTH: usize = 255;
    const MAX_PATH_LENGTH: usize = 4096;
    const DEFAULT_FILE_PERMISSIONS: u32 = 0o644;
    
    println!("MAX_FILENAME_LENGTH = {}", MAX_FILENAME_LENGTH);
    println!("MAX_PATH_LENGTH = {}", MAX_PATH_LENGTH);
    println!("DEFAULT_FILE_PERMISSIONS = {:o}", DEFAULT_FILE_PERMISSIONS);
    println!();

    // Using constants in arrays
    println!("Using constants in arrays:");
    let weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    println!("There are {} days in a week:", DAYS_IN_WEEK);
    for (i, day) in weekdays.iter().enumerate() {
        println!("  Day {}: {}", i + 1, day);
    }
    println!();

    // Validation using constants
    println!("Validation using constants:");
    let user_score = 85;
    if user_score >= MIN_SCORE && user_score <= MAX_SCORE {
        println!("Score {} is valid (range: {}-{})", user_score, MIN_SCORE, MAX_SCORE);
    } else {
        println!("Score {} is invalid (range: {}-{})", user_score, MIN_SCORE, MAX_SCORE);
    }
    println!();

    // Constants in expressions
    println!("Constants in expressions:");
    const SECONDS_IN_DAY: i32 = HOURS_IN_DAY * MINUTES_IN_HOUR * SECONDS_IN_MINUTE;
    const SECONDS_IN_WEEK: i32 = DAYS_IN_WEEK * SECONDS_IN_DAY;
    
    println!("Seconds in a day: {}", SECONDS_IN_DAY);
    println!("Seconds in a week: {}", SECONDS_IN_WEEK);
    println!();

    println!("=== End of Constants and Special Values Demo ===");
}
