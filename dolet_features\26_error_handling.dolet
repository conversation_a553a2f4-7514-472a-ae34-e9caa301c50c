# 26. Error Handling - Exception Management and Recovery
# This file demonstrates error handling in Dolet

print "=== Error Handling Demo ==="
print ""

# Basic error checking
print "Basic Error Checking:"

fun safe_division(dividend, divisor) {
    print "Attempting division: " + dividend + " / " + divisor
    
    if divisor == 0
        print "  ERROR: Division by zero"
        print "  Returning error code: -1"
        return -1
    else
        int result = dividend / divisor
        print "  SUCCESS: Result = " + result
        return result
}

int result1 = safe_division(10, 2)
int result2 = safe_division(15, 0)
int result3 = safe_division(20, 4)
print ""

# Input validation
print "Input Validation:"

fun validate_age(age) {
    print "Validating age: " + age
    
    if age < 0
        print "  ERROR: Age cannot be negative"
        return false
    elif age > 150
        print "  ERROR: Age too high (max 150)"
        return false
    elif age == 0
        print "  WARNING: Age is zero"
        return true
    else
        print "  SUCCESS: Age is valid"
        return true
}

bool valid1 = validate_age(25)
bool valid2 = validate_age(-5)
bool valid3 = validate_age(200)
bool valid4 = validate_age(0)
print ""

# Array bounds checking
print "Array Bounds Checking:"

fun safe_array_get(arr, index, size) {
    print "Accessing array[" + index + "] (size=" + size + ")"
    
    if index < 0
        print "  ERROR: Negative index"
        return -999  # Error value
    elif index >= size
        print "  ERROR: Index out of bounds"
        return -999  # Error value
    else
        int value = arr[index]
        print "  SUCCESS: Value = " + value
        return value
}

array int numbers = [10, 20, 30, 40, 50]
int val1 = safe_array_get(numbers, 2, 5)
int val2 = safe_array_get(numbers, -1, 5)
int val3 = safe_array_get(numbers, 10, 5)
print ""

# File operation error handling
print "File Operation Error Handling:"

fun safe_file_read(filename) {
    print "Attempting to read file: '" + filename + "'"
    
    # Simulate file existence check
    if filename == ""
        print "  ERROR: Empty filename"
        return ""
    elif filename == "nonexistent.txt"
        print "  ERROR: File not found"
        return ""
    elif filename == "locked.txt"
        print "  ERROR: File is locked"
        return ""
    else
        print "  SUCCESS: File read successfully"
        return "File content here"
}

string content1 = safe_file_read("data.txt")
string content2 = safe_file_read("")
string content3 = safe_file_read("nonexistent.txt")
string content4 = safe_file_read("locked.txt")
print ""

# Network operation error simulation
print "Network Operation Error Simulation:"

fun connect_to_server(host, port) {
    print "Connecting to " + host + ":" + port
    
    if host == ""
        print "  ERROR: Empty host"
        return false
    elif port <= 0 or port > 65535
        print "  ERROR: Invalid port number"
        return false
    elif host == "unreachable.com"
        print "  ERROR: Host unreachable"
        return false
    elif port == 9999
        print "  ERROR: Connection refused"
        return false
    else
        print "  SUCCESS: Connected successfully"
        return true
}

bool conn1 = connect_to_server("example.com", 80)
bool conn2 = connect_to_server("", 80)
bool conn3 = connect_to_server("example.com", -1)
bool conn4 = connect_to_server("unreachable.com", 80)
print ""

# Memory allocation error simulation
print "Memory Allocation Error Simulation:"

fun allocate_memory(size) {
    print "Allocating " + size + " bytes of memory"
    
    if size <= 0
        print "  ERROR: Invalid size"
        return 0
    elif size > 1000000
        print "  ERROR: Size too large"
        return 0
    else
        print "  SUCCESS: Memory allocated"
        return size  # Return handle/pointer simulation
}

int mem1 = allocate_memory(1024)
int mem2 = allocate_memory(-100)
int mem3 = allocate_memory(2000000)
print ""

# Error recovery strategies
print "Error Recovery Strategies:"

fun robust_operation(data, max_retries) {
    print "Performing robust operation with " + max_retries + " max retries"
    
    int attempts = 0
    bool success = false
    
    while attempts < max_retries and not success
        set attempts = attempts + 1
        print "  Attempt " + attempts + ":"
        
        # Simulate operation that might fail
        if attempts == 1 and data == "fail_first"
            print "    Operation failed - retrying..."
        elif attempts == 2 and data == "fail_twice"
            print "    Operation failed again - retrying..."
        else
            print "    Operation succeeded!"
            set success = true
    
    if success
        print "  RESULT: Operation completed successfully after " + attempts + " attempts"
        return true
    else
        print "  RESULT: Operation failed after " + attempts + " attempts"
        return false
}

bool op1 = robust_operation("normal", 3)
bool op2 = robust_operation("fail_first", 3)
bool op3 = robust_operation("fail_twice", 3)
bool op4 = robust_operation("always_fail", 3)
print ""

# Error logging simulation
print "Error Logging Simulation:"

string error_log = ""

fun log_error(level, message, function_name) {
    string timestamp = "2024-01-15 14:30:00"  # Simulated
    string log_entry = "[" + timestamp + "] " + level + " in " + function_name + ": " + message
    
    print "LOGGING: " + log_entry
    set error_log = error_log + log_entry + "\n"
}

fun risky_operation(input) {
    print "Performing risky operation with input: " + input
    
    if input == ""
        log_error("ERROR", "Empty input provided", "risky_operation")
        return false
    elif input == "invalid"
        log_error("WARNING", "Invalid input detected", "risky_operation")
        return false
    elif input == "dangerous"
        log_error("CRITICAL", "Dangerous input - operation aborted", "risky_operation")
        return false
    else
        log_error("INFO", "Operation completed successfully", "risky_operation")
        return true
}

bool risk1 = risky_operation("normal")
bool risk2 = risky_operation("")
bool risk3 = risky_operation("invalid")
bool risk4 = risky_operation("dangerous")

print "Error log contents:"
print error_log
print ""

# Graceful degradation
print "Graceful Degradation:"

fun feature_with_fallback(use_advanced) {
    print "Attempting to use " + (use_advanced ? "advanced" : "basic") + " feature"
    
    if use_advanced
        print "  Trying advanced algorithm..."
        
        # Simulate advanced feature failure
        bool advanced_available = false  # Simulated check
        
        if advanced_available
            print "  SUCCESS: Advanced feature working"
            return "Advanced result"
        else
            print "  WARNING: Advanced feature unavailable"
            print "  Falling back to basic implementation..."
            return feature_with_fallback(false)
    else
        print "  Using basic algorithm..."
        print "  SUCCESS: Basic feature working"
        return "Basic result"
}

string result_adv = feature_with_fallback(true)
string result_basic = feature_with_fallback(false)
print ""

# Error aggregation
print "Error Aggregation:"

fun validate_user_data(name, email, age) {
    print "Validating user data:"
    print "  Name: '" + name + "'"
    print "  Email: '" + email + "'"
    print "  Age: " + age
    
    array string errors = ["", "", "", "", ""]  # Max 5 errors
    int error_count = 0
    
    if name == ""
        set errors[error_count] = "Name is required"
        set error_count = error_count + 1
    
    if email == ""
        set errors[error_count] = "Email is required"
        set error_count = error_count + 1
    elif email != "" and not (email == "<EMAIL>")  # Simplified email check
        set errors[error_count] = "Invalid email format"
        set error_count = error_count + 1
    
    if age < 0
        set errors[error_count] = "Age cannot be negative"
        set error_count = error_count + 1
    elif age > 120
        set errors[error_count] = "Age too high"
        set error_count = error_count + 1
    
    if error_count == 0
        print "  VALIDATION: All data is valid"
        return true
    else
        print "  VALIDATION ERRORS (" + error_count + "):"
        for i = 0 to error_count - 1
            print "    - " + errors[i]
        return false
    
}

bool user1_valid = validate_user_data("Ahmed", "<EMAIL>", 25)
bool user2_valid = validate_user_data("", "invalid-email", -5)
print ""

print "=== End of Error Handling Demo ==="
