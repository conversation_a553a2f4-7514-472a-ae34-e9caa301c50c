; ModuleID = '14_complex_expressions.d19a203f6a6f531f-cgu.0'
source_filename = "14_complex_expressions.d19a203f6a6f531f-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
%"core::fmt::rt::Placeholder" = type { %"core::fmt::rt::Count", %"core::fmt::rt::Count", i64, i32, [1 x i32] }
%"core::fmt::rt::Count" = type { i16, [7 x i16] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h36b3cab715787970E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h60c0efea28ac9ddbE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h60c0efea28ac9ddbE" }>, align 8
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@anon.2672cf45e15a2962278e8aadee460ad1.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@vtable.1 = private unnamed_addr constant <{ [24 x i8], ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h8567fcf10934acabE" }>, align 8
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8 = private unnamed_addr constant [85 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\accum.rs", align 1
@alloc_2ff564f83739a041825038989c62f69d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8, [16 x i8] c"U\00\00\00\00\00\00\00\95\00\00\00\01\00\00\00" }>, align 8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_3eaeb57b016d0ebe78fe52aed161235e = private unnamed_addr constant [40 x i8] c"=== Complex Expressions Demo (Rust) ===\0A", align 1
@alloc_cfbe37048658dff96b0aa76ee7f1528d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3eaeb57b016d0ebe78fe52aed161235e, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b414a0688b3274030dabac5a381742ad = private unnamed_addr constant [29 x i8] c"Basic arithmetic precedence:\0A", align 1
@alloc_69700bad470cb74ae425ab33d059a06c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b414a0688b3274030dabac5a381742ad, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_942c293f5c46b62f670b4441e7337013 = private unnamed_addr constant [25 x i8] c"14_complex_expressions.rs", align 1
@alloc_da461e915def639f3179374437f40cfd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0A\00\00\00\17\00\00\00" }>, align 8
@alloc_1c2b700c3c681a6c5268f2d06672de11 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0A\00\00\00\13\00\00\00" }>, align 8
@alloc_f003f04333b6bbd88260141286c74976 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0B\00\00\00\13\00\00\00" }>, align 8
@alloc_45740137045ff104de731745d8b5cb7d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0C\00\00\00\13\00\00\00" }>, align 8
@alloc_9b951957568e03c9a0c034c73258a25f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0D\00\00\00\17\00\00\00" }>, align 8
@alloc_4d11f1c0cd122367d6737f4fa1979859 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\0D\00\00\00\13\00\00\00" }>, align 8
@alloc_b2ffa149fc318892a05162e9e4758490 = private unnamed_addr constant [12 x i8] c"2 + 3 * 4 = ", align 1
@alloc_fcae916d41549218b2cf57820b767722 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b2ffa149fc318892a05162e9e4758490, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_167df114222983c5506c2cbacf27fa64 = private unnamed_addr constant [14 x i8] c"(2 + 3) * 4 = ", align 1
@alloc_78badedb11779d552d26e1f114710b54 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_167df114222983c5506c2cbacf27fa64, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aefa5b136165020d8322437ae1cbc66d = private unnamed_addr constant [12 x i8] c"2 * 3 + 4 = ", align 1
@alloc_34caf6c014131a256c30efd32da88240 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aefa5b136165020d8322437ae1cbc66d, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_755daa51b0bf08cec2966f120f70eef2 = private unnamed_addr constant [14 x i8] c"2 * (3 + 4) = ", align 1
@alloc_27b1d958ed7871064c75a2c5daee5698 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_755daa51b0bf08cec2966f120f70eef2, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_16f0ad14413d61a75b5ba82477862a43 = private unnamed_addr constant [34 x i8] c"Complex mathematical expressions:\0A", align 1
@alloc_69adafd4b29da1cdb47a1317bc8a9574 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_16f0ad14413d61a75b5ba82477862a43, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_da49f48b2839e334d1c63462ea1da08e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1C\00\00\00\18\00\00\00" }>, align 8
@alloc_853b9e6f335723815475fad2d137aaf6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1C\00\00\00\14\00\00\00" }>, align 8
@alloc_f9f191c23a2db8c2eb93c7b3db5ca99b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1D\00\00\00\14\00\00\00" }>, align 8
@alloc_c5a472ec5c306e0dbff6508ff1f36c7f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1D\00\00\00\1E\00\00\00" }>, align 8
@alloc_5b4c05f0d6098d73e6d2681d331a50f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1E\00\00\00\14\00\00\00" }>, align 8
@alloc_e11dfcd20d35445ad2a4acce6ae9cc0b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1F\00\00\00\18\00\00\00" }>, align 8
@alloc_540719b2ca7672a7eda3b418f2daae86 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1F\00\00\00\14\00\00\00" }>, align 8
@alloc_7a50ec23b1c273891a51054aad1fad06 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\1F\00\00\00\22\00\00\00" }>, align 8
@alloc_9a0ea554a36753cc5c46d3665723d339 = private unnamed_addr constant [2 x i8] c"a=", align 1
@alloc_1bf56508c9d6ee26fef6c59b492dd8c3 = private unnamed_addr constant [4 x i8] c", b=", align 1
@alloc_aa205d24572557a9555198ce38ff7de8 = private unnamed_addr constant [4 x i8] c", c=", align 1
@alloc_3f8e106cfb04c3ed1d6e0f2e625b5a50 = private unnamed_addr constant [4 x i8] c", d=", align 1
@alloc_8ba0051b8279a8a7ffde3f410f1f5868 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9a0ea554a36753cc5c46d3665723d339, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_1bf56508c9d6ee26fef6c59b492dd8c3, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_aa205d24572557a9555198ce38ff7de8, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3f8e106cfb04c3ed1d6e0f2e625b5a50, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1a1d6f28a42b9f292264b1299770dc81 = private unnamed_addr constant [16 x i8] c"a + b * c - d = ", align 1
@alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf = private unnamed_addr constant [3 x i8] c" + ", align 1
@alloc_26402fef52f74f1c44c90ec1eef0b4f1 = private unnamed_addr constant [3 x i8] c" * ", align 1
@alloc_e2b8ff18cd338c23dd4ea4f16738ab74 = private unnamed_addr constant [3 x i8] c" - ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_95c67c5809decc7e1d926a0710a53fec = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1a1d6f28a42b9f292264b1299770dc81, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_052fb4ff67d0083f6750990f36809df8 = private unnamed_addr constant [21 x i8] c"(a + b) * (c - d) = (", align 1
@alloc_c5edac7c72281b46e2cb84b92f4f2ad8 = private unnamed_addr constant [5 x i8] c") * (", align 1
@alloc_12470cffb0ed9f3cc12c6066ef64d57b = private unnamed_addr constant [4 x i8] c") = ", align 1
@alloc_d7cd4bbd691d2cff323196c56bd09007 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_052fb4ff67d0083f6750990f36809df8, [8 x i8] c"\15\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_c5edac7c72281b46e2cb84b92f4f2ad8, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_25e89faae6984aa6fec1aba2df141e11 = private unnamed_addr constant [16 x i8] c"a * b / c + d = ", align 1
@alloc_37d018637e55331d5bf3277fd5414bcf = private unnamed_addr constant [3 x i8] c" / ", align 1
@alloc_c1db895016117a469a4f7713d8b14039 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_25e89faae6984aa6fec1aba2df141e11, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_37d018637e55331d5bf3277fd5414bcf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7704cdb7b949c9faed3fb51ef27df936 = private unnamed_addr constant [22 x i8] c"a / (b - c) + d * c = ", align 1
@alloc_dbe3a42ed0d94b623b5157649fd8da2e = private unnamed_addr constant [4 x i8] c" / (", align 1
@alloc_36bd765a9846d98e737fdf4276577bf6 = private unnamed_addr constant [4 x i8] c") + ", align 1
@alloc_be173c8d8eeb756f356db6f4bd8220a7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7704cdb7b949c9faed3fb51ef27df936, [8 x i8] c"\16\00\00\00\00\00\00\00", ptr @alloc_dbe3a42ed0d94b623b5157649fd8da2e, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_36bd765a9846d98e737fdf4276577bf6, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_62bf8622b96b9429b211b7ebd3ccb05c = private unnamed_addr constant [37 x i8] c"Boolean expressions with precedence:\0A", align 1
@alloc_e3e2326bce536aaf42e973228f89a1d6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_62bf8622b96b9429b211b7ebd3ccb05c, [8 x i8] c"%\00\00\00\00\00\00\00" }>, align 8
@alloc_00b1c969fb21a4bf72eb45ccfaf4da80 = private unnamed_addr constant [2 x i8] c"x=", align 1
@alloc_c0df973d7281575407de899f408c0f71 = private unnamed_addr constant [4 x i8] c", y=", align 1
@alloc_c18abd7517427ee900c099807587b1ed = private unnamed_addr constant [4 x i8] c", z=", align 1
@alloc_98b76634a7071f83ab1aba9c37ddc89a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_00b1c969fb21a4bf72eb45ccfaf4da80, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_c0df973d7281575407de899f408c0f71, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_c18abd7517427ee900c099807587b1ed, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_020e21f9f1ac25a398f549ec0bc2c986 = private unnamed_addr constant [14 x i8] c"x && y || z = ", align 1
@alloc_3508616d08d0c9d6c53674a47e0a11a8 = private unnamed_addr constant [4 x i8] c" && ", align 1
@alloc_70732e8f0eee82e298b406e43041a5f7 = private unnamed_addr constant [4 x i8] c" || ", align 1
@alloc_40c0cd29fbd31f4ca2281b56c2fb5201 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_020e21f9f1ac25a398f549ec0bc2c986, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_3508616d08d0c9d6c53674a47e0a11a8, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_70732e8f0eee82e298b406e43041a5f7, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_27bf587079dc67ce08fee610c3ccb91d = private unnamed_addr constant [16 x i8] c"x && (y || z) = ", align 1
@alloc_de31f20192fdf24ddf734bb27a10615b = private unnamed_addr constant [5 x i8] c" && (", align 1
@alloc_d5e09c07df792f89d3220e63ad1bfd89 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_27bf587079dc67ce08fee610c3ccb91d, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_de31f20192fdf24ddf734bb27a10615b, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_70732e8f0eee82e298b406e43041a5f7, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_82b857d1c7fc82d180cbbadc18b4e72e = private unnamed_addr constant [17 x i8] c"(x && y) || z = (", align 1
@alloc_783e22c1d77324054bbf8207582f93e2 = private unnamed_addr constant [5 x i8] c") || ", align 1
@alloc_cb11c86507dfa13fc420573fdc06d10e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_82b857d1c7fc82d180cbbadc18b4e72e, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_3508616d08d0c9d6c53674a47e0a11a8, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_783e22c1d77324054bbf8207582f93e2, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_36771bb3b554a588bcfb7c93803e22ac = private unnamed_addr constant [16 x i8] c"!x || y && z = !", align 1
@alloc_01142cf538361b6585bdfff4178eb242 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_36771bb3b554a588bcfb7c93803e22ac, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_70732e8f0eee82e298b406e43041a5f7, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3508616d08d0c9d6c53674a47e0a11a8, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8fa47487f297a8aede5cb9719d419d79 = private unnamed_addr constant [19 x i8] c"!(x || y) && z = !(", align 1
@alloc_58db26239248b1161b1f7ccebd060996 = private unnamed_addr constant [5 x i8] c") && ", align 1
@alloc_6cd0f7c45522d61c8d7a0f1b65e5a7a2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8fa47487f297a8aede5cb9719d419d79, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_70732e8f0eee82e298b406e43041a5f7, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_58db26239248b1161b1f7ccebd060996, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b35fd8a5354b21f50abf4851d88aff04 = private unnamed_addr constant [24 x i8] c"Comparison expressions:\0A", align 1
@alloc_be172a90f574601b871136f96cb2d4ea = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b35fd8a5354b21f50abf4851d88aff04, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_f7e2f65d0d968026a00ba388d1be650d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00D\00\00\00\11\00\00\00" }>, align 8
@alloc_d38a491bfb7f2fe696093c2673f5c703 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00E\00\00\00\11\00\00\00" }>, align 8
@alloc_69593917f132cc32b38ddbba32f4932b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00E\00\00\00\1D\00\00\00" }>, align 8
@alloc_38b86ae79401dad871240e0d906b71b9 = private unnamed_addr constant [5 x i8] c"num1=", align 1
@alloc_ab4356670422181543789ba3d48d6644 = private unnamed_addr constant [7 x i8] c", num2=", align 1
@alloc_a1b5a25b1b3df853168809f17925b608 = private unnamed_addr constant [7 x i8] c", num3=", align 1
@alloc_b24baafdee1c304485ca24c9cd903b20 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_38b86ae79401dad871240e0d906b71b9, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_ab4356670422181543789ba3d48d6644, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_a1b5a25b1b3df853168809f17925b608, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f6540159fe5fde672097e2e774e54178 = private unnamed_addr constant [29 x i8] c"num1 > num2 && num1 < num3 = ", align 1
@alloc_a1fc54a3d1c1b0e65200d44550a0c47c = private unnamed_addr constant [3 x i8] c" > ", align 1
@alloc_485aece92251293aba303622a598e846 = private unnamed_addr constant [3 x i8] c" < ", align 1
@alloc_4326605021b0032e745a4be032926eae = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f6540159fe5fde672097e2e774e54178, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_a1fc54a3d1c1b0e65200d44550a0c47c, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_3508616d08d0c9d6c53674a47e0a11a8, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_485aece92251293aba303622a598e846, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e1e2c361d89ba973570262b71ec39939 = private unnamed_addr constant [31 x i8] c"num1 >= num2 || num1 <= num3 = ", align 1
@alloc_3d5f084549f1b6ffc93d5cdb66afa2e4 = private unnamed_addr constant [4 x i8] c" >= ", align 1
@alloc_0d8f95680cb954489259f79349b0e35b = private unnamed_addr constant [4 x i8] c" <= ", align 1
@alloc_37d01627bbe604c06144acf1af098375 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e1e2c361d89ba973570262b71ec39939, [8 x i8] c"\1F\00\00\00\00\00\00\00", ptr @alloc_3d5f084549f1b6ffc93d5cdb66afa2e4, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_70732e8f0eee82e298b406e43041a5f7, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0d8f95680cb954489259f79349b0e35b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_53cc2044271356e2378504567fe152e1 = private unnamed_addr constant [24 x i8] c"(num1 + num2) > num3 = (", align 1
@alloc_7a390a14d59db7178f326f3c34196e0c = private unnamed_addr constant [4 x i8] c") > ", align 1
@alloc_835288a1bcccc5727d3c0ddb6128a73c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_53cc2044271356e2378504567fe152e1, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_7a390a14d59db7178f326f3c34196e0c, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_db9ddd2ce941f477cf14e3354bbe1740 = private unnamed_addr constant [26 x i8] c"num1 * 2 == num3 + num2 = ", align 1
@alloc_bb756e223dd575425fac9aba0205f584 = private unnamed_addr constant [8 x i8] c" * 2 == ", align 1
@alloc_98850fb03a01b1acab2669d692bb446b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_db9ddd2ce941f477cf14e3354bbe1740, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_bb756e223dd575425fac9aba0205f584, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5cdf428c1f420ffa2a4d9bfb80774999 = private unnamed_addr constant [23 x i8] c"Nested function calls:\0A", align 1
@alloc_8d54751145604d7e5f90749bfc9193bf = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5cdf428c1f420ffa2a4d9bfb80774999, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_03d15edacaaf775e0c10fc3519c6973f = private unnamed_addr constant [43 x i8] c"add(multiply(3, 4), subtract(10, 5)) = add(", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_a863b5029b656c35414ce8ca23cbce15 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_03d15edacaaf775e0c10fc3519c6973f, [8 x i8] c"+\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a773360f3af6e6bd529b6e2a114544f7 = private unnamed_addr constant [47 x i8] c"multiply(add(2, 3), subtract(8, 3)) = multiply(", align 1
@alloc_6f1ba981de6bcac26a12e82736dac95a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a773360f3af6e6bd529b6e2a114544f7, [8 x i8] c"/\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_15ecd1f40bd68a8bc0d7ab78ed1e0f94 = private unnamed_addr constant [58 x i8] c"divide(add(multiply(4, 5), 10), subtract(15, 5)) = divide(", align 1
@alloc_8490394ebf2b0ec542e3c0fd4c759000 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_15ecd1f40bd68a8bc0d7ab78ed1e0f94, [8 x i8] c":\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7697e76c12a4e9ac57a194979f197af3 = private unnamed_addr constant [19 x i8] c"Array expressions:\0A", align 1
@alloc_a33870c54c442141aee5f80dcd43c46c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7697e76c12a4e9ac57a194979f197af3, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_1d7d9a2d7a456e2660a2c383314c41fd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00^\00\00\00\17\00\00\00" }>, align 8
@alloc_983e72a3fd8c8973079ab609ec1da3b1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00_\00\00\00\17\00\00\00" }>, align 8
@alloc_146f499f10a72cb30a4745641f82e622 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00`\00\00\00\17\00\00\00" }>, align 8
@alloc_d064960ba72f2810f8cd42e3020ef372 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00`\00\00\00-\00\00\00" }>, align 8
@alloc_71bbba8a53a05973e95895ded9dc987d = private unnamed_addr constant [20 x i8] c"arr1[0] + arr1[4] = ", align 1
@alloc_04f87af1904a532fcdd937820d028c5d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_71bbba8a53a05973e95895ded9dc987d, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c2b298e24646b23f3c6d160fed5ad650 = private unnamed_addr constant [30 x i8] c"arr1[1] * arr2[0] / arr1[2] = ", align 1
@alloc_1ebf4633a2d7ab9acf0ac44a3d7d13e6 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c2b298e24646b23f3c6d160fed5ad650, [8 x i8] c"\1E\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_37d018637e55331d5bf3277fd5414bcf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d2bb83f12086660041600117b0265076 = private unnamed_addr constant [45 x i8] c"(arr1[0] + arr1[1]) * (arr2[1] - arr2[0]) = (", align 1
@alloc_3f4b7259998fe1b24df51e4dcc5d6883 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d2bb83f12086660041600117b0265076, [8 x i8] c"-\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_c5edac7c72281b46e2cb84b92f4f2ad8, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_915ceec618337f4d0deab5e72a72d01a = private unnamed_addr constant [20 x i8] c"String expressions:\0A", align 1
@alloc_d071d3bea5240355f1714484fe5d0e46 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_915ceec618337f4d0deab5e72a72d01a, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_3edef0b68cfa9c8c95e6d4fe1a68842b = private unnamed_addr constant [5 x i8] c"Hello", align 1
@alloc_7c36acd6e5096800e8610a1984ba6ddd = private unnamed_addr constant [5 x i8] c"World", align 1
@alloc_ebacd5270fb181ba2143a73d4427458e = private unnamed_addr constant [1 x i8] c"!", align 1
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_59f946dc1e3b59f4bf03b40f89f6c3a5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer }>, align 8
@alloc_4259d2f26910294ca303b2cfb05a622d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_435ef7ef85a480a6b521c76a63dea850 = private unnamed_addr constant [16 x i8] c"Concatenation: '", align 1
@alloc_0ae731365013fc00518aae99e353cb81 = private unnamed_addr constant [11 x i8] c"' + ' ' + '", align 1
@alloc_52e02ef35d8cbd87a274e144e583f20b = private unnamed_addr constant [5 x i8] c"' + '", align 1
@alloc_965e31386ecabf34c821a81a66b7503d = private unnamed_addr constant [5 x i8] c"' = '", align 1
@alloc_12a9d76f5dbcbafc68e14c1df740ed24 = private unnamed_addr constant [2 x i8] c"'\0A", align 1
@alloc_9da98b58f4fa525bc5833722a031efb5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_435ef7ef85a480a6b521c76a63dea850, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_0ae731365013fc00518aae99e353cb81, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_52e02ef35d8cbd87a274e144e583f20b, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_965e31386ecabf34c821a81a66b7503d, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_78fc7aac3a9ef8275ccf8ea262349782 = private unnamed_addr constant [65 x i8] c"With case changes: 'str1.upper() + ' ' + str2.lower() + str3' = '", align 1
@alloc_265b08344f34466dc1fbf2c86068ba5c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_78fc7aac3a9ef8275ccf8ea262349782, [8 x i8] c"A\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_3d53958902e46de51e0792a6a53e246e = private unnamed_addr constant [25 x i8] c"Conditional expressions:\0A", align 1
@alloc_ea9fd32ba7a8d3e547ba47f42b4dc55a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3d53958902e46de51e0792a6a53e246e, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_e57470275a219d8492d489e56910499e = private unnamed_addr constant [1 x i8] c"C", align 1
@alloc_d3bbdebcd7d668a59dc59a90afdc2fa1 = private unnamed_addr constant [1 x i8] c"B", align 1
@alloc_e2ead6761956d440a2a6c3412b417ffa = private unnamed_addr constant [1 x i8] c"A", align 1
@alloc_dabdbfe0e9e5b3ed7cf46e94448ac36a = private unnamed_addr constant [4 x i8] c"Fail", align 1
@alloc_5da6b24bde949a094e0a3627f43ac929 = private unnamed_addr constant [4 x i8] c"Pass", align 1
@alloc_2e2f2e05479b7f96f9e0ace3eeea3d1d = private unnamed_addr constant [7 x i8] c"Score: ", align 1
@alloc_1cf0cfa27d966b793d90aa29deba86f7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2e2f2e05479b7f96f9e0ace3eeea3d1d, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8da1f7b97d1cc302c3fcee02febbc08e = private unnamed_addr constant [7 x i8] c"Grade: ", align 1
@alloc_e090ef3856952b80502bcd1249ebe4ce = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8da1f7b97d1cc302c3fcee02febbc08e, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_256738020a264961f7778b77b749e2d7 = private unnamed_addr constant [8 x i8] c"Status: ", align 1
@alloc_cc40b1d2a73d98ed884c1e7c1a6ae7b4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_256738020a264961f7778b77b749e2d7, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_18f507983d5f04faa14a76d7c9046079 = private unnamed_addr constant [7 x i8] c"Bonus: ", align 1
@alloc_bf3ee10a05411f9d03dc0127aaafb64b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_18f507983d5f04faa14a76d7c9046079, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ad9203ee9cf67d15cf6cb9cd17013edd = private unnamed_addr constant [33 x i8] c"Complex conditional expressions:\0A", align 1
@alloc_bee085d3de4108e166b431475a32c47c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ad9203ee9cf67d15cf6cb9cd17013edd, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_aa11b714384947dbd3caef261ef0dc2e = private unnamed_addr constant [5 x i8] c"Age: ", align 1
@alloc_77d7aa2e49b700badee8a6e65ecb8a46 = private unnamed_addr constant [10 x i8] c", Income: ", align 1
@alloc_f5312c252ac5ab730aba0113abe2e708 = private unnamed_addr constant [11 x i8] c", Has job: ", align 1
@alloc_11124f97728cfabdcfad1b66b0915012 = private unnamed_addr constant [16 x i8] c", Credit score: ", align 1
@alloc_6434d4002f1a18ec86e3fa2104286004 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aa11b714384947dbd3caef261ef0dc2e, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_77d7aa2e49b700badee8a6e65ecb8a46, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_f5312c252ac5ab730aba0113abe2e708, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_11124f97728cfabdcfad1b66b0915012, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3e1d145153340b75d64676a1d4e1d39b = private unnamed_addr constant [15 x i8] c"Loan eligible: ", align 1
@alloc_69256651a60b76d1b35eadc7c19013bb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3e1d145153340b75d64676a1d4e1d39b, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_91071adc474510031c74e5a80548a1d5 = private unnamed_addr constant [13 x i8] c"Loan amount: ", align 1
@alloc_470dbb0e005c3e0560292550f0ea928a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_91071adc474510031c74e5a80548a1d5, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f3657e1898f5dd7c9af51b9f00da25b0 = private unnamed_addr constant [23 x i8] c"Mathematical formulas:\0A", align 1
@alloc_a2e8401817c1d8434e64a0fe0ec9ee12 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f3657e1898f5dd7c9af51b9f00da25b0, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_ecfbf5a299a01a61c98d8657cdc316eb = private unnamed_addr constant [8 x i8] c"Radius: ", align 1
@alloc_6dd5acfe24498811cd932b648df9cbd7 = private unnamed_addr constant [10 x i8] c", Height: ", align 1
@alloc_72122d5ce80b5c28f4be157be7ce4956 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ecfbf5a299a01a61c98d8657cdc316eb, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_6dd5acfe24498811cd932b648df9cbd7, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_81009029a95b0a659f3206f5b0e18f4e = private unnamed_addr constant [24 x i8] c"Circle area (\CF\80 * r\C2\B2): ", align 1
@alloc_b54d2dc93c1d4b63ced5269c53b74188 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_81009029a95b0a659f3206f5b0e18f4e, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b0f3950b7800dd153c1898f88a85e6ba = private unnamed_addr constant [32 x i8] c"Cylinder volume (\CF\80 * r\C2\B2 * h): ", align 1
@alloc_c9b86847efaf5d099fbb5727db1cb872 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b0f3950b7800dd153c1898f88a85e6ba, [8 x i8] c" \00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d7e5597613eec95694772cca334834ad = private unnamed_addr constant [34 x i8] c"Sphere volume ((4/3) * \CF\80 * r\C2\B3): ", align 1
@alloc_7acc382335ac0a23c1b61f811ecb5958 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d7e5597613eec95694772cca334834ad, [8 x i8] c"\22\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c43628fdc3eecf938786e321b8db9f6c = private unnamed_addr constant [27 x i8] c"Compound interest formula:\0A", align 1
@alloc_765455df4fbb4e4a465c71f3f38f84f9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c43628fdc3eecf938786e321b8db9f6c, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_40309ba347e1180eabebc4062dce573d = private unnamed_addr constant [12 x i8] c"Principal: $", align 1
@alloc_f3605924ed5959fc81c8e45edc7a92e7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_40309ba347e1180eabebc4062dce573d, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_55c384264ea9014f968616b028a5d8da = private unnamed_addr constant [6 x i8] c"Rate: ", align 1
@alloc_1c66b5fb2c6e357af3588db0097b572a = private unnamed_addr constant [2 x i8] c"%\0A", align 1
@alloc_280cc4bd13691fcf8685661ccf810602 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_55c384264ea9014f968616b028a5d8da, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_1c66b5fb2c6e357af3588db0097b572a, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_d9ebe39abeab7db2c3999bcb5baaf7b1 = private unnamed_addr constant [6 x i8] c"Time: ", align 1
@alloc_f1090a81acfc81b45c6dc1b4df6e1c0d = private unnamed_addr constant [7 x i8] c" years\0A", align 1
@alloc_ab67f7fe1291fe0d5abc97efdf942547 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d9ebe39abeab7db2c3999bcb5baaf7b1, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_f1090a81acfc81b45c6dc1b4df6e1c0d, [8 x i8] c"\07\00\00\00\00\00\00\00" }>, align 8
@alloc_981fd6834fdbab37fbd120054a3c628e = private unnamed_addr constant [13 x i8] c"Compounding: ", align 1
@alloc_570487da0c8216cadf4e1efc65a51422 = private unnamed_addr constant [16 x i8] c" times per year\0A", align 1
@alloc_088e36290ee483694f96bff57a5c27fa = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_981fd6834fdbab37fbd120054a3c628e, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_570487da0c8216cadf4e1efc65a51422, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_0c3f0dfabac36e3e1688ebb4015acc1f = private unnamed_addr constant [15 x i8] c"Final amount: $", align 1
@alloc_484598a9a2d17a346ada0a4a52f37371 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0c3f0dfabac36e3e1688ebb4015acc1f, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5e4340ef2b23b6a51d08b67f0de8da21 = private unnamed_addr constant [18 x i8] c"Interest earned: $", align 1
@alloc_024462f0deb68215125012e9bae6f974 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5e4340ef2b23b6a51d08b67f0de8da21, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b585fb1c45ade6f52535ac2fa3f0b7db = private unnamed_addr constant [19 x i8] c"Quadratic formula:\0A", align 1
@alloc_a75b41b299785b6ffb1ed77c12554cb8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b585fb1c45ade6f52535ac2fa3f0b7db, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_67c162ea211b45c2817b4470d5dddaeb = private unnamed_addr constant [30 x i8] c"No real roots (discriminant = ", align 1
@alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b = private unnamed_addr constant [2 x i8] c")\0A", align 1
@alloc_caa20d8d2d1a386884338e46f7c06f22 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_67c162ea211b45c2817b4470d5dddaeb, [8 x i8] c"\1E\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_dee6535fb31564d8c9e927fcb80a9f87 = private unnamed_addr constant [10 x i8] c"Equation: ", align 1
@alloc_5acb84dc9f4d65f2092de6d54f8d7f73 = private unnamed_addr constant [6 x i8] c"x\C2\B2 + ", align 1
@alloc_f47ed691093b2f9f55ece2e4e11fb5c2 = private unnamed_addr constant [4 x i8] c"x + ", align 1
@alloc_3ea3a59d70b58578e5c3675f52f2d3bb = private unnamed_addr constant [5 x i8] c" = 0\0A", align 1
@alloc_773bcb544d5517a5957bf6f9b6e2778b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_dee6535fb31564d8c9e927fcb80a9f87, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_5acb84dc9f4d65f2092de6d54f8d7f73, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_f47ed691093b2f9f55ece2e4e11fb5c2, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3ea3a59d70b58578e5c3675f52f2d3bb, [8 x i8] c"\05\00\00\00\00\00\00\00" }>, align 8
@alloc_3b5ea8846bc13acb407bded9d1cc067e = private unnamed_addr constant [14 x i8] c"Discriminant: ", align 1
@alloc_1abd9def04fe91dc3f020712e62c09f0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3b5ea8846bc13acb407bded9d1cc067e, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_994608aecaf0b2b88c00c0746c14332a = private unnamed_addr constant [8 x i8] c"Root 1: ", align 1
@alloc_46daf9f2f111acb9e5443e1371d31f4a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_994608aecaf0b2b88c00c0746c14332a, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_192ea62e627a49a3b7b216a6881b5785 = private unnamed_addr constant [8 x i8] c"Root 2: ", align 1
@alloc_0f7d3133d3a337687dbde27bdce296f9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_192ea62e627a49a3b7b216a6881b5785, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f2b63ade0e8ef792b722d3524ab53074 = private unnamed_addr constant [26 x i8] c"Complex array operations:\0A", align 1
@alloc_c74fd58df1e47fefedad42253dd34d31 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f2b63ade0e8ef792b722d3524ab53074, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_0d79f432d3df6806890e394de42d7793 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\D9\00\00\00)\00\00\00" }>, align 8
@alloc_1c072410cfb5c78f476795683b7d7dff = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\DA\00\00\00)\00\00\00" }>, align 8
@alloc_490cb4e03a0a74e11e380be5f72eed5c = private unnamed_addr constant [9 x i8] c"Numbers: ", align 1
@alloc_c2ea8befab079717f7857c3672236466 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_490cb4e03a0a74e11e380be5f72eed5c, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5b0102dc258292fd5e83bca8293a8d53 = private unnamed_addr constant [5 x i8] c"Sum: ", align 1
@alloc_fae7b78f3403b48b2870d7d78fcc4226 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5b0102dc258292fd5e83bca8293a8d53, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b1f2c95baf3fd0056321c45a348389c5 = private unnamed_addr constant [9 x i8] c"Product: ", align 1
@alloc_5a4976cd5630db43316aecd3ccde6279 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b1f2c95baf3fd0056321c45a348389c5, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e0410df4b5c730b8f5eb072f94c35732 = private unnamed_addr constant [9 x i8] c"Average: ", align 1
@alloc_e93491e3b40beb615f607f92c37da4a2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e0410df4b5c730b8f5eb072f94c35732, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c4128a4d91a1164e83aa65bff8a5ab8d = private unnamed_addr constant [9 x i8] c"Maximum: ", align 1
@alloc_3fed9fdde7e6ef1b81519ccb53d625ea = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c4128a4d91a1164e83aa65bff8a5ab8d, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1acb7b455fc43f50b6d4723e6ad8b161 = private unnamed_addr constant [9 x i8] c"Minimum: ", align 1
@alloc_7ded8a01a82054e8f58ce74272b5b106 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1acb7b455fc43f50b6d4723e6ad8b161, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_51ac2a8b4aa57ba133e6af4352ae8b59 = private unnamed_addr constant [7 x i8] c"Range: ", align 1
@alloc_7a517152eb3d8bddd47643ba929d2459 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_51ac2a8b4aa57ba133e6af4352ae8b59, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8100a3c8c45e35b9b8b1d6a79802b479 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\E2\00\00\00\1B\00\00\00" }>, align 8
@alloc_f39386b0f5fb1afa83c251b175ea2cf7 = private unnamed_addr constant [40 x i8] c"=== End of Complex Expressions Demo ===\0A", align 1
@alloc_c318ab582d15789fa478dcc21017cef5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f39386b0f5fb1afa83c251b175ea2cf7, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_1c33ef6b15b55eabd3c7a80119b2a86b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\EA\00\00\00\05\00\00\00" }>, align 8
@alloc_882aa4664372adbd6ebabb5a39b3cb21 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\EE\00\00\00\05\00\00\00" }>, align 8
@alloc_7ebbfff03df90df7ab1b39d9ff787f1f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\F2\00\00\00\05\00\00\00" }>, align 8
@alloc_c9d173d9c9fa614c31af073df1b8a79b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_942c293f5c46b62f670b4441e7337013, [16 x i8] c"\19\00\00\00\00\00\00\00\F6\00\00\00\05\00\00\00" }>, align 8

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hc704dace1bbd4a87E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h60c0efea28ac9ddbE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17had7b5ece52770ab5E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h66f766e562a0a6fdE"()
  ret i32 %self
}

; std::f64::<impl f64>::powf
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17hb769435b630f5b5bE"(double %self, double %n) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.pow.f64(double %self, double %n)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::sqrt
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17h7044fc1fbcfbb1b6E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.sqrt.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17had7b5ece52770ab5E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h02d3a7633b6d8673E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h8567fcf10934acabE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::num::<impl core::fmt::Debug for i32>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h3cd98f2ebacd063fE"(ptr align 4 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17heee53ef3f62fc56fE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <[T] as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17hdd374bed88c01c5aE"(ptr align 4 %self.0, i64 %self.1, ptr align 8 %f) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %_5 = alloca [16 x i8], align 8
; call core::fmt::Formatter::debug_list
  call void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8 %_5, ptr align 8 %f)
  br label %bb5

bb5:                                              ; preds = %start
  %_11 = getelementptr inbounds nuw i32, ptr %self.0, i64 %self.1
  store ptr %_11, ptr %end_or_len, align 8
  br label %bb6

bb6:                                              ; preds = %bb5
  %_13 = load ptr, ptr %end_or_len, align 8
; call core::fmt::builders::DebugList::entries
  %_3 = call align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h5337a89281986bbcE(ptr align 8 %_5, ptr %self.0, ptr %_13)
; call core::fmt::builders::DebugList::finish
  %_0 = call zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8 %_3)
  ret i1 %_0

bb4:                                              ; No predecessors!
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17hca83bdb0a75cf813E(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #15
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #15
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h7314554ffedcdc9cE(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #16 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #17
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17he70e4fa1ebaafc1eE() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::impls::<impl core::cmp::Ord for i32>::cmp
; Function Attrs: inlinehint uwtable
define internal i8 @"_ZN4core3cmp5impls48_$LT$impl$u20$core..cmp..Ord$u20$for$u20$i32$GT$3cmp17he90c849b8cfc1c3fE"(ptr align 4 %self, ptr align 4 %other) unnamed_addr #1 {
start:
  %_3 = load i32, ptr %self, align 4
  %_4 = load i32, ptr %other, align 4
  %_0 = call i8 @llvm.scmp.i8.i32(i32 %_3, i32 %_4)
  ret i8 %_0
}

; core::cmp::impls::<impl core::cmp::Ord for &A>::cmp
; Function Attrs: inlinehint uwtable
define internal i8 @"_ZN4core3cmp5impls50_$LT$impl$u20$core..cmp..Ord$u20$for$u20$$RF$A$GT$3cmp17h8b0f8b85faea0266E"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_3 = load ptr, ptr %self, align 8
  %_4 = load ptr, ptr %other, align 8
; call core::cmp::impls::<impl core::cmp::Ord for i32>::cmp
  %_0 = call i8 @"_ZN4core3cmp5impls48_$LT$impl$u20$core..cmp..Ord$u20$for$u20$i32$GT$3cmp17he90c849b8cfc1c3fE"(ptr align 4 %_3, ptr align 4 %_4)
  ret i8 %_0
}

; core::cmp::max_by
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core3cmp6max_by17heaabaa6b43011cfcE(ptr align 4 %0, ptr align 4 %1, ptr align 1 %compare) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_9 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %v2 = alloca [8 x i8], align 8
  %v1 = alloca [8 x i8], align 8
  store ptr %0, ptr %v1, align 8
  store ptr %1, ptr %v2, align 8
  store i8 1, ptr %_9, align 1
; invoke core::ops::function::impls::<impl core::ops::function::FnOnce<A> for &mut F>::call_once
  %self = invoke i8 @"_ZN4core3ops8function5impls80_$LT$impl$u20$core..ops..function..FnOnce$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$9call_once17hee60dc19642d7ddfE"(ptr align 1 %compare, ptr align 8 %v2, ptr align 8 %v1)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  %_4 = icmp slt i8 %self, 0
  br i1 %_4, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load ptr, ptr %v2, align 8
  store ptr %2, ptr %_0, align 8
  %3 = load i8, ptr %_9, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_9, align 1
  %5 = load ptr, ptr %v1, align 8
  store ptr %5, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load ptr, ptr %_0, align 8
  ret ptr %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_9, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::cmp::min_by
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core3cmp6min_by17h65788eb1f74ccd77E(ptr align 4 %0, ptr align 4 %1, ptr align 1 %compare) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_9 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %v2 = alloca [8 x i8], align 8
  %v1 = alloca [8 x i8], align 8
  store ptr %0, ptr %v1, align 8
  store ptr %1, ptr %v2, align 8
  store i8 1, ptr %_9, align 1
; invoke core::ops::function::impls::<impl core::ops::function::FnOnce<A> for &mut F>::call_once
  %self = invoke i8 @"_ZN4core3ops8function5impls80_$LT$impl$u20$core..ops..function..FnOnce$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$9call_once17hee60dc19642d7ddfE"(ptr align 1 %compare, ptr align 8 %v2, ptr align 8 %v1)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  %_4 = icmp slt i8 %self, 0
  br i1 %_4, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  store i8 0, ptr %_9, align 1
  %2 = load ptr, ptr %v1, align 8
  store ptr %2, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb1
  %3 = load ptr, ptr %v2, align 8
  store ptr %3, ptr %_0, align 8
  %4 = load i8, ptr %_9, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb4

bb4:                                              ; preds = %bb7, %bb2, %bb3
  %6 = load ptr, ptr %_0, align 8
  ret ptr %6

bb7:                                              ; preds = %bb2
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_9, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::fmt::rt::Placeholder::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_0, i64 %position, i32 %flags, ptr align 8 %precision, ptr align 8 %width) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 %position, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 40
  store i32 %flags, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %precision, i64 16, i1 false)
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %width, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17haf1723d329d1dc46E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17h23969d6b84d04e65E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17heee53ef3f62fc56fE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_debug
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_debug17hb12e034bd56b9962E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core5array69_$LT$impl$u20$core..fmt..Debug$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$3fmt17hc40106d448c1aaa0E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::UnsafeArg::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE() unnamed_addr #1 {
start:
  ret void
}

; core::fmt::num::<impl core::fmt::Debug for i32>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h3cd98f2ebacd063fE"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = getelementptr inbounds i8, ptr %f, i64 16
  %_4 = load i32, ptr %0, align 8
  %_3 = and i32 %_4, 33554432
  %1 = icmp eq i32 %_3, 0
  br i1 %1, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %f, i64 16
  %_6 = load i32, ptr %2, align 8
  %_5 = and i32 %_6, 67108864
  %3 = icmp eq i32 %_5, 0
  br i1 %3, label %bb4, label %bb3

bb1:                                              ; preds = %start
; call core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
  %4 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4 %self, ptr align 8 %f)
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb6

bb4:                                              ; preds = %bb2
; call core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
  %6 = call zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4 %self, ptr align 8 %f)
  %7 = zext i1 %6 to i8
  store i8 %7, ptr %_0, align 1
  br label %bb5

bb3:                                              ; preds = %bb2
; call core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
  %8 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4 %self, ptr align 8 %f)
  %9 = zext i1 %8 to i8
  store i8 %9, ptr %_0, align 1
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  br label %bb6

bb6:                                              ; preds = %bb1, %bb5
  %10 = load i8, ptr %_0, align 1
  %11 = trunc nuw i8 %10 to i1
  ret i1 %11
}

; core::fmt::builders::DebugList::entries
; Function Attrs: uwtable
define internal align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h5337a89281986bbcE(ptr align 8 %self, ptr %entries.0, ptr %entries.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %entry = alloca [8 x i8], align 8
  %_5 = alloca [8 x i8], align 8
  %iter = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17he1e204b9fc2e9e2eE"(ptr %entries.0, ptr %entries.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
  store ptr %_3.0, ptr %iter, align 8
  %1 = getelementptr inbounds i8, ptr %iter, i64 8
  store ptr %_3.1, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %bb8, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h116380a50168beb2E"(ptr align 8 %iter)
          to label %bb3 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb10, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb3:                                              ; preds = %bb2
  store ptr %2, ptr %_5, align 8
  %3 = load ptr, ptr %_5, align 8
  %4 = ptrtoint ptr %3 to i64
  %5 = icmp eq i64 %4, 0
  %_7 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_7 to i1
  br i1 %6, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %7 = load ptr, ptr %_5, align 8
  store ptr %7, ptr %entry, align 8
; invoke core::fmt::builders::DebugList::entry
  %_9 = invoke align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8 %self, ptr align 1 %entry, ptr align 8 @vtable.1)
          to label %bb7 unwind label %funclet_bb10

bb6:                                              ; preds = %bb3
  ret ptr %self

bb10:                                             ; preds = %funclet_bb10
  cleanupret from %cleanuppad1 unwind label %funclet_bb11

funclet_bb10:                                     ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb10

bb7:                                              ; preds = %bb5
  br label %bb8

bb8:                                              ; preds = %bb7
  br label %bb2

bb4:                                              ; No predecessors!
  unreachable
}

; core::fmt::Arguments::new_v1_formatted
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces.0, i64 %pieces.1, ptr align 8 %args.0, i64 %args.1, ptr align 8 %fmt.0, i64 %fmt.1) unnamed_addr #1 {
start:
  %_5 = alloca [16 x i8], align 8
  store ptr %fmt.0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %fmt.1, ptr %0, align 8
  store ptr %pieces.0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pieces.1, ptr %1, align 8
  %2 = load ptr, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args.0, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  store i64 %args.1, ptr %8, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 5, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 4, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 6, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 5, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h4b9ce4dccea2e245E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h5290eaeab44b7695E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 7, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 6, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h77d88efe42cf4eccE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h07f5ac9e099e41d1E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #17
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17hd9250fba82c2e163E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #17
  unreachable
}

; core::ops::function::FnMut::call_mut
; Function Attrs: inlinehint uwtable
define internal i8 @_ZN4core3ops8function5FnMut8call_mut17h311f874e04c43a96E(ptr align 1 %_1, ptr align 8 %0, ptr align 8 %1) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store ptr %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load ptr, ptr %4, align 8
; call core::cmp::impls::<impl core::cmp::Ord for &A>::cmp
  %_0 = call i8 @"_ZN4core3cmp5impls50_$LT$impl$u20$core..cmp..Ord$u20$for$u20$$RF$A$GT$3cmp17h8b0f8b85faea0266E"(ptr align 8 %3, ptr align 8 %5)
  ret i8 %_0
}

; core::ops::function::impls::<impl core::ops::function::FnOnce<A> for &mut F>::call_once
; Function Attrs: uwtable
define internal i8 @"_ZN4core3ops8function5impls80_$LT$impl$u20$core..ops..function..FnOnce$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$9call_once17hee60dc19642d7ddfE"(ptr align 1 %self, ptr align 8 %0, ptr align 8 %1) unnamed_addr #0 {
start:
  %args = alloca [16 x i8], align 8
  store ptr %0, ptr %args, align 8
  %2 = getelementptr inbounds i8, ptr %args, i64 8
  store ptr %1, ptr %2, align 8
  %3 = load ptr, ptr %args, align 8
  %4 = getelementptr inbounds i8, ptr %args, i64 8
  %5 = load ptr, ptr %4, align 8
; call core::ops::function::FnMut::call_mut
  %_0 = call i8 @_ZN4core3ops8function5FnMut8call_mut17h311f874e04c43a96E(ptr align 1 %self, ptr align 8 %3, ptr align 8 %5)
  ret i8 %_0
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h36b3cab715787970E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h8306567be6f0e601E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h02d3a7633b6d8673E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h8306567be6f0e601E(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h60c0efea28ac9ddbE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h9cca6fd72824ca56E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load i64, ptr %4, align 8
; call alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
  call void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h2c9ea92721af6099E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %3, i64 %5)
  ret void
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17h50d05caaf2862cc0E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #15
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #17
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #16 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<&i32>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr28drop_in_place$LT$$RF$i32$GT$17h421a47cd113d0907E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hb8341908d5bf25b7E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hb8341908d5bf25b7E"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hfb32c01c83e07717E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h582ed23c96bbb4fcE"(ptr align 8 %_1) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h582ed23c96bbb4fcE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h582ed23c96bbb4fcE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h41c6e00d2daf39c5E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h01d138644bcda88fE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h337f0c4025933c15E"(ptr %ptr) unnamed_addr #3 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #17
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17haafb8d247f95b89aE"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hc1a166fa74583b89E"(ptr %self, ptr %subtracted) #19
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 4
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #15
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hc1a166fa74583b89E"(ptr %this, ptr %origin) unnamed_addr #3 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #17
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17h0460fba33ee6d917E(i1 zeroext %cond) unnamed_addr #3 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #17
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::iter::traits::iterator::Iterator::max
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator3max17h24ec3c72f0637502E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
; call core::iter::traits::iterator::Iterator::max_by
  %_0 = call align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6max_by17h9ffc6bf356f0dc77E(ptr %self.0, ptr %self.1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::min
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator3min17h9b56df02fed012a5E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
; call core::iter::traits::iterator::Iterator::min_by
  %_0 = call align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6min_by17h420edb2abaef84f1E(ptr %self.0, ptr %self.1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal i32 @_ZN4core4iter6traits8iterator8Iterator3sum17h1e482a0f11f974bfE(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
; call <i32 as core::iter::traits::accum::Sum<&i32>>::sum
  %_0 = call i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17h66d591f3c6686800E"(ptr %self.0, ptr %self.1)
  ret i32 %_0
}

; core::iter::traits::iterator::Iterator::max_by
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6max_by17h9ffc6bf356f0dc77E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
; call core::iter::traits::iterator::Iterator::reduce
  %_0 = call align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6reduce17h55fc30c56885feedE(ptr %self.0, ptr %self.1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::max_by::fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN4core4iter6traits8iterator8Iterator6max_by4fold28_$u7b$$u7b$closure$u7d$$u7d$17h916893699e4812f5E"(ptr align 1 %_1, ptr align 4 %x, ptr align 4 %y) unnamed_addr #1 {
start:
; call core::cmp::max_by
  %_0 = call align 4 ptr @_ZN4core3cmp6max_by17heaabaa6b43011cfcE(ptr align 4 %x, ptr align 4 %y, ptr align 1 %_1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::min_by
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6min_by17h420edb2abaef84f1E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
; call core::iter::traits::iterator::Iterator::reduce
  %_0 = call align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6reduce17h6ac1added810347bE(ptr %self.0, ptr %self.1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::min_by::fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN4core4iter6traits8iterator8Iterator6min_by4fold28_$u7b$$u7b$closure$u7d$$u7d$17h29e9021d7afb91dfE"(ptr align 1 %_1, ptr align 4 %x, ptr align 4 %y) unnamed_addr #1 {
start:
; call core::cmp::min_by
  %_0 = call align 4 ptr @_ZN4core3cmp6min_by17h65788eb1f74ccd77E(ptr align 4 %x, ptr align 4 %y, ptr align 1 %_1)
  ret ptr %_0
}

; core::iter::traits::iterator::Iterator::reduce
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6reduce17h55fc30c56885feedE(ptr %0, ptr %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_11 = alloca [1 x i8], align 1
  %_10 = alloca [1 x i8], align 1
  %self1 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  store i8 1, ptr %_11, align 1
  store i8 1, ptr %_10, align 1
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %3 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h116380a50168beb2E"(ptr align 8 %self)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %4 = load i8, ptr %_10, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb8_cleanup_trampoline_bb10

funclet_bb8:                                      ; preds = %bb12, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  store ptr %3, ptr %self1, align 8
  %6 = load ptr, ptr %self1, align 8
  %7 = ptrtoint ptr %6 to i64
  %8 = icmp eq i64 %7, 0
  %_12 = select i1 %8, i64 0, i64 1
  %9 = trunc nuw i64 %_12 to i1
  br i1 %9, label %bb12, label %bb11

bb12:                                             ; preds = %bb1
  %v = load ptr, ptr %self1, align 8
  store ptr %v, ptr %_3, align 8
  %first = load ptr, ptr %_3, align 8
  store i8 0, ptr %_11, align 1
  %_8.0 = load ptr, ptr %self, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %_8.1 = load ptr, ptr %10, align 8
  store i8 0, ptr %_10, align 1
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_7 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h0f86adcd8e6bd1c0E"(ptr %_8.0, ptr %_8.1, ptr align 4 %first)
          to label %bb3 unwind label %funclet_bb8

bb11:                                             ; preds = %bb1
  store ptr null, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb11
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %11 = load ptr, ptr %_0, align 8
  ret ptr %11

bb3:                                              ; preds = %bb12
  store ptr %_7, ptr %_0, align 8
  br label %bb5

bb2:                                              ; No predecessors!
  unreachable

bb10:                                             ; preds = %funclet_bb10
  %12 = load i8, ptr %_11, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

funclet_bb10:                                     ; preds = %bb7, %bb8_cleanup_trampoline_bb10
  %cleanuppad2 = cleanuppad within none []
  br label %bb10

bb8_cleanup_trampoline_bb10:                      ; preds = %bb8
  cleanupret from %cleanuppad unwind label %funclet_bb10

bb7:                                              ; preds = %bb8
  cleanupret from %cleanuppad unwind label %funclet_bb10

bb6:                                              ; preds = %bb9, %bb10
  cleanupret from %cleanuppad2 unwind to caller

bb9:                                              ; preds = %bb10
  br label %bb6
}

; core::iter::traits::iterator::Iterator::reduce
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @_ZN4core4iter6traits8iterator8Iterator6reduce17h6ac1added810347bE(ptr %0, ptr %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_11 = alloca [1 x i8], align 1
  %_10 = alloca [1 x i8], align 1
  %self1 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  store i8 1, ptr %_11, align 1
  store i8 1, ptr %_10, align 1
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %3 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h116380a50168beb2E"(ptr align 8 %self)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %4 = load i8, ptr %_10, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb7, label %bb8_cleanup_trampoline_bb10

funclet_bb8:                                      ; preds = %bb12, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  store ptr %3, ptr %self1, align 8
  %6 = load ptr, ptr %self1, align 8
  %7 = ptrtoint ptr %6 to i64
  %8 = icmp eq i64 %7, 0
  %_12 = select i1 %8, i64 0, i64 1
  %9 = trunc nuw i64 %_12 to i1
  br i1 %9, label %bb12, label %bb11

bb12:                                             ; preds = %bb1
  %v = load ptr, ptr %self1, align 8
  store ptr %v, ptr %_3, align 8
  %first = load ptr, ptr %_3, align 8
  store i8 0, ptr %_11, align 1
  %_8.0 = load ptr, ptr %self, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %_8.1 = load ptr, ptr %10, align 8
  store i8 0, ptr %_10, align 1
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_7 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h606cfec6ce5d2cbaE"(ptr %_8.0, ptr %_8.1, ptr align 4 %first)
          to label %bb3 unwind label %funclet_bb8

bb11:                                             ; preds = %bb1
  store ptr null, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb11
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %11 = load ptr, ptr %_0, align 8
  ret ptr %11

bb3:                                              ; preds = %bb12
  store ptr %_7, ptr %_0, align 8
  br label %bb5

bb2:                                              ; No predecessors!
  unreachable

bb10:                                             ; preds = %funclet_bb10
  %12 = load i8, ptr %_11, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

funclet_bb10:                                     ; preds = %bb7, %bb8_cleanup_trampoline_bb10
  %cleanuppad2 = cleanuppad within none []
  br label %bb10

bb8_cleanup_trampoline_bb10:                      ; preds = %bb8
  cleanupret from %cleanuppad unwind label %funclet_bb10

bb7:                                              ; preds = %bb8
  cleanupret from %cleanuppad unwind label %funclet_bb10

bb6:                                              ; preds = %bb9, %bb10
  cleanupret from %cleanuppad2 unwind to caller

bb9:                                              ; preds = %bb10
  br label %bb6
}

; core::iter::traits::iterator::Iterator::product
; Function Attrs: uwtable
define internal i32 @_ZN4core4iter6traits8iterator8Iterator7product17h5bb341e8c1b7cf17E(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
; call <i32 as core::iter::traits::accum::Product<&i32>>::product
  %_0 = call i32 @"_ZN73_$LT$i32$u20$as$u20$core..iter..traits..accum..Product$LT$$RF$i32$GT$$GT$7product17h44b9d93add12f3e9E"(ptr %self.0, ptr %self.1)
  ret i32 %_0
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hb3c01059c96aa1fcE(ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17haa8c9de0a4ff6fe4E(i64 %size, i64 %align) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #16 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #17
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h4f176e955902fb5dE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17haa8c9de0a4ff6fe4E(i64 %new_size, i64 %_23) #19
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hb3c01059c96aa1fcE(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::array::<impl core::fmt::Debug for [T; N]>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN4core5array69_$LT$impl$u20$core..fmt..Debug$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$3fmt17hc40106d448c1aaa0E"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_4 = alloca [16 x i8], align 8
  store ptr %self, ptr %_4, align 8
  %0 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 10, ptr %0, align 8
; call <[T] as core::fmt::Debug>::fmt
  %_0 = call zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17hdd374bed88c01c5aE"(ptr align 4 %self, i64 10, ptr align 8 %f)
  ret i1 %_0
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdea02c331404f766E"(ptr align 4 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hf24cc179f687cd58E"(ptr align 4 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17hb226da900c728e7aE(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #15
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #15
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #16 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #17
  unreachable
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hf24cc179f687cd58E"(ptr align 4 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::option::Option<T>::map_or_else
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hec3f701d4de942faE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 8 %default) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_9 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  store i8 1, ptr %_10, align 1
  store i8 1, ptr %_9, align 1
  %3 = load ptr, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = ptrtoint ptr %3 to i64
  %7 = icmp eq i64 %6, 0
  %_4 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_4 to i1
  br i1 %8, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %t.0 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %9, align 8
  store i8 0, ptr %_9, align 1
; invoke core::ops::function::FnOnce::call_once
  invoke void @_ZN4core3ops8function6FnOnce9call_once17h9cca6fd72824ca56E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %t.0, i64 %t.1)
          to label %bb4 unwind label %funclet_bb11

bb2:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke alloc::fmt::format::{{closure}}
  invoke void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h8b3df49004cde245E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %default)
          to label %bb5 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %10 = load i8, ptr %_9, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb10, label %bb11_cleanup_trampoline_bb7

funclet_bb11:                                     ; preds = %bb3, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb2
  br label %bb6

bb6:                                              ; preds = %bb9, %bb4, %bb5
  ret void

bb4:                                              ; preds = %bb3
  %12 = load i8, ptr %_10, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

bb9:                                              ; preds = %bb4
  br label %bb6

bb7:                                              ; preds = %funclet_bb7
  %14 = load i8, ptr %_10, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb12, label %bb8

funclet_bb7:                                      ; preds = %bb10, %bb11_cleanup_trampoline_bb7
  %cleanuppad1 = cleanuppad within none []
  br label %bb7

bb11_cleanup_trampoline_bb7:                      ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb10:                                             ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb8:                                              ; preds = %bb12, %bb7
  cleanupret from %cleanuppad1 unwind to caller

bb12:                                             ; preds = %bb7
  br label %bb8

bb1:                                              ; No predecessors!
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h7314554ffedcdc9cE(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #1 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #17
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h66f766e562a0a6fdE"() unnamed_addr #1 {
start:
  ret i32 0
}

; alloc::fmt::format
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc3fmt6format17hfa9b33a30e7e0548E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %args) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  %_6.0 = load ptr, ptr %args, align 8
  %0 = getelementptr inbounds i8, ptr %args, i64 8
  %_6.1 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %args, i64 16
  %_7.0 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7.1 = load i64, ptr %2, align 8
  %3 = icmp eq i64 %_6.1, 0
  br i1 %3, label %bb4, label %bb5

bb4:                                              ; preds = %start
  %4 = icmp eq i64 %_7.1, 0
  br i1 %4, label %bb8, label %bb3

bb5:                                              ; preds = %start
  %5 = icmp eq i64 %_6.1, 1
  br i1 %5, label %bb6, label %bb3

bb8:                                              ; preds = %bb4
  store ptr inttoptr (i64 1 to ptr), ptr %_2, align 8
  %6 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 0, ptr %6, align 8
  br label %bb2

bb3:                                              ; preds = %bb6, %bb5, %bb4
  %7 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %8 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  store ptr %7, ptr %_2, align 8
  %9 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb2

bb2:                                              ; preds = %bb3, %bb7, %bb8
  %10 = load ptr, ptr %_2, align 8
  %11 = getelementptr inbounds i8, ptr %_2, i64 8
  %12 = load i64, ptr %11, align 8
; call core::option::Option<T>::map_or_else
  call void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hec3f701d4de942faE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %10, i64 %12, ptr align 8 %args)
  ret void

bb6:                                              ; preds = %bb5
  %13 = icmp eq i64 %_7.1, 0
  br i1 %13, label %bb7, label %bb3

bb7:                                              ; preds = %bb6
  %s = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %14 = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %_13.0 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  %_13.1 = load i64, ptr %15, align 8
  store ptr %_13.0, ptr %_2, align 8
  %16 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %_13.1, ptr %16, align 8
  br label %bb2
}

; alloc::fmt::format::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h8b3df49004cde245E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_1) unnamed_addr #1 {
start:
  %_2 = alloca [48 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %_1, i64 48, i1 false)
; call alloc::fmt::format::format_inner
  call void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_2)
  ret void
}

; alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h2c9ea92721af6099E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17hd8bcfdf7de73c3e0E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17hd1c7ba34b31b2dccE(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h50d05caaf2862cc0E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #19
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #19
  ret ptr %_0
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h6534e4dde6bddad7E(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h50d05caaf2862cc0E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #19
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #19
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc217196682d3b609E(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h337f0c4025933c15E"(ptr %_23) #19
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h6534e4dde6bddad7E(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17hd1c7ba34b31b2dccE(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h337f0c4025933c15E"(ptr %ptr) #19
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h337f0c4025933c15E"(ptr %ptr4) #19
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17hbe1da165f503ab2fE"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hca21c4e9f3d4a152E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17ha847717afd743f99E"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hca21c4e9f3d4a152E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17hd9250fba82c2e163E"(i64 %self1, i64 %self3) #19
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17haa8c9de0a4ff6fe4E(i64 %8, i64 %9) #19
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h3f600872fe41d570E"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h4f176e955902fb5dE(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.2672cf45e15a2962278e8aadee460ad1.0, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h9e52d11983038e65E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h8b0f9e3ca5518050E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17hd278535d2e6a757eE"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h3f600872fe41d570E"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #15
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17h0460fba33ee6d917E(i1 zeroext %cond) #19
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17h23969d6b84d04e65E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17hb226da900c728e7aE(ptr %_8, i64 1, i64 1, i64 %len) #19
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17he1e204b9fc2e9e2eE"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17ha847717afd743f99E"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #19
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h9e52d11983038e65E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc217196682d3b609E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h8b0f9e3ca5518050E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hc217196682d3b609E(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum
; Function Attrs: uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17h66d591f3c6686800E"(ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h21f1d51fd85b4527E"(ptr %iter.0, ptr %iter.1, i32 0)
  ret i32 %_0
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17he2ffe3ff464f51b3E"(ptr align 1 %_1, i32 %a, ptr align 4 %b) unnamed_addr #1 {
start:
  %other = load i32, ptr %b, align 4
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %a, i32 %other)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_5.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2ff564f83739a041825038989c62f69d) #15
  unreachable
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hfb32c01c83e07717E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <i32 as core::iter::traits::accum::Product<&i32>>::product
; Function Attrs: uwtable
define internal i32 @"_ZN73_$LT$i32$u20$as$u20$core..iter..traits..accum..Product$LT$$RF$i32$GT$$GT$7product17h44b9d93add12f3e9E"(ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17hba110978e953e789E"(ptr %iter.0, ptr %iter.1, i32 1)
  ret i32 %_0
}

; <i32 as core::iter::traits::accum::Product<&i32>>::product::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN73_$LT$i32$u20$as$u20$core..iter..traits..accum..Product$LT$$RF$i32$GT$$GT$7product28_$u7b$$u7b$closure$u7d$$u7d$17hdb63be67cc29e50fE"(ptr align 1 %_1, i32 %a, ptr align 4 %b) unnamed_addr #1 {
start:
  %other = load i32, ptr %b, align 4
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %a, i32 %other)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_5.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_2ff564f83739a041825038989c62f69d) #15
  unreachable
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h41c6e00d2daf39c5E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17hbe1da165f503ab2fE"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17hd8bcfdf7de73c3e0E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #1 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17hd278535d2e6a757eE"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17hca83bdb0a75cf813E(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #19
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h0f86adcd8e6bd1c0E"(ptr %0, ptr %1, ptr align 4 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store ptr %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store ptr %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17haafb8d247f95b89aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load ptr, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke core::iter::traits::iterator::Iterator::max_by::fold::{{closure}}
  %_19 = invoke align 4 ptr @"_ZN4core4iter6traits8iterator8Iterator6max_by4fold28_$u7b$$u7b$closure$u7d$$u7d$17h916893699e4812f5E"(ptr align 1 %f, ptr align 4 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store ptr %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h07f5ac9e099e41d1E"(i64 %self2, i64 1) #19
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load ptr, ptr %acc, align 8
  store ptr %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load ptr, ptr %_0, align 8
  ret ptr %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h21f1d51fd85b4527E"(ptr %0, ptr %1, i32 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [4 x i8], align 4
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [4 x i8], align 4
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i32 %init, ptr %acc, align 4
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i32 %init, ptr %_0, align 4
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17haafb8d247f95b89aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load i32, ptr %acc, align 4
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
  %_19 = invoke i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17he2ffe3ff464f51b3E"(ptr align 1 %f, i32 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store i32 %_19, ptr %acc, align 4
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h07f5ac9e099e41d1E"(i64 %self2, i64 1) #19
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load i32, ptr %acc, align 4
  store i32 %12, ptr %_0, align 4
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load i32, ptr %_0, align 4
  ret i32 %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h606cfec6ce5d2cbaE"(ptr %0, ptr %1, ptr align 4 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store ptr %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store ptr %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17haafb8d247f95b89aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load ptr, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke core::iter::traits::iterator::Iterator::min_by::fold::{{closure}}
  %_19 = invoke align 4 ptr @"_ZN4core4iter6traits8iterator8Iterator6min_by4fold28_$u7b$$u7b$closure$u7d$$u7d$17h29e9021d7afb91dfE"(ptr align 1 %f, ptr align 4 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store ptr %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h07f5ac9e099e41d1E"(i64 %self2, i64 1) #19
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load ptr, ptr %acc, align 8
  store ptr %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load ptr, ptr %_0, align 8
  ret ptr %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17hba110978e953e789E"(ptr %0, ptr %1, i32 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [4 x i8], align 4
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [4 x i8], align 4
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i32 %init, ptr %acc, align 4
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i32 %init, ptr %_0, align 4
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17haafb8d247f95b89aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load i32, ptr %acc, align 4
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke <i32 as core::iter::traits::accum::Product<&i32>>::product::{{closure}}
  %_19 = invoke i32 @"_ZN73_$LT$i32$u20$as$u20$core..iter..traits..accum..Product$LT$$RF$i32$GT$$GT$7product28_$u7b$$u7b$closure$u7d$$u7d$17hdb63be67cc29e50fE"(ptr align 1 %f, i32 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store i32 %_19, ptr %acc, align 4
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h07f5ac9e099e41d1E"(i64 %self2, i64 1) #19
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load i32, ptr %acc, align 4
  store i32 %12, ptr %_0, align 4
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load i32, ptr %_0, align 4
  ret i32 %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h116380a50168beb2E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i32, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; _14_complex_expressions::main
; Function Attrs: uwtable
define internal void @_ZN23_14_complex_expressions4main17h33b999dc8a25de6bE() unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self.i40 = alloca [8 x i8], align 8
  %self.i = alloca [8 x i8], align 8
  %_1040 = alloca [48 x i8], align 8
  %_1037 = alloca [48 x i8], align 8
  %_1034 = alloca [4 x i8], align 4
  %_1032 = alloca [16 x i8], align 8
  %_1031 = alloca [16 x i8], align 8
  %_1028 = alloca [48 x i8], align 8
  %_1025 = alloca [16 x i8], align 8
  %_1024 = alloca [16 x i8], align 8
  %_1021 = alloca [48 x i8], align 8
  %_1018 = alloca [16 x i8], align 8
  %_1017 = alloca [16 x i8], align 8
  %_1014 = alloca [48 x i8], align 8
  %_1011 = alloca [16 x i8], align 8
  %_1010 = alloca [16 x i8], align 8
  %_1009 = alloca [48 x i8], align 8
  %_1008 = alloca [48 x i8], align 8
  %_1004 = alloca [16 x i8], align 8
  %_1003 = alloca [16 x i8], align 8
  %_998 = alloca [48 x i8], align 8
  %_995 = alloca [16 x i8], align 8
  %_994 = alloca [16 x i8], align 8
  %_991 = alloca [48 x i8], align 8
  %_988 = alloca [16 x i8], align 8
  %_987 = alloca [16 x i8], align 8
  %_984 = alloca [48 x i8], align 8
  %_981 = alloca [16 x i8], align 8
  %_980 = alloca [16 x i8], align 8
  %_977 = alloca [48 x i8], align 8
  %min_val = alloca [4 x i8], align 4
  %max_val = alloca [4 x i8], align 4
  %average = alloca [8 x i8], align 8
  %product = alloca [4 x i8], align 4
  %sum = alloca [4 x i8], align 4
  %numbers = alloca [40 x i8], align 4
  %_947 = alloca [48 x i8], align 8
  %_944 = alloca [48 x i8], align 8
  %_941 = alloca [16 x i8], align 8
  %_940 = alloca [16 x i8], align 8
  %_939 = alloca [48 x i8], align 8
  %_938 = alloca [48 x i8], align 8
  %_934 = alloca [16 x i8], align 8
  %_933 = alloca [16 x i8], align 8
  %_928 = alloca [48 x i8], align 8
  %_925 = alloca [16 x i8], align 8
  %_924 = alloca [16 x i8], align 8
  %_923 = alloca [48 x i8], align 8
  %_922 = alloca [48 x i8], align 8
  %_918 = alloca [16 x i8], align 8
  %_917 = alloca [16 x i8], align 8
  %_912 = alloca [48 x i8], align 8
  %_909 = alloca [16 x i8], align 8
  %_908 = alloca [16 x i8], align 8
  %_907 = alloca [48 x i8], align 8
  %_906 = alloca [48 x i8], align 8
  %_902 = alloca [16 x i8], align 8
  %_901 = alloca [16 x i8], align 8
  %_896 = alloca [48 x i8], align 8
  %_893 = alloca [16 x i8], align 8
  %_892 = alloca [16 x i8], align 8
  %_891 = alloca [48 x i8], align 8
  %_890 = alloca [48 x i8], align 8
  %_886 = alloca [16 x i8], align 8
  %_885 = alloca [16 x i8], align 8
  %_880 = alloca [48 x i8], align 8
  %_877 = alloca [16 x i8], align 8
  %_875 = alloca [16 x i8], align 8
  %_873 = alloca [16 x i8], align 8
  %_872 = alloca [48 x i8], align 8
  %_869 = alloca [48 x i8], align 8
  %root2 = alloca [8 x i8], align 8
  %root1 = alloca [8 x i8], align 8
  %discriminant = alloca [8 x i8], align 8
  %c_coeff = alloca [8 x i8], align 8
  %b_coeff = alloca [8 x i8], align 8
  %a_coeff = alloca [8 x i8], align 8
  %_848 = alloca [48 x i8], align 8
  %_845 = alloca [48 x i8], align 8
  %_842 = alloca [16 x i8], align 8
  %_841 = alloca [16 x i8], align 8
  %_840 = alloca [48 x i8], align 8
  %_839 = alloca [48 x i8], align 8
  %_835 = alloca [16 x i8], align 8
  %_834 = alloca [16 x i8], align 8
  %_829 = alloca [48 x i8], align 8
  %_826 = alloca [16 x i8], align 8
  %_825 = alloca [16 x i8], align 8
  %_824 = alloca [48 x i8], align 8
  %_823 = alloca [48 x i8], align 8
  %_819 = alloca [16 x i8], align 8
  %_818 = alloca [16 x i8], align 8
  %_813 = alloca [48 x i8], align 8
  %_810 = alloca [16 x i8], align 8
  %_809 = alloca [16 x i8], align 8
  %_808 = alloca [48 x i8], align 8
  %_807 = alloca [48 x i8], align 8
  %_803 = alloca [16 x i8], align 8
  %_802 = alloca [16 x i8], align 8
  %_797 = alloca [48 x i8], align 8
  %_794 = alloca [16 x i8], align 8
  %_793 = alloca [16 x i8], align 8
  %_792 = alloca [48 x i8], align 8
  %_791 = alloca [48 x i8], align 8
  %_787 = alloca [16 x i8], align 8
  %_786 = alloca [16 x i8], align 8
  %_781 = alloca [48 x i8], align 8
  %_778 = alloca [16 x i8], align 8
  %_777 = alloca [16 x i8], align 8
  %_776 = alloca [48 x i8], align 8
  %_775 = alloca [48 x i8], align 8
  %_772 = alloca [8 x i8], align 8
  %_770 = alloca [16 x i8], align 8
  %_769 = alloca [16 x i8], align 8
  %_764 = alloca [48 x i8], align 8
  %_761 = alloca [16 x i8], align 8
  %_760 = alloca [16 x i8], align 8
  %_759 = alloca [48 x i8], align 8
  %_758 = alloca [48 x i8], align 8
  %_754 = alloca [16 x i8], align 8
  %_753 = alloca [16 x i8], align 8
  %_748 = alloca [48 x i8], align 8
  %interest = alloca [8 x i8], align 8
  %amount = alloca [8 x i8], align 8
  %n = alloca [8 x i8], align 8
  %time = alloca [8 x i8], align 8
  %principal = alloca [8 x i8], align 8
  %_735 = alloca [48 x i8], align 8
  %_732 = alloca [48 x i8], align 8
  %_729 = alloca [16 x i8], align 8
  %_728 = alloca [16 x i8], align 8
  %_727 = alloca [48 x i8], align 8
  %_726 = alloca [48 x i8], align 8
  %_722 = alloca [16 x i8], align 8
  %_721 = alloca [16 x i8], align 8
  %_716 = alloca [48 x i8], align 8
  %_713 = alloca [16 x i8], align 8
  %_712 = alloca [16 x i8], align 8
  %_711 = alloca [48 x i8], align 8
  %_710 = alloca [48 x i8], align 8
  %_706 = alloca [16 x i8], align 8
  %_705 = alloca [16 x i8], align 8
  %_700 = alloca [48 x i8], align 8
  %_697 = alloca [16 x i8], align 8
  %_696 = alloca [16 x i8], align 8
  %_695 = alloca [48 x i8], align 8
  %_694 = alloca [48 x i8], align 8
  %_690 = alloca [16 x i8], align 8
  %_689 = alloca [16 x i8], align 8
  %_684 = alloca [48 x i8], align 8
  %_681 = alloca [16 x i8], align 8
  %_679 = alloca [16 x i8], align 8
  %_678 = alloca [32 x i8], align 8
  %_675 = alloca [48 x i8], align 8
  %sphere_volume = alloca [8 x i8], align 8
  %cylinder_volume = alloca [8 x i8], align 8
  %circle_area = alloca [8 x i8], align 8
  %height = alloca [8 x i8], align 8
  %radius = alloca [8 x i8], align 8
  %_659 = alloca [48 x i8], align 8
  %_656 = alloca [48 x i8], align 8
  %_653 = alloca [16 x i8], align 8
  %_652 = alloca [16 x i8], align 8
  %_649 = alloca [48 x i8], align 8
  %_646 = alloca [16 x i8], align 8
  %_645 = alloca [16 x i8], align 8
  %_642 = alloca [48 x i8], align 8
  %_639 = alloca [16 x i8], align 8
  %_637 = alloca [16 x i8], align 8
  %_635 = alloca [16 x i8], align 8
  %_633 = alloca [16 x i8], align 8
  %_632 = alloca [64 x i8], align 8
  %_629 = alloca [48 x i8], align 8
  %loan_amount = alloca [4 x i8], align 4
  %loan_eligible = alloca [1 x i8], align 1
  %credit_score = alloca [4 x i8], align 4
  %has_job = alloca [1 x i8], align 1
  %income = alloca [4 x i8], align 4
  %age = alloca [4 x i8], align 4
  %_615 = alloca [48 x i8], align 8
  %_612 = alloca [48 x i8], align 8
  %_609 = alloca [16 x i8], align 8
  %_608 = alloca [16 x i8], align 8
  %_605 = alloca [48 x i8], align 8
  %_602 = alloca [16 x i8], align 8
  %_601 = alloca [16 x i8], align 8
  %_598 = alloca [48 x i8], align 8
  %_595 = alloca [16 x i8], align 8
  %_594 = alloca [16 x i8], align 8
  %_591 = alloca [48 x i8], align 8
  %_588 = alloca [16 x i8], align 8
  %_587 = alloca [16 x i8], align 8
  %_584 = alloca [48 x i8], align 8
  %bonus = alloca [4 x i8], align 4
  %status = alloca [16 x i8], align 8
  %_577 = alloca [16 x i8], align 8
  %grade = alloca [16 x i8], align 8
  %score = alloca [4 x i8], align 4
  %_572 = alloca [48 x i8], align 8
  %_569 = alloca [48 x i8], align 8
  %_566 = alloca [16 x i8], align 8
  %_565 = alloca [16 x i8], align 8
  %_562 = alloca [48 x i8], align 8
  %_559 = alloca [16 x i8], align 8
  %_557 = alloca [16 x i8], align 8
  %_555 = alloca [16 x i8], align 8
  %_553 = alloca [16 x i8], align 8
  %_552 = alloca [64 x i8], align 8
  %_549 = alloca [48 x i8], align 8
  %_546 = alloca [16 x i8], align 8
  %_545 = alloca [24 x i8], align 8
  %_543 = alloca [16 x i8], align 8
  %_542 = alloca [24 x i8], align 8
  %_540 = alloca [16 x i8], align 8
  %_539 = alloca [48 x i8], align 8
  %_536 = alloca [48 x i8], align 8
  %res1 = alloca [24 x i8], align 8
  %_534 = alloca [24 x i8], align 8
  %str_expr2 = alloca [24 x i8], align 8
  %_531 = alloca [16 x i8], align 8
  %_529 = alloca [16 x i8], align 8
  %_527 = alloca [16 x i8], align 8
  %_526 = alloca [48 x i8], align 8
  %_523 = alloca [48 x i8], align 8
  %res = alloca [24 x i8], align 8
  %str_expr1 = alloca [24 x i8], align 8
  %str3 = alloca [16 x i8], align 8
  %str2 = alloca [16 x i8], align 8
  %str1 = alloca [16 x i8], align 8
  %_516 = alloca [48 x i8], align 8
  %_513 = alloca [48 x i8], align 8
  %_510 = alloca [16 x i8], align 8
  %_506 = alloca [16 x i8], align 8
  %_502 = alloca [16 x i8], align 8
  %_498 = alloca [16 x i8], align 8
  %_494 = alloca [16 x i8], align 8
  %_493 = alloca [80 x i8], align 8
  %_490 = alloca [48 x i8], align 8
  %_487 = alloca [16 x i8], align 8
  %_483 = alloca [16 x i8], align 8
  %_479 = alloca [16 x i8], align 8
  %_475 = alloca [16 x i8], align 8
  %_474 = alloca [64 x i8], align 8
  %_471 = alloca [48 x i8], align 8
  %_468 = alloca [16 x i8], align 8
  %_464 = alloca [16 x i8], align 8
  %_460 = alloca [16 x i8], align 8
  %_459 = alloca [48 x i8], align 8
  %_456 = alloca [48 x i8], align 8
  %array_expr3 = alloca [4 x i8], align 4
  %array_expr2 = alloca [4 x i8], align 4
  %array_expr1 = alloca [4 x i8], align 4
  %arr2 = alloca [12 x i8], align 4
  %arr1 = alloca [20 x i8], align 4
  %_409 = alloca [48 x i8], align 8
  %_406 = alloca [48 x i8], align 8
  %_403 = alloca [16 x i8], align 8
  %_402 = alloca [4 x i8], align 4
  %_400 = alloca [16 x i8], align 8
  %_398 = alloca [4 x i8], align 4
  %_396 = alloca [16 x i8], align 8
  %_395 = alloca [48 x i8], align 8
  %_392 = alloca [48 x i8], align 8
  %_389 = alloca [16 x i8], align 8
  %_388 = alloca [4 x i8], align 4
  %_386 = alloca [16 x i8], align 8
  %_385 = alloca [4 x i8], align 4
  %_383 = alloca [16 x i8], align 8
  %_382 = alloca [48 x i8], align 8
  %_379 = alloca [48 x i8], align 8
  %_376 = alloca [16 x i8], align 8
  %_375 = alloca [4 x i8], align 4
  %_373 = alloca [16 x i8], align 8
  %_372 = alloca [4 x i8], align 4
  %_370 = alloca [16 x i8], align 8
  %_369 = alloca [48 x i8], align 8
  %_366 = alloca [48 x i8], align 8
  %nested3 = alloca [4 x i8], align 4
  %nested2 = alloca [4 x i8], align 4
  %nested1 = alloca [4 x i8], align 4
  %_353 = alloca [48 x i8], align 8
  %_350 = alloca [48 x i8], align 8
  %_347 = alloca [16 x i8], align 8
  %_345 = alloca [16 x i8], align 8
  %_343 = alloca [16 x i8], align 8
  %_341 = alloca [16 x i8], align 8
  %_340 = alloca [64 x i8], align 8
  %_337 = alloca [48 x i8], align 8
  %_334 = alloca [16 x i8], align 8
  %_332 = alloca [16 x i8], align 8
  %_330 = alloca [16 x i8], align 8
  %_328 = alloca [16 x i8], align 8
  %_327 = alloca [64 x i8], align 8
  %_324 = alloca [48 x i8], align 8
  %_321 = alloca [16 x i8], align 8
  %_319 = alloca [16 x i8], align 8
  %_317 = alloca [16 x i8], align 8
  %_315 = alloca [16 x i8], align 8
  %_313 = alloca [16 x i8], align 8
  %_312 = alloca [80 x i8], align 8
  %_309 = alloca [48 x i8], align 8
  %_306 = alloca [16 x i8], align 8
  %_304 = alloca [16 x i8], align 8
  %_302 = alloca [16 x i8], align 8
  %_300 = alloca [16 x i8], align 8
  %_298 = alloca [16 x i8], align 8
  %_297 = alloca [80 x i8], align 8
  %_294 = alloca [48 x i8], align 8
  %_291 = alloca [16 x i8], align 8
  %_289 = alloca [16 x i8], align 8
  %_287 = alloca [16 x i8], align 8
  %_286 = alloca [48 x i8], align 8
  %_283 = alloca [48 x i8], align 8
  %comp4 = alloca [1 x i8], align 1
  %comp3 = alloca [1 x i8], align 1
  %comp2 = alloca [1 x i8], align 1
  %comp1 = alloca [1 x i8], align 1
  %num3 = alloca [4 x i8], align 4
  %num2 = alloca [4 x i8], align 4
  %num1 = alloca [4 x i8], align 4
  %_265 = alloca [48 x i8], align 8
  %_262 = alloca [48 x i8], align 8
  %_259 = alloca [16 x i8], align 8
  %_257 = alloca [16 x i8], align 8
  %_255 = alloca [16 x i8], align 8
  %_253 = alloca [16 x i8], align 8
  %_252 = alloca [64 x i8], align 8
  %_249 = alloca [48 x i8], align 8
  %_246 = alloca [16 x i8], align 8
  %_244 = alloca [16 x i8], align 8
  %_242 = alloca [16 x i8], align 8
  %_240 = alloca [16 x i8], align 8
  %_239 = alloca [64 x i8], align 8
  %_236 = alloca [48 x i8], align 8
  %_233 = alloca [16 x i8], align 8
  %_231 = alloca [16 x i8], align 8
  %_229 = alloca [16 x i8], align 8
  %_227 = alloca [16 x i8], align 8
  %_226 = alloca [64 x i8], align 8
  %_223 = alloca [48 x i8], align 8
  %_220 = alloca [16 x i8], align 8
  %_218 = alloca [16 x i8], align 8
  %_216 = alloca [16 x i8], align 8
  %_214 = alloca [16 x i8], align 8
  %_213 = alloca [64 x i8], align 8
  %_210 = alloca [48 x i8], align 8
  %_207 = alloca [16 x i8], align 8
  %_205 = alloca [16 x i8], align 8
  %_203 = alloca [16 x i8], align 8
  %_201 = alloca [16 x i8], align 8
  %_200 = alloca [64 x i8], align 8
  %_197 = alloca [48 x i8], align 8
  %_194 = alloca [16 x i8], align 8
  %_192 = alloca [16 x i8], align 8
  %_190 = alloca [16 x i8], align 8
  %_189 = alloca [48 x i8], align 8
  %_186 = alloca [48 x i8], align 8
  %bool5 = alloca [1 x i8], align 1
  %bool4 = alloca [1 x i8], align 1
  %bool3 = alloca [1 x i8], align 1
  %bool2 = alloca [1 x i8], align 1
  %bool1 = alloca [1 x i8], align 1
  %z = alloca [1 x i8], align 1
  %y = alloca [1 x i8], align 1
  %x = alloca [1 x i8], align 1
  %_175 = alloca [48 x i8], align 8
  %_172 = alloca [48 x i8], align 8
  %_169 = alloca [16 x i8], align 8
  %_167 = alloca [16 x i8], align 8
  %_165 = alloca [16 x i8], align 8
  %_163 = alloca [16 x i8], align 8
  %_161 = alloca [16 x i8], align 8
  %_159 = alloca [16 x i8], align 8
  %_158 = alloca [96 x i8], align 8
  %_155 = alloca [48 x i8], align 8
  %_152 = alloca [16 x i8], align 8
  %_150 = alloca [16 x i8], align 8
  %_148 = alloca [16 x i8], align 8
  %_146 = alloca [16 x i8], align 8
  %_144 = alloca [16 x i8], align 8
  %_143 = alloca [80 x i8], align 8
  %_140 = alloca [48 x i8], align 8
  %_137 = alloca [16 x i8], align 8
  %_135 = alloca [16 x i8], align 8
  %_133 = alloca [16 x i8], align 8
  %_131 = alloca [16 x i8], align 8
  %_129 = alloca [16 x i8], align 8
  %_128 = alloca [80 x i8], align 8
  %_125 = alloca [48 x i8], align 8
  %_122 = alloca [16 x i8], align 8
  %_120 = alloca [16 x i8], align 8
  %_118 = alloca [16 x i8], align 8
  %_116 = alloca [16 x i8], align 8
  %_114 = alloca [16 x i8], align 8
  %_113 = alloca [80 x i8], align 8
  %_110 = alloca [48 x i8], align 8
  %_107 = alloca [16 x i8], align 8
  %_105 = alloca [16 x i8], align 8
  %_103 = alloca [16 x i8], align 8
  %_101 = alloca [16 x i8], align 8
  %_100 = alloca [64 x i8], align 8
  %_97 = alloca [48 x i8], align 8
  %complex4 = alloca [4 x i8], align 4
  %complex3 = alloca [4 x i8], align 4
  %complex2 = alloca [4 x i8], align 4
  %complex1 = alloca [4 x i8], align 4
  %d = alloca [4 x i8], align 4
  %c = alloca [4 x i8], align 4
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_58 = alloca [48 x i8], align 8
  %_55 = alloca [48 x i8], align 8
  %_52 = alloca [16 x i8], align 8
  %_51 = alloca [16 x i8], align 8
  %_48 = alloca [48 x i8], align 8
  %_45 = alloca [16 x i8], align 8
  %_44 = alloca [16 x i8], align 8
  %_41 = alloca [48 x i8], align 8
  %_38 = alloca [16 x i8], align 8
  %_37 = alloca [16 x i8], align 8
  %_34 = alloca [48 x i8], align 8
  %_31 = alloca [16 x i8], align 8
  %_30 = alloca [16 x i8], align 8
  %_27 = alloca [48 x i8], align 8
  %result4 = alloca [4 x i8], align 4
  %result3 = alloca [4 x i8], align 4
  %result2 = alloca [4 x i8], align 4
  %result1 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_cfbe37048658dff96b0aa76ee7f1528d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_69700bad470cb74ae425ab33d059a06c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 3, i32 4)
  %_12.0 = extractvalue { i32, i1 } %0, 0
  %_12.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_12.1, label %panic, label %bb7

bb7:                                              ; preds = %start
  %1 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 2, i32 %_12.0)
  %_13.0 = extractvalue { i32, i1 } %1, 0
  %_13.1 = extractvalue { i32, i1 } %1, 1
  br i1 %_13.1, label %panic2, label %bb8

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_da461e915def639f3179374437f40cfd) #15
  unreachable

bb8:                                              ; preds = %bb7
  store i32 %_13.0, ptr %result1, align 4
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 2, i32 3)
  %_16.0 = extractvalue { i32, i1 } %2, 0
  %_16.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_16.1, label %panic3, label %bb9

panic2:                                           ; preds = %bb7
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_1c2b700c3c681a6c5268f2d06672de11) #15
  unreachable

bb9:                                              ; preds = %bb8
  %3 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_16.0, i32 4)
  %_17.0 = extractvalue { i32, i1 } %3, 0
  %_17.1 = extractvalue { i32, i1 } %3, 1
  br i1 %_17.1, label %panic4, label %bb10

panic3:                                           ; preds = %bb8
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f003f04333b6bbd88260141286c74976) #15
  unreachable

bb10:                                             ; preds = %bb9
  store i32 %_17.0, ptr %result2, align 4
  %4 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 2, i32 3)
  %_20.0 = extractvalue { i32, i1 } %4, 0
  %_20.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_20.1, label %panic5, label %bb11

panic4:                                           ; preds = %bb9
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_f003f04333b6bbd88260141286c74976) #15
  unreachable

bb11:                                             ; preds = %bb10
  %5 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_20.0, i32 4)
  %_21.0 = extractvalue { i32, i1 } %5, 0
  %_21.1 = extractvalue { i32, i1 } %5, 1
  br i1 %_21.1, label %panic6, label %bb12

panic5:                                           ; preds = %bb10
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_45740137045ff104de731745d8b5cb7d) #15
  unreachable

bb12:                                             ; preds = %bb11
  store i32 %_21.0, ptr %result3, align 4
  %6 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 3, i32 4)
  %_24.0 = extractvalue { i32, i1 } %6, 0
  %_24.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_24.1, label %panic7, label %bb13

panic6:                                           ; preds = %bb11
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_45740137045ff104de731745d8b5cb7d) #15
  unreachable

bb13:                                             ; preds = %bb12
  %7 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 2, i32 %_24.0)
  %_25.0 = extractvalue { i32, i1 } %7, 0
  %_25.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_25.1, label %panic8, label %bb14

panic7:                                           ; preds = %bb12
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_9b951957568e03c9a0c034c73258a25f) #15
  unreachable

bb14:                                             ; preds = %bb13
  store i32 %_25.0, ptr %result4, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_31, ptr align 4 %result1)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_30, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_31, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_27, ptr align 8 @alloc_fcae916d41549218b2cf57820b767722, ptr align 8 %_30)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_27)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_38, ptr align 4 %result2)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_37, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_38, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_34, ptr align 8 @alloc_78badedb11779d552d26e1f114710b54, ptr align 8 %_37)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_34)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_45, ptr align 4 %result3)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_44, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_45, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_41, ptr align 8 @alloc_34caf6c014131a256c30efd32da88240, ptr align 8 %_44)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_41)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_52, ptr align 4 %result4)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_51, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_52, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_48, ptr align 8 @alloc_27b1d958ed7871064c75a2c5daee5698, ptr align 8 %_51)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_48)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_55, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_55)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_58, ptr align 8 @alloc_69adafd4b29da1cdb47a1317bc8a9574)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_58)
  store i32 10, ptr %a, align 4
  store i32 5, ptr %b, align 4
  store i32 3, ptr %c, align 4
  store i32 2, ptr %d, align 4
  %12 = load i32, ptr %b, align 4
  %13 = load i32, ptr %c, align 4
  %14 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %12, i32 %13)
  %_67.0 = extractvalue { i32, i1 } %14, 0
  %_67.1 = extractvalue { i32, i1 } %14, 1
  br i1 %_67.1, label %panic9, label %bb31

panic8:                                           ; preds = %bb13
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_4d11f1c0cd122367d6737f4fa1979859) #15
  unreachable

bb31:                                             ; preds = %bb14
  %15 = load i32, ptr %a, align 4
  %16 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %15, i32 %_67.0)
  %_68.0 = extractvalue { i32, i1 } %16, 0
  %_68.1 = extractvalue { i32, i1 } %16, 1
  br i1 %_68.1, label %panic10, label %bb32

panic9:                                           ; preds = %bb14
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_da49f48b2839e334d1c63462ea1da08e) #15
  unreachable

bb32:                                             ; preds = %bb31
  %17 = load i32, ptr %d, align 4
  %18 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_68.0, i32 %17)
  %_69.0 = extractvalue { i32, i1 } %18, 0
  %_69.1 = extractvalue { i32, i1 } %18, 1
  br i1 %_69.1, label %panic11, label %bb33

panic10:                                          ; preds = %bb31
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_853b9e6f335723815475fad2d137aaf6) #15
  unreachable

bb33:                                             ; preds = %bb32
  store i32 %_69.0, ptr %complex1, align 4
  %19 = load i32, ptr %a, align 4
  %20 = load i32, ptr %b, align 4
  %21 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %19, i32 %20)
  %_72.0 = extractvalue { i32, i1 } %21, 0
  %_72.1 = extractvalue { i32, i1 } %21, 1
  br i1 %_72.1, label %panic12, label %bb34

panic11:                                          ; preds = %bb32
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_853b9e6f335723815475fad2d137aaf6) #15
  unreachable

bb34:                                             ; preds = %bb33
  %22 = load i32, ptr %c, align 4
  %23 = load i32, ptr %d, align 4
  %24 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %22, i32 %23)
  %_74.0 = extractvalue { i32, i1 } %24, 0
  %_74.1 = extractvalue { i32, i1 } %24, 1
  br i1 %_74.1, label %panic13, label %bb35

panic12:                                          ; preds = %bb33
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f9f191c23a2db8c2eb93c7b3db5ca99b) #15
  unreachable

bb35:                                             ; preds = %bb34
  %25 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_72.0, i32 %_74.0)
  %_75.0 = extractvalue { i32, i1 } %25, 0
  %_75.1 = extractvalue { i32, i1 } %25, 1
  br i1 %_75.1, label %panic14, label %bb36

panic13:                                          ; preds = %bb34
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_c5a472ec5c306e0dbff6508ff1f36c7f) #15
  unreachable

bb36:                                             ; preds = %bb35
  store i32 %_75.0, ptr %complex2, align 4
  %26 = load i32, ptr %a, align 4
  %27 = load i32, ptr %b, align 4
  %28 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %26, i32 %27)
  %_79.0 = extractvalue { i32, i1 } %28, 0
  %_79.1 = extractvalue { i32, i1 } %28, 1
  br i1 %_79.1, label %panic15, label %bb37

panic14:                                          ; preds = %bb35
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_f9f191c23a2db8c2eb93c7b3db5ca99b) #15
  unreachable

bb37:                                             ; preds = %bb36
  %29 = load i32, ptr %c, align 4
  %_80 = icmp eq i32 %29, 0
  br i1 %_80, label %panic16, label %bb38

panic15:                                          ; preds = %bb36
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_5b4c05f0d6098d73e6d2681d331a50f4) #15
  unreachable

bb38:                                             ; preds = %bb37
  %30 = load i32, ptr %c, align 4
  %_81 = icmp eq i32 %30, -1
  %_82 = icmp eq i32 %_79.0, -2147483648
  %_83 = and i1 %_81, %_82
  br i1 %_83, label %panic17, label %bb39

panic16:                                          ; preds = %bb37
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_5b4c05f0d6098d73e6d2681d331a50f4) #15
  unreachable

bb39:                                             ; preds = %bb38
  %31 = load i32, ptr %c, align 4
  %_77 = sdiv i32 %_79.0, %31
  %32 = load i32, ptr %d, align 4
  %33 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_77, i32 %32)
  %_84.0 = extractvalue { i32, i1 } %33, 0
  %_84.1 = extractvalue { i32, i1 } %33, 1
  br i1 %_84.1, label %panic18, label %bb40

panic17:                                          ; preds = %bb38
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_5b4c05f0d6098d73e6d2681d331a50f4) #15
  unreachable

bb40:                                             ; preds = %bb39
  store i32 %_84.0, ptr %complex3, align 4
  %34 = load i32, ptr %b, align 4
  %35 = load i32, ptr %c, align 4
  %36 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %34, i32 %35)
  %_88.0 = extractvalue { i32, i1 } %36, 0
  %_88.1 = extractvalue { i32, i1 } %36, 1
  br i1 %_88.1, label %panic19, label %bb41

panic18:                                          ; preds = %bb39
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_5b4c05f0d6098d73e6d2681d331a50f4) #15
  unreachable

bb41:                                             ; preds = %bb40
  %_89 = icmp eq i32 %_88.0, 0
  br i1 %_89, label %panic20, label %bb42

panic19:                                          ; preds = %bb40
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_e11dfcd20d35445ad2a4acce6ae9cc0b) #15
  unreachable

bb42:                                             ; preds = %bb41
  %_90 = icmp eq i32 %_88.0, -1
  %37 = load i32, ptr %a, align 4
  %_91 = icmp eq i32 %37, -2147483648
  %_92 = and i1 %_90, %_91
  br i1 %_92, label %panic21, label %bb43

panic20:                                          ; preds = %bb41
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_540719b2ca7672a7eda3b418f2daae86) #15
  unreachable

bb43:                                             ; preds = %bb42
  %38 = load i32, ptr %a, align 4
  %_86 = sdiv i32 %38, %_88.0
  %39 = load i32, ptr %d, align 4
  %40 = load i32, ptr %c, align 4
  %41 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %39, i32 %40)
  %_94.0 = extractvalue { i32, i1 } %41, 0
  %_94.1 = extractvalue { i32, i1 } %41, 1
  br i1 %_94.1, label %panic22, label %bb44

panic21:                                          ; preds = %bb42
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_540719b2ca7672a7eda3b418f2daae86) #15
  unreachable

bb44:                                             ; preds = %bb43
  %42 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_86, i32 %_94.0)
  %_95.0 = extractvalue { i32, i1 } %42, 0
  %_95.1 = extractvalue { i32, i1 } %42, 1
  br i1 %_95.1, label %panic23, label %bb45

panic22:                                          ; preds = %bb43
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_7a50ec23b1c273891a51054aad1fad06) #15
  unreachable

bb45:                                             ; preds = %bb44
  store i32 %_95.0, ptr %complex4, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_101, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_103, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_105, ptr align 4 %c)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_107, ptr align 4 %d)
  %43 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_100, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %43, ptr align 8 %_101, i64 16, i1 false)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_100, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_103, i64 16, i1 false)
  %45 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_100, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %45, ptr align 8 %_105, i64 16, i1 false)
  %46 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_100, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %46, ptr align 8 %_107, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_97, ptr align 8 @alloc_8ba0051b8279a8a7ffde3f410f1f5868, ptr align 8 %_100)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_97)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_114, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_116, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_118, ptr align 4 %c)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_120, ptr align 4 %d)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_122, ptr align 4 %complex1)
  %47 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %47, ptr align 8 %_114, i64 16, i1 false)
  %48 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %48, ptr align 8 %_116, i64 16, i1 false)
  %49 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %49, ptr align 8 %_118, i64 16, i1 false)
  %50 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %50, ptr align 8 %_120, i64 16, i1 false)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_122, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_110, ptr align 8 @alloc_95c67c5809decc7e1d926a0710a53fec, ptr align 8 %_113)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_110)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_129, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_131, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_133, ptr align 4 %c)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_135, ptr align 4 %d)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_137, ptr align 4 %complex2)
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_129, i64 16, i1 false)
  %53 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %53, ptr align 8 %_131, i64 16, i1 false)
  %54 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %54, ptr align 8 %_133, i64 16, i1 false)
  %55 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %55, ptr align 8 %_135, i64 16, i1 false)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_137, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_125, ptr align 8 @alloc_d7cd4bbd691d2cff323196c56bd09007, ptr align 8 %_128)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_125)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_144, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_146, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_148, ptr align 4 %c)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_150, ptr align 4 %d)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_152, ptr align 4 %complex3)
  %57 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_143, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %57, ptr align 8 %_144, i64 16, i1 false)
  %58 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_143, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %58, ptr align 8 %_146, i64 16, i1 false)
  %59 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_143, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %59, ptr align 8 %_148, i64 16, i1 false)
  %60 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_143, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %60, ptr align 8 %_150, i64 16, i1 false)
  %61 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_143, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %61, ptr align 8 %_152, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_140, ptr align 8 @alloc_c1db895016117a469a4f7713d8b14039, ptr align 8 %_143)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_140)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_159, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_161, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_163, ptr align 4 %c)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_165, ptr align 4 %d)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_167, ptr align 4 %d)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_169, ptr align 4 %complex4)
  %62 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %62, ptr align 8 %_159, i64 16, i1 false)
  %63 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %63, ptr align 8 %_161, i64 16, i1 false)
  %64 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %64, ptr align 8 %_163, i64 16, i1 false)
  %65 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %65, ptr align 8 %_165, i64 16, i1 false)
  %66 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %66, ptr align 8 %_167, i64 16, i1 false)
  %67 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 5
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %67, ptr align 8 %_169, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5290eaeab44b7695E(ptr sret([48 x i8]) align 8 %_155, ptr align 8 @alloc_be173c8d8eeb756f356db6f4bd8220a7, ptr align 8 %_158)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_155)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_172, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_172)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_175, ptr align 8 @alloc_e3e2326bce536aaf42e973228f89a1d6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_175)
  store i8 1, ptr %x, align 1
  store i8 0, ptr %y, align 1
  store i8 1, ptr %z, align 1
  %68 = load i8, ptr %x, align 1
  %69 = trunc nuw i8 %68 to i1
  br i1 %69, label %bb85, label %bb87

panic23:                                          ; preds = %bb44
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_540719b2ca7672a7eda3b418f2daae86) #15
  unreachable

bb87:                                             ; preds = %bb85, %bb45
  %70 = load i8, ptr %z, align 1
  %71 = trunc nuw i8 %70 to i1
  %72 = zext i1 %71 to i8
  store i8 %72, ptr %bool1, align 1
  br label %bb88

bb85:                                             ; preds = %bb45
  %73 = load i8, ptr %y, align 1
  %74 = trunc nuw i8 %73 to i1
  br i1 %74, label %bb86, label %bb87

bb86:                                             ; preds = %bb85
  store i8 1, ptr %bool1, align 1
  br label %bb88

bb88:                                             ; preds = %bb86, %bb87
  %75 = load i8, ptr %x, align 1
  %76 = trunc nuw i8 %75 to i1
  br i1 %76, label %bb89, label %bb90

bb90:                                             ; preds = %bb88
  store i8 0, ptr %bool2, align 1
  br label %bb93

bb89:                                             ; preds = %bb88
  %77 = load i8, ptr %y, align 1
  %78 = trunc nuw i8 %77 to i1
  br i1 %78, label %bb91, label %bb92

bb93:                                             ; preds = %bb91, %bb92, %bb90
  %79 = load i8, ptr %x, align 1
  %80 = trunc nuw i8 %79 to i1
  br i1 %80, label %bb94, label %bb96

bb92:                                             ; preds = %bb89
  %81 = load i8, ptr %z, align 1
  %82 = trunc nuw i8 %81 to i1
  %83 = zext i1 %82 to i8
  store i8 %83, ptr %bool2, align 1
  br label %bb93

bb91:                                             ; preds = %bb89
  store i8 1, ptr %bool2, align 1
  br label %bb93

bb96:                                             ; preds = %bb94, %bb93
  %84 = load i8, ptr %z, align 1
  %85 = trunc nuw i8 %84 to i1
  %86 = zext i1 %85 to i8
  store i8 %86, ptr %bool3, align 1
  br label %bb97

bb94:                                             ; preds = %bb93
  %87 = load i8, ptr %y, align 1
  %88 = trunc nuw i8 %87 to i1
  br i1 %88, label %bb95, label %bb96

bb95:                                             ; preds = %bb94
  store i8 1, ptr %bool3, align 1
  br label %bb97

bb97:                                             ; preds = %bb95, %bb96
  %89 = load i8, ptr %x, align 1
  %90 = trunc nuw i8 %89 to i1
  br i1 %90, label %bb99, label %bb98

bb98:                                             ; preds = %bb97
  store i8 1, ptr %bool4, align 1
  br label %bb102

bb99:                                             ; preds = %bb97
  %91 = load i8, ptr %y, align 1
  %92 = trunc nuw i8 %91 to i1
  br i1 %92, label %bb100, label %bb101

bb102:                                            ; preds = %bb100, %bb101, %bb98
  %93 = load i8, ptr %x, align 1
  %94 = trunc nuw i8 %93 to i1
  br i1 %94, label %bb105, label %bb103

bb101:                                            ; preds = %bb99
  store i8 0, ptr %bool4, align 1
  br label %bb102

bb100:                                            ; preds = %bb99
  %95 = load i8, ptr %z, align 1
  %96 = trunc nuw i8 %95 to i1
  %97 = zext i1 %96 to i8
  store i8 %97, ptr %bool4, align 1
  br label %bb102

bb103:                                            ; preds = %bb102
  %98 = load i8, ptr %y, align 1
  %99 = trunc nuw i8 %98 to i1
  br i1 %99, label %bb105, label %bb104

bb105:                                            ; preds = %bb103, %bb102
  store i8 0, ptr %bool5, align 1
  br label %bb106

bb104:                                            ; preds = %bb103
  %100 = load i8, ptr %z, align 1
  %101 = trunc nuw i8 %100 to i1
  %102 = zext i1 %101 to i8
  store i8 %102, ptr %bool5, align 1
  br label %bb106

bb106:                                            ; preds = %bb105, %bb104
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_190, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_192, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_194, ptr align 1 %z)
  %103 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_189, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %103, ptr align 8 %_190, i64 16, i1 false)
  %104 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_189, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %104, ptr align 8 %_192, i64 16, i1 false)
  %105 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_189, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %105, ptr align 8 %_194, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_186, ptr align 8 @alloc_98b76634a7071f83ab1aba9c37ddc89a, ptr align 8 %_189)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_186)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_201, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_203, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_205, ptr align 1 %z)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_207, ptr align 1 %bool1)
  %106 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_200, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %106, ptr align 8 %_201, i64 16, i1 false)
  %107 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_200, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %107, ptr align 8 %_203, i64 16, i1 false)
  %108 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_200, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %108, ptr align 8 %_205, i64 16, i1 false)
  %109 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_200, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %109, ptr align 8 %_207, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_197, ptr align 8 @alloc_40c0cd29fbd31f4ca2281b56c2fb5201, ptr align 8 %_200)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_197)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_214, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_216, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_218, ptr align 1 %z)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_220, ptr align 1 %bool2)
  %110 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %110, ptr align 8 %_214, i64 16, i1 false)
  %111 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %111, ptr align 8 %_216, i64 16, i1 false)
  %112 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %112, ptr align 8 %_218, i64 16, i1 false)
  %113 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %113, ptr align 8 %_220, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_210, ptr align 8 @alloc_d5e09c07df792f89d3220e63ad1bfd89, ptr align 8 %_213)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_210)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_227, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_229, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_231, ptr align 1 %z)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_233, ptr align 1 %bool3)
  %114 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_226, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %114, ptr align 8 %_227, i64 16, i1 false)
  %115 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_226, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %115, ptr align 8 %_229, i64 16, i1 false)
  %116 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_226, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %116, ptr align 8 %_231, i64 16, i1 false)
  %117 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_226, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %117, ptr align 8 %_233, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_223, ptr align 8 @alloc_cb11c86507dfa13fc420573fdc06d10e, ptr align 8 %_226)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_223)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_240, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_242, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_244, ptr align 1 %z)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_246, ptr align 1 %bool4)
  %118 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_239, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %118, ptr align 8 %_240, i64 16, i1 false)
  %119 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_239, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %119, ptr align 8 %_242, i64 16, i1 false)
  %120 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_239, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %120, ptr align 8 %_244, i64 16, i1 false)
  %121 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_239, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %121, ptr align 8 %_246, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_236, ptr align 8 @alloc_01142cf538361b6585bdfff4178eb242, ptr align 8 %_239)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_236)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_253, ptr align 1 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_255, ptr align 1 %y)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_257, ptr align 1 %z)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_259, ptr align 1 %bool5)
  %122 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_252, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %122, ptr align 8 %_253, i64 16, i1 false)
  %123 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_252, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %123, ptr align 8 %_255, i64 16, i1 false)
  %124 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_252, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %124, ptr align 8 %_257, i64 16, i1 false)
  %125 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_252, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %125, ptr align 8 %_259, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_249, ptr align 8 @alloc_6cd0f7c45522d61c8d7a0f1b65e5a7a2, ptr align 8 %_252)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_249)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_262, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_262)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_265, ptr align 8 @alloc_be172a90f574601b871136f96cb2d4ea)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_265)
  store i32 15, ptr %num1, align 4
  store i32 10, ptr %num2, align 4
  store i32 20, ptr %num3, align 4
  %126 = load i32, ptr %num1, align 4
  %127 = load i32, ptr %num2, align 4
  %_271 = icmp sgt i32 %126, %127
  br i1 %_271, label %bb146, label %bb147

bb147:                                            ; preds = %bb106
  store i8 0, ptr %comp1, align 1
  br label %bb148

bb146:                                            ; preds = %bb106
  %128 = load i32, ptr %num1, align 4
  %129 = load i32, ptr %num3, align 4
  %130 = icmp slt i32 %128, %129
  %131 = zext i1 %130 to i8
  store i8 %131, ptr %comp1, align 1
  br label %bb148

bb148:                                            ; preds = %bb146, %bb147
  %132 = load i32, ptr %num1, align 4
  %133 = load i32, ptr %num2, align 4
  %_273 = icmp sge i32 %132, %133
  br i1 %_273, label %bb149, label %bb150

bb150:                                            ; preds = %bb148
  %134 = load i32, ptr %num1, align 4
  %135 = load i32, ptr %num3, align 4
  %136 = icmp sle i32 %134, %135
  %137 = zext i1 %136 to i8
  store i8 %137, ptr %comp2, align 1
  br label %bb151

bb149:                                            ; preds = %bb148
  store i8 1, ptr %comp2, align 1
  br label %bb151

bb151:                                            ; preds = %bb149, %bb150
  %138 = load i32, ptr %num1, align 4
  %139 = load i32, ptr %num2, align 4
  %140 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %138, i32 %139)
  %_276.0 = extractvalue { i32, i1 } %140, 0
  %_276.1 = extractvalue { i32, i1 } %140, 1
  br i1 %_276.1, label %panic24, label %bb152

bb152:                                            ; preds = %bb151
  %141 = load i32, ptr %num3, align 4
  %142 = icmp sgt i32 %_276.0, %141
  %143 = zext i1 %142 to i8
  store i8 %143, ptr %comp3, align 1
  %144 = load i32, ptr %num1, align 4
  %145 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %144, i32 2)
  %_279.0 = extractvalue { i32, i1 } %145, 0
  %_279.1 = extractvalue { i32, i1 } %145, 1
  br i1 %_279.1, label %panic25, label %bb153

panic24:                                          ; preds = %bb151
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f7e2f65d0d968026a00ba388d1be650d) #15
  unreachable

bb153:                                            ; preds = %bb152
  %146 = load i32, ptr %num3, align 4
  %147 = load i32, ptr %num2, align 4
  %148 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %146, i32 %147)
  %_281.0 = extractvalue { i32, i1 } %148, 0
  %_281.1 = extractvalue { i32, i1 } %148, 1
  br i1 %_281.1, label %panic26, label %bb154

panic25:                                          ; preds = %bb152
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_d38a491bfb7f2fe696093c2673f5c703) #15
  unreachable

bb154:                                            ; preds = %bb153
  %149 = icmp eq i32 %_279.0, %_281.0
  %150 = zext i1 %149 to i8
  store i8 %150, ptr %comp4, align 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_287, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_289, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_291, ptr align 4 %num3)
  %151 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_286, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %151, ptr align 8 %_287, i64 16, i1 false)
  %152 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_286, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %152, ptr align 8 %_289, i64 16, i1 false)
  %153 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_286, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %153, ptr align 8 %_291, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_283, ptr align 8 @alloc_b24baafdee1c304485ca24c9cd903b20, ptr align 8 %_286)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_283)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_298, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_300, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_302, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_304, ptr align 4 %num3)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_306, ptr align 1 %comp1)
  %154 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %154, ptr align 8 %_298, i64 16, i1 false)
  %155 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %155, ptr align 8 %_300, i64 16, i1 false)
  %156 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %156, ptr align 8 %_302, i64 16, i1 false)
  %157 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %157, ptr align 8 %_304, i64 16, i1 false)
  %158 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %158, ptr align 8 %_306, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_294, ptr align 8 @alloc_4326605021b0032e745a4be032926eae, ptr align 8 %_297)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_294)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_313, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_315, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_317, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_319, ptr align 4 %num3)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_321, ptr align 1 %comp2)
  %159 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_312, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %159, ptr align 8 %_313, i64 16, i1 false)
  %160 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_312, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %160, ptr align 8 %_315, i64 16, i1 false)
  %161 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_312, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %161, ptr align 8 %_317, i64 16, i1 false)
  %162 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_312, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %162, ptr align 8 %_319, i64 16, i1 false)
  %163 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_312, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %163, ptr align 8 %_321, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_309, ptr align 8 @alloc_37d01627bbe604c06144acf1af098375, ptr align 8 %_312)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_309)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_328, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_330, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_332, ptr align 4 %num3)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_334, ptr align 1 %comp3)
  %164 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_327, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %164, ptr align 8 %_328, i64 16, i1 false)
  %165 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_327, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %165, ptr align 8 %_330, i64 16, i1 false)
  %166 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_327, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %166, ptr align 8 %_332, i64 16, i1 false)
  %167 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_327, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %167, ptr align 8 %_334, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_324, ptr align 8 @alloc_835288a1bcccc5727d3c0ddb6128a73c, ptr align 8 %_327)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_324)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_341, ptr align 4 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_343, ptr align 4 %num3)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_345, ptr align 4 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_347, ptr align 1 %comp4)
  %168 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %168, ptr align 8 %_341, i64 16, i1 false)
  %169 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %169, ptr align 8 %_343, i64 16, i1 false)
  %170 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %170, ptr align 8 %_345, i64 16, i1 false)
  %171 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %171, ptr align 8 %_347, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_337, ptr align 8 @alloc_98850fb03a01b1acab2669d692bb446b, ptr align 8 %_340)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_337)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_350, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_350)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_353, ptr align 8 @alloc_8d54751145604d7e5f90749bfc9193bf)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_353)
; call _14_complex_expressions::multiply
  %_356 = call i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 3, i32 4)
; call _14_complex_expressions::subtract
  %_357 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 10, i32 5)
; call _14_complex_expressions::add
  %172 = call i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 %_356, i32 %_357)
  store i32 %172, ptr %nested1, align 4
; call _14_complex_expressions::add
  %_359 = call i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 2, i32 3)
; call _14_complex_expressions::subtract
  %_360 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 8, i32 3)
; call _14_complex_expressions::multiply
  %173 = call i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 %_359, i32 %_360)
  store i32 %173, ptr %nested2, align 4
; call _14_complex_expressions::multiply
  %_363 = call i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 4, i32 5)
; call _14_complex_expressions::add
  %_362 = call i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 %_363, i32 10)
; call _14_complex_expressions::subtract
  %_364 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 15, i32 5)
; call _14_complex_expressions::divide
  %174 = call i32 @_ZN23_14_complex_expressions6divide17h2d733e1ee658214fE(i32 %_362, i32 %_364)
  store i32 %174, ptr %nested3, align 4
; call _14_complex_expressions::multiply
  %175 = call i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 3, i32 4)
  store i32 %175, ptr %_372, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_370, ptr align 4 %_372)
; call _14_complex_expressions::subtract
  %176 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 10, i32 5)
  store i32 %176, ptr %_375, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_373, ptr align 4 %_375)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_376, ptr align 4 %nested1)
  %177 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_369, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %177, ptr align 8 %_370, i64 16, i1 false)
  %178 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_369, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %178, ptr align 8 %_373, i64 16, i1 false)
  %179 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_369, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %179, ptr align 8 %_376, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_366, ptr align 8 @alloc_a863b5029b656c35414ce8ca23cbce15, ptr align 8 %_369)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_366)
; call _14_complex_expressions::add
  %180 = call i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 2, i32 3)
  store i32 %180, ptr %_385, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_383, ptr align 4 %_385)
; call _14_complex_expressions::subtract
  %181 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 8, i32 3)
  store i32 %181, ptr %_388, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_386, ptr align 4 %_388)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_389, ptr align 4 %nested2)
  %182 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_382, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %182, ptr align 8 %_383, i64 16, i1 false)
  %183 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_382, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %183, ptr align 8 %_386, i64 16, i1 false)
  %184 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_382, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %184, ptr align 8 %_389, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_379, ptr align 8 @alloc_6f1ba981de6bcac26a12e82736dac95a, ptr align 8 %_382)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_379)
; call _14_complex_expressions::multiply
  %_399 = call i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 4, i32 5)
; call _14_complex_expressions::add
  %185 = call i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 %_399, i32 10)
  store i32 %185, ptr %_398, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_396, ptr align 4 %_398)
; call _14_complex_expressions::subtract
  %186 = call i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 15, i32 5)
  store i32 %186, ptr %_402, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_400, ptr align 4 %_402)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_403, ptr align 4 %nested3)
  %187 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_395, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %187, ptr align 8 %_396, i64 16, i1 false)
  %188 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_395, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %188, ptr align 8 %_400, i64 16, i1 false)
  %189 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_395, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %189, ptr align 8 %_403, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_392, ptr align 8 @alloc_8490394ebf2b0ec542e3c0fd4c759000, ptr align 8 %_395)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_392)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_406, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_406)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_409, ptr align 8 @alloc_a33870c54c442141aee5f80dcd43c46c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_409)
  %190 = getelementptr inbounds nuw i32, ptr %arr1, i64 0
  store i32 1, ptr %190, align 4
  %191 = getelementptr inbounds nuw i32, ptr %arr1, i64 1
  store i32 2, ptr %191, align 4
  %192 = getelementptr inbounds nuw i32, ptr %arr1, i64 2
  store i32 3, ptr %192, align 4
  %193 = getelementptr inbounds nuw i32, ptr %arr1, i64 3
  store i32 4, ptr %193, align 4
  %194 = getelementptr inbounds nuw i32, ptr %arr1, i64 4
  store i32 5, ptr %194, align 4
  %195 = getelementptr inbounds nuw i32, ptr %arr2, i64 0
  store i32 10, ptr %195, align 4
  %196 = getelementptr inbounds nuw i32, ptr %arr2, i64 1
  store i32 20, ptr %196, align 4
  %197 = getelementptr inbounds nuw i32, ptr %arr2, i64 2
  store i32 30, ptr %197, align 4
  %198 = getelementptr inbounds nuw i32, ptr %arr1, i64 0
  %_414 = load i32, ptr %198, align 4
  %199 = getelementptr inbounds nuw i32, ptr %arr1, i64 4
  %_417 = load i32, ptr %199, align 4
  %200 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_414, i32 %_417)
  %_420.0 = extractvalue { i32, i1 } %200, 0
  %_420.1 = extractvalue { i32, i1 } %200, 1
  br i1 %_420.1, label %panic27, label %bb228

panic26:                                          ; preds = %bb153
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_69593917f132cc32b38ddbba32f4932b) #15
  unreachable

bb228:                                            ; preds = %bb154
  store i32 %_420.0, ptr %array_expr1, align 4
  %201 = getelementptr inbounds nuw i32, ptr %arr1, i64 1
  %_423 = load i32, ptr %201, align 4
  %202 = getelementptr inbounds nuw i32, ptr %arr2, i64 0
  %_426 = load i32, ptr %202, align 4
  %203 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_423, i32 %_426)
  %_429.0 = extractvalue { i32, i1 } %203, 0
  %_429.1 = extractvalue { i32, i1 } %203, 1
  br i1 %_429.1, label %panic28, label %bb231

panic27:                                          ; preds = %bb154
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_1d7d9a2d7a456e2660a2c383314c41fd) #15
  unreachable

bb231:                                            ; preds = %bb228
  %204 = getelementptr inbounds nuw i32, ptr %arr1, i64 2
  %_430 = load i32, ptr %204, align 4
  %_433 = icmp eq i32 %_430, 0
  br i1 %_433, label %panic29, label %bb233

panic28:                                          ; preds = %bb228
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_983e72a3fd8c8973079ab609ec1da3b1) #15
  unreachable

bb233:                                            ; preds = %bb231
  %_434 = icmp eq i32 %_430, -1
  %_435 = icmp eq i32 %_429.0, -2147483648
  %_436 = and i1 %_434, %_435
  br i1 %_436, label %panic30, label %bb234

panic29:                                          ; preds = %bb231
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_983e72a3fd8c8973079ab609ec1da3b1) #15
  unreachable

bb234:                                            ; preds = %bb233
  %205 = sdiv i32 %_429.0, %_430
  store i32 %205, ptr %array_expr2, align 4
  %206 = getelementptr inbounds nuw i32, ptr %arr1, i64 0
  %_439 = load i32, ptr %206, align 4
  %207 = getelementptr inbounds nuw i32, ptr %arr1, i64 1
  %_442 = load i32, ptr %207, align 4
  %208 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_439, i32 %_442)
  %_445.0 = extractvalue { i32, i1 } %208, 0
  %_445.1 = extractvalue { i32, i1 } %208, 1
  br i1 %_445.1, label %panic31, label %bb237

panic30:                                          ; preds = %bb233
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_983e72a3fd8c8973079ab609ec1da3b1) #15
  unreachable

bb237:                                            ; preds = %bb234
  %209 = getelementptr inbounds nuw i32, ptr %arr2, i64 1
  %_447 = load i32, ptr %209, align 4
  %210 = getelementptr inbounds nuw i32, ptr %arr2, i64 0
  %_450 = load i32, ptr %210, align 4
  %211 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_447, i32 %_450)
  %_453.0 = extractvalue { i32, i1 } %211, 0
  %_453.1 = extractvalue { i32, i1 } %211, 1
  br i1 %_453.1, label %panic32, label %bb240

panic31:                                          ; preds = %bb234
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_146f499f10a72cb30a4745641f82e622) #15
  unreachable

bb240:                                            ; preds = %bb237
  %212 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_445.0, i32 %_453.0)
  %_454.0 = extractvalue { i32, i1 } %212, 0
  %_454.1 = extractvalue { i32, i1 } %212, 1
  br i1 %_454.1, label %panic33, label %bb241

panic32:                                          ; preds = %bb237
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_d064960ba72f2810f8cd42e3020ef372) #15
  unreachable

bb241:                                            ; preds = %bb240
  store i32 %_454.0, ptr %array_expr3, align 4
  %_461 = getelementptr inbounds nuw i32, ptr %arr1, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_460, ptr align 4 %_461)
  %_465 = getelementptr inbounds nuw i32, ptr %arr1, i64 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_464, ptr align 4 %_465)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_468, ptr align 4 %array_expr1)
  %213 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_459, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %213, ptr align 8 %_460, i64 16, i1 false)
  %214 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_459, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %214, ptr align 8 %_464, i64 16, i1 false)
  %215 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_459, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %215, ptr align 8 %_468, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_456, ptr align 8 @alloc_04f87af1904a532fcdd937820d028c5d, ptr align 8 %_459)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_456)
  %_476 = getelementptr inbounds nuw i32, ptr %arr1, i64 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_475, ptr align 4 %_476)
  %_480 = getelementptr inbounds nuw i32, ptr %arr2, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_479, ptr align 4 %_480)
  %_484 = getelementptr inbounds nuw i32, ptr %arr1, i64 2
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_483, ptr align 4 %_484)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_487, ptr align 4 %array_expr2)
  %216 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_474, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %216, ptr align 8 %_475, i64 16, i1 false)
  %217 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_474, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %217, ptr align 8 %_479, i64 16, i1 false)
  %218 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_474, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %218, ptr align 8 %_483, i64 16, i1 false)
  %219 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_474, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %219, ptr align 8 %_487, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_471, ptr align 8 @alloc_1ebf4633a2d7ab9acf0ac44a3d7d13e6, ptr align 8 %_474)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_471)
  %_495 = getelementptr inbounds nuw i32, ptr %arr1, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_494, ptr align 4 %_495)
  %_499 = getelementptr inbounds nuw i32, ptr %arr1, i64 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_498, ptr align 4 %_499)
  %_503 = getelementptr inbounds nuw i32, ptr %arr2, i64 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_502, ptr align 4 %_503)
  %_507 = getelementptr inbounds nuw i32, ptr %arr2, i64 0
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_506, ptr align 4 %_507)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_510, ptr align 4 %array_expr3)
  %220 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_493, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %220, ptr align 8 %_494, i64 16, i1 false)
  %221 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_493, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %221, ptr align 8 %_498, i64 16, i1 false)
  %222 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_493, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %222, ptr align 8 %_502, i64 16, i1 false)
  %223 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_493, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %223, ptr align 8 %_506, i64 16, i1 false)
  %224 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_493, i64 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %224, ptr align 8 %_510, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3d7da381d3a8dbe8E(ptr sret([48 x i8]) align 8 %_490, ptr align 8 @alloc_3f4b7259998fe1b24df51e4dcc5d6883, ptr align 8 %_493)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_490)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_513, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_513)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_516, ptr align 8 @alloc_d071d3bea5240355f1714484fe5d0e46)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_516)
  store ptr @alloc_3edef0b68cfa9c8c95e6d4fe1a68842b, ptr %str1, align 8
  %225 = getelementptr inbounds i8, ptr %str1, i64 8
  store i64 5, ptr %225, align 8
  store ptr @alloc_7c36acd6e5096800e8610a1984ba6ddd, ptr %str2, align 8
  %226 = getelementptr inbounds i8, ptr %str2, i64 8
  store i64 5, ptr %226, align 8
  store ptr @alloc_ebacd5270fb181ba2143a73d4427458e, ptr %str3, align 8
  %227 = getelementptr inbounds i8, ptr %str3, i64 8
  store i64 1, ptr %227, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_527, ptr align 8 %str1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_529, ptr align 8 %str2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_531, ptr align 8 %str3)
  %228 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_526, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %228, ptr align 8 %_527, i64 16, i1 false)
  %229 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_526, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %229, ptr align 8 %_529, i64 16, i1 false)
  %230 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_526, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %230, ptr align 8 %_531, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h77d88efe42cf4eccE(ptr sret([48 x i8]) align 8 %_523, ptr align 8 @alloc_59f946dc1e3b59f4bf03b40f89f6c3a5, ptr align 8 %_526)
; call alloc::fmt::format
  call void @_ZN5alloc3fmt6format17hfa9b33a30e7e0548E(ptr sret([24 x i8]) align 8 %res, ptr align 8 %_523)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %str_expr1, ptr align 8 %res, i64 24, i1 false)
  %231 = load ptr, ptr %str1, align 8
  %232 = getelementptr inbounds i8, ptr %str1, i64 8
  %233 = load i64, ptr %232, align 8
; invoke alloc::str::<impl str>::to_uppercase
  invoke void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$12to_uppercase17h024e81649237501cE"(ptr sret([24 x i8]) align 8 %_542, ptr align 1 %231, i64 %233)
          to label %bb279 unwind label %funclet_bb492

panic33:                                          ; preds = %bb240
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_146f499f10a72cb30a4745641f82e622) #15
  unreachable

bb492:                                            ; preds = %funclet_bb492
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %str_expr1) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb492:                                    ; preds = %bb491, %bb489, %bb487, %bb484, %bb241
  %cleanuppad = cleanuppad within none []
  br label %bb492

bb279:                                            ; preds = %bb241
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17haf1723d329d1dc46E(ptr sret([16 x i8]) align 8 %_540, ptr align 8 %_542)
          to label %bb280 unwind label %funclet_bb491

bb491:                                            ; preds = %funclet_bb491
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_542) #18 [ "funclet"(token %cleanuppad34) ]
  cleanupret from %cleanuppad34 unwind label %funclet_bb492

funclet_bb491:                                    ; preds = %bb490, %bb280, %bb279
  %cleanuppad34 = cleanuppad within none []
  br label %bb491

bb280:                                            ; preds = %bb279
  %234 = load ptr, ptr %str2, align 8
  %235 = getelementptr inbounds i8, ptr %str2, i64 8
  %236 = load i64, ptr %235, align 8
; invoke alloc::str::<impl str>::to_lowercase
  invoke void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$12to_lowercase17h72d8b3d343d0da55E"(ptr sret([24 x i8]) align 8 %_545, ptr align 1 %234, i64 %236)
          to label %bb281 unwind label %funclet_bb491

bb281:                                            ; preds = %bb280
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17haf1723d329d1dc46E(ptr sret([16 x i8]) align 8 %_543, ptr align 8 %_545)
          to label %bb282 unwind label %funclet_bb490

bb490:                                            ; preds = %funclet_bb490
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_545) #18 [ "funclet"(token %cleanuppad35) ]
  cleanupret from %cleanuppad35 unwind label %funclet_bb491

funclet_bb490:                                    ; preds = %bb284, %bb283, %bb282, %bb281
  %cleanuppad35 = cleanuppad within none []
  br label %bb490

bb282:                                            ; preds = %bb281
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_546, ptr align 8 %str3)
          to label %bb283 unwind label %funclet_bb490

bb283:                                            ; preds = %bb282
  %237 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_539, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %237, ptr align 8 %_540, i64 16, i1 false)
  %238 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_539, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %238, ptr align 8 %_543, i64 16, i1 false)
  %239 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_539, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %239, ptr align 8 %_546, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h77d88efe42cf4eccE(ptr sret([48 x i8]) align 8 %_536, ptr align 8 @alloc_4259d2f26910294ca303b2cfb05a622d, ptr align 8 %_539)
          to label %bb284 unwind label %funclet_bb490

bb284:                                            ; preds = %bb283
; invoke alloc::fmt::format
  invoke void @_ZN5alloc3fmt6format17hfa9b33a30e7e0548E(ptr sret([24 x i8]) align 8 %res1, ptr align 8 %_536)
          to label %bb285 unwind label %funclet_bb490

bb285:                                            ; preds = %bb284
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_545)
          to label %bb286 unwind label %funclet_bb488

bb488:                                            ; preds = %funclet_bb488
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_542) #18 [ "funclet"(token %cleanuppad36) ]
  cleanupret from %cleanuppad36 unwind label %funclet_bb489

funclet_bb488:                                    ; preds = %bb285
  %cleanuppad36 = cleanuppad within none []
  br label %bb488

bb286:                                            ; preds = %bb285
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %_542)
          to label %bb287 unwind label %funclet_bb489

bb489:                                            ; preds = %funclet_bb489
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %res1) #18 [ "funclet"(token %cleanuppad37) ]
  cleanupret from %cleanuppad37 unwind label %funclet_bb492

funclet_bb489:                                    ; preds = %bb488, %bb286
  %cleanuppad37 = cleanuppad within none []
  br label %bb489

bb287:                                            ; preds = %bb286
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_534, ptr align 8 %res1, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %str_expr2, ptr align 8 %_534, i64 24, i1 false)
  br label %bb288

bb288:                                            ; preds = %bb287
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_553, ptr align 8 %str1)
          to label %bb289 unwind label %funclet_bb487

bb487:                                            ; preds = %funclet_bb487
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %str_expr2) #18 [ "funclet"(token %cleanuppad38) ]
  cleanupret from %cleanuppad38 unwind label %funclet_bb492

funclet_bb487:                                    ; preds = %bb2.i42, %bb2.i, %bb483, %bb482, %bb481, %bb480, %bb479, %bb478, %bb477, %panic39, %bb475, %bb474, %bb473, %bb472, %bb471, %bb470, %bb469, %bb468, %bb467, %bb466, %bb465, %bb464, %bb463, %bb462, %bb461, %bb460, %bb459, %bb458, %bb457, %bb456, %bb454, %bb453, %bb451, %bb450, %bb449, %bb448, %bb447, %bb446, %bb445, %bb444, %bb443, %bb442, %bb436, %bb435, %bb434, %bb433, %bb432, %bb431, %bb430, %bb429, %bb428, %bb427, %bb426, %bb425, %bb424, %bb423, %bb422, %bb421, %bb420, %bb419, %bb418, %bb417, %bb416, %bb415, %bb441, %bb440, %bb439, %bb438, %bb437, %bb413, %bb412, %bb411, %bb410, %bb409, %bb408, %bb407, %bb406, %bb405, %bb404, %bb403, %bb402, %bb401, %bb400, %bb399, %bb398, %bb397, %bb396, %bb395, %bb394, %bb393, %bb392, %bb391, %bb390, %bb389, %bb388, %bb387, %bb386, %bb385, %bb384, %bb383, %bb382, %bb381, %bb380, %bb379, %bb378, %bb377, %bb376, %bb375, %bb374, %bb373, %bb372, %bb371, %bb370, %bb369, %bb368, %bb367, %bb366, %bb365, %bb364, %bb363, %bb362, %bb361, %bb360, %bb359, %bb358, %bb357, %bb356, %bb355, %bb354, %bb353, %bb352, %bb351, %bb350, %bb349, %bb348, %bb347, %bb346, %bb345, %bb344, %bb343, %bb342, %bb341, %bb340, %bb328, %bb327, %bb326, %bb325, %bb324, %bb323, %bb322, %bb321, %bb320, %bb319, %bb318, %bb317, %bb316, %bb315, %bb314, %bb313, %bb300, %bb299, %bb298, %bb297, %bb296, %bb295, %bb294, %bb293, %bb292, %bb291, %bb290, %bb289, %bb288
  %cleanuppad38 = cleanuppad within none []
  br label %bb487

bb289:                                            ; preds = %bb288
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_555, ptr align 8 %str2)
          to label %bb290 unwind label %funclet_bb487

bb290:                                            ; preds = %bb289
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_557, ptr align 8 %str3)
          to label %bb291 unwind label %funclet_bb487

bb291:                                            ; preds = %bb290
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17haf1723d329d1dc46E(ptr sret([16 x i8]) align 8 %_559, ptr align 8 %str_expr1)
          to label %bb292 unwind label %funclet_bb487

bb292:                                            ; preds = %bb291
  %240 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_552, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %240, ptr align 8 %_553, i64 16, i1 false)
  %241 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_552, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %241, ptr align 8 %_555, i64 16, i1 false)
  %242 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_552, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %242, ptr align 8 %_557, i64 16, i1 false)
  %243 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_552, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %243, ptr align 8 %_559, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_549, ptr align 8 @alloc_9da98b58f4fa525bc5833722a031efb5, ptr align 8 %_552)
          to label %bb293 unwind label %funclet_bb487

bb293:                                            ; preds = %bb292
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_549)
          to label %bb294 unwind label %funclet_bb487

bb294:                                            ; preds = %bb293
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17haf1723d329d1dc46E(ptr sret([16 x i8]) align 8 %_566, ptr align 8 %str_expr2)
          to label %bb295 unwind label %funclet_bb487

bb295:                                            ; preds = %bb294
  %244 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_565, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %244, ptr align 8 %_566, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_562, ptr align 8 @alloc_265b08344f34466dc1fbf2c86068ba5c, ptr align 8 %_565)
          to label %bb296 unwind label %funclet_bb487

bb296:                                            ; preds = %bb295
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_562)
          to label %bb297 unwind label %funclet_bb487

bb297:                                            ; preds = %bb296
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_569, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb298 unwind label %funclet_bb487

bb298:                                            ; preds = %bb297
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_569)
          to label %bb299 unwind label %funclet_bb487

bb299:                                            ; preds = %bb298
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_572, ptr align 8 @alloc_ea9fd32ba7a8d3e547ba47f42b4dc55a)
          to label %bb300 unwind label %funclet_bb487

bb300:                                            ; preds = %bb299
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_572)
          to label %bb301 unwind label %funclet_bb487

bb301:                                            ; preds = %bb300
  store i32 85, ptr %score, align 4
  %245 = load i32, ptr %score, align 4
  %_576 = icmp sge i32 %245, 90
  br i1 %_576, label %bb302, label %bb303

bb303:                                            ; preds = %bb301
  %246 = load i32, ptr %score, align 4
  %_578 = icmp sge i32 %246, 80
  br i1 %_578, label %bb304, label %bb305

bb302:                                            ; preds = %bb301
  store ptr @alloc_e2ead6761956d440a2a6c3412b417ffa, ptr %grade, align 8
  %247 = getelementptr inbounds i8, ptr %grade, i64 8
  store i64 1, ptr %247, align 8
  br label %bb307

bb305:                                            ; preds = %bb303
  store ptr @alloc_e57470275a219d8492d489e56910499e, ptr %_577, align 8
  %248 = getelementptr inbounds i8, ptr %_577, i64 8
  store i64 1, ptr %248, align 8
  br label %bb306

bb304:                                            ; preds = %bb303
  store ptr @alloc_d3bbdebcd7d668a59dc59a90afdc2fa1, ptr %_577, align 8
  %249 = getelementptr inbounds i8, ptr %_577, i64 8
  store i64 1, ptr %249, align 8
  br label %bb306

bb306:                                            ; preds = %bb304, %bb305
  %250 = load ptr, ptr %_577, align 8
  %251 = getelementptr inbounds i8, ptr %_577, i64 8
  %252 = load i64, ptr %251, align 8
  store ptr %250, ptr %grade, align 8
  %253 = getelementptr inbounds i8, ptr %grade, i64 8
  store i64 %252, ptr %253, align 8
  br label %bb307

bb307:                                            ; preds = %bb302, %bb306
  %254 = load i32, ptr %score, align 4
  %_580 = icmp sge i32 %254, 60
  br i1 %_580, label %bb308, label %bb309

bb309:                                            ; preds = %bb307
  store ptr @alloc_dabdbfe0e9e5b3ed7cf46e94448ac36a, ptr %status, align 8
  %255 = getelementptr inbounds i8, ptr %status, i64 8
  store i64 4, ptr %255, align 8
  br label %bb310

bb308:                                            ; preds = %bb307
  store ptr @alloc_5da6b24bde949a094e0a3627f43ac929, ptr %status, align 8
  %256 = getelementptr inbounds i8, ptr %status, i64 8
  store i64 4, ptr %256, align 8
  br label %bb310

bb310:                                            ; preds = %bb308, %bb309
  %257 = load i32, ptr %score, align 4
  %_582 = icmp sgt i32 %257, 95
  br i1 %_582, label %bb311, label %bb312

bb312:                                            ; preds = %bb310
  store i32 0, ptr %bonus, align 4
  br label %bb313

bb311:                                            ; preds = %bb310
  store i32 100, ptr %bonus, align 4
  br label %bb313

bb313:                                            ; preds = %bb311, %bb312
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_588, ptr align 4 %score)
          to label %bb314 unwind label %funclet_bb487

bb314:                                            ; preds = %bb313
  %258 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_587, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %258, ptr align 8 %_588, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_584, ptr align 8 @alloc_1cf0cfa27d966b793d90aa29deba86f7, ptr align 8 %_587)
          to label %bb315 unwind label %funclet_bb487

bb315:                                            ; preds = %bb314
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_584)
          to label %bb316 unwind label %funclet_bb487

bb316:                                            ; preds = %bb315
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_595, ptr align 8 %grade)
          to label %bb317 unwind label %funclet_bb487

bb317:                                            ; preds = %bb316
  %259 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_594, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %259, ptr align 8 %_595, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_591, ptr align 8 @alloc_e090ef3856952b80502bcd1249ebe4ce, ptr align 8 %_594)
          to label %bb318 unwind label %funclet_bb487

bb318:                                            ; preds = %bb317
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_591)
          to label %bb319 unwind label %funclet_bb487

bb319:                                            ; preds = %bb318
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17he97392741ce04843E(ptr sret([16 x i8]) align 8 %_602, ptr align 8 %status)
          to label %bb320 unwind label %funclet_bb487

bb320:                                            ; preds = %bb319
  %260 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_601, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %260, ptr align 8 %_602, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_598, ptr align 8 @alloc_cc40b1d2a73d98ed884c1e7c1a6ae7b4, ptr align 8 %_601)
          to label %bb321 unwind label %funclet_bb487

bb321:                                            ; preds = %bb320
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_598)
          to label %bb322 unwind label %funclet_bb487

bb322:                                            ; preds = %bb321
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_609, ptr align 4 %bonus)
          to label %bb323 unwind label %funclet_bb487

bb323:                                            ; preds = %bb322
  %261 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_608, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %261, ptr align 8 %_609, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_605, ptr align 8 @alloc_bf3ee10a05411f9d03dc0127aaafb64b, ptr align 8 %_608)
          to label %bb324 unwind label %funclet_bb487

bb324:                                            ; preds = %bb323
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_605)
          to label %bb325 unwind label %funclet_bb487

bb325:                                            ; preds = %bb324
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_612, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb326 unwind label %funclet_bb487

bb326:                                            ; preds = %bb325
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_612)
          to label %bb327 unwind label %funclet_bb487

bb327:                                            ; preds = %bb326
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_615, ptr align 8 @alloc_bee085d3de4108e166b431475a32c47c)
          to label %bb328 unwind label %funclet_bb487

bb328:                                            ; preds = %bb327
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_615)
          to label %bb329 unwind label %funclet_bb487

bb329:                                            ; preds = %bb328
  store i32 25, ptr %age, align 4
  store i32 50000, ptr %income, align 4
  store i8 1, ptr %has_job, align 1
  store i32 750, ptr %credit_score, align 4
  %262 = load i32, ptr %age, align 4
  %_622 = icmp sge i32 %262, 18
  br i1 %_622, label %bb330, label %bb334

bb334:                                            ; preds = %bb332, %bb331, %bb330, %bb329
  store i8 0, ptr %loan_eligible, align 1
  br label %bb335

bb330:                                            ; preds = %bb329
  %263 = load i32, ptr %age, align 4
  %_623 = icmp sle i32 %263, 65
  br i1 %_623, label %bb331, label %bb334

bb331:                                            ; preds = %bb330
  %264 = load i32, ptr %income, align 4
  %_624 = icmp sge i32 %264, 30000
  br i1 %_624, label %bb332, label %bb334

bb332:                                            ; preds = %bb331
  %265 = load i8, ptr %has_job, align 1
  %266 = trunc nuw i8 %265 to i1
  br i1 %266, label %bb333, label %bb334

bb333:                                            ; preds = %bb332
  %267 = load i32, ptr %credit_score, align 4
  %268 = icmp sge i32 %267, 700
  %269 = zext i1 %268 to i8
  store i8 %269, ptr %loan_eligible, align 1
  br label %bb335

bb335:                                            ; preds = %bb333, %bb334
  %270 = load i8, ptr %loan_eligible, align 1
  %_626 = trunc nuw i8 %270 to i1
  br i1 %_626, label %bb336, label %bb339

bb339:                                            ; preds = %bb335
  store i32 0, ptr %loan_amount, align 4
  br label %bb340

bb336:                                            ; preds = %bb335
  %271 = load i32, ptr %income, align 4
  %_627 = icmp sgt i32 %271, 80000
  br i1 %_627, label %bb337, label %bb338

bb340:                                            ; preds = %bb337, %bb338, %bb339
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_633, ptr align 4 %age)
          to label %bb341 unwind label %funclet_bb487

bb338:                                            ; preds = %bb336
  store i32 300000, ptr %loan_amount, align 4
  br label %bb340

bb337:                                            ; preds = %bb336
  store i32 500000, ptr %loan_amount, align 4
  br label %bb340

bb341:                                            ; preds = %bb340
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_635, ptr align 4 %income)
          to label %bb342 unwind label %funclet_bb487

bb342:                                            ; preds = %bb341
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_637, ptr align 1 %has_job)
          to label %bb343 unwind label %funclet_bb487

bb343:                                            ; preds = %bb342
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_639, ptr align 4 %credit_score)
          to label %bb344 unwind label %funclet_bb487

bb344:                                            ; preds = %bb343
  %272 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %272, ptr align 8 %_633, i64 16, i1 false)
  %273 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %273, ptr align 8 %_635, i64 16, i1 false)
  %274 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %274, ptr align 8 %_637, i64 16, i1 false)
  %275 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %275, ptr align 8 %_639, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h0edceadac5ba89aaE(ptr sret([48 x i8]) align 8 %_629, ptr align 8 @alloc_6434d4002f1a18ec86e3fa2104286004, ptr align 8 %_632)
          to label %bb345 unwind label %funclet_bb487

bb345:                                            ; preds = %bb344
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_629)
          to label %bb346 unwind label %funclet_bb487

bb346:                                            ; preds = %bb345
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h499058c2baf3615dE(ptr sret([16 x i8]) align 8 %_646, ptr align 1 %loan_eligible)
          to label %bb347 unwind label %funclet_bb487

bb347:                                            ; preds = %bb346
  %276 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_645, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %276, ptr align 8 %_646, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_642, ptr align 8 @alloc_69256651a60b76d1b35eadc7c19013bb, ptr align 8 %_645)
          to label %bb348 unwind label %funclet_bb487

bb348:                                            ; preds = %bb347
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_642)
          to label %bb349 unwind label %funclet_bb487

bb349:                                            ; preds = %bb348
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_653, ptr align 4 %loan_amount)
          to label %bb350 unwind label %funclet_bb487

bb350:                                            ; preds = %bb349
  %277 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_652, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %277, ptr align 8 %_653, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_649, ptr align 8 @alloc_470dbb0e005c3e0560292550f0ea928a, ptr align 8 %_652)
          to label %bb351 unwind label %funclet_bb487

bb351:                                            ; preds = %bb350
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_649)
          to label %bb352 unwind label %funclet_bb487

bb352:                                            ; preds = %bb351
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_656, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb353 unwind label %funclet_bb487

bb353:                                            ; preds = %bb352
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_656)
          to label %bb354 unwind label %funclet_bb487

bb354:                                            ; preds = %bb353
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_659, ptr align 8 @alloc_a2e8401817c1d8434e64a0fe0ec9ee12)
          to label %bb355 unwind label %funclet_bb487

bb355:                                            ; preds = %bb354
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_659)
          to label %bb356 unwind label %funclet_bb487

bb356:                                            ; preds = %bb355
  store double 5.000000e+00, ptr %radius, align 8
  store double 1.000000e+01, ptr %height, align 8
  %278 = load double, ptr %radius, align 8
  %_665 = fmul double 3.141590e+00, %278
  %279 = load double, ptr %radius, align 8
  %280 = fmul double %_665, %279
  store double %280, ptr %circle_area, align 8
  %281 = load double, ptr %radius, align 8
  %_668 = fmul double 3.141590e+00, %281
  %282 = load double, ptr %radius, align 8
  %_667 = fmul double %_668, %282
  %283 = load double, ptr %height, align 8
  %284 = fmul double %_667, %283
  store double %284, ptr %cylinder_volume, align 8
  %285 = load double, ptr %radius, align 8
  %_671 = fmul double 0x4010C1514ABD0449, %285
  %286 = load double, ptr %radius, align 8
  %_670 = fmul double %_671, %286
  %287 = load double, ptr %radius, align 8
  %288 = fmul double %_670, %287
  store double %288, ptr %sphere_volume, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_679, ptr align 8 %radius)
          to label %bb357 unwind label %funclet_bb487

bb357:                                            ; preds = %bb356
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_681, ptr align 8 %height)
          to label %bb358 unwind label %funclet_bb487

bb358:                                            ; preds = %bb357
  %289 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_678, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %289, ptr align 8 %_679, i64 16, i1 false)
  %290 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_678, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %290, ptr align 8 %_681, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h4b9ce4dccea2e245E(ptr sret([48 x i8]) align 8 %_675, ptr align 8 @alloc_72122d5ce80b5c28f4be157be7ce4956, ptr align 8 %_678)
          to label %bb359 unwind label %funclet_bb487

bb359:                                            ; preds = %bb358
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_675)
          to label %bb360 unwind label %funclet_bb487

bb360:                                            ; preds = %bb359
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_690, ptr align 8 %circle_area)
          to label %bb361 unwind label %funclet_bb487

bb361:                                            ; preds = %bb360
  %291 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_689, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %291, ptr align 8 %_690, i64 16, i1 false)
  %292 = getelementptr inbounds i8, ptr %_696, i64 2
  store i16 2, ptr %292, align 2
  store i16 0, ptr %_696, align 8
  store i16 2, ptr %_697, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_695, i64 0, i32 -268435424, ptr align 8 %_696, ptr align 8 %_697)
          to label %bb362 unwind label %funclet_bb487

bb362:                                            ; preds = %bb361
  %293 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_694, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %293, ptr align 8 %_695, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb363 unwind label %funclet_bb487

bb363:                                            ; preds = %bb362
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_684, ptr align 8 @alloc_b54d2dc93c1d4b63ced5269c53b74188, i64 2, ptr align 8 %_689, i64 1, ptr align 8 %_694, i64 1)
          to label %bb364 unwind label %funclet_bb487

bb364:                                            ; preds = %bb363
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_684)
          to label %bb365 unwind label %funclet_bb487

bb365:                                            ; preds = %bb364
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_706, ptr align 8 %cylinder_volume)
          to label %bb366 unwind label %funclet_bb487

bb366:                                            ; preds = %bb365
  %294 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_705, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %294, ptr align 8 %_706, i64 16, i1 false)
  %295 = getelementptr inbounds i8, ptr %_712, i64 2
  store i16 2, ptr %295, align 2
  store i16 0, ptr %_712, align 8
  store i16 2, ptr %_713, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_711, i64 0, i32 -268435424, ptr align 8 %_712, ptr align 8 %_713)
          to label %bb367 unwind label %funclet_bb487

bb367:                                            ; preds = %bb366
  %296 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_710, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %296, ptr align 8 %_711, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb368 unwind label %funclet_bb487

bb368:                                            ; preds = %bb367
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_700, ptr align 8 @alloc_c9b86847efaf5d099fbb5727db1cb872, i64 2, ptr align 8 %_705, i64 1, ptr align 8 %_710, i64 1)
          to label %bb369 unwind label %funclet_bb487

bb369:                                            ; preds = %bb368
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_700)
          to label %bb370 unwind label %funclet_bb487

bb370:                                            ; preds = %bb369
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_722, ptr align 8 %sphere_volume)
          to label %bb371 unwind label %funclet_bb487

bb371:                                            ; preds = %bb370
  %297 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_721, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %297, ptr align 8 %_722, i64 16, i1 false)
  %298 = getelementptr inbounds i8, ptr %_728, i64 2
  store i16 2, ptr %298, align 2
  store i16 0, ptr %_728, align 8
  store i16 2, ptr %_729, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_727, i64 0, i32 -268435424, ptr align 8 %_728, ptr align 8 %_729)
          to label %bb372 unwind label %funclet_bb487

bb372:                                            ; preds = %bb371
  %299 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_726, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %299, ptr align 8 %_727, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb373 unwind label %funclet_bb487

bb373:                                            ; preds = %bb372
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_716, ptr align 8 @alloc_7acc382335ac0a23c1b61f811ecb5958, i64 2, ptr align 8 %_721, i64 1, ptr align 8 %_726, i64 1)
          to label %bb374 unwind label %funclet_bb487

bb374:                                            ; preds = %bb373
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_716)
          to label %bb375 unwind label %funclet_bb487

bb375:                                            ; preds = %bb374
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_732, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb376 unwind label %funclet_bb487

bb376:                                            ; preds = %bb375
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_732)
          to label %bb377 unwind label %funclet_bb487

bb377:                                            ; preds = %bb376
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_735, ptr align 8 @alloc_765455df4fbb4e4a465c71f3f38f84f9)
          to label %bb378 unwind label %funclet_bb487

bb378:                                            ; preds = %bb377
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_735)
          to label %bb379 unwind label %funclet_bb487

bb379:                                            ; preds = %bb378
  store double 1.000000e+03, ptr %principal, align 8
  store double 3.000000e+00, ptr %time, align 8
  store double 1.200000e+01, ptr %n, align 8
  %300 = load double, ptr %n, align 8
  %_744 = fdiv double 5.000000e-02, %300
  %_743 = fadd double 1.000000e+00, %_744
  %301 = load double, ptr %n, align 8
  %302 = load double, ptr %time, align 8
  %_745 = fmul double %301, %302
; invoke std::f64::<impl f64>::powf
  %_742 = invoke double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17hb769435b630f5b5bE"(double %_743, double %_745)
          to label %bb380 unwind label %funclet_bb487

bb380:                                            ; preds = %bb379
  %303 = load double, ptr %principal, align 8
  %304 = fmul double %303, %_742
  store double %304, ptr %amount, align 8
  %305 = load double, ptr %amount, align 8
  %306 = load double, ptr %principal, align 8
  %307 = fsub double %305, %306
  store double %307, ptr %interest, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_754, ptr align 8 %principal)
          to label %bb381 unwind label %funclet_bb487

bb381:                                            ; preds = %bb380
  %308 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_753, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %308, ptr align 8 %_754, i64 16, i1 false)
  %309 = getelementptr inbounds i8, ptr %_760, i64 2
  store i16 2, ptr %309, align 2
  store i16 0, ptr %_760, align 8
  store i16 2, ptr %_761, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_759, i64 0, i32 -268435424, ptr align 8 %_760, ptr align 8 %_761)
          to label %bb382 unwind label %funclet_bb487

bb382:                                            ; preds = %bb381
  %310 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_758, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %310, ptr align 8 %_759, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb383 unwind label %funclet_bb487

bb383:                                            ; preds = %bb382
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_748, ptr align 8 @alloc_f3605924ed5959fc81c8e45edc7a92e7, i64 2, ptr align 8 %_753, i64 1, ptr align 8 %_758, i64 1)
          to label %bb384 unwind label %funclet_bb487

bb384:                                            ; preds = %bb383
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_748)
          to label %bb385 unwind label %funclet_bb487

bb385:                                            ; preds = %bb384
  store double 5.000000e+00, ptr %_772, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_770, ptr align 8 %_772)
          to label %bb386 unwind label %funclet_bb487

bb386:                                            ; preds = %bb385
  %311 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_769, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %311, ptr align 8 %_770, i64 16, i1 false)
  %312 = getelementptr inbounds i8, ptr %_777, i64 2
  store i16 1, ptr %312, align 2
  store i16 0, ptr %_777, align 8
  store i16 2, ptr %_778, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_776, i64 0, i32 -268435424, ptr align 8 %_777, ptr align 8 %_778)
          to label %bb387 unwind label %funclet_bb487

bb387:                                            ; preds = %bb386
  %313 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_775, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %313, ptr align 8 %_776, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb388 unwind label %funclet_bb487

bb388:                                            ; preds = %bb387
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_764, ptr align 8 @alloc_280cc4bd13691fcf8685661ccf810602, i64 2, ptr align 8 %_769, i64 1, ptr align 8 %_775, i64 1)
          to label %bb389 unwind label %funclet_bb487

bb389:                                            ; preds = %bb388
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_764)
          to label %bb390 unwind label %funclet_bb487

bb390:                                            ; preds = %bb389
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_787, ptr align 8 %time)
          to label %bb391 unwind label %funclet_bb487

bb391:                                            ; preds = %bb390
  %314 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_786, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %314, ptr align 8 %_787, i64 16, i1 false)
  %315 = getelementptr inbounds i8, ptr %_793, i64 2
  store i16 0, ptr %315, align 2
  store i16 0, ptr %_793, align 8
  store i16 2, ptr %_794, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_792, i64 0, i32 -268435424, ptr align 8 %_793, ptr align 8 %_794)
          to label %bb392 unwind label %funclet_bb487

bb392:                                            ; preds = %bb391
  %316 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_791, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %316, ptr align 8 %_792, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb393 unwind label %funclet_bb487

bb393:                                            ; preds = %bb392
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_781, ptr align 8 @alloc_ab67f7fe1291fe0d5abc97efdf942547, i64 2, ptr align 8 %_786, i64 1, ptr align 8 %_791, i64 1)
          to label %bb394 unwind label %funclet_bb487

bb394:                                            ; preds = %bb393
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_781)
          to label %bb395 unwind label %funclet_bb487

bb395:                                            ; preds = %bb394
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_803, ptr align 8 %n)
          to label %bb396 unwind label %funclet_bb487

bb396:                                            ; preds = %bb395
  %317 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_802, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %317, ptr align 8 %_803, i64 16, i1 false)
  %318 = getelementptr inbounds i8, ptr %_809, i64 2
  store i16 0, ptr %318, align 2
  store i16 0, ptr %_809, align 8
  store i16 2, ptr %_810, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_808, i64 0, i32 -268435424, ptr align 8 %_809, ptr align 8 %_810)
          to label %bb397 unwind label %funclet_bb487

bb397:                                            ; preds = %bb396
  %319 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_807, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %319, ptr align 8 %_808, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb398 unwind label %funclet_bb487

bb398:                                            ; preds = %bb397
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_797, ptr align 8 @alloc_088e36290ee483694f96bff57a5c27fa, i64 2, ptr align 8 %_802, i64 1, ptr align 8 %_807, i64 1)
          to label %bb399 unwind label %funclet_bb487

bb399:                                            ; preds = %bb398
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_797)
          to label %bb400 unwind label %funclet_bb487

bb400:                                            ; preds = %bb399
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_819, ptr align 8 %amount)
          to label %bb401 unwind label %funclet_bb487

bb401:                                            ; preds = %bb400
  %320 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_818, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %320, ptr align 8 %_819, i64 16, i1 false)
  %321 = getelementptr inbounds i8, ptr %_825, i64 2
  store i16 2, ptr %321, align 2
  store i16 0, ptr %_825, align 8
  store i16 2, ptr %_826, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_824, i64 0, i32 -268435424, ptr align 8 %_825, ptr align 8 %_826)
          to label %bb402 unwind label %funclet_bb487

bb402:                                            ; preds = %bb401
  %322 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_823, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %322, ptr align 8 %_824, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb403 unwind label %funclet_bb487

bb403:                                            ; preds = %bb402
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_813, ptr align 8 @alloc_484598a9a2d17a346ada0a4a52f37371, i64 2, ptr align 8 %_818, i64 1, ptr align 8 %_823, i64 1)
          to label %bb404 unwind label %funclet_bb487

bb404:                                            ; preds = %bb403
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_813)
          to label %bb405 unwind label %funclet_bb487

bb405:                                            ; preds = %bb404
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_835, ptr align 8 %interest)
          to label %bb406 unwind label %funclet_bb487

bb406:                                            ; preds = %bb405
  %323 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_834, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %323, ptr align 8 %_835, i64 16, i1 false)
  %324 = getelementptr inbounds i8, ptr %_841, i64 2
  store i16 2, ptr %324, align 2
  store i16 0, ptr %_841, align 8
  store i16 2, ptr %_842, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_840, i64 0, i32 -268435424, ptr align 8 %_841, ptr align 8 %_842)
          to label %bb407 unwind label %funclet_bb487

bb407:                                            ; preds = %bb406
  %325 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_839, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %325, ptr align 8 %_840, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb408 unwind label %funclet_bb487

bb408:                                            ; preds = %bb407
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_829, ptr align 8 @alloc_024462f0deb68215125012e9bae6f974, i64 2, ptr align 8 %_834, i64 1, ptr align 8 %_839, i64 1)
          to label %bb409 unwind label %funclet_bb487

bb409:                                            ; preds = %bb408
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_829)
          to label %bb410 unwind label %funclet_bb487

bb410:                                            ; preds = %bb409
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_845, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb411 unwind label %funclet_bb487

bb411:                                            ; preds = %bb410
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_845)
          to label %bb412 unwind label %funclet_bb487

bb412:                                            ; preds = %bb411
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_848, ptr align 8 @alloc_a75b41b299785b6ffb1ed77c12554cb8)
          to label %bb413 unwind label %funclet_bb487

bb413:                                            ; preds = %bb412
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_848)
          to label %bb414 unwind label %funclet_bb487

bb414:                                            ; preds = %bb413
  store double 1.000000e+00, ptr %a_coeff, align 8
  store double -5.000000e+00, ptr %b_coeff, align 8
  store double 6.000000e+00, ptr %c_coeff, align 8
  %326 = load double, ptr %b_coeff, align 8
  %327 = load double, ptr %b_coeff, align 8
  %_854 = fmul double %326, %327
  %328 = load double, ptr %a_coeff, align 8
  %_856 = fmul double 4.000000e+00, %328
  %329 = load double, ptr %c_coeff, align 8
  %_855 = fmul double %_856, %329
  %330 = fsub double %_854, %_855
  store double %330, ptr %discriminant, align 8
  %331 = load double, ptr %discriminant, align 8
  %_857 = fcmp oge double %331, 0.000000e+00
  br i1 %_857, label %bb415, label %bb437

bb437:                                            ; preds = %bb414
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_934, ptr align 8 %discriminant)
          to label %bb438 unwind label %funclet_bb487

bb415:                                            ; preds = %bb414
  %332 = load double, ptr %b_coeff, align 8
  %_860 = fneg double %332
  %333 = load double, ptr %discriminant, align 8
; invoke std::f64::<impl f64>::sqrt
  %_861 = invoke double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17h7044fc1fbcfbb1b6E"(double %333)
          to label %bb416 unwind label %funclet_bb487

bb438:                                            ; preds = %bb437
  %334 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_933, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %334, ptr align 8 %_934, i64 16, i1 false)
  %335 = getelementptr inbounds i8, ptr %_940, i64 2
  store i16 2, ptr %335, align 2
  store i16 0, ptr %_940, align 8
  store i16 2, ptr %_941, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_939, i64 0, i32 -268435424, ptr align 8 %_940, ptr align 8 %_941)
          to label %bb439 unwind label %funclet_bb487

bb439:                                            ; preds = %bb438
  %336 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_938, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %336, ptr align 8 %_939, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb440 unwind label %funclet_bb487

bb440:                                            ; preds = %bb439
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_928, ptr align 8 @alloc_caa20d8d2d1a386884338e46f7c06f22, i64 2, ptr align 8 %_933, i64 1, ptr align 8 %_938, i64 1)
          to label %bb441 unwind label %funclet_bb487

bb441:                                            ; preds = %bb440
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_928)
          to label %bb495 unwind label %funclet_bb487

bb495:                                            ; preds = %bb441
  br label %bb442

bb442:                                            ; preds = %bb494, %bb495
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_944, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb443 unwind label %funclet_bb487

bb416:                                            ; preds = %bb415
  %_859 = fadd double %_860, %_861
  %337 = load double, ptr %a_coeff, align 8
  %_862 = fmul double 2.000000e+00, %337
  %338 = fdiv double %_859, %_862
  store double %338, ptr %root1, align 8
  %339 = load double, ptr %b_coeff, align 8
  %_865 = fneg double %339
  %340 = load double, ptr %discriminant, align 8
; invoke std::f64::<impl f64>::sqrt
  %_866 = invoke double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17h7044fc1fbcfbb1b6E"(double %340)
          to label %bb417 unwind label %funclet_bb487

bb417:                                            ; preds = %bb416
  %_864 = fsub double %_865, %_866
  %341 = load double, ptr %a_coeff, align 8
  %_867 = fmul double 2.000000e+00, %341
  %342 = fdiv double %_864, %_867
  store double %342, ptr %root2, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_873, ptr align 8 %a_coeff)
          to label %bb418 unwind label %funclet_bb487

bb418:                                            ; preds = %bb417
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_875, ptr align 8 %b_coeff)
          to label %bb419 unwind label %funclet_bb487

bb419:                                            ; preds = %bb418
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_877, ptr align 8 %c_coeff)
          to label %bb420 unwind label %funclet_bb487

bb420:                                            ; preds = %bb419
  %343 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_872, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %343, ptr align 8 %_873, i64 16, i1 false)
  %344 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_872, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %344, ptr align 8 %_875, i64 16, i1 false)
  %345 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_872, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %345, ptr align 8 %_877, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hb2c2cec496703428E(ptr sret([48 x i8]) align 8 %_869, ptr align 8 @alloc_773bcb544d5517a5957bf6f9b6e2778b, ptr align 8 %_872)
          to label %bb421 unwind label %funclet_bb487

bb421:                                            ; preds = %bb420
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_869)
          to label %bb422 unwind label %funclet_bb487

bb422:                                            ; preds = %bb421
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_886, ptr align 8 %discriminant)
          to label %bb423 unwind label %funclet_bb487

bb423:                                            ; preds = %bb422
  %346 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_885, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %346, ptr align 8 %_886, i64 16, i1 false)
  %347 = getelementptr inbounds i8, ptr %_892, i64 2
  store i16 2, ptr %347, align 2
  store i16 0, ptr %_892, align 8
  store i16 2, ptr %_893, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_891, i64 0, i32 -268435424, ptr align 8 %_892, ptr align 8 %_893)
          to label %bb424 unwind label %funclet_bb487

bb424:                                            ; preds = %bb423
  %348 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_890, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %348, ptr align 8 %_891, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb425 unwind label %funclet_bb487

bb425:                                            ; preds = %bb424
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_880, ptr align 8 @alloc_1abd9def04fe91dc3f020712e62c09f0, i64 2, ptr align 8 %_885, i64 1, ptr align 8 %_890, i64 1)
          to label %bb426 unwind label %funclet_bb487

bb426:                                            ; preds = %bb425
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_880)
          to label %bb427 unwind label %funclet_bb487

bb427:                                            ; preds = %bb426
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_902, ptr align 8 %root1)
          to label %bb428 unwind label %funclet_bb487

bb428:                                            ; preds = %bb427
  %349 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_901, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %349, ptr align 8 %_902, i64 16, i1 false)
  %350 = getelementptr inbounds i8, ptr %_908, i64 2
  store i16 2, ptr %350, align 2
  store i16 0, ptr %_908, align 8
  store i16 2, ptr %_909, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_907, i64 0, i32 -268435424, ptr align 8 %_908, ptr align 8 %_909)
          to label %bb429 unwind label %funclet_bb487

bb429:                                            ; preds = %bb428
  %351 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_906, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %351, ptr align 8 %_907, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb430 unwind label %funclet_bb487

bb430:                                            ; preds = %bb429
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_896, ptr align 8 @alloc_46daf9f2f111acb9e5443e1371d31f4a, i64 2, ptr align 8 %_901, i64 1, ptr align 8 %_906, i64 1)
          to label %bb431 unwind label %funclet_bb487

bb431:                                            ; preds = %bb430
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_896)
          to label %bb432 unwind label %funclet_bb487

bb432:                                            ; preds = %bb431
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_918, ptr align 8 %root2)
          to label %bb433 unwind label %funclet_bb487

bb433:                                            ; preds = %bb432
  %352 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_917, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %352, ptr align 8 %_918, i64 16, i1 false)
  %353 = getelementptr inbounds i8, ptr %_924, i64 2
  store i16 2, ptr %353, align 2
  store i16 0, ptr %_924, align 8
  store i16 2, ptr %_925, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_923, i64 0, i32 -268435424, ptr align 8 %_924, ptr align 8 %_925)
          to label %bb434 unwind label %funclet_bb487

bb434:                                            ; preds = %bb433
  %354 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_922, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %354, ptr align 8 %_923, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb435 unwind label %funclet_bb487

bb435:                                            ; preds = %bb434
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_912, ptr align 8 @alloc_0f7d3133d3a337687dbde27bdce296f9, i64 2, ptr align 8 %_917, i64 1, ptr align 8 %_922, i64 1)
          to label %bb436 unwind label %funclet_bb487

bb436:                                            ; preds = %bb435
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_912)
          to label %bb494 unwind label %funclet_bb487

bb494:                                            ; preds = %bb436
  br label %bb442

bb443:                                            ; preds = %bb442
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_944)
          to label %bb444 unwind label %funclet_bb487

bb444:                                            ; preds = %bb443
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_947, ptr align 8 @alloc_c74fd58df1e47fefedad42253dd34d31)
          to label %bb445 unwind label %funclet_bb487

bb445:                                            ; preds = %bb444
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_947)
          to label %bb446 unwind label %funclet_bb487

bb446:                                            ; preds = %bb445
  %355 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
  store i32 1, ptr %355, align 4
  %356 = getelementptr inbounds nuw i32, ptr %numbers, i64 1
  store i32 2, ptr %356, align 4
  %357 = getelementptr inbounds nuw i32, ptr %numbers, i64 2
  store i32 3, ptr %357, align 4
  %358 = getelementptr inbounds nuw i32, ptr %numbers, i64 3
  store i32 4, ptr %358, align 4
  %359 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
  store i32 5, ptr %359, align 4
  %360 = getelementptr inbounds nuw i32, ptr %numbers, i64 5
  store i32 6, ptr %360, align 4
  %361 = getelementptr inbounds nuw i32, ptr %numbers, i64 6
  store i32 7, ptr %361, align 4
  %362 = getelementptr inbounds nuw i32, ptr %numbers, i64 7
  store i32 8, ptr %362, align 4
  %363 = getelementptr inbounds nuw i32, ptr %numbers, i64 8
  store i32 9, ptr %363, align 4
  %364 = getelementptr inbounds nuw i32, ptr %numbers, i64 9
  store i32 10, ptr %364, align 4
; invoke core::slice::<impl [T]>::iter
  %365 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdea02c331404f766E"(ptr align 4 %numbers, i64 10)
          to label %bb447 unwind label %funclet_bb487

bb447:                                            ; preds = %bb446
  %_951.0 = extractvalue { ptr, ptr } %365, 0
  %_951.1 = extractvalue { ptr, ptr } %365, 1
; invoke core::iter::traits::iterator::Iterator::sum
  %366 = invoke i32 @_ZN4core4iter6traits8iterator8Iterator3sum17h1e482a0f11f974bfE(ptr %_951.0, ptr %_951.1)
          to label %bb448 unwind label %funclet_bb487

bb448:                                            ; preds = %bb447
  store i32 %366, ptr %sum, align 4
; invoke core::slice::<impl [T]>::iter
  %367 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdea02c331404f766E"(ptr align 4 %numbers, i64 10)
          to label %bb449 unwind label %funclet_bb487

bb449:                                            ; preds = %bb448
  %_955.0 = extractvalue { ptr, ptr } %367, 0
  %_955.1 = extractvalue { ptr, ptr } %367, 1
; invoke core::iter::traits::iterator::Iterator::product
  %368 = invoke i32 @_ZN4core4iter6traits8iterator8Iterator7product17h5bb341e8c1b7cf17E(ptr %_955.0, ptr %_955.1)
          to label %bb450 unwind label %funclet_bb487

bb450:                                            ; preds = %bb449
  store i32 %368, ptr %product, align 4
  %369 = load i32, ptr %sum, align 4
  %_959 = sitofp i32 %369 to double
  %370 = fdiv double %_959, 1.000000e+01
  store double %370, ptr %average, align 8
; invoke core::slice::<impl [T]>::iter
  %371 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdea02c331404f766E"(ptr align 4 %numbers, i64 10)
          to label %bb451 unwind label %funclet_bb487

bb451:                                            ; preds = %bb450
  %_967.0 = extractvalue { ptr, ptr } %371, 0
  %_967.1 = extractvalue { ptr, ptr } %371, 1
; invoke core::iter::traits::iterator::Iterator::max
  %_966 = invoke align 4 ptr @_ZN4core4iter6traits8iterator8Iterator3max17h24ec3c72f0637502E(ptr %_967.0, ptr %_967.1)
          to label %bb452 unwind label %funclet_bb487

bb452:                                            ; preds = %bb451
  store ptr %_966, ptr %self.i40, align 8
  %372 = load ptr, ptr %self.i40, align 8
  %373 = ptrtoint ptr %372 to i64
  %374 = icmp eq i64 %373, 0
  %_2.i41 = select i1 %374, i64 0, i64 1
  %375 = trunc nuw i64 %_2.i41 to i1
  br i1 %375, label %"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit45", label %bb2.i42

bb2.i42:                                          ; preds = %bb452
; invoke core::option::unwrap_failed
  invoke void @_ZN4core6option13unwrap_failed17hcd806f704fe1a5cbE(ptr align 8 @alloc_0d79f432d3df6806890e394de42d7793) #15
          to label %.noexc44 unwind label %funclet_bb487

.noexc44:                                         ; preds = %bb2.i42
  unreachable

"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit45": ; preds = %bb452
  %val.i43 = load ptr, ptr %self.i40, align 8
  br label %bb453

bb453:                                            ; preds = %"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit45"
  %376 = load i32, ptr %val.i43, align 4
  store i32 %376, ptr %max_val, align 4
; invoke core::slice::<impl [T]>::iter
  %377 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdea02c331404f766E"(ptr align 4 %numbers, i64 10)
          to label %bb454 unwind label %funclet_bb487

bb454:                                            ; preds = %bb453
  %_973.0 = extractvalue { ptr, ptr } %377, 0
  %_973.1 = extractvalue { ptr, ptr } %377, 1
; invoke core::iter::traits::iterator::Iterator::min
  %_972 = invoke align 4 ptr @_ZN4core4iter6traits8iterator8Iterator3min17h9b56df02fed012a5E(ptr %_973.0, ptr %_973.1)
          to label %bb455 unwind label %funclet_bb487

bb455:                                            ; preds = %bb454
  store ptr %_972, ptr %self.i, align 8
  %378 = load ptr, ptr %self.i, align 8
  %379 = ptrtoint ptr %378 to i64
  %380 = icmp eq i64 %379, 0
  %_2.i = select i1 %380, i64 0, i64 1
  %381 = trunc nuw i64 %_2.i to i1
  br i1 %381, label %"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit", label %bb2.i

bb2.i:                                            ; preds = %bb455
; invoke core::option::unwrap_failed
  invoke void @_ZN4core6option13unwrap_failed17hcd806f704fe1a5cbE(ptr align 8 @alloc_1c072410cfb5c78f476795683b7d7dff) #15
          to label %.noexc unwind label %funclet_bb487

.noexc:                                           ; preds = %bb2.i
  unreachable

"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit": ; preds = %bb455
  %val.i = load ptr, ptr %self.i, align 8
  br label %bb456

bb456:                                            ; preds = %"_ZN4core6option15Option$LT$T$GT$6unwrap17hbd055b6d7635943aE.exit"
  %382 = load i32, ptr %val.i, align 4
  store i32 %382, ptr %min_val, align 4
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17hb12e034bd56b9962E(ptr sret([16 x i8]) align 8 %_981, ptr align 4 %numbers)
          to label %bb457 unwind label %funclet_bb487

bb457:                                            ; preds = %bb456
  %383 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_980, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %383, ptr align 8 %_981, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_977, ptr align 8 @alloc_c2ea8befab079717f7857c3672236466, ptr align 8 %_980)
          to label %bb458 unwind label %funclet_bb487

bb458:                                            ; preds = %bb457
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_977)
          to label %bb459 unwind label %funclet_bb487

bb459:                                            ; preds = %bb458
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_988, ptr align 4 %sum)
          to label %bb460 unwind label %funclet_bb487

bb460:                                            ; preds = %bb459
  %384 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_987, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %384, ptr align 8 %_988, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_984, ptr align 8 @alloc_fae7b78f3403b48b2870d7d78fcc4226, ptr align 8 %_987)
          to label %bb461 unwind label %funclet_bb487

bb461:                                            ; preds = %bb460
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_984)
          to label %bb462 unwind label %funclet_bb487

bb462:                                            ; preds = %bb461
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_995, ptr align 4 %product)
          to label %bb463 unwind label %funclet_bb487

bb463:                                            ; preds = %bb462
  %385 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_994, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %385, ptr align 8 %_995, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_991, ptr align 8 @alloc_5a4976cd5630db43316aecd3ccde6279, ptr align 8 %_994)
          to label %bb464 unwind label %funclet_bb487

bb464:                                            ; preds = %bb463
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_991)
          to label %bb465 unwind label %funclet_bb487

bb465:                                            ; preds = %bb464
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7c808a5e17a122d8E(ptr sret([16 x i8]) align 8 %_1004, ptr align 8 %average)
          to label %bb466 unwind label %funclet_bb487

bb466:                                            ; preds = %bb465
  %386 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1003, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %386, ptr align 8 %_1004, i64 16, i1 false)
  %387 = getelementptr inbounds i8, ptr %_1010, i64 2
  store i16 2, ptr %387, align 2
  store i16 0, ptr %_1010, align 8
  store i16 2, ptr %_1011, align 8
; invoke core::fmt::rt::Placeholder::new
  invoke void @_ZN4core3fmt2rt11Placeholder3new17h6b95670afd700a55E(ptr sret([48 x i8]) align 8 %_1009, i64 0, i32 -268435424, ptr align 8 %_1010, ptr align 8 %_1011)
          to label %bb467 unwind label %funclet_bb487

bb467:                                            ; preds = %bb466
  %388 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1008, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %388, ptr align 8 %_1009, i64 48, i1 false)
; invoke core::fmt::rt::UnsafeArg::new
  invoke void @_ZN4core3fmt2rt9UnsafeArg3new17h3eaf1ff098081fefE()
          to label %bb468 unwind label %funclet_bb487

bb468:                                            ; preds = %bb467
; invoke core::fmt::Arguments::new_v1_formatted
  invoke void @_ZN4core3fmt9Arguments16new_v1_formatted17h19658fbdf8fd43a5E(ptr sret([48 x i8]) align 8 %_998, ptr align 8 @alloc_e93491e3b40beb615f607f92c37da4a2, i64 2, ptr align 8 %_1003, i64 1, ptr align 8 %_1008, i64 1)
          to label %bb469 unwind label %funclet_bb487

bb469:                                            ; preds = %bb468
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_998)
          to label %bb470 unwind label %funclet_bb487

bb470:                                            ; preds = %bb469
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_1018, ptr align 4 %max_val)
          to label %bb471 unwind label %funclet_bb487

bb471:                                            ; preds = %bb470
  %389 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1017, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %389, ptr align 8 %_1018, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_1014, ptr align 8 @alloc_3fed9fdde7e6ef1b81519ccb53d625ea, ptr align 8 %_1017)
          to label %bb472 unwind label %funclet_bb487

bb472:                                            ; preds = %bb471
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1014)
          to label %bb473 unwind label %funclet_bb487

bb473:                                            ; preds = %bb472
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_1025, ptr align 4 %min_val)
          to label %bb474 unwind label %funclet_bb487

bb474:                                            ; preds = %bb473
  %390 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1024, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %390, ptr align 8 %_1025, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_1021, ptr align 8 @alloc_7ded8a01a82054e8f58ce74272b5b106, ptr align 8 %_1024)
          to label %bb475 unwind label %funclet_bb487

bb475:                                            ; preds = %bb474
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1021)
          to label %bb476 unwind label %funclet_bb487

bb476:                                            ; preds = %bb475
  %391 = load i32, ptr %max_val, align 4
  %392 = load i32, ptr %min_val, align 4
  %393 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %391, i32 %392)
  %_1035.0 = extractvalue { i32, i1 } %393, 0
  %_1035.1 = extractvalue { i32, i1 } %393, 1
  br i1 %_1035.1, label %panic39, label %bb477

bb477:                                            ; preds = %bb476
  store i32 %_1035.0, ptr %_1034, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h7a08d35c552c72c7E(ptr sret([16 x i8]) align 8 %_1032, ptr align 4 %_1034)
          to label %bb478 unwind label %funclet_bb487

panic39:                                          ; preds = %bb476
; invoke core::panicking::panic_const::panic_const_sub_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_8100a3c8c45e35b9b8b1d6a79802b479) #15
          to label %unreachable unwind label %funclet_bb487

unreachable:                                      ; preds = %panic39
  unreachable

bb478:                                            ; preds = %bb477
  %394 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1031, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %394, ptr align 8 %_1032, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h731e8d8909141db9E(ptr sret([48 x i8]) align 8 %_1028, ptr align 8 @alloc_7a517152eb3d8bddd47643ba929d2459, ptr align 8 %_1031)
          to label %bb479 unwind label %funclet_bb487

bb479:                                            ; preds = %bb478
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1028)
          to label %bb480 unwind label %funclet_bb487

bb480:                                            ; preds = %bb479
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_1037, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb481 unwind label %funclet_bb487

bb481:                                            ; preds = %bb480
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1037)
          to label %bb482 unwind label %funclet_bb487

bb482:                                            ; preds = %bb481
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h285be35d7f64db10E(ptr sret([48 x i8]) align 8 %_1040, ptr align 8 @alloc_c318ab582d15789fa478dcc21017cef5)
          to label %bb483 unwind label %funclet_bb487

bb483:                                            ; preds = %bb482
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1040)
          to label %bb484 unwind label %funclet_bb487

bb484:                                            ; preds = %bb483
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %str_expr2)
          to label %bb485 unwind label %funclet_bb492

bb485:                                            ; preds = %bb484
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hf163b58f12204a96E"(ptr align 8 %str_expr1)
  ret void
}

; _14_complex_expressions::add
; Function Attrs: uwtable
define internal i32 @_ZN23_14_complex_expressions3add17hc8bafc426c06af54E(i32 %a, i32 %b) unnamed_addr #0 {
start:
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %a, i32 %b)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_1c33ef6b15b55eabd3c7a80119b2a86b) #15
  unreachable
}

; _14_complex_expressions::subtract
; Function Attrs: uwtable
define internal i32 @_ZN23_14_complex_expressions8subtract17hff60853082737390E(i32 %a, i32 %b) unnamed_addr #0 {
start:
  %0 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %a, i32 %b)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_882aa4664372adbd6ebabb5a39b3cb21) #15
  unreachable
}

; _14_complex_expressions::multiply
; Function Attrs: uwtable
define internal i32 @_ZN23_14_complex_expressions8multiply17he62b7dc3bd29cea7E(i32 %a, i32 %b) unnamed_addr #0 {
start:
  %0 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %a, i32 %b)
  %_3.0 = extractvalue { i32, i1 } %0, 0
  %_3.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_3.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_7ebbfff03df90df7ab1b39d9ff787f1f) #15
  unreachable
}

; _14_complex_expressions::divide
; Function Attrs: uwtable
define internal i32 @_ZN23_14_complex_expressions6divide17h2d733e1ee658214fE(i32 %a, i32 %b) unnamed_addr #0 {
start:
  %_3 = icmp eq i32 %b, 0
  br i1 %_3, label %panic, label %bb1

bb1:                                              ; preds = %start
  %_4 = icmp eq i32 %b, -1
  %_5 = icmp eq i32 %a, -2147483648
  %_6 = and i1 %_4, %_5
  br i1 %_6, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_c9d173d9c9fa614c31af073df1b8a79b) #15
  unreachable

bb2:                                              ; preds = %bb1
  %_0 = sdiv i32 %a, %b
  ret i32 %_0

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_c9d173d9c9fa614c31af073df1b8a79b) #15
  unreachable
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.pow.f64(double, double) #5

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.sqrt.f64(double) #5

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #0

; core::fmt::Formatter::debug_list
; Function Attrs: uwtable
declare void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::finish
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8) unnamed_addr #0

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #5

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #7

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i8 @llvm.scmp.i8.i32(i32, i32) #5

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #10

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::float::<impl core::fmt::Display for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E"(ptr align 8, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::entry
; Function Attrs: uwtable
declare align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8, ptr align 1, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #5

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #5

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #0

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; core::option::unwrap_failed
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core6option13unwrap_failed17hcd806f704fe1a5cbE(ptr align 8) unnamed_addr #8

; alloc::fmt::format::format_inner
; Function Attrs: uwtable
declare void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8, ptr align 8) unnamed_addr #0

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #11

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #12

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #13

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #14

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #8

; alloc::str::<impl str>::to_uppercase
; Function Attrs: uwtable
declare void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$12to_uppercase17h024e81649237501cE"(ptr sret([24 x i8]) align 8, ptr align 1, i64) unnamed_addr #0

; alloc::str::<impl str>::to_lowercase
; Function Attrs: uwtable
declare void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$12to_lowercase17h72d8b3d343d0da55E"(ptr sret([24 x i8]) align 8, ptr align 1, i64) unnamed_addr #0

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hc704dace1bbd4a87E(ptr @_ZN23_14_complex_expressions4main17h33b999dc8a25de6bE, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #10 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #11 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { noreturn }
attributes #16 = { cold noreturn nounwind }
attributes #17 = { noreturn nounwind }
attributes #18 = { cold }
attributes #19 = { nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 6140239866613857}
