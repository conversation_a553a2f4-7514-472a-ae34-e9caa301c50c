; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 13 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"World\0A\00", align 1
@.str_2 = private unnamed_addr constant [13 x i8] c"Line1\0ALine2\0A\00", align 1
@.str_3 = private unnamed_addr constant [11 x i8] c"Col1\09Col2\0A\00", align 1
@.str_4 = private unnamed_addr constant [8 x i8] c"Answer\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [11 x i8] c"Combined: \00", align 1
@.str_expr_1 = private unnamed_addr constant [15 x i8] c"Newline test: \00", align 1
@.str_expr_2 = private unnamed_addr constant [11 x i8] c"Tab test: \00", align 1
@.str_expr_3 = private unnamed_addr constant [8 x i8] c"Mixed: \00", align 1
@.str_expr_4 = private unnamed_addr constant [5 x i8] c" is \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1
@str_dyn_0 = private unnamed_addr constant [13 x i8] c"Line1\\nLine2\00", align 1
@str_dyn_1 = private unnamed_addr constant [11 x i8] c"Col1\\tCol2\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_part1 = global i8* null, align 8
@global_part2 = global i8* null, align 8
@global_combined = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_with_newline = global i8* null, align 8
@global_with_tab = global i8* null, align 8
@global_number = global i32 0, align 4
@global_text = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== SIMPLE TEST ===" (inline)
  %tmp_1_str = alloca [20 x i8], align 1
  store [20 x i8] c"=== SIMPLE TEST ===\00", [20 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [20 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string part1 = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_part1, align 8
  ; string part2 = "World"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_part2, align 8
  ; string combined = part1 + part2
  ; string assignment from variable/expression: combined = part1 + part2
  ; print expression: "Combined: " + combined
  %tmp_3_print_str = alloca [11 x i8], align 1
  %tmp_4 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 0
  store i8 67, i8* %tmp_4, align 1
  %tmp_5 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 1
  store i8 111, i8* %tmp_5, align 1
  %tmp_6 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 2
  store i8 109, i8* %tmp_6, align 1
  %tmp_7 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 3
  store i8 98, i8* %tmp_7, align 1
  %tmp_8 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 4
  store i8 105, i8* %tmp_8, align 1
  %tmp_9 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 5
  store i8 110, i8* %tmp_9, align 1
  %tmp_10 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 6
  store i8 101, i8* %tmp_10, align 1
  %tmp_11 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 7
  store i8 100, i8* %tmp_11, align 1
  %tmp_12 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 8
  store i8 58, i8* %tmp_12, align 1
  %tmp_13 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 9
  store i8 32, i8* %tmp_13, align 1
  %tmp_14 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_3_print_str, i64 0, i64 10
  store i8 0, i8* %tmp_14, align 1
  %tmp_15 = bitcast [11 x i8]* %tmp_3_print_str to i8*
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_15)
  %tmp_16 = load i8*, i8** @global_combined, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string with_newline = "Line1\nLine2"
  store i8* getelementptr inbounds ([13 x i8], [13 x i8]* @str_dyn_0, i64 0, i64 0), i8** @global_with_newline, align 8
  ; string with_tab = "Col1\tCol2"
  store i8* getelementptr inbounds ([11 x i8], [11 x i8]* @str_dyn_1, i64 0, i64 0), i8** @global_with_tab, align 8
  ; print expression: "Newline test: " + with_newline
  %tmp_17_print_str = alloca [15 x i8], align 1
  %tmp_18 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 0
  store i8 78, i8* %tmp_18, align 1
  %tmp_19 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 1
  store i8 101, i8* %tmp_19, align 1
  %tmp_20 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 2
  store i8 119, i8* %tmp_20, align 1
  %tmp_21 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 3
  store i8 108, i8* %tmp_21, align 1
  %tmp_22 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 4
  store i8 105, i8* %tmp_22, align 1
  %tmp_23 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 5
  store i8 110, i8* %tmp_23, align 1
  %tmp_24 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 6
  store i8 101, i8* %tmp_24, align 1
  %tmp_25 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 7
  store i8 32, i8* %tmp_25, align 1
  %tmp_26 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 8
  store i8 116, i8* %tmp_26, align 1
  %tmp_27 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 9
  store i8 101, i8* %tmp_27, align 1
  %tmp_28 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 10
  store i8 115, i8* %tmp_28, align 1
  %tmp_29 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 11
  store i8 116, i8* %tmp_29, align 1
  %tmp_30 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 12
  store i8 58, i8* %tmp_30, align 1
  %tmp_31 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 13
  store i8 32, i8* %tmp_31, align 1
  %tmp_32 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_17_print_str, i64 0, i64 14
  store i8 0, i8* %tmp_32, align 1
  %tmp_33 = bitcast [15 x i8]* %tmp_17_print_str to i8*
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_33)
  %tmp_34 = load i8*, i8** @global_with_newline, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Tab test: " + with_tab
  %tmp_35_print_str = alloca [11 x i8], align 1
  %tmp_36 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 0
  store i8 84, i8* %tmp_36, align 1
  %tmp_37 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 1
  store i8 97, i8* %tmp_37, align 1
  %tmp_38 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 2
  store i8 98, i8* %tmp_38, align 1
  %tmp_39 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 3
  store i8 32, i8* %tmp_39, align 1
  %tmp_40 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 4
  store i8 116, i8* %tmp_40, align 1
  %tmp_41 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 5
  store i8 101, i8* %tmp_41, align 1
  %tmp_42 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 6
  store i8 115, i8* %tmp_42, align 1
  %tmp_43 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 7
  store i8 116, i8* %tmp_43, align 1
  %tmp_44 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 8
  store i8 58, i8* %tmp_44, align 1
  %tmp_45 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 9
  store i8 32, i8* %tmp_45, align 1
  %tmp_46 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_35_print_str, i64 0, i64 10
  store i8 0, i8* %tmp_46, align 1
  %tmp_47 = bitcast [11 x i8]* %tmp_35_print_str to i8*
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_47)
  %tmp_48 = load i8*, i8** @global_with_tab, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_48)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int number = 42
  store i32 42, i32* @global_number, align 4
  ; string text = "Answer"
  store i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_4, i64 0, i64 0), i8** @global_text, align 8
  ; print expression: "Mixed: " + text + " is " + number
  %tmp_49_print_str = alloca [8 x i8], align 1
  %tmp_50 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 0
  store i8 77, i8* %tmp_50, align 1
  %tmp_51 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 1
  store i8 105, i8* %tmp_51, align 1
  %tmp_52 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 2
  store i8 120, i8* %tmp_52, align 1
  %tmp_53 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 3
  store i8 101, i8* %tmp_53, align 1
  %tmp_54 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 4
  store i8 100, i8* %tmp_54, align 1
  %tmp_55 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 5
  store i8 58, i8* %tmp_55, align 1
  %tmp_56 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 6
  store i8 32, i8* %tmp_56, align 1
  %tmp_57 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_49_print_str, i64 0, i64 7
  store i8 0, i8* %tmp_57, align 1
  %tmp_58 = bitcast [8 x i8]* %tmp_49_print_str to i8*
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_58)
  %tmp_59 = load i8*, i8** @global_text, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_59)
  %tmp_60_print_str = alloca [5 x i8], align 1
  %tmp_61 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_60_print_str, i64 0, i64 0
  store i8 32, i8* %tmp_61, align 1
  %tmp_62 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_60_print_str, i64 0, i64 1
  store i8 105, i8* %tmp_62, align 1
  %tmp_63 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_60_print_str, i64 0, i64 2
  store i8 115, i8* %tmp_63, align 1
  %tmp_64 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_60_print_str, i64 0, i64 3
  store i8 32, i8* %tmp_64, align 1
  %tmp_65 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_60_print_str, i64 0, i64 4
  store i8 0, i8* %tmp_65, align 1
  %tmp_66 = bitcast [5 x i8]* %tmp_60_print_str to i8*
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_66)
  %tmp_67 = load i32, i32* @global_number, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_67)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== END TEST ===" (inline)
  %tmp_68_str = alloca [17 x i8], align 1
  store [17 x i8] c"=== END TEST ===\00", [17 x i8]* %tmp_68_str, align 1
  %tmp_69 = bitcast [17 x i8]* %tmp_68_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_69)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
