# 14. Constants and Special Values - Fixed Values and Literals
# This file demonstrates constants and special values in Dolet

print "=== Constants and Special Values Demo ==="
print ""

# Numeric constants
print "Numeric Constants:"
const int MAX_SIZE = 100
const int MIN_SIZE = 1
const int DEFAULT_SIZE = 10

print "MAX_SIZE = " + MAX_SIZE
print "MIN_SIZE = " + MIN_SIZE  
print "DEFAULT_SIZE = " + DEFAULT_SIZE
print ""

# Mathematical constants
print "Mathematical Constants:"
const float PI = 3.14159265359
const float E = 2.71828182846
const float GOLDEN_RATIO = 1.61803398875

print "PI = " + PI
print "E = " + E
print "GOLDEN_RATIO = " + GOLDEN_RATIO
print ""

# String constants
print "String Constants:"
const string COMPANY_NAME = "TechCorp Solutions"
const string VERSION = "2.1.0"
const string COPYRIGHT = "2024 All Rights Reserved"
const string EMPTY_STRING = ""

print "COMPANY_NAME = " + COMPANY_NAME
print "VERSION = " + VERSION
print "COPYRIGHT = " + COPYRIGHT
print "EMPTY_STRING = '" + EMPTY_STRING + "'"
print ""

# Boolean constants
print "Boolean Constants:"
const bool DEBUG_MODE = true
const bool PRODUCTION_MODE = false
const bool LOGGING_ENABLED = true

print "DEBUG_MODE = " + DEBUG_MODE
print "PRODUCTION_MODE = " + PRODUCTION_MODE
print "LOGGING_ENABLED = " + LOGGING_ENABLED
print ""

# Character constants
print "Character Constants:"
const char SEPARATOR = ','
const char NEWLINE = '\n'
const char TAB = '\t'
const char SPACE = ' '

print "SEPARATOR = '" + SEPARATOR + "'"
print "NEWLINE = '\\n'"
print "TAB = '\\t'"
print "SPACE = '" + SPACE + "'"
print ""

# Special numeric values
print "Special Numeric Values:"
int zero = 0
int positive_max = 2147483647  # Maximum 32-bit integer
int negative_max = -2147483648 # Minimum 32-bit integer

float zero_float = 0.0
float positive_infinity = *********.0  # Simulated infinity
float negative_infinity = -*********.0 # Simulated negative infinity

print "zero = " + zero
print "positive_max = " + positive_max
print "negative_max = " + negative_max
print "zero_float = " + zero_float
print "positive_infinity = " + positive_infinity
print "negative_infinity = " + negative_infinity
print ""

# Null and undefined values
print "Null and Undefined Values:"
string null_string = ""
int null_int = 0
bool null_bool = false

print "null_string = '" + null_string + "'"
print "null_int = " + null_int
print "null_bool = " + null_bool
print ""

# Configuration constants
print "Configuration Constants:"
const int MAX_CONNECTIONS = 1000
const int TIMEOUT_SECONDS = 30
const int RETRY_ATTEMPTS = 3
const float CONNECTION_TIMEOUT = 5.5

print "MAX_CONNECTIONS = " + MAX_CONNECTIONS
print "TIMEOUT_SECONDS = " + TIMEOUT_SECONDS
print "RETRY_ATTEMPTS = " + RETRY_ATTEMPTS
print "CONNECTION_TIMEOUT = " + CONNECTION_TIMEOUT
print ""

# Color constants (as integers)
print "Color Constants:"
const int RED = 255
const int GREEN = 128
const int BLUE = 64
const int WHITE = 255
const int BLACK = 0

print "RED = " + RED
print "GREEN = " + GREEN
print "BLUE = " + BLUE
print "WHITE = " + WHITE
print "BLACK = " + BLACK
print ""

# Status constants
print "Status Constants:"
const int STATUS_SUCCESS = 0
const int STATUS_ERROR = -1
const int STATUS_WARNING = 1
const int STATUS_PENDING = 2

print "STATUS_SUCCESS = " + STATUS_SUCCESS
print "STATUS_ERROR = " + STATUS_ERROR
print "STATUS_WARNING = " + STATUS_WARNING
print "STATUS_PENDING = " + STATUS_PENDING
print ""

# Using constants in calculations
print "Using Constants in Calculations:"
float circle_radius = 5.0
float circle_area = PI * circle_radius * circle_radius
float circle_circumference = 2.0 * PI * circle_radius

print "Circle with radius " + circle_radius + ":"
print "Area = " + circle_area
print "Circumference = " + circle_circumference
print ""

# Using constants in conditions
print "Using Constants in Conditions:"
int user_count = 150

if user_count > MAX_CONNECTIONS
    print "Warning: Too many users (" + user_count + " > " + MAX_CONNECTIONS + ")"
elif user_count < MIN_SIZE
    print "Warning: Too few users (" + user_count + " < " + MIN_SIZE + ")"
else
    print "User count is within acceptable range: " + user_count

print ""

# Array of constants
print "Array of Constants:"
array int HTTP_CODES = [200, 404, 500, 403, 401]
array string HTTP_MESSAGES = ["OK", "Not Found", "Server Error", "Forbidden", "Unauthorized"]

print "HTTP Status Codes:"
for i = 0 to 4
    print HTTP_CODES[i] + ": " + HTTP_MESSAGES[i]
print ""

# Constants in functions
print "Constants in Functions:"

fun validate_password(password) {
    const int MIN_LENGTH = 8
    const int MAX_LENGTH = 50
    
    int length = len(password)  # Simulated length function
    
    if length < MIN_LENGTH
        print "Password too short (min " + MIN_LENGTH + " chars)"
        return false
    elif length > MAX_LENGTH
        print "Password too long (max " + MAX_LENGTH + " chars)"
        return false
    else
        print "Password length is valid (" + length + " chars)"
        return true
}

bool valid1 = validate_password("abc")      # Too short
bool valid2 = validate_password("password123") # Valid
print ""

# Environment constants
print "Environment Constants:"
const string ENVIRONMENT = "development"
const string DATABASE_URL = "localhost:5432"
const string API_VERSION = "v1"

if ENVIRONMENT == "development"
    print "Running in development mode"
    print "Database: " + DATABASE_URL
    print "API Version: " + API_VERSION
print ""

print "=== End of Constants and Special Values Demo ==="
