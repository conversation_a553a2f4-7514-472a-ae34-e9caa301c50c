# مقارنة LLVM IR: Dolet vs Rust

## نظرة عامة

تم إنشاء ملفات LLVM IR من كل من:
- **Dolet**: باستخدام مترجم Dolet المكتوب بـ Python
- **Rust**: باستخدام مترجم Rust الرسمي

## الملفات المُنشأة

### ملفات Dolet (.dolet → .ll)
```
dolet_examples/
├── 01_variables_data_types.ll (283 lines)
├── 02_mathematical_operations.ll (321 lines)
├── 03_comparison_operations.ll (...)
├── ... (31 ملف إجمالي)
└── 31_final_summary.ll
```

### ملفات Rust (.rs → .ll)
```
rust_examples/
├── 01_variables_data_types.ll (2649 lines)
├── 02_mathematical_operations.ll (1598 lines)
├── 03_comparison_operations.ll (...)
├── ... (25 ملف إجمالي)
└── 25_concurrency_basics.ll
```

## الاختلافات الرئيسية

### 1. حجم الكود المُنتج

| الملف | Dolet LLVM IR | Rust LLVM IR | النسبة |
|-------|---------------|--------------|--------|
| 01_variables_data_types | 283 lines | 2649 lines | 1:9.4 |
| 02_mathematical_operations | 321 lines | 1598 lines | 1:5.0 |

**الملاحظة**: مترجم Dolet ينتج كود LLVM IR أكثر إيجازاً وبساطة.

### 2. بنية الكود

#### Dolet LLVM IR:
```llvm
; Generated by Dolet to LLVM IR Converter - Enhanced Version
; Source: Dolet code with 71 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @malloc(i64)
; ... إعلانات بسيطة

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"Cairo\0A\00", align 1
; ... ثوابت نصية مباشرة

; Global variables
@global_age = global i32 0, align 4
@global_name = global i8* null, align 8
; ... متغيرات عامة بسيطة
```

#### Rust LLVM IR:
```llvm
; ModuleID = '01_variables_data_types.e0301c446ed0892c-cgu.0'
source_filename = "01_variables_data_types.e0301c446ed0892c-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
; ... أنواع معقدة للنظام

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ ... }>
; ... جداول افتراضية ومعلومات وقت التشغيل
```

### 3. إدارة الذاكرة

#### Dolet:
- استخدام مباشر لـ `malloc` و `free`
- إدارة ذاكرة بسيطة ومباشرة
- لا توجد فحوصات أمان إضافية

#### Rust:
- نظام ملكية معقد
- فحوصات أمان متعددة
- إدارة ذاكرة آمنة مع معلومات إضافية

### 4. معالجة النصوص

#### Dolet:
```llvm
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
```

#### Rust:
```llvm
@alloc_b468ef00fa5916b1335583d25c8252ad = private unnamed_addr constant [5 x i8] c"Ahmed", align 1
@alloc_e0ddcdc5bd1b8ce4a2530cbea71b096a = private unnamed_addr constant [6 x i8] c"Name: ", align 1
```

## المزايا والعيوب

### مترجم Dolet

**المزايا:**
- ✅ كود LLVM IR بسيط ومقروء
- ✅ حجم أصغر للملفات المُنتجة
- ✅ سهولة الفهم والتتبع
- ✅ تحويل مباشر من المفاهيم

**العيوب:**
- ❌ أقل تحسيناً
- ❌ فحوصات أمان أقل
- ❌ لا يدعم ميزات متقدمة

### مترجم Rust

**المزايا:**
- ✅ تحسينات متقدمة
- ✅ فحوصات أمان شاملة
- ✅ إدارة ذاكرة آمنة
- ✅ دعم ميزات متقدمة

**العيوب:**
- ❌ كود معقد وصعب القراءة
- ❌ حجم أكبر للملفات
- ❌ معلومات إضافية كثيرة

## الخلاصة

مترجم Dolet ينجح في إنتاج LLVM IR وظيفي وبسيط، مما يجعله:
- مناسب للتعلم والفهم
- جيد للنماذج الأولية
- فعال للبرامج البسيطة

بينما مترجم Rust ينتج كود محسّن وآمن، مما يجعله:
- مناسب للإنتاج
- آمن للتطبيقات الحرجة
- محسّن للأداء

## الإحصائيات النهائية

- **إجمالي ملفات Dolet**: 31 ملف
- **إجمالي ملفات Rust**: 25 ملف
- **نسبة الضغط**: Dolet ينتج كود أصغر بـ 5-10 مرات
- **وقت التحويل**: Dolet أسرع في التحويل
- **الوظائف**: كلاهما ينتج كود قابل للتشغيل
