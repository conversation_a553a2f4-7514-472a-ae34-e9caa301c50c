# 24. Date and Time - Date/Time Operations and Formatting
# This file demonstrates date and time operations in Dolet

print "=== Date and Time Demo ==="
print ""

# Current date and time (simulated)
print "Current Date and Time:"
string current_date = "2024-01-15"
string current_time = "14:30:25"
string current_datetime = current_date + " " + current_time

print "Current date: " + current_date
print "Current time: " + current_time
print "Current datetime: " + current_datetime
print ""

# Date components
print "Date Components:"
int year = 2024
int month = 1
int day = 15
int hour = 14
int minute = 30
int second = 25

print "Year: " + year
print "Month: " + month
print "Day: " + day
print "Hour: " + hour
print "Minute: " + minute
print "Second: " + second
print ""

# Date formatting
print "Date Formatting:"

fun format_date(y, m, d, format) {
    if format == "YYYY-MM-DD"
        return y + "-" + m + "-" + d
    elif format == "DD/MM/YYYY"
        return d + "/" + m + "/" + y
    elif format == "MM-DD-YYYY"
        return m + "-" + d + "-" + y
    else
        return y + "/" + m + "/" + d
}

string iso_format = format_date(year, month, day, "YYYY-MM-DD")
string us_format = format_date(year, month, day, "MM-DD-YYYY")
string eu_format = format_date(year, month, day, "DD/MM/YYYY")

print "ISO format: " + iso_format
print "US format: " + us_format
print "EU format: " + eu_format
print ""

# Time formatting
print "Time Formatting:"

fun format_time(h, m, s, format) {
    if format == "24H"
        return h + ":" + m + ":" + s
    elif format == "12H"
        if h > 12
            return (h - 12) + ":" + m + ":" + s + " PM"
        elif h == 12
            return h + ":" + m + ":" + s + " PM"
        elif h == 0
            return "12:" + m + ":" + s + " AM"
        else
            return h + ":" + m + ":" + s + " AM"
    else
        return h + ":" + m + ":" + s
}

string time_24h = format_time(hour, minute, second, "24H")
string time_12h = format_time(hour, minute, second, "12H")

print "24-hour format: " + time_24h
print "12-hour format: " + time_12h
print ""

# Date calculations
print "Date Calculations:"

fun days_in_month(month, year) {
    if month == 2
        if year % 4 == 0
            return 29  # Leap year
        else
            return 28
    elif month == 4 or month == 6 or month == 9 or month == 11
        return 30
    else
        return 31
}

fun is_leap_year(year) {
    if year % 4 == 0
        if year % 100 == 0
            if year % 400 == 0
                return true
            else
                return false
        else
            return true
    else
        return false
}

int days_jan = days_in_month(1, 2024)
int days_feb = days_in_month(2, 2024)
bool leap_2024 = is_leap_year(2024)
bool leap_2023 = is_leap_year(2023)

print "Days in January 2024: " + days_jan
print "Days in February 2024: " + days_feb
print "2024 is leap year: " + leap_2024
print "2023 is leap year: " + leap_2023
print ""

# Age calculation
print "Age Calculation:"

fun calculate_age(birth_year, birth_month, birth_day, current_year, current_month, current_day) {
    int age = current_year - birth_year
    
    if current_month < birth_month
        set age = age - 1
    elif current_month == birth_month and current_day < birth_day
        set age = age - 1
    
    return age
}

int person_age = calculate_age(1995, 6, 15, 2024, 1, 15)
print "Person born on 1995-06-15 is " + person_age + " years old on 2024-01-15"
print ""

# Day of week calculation (simplified)
print "Day of Week Calculation:"

fun get_day_name(day_number) {
    if day_number == 0
        return "Sunday"
    elif day_number == 1
        return "Monday"
    elif day_number == 2
        return "Tuesday"
    elif day_number == 3
        return "Wednesday"
    elif day_number == 4
        return "Thursday"
    elif day_number == 5
        return "Friday"
    else
        return "Saturday"
}

# Simplified day calculation (would use proper algorithm)
int day_of_week = 1  # Monday for 2024-01-15
string day_name = get_day_name(day_of_week)
print "2024-01-15 is a " + day_name
print ""

# Time zones (simulation)
print "Time Zone Handling:"

fun convert_timezone(hour, from_offset, to_offset) {
    int new_hour = hour + (to_offset - from_offset)
    
    if new_hour < 0
        set new_hour = new_hour + 24
    elif new_hour >= 24
        set new_hour = new_hour - 24
    
    return new_hour
}

int utc_hour = 12
int cairo_hour = convert_timezone(utc_hour, 0, 2)    # UTC+2
int london_hour = convert_timezone(utc_hour, 0, 0)   # UTC+0
int ny_hour = convert_timezone(utc_hour, 0, -5)      # UTC-5

print "When it's " + utc_hour + ":00 UTC:"
print "  Cairo (UTC+2): " + cairo_hour + ":00"
print "  London (UTC+0): " + london_hour + ":00"
print "  New York (UTC-5): " + ny_hour + ":00"
print ""

# Duration calculations
print "Duration Calculations:"

fun calculate_duration(start_hour, start_min, end_hour, end_min) {
    int total_start_minutes = start_hour * 60 + start_min
    int total_end_minutes = end_hour * 60 + end_min
    
    int duration_minutes = total_end_minutes - total_start_minutes
    
    int hours = duration_minutes / 60
    int minutes = duration_minutes % 60
    
    print "Duration: " + hours + " hours and " + minutes + " minutes"
    return duration_minutes
}

print "Meeting from 09:30 to 11:45:"
int meeting_duration = calculate_duration(9, 30, 11, 45)
print ""

# Calendar operations
print "Calendar Operations:"

fun print_month_calendar(year, month) {
    print "Calendar for " + month + "/" + year + ":"
    print "Sun Mon Tue Wed Thu Fri Sat"
    
    int days = days_in_month(month, year)
    int start_day = 1  # Simplified - would calculate actual start day
    
    # Print first week
    print "  1   2   3   4   5   6   7"
    print "  8   9  10  11  12  13  14"
    print " 15  16  17  18  19  20  21"
    print " 22  23  24  25  26  27  28"
    
    if days > 28
        if days == 29
            print " 29"
        elif days == 30
            print " 29  30"
        else
            print " 29  30  31"
}

print_month_calendar(2024, 1)
print ""

# Timestamp operations
print "Timestamp Operations:"

fun create_timestamp(year, month, day, hour, minute, second) {
    # Simplified timestamp (would use proper epoch calculation)
    int timestamp = year * 10000000000 + month * 100000000 + day * 1000000 + hour * 10000 + minute * 100 + second
    return timestamp
}

fun parse_timestamp(timestamp) {
    int year = timestamp / 10000000000
    int remainder = timestamp % 10000000000
    int month = remainder / 100000000
    set remainder = remainder % 100000000
    int day = remainder / 1000000
    set remainder = remainder % 1000000
    int hour = remainder / 10000
    set remainder = remainder % 10000
    int minute = remainder / 100
    int second = remainder % 100
    
    print "Parsed timestamp: " + year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
}

int timestamp = create_timestamp(2024, 1, 15, 14, 30, 25)
print "Created timestamp: " + timestamp
parse_timestamp(timestamp)
print ""

# Date validation
print "Date Validation:"

fun is_valid_date(year, month, day) {
    if year < 1 or year > 9999
        return false
    
    if month < 1 or month > 12
        return false
    
    int max_days = days_in_month(month, year)
    if day < 1 or day > max_days
        return false
    
    return true
}

bool valid1 = is_valid_date(2024, 2, 29)  # Valid leap year date
bool valid2 = is_valid_date(2023, 2, 29)  # Invalid - not leap year
bool valid3 = is_valid_date(2024, 13, 1)  # Invalid month

print "2024-02-29 is valid: " + valid1
print "2023-02-29 is valid: " + valid2
print "2024-13-01 is valid: " + valid3
print ""

print "=== End of Date and Time Demo ==="
