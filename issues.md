


2. 
    not works:
        print "int + float: " + int_val + " + " + float_val + " = " + (int_val + float_val)
        "
        PS C:\Users\<USER>\Desktop\dolet lang\underdev\Dolet\Dolet-bootstrap-compiler\src> clang 02_mathematical_operations.ll -o 02_mathematical_operations.exe
        02_mathematical_operations.ll:234:36: error: expected instruction opcode
        234 |   %tmp_18 = load i32, i32* @global_(int_val, align 4
            |                                    ^
        1 error generated.
        "
    works:
        print "int + float: " + int_val + " + " + float_val + " = " + int_val + float_val
        "
        PS C:\Users\<USER>\Desktop\dolet lang\underdev\Dolet\Dolet-bootstrap-compiler\src> clang 02_mathematical_operations.ll -o 02_mathematical_operations.exe
        warning: overriding the module target triple with x86_64-pc-windows-msvc19.44.35208 [-Woverride-module]
        1 warning generated.
        "
4.  not works:
        print "int * float: " + int_val + " * " + float_val + " = " + int_val * float_val
        "
        PS C:\Users\<USER>\Desktop\dolet lang\underdev\Dolet\Dolet-bootstrap-compiler\src> clang 02_mathematical_operations.ll -o 02_mathematical_operations.exe
        02_mathematical_operations.ll:241:44: error: expected instruction opcode
        241 |   %tmp_19 = load i32, i32* @global_int_val * float_val, align 4
            |                                            ^
        1 error generated.
        "
    works:
        print "int * float: " + int_val + " * " + float_val + " = " + (int_val * float_val)
        "
        PS C:\Users\<USER>\Desktop\dolet lang\underdev\Dolet\Dolet-bootstrap-compiler\src> clang 02_mathematical_operations.ll -o 02_mathematical_operations.exe
        warning: overriding the module target triple with x86_64-pc-windows-msvc19.44.35208 [-Woverride-module]
        1 warning generated.
        "
5.  