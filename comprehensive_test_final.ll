; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 181 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_2 = private unnamed_addr constant [13 x i8] c"Hello World\0A\00", align 1
@.str_3 = private unnamed_addr constant [9 x i8] c"Test123\0A\00", align 1
@.str_4 = private unnamed_addr constant [13 x i8] c"!@#$%%^&*()\0A\00", align 1
@.str_5 = private unnamed_addr constant [18 x i8] c"She said \22Hello\22\0A\00", align 1
@.str_6 = private unnamed_addr constant [19 x i8] c"Line1\0ALine2\0ALine3\0A\00", align 1
@.str_7 = private unnamed_addr constant [16 x i8] c"Col1\09Col2\09Col3\0A\00", align 1
@.str_8 = private unnamed_addr constant [7 x i8] c"Value\0A\00", align 1
@.str_9 = private unnamed_addr constant [124 x i8] c"This is a very long string that contains many words and should test the string handling capabilities of the Dolet compiler\0A\00", align 1
@.str_10 = private unnamed_addr constant [87 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\22,./<>?   \0A\00", align 1
@.str_11 = private unnamed_addr constant [7 x i8] c"12345\0A\00", align 1
@.str_12 = private unnamed_addr constant [9 x i8] c"3.14159\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [8 x i8] c"zero = \00", align 1
@.str_expr_1 = private unnamed_addr constant [12 x i8] c"positive = \00", align 1
@.str_expr_2 = private unnamed_addr constant [12 x i8] c"negative = \00", align 1
@.str_expr_3 = private unnamed_addr constant [9 x i8] c"large = \00", align 1
@.str_expr_4 = private unnamed_addr constant [18 x i8] c"small_negative = \00", align 1
@.str_expr_5 = private unnamed_addr constant [14 x i8] c"zero_float = \00", align 1
@.str_expr_6 = private unnamed_addr constant [18 x i8] c"positive_float = \00", align 1
@.str_expr_7 = private unnamed_addr constant [18 x i8] c"negative_float = \00", align 1
@.str_expr_8 = private unnamed_addr constant [17 x i8] c"small_decimal = \00", align 1
@.str_expr_9 = private unnamed_addr constant [17 x i8] c"large_decimal = \00", align 1
@.str_expr_10 = private unnamed_addr constant [20 x i8] c"scientific_small = \00", align 1
@.str_expr_11 = private unnamed_addr constant [20 x i8] c"scientific_large = \00", align 1
@.str_expr_12 = private unnamed_addr constant [15 x i8] c"zero_double = \00", align 1
@.str_expr_13 = private unnamed_addr constant [14 x i8] c"pi_precise = \00", align 1
@.str_expr_14 = private unnamed_addr constant [19 x i8] c"negative_double = \00", align 1
@.str_expr_15 = private unnamed_addr constant [14 x i8] c"very_small = \00", align 1
@.str_expr_16 = private unnamed_addr constant [14 x i8] c"very_large = \00", align 1
@.str_expr_17 = private unnamed_addr constant [10 x i8] c"empty = '\00", align 1
@.str_expr_18 = private unnamed_addr constant [2 x i8] c"'\00", align 1
@.str_expr_19 = private unnamed_addr constant [10 x i8] c"simple = \00", align 1
@.str_expr_20 = private unnamed_addr constant [15 x i8] c"with_spaces = \00", align 1
@.str_expr_21 = private unnamed_addr constant [16 x i8] c"with_numbers = \00", align 1
@.str_expr_22 = private unnamed_addr constant [16 x i8] c"with_symbols = \00", align 1
@.str_expr_23 = private unnamed_addr constant [15 x i8] c"with_quotes = \00", align 1
@.str_expr_24 = private unnamed_addr constant [13 x i8] c"multiline = \00", align 1
@.str_expr_25 = private unnamed_addr constant [13 x i8] c"with_tabs = \00", align 1
@.str_expr_26 = private unnamed_addr constant [9 x i8] c"empty = \00", align 1
@.str_expr_27 = private unnamed_addr constant [16 x i8] c"with_spaces = '\00", align 1
@.str_expr_28 = private unnamed_addr constant [16 x i8] c"letter_upper = \00", align 1
@.str_expr_29 = private unnamed_addr constant [16 x i8] c"letter_lower = \00", align 1
@.str_expr_30 = private unnamed_addr constant [14 x i8] c"digit_char = \00", align 1
@.str_expr_31 = private unnamed_addr constant [15 x i8] c"space_char = '\00", align 1
@.str_expr_32 = private unnamed_addr constant [15 x i8] c"symbol_char = \00", align 1
@.str_expr_33 = private unnamed_addr constant [14 x i8] c"quote_char = \00", align 1
@.str_expr_34 = private unnamed_addr constant [18 x i8] c"backslash_char = \00", align 1
@.str_expr_35 = private unnamed_addr constant [12 x i8] c"true_val = \00", align 1
@.str_expr_36 = private unnamed_addr constant [13 x i8] c"false_val = \00", align 1
@.str_expr_37 = private unnamed_addr constant [16 x i8] c"another_true = \00", align 1
@.str_expr_38 = private unnamed_addr constant [17 x i8] c"another_false = \00", align 1
@.str_expr_39 = private unnamed_addr constant [15 x i8] c"Int + String: \00", align 1
@.str_expr_40 = private unnamed_addr constant [15 x i8] c" is the answer\00", align 1
@.str_expr_41 = private unnamed_addr constant [17 x i8] c"Float + String: \00", align 1
@.str_expr_42 = private unnamed_addr constant [7 x i8] c" is pi\00", align 1
@.str_expr_43 = private unnamed_addr constant [16 x i8] c"String + Char: \00", align 1
@.str_expr_44 = private unnamed_addr constant [2 x i8] c" \00", align 1
@.str_expr_45 = private unnamed_addr constant [16 x i8] c"Bool + String: \00", align 1
@.str_expr_46 = private unnamed_addr constant [10 x i8] c" or false\00", align 1
@.str_expr_47 = private unnamed_addr constant [11 x i8] c"Multiple: \00", align 1
@.str_expr_48 = private unnamed_addr constant [15 x i8] c"long_string = \00", align 1
@.str_expr_49 = private unnamed_addr constant [18 x i8] c"special_string = \00", align 1
@.str_expr_50 = private unnamed_addr constant [17 x i8] c"number_string = \00", align 1
@.str_expr_51 = private unnamed_addr constant [16 x i8] c"float_string = \00", align 1
@.str_expr_52 = private unnamed_addr constant [10 x i8] c"Integer: \00", align 1
@.str_expr_53 = private unnamed_addr constant [8 x i8] c"Float: \00", align 1
@.str_expr_54 = private unnamed_addr constant [9 x i8] c"String: \00", align 1
@.str_expr_55 = private unnamed_addr constant [12 x i8] c"Character: \00", align 1
@.str_expr_56 = private unnamed_addr constant [10 x i8] c"Boolean: \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1
@str_dyn_0 = private unnamed_addr constant [20 x i8] c"Line1\\nLine2\\nLine3\00", align 1
@str_dyn_1 = private unnamed_addr constant [17 x i8] c"Col1\\tCol2\\tCol3\00", align 1
@str_dyn_2 = private unnamed_addr constant [87 x i8] c"   Multiple   Spaces   And   Special   Characters   !@#$%%^&*()_+-=[]{}|;':\\\22,./<>?   \00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_zero = global i32 0, align 4
@global_positive = global i32 0, align 4
@global_negative = global i32 0, align 4
@global_large = global i32 0, align 4
@global_small_negative = global i32 0, align 4
@global_zero_float = global float 0.0, align 4
@global_positive_float = global float 0.0, align 4
@global_negative_float = global float 0.0, align 4
@global_small_decimal = global float 0.0, align 4
@global_large_decimal = global float 0.0, align 4
@global_scientific_small = global float 0.0, align 4
@global_scientific_large = global float 0.0, align 4
@global_zero_double = global double 0.0, align 8
@global_pi_precise = global double 0.0, align 8
@global_negative_double = global double 0.0, align 8
@global_very_small = global double 0.0, align 8
@global_very_large = global double 0.0, align 8
@global_empty = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_simple = global i8* null, align 8
@global_with_spaces = global i8* null, align 8
@global_with_numbers = global i8* null, align 8
@global_with_symbols = global i8* null, align 8
@global_with_quotes = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_multiline = global i8* null, align 8
@global_with_tabs = global i8* null, align 8
@global_letter_upper = global i8 0, align 1
@global_letter_lower = global i8 0, align 1
@global_digit_char = global i8 0, align 1
@global_space_char = global i8 0, align 1
@global_symbol_char = global i8 0, align 1
@global_newline_char = global i8 0, align 1
@global_tab_char = global i8 0, align 1
@global_quote_char = global i8 0, align 1
@global_backslash_char = global i8 0, align 1
@global_true_val = global i1 0, align 1
@global_false_val = global i1 0, align 1
@global_another_true = global i1 0, align 1
@global_another_false = global i1 0, align 1
@global_test_int = global i32 0, align 4
@global_test_float = global float 0.0, align 4
@global_test_string = global i8* null, align 8
@global_test_char = global i8 0, align 1
@global_test_bool = global i1 0, align 1
@global_long_string = global i8* null, align 8
@global_special_string = global i8* null, align 8
@global_number_string = global i8* null, align 8
@global_float_string = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== COMPREHENSIVE DATA TYPES TEST ===" (inline)
  %tmp_1_str = alloca [38 x i8], align 1
  store [38 x i8] c"=== COMPREHENSIVE DATA TYPES TEST ===\00", [38 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [38 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== INTEGER TESTS ===" (inline)
  %tmp_3_str = alloca [22 x i8], align 1
  store [22 x i8] c"=== INTEGER TESTS ===\00", [22 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [22 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int zero = 0
  store i32 0, i32* @global_zero, align 4
  ; int positive = 42
  store i32 42, i32* @global_positive, align 4
  ; int negative = -123
  store i32 -123, i32* @global_negative, align 4
  ; int large = 2147483647
  store i32 2147483647, i32* @global_large, align 4
  ; int small_negative = -2147483648
  store i32 -2147483648, i32* @global_small_negative, align 4
  ; print "Basic integers:" (inline)
  %tmp_5_str = alloca [16 x i8], align 1
  store [16 x i8] c"Basic integers:\00", [16 x i8]* %tmp_5_str, align 1
  %tmp_6 = bitcast [16 x i8]* %tmp_5_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero = " + zero
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_7 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "positive = " + positive
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_8 = load i32, i32* @global_positive, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative = " + negative
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_9 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large = " + large
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_10 = load i32, i32* @global_large, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "small_negative = " + small_negative
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_11 = load i32, i32* @global_small_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Integer modifications:" (inline)
  %tmp_12_str = alloca [23 x i8], align 1
  store [23 x i8] c"Integer modifications:\00", [23 x i8]* %tmp_12_str, align 1
  %tmp_13 = bitcast [23 x i8]* %tmp_12_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set zero = 100
  store i32 100, i32* @global_zero, align 4
  ; set positive = -50
  %tmp_14 = add i32 0, 0  ; complex expression fallback
  store i32 %tmp_14, i32* @global_positive, align 4
  ; set negative = 999
  store i32 999, i32* @global_negative, align 4
  ; print "After modification:" (inline)
  %tmp_15_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_15_str, align 1
  %tmp_16 = bitcast [20 x i8]* %tmp_15_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero = " + zero
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_17 = load i32, i32* @global_zero, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "positive = " + positive
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_18 = load i32, i32* @global_positive, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative = " + negative
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_19 = load i32, i32* @global_negative, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== FLOAT TESTS ===" (inline)
  %tmp_20_str = alloca [20 x i8], align 1
  store [20 x i8] c"=== FLOAT TESTS ===\00", [20 x i8]* %tmp_20_str, align 1
  %tmp_21 = bitcast [20 x i8]* %tmp_20_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_21)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float zero_float = 0.0
  %tmp_22 = bitcast i32 0 to float
  store float %tmp_22, float* @global_zero_float, align 4
  ; float positive_float = 3.14159
  %tmp_23 = bitcast i32 1078530000 to float
  store float %tmp_23, float* @global_positive_float, align 4
  ; float negative_float = -2.718
  %tmp_24 = bitcast i32 3224236982 to float
  store float %tmp_24, float* @global_negative_float, align 4
  ; float small_decimal = 0.001
  %tmp_25 = bitcast i32 981668463 to float
  store float %tmp_25, float* @global_small_decimal, align 4
  ; float large_decimal = 999.999
  %tmp_26 = bitcast i32 1148846064 to float
  store float %tmp_26, float* @global_large_decimal, align 4
  ; float scientific_small = 1.23e-5
  %tmp_27 = bitcast i32 927882265 to float
  store float %tmp_27, float* @global_scientific_small, align 4
  ; float scientific_large = 1.23e5
  %tmp_28 = bitcast i32 1206926336 to float
  store float %tmp_28, float* @global_scientific_large, align 4
  ; print "Basic floats:" (inline)
  %tmp_29_str = alloca [14 x i8], align 1
  store [14 x i8] c"Basic floats:\00", [14 x i8]* %tmp_29_str, align 1
  %tmp_30 = bitcast [14 x i8]* %tmp_29_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero_float = " + zero_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_31 = load float, float* @global_zero_float, align 4
  %tmp_32 = fpext float %tmp_31 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "positive_float = " + positive_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_33 = load float, float* @global_positive_float, align 4
  %tmp_34 = fpext float %tmp_33 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative_float = " + negative_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_35 = load float, float* @global_negative_float, align 4
  %tmp_36 = fpext float %tmp_35 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_36)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "small_decimal = " + small_decimal
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_37 = load float, float* @global_small_decimal, align 4
  %tmp_38 = fpext float %tmp_37 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_38)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "large_decimal = " + large_decimal
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_39 = load float, float* @global_large_decimal, align 4
  %tmp_40 = fpext float %tmp_39 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "scientific_small = " + scientific_small
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_41 = load float, float* @global_scientific_small, align 4
  %tmp_42 = fpext float %tmp_41 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "scientific_large = " + scientific_large
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([20 x i8], [20 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_43 = load float, float* @global_scientific_large, align 4
  %tmp_44 = fpext float %tmp_43 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_44)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Float modifications:" (inline)
  %tmp_45_str = alloca [21 x i8], align 1
  store [21 x i8] c"Float modifications:\00", [21 x i8]* %tmp_45_str, align 1
  %tmp_46 = bitcast [21 x i8]* %tmp_45_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_46)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set zero_float = 1.5
  %tmp_47 = bitcast i32 1069547520 to float
  store float %tmp_47, float* @global_zero_float, align 4
  ; set positive_float = -9.99
  %tmp_48 = bitcast i32 3240089354 to float
  store float %tmp_48, float* @global_positive_float, align 4
  ; set negative_float = 0.0001
  %tmp_49 = bitcast i32 953267991 to float
  store float %tmp_49, float* @global_negative_float, align 4
  ; print "After modification:" (inline)
  %tmp_50_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_50_str, align 1
  %tmp_51 = bitcast [20 x i8]* %tmp_50_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_51)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero_float = " + zero_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_52 = load float, float* @global_zero_float, align 4
  %tmp_53 = fpext float %tmp_52 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_53)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "positive_float = " + positive_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_54 = load float, float* @global_positive_float, align 4
  %tmp_55 = fpext float %tmp_54 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_55)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative_float = " + negative_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_56 = load float, float* @global_negative_float, align 4
  %tmp_57 = fpext float %tmp_56 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_57)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== DOUBLE TESTS ===" (inline)
  %tmp_58_str = alloca [21 x i8], align 1
  store [21 x i8] c"=== DOUBLE TESTS ===\00", [21 x i8]* %tmp_58_str, align 1
  %tmp_59 = bitcast [21 x i8]* %tmp_58_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_59)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; double zero_double = 0.0
  %tmp_60 = bitcast i64 0 to double
  store double %tmp_60, double* @global_zero_double, align 8
  ; double pi_precise = 3.141592653589793
  %tmp_61 = bitcast i64 4614256656552045848 to double
  store double %tmp_61, double* @global_pi_precise, align 8
  ; double negative_double = -123.456789012345
  %tmp_62 = bitcast i64 13861759952994651241 to double
  store double %tmp_62, double* @global_negative_double, align 8
  ; double very_small = 1.23e-15
  %tmp_63 = bitcast i64 4383735678757245001 to double
  store double %tmp_63, double* @global_very_small, align 8
  ; double very_large = 1.23e15
  %tmp_64 = bitcast i64 4832778800541171712 to double
  store double %tmp_64, double* @global_very_large, align 8
  ; print "Basic doubles:" (inline)
  %tmp_65_str = alloca [15 x i8], align 1
  store [15 x i8] c"Basic doubles:\00", [15 x i8]* %tmp_65_str, align 1
  %tmp_66 = bitcast [15 x i8]* %tmp_65_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_66)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero_double = " + zero_double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_67 = load double, double* @global_zero_double, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_67)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "pi_precise = " + pi_precise
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_68 = load double, double* @global_pi_precise, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_68)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "negative_double = " + negative_double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_69 = load double, double* @global_negative_double, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_69)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "very_small = " + very_small
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_70 = load double, double* @global_very_small, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_70)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "very_large = " + very_large
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_71 = load double, double* @global_very_large, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_71)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Double modifications:" (inline)
  %tmp_72_str = alloca [22 x i8], align 1
  store [22 x i8] c"Double modifications:\00", [22 x i8]* %tmp_72_str, align 1
  %tmp_73 = bitcast [22 x i8]* %tmp_72_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_73)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set zero_double = 2.718281828459045
  %tmp_74 = bitcast i64 4613303445314885481 to double
  store double %tmp_74, double* @global_zero_double, align 8
  ; set pi_precise = -1.414213562373095
  %tmp_75 = bitcast i64 13832419907699948492 to double
  store double %tmp_75, double* @global_pi_precise, align 8
  ; print "After modification:" (inline)
  %tmp_76_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_76_str, align 1
  %tmp_77 = bitcast [20 x i8]* %tmp_76_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_77)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "zero_double = " + zero_double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_78 = load double, double* @global_zero_double, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_78)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "pi_precise = " + pi_precise
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_79 = load double, double* @global_pi_precise, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_79)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== STRING TESTS ===" (inline)
  %tmp_80_str = alloca [21 x i8], align 1
  store [21 x i8] c"=== STRING TESTS ===\00", [21 x i8]* %tmp_80_str, align 1
  %tmp_81 = bitcast [21 x i8]* %tmp_80_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_81)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string empty = ""
  store i8* getelementptr inbounds ([1 x i8], [1 x i8]* @.str_0, i64 0, i64 0), i8** @global_empty, align 8
  ; string simple = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_simple, align 8
  ; string with_spaces = "Hello World"
  store i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_2, i64 0, i64 0), i8** @global_with_spaces, align 8
  ; string with_numbers = "Test123"
  store i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_3, i64 0, i64 0), i8** @global_with_numbers, align 8
  ; string with_symbols = "!@#$%^&*()"
  store i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_4, i64 0, i64 0), i8** @global_with_symbols, align 8
  ; string with_quotes = "She said \"Hello\"" # Fixed quote escaping
  ; string assignment from variable/expression: with_quotes = "She said \"Hello\"" # Fixed quote escaping
  ; string multiline = "Line1\nLine2\nLine3"
  store i8* getelementptr inbounds ([20 x i8], [20 x i8]* @str_dyn_0, i64 0, i64 0), i8** @global_multiline, align 8
  ; string with_tabs = "Col1\tCol2\tCol3"
  store i8* getelementptr inbounds ([17 x i8], [17 x i8]* @str_dyn_1, i64 0, i64 0), i8** @global_with_tabs, align 8
  ; print "Basic strings:" (inline)
  %tmp_82_str = alloca [15 x i8], align 1
  store [15 x i8] c"Basic strings:\00", [15 x i8]* %tmp_82_str, align 1
  %tmp_83 = bitcast [15 x i8]* %tmp_82_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_83)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "empty = '" + empty + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_84 = load i8*, i8** @global_empty, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_18, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "simple = " + simple
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_85 = load i8*, i8** @global_simple, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_85)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_spaces = " + with_spaces
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_86 = load i8*, i8** @global_with_spaces, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_numbers = " + with_numbers
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_87 = load i8*, i8** @global_with_numbers, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_87)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_symbols = " + with_symbols
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_88 = load i8*, i8** @global_with_symbols, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_88)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_quotes = " + with_quotes
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_23, i32 0, i32 0))
  %tmp_89 = load i8*, i8** @global_with_quotes, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_89)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "multiline = " + multiline
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_90 = load i8*, i8** @global_multiline, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_tabs = " + with_tabs
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_25, i32 0, i32 0))
  %tmp_91 = load i8*, i8** @global_with_tabs, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_91)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "String modifications:" (inline)
  %tmp_92_str = alloca [22 x i8], align 1
  store [22 x i8] c"String modifications:\00", [22 x i8]* %tmp_92_str, align 1
  %tmp_93 = bitcast [22 x i8]* %tmp_92_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set empty = "Not empty anymore"
  %tmp_94_str = alloca [18 x i8], align 1
  %tmp_95 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 0
  store i8 78, i8* %tmp_95, align 1
  %tmp_96 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 1
  store i8 111, i8* %tmp_96, align 1
  %tmp_97 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 2
  store i8 116, i8* %tmp_97, align 1
  %tmp_98 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 3
  store i8 32, i8* %tmp_98, align 1
  %tmp_99 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 4
  store i8 101, i8* %tmp_99, align 1
  %tmp_100 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 5
  store i8 109, i8* %tmp_100, align 1
  %tmp_101 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 6
  store i8 112, i8* %tmp_101, align 1
  %tmp_102 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 7
  store i8 116, i8* %tmp_102, align 1
  %tmp_103 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 8
  store i8 121, i8* %tmp_103, align 1
  %tmp_104 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 9
  store i8 32, i8* %tmp_104, align 1
  %tmp_105 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 10
  store i8 97, i8* %tmp_105, align 1
  %tmp_106 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 11
  store i8 110, i8* %tmp_106, align 1
  %tmp_107 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 12
  store i8 121, i8* %tmp_107, align 1
  %tmp_108 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 13
  store i8 109, i8* %tmp_108, align 1
  %tmp_109 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 14
  store i8 111, i8* %tmp_109, align 1
  %tmp_110 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 15
  store i8 114, i8* %tmp_110, align 1
  %tmp_111 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 16
  store i8 101, i8* %tmp_111, align 1
  %tmp_112 = getelementptr inbounds [18 x i8], [18 x i8]* %tmp_94_str, i64 0, i64 17
  store i8 0, i8* %tmp_112, align 1
  %tmp_113 = bitcast [18 x i8]* %tmp_94_str to i8*
  store i8* %tmp_113, i8** @global_empty, align 8
  ; set simple = "Modified"
  %tmp_114_str = alloca [9 x i8], align 1
  %tmp_115 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 0
  store i8 77, i8* %tmp_115, align 1
  %tmp_116 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 1
  store i8 111, i8* %tmp_116, align 1
  %tmp_117 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 2
  store i8 100, i8* %tmp_117, align 1
  %tmp_118 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 3
  store i8 105, i8* %tmp_118, align 1
  %tmp_119 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 4
  store i8 102, i8* %tmp_119, align 1
  %tmp_120 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 5
  store i8 105, i8* %tmp_120, align 1
  %tmp_121 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 6
  store i8 101, i8* %tmp_121, align 1
  %tmp_122 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 7
  store i8 100, i8* %tmp_122, align 1
  %tmp_123 = getelementptr inbounds [9 x i8], [9 x i8]* %tmp_114_str, i64 0, i64 8
  store i8 0, i8* %tmp_123, align 1
  %tmp_124 = bitcast [9 x i8]* %tmp_114_str to i8*
  store i8* %tmp_124, i8** @global_simple, align 8
  ; set with_spaces = ""
  %tmp_125_str = alloca [1 x i8], align 1
  %tmp_126 = getelementptr inbounds [1 x i8], [1 x i8]* %tmp_125_str, i64 0, i64 0
  store i8 0, i8* %tmp_126, align 1
  %tmp_127 = bitcast [1 x i8]* %tmp_125_str to i8*
  store i8* %tmp_127, i8** @global_with_spaces, align 8
  ; print "After modification:" (inline)
  %tmp_128_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_128_str, align 1
  %tmp_129 = bitcast [20 x i8]* %tmp_128_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "empty = " + empty
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_26, i32 0, i32 0))
  %tmp_130 = load i8*, i8** @global_empty, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_130)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "simple = " + simple
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_131 = load i8*, i8** @global_simple, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "with_spaces = '" + with_spaces + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_27, i32 0, i32 0))
  %tmp_132 = load i8*, i8** @global_with_spaces, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_132)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_18, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== CHARACTER TESTS ===" (inline)
  %tmp_133_str = alloca [24 x i8], align 1
  store [24 x i8] c"=== CHARACTER TESTS ===\00", [24 x i8]* %tmp_133_str, align 1
  %tmp_134 = bitcast [24 x i8]* %tmp_133_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_134)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; char letter_upper = 'A'
  store i8 65, i8* @global_letter_upper, align 1
  ; char letter_lower = 'z'
  store i8 122, i8* @global_letter_lower, align 1
  ; char digit_char = '9'
  store i8 57, i8* @global_digit_char, align 1
  ; char space_char = ' '
  store i8 32, i8* @global_space_char, align 1
  ; char symbol_char = '@'
  store i8 64, i8* @global_symbol_char, align 1
  ; char newline_char = '\n'
  ; char assignment from expression: newline_char = '\n'
  ; char tab_char = '\t'
  ; char assignment from expression: tab_char = '\t'
  ; char quote_char = '\''
  ; char assignment from expression: quote_char = '\''
  ; char backslash_char = '\\'
  ; char assignment from expression: backslash_char = '\\'
  ; print "Basic characters:" (inline)
  %tmp_135_str = alloca [18 x i8], align 1
  store [18 x i8] c"Basic characters:\00", [18 x i8]* %tmp_135_str, align 1
  %tmp_136 = bitcast [18 x i8]* %tmp_135_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_136)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "letter_upper = " + letter_upper
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_137 = load i8, i8* @global_letter_upper, align 1
  %tmp_138 = zext i8 %tmp_137 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_138)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "letter_lower = " + letter_lower
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_139 = load i8, i8* @global_letter_lower, align 1
  %tmp_140 = zext i8 %tmp_139 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_140)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "digit_char = " + digit_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_141 = load i8, i8* @global_digit_char, align 1
  %tmp_142 = zext i8 %tmp_141 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_142)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "space_char = '" + space_char + "'"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_143 = load i8, i8* @global_space_char, align 1
  %tmp_144 = zext i8 %tmp_143 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_144)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_18, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "symbol_char = " + symbol_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_145 = load i8, i8* @global_symbol_char, align 1
  %tmp_146 = zext i8 %tmp_145 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "quote_char = " + quote_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_147 = load i8, i8* @global_quote_char, align 1
  %tmp_148 = zext i8 %tmp_147 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_148)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "backslash_char = " + backslash_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_34, i32 0, i32 0))
  %tmp_149 = load i8, i8* @global_backslash_char, align 1
  %tmp_150 = zext i8 %tmp_149 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_150)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Character modifications:" (inline)
  %tmp_151_str = alloca [25 x i8], align 1
  store [25 x i8] c"Character modifications:\00", [25 x i8]* %tmp_151_str, align 1
  %tmp_152 = bitcast [25 x i8]* %tmp_151_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_152)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set letter_upper = 'B'
  store i8 66, i8* @global_letter_upper, align 1
  ; set letter_lower = 'Y'
  store i8 89, i8* @global_letter_lower, align 1
  ; set digit_char = '0'
  store i8 48, i8* @global_digit_char, align 1
  ; print "After modification:" (inline)
  %tmp_153_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_153_str, align 1
  %tmp_154 = bitcast [20 x i8]* %tmp_153_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_154)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "letter_upper = " + letter_upper
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_155 = load i8, i8* @global_letter_upper, align 1
  %tmp_156 = zext i8 %tmp_155 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_156)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "letter_lower = " + letter_lower
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_157 = load i8, i8* @global_letter_lower, align 1
  %tmp_158 = zext i8 %tmp_157 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_158)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "digit_char = " + digit_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_159 = load i8, i8* @global_digit_char, align 1
  %tmp_160 = zext i8 %tmp_159 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_160)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== BOOLEAN TESTS ===" (inline)
  %tmp_161_str = alloca [22 x i8], align 1
  store [22 x i8] c"=== BOOLEAN TESTS ===\00", [22 x i8]* %tmp_161_str, align 1
  %tmp_162 = bitcast [22 x i8]* %tmp_161_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_162)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; bool true_val = true
  store i1 1, i1* @global_true_val, align 1
  ; bool false_val = false
  store i1 0, i1* @global_false_val, align 1
  ; bool another_true = true
  store i1 1, i1* @global_another_true, align 1
  ; bool another_false = false
  store i1 0, i1* @global_another_false, align 1
  ; print "Basic booleans:" (inline)
  %tmp_163_str = alloca [16 x i8], align 1
  store [16 x i8] c"Basic booleans:\00", [16 x i8]* %tmp_163_str, align 1
  %tmp_164 = bitcast [16 x i8]* %tmp_163_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_164)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "true_val = " + true_val
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_35, i32 0, i32 0))
  %tmp_165 = load i1, i1* @global_true_val, align 1
  br i1 %tmp_165, label %tmp_166, label %tmp_167
tmp_166:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_168
tmp_167:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_168
tmp_168:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "false_val = " + false_val
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_36, i32 0, i32 0))
  %tmp_169 = load i1, i1* @global_false_val, align 1
  br i1 %tmp_169, label %tmp_170, label %tmp_171
tmp_170:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_172
tmp_171:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_172
tmp_172:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "another_true = " + another_true
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_37, i32 0, i32 0))
  %tmp_173 = load i1, i1* @global_another_true, align 1
  br i1 %tmp_173, label %tmp_174, label %tmp_175
tmp_174:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_176
tmp_175:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_176
tmp_176:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "another_false = " + another_false
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_177 = load i1, i1* @global_another_false, align 1
  br i1 %tmp_177, label %tmp_178, label %tmp_179
tmp_178:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_180
tmp_179:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_180
tmp_180:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Boolean modifications:" (inline)
  %tmp_181_str = alloca [23 x i8], align 1
  store [23 x i8] c"Boolean modifications:\00", [23 x i8]* %tmp_181_str, align 1
  %tmp_182 = bitcast [23 x i8]* %tmp_181_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_182)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set true_val = false
  store i1 0, i1* @global_true_val, align 1
  ; set false_val = true
  store i1 1, i1* @global_false_val, align 1
  ; set another_true = false
  store i1 0, i1* @global_another_true, align 1
  ; set another_false = true
  store i1 1, i1* @global_another_false, align 1
  ; print "After modification:" (inline)
  %tmp_183_str = alloca [20 x i8], align 1
  store [20 x i8] c"After modification:\00", [20 x i8]* %tmp_183_str, align 1
  %tmp_184 = bitcast [20 x i8]* %tmp_183_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_184)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "true_val = " + true_val
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_35, i32 0, i32 0))
  %tmp_185 = load i1, i1* @global_true_val, align 1
  br i1 %tmp_185, label %tmp_186, label %tmp_187
tmp_186:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_188
tmp_187:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_188
tmp_188:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "false_val = " + false_val
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_36, i32 0, i32 0))
  %tmp_189 = load i1, i1* @global_false_val, align 1
  br i1 %tmp_189, label %tmp_190, label %tmp_191
tmp_190:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_192
tmp_191:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_192
tmp_192:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "another_true = " + another_true
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_37, i32 0, i32 0))
  %tmp_193 = load i1, i1* @global_another_true, align 1
  br i1 %tmp_193, label %tmp_194, label %tmp_195
tmp_194:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_196
tmp_195:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_196
tmp_196:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "another_false = " + another_false
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_197 = load i1, i1* @global_another_false, align 1
  br i1 %tmp_197, label %tmp_198, label %tmp_199
tmp_198:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_200
tmp_199:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_200
tmp_200:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== MIXED TYPE CONCATENATIONS ===" (inline)
  %tmp_201_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== MIXED TYPE CONCATENATIONS ===\00", [34 x i8]* %tmp_201_str, align 1
  %tmp_202 = bitcast [34 x i8]* %tmp_201_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_202)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int test_int = 42
  store i32 42, i32* @global_test_int, align 4
  ; float test_float = 3.14
  %tmp_203 = bitcast i32 1078523331 to float
  store float %tmp_203, float* @global_test_float, align 4
  ; string test_string = "Value"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_8, i64 0, i64 0), i8** @global_test_string, align 8
  ; char test_char = 'X'
  store i8 88, i8* @global_test_char, align 1
  ; bool test_bool = true
  store i1 1, i1* @global_test_bool, align 1
  ; print "Mixed concatenations:" (inline)
  %tmp_204_str = alloca [22 x i8], align 1
  store [22 x i8] c"Mixed concatenations:\00", [22 x i8]* %tmp_204_str, align 1
  %tmp_205 = bitcast [22 x i8]* %tmp_204_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_205)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Int + String: " + test_int + " is the answer"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_39, i32 0, i32 0))
  %tmp_206 = load i32, i32* @global_test_int, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_206)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_40, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float + String: " + test_float + " is pi"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_41, i32 0, i32 0))
  %tmp_207 = load float, float* @global_test_float, align 4
  %tmp_208 = fpext float %tmp_207 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_208)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_42, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "String + Char: " + test_string + " " + test_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_43, i32 0, i32 0))
  %tmp_209 = load i8*, i8** @global_test_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_209)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_210 = load i8, i8* @global_test_char, align 1
  %tmp_211 = zext i8 %tmp_210 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_211)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Bool + String: " + test_bool + " or false"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_45, i32 0, i32 0))
  %tmp_212 = load i1, i1* @global_test_bool, align 1
  br i1 %tmp_212, label %tmp_213, label %tmp_214
tmp_213:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_215
tmp_214:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_215
tmp_215:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_46, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Multiple: " + test_string + " " + test_int + " " ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_47, i32 0, i32 0))
  %tmp_216 = load i8*, i8** @global_test_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_216)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_217 = load i32, i32* @global_test_int, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_217)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_218 = load float, float* @global_test_float, align 4
  %tmp_219 = fpext float %tmp_218 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_219)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_220 = load i8, i8* @global_test_char, align 1
  %tmp_221 = zext i8 %tmp_220 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_221)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_expr_44, i32 0, i32 0))
  %tmp_222 = load i1, i1* @global_test_bool, align 1
  br i1 %tmp_222, label %tmp_223, label %tmp_224
tmp_223:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_225
tmp_224:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_225
tmp_225:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== EDGE CASES ===" (inline)
  %tmp_226_str = alloca [19 x i8], align 1
  store [19 x i8] c"=== EDGE CASES ===\00", [19 x i8]* %tmp_226_str, align 1
  %tmp_227 = bitcast [19 x i8]* %tmp_226_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_227)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string long_string = "This is a very long string that contains many words and should test the string handling capabilities of the Dolet compiler"
  store i8* getelementptr inbounds ([123 x i8], [123 x i8]* @.str_9, i64 0, i64 0), i8** @global_long_string, align 8
  ; string special_string = "   Multiple   Spaces   And   Special   Characters   !@#$%^&*()_+-=[]{}|;':\",./<>?   "
  store i8* getelementptr inbounds ([86 x i8], [86 x i8]* @str_dyn_2, i64 0, i64 0), i8** @global_special_string, align 8
  ; string number_string = "12345"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_11, i64 0, i64 0), i8** @global_number_string, align 8
  ; string float_string = "3.14159"
  store i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_12, i64 0, i64 0), i8** @global_float_string, align 8
  ; print "Edge cases:" (inline)
  %tmp_228_str = alloca [12 x i8], align 1
  store [12 x i8] c"Edge cases:\00", [12 x i8]* %tmp_228_str, align 1
  %tmp_229 = bitcast [12 x i8]* %tmp_228_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_229)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "long_string = " + long_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_48, i32 0, i32 0))
  %tmp_230 = load i8*, i8** @global_long_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_230)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "special_string = " + special_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_49, i32 0, i32 0))
  %tmp_231 = load i8*, i8** @global_special_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_231)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "number_string = " + number_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_50, i32 0, i32 0))
  %tmp_232 = load i8*, i8** @global_number_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_232)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "float_string = " + float_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([16 x i8], [16 x i8]* @.str_expr_51, i32 0, i32 0))
  %tmp_233 = load i8*, i8** @global_float_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_233)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== FINAL SUMMARY ===" (inline)
  %tmp_234_str = alloca [22 x i8], align 1
  store [22 x i8] c"=== FINAL SUMMARY ===\00", [22 x i8]* %tmp_234_str, align 1
  %tmp_235 = bitcast [22 x i8]* %tmp_234_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_235)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "All data types tested successfully!" (inline)
  %tmp_236_str = alloca [36 x i8], align 1
  store [36 x i8] c"All data types tested successfully!\00", [36 x i8]* %tmp_236_str, align 1
  %tmp_237 = bitcast [36 x i8]* %tmp_236_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_237)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Integer: " + test_int
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_52, i32 0, i32 0))
  %tmp_238 = load i32, i32* @global_test_int, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_238)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Float: " + test_float
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_53, i32 0, i32 0))
  %tmp_239 = load float, float* @global_test_float, align 4
  %tmp_240 = fpext float %tmp_239 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_240)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "String: " + test_string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_54, i32 0, i32 0))
  %tmp_241 = load i8*, i8** @global_test_string, align 8
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_241)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Character: " + test_char
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([12 x i8], [12 x i8]* @.str_expr_55, i32 0, i32 0))
  %tmp_242 = load i8, i8* @global_test_char, align 1
  %tmp_243 = zext i8 %tmp_242 to i32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_char, i32 0, i32 0), i32 %tmp_243)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Boolean: " + test_bool
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_56, i32 0, i32 0))
  %tmp_244 = load i1, i1* @global_test_bool, align 1
  br i1 %tmp_244, label %tmp_245, label %tmp_246
tmp_245:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_true, i32 0, i32 0))
  br label %tmp_247
tmp_246:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_false, i32 0, i32 0))
  br label %tmp_247
tmp_247:
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_0, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== END OF COMPREHENSIVE TEST ===" (inline)
  %tmp_248_str = alloca [34 x i8], align 1
  store [34 x i8] c"=== END OF COMPREHENSIVE TEST ===\00", [34 x i8]* %tmp_248_str, align 1
  %tmp_249 = bitcast [34 x i8]* %tmp_248_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_249)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
