; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: Dolet code with 28 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_expr_0 = private unnamed_addr constant [11 x i8] c"Integers: \00", align 1
@.str_expr_1 = private unnamed_addr constant [5 x i8] c"hi: \00", align 1
@.str_expr_2 = private unnamed_addr constant [7 x i8] c" ahmed\00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; set a = 10
  store i32 10, i32* @global_a, align 4
  ; print "=== Dolet Basic Syntax Demo ===" (inline)
  %tmp_1_str = alloca [32 x i8], align 1
  store [32 x i8] c"=== Dolet Basic Syntax Demo ===\00", [32 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [32 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Integers: " + a
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_3 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_3)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "hi: " + " ahmed"
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_1, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_2, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print a (integer)
  %tmp_4 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_int, i32 0, i32 0), i32 %tmp_4)
  ; print "Comments work with # symbol" (inline)
  %tmp_5_str = alloca [28 x i8], align 1
  store [28 x i8] c"Comments work with # symbol\00", [28 x i8]* %tmp_5_str, align 1
  %tmp_6 = bitcast [28 x i8]* %tmp_5_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Hello, World!" (inline)
  %tmp_7_str = alloca [14 x i8], align 1
  store [14 x i8] c"Hello, World!\00", [14 x i8]* %tmp_7_str, align 1
  %tmp_8 = bitcast [14 x i8]* %tmp_7_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "This is Dolet programming language" (inline)
  %tmp_9_str = alloca [35 x i8], align 1
  store [35 x i8] c"This is Dolet programming language\00", [35 x i8]* %tmp_9_str, align 1
  %tmp_10 = bitcast [35 x i8]* %tmp_9_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_11_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_11_str, align 1
  %tmp_12 = bitcast [1 x i8]* %tmp_11_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Empty lines help organize code" (inline)
  %tmp_13_str = alloca [31 x i8], align 1
  store [31 x i8] c"Empty lines help organize code\00", [31 x i8]* %tmp_13_str, align 1
  %tmp_14 = bitcast [31 x i8]* %tmp_13_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_14)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Multiple empty lines above" (inline)
  %tmp_15_str = alloca [27 x i8], align 1
  store [27 x i8] c"Multiple empty lines above\00", [27 x i8]* %tmp_15_str, align 1
  %tmp_16 = bitcast [27 x i8]* %tmp_15_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_16)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_17_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_17_str, align 1
  %tmp_18 = bitcast [1 x i8]* %tmp_17_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Double quotes work" (inline)
  %tmp_19_str = alloca [19 x i8], align 1
  store [19 x i8] c"Double quotes work\00", [19 x i8]* %tmp_19_str, align 1
  %tmp_20 = bitcast [19 x i8]* %tmp_19_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_21_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_21_str, align 1
  %tmp_22 = bitcast [1 x i8]* %tmp_21_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Special chars: @#$%^&*()" (inline)
  %tmp_23_str = alloca [25 x i8], align 1
  store [25 x i8] c"Special chars: @#$%^&*()\00", [25 x i8]* %tmp_23_str, align 1
  %tmp_24 = bitcast [25 x i8]* %tmp_23_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Numbers in strings: 123456789" (inline)
  %tmp_25_str = alloca [30 x i8], align 1
  store [30 x i8] c"Numbers in strings: 123456789\00", [30 x i8]* %tmp_25_str, align 1
  %tmp_26 = bitcast [30 x i8]* %tmp_25_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Mixed: Hello123World!" (inline)
  %tmp_27_str = alloca [22 x i8], align 1
  store [22 x i8] c"Mixed: Hello123World!\00", [22 x i8]* %tmp_27_str, align 1
  %tmp_28 = bitcast [22 x i8]* %tmp_27_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_28)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_29_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_29_str, align 1
  %tmp_30 = bitcast [1 x i8]* %tmp_29_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Line 1" (inline)
  %tmp_31_str = alloca [7 x i8], align 1
  store [7 x i8] c"Line 1\00", [7 x i8]* %tmp_31_str, align 1
  %tmp_32 = bitcast [7 x i8]* %tmp_31_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Line 2" (inline)
  %tmp_33_str = alloca [7 x i8], align 1
  store [7 x i8] c"Line 2\00", [7 x i8]* %tmp_33_str, align 1
  %tmp_34 = bitcast [7 x i8]* %tmp_33_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_34)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Line 3" (inline)
  %tmp_35_str = alloca [7 x i8], align 1
  store [7 x i8] c"Line 3\00", [7 x i8]* %tmp_35_str, align 1
  %tmp_36 = bitcast [7 x i8]* %tmp_35_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_36)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_37_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_37_str, align 1
  %tmp_38 = bitcast [1 x i8]* %tmp_37_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_38)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_39_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_39_str, align 1
  %tmp_40 = bitcast [1 x i8]* %tmp_39_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_40)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Empty line printed above" (inline)
  %tmp_41_str = alloca [25 x i8], align 1
  store [25 x i8] c"Empty line printed above\00", [25 x i8]* %tmp_41_str, align 1
  %tmp_42 = bitcast [25 x i8]* %tmp_41_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_42)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_43_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_43_str, align 1
  %tmp_44 = bitcast [1 x i8]* %tmp_43_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_44)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== End of Basic Syntax Demo ===" (inline)
  %tmp_45_str = alloca [33 x i8], align 1
  store [33 x i8] c"=== End of Basic Syntax Demo ===\00", [33 x i8]* %tmp_45_str, align 1
  %tmp_46 = bitcast [33 x i8]* %tmp_45_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_46)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
