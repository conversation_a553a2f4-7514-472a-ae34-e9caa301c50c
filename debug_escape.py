#!/usr/bin/env python3

def test_escape():
    # Test string with actual newline
    test_string = "Line1\nLine2"
    print(f"Original string: {repr(test_string)}")
    print(f"Length: {len(test_string)}")
    
    # Convert to UTF-8 bytes
    utf8_bytes = test_string.encode('utf-8')
    print(f"UTF-8 bytes: {list(utf8_bytes)}")
    
    # Manual escape
    result = ""
    for byte in utf8_bytes:
        if byte == 10:  # newline
            result += '\\0A'
        elif byte == 9:  # tab
            result += '\\09'
        else:
            result += chr(byte)
    
    print(f"Escaped result: {repr(result)}")

if __name__ == "__main__":
    test_escape()
