// 15. Imports and Modules - using external code (Rust version)
// This file demonstrates imports and modules in Rust for comparison with Dolet

// Standard library imports
use std::collections::HashMap;
use std::fs;
use std::io::{self, Write};
use std::time::{SystemTime, UNIX_EPOCH};

// Math module imports
use std::f64::consts::{PI, E};

// Multiple imports from same module
use std::cmp::{max, min};

fn main() {
    println!("=== Imports and Mo<PERSON><PERSON> Demo (Rust) ===");
    println!();

    // Using imported math constants
    println!("Using imported math constants:");
    println!("π (PI) = {:.10}", PI);
    println!("e (<PERSON><PERSON><PERSON>'s number) = {:.10}", E);
    println!();

    // Using imported comparison functions
    println!("Using imported comparison functions:");
    let a = 15;
    let b = 23;
    println!("max({}, {}) = {}", a, b, max(a, b));
    println!("min({}, {}) = {}", a, b, min(a, b));
    println!();

    // Using HashMap from collections
    println!("Using HashMap from collections:");
    let mut scores = HashMap::new();
    scores.insert("Ahmed", 95);
    scores.insert("Sara", 87);
    scores.insert("Omar", 92);
    
    println!("Student scores:");
    for (name, score) in &scores {
        println!("  {}: {}", name, score);
    }
    
    // Accessing specific values
    if let Some(score) = scores.get("Ahmed") {
        println!("Ahmed's score: {}", score);
    }
    println!();

    // Using time functions
    println!("Using time functions:");
    let now = SystemTime::now();
    match now.duration_since(UNIX_EPOCH) {
        Ok(duration) => {
            println!("Current Unix timestamp: {}", duration.as_secs());
            println!("Milliseconds: {}", duration.as_millis());
        }
        Err(e) => println!("Error getting time: {}", e),
    }
    println!();

    // File system operations
    println!("File system operations:");
    let test_content = "Hello from Rust modules demo!\nThis demonstrates file I/O.";
    
    // Write to file
    match fs::write("module_test.txt", test_content) {
        Ok(_) => println!("✓ File written successfully"),
        Err(e) => println!("✗ Error writing file: {}", e),
    }
    
    // Read from file
    match fs::read_to_string("module_test.txt") {
        Ok(content) => {
            println!("✓ File read successfully:");
            println!("Content: {}", content);
        }
        Err(e) => println!("✗ Error reading file: {}", e),
    }
    println!();

    // Using I/O functions
    println!("Using I/O functions:");
    print!("This is printed without newline... ");
    io::stdout().flush().unwrap(); // Flush the output buffer
    println!("and this completes the line!");
    println!();

    // Mathematical calculations using imported constants
    println!("Mathematical calculations using imported constants:");
    let radius = 7.5;
    let circle_area = PI * radius * radius;
    let circle_circumference = 2.0 * PI * radius;
    
    println!("Circle with radius {}:", radius);
    println!("  Area = π × r² = {:.2}", circle_area);
    println!("  Circumference = 2π × r = {:.2}", circle_circumference);
    println!();

    // Exponential calculations using E
    println!("Exponential calculations:");
    let x = 2.0;
    let exp_result = E.powf(x);
    let ln_result = exp_result.ln();
    
    println!("e^{} = {:.6}", x, exp_result);
    println!("ln({:.6}) = {:.6}", exp_result, ln_result);
    println!();

    // Using multiple imported functions together
    println!("Using multiple imported functions together:");
    let numbers = vec![45, 23, 67, 12, 89, 34, 56];
    let max_num = numbers.iter().max().unwrap();
    let min_num = numbers.iter().min().unwrap();
    let sum: i32 = numbers.iter().sum();
    let avg = sum as f64 / numbers.len() as f64;
    
    println!("Numbers: {:?}", numbers);
    println!("Maximum: {}", max_num);
    println!("Minimum: {}", min_num);
    println!("Sum: {}", sum);
    println!("Average: {:.2}", avg);
    println!("Range: {}", max_num - min_num);
    println!();

    // HashMap advanced operations
    println!("HashMap advanced operations:");
    let mut inventory = HashMap::new();
    inventory.insert("apples", 50);
    inventory.insert("bananas", 30);
    inventory.insert("oranges", 25);
    
    println!("Initial inventory:");
    for (item, quantity) in &inventory {
        println!("  {}: {}", item, quantity);
    }
    
    // Update quantities
    *inventory.get_mut("apples").unwrap() += 20;
    inventory.insert("grapes", 40);
    
    println!("Updated inventory:");
    for (item, quantity) in &inventory {
        println!("  {}: {}", item, quantity);
    }
    
    // Check if item exists
    if inventory.contains_key("apples") {
        println!("We have apples in stock!");
    }
    
    // Remove an item
    inventory.remove("bananas");
    println!("After removing bananas: {} items in inventory", inventory.len());
    println!();

    // String operations with imported functions
    println!("String operations:");
    let text = "Hello, World! This is a test string.";
    println!("Original text: '{}'", text);
    println!("Length: {}", text.len());
    println!("Contains 'World': {}", text.contains("World"));
    println!("Starts with 'Hello': {}", text.starts_with("Hello"));
    println!("Ends with 'string.': {}", text.ends_with("string."));
    
    let words: Vec<&str> = text.split_whitespace().collect();
    println!("Words: {:?}", words);
    println!("Word count: {}", words.len());
    println!();

    // Using custom module functions
    println!("Using custom module functions:");
    let calc_result1 = math_utils::add(10, 5);
    let calc_result2 = math_utils::multiply(7, 8);
    let calc_result3 = math_utils::power(2, 4);
    
    println!("add(10, 5) = {}", calc_result1);
    println!("multiply(7, 8) = {}", calc_result2);
    println!("power(2, 4) = {}", calc_result3);
    println!();

    // String utilities
    println!("String utilities:");
    let test_str = "hello world";
    let capitalized = string_utils::capitalize(&test_str);
    let word_count = string_utils::count_words(&test_str);
    let reversed = string_utils::reverse(&test_str);
    
    println!("Original: '{}'", test_str);
    println!("Capitalized: '{}'", capitalized);
    println!("Word count: {}", word_count);
    println!("Reversed: '{}'", reversed);
    println!();

    // Array utilities
    println!("Array utilities:");
    let data = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let evens = array_utils::filter_even(&data);
    let doubled = array_utils::double_values(&data);
    let sum_result = array_utils::sum(&data);
    
    println!("Original data: {:?}", data);
    println!("Even numbers: {:?}", evens);
    println!("Doubled values: {:?}", doubled);
    println!("Sum: {}", sum_result);
    println!();

    println!("=== End of Imports and Modules Demo ===");
}

// Custom modules defined inline for demonstration
mod math_utils {
    pub fn add(a: i32, b: i32) -> i32 {
        a + b
    }
    
    pub fn multiply(a: i32, b: i32) -> i32 {
        a * b
    }
    
    pub fn power(base: i32, exp: u32) -> i32 {
        base.pow(exp)
    }
}

mod string_utils {
    pub fn capitalize(s: &str) -> String {
        let mut chars: Vec<char> = s.chars().collect();
        if !chars.is_empty() {
            chars[0] = chars[0].to_uppercase().next().unwrap_or(chars[0]);
        }
        chars.into_iter().collect()
    }
    
    pub fn count_words(s: &str) -> usize {
        s.split_whitespace().count()
    }
    
    pub fn reverse(s: &str) -> String {
        s.chars().rev().collect()
    }
}

mod array_utils {
    pub fn filter_even(arr: &[i32]) -> Vec<i32> {
        arr.iter().filter(|&&x| x % 2 == 0).cloned().collect()
    }
    
    pub fn double_values(arr: &[i32]) -> Vec<i32> {
        arr.iter().map(|&x| x * 2).collect()
    }
    
    pub fn sum(arr: &[i32]) -> i32 {
        arr.iter().sum()
    }
}
