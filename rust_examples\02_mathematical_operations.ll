; ModuleID = '02_mathematical_operations.36499ee3f0ae387d-cgu.0'
source_filename = "02_mathematical_operations.36499ee3f0ae387d-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h205afa4ee1fc11a7E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd20251d1076b07d5E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd20251d1076b07d5E" }>, align 8
@anon.1341996f79a21f35a9a684b522082992.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_3f7edcf34442052c4bbfb44459ac5e5a = private unnamed_addr constant [75 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\num\\mod.rs", align 1
@alloc_6dad818da9723183e1ae0c6550f5b463 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_3f7edcf34442052c4bbfb44459ac5e5a, [16 x i8] c"K\00\00\00\00\00\00\00\1C\01\00\00\05\00\00\00" }>, align 8
@alloc_05f4496f07cd45cf7bc060589a0cb2d1 = private unnamed_addr constant [44 x i8] c"=== Mathematical Operations Demo (Rust) ===\0A", align 1
@alloc_89fcc3db5f16eea409184da9ea85b61c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_05f4496f07cd45cf7bc060589a0cb2d1, [8 x i8] c",\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_09f9eb9ae25af8eecc01fe2c443d4579 = private unnamed_addr constant [30 x i8] c"Basic Arithmetic (a=15, b=4):\0A", align 1
@alloc_58c9ba85c50a7628799c810bdca9a3b8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09f9eb9ae25af8eecc01fe2c443d4579, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_c5ac4ddba2a469cec06128ec840df2cb = private unnamed_addr constant [10 x i8] c"Addition: ", align 1
@alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf = private unnamed_addr constant [3 x i8] c" + ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_38e8ffe3ec979a6f4e0597d37318aa35 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c5ac4ddba2a469cec06128ec840df2cb, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_81349833ecc93dfedd05d2957dee230f = private unnamed_addr constant [29 x i8] c"02_mathematical_operations.rs", align 1
@alloc_80be1b397569490ad9766371626ea7e1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\0C\00\00\00.\00\00\00" }>, align 8
@alloc_78afebf5e831459291108b421ced022f = private unnamed_addr constant [13 x i8] c"Subtraction: ", align 1
@alloc_e2b8ff18cd338c23dd4ea4f16738ab74 = private unnamed_addr constant [3 x i8] c" - ", align 1
@alloc_15eff329c3d932f04ea78d5c4b3dc4b3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_78afebf5e831459291108b421ced022f, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_e2b8ff18cd338c23dd4ea4f16738ab74, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_74eed31d726cb49f19ecb4b071c7b0c6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\0D\00\00\001\00\00\00" }>, align 8
@alloc_7efb7f702e8916da6241d380107b31ac = private unnamed_addr constant [16 x i8] c"Multiplication: ", align 1
@alloc_26402fef52f74f1c44c90ec1eef0b4f1 = private unnamed_addr constant [3 x i8] c" * ", align 1
@alloc_9205339a3c06fe0f3e9c8c058e63de60 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7efb7f702e8916da6241d380107b31ac, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b51ffa590b44becd56066e0bab24a51e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\0E\00\00\004\00\00\00" }>, align 8
@alloc_34c8dd8d1c88547ea17d3cf121cb9d64 = private unnamed_addr constant [10 x i8] c"Division: ", align 1
@alloc_37d018637e55331d5bf3277fd5414bcf = private unnamed_addr constant [3 x i8] c" / ", align 1
@alloc_86b009f67958aaa915fb241a08c263fe = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_34c8dd8d1c88547ea17d3cf121cb9d64, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_37d018637e55331d5bf3277fd5414bcf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5c775938ff2283854783288ad0e1fa33 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\0F\00\00\00.\00\00\00" }>, align 8
@alloc_8765f3a5aac858c897dc62a771a18b1d = private unnamed_addr constant [8 x i8] c"Modulo: ", align 1
@alloc_21177a8874a7bbd02007689e9f6228b6 = private unnamed_addr constant [3 x i8] c" % ", align 1
@alloc_740aa841f8f493ef8fe6d5e5399521d5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8765f3a5aac858c897dc62a771a18b1d, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_21177a8874a7bbd02007689e9f6228b6, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_10c675ff07031e747ec9626450802dcd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\10\00\00\00,\00\00\00" }>, align 8
@alloc_2848a08591699a7894f64c168bace3ff = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\14\00\00\00\13\00\00\00" }>, align 8
@alloc_773b852e4f528c9e05aaa8be28862813 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\15\00\00\00\17\00\00\00" }>, align 8
@alloc_3b07814057dae4f70d36d1844904a306 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\15\00\00\00\13\00\00\00" }>, align 8
@alloc_65c11fcdbc53aea8434cf9a792e625bc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\16\00\00\00\13\00\00\00" }>, align 8
@alloc_09c8d6fb85ede852552849111b99755c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\16\00\00\00\1D\00\00\00" }>, align 8
@alloc_ae28320d6ecb42143d64761cee677e16 = private unnamed_addr constant [29 x i8] c"Operations with Parentheses:\0A", align 1
@alloc_4129cd9ab1076091e994e6706beda7d3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ae28320d6ecb42143d64761cee677e16, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_6d246fc19f113a3b517909af9a71eb00 = private unnamed_addr constant [14 x i8] c"(a + b) * 2 = ", align 1
@alloc_5b0bc919ce155623a6487d10c3543f30 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6d246fc19f113a3b517909af9a71eb00, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_08be088419afd3fcf98ac49f8791c00f = private unnamed_addr constant [14 x i8] c"a * (b + 5) = ", align 1
@alloc_9d5ebae3644c593c94668efc4df629cc = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_08be088419afd3fcf98ac49f8791c00f, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d313568d1dd89e6458e64b214d2401f0 = private unnamed_addr constant [20 x i8] c"(a * 2) + (b * 3) = ", align 1
@alloc_22be2209f2c12aa59dadf72d042a2e6a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d313568d1dd89e6458e64b214d2401f0, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e58d664dd796c2abb0a1630abb3e8a0e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1E\00\00\00\15\00\00\00" }>, align 8
@alloc_d61e293a0001e4bf611b2ea3f42cfd2c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1E\00\00\00\14\00\00\00" }>, align 8
@alloc_ed5c22227202f6f8ec7d35c939585fb4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1E\00\00\00$\00\00\00" }>, align 8
@alloc_2a92babf017e0816b3892420c970bf67 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1F\00\00\00\14\00\00\00" }>, align 8
@alloc_8a8aca99d705eec1f62988618b34bc36 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1F\00\00\00\1F\00\00\00" }>, align 8
@alloc_6b70f61efb29f3bb96f9788ea6b49951 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00\1F\00\00\00\1E\00\00\00" }>, align 8
@alloc_127813faeadcb162fc67b4a534888edb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00 \00\00\00\15\00\00\00" }>, align 8
@alloc_89aba8f9baf03f3f6fb22c73d3207df5 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00 \00\00\00\1F\00\00\00" }>, align 8
@alloc_8dbafd60f99f37e7c2b90d0a951ade38 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00 \00\00\00\14\00\00\00" }>, align 8
@alloc_d33204f637189479b5aa5f43d26cb375 = private unnamed_addr constant [28 x i8] c"Complex Nested Expressions:\0A", align 1
@alloc_b5e432210ac6a9954e8d42c563d2f3f8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d33204f637189479b5aa5f43d26cb375, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_7d1ef28b14946e10e66fdd4846c25a79 = private unnamed_addr constant [26 x i8] c"((a + b) * 2) - (a / b) = ", align 1
@alloc_2a24c8367bbf27ec4c7520595247c58a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7d1ef28b14946e10e66fdd4846c25a79, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_111b61462097cc4a7c2896e90eeebe8d = private unnamed_addr constant [26 x i8] c"(a * b) + ((a - b) * 2) = ", align 1
@alloc_0c010d16ed8437a9c54f278d20196ebb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_111b61462097cc4a7c2896e90eeebe8d, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_940814b3e3b4847163c7f1aee63d1795 = private unnamed_addr constant [26 x i8] c"((a + 5) * (b - 1)) / 2 = ", align 1
@alloc_9affd498751a5b6dc3f0f41ca0598362 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_940814b3e3b4847163c7f1aee63d1795, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_94f609b464e603cec712c1634a1a9123 = private unnamed_addr constant [34 x i8] c"Float Operations (x=10.5, y=3.2):\0A", align 1
@alloc_60c82de1804578c9882be35b92238261 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_94f609b464e603cec712c1634a1a9123, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_b5dbbc40ea67c9fc2b9dcb02ba3fb9c9 = private unnamed_addr constant [8 x i8] c"x + y = ", align 1
@alloc_54112bfeaebbf72930828791f3277879 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b5dbbc40ea67c9fc2b9dcb02ba3fb9c9, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_608301b5abb8bb62cf6b0ad332757888 = private unnamed_addr constant [8 x i8] c"x - y = ", align 1
@alloc_47179c1800567d33b9799d799e75d801 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_608301b5abb8bb62cf6b0ad332757888, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_83ddf18609b1c91586c5f1f1eebf403f = private unnamed_addr constant [8 x i8] c"x * y = ", align 1
@alloc_2cf3b54281497c2cc9a72e5dba0eb7a8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_83ddf18609b1c91586c5f1f1eebf403f, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_654b331bc7fa36ca19fc5cab621b6bfa = private unnamed_addr constant [8 x i8] c"x / y = ", align 1
@alloc_7c63f59770a35f3d355a97946474b6ea = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_654b331bc7fa36ca19fc5cab621b6bfa, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b3305f97a6839c60381e125b352f5719 = private unnamed_addr constant [23 x i8] c"Mixed Type Operations:\0A", align 1
@alloc_06129da6dfeff9c513c4e74ab49da3d4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b3305f97a6839c60381e125b352f5719, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_ea1e1450c2f45f25d3fe7c38e5a651ce = private unnamed_addr constant [13 x i8] c"int + float: ", align 1
@alloc_7fc420f7aa60691ab657ff678d587972 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ea1e1450c2f45f25d3fe7c38e5a651ce, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ef713a0f798b10a942ca9fcf3f320a73 = private unnamed_addr constant [13 x i8] c"int * float: ", align 1
@alloc_6cd41dc0894bbaf5943188c89a0a71e4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ef713a0f798b10a942ca9fcf3f320a73, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_26402fef52f74f1c44c90ec1eef0b4f1, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_af3b0d514f324200b666877b400fc5b6 = private unnamed_addr constant [28 x i8] c"Negative Number Operations:\0A", align 1
@alloc_7e5275f6fdff86590c5b2ab586eedb82 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_af3b0d514f324200b666877b400fc5b6, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_c880ceac9472a9342b9b81ec01709f80 = private unnamed_addr constant [14 x i8] c"(-5) + (-3) = ", align 1
@alloc_f3ff3fc7c6a3137788a8a0956363cec1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c880ceac9472a9342b9b81ec01709f80, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_45e51c78af411cdebb27a96236020820 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00=\00\00\00\22\00\00\00" }>, align 8
@alloc_bbb1eed4549669f5c02a86e9a77d78fa = private unnamed_addr constant [14 x i8] c"(-5) * (-3) = ", align 1
@alloc_83d40ba59aa0a73fb9398e69c377428f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bbb1eed4549669f5c02a86e9a77d78fa, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2ecf06e8d3e6fb07787954df2ce9118d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00>\00\00\00\22\00\00\00" }>, align 8
@alloc_69349d084cd5f145ca834686861dfbc7 = private unnamed_addr constant [14 x i8] c"(-5) - (-3) = ", align 1
@alloc_c0921044fe0c398035de1c238a558292 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_69349d084cd5f145ca834686861dfbc7, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c6af346309caf6c9c4bbe32841acaa8e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00?\00\00\00\22\00\00\00" }>, align 8
@alloc_fb9dfca6f0d1038a8ce28768376e6853 = private unnamed_addr constant [28 x i8] c"Assignment with operations:\0A", align 1
@alloc_fb212613a2a46962f28ab6c3278093c7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fb9dfca6f0d1038a8ce28768376e6853, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_8c2ae598e8ef7fac4c875acab19cdfae = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00E\00\00\00\05\00\00\00" }>, align 8
@alloc_0dcdf17dd87c2b9853ae3332a7dc5c67 = private unnamed_addr constant [25 x i8] c"counter after increment: ", align 1
@alloc_e7e975d66460c09f9e59c44583817025 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0dcdf17dd87c2b9853ae3332a7dc5c67, [8 x i8] c"\19\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9653a19b250d463ac66a3451736dd1cc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00G\00\00\00\05\00\00\00" }>, align 8
@alloc_2947848a03561a86cc9728e74dfca53a = private unnamed_addr constant [24 x i8] c"counter after doubling: ", align 1
@alloc_f35c991d4906723d647d8e191f07eaf8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2947848a03561a86cc9728e74dfca53a, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_dcad562e1110bed89974c5c889070375 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00I\00\00\00\05\00\00\00" }>, align 8
@alloc_5afe04982513253b43e6929bd90c3e84 = private unnamed_addr constant [29 x i8] c"counter after subtracting 5: ", align 1
@alloc_4a8f3fd87573436947a4c9e370e82972 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5afe04982513253b43e6929bd90c3e84, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e0b74e583a94b7d95c003b657b3b7c99 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00N\00\00\00\18\00\00\00" }>, align 8
@alloc_bac0bbdbcf24da6e913051fc0e2fc891 = private unnamed_addr constant [38 x i8] c"Chain operations: 1 + 2 + 3 + 4 + 5 = ", align 1
@alloc_8f4249dc54fdd48cd209f29ba8b1083b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bac0bbdbcf24da6e913051fc0e2fc891, [8 x i8] c"&\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_345aa6456900b637ba9bbbffa173eb55 = private unnamed_addr constant [27 x i8] c"Power operations (base=2):\0A", align 1
@alloc_0d1ff6ed40b992bf5d078e145b08b988 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_345aa6456900b637ba9bbbffa173eb55, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_e34d9eb88a82e85ca5054b6abebc6706 = private unnamed_addr constant [6 x i8] c"2^2 = ", align 1
@alloc_5c38de46dddfb09a13c6ea478ef46644 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e34d9eb88a82e85ca5054b6abebc6706, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a5c4c782def4d4ce699b8f8de82f9e0b = private unnamed_addr constant [6 x i8] c"2^3 = ", align 1
@alloc_67adefb42d8f3738b163ca17692b3d72 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a5c4c782def4d4ce699b8f8de82f9e0b, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7fa8883f3f530549a78410e585ef1fd7 = private unnamed_addr constant [6 x i8] c"2^4 = ", align 1
@alloc_b9dd465725ee0d65418377188b230fac = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7fa8883f3f530549a78410e585ef1fd7, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_af6e3f5c2166e7777fc028c6904f92a9 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00^\00\00\00\08\00\00\00" }>, align 8
@alloc_4105929b9d6c7151813c3fb9b3f0d994 = private unnamed_addr constant [27 x i8] c"num * 2 is greater than 25\0A", align 1
@alloc_dd5c0bf3e6e8c8a39dcc9d2db50c8e07 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4105929b9d6c7151813c3fb9b3f0d994, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_0df34a43145809935b840fa271228286 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_81349833ecc93dfedd05d2957dee230f, [16 x i8] c"\1D\00\00\00\00\00\00\00b\00\00\00\08\00\00\00" }>, align 8
@alloc_2f7098719db247707a8b435eb9f5bff0 = private unnamed_addr constant [18 x i8] c"num + 5 equals 20\0A", align 1
@alloc_cf8938a90160e9dc07cbefa234cede93 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2f7098719db247707a8b435eb9f5bff0, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_8822f0a226cd546f6490a025cc67cf23 = private unnamed_addr constant [44 x i8] c"=== End of Mathematical Operations Demo ===\0A", align 1
@alloc_5d3e0476b3d05b5b75fe3500eb55214a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8822f0a226cd546f6490a025cc67cf23, [8 x i8] c",\00\00\00\00\00\00\00" }>, align 8

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h2d957aede228f0b3E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd20251d1076b07d5E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h6e4e376fcdc84c90E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hc12371704d66d3ebE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h6e4e376fcdc84c90E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17hd6b4734353f3d834E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; core::intrinsics::is_val_statically_known
; Function Attrs: nounwind uwtable
define internal zeroext i1 @_ZN4core10intrinsics23is_val_statically_known17hc358000d283c81dcE(i32 %_arg) unnamed_addr #3 {
start:
  ret i1 false
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f32$GT$3fmt17h3f1d24a0f47d0dccE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.1341996f79a21f35a9a684b522082992.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.1341996f79a21f35a9a684b522082992.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.1341996f79a21f35a9a684b522082992.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.1341996f79a21f35a9a684b522082992.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.1341996f79a21f35a9a684b522082992.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.1341996f79a21f35a9a684b522082992.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl i32>::pow
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3num21_$LT$impl$u20$i32$GT$3pow17h8d97c2fdb90ab43bE"(i32 %self, i32 %0) unnamed_addr #1 {
start:
  %1 = alloca [1 x i8], align 1
  %acc = alloca [4 x i8], align 4
  %base = alloca [4 x i8], align 4
  %_0 = alloca [4 x i8], align 4
  %exp = alloca [4 x i8], align 4
  store i32 %0, ptr %exp, align 4
  %_3 = load i32, ptr %exp, align 4
  %2 = icmp eq i32 %_3, 0
  br i1 %2, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i32 1, ptr %_0, align 4
  br label %bb23

bb2:                                              ; preds = %start
  store i32 %self, ptr %base, align 4
  store i32 1, ptr %acc, align 4
  %_7 = load i32, ptr %exp, align 4
  %3 = call i1 @llvm.is.constant.i32(i32 %_7)
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %1, align 1
  %5 = load i8, ptr %1, align 1
  %_6 = trunc nuw i8 %5 to i1
  br i1 %_6, label %bb4, label %bb14

bb23:                                             ; preds = %bb13, %bb18, %bb1
  %6 = load i32, ptr %_0, align 4
  ret i32 %6

bb14:                                             ; preds = %bb2
  br label %bb15

bb4:                                              ; preds = %bb2
  br label %bb5

bb15:                                             ; preds = %bb22, %bb14
  %_22 = load i32, ptr %exp, align 4
  %_21 = and i32 %_22, 1
  %7 = icmp eq i32 %_21, 1
  br i1 %7, label %bb16, label %bb20

bb16:                                             ; preds = %bb15
  %_23 = load i32, ptr %acc, align 4
  %_24 = load i32, ptr %base, align 4
  %8 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_23, i32 %_24)
  %_25.0 = extractvalue { i32, i1 } %8, 0
  %_25.1 = extractvalue { i32, i1 } %8, 1
  br i1 %_25.1, label %panic, label %bb17

bb20:                                             ; preds = %bb15
  br label %bb21

bb17:                                             ; preds = %bb16
  store i32 %_25.0, ptr %acc, align 4
  %_26 = load i32, ptr %exp, align 4
  %9 = icmp eq i32 %_26, 1
  br i1 %9, label %bb18, label %bb19

panic:                                            ; preds = %bb16
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6dad818da9723183e1ae0c6550f5b463) #9
  unreachable

bb18:                                             ; preds = %bb17
  %10 = load i32, ptr %acc, align 4
  store i32 %10, ptr %_0, align 4
  br label %bb23

bb19:                                             ; preds = %bb17
  br label %bb21

bb21:                                             ; preds = %bb20, %bb19
  %11 = load i32, ptr %exp, align 4
  %12 = udiv i32 %11, 2
  store i32 %12, ptr %exp, align 4
  %_27 = load i32, ptr %base, align 4
  %_28 = load i32, ptr %base, align 4
  %13 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_27, i32 %_28)
  %_29.0 = extractvalue { i32, i1 } %13, 0
  %_29.1 = extractvalue { i32, i1 } %13, 1
  br i1 %_29.1, label %panic1, label %bb22

bb22:                                             ; preds = %bb21
  store i32 %_29.0, ptr %base, align 4
  br label %bb15

panic1:                                           ; preds = %bb21
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6dad818da9723183e1ae0c6550f5b463) #9
  unreachable

bb5:                                              ; preds = %bb11, %bb4
  %_9 = load i32, ptr %exp, align 4
  %_8 = icmp ugt i32 %_9, 1
  br i1 %_8, label %bb6, label %bb12

bb12:                                             ; preds = %bb5
  %_18 = load i32, ptr %acc, align 4
  %_19 = load i32, ptr %base, align 4
  %14 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_18, i32 %_19)
  %_20.0 = extractvalue { i32, i1 } %14, 0
  %_20.1 = extractvalue { i32, i1 } %14, 1
  br i1 %_20.1, label %panic2, label %bb13

bb6:                                              ; preds = %bb5
  %_11 = load i32, ptr %exp, align 4
  %_10 = and i32 %_11, 1
  %15 = icmp eq i32 %_10, 1
  br i1 %15, label %bb7, label %bb9

bb13:                                             ; preds = %bb12
  store i32 %_20.0, ptr %_0, align 4
  br label %bb23

panic2:                                           ; preds = %bb12
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6dad818da9723183e1ae0c6550f5b463) #9
  unreachable

bb7:                                              ; preds = %bb6
  %_12 = load i32, ptr %acc, align 4
  %_13 = load i32, ptr %base, align 4
  %16 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_12, i32 %_13)
  %_14.0 = extractvalue { i32, i1 } %16, 0
  %_14.1 = extractvalue { i32, i1 } %16, 1
  br i1 %_14.1, label %panic3, label %bb8

bb9:                                              ; preds = %bb6
  br label %bb10

bb8:                                              ; preds = %bb7
  store i32 %_14.0, ptr %acc, align 4
  br label %bb10

panic3:                                           ; preds = %bb7
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6dad818da9723183e1ae0c6550f5b463) #9
  unreachable

bb10:                                             ; preds = %bb9, %bb8
  %17 = load i32, ptr %exp, align 4
  %18 = udiv i32 %17, 2
  store i32 %18, ptr %exp, align 4
  %_15 = load i32, ptr %base, align 4
  %_16 = load i32, ptr %base, align 4
  %19 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_15, i32 %_16)
  %_17.0 = extractvalue { i32, i1 } %19, 0
  %_17.1 = extractvalue { i32, i1 } %19, 1
  br i1 %_17.1, label %panic4, label %bb11

bb11:                                             ; preds = %bb10
  store i32 %_17.0, ptr %base, align 4
  br label %bb5

panic4:                                           ; preds = %bb10
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6dad818da9723183e1ae0c6550f5b463) #9
  unreachable
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h205afa4ee1fc11a7E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h9770b1b0a63a4607E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h9770b1b0a63a4607E(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hd20251d1076b07d5E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17hd6b4734353f3d834E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h5aa42e2a43b75a96E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hc12371704d66d3ebE"() unnamed_addr #1 {
start:
  ret i32 0
}

; _02_mathematical_operations::main
; Function Attrs: uwtable
define internal void @_ZN27_02_mathematical_operations4main17h8eb9315c7e762f73E() unnamed_addr #0 {
start:
  %_386 = alloca [48 x i8], align 8
  %_383 = alloca [48 x i8], align 8
  %_380 = alloca [48 x i8], align 8
  %_375 = alloca [48 x i8], align 8
  %_368 = alloca [48 x i8], align 8
  %_365 = alloca [16 x i8], align 8
  %_364 = alloca [16 x i8], align 8
  %_361 = alloca [48 x i8], align 8
  %_358 = alloca [16 x i8], align 8
  %_357 = alloca [16 x i8], align 8
  %_354 = alloca [48 x i8], align 8
  %_351 = alloca [16 x i8], align 8
  %_350 = alloca [16 x i8], align 8
  %_347 = alloca [48 x i8], align 8
  %_344 = alloca [48 x i8], align 8
  %power4 = alloca [4 x i8], align 4
  %power3 = alloca [4 x i8], align 4
  %power2 = alloca [4 x i8], align 4
  %_337 = alloca [16 x i8], align 8
  %_336 = alloca [16 x i8], align 8
  %_333 = alloca [48 x i8], align 8
  %chain_result = alloca [4 x i8], align 4
  %_322 = alloca [48 x i8], align 8
  %_319 = alloca [16 x i8], align 8
  %_318 = alloca [16 x i8], align 8
  %_315 = alloca [48 x i8], align 8
  %_311 = alloca [16 x i8], align 8
  %_310 = alloca [16 x i8], align 8
  %_307 = alloca [48 x i8], align 8
  %_303 = alloca [16 x i8], align 8
  %_302 = alloca [16 x i8], align 8
  %_299 = alloca [48 x i8], align 8
  %_295 = alloca [48 x i8], align 8
  %counter = alloca [4 x i8], align 4
  %_291 = alloca [48 x i8], align 8
  %_288 = alloca [4 x i8], align 4
  %_286 = alloca [16 x i8], align 8
  %_285 = alloca [16 x i8], align 8
  %_282 = alloca [48 x i8], align 8
  %_279 = alloca [4 x i8], align 4
  %_277 = alloca [16 x i8], align 8
  %_276 = alloca [16 x i8], align 8
  %_273 = alloca [48 x i8], align 8
  %_270 = alloca [4 x i8], align 4
  %_268 = alloca [16 x i8], align 8
  %_267 = alloca [16 x i8], align 8
  %_264 = alloca [48 x i8], align 8
  %_261 = alloca [48 x i8], align 8
  %_256 = alloca [48 x i8], align 8
  %_253 = alloca [4 x i8], align 4
  %_251 = alloca [16 x i8], align 8
  %_249 = alloca [16 x i8], align 8
  %_247 = alloca [16 x i8], align 8
  %_246 = alloca [48 x i8], align 8
  %_243 = alloca [48 x i8], align 8
  %_240 = alloca [4 x i8], align 4
  %_238 = alloca [16 x i8], align 8
  %_236 = alloca [16 x i8], align 8
  %_234 = alloca [16 x i8], align 8
  %_233 = alloca [48 x i8], align 8
  %_230 = alloca [48 x i8], align 8
  %_227 = alloca [48 x i8], align 8
  %float_val = alloca [4 x i8], align 4
  %int_val = alloca [4 x i8], align 4
  %_222 = alloca [48 x i8], align 8
  %_220 = alloca [4 x i8], align 4
  %_218 = alloca [16 x i8], align 8
  %_217 = alloca [16 x i8], align 8
  %_214 = alloca [48 x i8], align 8
  %_212 = alloca [4 x i8], align 4
  %_210 = alloca [16 x i8], align 8
  %_209 = alloca [16 x i8], align 8
  %_206 = alloca [48 x i8], align 8
  %_204 = alloca [4 x i8], align 4
  %_202 = alloca [16 x i8], align 8
  %_201 = alloca [16 x i8], align 8
  %_198 = alloca [48 x i8], align 8
  %_196 = alloca [4 x i8], align 4
  %_194 = alloca [16 x i8], align 8
  %_193 = alloca [16 x i8], align 8
  %_190 = alloca [48 x i8], align 8
  %_187 = alloca [48 x i8], align 8
  %_182 = alloca [48 x i8], align 8
  %_179 = alloca [16 x i8], align 8
  %_178 = alloca [16 x i8], align 8
  %_175 = alloca [48 x i8], align 8
  %_172 = alloca [16 x i8], align 8
  %_171 = alloca [16 x i8], align 8
  %_168 = alloca [48 x i8], align 8
  %_165 = alloca [16 x i8], align 8
  %_164 = alloca [16 x i8], align 8
  %_161 = alloca [48 x i8], align 8
  %_158 = alloca [48 x i8], align 8
  %complex3 = alloca [4 x i8], align 4
  %complex2 = alloca [4 x i8], align 4
  %complex1 = alloca [4 x i8], align 4
  %_125 = alloca [48 x i8], align 8
  %_122 = alloca [16 x i8], align 8
  %_121 = alloca [16 x i8], align 8
  %_118 = alloca [48 x i8], align 8
  %_115 = alloca [16 x i8], align 8
  %_114 = alloca [16 x i8], align 8
  %_111 = alloca [48 x i8], align 8
  %_108 = alloca [16 x i8], align 8
  %_107 = alloca [16 x i8], align 8
  %_104 = alloca [48 x i8], align 8
  %_101 = alloca [48 x i8], align 8
  %result3 = alloca [4 x i8], align 4
  %result2 = alloca [4 x i8], align 4
  %result1 = alloca [4 x i8], align 4
  %_84 = alloca [48 x i8], align 8
  %_78 = alloca [4 x i8], align 4
  %_76 = alloca [16 x i8], align 8
  %_74 = alloca [16 x i8], align 8
  %_72 = alloca [16 x i8], align 8
  %_71 = alloca [48 x i8], align 8
  %_68 = alloca [48 x i8], align 8
  %_62 = alloca [4 x i8], align 4
  %_60 = alloca [16 x i8], align 8
  %_58 = alloca [16 x i8], align 8
  %_56 = alloca [16 x i8], align 8
  %_55 = alloca [48 x i8], align 8
  %_52 = alloca [48 x i8], align 8
  %_49 = alloca [4 x i8], align 4
  %_47 = alloca [16 x i8], align 8
  %_45 = alloca [16 x i8], align 8
  %_43 = alloca [16 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %_39 = alloca [48 x i8], align 8
  %_36 = alloca [4 x i8], align 4
  %_34 = alloca [16 x i8], align 8
  %_32 = alloca [16 x i8], align 8
  %_30 = alloca [16 x i8], align 8
  %_29 = alloca [48 x i8], align 8
  %_26 = alloca [48 x i8], align 8
  %_23 = alloca [4 x i8], align 4
  %_21 = alloca [16 x i8], align 8
  %_19 = alloca [16 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_16 = alloca [48 x i8], align 8
  %_13 = alloca [48 x i8], align 8
  %_10 = alloca [48 x i8], align 8
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_89fcc3db5f16eea409184da9ea85b61c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  store i32 15, ptr %a, align 4
  store i32 4, ptr %b, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_10, ptr align 8 @alloc_58c9ba85c50a7628799c810bdca9a3b8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_10)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_17, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_19, ptr align 4 %b)
  %0 = load i32, ptr %a, align 4
  %1 = load i32, ptr %b, align 4
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %0, i32 %1)
  %_24.0 = extractvalue { i32, i1 } %2, 0
  %_24.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_24.1, label %panic, label %bb9

bb9:                                              ; preds = %start
  store i32 %_24.0, ptr %_23, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_21, ptr align 4 %_23)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_17, i64 16, i1 false)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_19, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_21, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_13, ptr align 8 @alloc_38e8ffe3ec979a6f4e0597d37318aa35, ptr align 8 %_16)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_13)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_30, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_32, ptr align 4 %b)
  %6 = load i32, ptr %a, align 4
  %7 = load i32, ptr %b, align 4
  %8 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %6, i32 %7)
  %_37.0 = extractvalue { i32, i1 } %8, 0
  %_37.1 = extractvalue { i32, i1 } %8, 1
  br i1 %_37.1, label %panic1, label %bb15

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_80be1b397569490ad9766371626ea7e1) #9
  unreachable

bb15:                                             ; preds = %bb9
  store i32 %_37.0, ptr %_36, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_34, ptr align 4 %_36)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_29, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_30, i64 16, i1 false)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_29, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_32, i64 16, i1 false)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_29, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_34, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_26, ptr align 8 @alloc_15eff329c3d932f04ea78d5c4b3dc4b3, ptr align 8 %_29)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_26)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_43, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_45, ptr align 4 %b)
  %12 = load i32, ptr %a, align 4
  %13 = load i32, ptr %b, align 4
  %14 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %12, i32 %13)
  %_50.0 = extractvalue { i32, i1 } %14, 0
  %_50.1 = extractvalue { i32, i1 } %14, 1
  br i1 %_50.1, label %panic2, label %bb21

panic1:                                           ; preds = %bb9
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_74eed31d726cb49f19ecb4b071c7b0c6) #9
  unreachable

bb21:                                             ; preds = %bb15
  store i32 %_50.0, ptr %_49, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_47, ptr align 4 %_49)
  %15 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_42, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %15, ptr align 8 %_43, i64 16, i1 false)
  %16 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_42, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %16, ptr align 8 %_45, i64 16, i1 false)
  %17 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_42, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %17, ptr align 8 %_47, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_39, ptr align 8 @alloc_9205339a3c06fe0f3e9c8c058e63de60, ptr align 8 %_42)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_39)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_56, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_58, ptr align 4 %b)
  %18 = load i32, ptr %b, align 4
  %_63 = icmp eq i32 %18, 0
  br i1 %_63, label %panic3, label %bb27

panic2:                                           ; preds = %bb15
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_b51ffa590b44becd56066e0bab24a51e) #9
  unreachable

bb27:                                             ; preds = %bb21
  %19 = load i32, ptr %b, align 4
  %_64 = icmp eq i32 %19, -1
  %20 = load i32, ptr %a, align 4
  %_65 = icmp eq i32 %20, -2147483648
  %_66 = and i1 %_64, %_65
  br i1 %_66, label %panic4, label %bb28

panic3:                                           ; preds = %bb21
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_5c775938ff2283854783288ad0e1fa33) #9
  unreachable

bb28:                                             ; preds = %bb27
  %21 = load i32, ptr %a, align 4
  %22 = load i32, ptr %b, align 4
  %23 = sdiv i32 %21, %22
  store i32 %23, ptr %_62, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_60, ptr align 4 %_62)
  %24 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_55, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %24, ptr align 8 %_56, i64 16, i1 false)
  %25 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_55, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %25, ptr align 8 %_58, i64 16, i1 false)
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_55, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_60, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_52, ptr align 8 @alloc_86b009f67958aaa915fb241a08c263fe, ptr align 8 %_55)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_52)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_72, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_74, ptr align 4 %b)
  %27 = load i32, ptr %b, align 4
  %_79 = icmp eq i32 %27, 0
  br i1 %_79, label %panic5, label %bb34

panic4:                                           ; preds = %bb27
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_5c775938ff2283854783288ad0e1fa33) #9
  unreachable

bb34:                                             ; preds = %bb28
  %28 = load i32, ptr %b, align 4
  %_80 = icmp eq i32 %28, -1
  %29 = load i32, ptr %a, align 4
  %_81 = icmp eq i32 %29, -2147483648
  %_82 = and i1 %_80, %_81
  br i1 %_82, label %panic6, label %bb35

panic5:                                           ; preds = %bb28
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 @alloc_10c675ff07031e747ec9626450802dcd) #9
  unreachable

bb35:                                             ; preds = %bb34
  %30 = load i32, ptr %a, align 4
  %31 = load i32, ptr %b, align 4
  %32 = srem i32 %30, %31
  store i32 %32, ptr %_78, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_76, ptr align 4 %_78)
  %33 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_71, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %33, ptr align 8 %_72, i64 16, i1 false)
  %34 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_71, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %34, ptr align 8 %_74, i64 16, i1 false)
  %35 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_71, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %35, ptr align 8 %_76, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_68, ptr align 8 @alloc_740aa841f8f493ef8fe6d5e5399521d5, ptr align 8 %_71)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_68)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_84, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_84)
  %36 = load i32, ptr %a, align 4
  %37 = load i32, ptr %b, align 4
  %38 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %36, i32 %37)
  %_88.0 = extractvalue { i32, i1 } %38, 0
  %_88.1 = extractvalue { i32, i1 } %38, 1
  br i1 %_88.1, label %panic7, label %bb41

panic6:                                           ; preds = %bb34
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_10c675ff07031e747ec9626450802dcd) #9
  unreachable

bb41:                                             ; preds = %bb35
  %39 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_88.0, i32 2)
  %_89.0 = extractvalue { i32, i1 } %39, 0
  %_89.1 = extractvalue { i32, i1 } %39, 1
  br i1 %_89.1, label %panic8, label %bb42

panic7:                                           ; preds = %bb35
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2848a08591699a7894f64c168bace3ff) #9
  unreachable

bb42:                                             ; preds = %bb41
  store i32 %_89.0, ptr %result1, align 4
  %40 = load i32, ptr %b, align 4
  %41 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %40, i32 5)
  %_92.0 = extractvalue { i32, i1 } %41, 0
  %_92.1 = extractvalue { i32, i1 } %41, 1
  br i1 %_92.1, label %panic9, label %bb43

panic8:                                           ; preds = %bb41
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_2848a08591699a7894f64c168bace3ff) #9
  unreachable

bb43:                                             ; preds = %bb42
  %42 = load i32, ptr %a, align 4
  %43 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %42, i32 %_92.0)
  %_93.0 = extractvalue { i32, i1 } %43, 0
  %_93.1 = extractvalue { i32, i1 } %43, 1
  br i1 %_93.1, label %panic10, label %bb44

panic9:                                           ; preds = %bb42
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_773b852e4f528c9e05aaa8be28862813) #9
  unreachable

bb44:                                             ; preds = %bb43
  store i32 %_93.0, ptr %result2, align 4
  %44 = load i32, ptr %a, align 4
  %45 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %44, i32 2)
  %_96.0 = extractvalue { i32, i1 } %45, 0
  %_96.1 = extractvalue { i32, i1 } %45, 1
  br i1 %_96.1, label %panic11, label %bb45

panic10:                                          ; preds = %bb43
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_3b07814057dae4f70d36d1844904a306) #9
  unreachable

bb45:                                             ; preds = %bb44
  %46 = load i32, ptr %b, align 4
  %47 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %46, i32 3)
  %_98.0 = extractvalue { i32, i1 } %47, 0
  %_98.1 = extractvalue { i32, i1 } %47, 1
  br i1 %_98.1, label %panic12, label %bb46

panic11:                                          ; preds = %bb44
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_65c11fcdbc53aea8434cf9a792e625bc) #9
  unreachable

bb46:                                             ; preds = %bb45
  %48 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_96.0, i32 %_98.0)
  %_99.0 = extractvalue { i32, i1 } %48, 0
  %_99.1 = extractvalue { i32, i1 } %48, 1
  br i1 %_99.1, label %panic13, label %bb47

panic12:                                          ; preds = %bb45
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_09c8d6fb85ede852552849111b99755c) #9
  unreachable

bb47:                                             ; preds = %bb46
  store i32 %_99.0, ptr %result3, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_101, ptr align 8 @alloc_4129cd9ab1076091e994e6706beda7d3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_101)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_108, ptr align 4 %result1)
  %49 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_107, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %49, ptr align 8 %_108, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_104, ptr align 8 @alloc_5b0bc919ce155623a6487d10c3543f30, ptr align 8 %_107)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_104)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_115, ptr align 4 %result2)
  %50 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_114, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %50, ptr align 8 %_115, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_111, ptr align 8 @alloc_9d5ebae3644c593c94668efc4df629cc, ptr align 8 %_114)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_111)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_122, ptr align 4 %result3)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_121, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_122, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_118, ptr align 8 @alloc_22be2209f2c12aa59dadf72d042a2e6a, ptr align 8 %_121)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_118)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_125, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_125)
  %52 = load i32, ptr %a, align 4
  %53 = load i32, ptr %b, align 4
  %54 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %52, i32 %53)
  %_130.0 = extractvalue { i32, i1 } %54, 0
  %_130.1 = extractvalue { i32, i1 } %54, 1
  br i1 %_130.1, label %panic14, label %bb61

panic13:                                          ; preds = %bb46
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_65c11fcdbc53aea8434cf9a792e625bc) #9
  unreachable

bb61:                                             ; preds = %bb47
  %55 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_130.0, i32 2)
  %_131.0 = extractvalue { i32, i1 } %55, 0
  %_131.1 = extractvalue { i32, i1 } %55, 1
  br i1 %_131.1, label %panic15, label %bb62

panic14:                                          ; preds = %bb47
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e58d664dd796c2abb0a1630abb3e8a0e) #9
  unreachable

bb62:                                             ; preds = %bb61
  %56 = load i32, ptr %b, align 4
  %_133 = icmp eq i32 %56, 0
  br i1 %_133, label %panic16, label %bb63

panic15:                                          ; preds = %bb61
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_d61e293a0001e4bf611b2ea3f42cfd2c) #9
  unreachable

bb63:                                             ; preds = %bb62
  %57 = load i32, ptr %b, align 4
  %_134 = icmp eq i32 %57, -1
  %58 = load i32, ptr %a, align 4
  %_135 = icmp eq i32 %58, -2147483648
  %_136 = and i1 %_134, %_135
  br i1 %_136, label %panic17, label %bb64

panic16:                                          ; preds = %bb62
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_ed5c22227202f6f8ec7d35c939585fb4) #9
  unreachable

bb64:                                             ; preds = %bb63
  %59 = load i32, ptr %a, align 4
  %60 = load i32, ptr %b, align 4
  %_132 = sdiv i32 %59, %60
  %61 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_131.0, i32 %_132)
  %_137.0 = extractvalue { i32, i1 } %61, 0
  %_137.1 = extractvalue { i32, i1 } %61, 1
  br i1 %_137.1, label %panic18, label %bb65

panic17:                                          ; preds = %bb63
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_ed5c22227202f6f8ec7d35c939585fb4) #9
  unreachable

bb65:                                             ; preds = %bb64
  store i32 %_137.0, ptr %complex1, align 4
  %62 = load i32, ptr %a, align 4
  %63 = load i32, ptr %b, align 4
  %64 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %62, i32 %63)
  %_140.0 = extractvalue { i32, i1 } %64, 0
  %_140.1 = extractvalue { i32, i1 } %64, 1
  br i1 %_140.1, label %panic19, label %bb66

panic18:                                          ; preds = %bb64
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_d61e293a0001e4bf611b2ea3f42cfd2c) #9
  unreachable

bb66:                                             ; preds = %bb65
  %65 = load i32, ptr %a, align 4
  %66 = load i32, ptr %b, align 4
  %67 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %65, i32 %66)
  %_143.0 = extractvalue { i32, i1 } %67, 0
  %_143.1 = extractvalue { i32, i1 } %67, 1
  br i1 %_143.1, label %panic20, label %bb67

panic19:                                          ; preds = %bb65
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_2a92babf017e0816b3892420c970bf67) #9
  unreachable

bb67:                                             ; preds = %bb66
  %68 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_143.0, i32 2)
  %_144.0 = extractvalue { i32, i1 } %68, 0
  %_144.1 = extractvalue { i32, i1 } %68, 1
  br i1 %_144.1, label %panic21, label %bb68

panic20:                                          ; preds = %bb66
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_8a8aca99d705eec1f62988618b34bc36) #9
  unreachable

bb68:                                             ; preds = %bb67
  %69 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_140.0, i32 %_144.0)
  %_145.0 = extractvalue { i32, i1 } %69, 0
  %_145.1 = extractvalue { i32, i1 } %69, 1
  br i1 %_145.1, label %panic22, label %bb69

panic21:                                          ; preds = %bb67
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6b70f61efb29f3bb96f9788ea6b49951) #9
  unreachable

bb69:                                             ; preds = %bb68
  store i32 %_145.0, ptr %complex2, align 4
  %70 = load i32, ptr %a, align 4
  %71 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %70, i32 5)
  %_149.0 = extractvalue { i32, i1 } %71, 0
  %_149.1 = extractvalue { i32, i1 } %71, 1
  br i1 %_149.1, label %panic23, label %bb70

panic22:                                          ; preds = %bb68
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2a92babf017e0816b3892420c970bf67) #9
  unreachable

bb70:                                             ; preds = %bb69
  %72 = load i32, ptr %b, align 4
  %73 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %72, i32 1)
  %_151.0 = extractvalue { i32, i1 } %73, 0
  %_151.1 = extractvalue { i32, i1 } %73, 1
  br i1 %_151.1, label %panic24, label %bb71

panic23:                                          ; preds = %bb69
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_127813faeadcb162fc67b4a534888edb) #9
  unreachable

bb71:                                             ; preds = %bb70
  %74 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_149.0, i32 %_151.0)
  %_152.0 = extractvalue { i32, i1 } %74, 0
  %_152.1 = extractvalue { i32, i1 } %74, 1
  br i1 %_152.1, label %panic25, label %bb72

panic24:                                          ; preds = %bb70
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_89aba8f9baf03f3f6fb22c73d3207df5) #9
  unreachable

bb72:                                             ; preds = %bb71
  %_155 = icmp eq i32 %_152.0, -2147483648
  %_156 = and i1 false, %_155
  br i1 %_156, label %panic26, label %bb74

panic25:                                          ; preds = %bb71
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_8dbafd60f99f37e7c2b90d0a951ade38) #9
  unreachable

bb74:                                             ; preds = %bb72
  %75 = sdiv i32 %_152.0, 2
  store i32 %75, ptr %complex3, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_158, ptr align 8 @alloc_b5e432210ac6a9954e8d42c563d2f3f8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_158)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_165, ptr align 4 %complex1)
  %76 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_164, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %76, ptr align 8 %_165, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_161, ptr align 8 @alloc_2a24c8367bbf27ec4c7520595247c58a, ptr align 8 %_164)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_161)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_172, ptr align 4 %complex2)
  %77 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_171, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %77, ptr align 8 %_172, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_168, ptr align 8 @alloc_0c010d16ed8437a9c54f278d20196ebb, ptr align 8 %_171)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_168)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_179, ptr align 4 %complex3)
  %78 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_178, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %78, ptr align 8 %_179, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_175, ptr align 8 @alloc_9affd498751a5b6dc3f0f41ca0598362, ptr align 8 %_178)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_175)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_182, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_182)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_187, ptr align 8 @alloc_60c82de1804578c9882be35b92238261)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_187)
  store float 0x402B666660000000, ptr %_196, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_194, ptr align 4 %_196)
  %79 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_193, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %79, ptr align 8 %_194, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_190, ptr align 8 @alloc_54112bfeaebbf72930828791f3277879, ptr align 8 %_193)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_190)
  store float 0x401D333340000000, ptr %_204, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_202, ptr align 4 %_204)
  %80 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_201, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %80, ptr align 8 %_202, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_198, ptr align 8 @alloc_47179c1800567d33b9799d799e75d801, ptr align 8 %_201)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_198)
  store float 0x4040CCCCE0000000, ptr %_212, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_210, ptr align 4 %_212)
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_209, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_210, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_206, ptr align 8 @alloc_2cf3b54281497c2cc9a72e5dba0eb7a8, ptr align 8 %_209)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_206)
  store float 3.281250e+00, ptr %_220, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_218, ptr align 4 %_220)
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_217, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_218, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_214, ptr align 8 @alloc_7c63f59770a35f3d355a97946474b6ea, ptr align 8 %_217)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_214)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_222, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_222)
  store i32 10, ptr %int_val, align 4
  store float 2.500000e+00, ptr %float_val, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_227, ptr align 8 @alloc_06129da6dfeff9c513c4e74ab49da3d4)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_227)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_234, ptr align 4 %int_val)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_236, ptr align 4 %float_val)
  %83 = load i32, ptr %int_val, align 4
  %_241 = sitofp i32 %83 to float
  %84 = load float, ptr %float_val, align 4
  %85 = fadd float %_241, %84
  store float %85, ptr %_240, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_238, ptr align 4 %_240)
  %86 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_233, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %86, ptr align 8 %_234, i64 16, i1 false)
  %87 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_233, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %87, ptr align 8 %_236, i64 16, i1 false)
  %88 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_233, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %88, ptr align 8 %_238, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_230, ptr align 8 @alloc_7fc420f7aa60691ab657ff678d587972, ptr align 8 %_233)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_230)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_247, ptr align 4 %int_val)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_249, ptr align 4 %float_val)
  %89 = load i32, ptr %int_val, align 4
  %_254 = sitofp i32 %89 to float
  %90 = load float, ptr %float_val, align 4
  %91 = fmul float %_254, %90
  store float %91, ptr %_253, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hfadf1711b11895a5E(ptr sret([16 x i8]) align 8 %_251, ptr align 4 %_253)
  %92 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_246, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %92, ptr align 8 %_247, i64 16, i1 false)
  %93 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_246, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %93, ptr align 8 %_249, i64 16, i1 false)
  %94 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_246, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %94, ptr align 8 %_251, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h95d3e1f61d4766beE(ptr sret([48 x i8]) align 8 %_243, ptr align 8 @alloc_6cd41dc0894bbaf5943188c89a0a71e4, ptr align 8 %_246)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_243)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_256, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_256)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_261, ptr align 8 @alloc_7e5275f6fdff86590c5b2ab586eedb82)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_261)
  %95 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 -5, i32 -3)
  %_271.0 = extractvalue { i32, i1 } %95, 0
  %_271.1 = extractvalue { i32, i1 } %95, 1
  br i1 %_271.1, label %panic27, label %bb120

panic26:                                          ; preds = %bb72
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_8dbafd60f99f37e7c2b90d0a951ade38) #9
  unreachable

bb120:                                            ; preds = %bb74
  store i32 %_271.0, ptr %_270, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_268, ptr align 4 %_270)
  %96 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_267, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %96, ptr align 8 %_268, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_264, ptr align 8 @alloc_f3ff3fc7c6a3137788a8a0956363cec1, ptr align 8 %_267)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_264)
  %97 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 -5, i32 -3)
  %_280.0 = extractvalue { i32, i1 } %97, 0
  %_280.1 = extractvalue { i32, i1 } %97, 1
  br i1 %_280.1, label %panic28, label %bb124

panic27:                                          ; preds = %bb74
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_45e51c78af411cdebb27a96236020820) #9
  unreachable

bb124:                                            ; preds = %bb120
  store i32 %_280.0, ptr %_279, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_277, ptr align 4 %_279)
  %98 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_276, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %98, ptr align 8 %_277, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_273, ptr align 8 @alloc_83d40ba59aa0a73fb9398e69c377428f, ptr align 8 %_276)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_273)
  %99 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 -5, i32 -3)
  %_289.0 = extractvalue { i32, i1 } %99, 0
  %_289.1 = extractvalue { i32, i1 } %99, 1
  br i1 %_289.1, label %panic29, label %bb128

panic28:                                          ; preds = %bb120
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_2ecf06e8d3e6fb07787954df2ce9118d) #9
  unreachable

bb128:                                            ; preds = %bb124
  store i32 %_289.0, ptr %_288, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_286, ptr align 4 %_288)
  %100 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_285, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %100, ptr align 8 %_286, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_282, ptr align 8 @alloc_c0921044fe0c398035de1c238a558292, ptr align 8 %_285)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_282)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_291, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_291)
  store i32 0, ptr %counter, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_295, ptr align 8 @alloc_fb212613a2a46962f28ab6c3278093c7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_295)
  %101 = load i32, ptr %counter, align 4
  %102 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %101, i32 1)
  %_297.0 = extractvalue { i32, i1 } %102, 0
  %_297.1 = extractvalue { i32, i1 } %102, 1
  br i1 %_297.1, label %panic30, label %bb136

panic29:                                          ; preds = %bb124
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_c6af346309caf6c9c4bbe32841acaa8e) #9
  unreachable

bb136:                                            ; preds = %bb128
  store i32 %_297.0, ptr %counter, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_303, ptr align 4 %counter)
  %103 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_302, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %103, ptr align 8 %_303, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_299, ptr align 8 @alloc_e7e975d66460c09f9e59c44583817025, ptr align 8 %_302)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_299)
  %104 = load i32, ptr %counter, align 4
  %105 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %104, i32 2)
  %_305.0 = extractvalue { i32, i1 } %105, 0
  %_305.1 = extractvalue { i32, i1 } %105, 1
  br i1 %_305.1, label %panic31, label %bb140

panic30:                                          ; preds = %bb128
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_8c2ae598e8ef7fac4c875acab19cdfae) #9
  unreachable

bb140:                                            ; preds = %bb136
  store i32 %_305.0, ptr %counter, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_311, ptr align 4 %counter)
  %106 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_310, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %106, ptr align 8 %_311, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_307, ptr align 8 @alloc_f35c991d4906723d647d8e191f07eaf8, ptr align 8 %_310)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_307)
  %107 = load i32, ptr %counter, align 4
  %108 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %107, i32 5)
  %_313.0 = extractvalue { i32, i1 } %108, 0
  %_313.1 = extractvalue { i32, i1 } %108, 1
  br i1 %_313.1, label %panic32, label %bb144

panic31:                                          ; preds = %bb136
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_9653a19b250d463ac66a3451736dd1cc) #9
  unreachable

bb144:                                            ; preds = %bb140
  store i32 %_313.0, ptr %counter, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_319, ptr align 4 %counter)
  %109 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_318, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %109, ptr align 8 %_319, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_315, ptr align 8 @alloc_4a8f3fd87573436947a4c9e370e82972, ptr align 8 %_318)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_315)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_322, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_322)
  %110 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 1, i32 2)
  %_328.0 = extractvalue { i32, i1 } %110, 0
  %_328.1 = extractvalue { i32, i1 } %110, 1
  br i1 %_328.1, label %panic33, label %bb150

panic32:                                          ; preds = %bb140
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_dcad562e1110bed89974c5c889070375) #9
  unreachable

bb150:                                            ; preds = %bb144
  %111 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_328.0, i32 3)
  %_329.0 = extractvalue { i32, i1 } %111, 0
  %_329.1 = extractvalue { i32, i1 } %111, 1
  br i1 %_329.1, label %panic34, label %bb151

panic33:                                          ; preds = %bb144
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e0b74e583a94b7d95c003b657b3b7c99) #9
  unreachable

bb151:                                            ; preds = %bb150
  %112 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_329.0, i32 4)
  %_330.0 = extractvalue { i32, i1 } %112, 0
  %_330.1 = extractvalue { i32, i1 } %112, 1
  br i1 %_330.1, label %panic35, label %bb152

panic34:                                          ; preds = %bb150
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e0b74e583a94b7d95c003b657b3b7c99) #9
  unreachable

bb152:                                            ; preds = %bb151
  %113 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_330.0, i32 5)
  %_331.0 = extractvalue { i32, i1 } %113, 0
  %_331.1 = extractvalue { i32, i1 } %113, 1
  br i1 %_331.1, label %panic36, label %bb153

panic35:                                          ; preds = %bb151
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e0b74e583a94b7d95c003b657b3b7c99) #9
  unreachable

bb153:                                            ; preds = %bb152
  store i32 %_331.0, ptr %chain_result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_337, ptr align 4 %chain_result)
  %114 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_336, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %114, ptr align 8 %_337, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_333, ptr align 8 @alloc_8f4249dc54fdd48cd209f29ba8b1083b, ptr align 8 %_336)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_333)
; call core::num::<impl i32>::pow
  %115 = call i32 @"_ZN4core3num21_$LT$impl$u20$i32$GT$3pow17h8d97c2fdb90ab43bE"(i32 2, i32 2)
  store i32 %115, ptr %power2, align 4
; call core::num::<impl i32>::pow
  %116 = call i32 @"_ZN4core3num21_$LT$impl$u20$i32$GT$3pow17h8d97c2fdb90ab43bE"(i32 2, i32 3)
  store i32 %116, ptr %power3, align 4
; call core::num::<impl i32>::pow
  %117 = call i32 @"_ZN4core3num21_$LT$impl$u20$i32$GT$3pow17h8d97c2fdb90ab43bE"(i32 2, i32 4)
  store i32 %117, ptr %power4, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_344, ptr align 8 @alloc_0d1ff6ed40b992bf5d078e145b08b988)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_344)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_351, ptr align 4 %power2)
  %118 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_350, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %118, ptr align 8 %_351, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_347, ptr align 8 @alloc_5c38de46dddfb09a13c6ea478ef46644, ptr align 8 %_350)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_347)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_358, ptr align 4 %power3)
  %119 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_357, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %119, ptr align 8 %_358, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_354, ptr align 8 @alloc_67adefb42d8f3738b163ca17692b3d72, ptr align 8 %_357)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_354)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h440f9b7dd96e92e6E(ptr sret([16 x i8]) align 8 %_365, ptr align 4 %power4)
  %120 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_364, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %120, ptr align 8 %_365, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h4253ad616b5c89a8E(ptr sret([48 x i8]) align 8 %_361, ptr align 8 @alloc_b9dd465725ee0d65418377188b230fac, ptr align 8 %_364)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_361)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_368, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_368)
  %121 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 15, i32 2)
  %_373.0 = extractvalue { i32, i1 } %121, 0
  %_373.1 = extractvalue { i32, i1 } %121, 1
  br i1 %_373.1, label %panic37, label %bb173

panic36:                                          ; preds = %bb152
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e0b74e583a94b7d95c003b657b3b7c99) #9
  unreachable

bb173:                                            ; preds = %bb153
  %_371 = icmp sgt i32 %_373.0, 25
  br i1 %_371, label %bb174, label %bb176

panic37:                                          ; preds = %bb153
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_af6e3f5c2166e7777fc028c6904f92a9) #9
  unreachable

bb176:                                            ; preds = %bb174, %bb173
  %122 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 15, i32 5)
  %_378.0 = extractvalue { i32, i1 } %122, 0
  %_378.1 = extractvalue { i32, i1 } %122, 1
  br i1 %_378.1, label %panic38, label %bb177

bb174:                                            ; preds = %bb173
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_375, ptr align 8 @alloc_dd5c0bf3e6e8c8a39dcc9d2db50c8e07)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_375)
  br label %bb176

bb177:                                            ; preds = %bb176
  %123 = icmp eq i32 %_378.0, 20
  br i1 %123, label %bb178, label %bb180

panic38:                                          ; preds = %bb176
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_0df34a43145809935b840fa271228286) #9
  unreachable

bb178:                                            ; preds = %bb177
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_380, ptr align 8 @alloc_cf8938a90160e9dc07cbefa234cede93)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_380)
  br label %bb180

bb180:                                            ; preds = %bb178, %bb177
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_383, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_383)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h4c5fdd9393a97e90E(ptr sret([48 x i8]) align 8 %_386, ptr align 8 @alloc_5d3e0476b3d05b5b75fe3500eb55214a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_386)
  ret void
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #4

; core::fmt::float::<impl core::fmt::Display for f32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f32$GT$3fmt17h3f1d24a0f47d0dccE"(ptr align 4, ptr align 8) unnamed_addr #0

; Function Attrs: convergent nocallback nofree nosync nounwind willreturn memory(none)
declare i1 @llvm.is.constant.i32(i32) #5

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #6

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #7

declare i32 @__CxxFrameHandler3(...) unnamed_addr #8

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #6

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #6

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #7

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #7

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #7

; core::panicking::panic_const::panic_const_rem_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8) unnamed_addr #7

; core::panicking::panic_const::panic_const_rem_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8) unnamed_addr #7

define i32 @main(i32 %0, ptr %1) unnamed_addr #8 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h2d957aede228f0b3E(ptr @_ZN27_02_mathematical_operations4main17h8eb9315c7e762f73E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #5 = { convergent nocallback nofree nosync nounwind willreturn memory(none) }
attributes #6 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #7 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { "target-cpu"="x86-64" }
attributes #9 = { noreturn }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 10204773578195213}
