# Comprehensive Arithmetic Operations Test
# Testing all possible mathematical expressions users might write

print "=== COMPREHENSIVE ARITHMETIC OPERATIONS TEST ==="
print ""

# ========================================
# BASIC VARIABLES FOR TESTING
# ========================================
int a = 10
int b = 3
int c = 5
int d = 2
float x = 7.5
float y = 2.5
float z = 1.2
int negative = -4
int zero = 0
int large = 100

print "=== TEST VARIABLES ==="
print "a=" + a + ", b=" + b + ", c=" + c + ", d=" + d
print "x=" + x + ", y=" + y + ", z=" + z
print "negative=" + negative + ", zero=" + zero + ", large=" + large
print ""

# ========================================
# SIMPLE BINARY OPERATIONS
# ========================================
print "=== SIMPLE BINARY OPERATIONS ==="

# Addition variations
int add1 = a + b
int add2 = a + 5
int add3 = 15 + b
int add4 = 7 + 8
float add5 = x + y
float add6 = x + 3.0
int add7 = negative + a
int add8 = zero + b

print "Addition Tests:"
print "a + b = " + add1 + " (10 + 3 = 13)"
print "a + 5 = " + add2 + " (10 + 5 = 15)"
print "15 + b = " + add3 + " (15 + 3 = 18)"
print "7 + 8 = " + add4 + " (7 + 8 = 15)"
print "x + y = " + add5 + " (7.5 + 2.5 = 10.0)"
print "x + 3.0 = " + add6 + " (7.5 + 3.0 = 10.5)"
print "negative + a = " + add7 + " (-4 + 10 = 6)"
print "zero + b = " + add8 + " (0 + 3 = 3)"
print ""

# Subtraction variations
int sub1 = a - b
int sub2 = a - 7
int sub3 = 20 - b
int sub4 = 15 - 8
float sub5 = x - y
int sub6 = a - negative
int sub7 = zero - b

print "Subtraction Tests:"
print "a - b = " + sub1 + " (10 - 3 = 7)"
print "a - 7 = " + sub2 + " (10 - 7 = 3)"
print "20 - b = " + sub3 + " (20 - 3 = 17)"
print "15 - 8 = " + sub4 + " (15 - 8 = 7)"
print "x - y = " + sub5 + " (7.5 - 2.5 = 5.0)"
print "a - negative = " + sub6 + " (10 - (-4) = 14)"
print "zero - b = " + sub7 + " (0 - 3 = -3)"
print ""

# Multiplication variations
int mul1 = a * b
int mul2 = a * 4
int mul3 = 6 * b
int mul4 = 7 * 8
float mul5 = x * y
int mul6 = negative * b
int mul7 = zero * a

print "Multiplication Tests:"
print "a * b = " + mul1 + " (10 * 3 = 30)"
print "a * 4 = " + mul2 + " (10 * 4 = 40)"
print "6 * b = " + mul3 + " (6 * 3 = 18)"
print "7 * 8 = " + mul4 + " (7 * 8 = 56)"
print "x * y = " + mul5 + " (7.5 * 2.5 = 18.75)"
print "negative * b = " + mul6 + " (-4 * 3 = -12)"
print "zero * a = " + mul7 + " (0 * 10 = 0)"
print ""

# Division variations
int div1 = a / b
int div2 = a / 2
int div3 = 20 / b
int div4 = 15 / 3
float div5 = x / y
int div6 = negative / d
int div7 = large / a

print "Division Tests:"
print "a / b = " + div1 + " (10 / 3 = 3)"
print "a / 2 = " + div2 + " (10 / 2 = 5)"
print "20 / b = " + div3 + " (20 / 3 = 6)"
print "15 / 3 = " + div4 + " (15 / 3 = 5)"
print "x / y = " + div5 + " (7.5 / 2.5 = 3.0)"
print "negative / d = " + div6 + " (-4 / 2 = -2)"
print "large / a = " + div7 + " (100 / 10 = 10)"
print ""

# Modulo variations
int mod1 = a % b
int mod2 = a % 4
int mod3 = 17 % b
int mod4 = 15 % 4
int mod5 = large % a
int mod6 = 13 % 5

print "Modulo Tests:"
print "a % b = " + mod1 + " (10 % 3 = 1)"
print "a % 4 = " + mod2 + " (10 % 4 = 2)"
print "17 % b = " + mod3 + " (17 % 3 = 2)"
print "15 % 4 = " + mod4 + " (15 % 4 = 3)"
print "large % a = " + mod5 + " (100 % 10 = 0)"
print "13 % 5 = " + mod6 + " (13 % 5 = 3)"
print ""

# ========================================
# PARENTHESES OPERATIONS
# ========================================
print "=== PARENTHESES OPERATIONS ==="

int paren1 = (a + b) * c
int paren2 = a * (b + c)
int paren3 = (a - b) + (c * d)
int paren4 = (a + b) - (c - d)
int paren5 = (a * b) / (c - d)
int paren6 = (a + b + c) * d
float paren7 = (x + y) * z
int paren8 = (negative + a) * b

print "Parentheses Tests:"
print "(a + b) * c = " + paren1 + " ((10 + 3) * 5 = 65)"
print "a * (b + c) = " + paren2 + " (10 * (3 + 5) = 80)"
print "(a - b) + (c * d) = " + paren3 + " ((10 - 3) + (5 * 2) = 17)"
print "(a + b) - (c - d) = " + paren4 + " ((10 + 3) - (5 - 2) = 10)"
print "(a * b) / (c - d) = " + paren5 + " ((10 * 3) / (5 - 2) = 10)"
print "(a + b + c) * d = " + paren6 + " ((10 + 3 + 5) * 2 = 36)"
print "(x + y) * z = " + paren7 + " ((7.5 + 2.5) * 1.2 = 12.0)"
print "(negative + a) * b = " + paren8 + " ((-4 + 10) * 3 = 18)"
print ""

# ========================================
# NESTED PARENTHESES
# ========================================
print "=== NESTED PARENTHESES ==="

int nested1 = ((a + b) * c) - d
int nested2 = a + ((b * c) - d)
int nested3 = (a * (b + c)) / d
int nested4 = ((a - b) + c) * (d + 1)
int nested5 = ((a + b) * (c - d)) + 1
int nested6 = (a + (b * (c + d))) - 5
float nested7 = ((x + y) / z) * 2.0
int nested8 = ((negative * b) + a) / d

print "Nested Parentheses Tests:"
print "((a + b) * c) - d = " + nested1 + " (((10 + 3) * 5) - 2 = 63)"
print "a + ((b * c) - d) = " + nested2 + " (10 + ((3 * 5) - 2) = 23)"
print "(a * (b + c)) / d = " + nested3 + " ((10 * (3 + 5)) / 2 = 40)"
print "((a - b) + c) * (d + 1) = " + nested4 + " (((10 - 3) + 5) * (2 + 1) = 36)"
print "((a + b) * (c - d)) + 1 = " + nested5 + " (((10 + 3) * (5 - 2)) + 1 = 40)"
print "(a + (b * (c + d))) - 5 = " + nested6 + " ((10 + (3 * (5 + 2))) - 5 = 26)"
print "((x + y) / z) * 2.0 = " + nested7 + " (((7.5 + 2.5) / 1.2) * 2.0 = 16.667)"
print "(negative * b + a) / d = " + nested8 + " (((-4 * 3) + 10) / 2 = -1)"
print ""

# ========================================
# CHAIN OPERATIONS
# ========================================
print "=== CHAIN OPERATIONS ==="

int chain1 = a + b + c + d
int chain2 = a - b - c - d
int chain3 = a * b * c * d
int chain4 = 1 + 2 + 3 + 4 + 5 + 6
int chain5 = 2 * 3 * 4 * 5
int chain6 = 100 - 10 - 5 - 3 - 2
float chain7 = x + y + z + 1.0
int chain8 = a + b - c + d - 1

print "Chain Operations Tests:"
print "a + b + c + d = " + chain1 + " (10 + 3 + 5 + 2 = 20)"
print "a - b - c - d = " + chain2 + " (10 - 3 - 5 - 2 = 0)"
print "a * b * c * d = " + chain3 + " (10 * 3 * 5 * 2 = 300)"
print "1 + 2 + 3 + 4 + 5 + 6 = " + chain4 + " (1+2+3+4+5+6 = 21)"
print "2 * 3 * 4 * 5 = " + chain5 + " (2*3*4*5 = 120)"
print "100 - 10 - 5 - 3 - 2 = " + chain6 + " (100-10-5-3-2 = 80)"
print "x + y + z + 1.0 = " + chain7 + " (7.5+2.5+1.2+1.0 = 12.2)"
print "a + b - c + d - 1 = " + chain8 + " (10+3-5+2-1 = 9)"
print ""

# ========================================
# OPERATOR PRECEDENCE
# ========================================
print "=== OPERATOR PRECEDENCE ==="

int prec1 = a + b * c
int prec2 = a * b + c
int prec3 = a + b * c - d
int prec4 = a - b * c + d
int prec5 = a * b + c * d
int prec6 = a * b - c * d
int prec7 = a + b / c * d
int prec8 = a * b / c + d
float prec9 = x + y * z
int prec10 = a % b + c * d

print "Operator Precedence Tests:"
print "a + b * c = " + prec1 + " (10 + (3 * 5) = 25)"
print "a * b + c = " + prec2 + " ((10 * 3) + 5 = 35)"
print "a + b * c - d = " + prec3 + " (10 + (3 * 5) - 2 = 23)"
print "a - b * c + d = " + prec4 + " (10 - (3 * 5) + 2 = -3)"
print "a * b + c * d = " + prec5 + " ((10 * 3) + (5 * 2) = 40)"
print "a * b - c * d = " + prec6 + " ((10 * 3) - (5 * 2) = 20)"
print "a + b / c * d = " + prec7 + " (10 + ((3 / 5) * 2) = 10)"
print "a * b / c + d = " + prec8 + " (((10 * 3) / 5) + 2 = 8)"
print "x + y * z = " + prec9 + " (7.5 + (2.5 * 1.2) = 10.5)"
print "a % b + c * d = " + prec10 + " ((10 % 3) + (5 * 2) = 11)"
print ""

# ========================================
# MIXED PRECEDENCE WITH PARENTHESES
# ========================================
print "=== MIXED PRECEDENCE WITH PARENTHESES ==="

int mixed1 = (a + b) * c - d
int mixed2 = a + (b * c) - d
int mixed3 = (a + b) * (c - d)
int mixed4 = a * (b + c) - (d * 2)
int mixed5 = (a - b) * c + (d * 3)
int mixed6 = a + b * (c - d) + 1
int mixed7 = (a * b) + c - (d / 2)
int mixed8 = a - (b + c) * d + 5

print "Mixed Precedence Tests:"
print "(a + b) * c - d = " + mixed1 + " ((10 + 3) * 5 - 2 = 63)"
print "a + (b * c) - d = " + mixed2 + " (10 + (3 * 5) - 2 = 23)"
print "(a + b) * (c - d) = " + mixed3 + " ((10 + 3) * (5 - 2) = 39)"
print "a * (b + c) - (d * 2) = " + mixed4 + " (10 * (3 + 5) - (2 * 2) = 76)"
print "(a - b) * c + (d * 3) = " + mixed5 + " ((10 - 3) * 5 + (2 * 3) = 41)"
print "a + b * (c - d) + 1 = " + mixed6 + " (10 + 3 * (5 - 2) + 1 = 20)"
print "(a * b) + c - (d / 2) = " + mixed7 + " ((10 * 3) + 5 - (2 / 2) = 34)"
print "a - (b + c) * d + 5 = " + mixed8 + " (10 - (3 + 5) * 2 + 5 = -1)"
print ""

# ========================================
# EDGE CASES AND SPECIAL SCENARIOS
# ========================================
print "=== EDGE CASES AND SPECIAL SCENARIOS ==="

# Zero operations
int zero1 = zero + a
int zero2 = zero * a
int zero3 = a + zero
int zero4 = a * zero
int zero5 = zero - a
int zero6 = a - zero

print "Zero Operations:"
print "zero + a = " + zero1 + " (0 + 10 = 10)"
print "zero * a = " + zero2 + " (0 * 10 = 0)"
print "a + zero = " + zero3 + " (10 + 0 = 10)"
print "a * zero = " + zero4 + " (10 * 0 = 0)"
print "zero - a = " + zero5 + " (0 - 10 = -10)"
print "a - zero = " + zero6 + " (10 - 0 = 10)"
print ""

# Negative number operations
int neg1 = negative + negative
int neg2 = negative * negative
int neg3 = negative - negative
int neg4 = a + negative
int neg5 = a * negative
int neg6 = negative / d

print "Negative Number Operations:"
print "negative + negative = " + neg1 + " (-4 + (-4) = -8)"
print "negative * negative = " + neg2 + " (-4 * (-4) = 16)"
print "negative - negative = " + neg3 + " (-4 - (-4) = 0)"
print "a + negative = " + neg4 + " (10 + (-4) = 6)"
print "a * negative = " + neg5 + " (10 * (-4) = -40)"
print "negative / d = " + neg6 + " (-4 / 2 = -2)"
print ""

# Large number operations
int large1 = large + large
int large2 = large * a
int large3 = large / a
int large4 = large % a
int large5 = large - a

print "Large Number Operations:"
print "large + large = " + large1 + " (100 + 100 = 200)"
print "large * a = " + large2 + " (100 * 10 = 1000)"
print "large / a = " + large3 + " (100 / 10 = 10)"
print "large % a = " + large4 + " (100 % 10 = 0)"
print "large - a = " + large5 + " (100 - 10 = 90)"
print ""

# ========================================
# COMPLEX REAL-WORLD SCENARIOS
# ========================================
print "=== COMPLEX REAL-WORLD SCENARIOS ==="

# Mathematical formulas
int area_rectangle = a * b
int perimeter_rectangle = (a + b) * 2
int area_triangle = (a * b) / 2
int pythagorean = (a * a) + (b * b)
int compound_interest = large * (1 + (c / 100))
int average = (a + b + c + d) / 4

print "Mathematical Formulas:"
print "Rectangle area (a * b) = " + area_rectangle + " (10 * 3 = 30)"
print "Rectangle perimeter ((a + b) * 2) = " + perimeter_rectangle + " ((10 + 3) * 2 = 26)"
print "Triangle area ((a * b) / 2) = " + area_triangle + " ((10 * 3) / 2 = 15)"
print "Pythagorean ((a * a) + (b * b)) = " + pythagorean + " ((10 * 10) + (3 * 3) = 109)"
print "Simple interest (large * (1 + (c / 100))) = " + compound_interest + " (100 * (1 + (5 / 100)) = 105)"
print "Average ((a + b + c + d) / 4) = " + average + " ((10 + 3 + 5 + 2) / 4 = 5)"
print ""

# Nested calculations
int calc1 = ((a + b) * c) + ((d * 2) - 1)
int calc2 = (a * (b + c)) - ((d + 1) * 2)
int calc3 = ((a - b) + (c * d)) * ((a / b) + 1)
int calc4 = (((a + b) * c) / d) + ((a * b) % c)
float calc5 = ((x + y) * z) - ((x - y) / 2.0)

print "Complex Nested Calculations:"
print "((a + b) * c) + ((d * 2) - 1) = " + calc1 + " (((10 + 3) * 5) + ((2 * 2) - 1) = 68)"
print "(a * (b + c)) - ((d + 1) * 2) = " + calc2 + " ((10 * (3 + 5)) - ((2 + 1) * 2) = 74)"
print "((a - b) + (c * d)) * ((a / b) + 1) = " + calc3 + " (((10 - 3) + (5 * 2)) * ((10 / 3) + 1) = 68)"
print "(((a + b) * c) / d) + ((a * b) % c) = " + calc4 + " ((((10 + 3) * 5) / 2) + ((10 * 3) % 5) = 32)"
print "((x + y) * z) - ((x - y) / 2.0) = " + calc5 + " (((7.5 + 2.5) * 1.2) - ((7.5 - 2.5) / 2.0) = 9.5)"
print ""

# ========================================
# EXTREME NESTING TESTS
# ========================================
print "=== EXTREME NESTING TESTS ==="

int extreme1 = (((a + b) * c) - d) + 1
int extreme2 = a + (((b * c) - d) * 2)
int extreme3 = ((a + (b * c)) - (d + 1)) * 2
int extreme4 = (a * ((b + c) - d)) + ((a - b) * c)
int extreme5 = ((a + b) * (c + d)) - ((a - b) * (c - d))

print "Extreme Nesting Tests:"
print "(((a + b) * c) - d) + 1 = " + extreme1 + " ((((10 + 3) * 5) - 2) + 1 = 64)"
print "a + (((b * c) - d) * 2) = " + extreme2 + " (10 + (((3 * 5) - 2) * 2) = 36)"
print "((a + (b * c)) - (d + 1)) * 2 = " + extreme3 + " (((10 + (3 * 5)) - (2 + 1)) * 2 = 44)"
print "(a * ((b + c) - d)) + ((a - b) * c) = " + extreme4 + " ((10 * ((3 + 5) - 2)) + ((10 - 3) * 5) = 95)"
print "((a + b) * (c + d)) - ((a - b) * (c - d)) = " + extreme5 + " (((10 + 3) * (5 + 2)) - ((10 - 3) * (5 - 2)) = 70)"
print ""

# ========================================
# PRINT EXPRESSION CALCULATIONS
# ========================================
print "=== PRINT EXPRESSION CALCULATIONS ==="

print "Direct calculations in print:"
print "Simple: (a + b) = " + (a + b)
print "Complex: ((a + b) * c) = " + ((a + b) * c)
print "Nested: (((a + b) * c) - d) = " + (((a + b) * c) - d)
print "Mixed: (a * b) + (c - d) = " + ((a * b) + (c - d))
print "Float: (x + y) * z = " + ((x + y) * z)
print "Chain: a + b + c + d = " + (a + b + c + d)
print "Precedence: a + b * c = " + (a + b * c)
print "Parentheses override: (a + b) * c = " + ((a + b) * c)
print ""

# ========================================
# ASSIGNMENT OPERATIONS
# ========================================
print "=== ASSIGNMENT OPERATIONS ==="

int counter = 0
print "Initial counter = " + counter

set counter = counter + 1
print "After +1: " + counter

set counter = counter * 3
print "After *3: " + counter

set counter = counter - 2
print "After -2: " + counter

set counter = counter / 2
print "After /2: " + counter

set counter = (counter + 5) * 2
print "After (counter + 5) * 2: " + counter

set counter = ((counter - 3) * 2) + 1
print "After ((counter - 3) * 2) + 1: " + counter
print ""

print "=== END OF COMPREHENSIVE ARITHMETIC TEST ==="
print "All arithmetic operations tested successfully!"
