# Simple test to check remaining issues

print "=== SIMPLE TEST ==="

# Test 1: String concatenation
string part1 = "Hello"
string part2 = "World"
string combined = part1 + part2
print "Combined: " + combined

# Test 2: Newlines and tabs
string with_newline = "Line1\nLine2"
string with_tab = "Col1\tCol2"
print "Newline test: " + with_newline
print "Tab test: " + with_tab

# Test 3: Mixed concatenation
int number = 42
string text = "Answer"
print "Mixed: " + text + " is " + number

print "=== END TEST ==="
