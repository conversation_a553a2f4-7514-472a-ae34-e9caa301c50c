# 27. Memory Management - Memory Allocation and Optimization
# This file demonstrates memory management concepts in Dolet

print "=== Memory Management Demo ==="
print ""

# Stack vs Heap simulation
print "Stack vs Heap Memory:"

fun demonstrate_stack_memory() {
    print "Stack Memory (Local Variables):"
    
    # Local variables are allocated on the stack
    int local_int = 42
    string local_string = "Stack Variable"
    array int local_array = [1, 2, 3, 4, 5]
    
    print "  local_int = " + local_int + " (on stack)"
    print "  local_string = '" + local_string + "' (on stack)"
    print "  local_array size = 5 (on stack)"
    
    print "  Stack variables are automatically freed when function ends"
}

fun demonstrate_heap_memory() {
    print "Heap Memory (Dynamic Allocation):"
    
    # Simulated heap allocation
    print "  Allocating 1024 bytes on heap..."
    int heap_handle = 12345  # Simulated memory handle
    
    print "  Memory handle: " + heap_handle
    print "  Heap memory must be manually freed"
    
    # Simulated memory deallocation
    print "  Freeing heap memory..."
    set heap_handle = 0
    print "  Memory freed successfully"
}

demonstrate_stack_memory()
demonstrate_heap_memory()
print ""

# Memory allocation patterns
print "Memory Allocation Patterns:"

fun allocate_buffer(size) {
    print "Allocating buffer of " + size + " bytes"
    
    if size <= 0
        print "  ERROR: Invalid size"
        return 0
    elif size > 1000000
        print "  ERROR: Size too large"
        return 0
    else
        print "  SUCCESS: Buffer allocated"
        return size  # Return handle
}

fun deallocate_buffer(handle) {
    print "Deallocating buffer with handle " + handle
    
    if handle == 0
        print "  ERROR: Invalid handle"
        return false
    else
        print "  SUCCESS: Buffer deallocated"
        return true
}

int buffer1 = allocate_buffer(1024)
int buffer2 = allocate_buffer(2048)
bool freed1 = deallocate_buffer(buffer1)
bool freed2 = deallocate_buffer(buffer2)
print ""

# Memory pool simulation
print "Memory Pool Simulation:"

fun create_memory_pool(pool_size, block_size) {
    print "Creating memory pool:"
    print "  Pool size: " + pool_size + " bytes"
    print "  Block size: " + block_size + " bytes"
    
    int total_blocks = pool_size / block_size
    print "  Total blocks: " + total_blocks
    
    return total_blocks
}

fun allocate_from_pool(pool_handle, blocks_needed) {
    print "Allocating " + blocks_needed + " blocks from pool " + pool_handle
    
    if blocks_needed <= 0
        print "  ERROR: Invalid block count"
        return 0
    elif blocks_needed > pool_handle
        print "  ERROR: Not enough blocks available"
        return 0
    else
        print "  SUCCESS: Blocks allocated"
        return blocks_needed
}

int memory_pool = create_memory_pool(4096, 64)
int allocation1 = allocate_from_pool(memory_pool, 5)
int allocation2 = allocate_from_pool(memory_pool, 100)  # Should fail
print ""

# Garbage collection simulation
print "Garbage Collection Simulation:"

int allocated_objects = 0
int gc_threshold = 10

fun allocate_object(object_type) {
    print "Allocating " + object_type + " object"
    set allocated_objects = allocated_objects + 1
    
    print "  Objects in memory: " + allocated_objects
    
    if allocated_objects >= gc_threshold
        print "  GC threshold reached - triggering garbage collection"
        trigger_garbage_collection()
    
    return allocated_objects
}

fun trigger_garbage_collection() {
    print "  Running garbage collector..."
    
    # Simulate marking and sweeping
    int objects_before = allocated_objects
    int collected = allocated_objects / 2  # Simulate collecting half
    set allocated_objects = allocated_objects - collected
    
    print "    Objects before GC: " + objects_before
    print "    Objects collected: " + collected
    print "    Objects remaining: " + allocated_objects
    print "  Garbage collection completed"
}

int obj1 = allocate_object("String")
int obj2 = allocate_object("Array")
int obj3 = allocate_object("Object")
int obj4 = allocate_object("Buffer")
int obj5 = allocate_object("List")
int obj6 = allocate_object("Map")
int obj7 = allocate_object("Set")
int obj8 = allocate_object("Tree")
int obj9 = allocate_object("Graph")
int obj10 = allocate_object("Queue")
int obj11 = allocate_object("Stack")  # Should trigger GC
print ""

# Memory leak detection
print "Memory Leak Detection:"

array int allocated_handles = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
int handle_count = 0

fun track_allocation(size) {
    print "Tracking allocation of " + size + " bytes"
    
    if handle_count >= 10
        print "  ERROR: Too many allocations tracked"
        return 0
    
    int handle = 1000 + handle_count
    set allocated_handles[handle_count] = handle
    set handle_count = handle_count + 1
    
    print "  Assigned handle: " + handle
    print "  Total tracked allocations: " + handle_count
    
    return handle
}

fun track_deallocation(handle) {
    print "Tracking deallocation of handle " + handle
    
    bool found = false
    for i = 0 to handle_count - 1
        if allocated_handles[i] == handle
            set allocated_handles[i] = 0
            set found = true
            print "  Handle " + handle + " removed from tracking"
    
    if not found
        print "  WARNING: Handle " + handle + " not found in tracking"
}

fun check_for_leaks() {
    print "Checking for memory leaks:"
    int leak_count = 0
    
    for i = 0 to handle_count - 1
        if allocated_handles[i] != 0
            print "  LEAK: Handle " + allocated_handles[i] + " not deallocated"
            set leak_count = leak_count + 1
    
    if leak_count == 0
        print "  No memory leaks detected"
    else
        print "  " + leak_count + " memory leaks detected"
}

int handle_a = track_allocation(512)
int handle_b = track_allocation(1024)
int handle_c = track_allocation(256)

track_deallocation(handle_a)
# handle_b and handle_c not deallocated - will be detected as leaks

check_for_leaks()
print ""

# Memory optimization techniques
print "Memory Optimization Techniques:"

fun optimize_string_storage(strings, count) {
    print "Optimizing storage for " + count + " strings"
    
    # String interning simulation
    array string unique_strings = ["", "", "", "", ""]
    int unique_count = 0
    
    print "  Before optimization: " + count + " string objects"
    
    # Simulate finding unique strings
    for i = 0 to count - 1
        bool is_unique = true
        
        # Check if string already exists (simplified)
        for j = 0 to unique_count - 1
            if strings[i] == unique_strings[j]
                set is_unique = false
        
        if is_unique and unique_count < 5
            set unique_strings[unique_count] = strings[i]
            set unique_count = unique_count + 1
    
    print "  After optimization: " + unique_count + " unique string objects"
    print "  Memory saved: " + (count - unique_count) + " string objects"
}

array string test_strings = ["hello", "world", "hello", "test", "world"]
optimize_string_storage(test_strings, 5)
print ""

# Memory profiling simulation
print "Memory Profiling:"

fun profile_memory_usage() {
    print "Memory profiling report:"
    
    # Simulated memory statistics
    int total_memory = 1048576  # 1MB
    int used_memory = 524288    # 512KB
    int free_memory = total_memory - used_memory
    
    print "  Total memory: " + total_memory + " bytes"
    print "  Used memory: " + used_memory + " bytes"
    print "  Free memory: " + free_memory + " bytes"
    print "  Memory usage: " + (used_memory * 100 / total_memory) + "%"
    
    # Memory breakdown
    print "  Memory breakdown:"
    print "    Stack: 65536 bytes (6%)"
    print "    Heap: 458752 bytes (44%)"
    print "    Code: 32768 bytes (3%)"
    print "    Data: 16384 bytes (2%)"
}

profile_memory_usage()
print ""

# Memory-efficient algorithms
print "Memory-Efficient Algorithms:"

fun in_place_array_reverse(arr, size) {
    print "Reversing array in-place (memory-efficient):"
    print "  Original memory usage: " + size + " elements"
    
    for i = 0 to (size / 2) - 1
        int opposite = size - 1 - i
        print "    Swapping arr[" + i + "] with arr[" + opposite + "]"
        
        int temp = arr[i]
        set arr[i] = arr[opposite]
        set arr[opposite] = temp
    
    print "  Additional memory used: 1 temporary variable"
    print "  Space complexity: O(1)"
}

array int efficiency_test = [1, 2, 3, 4, 5, 6]
print "Before: [1, 2, 3, 4, 5, 6]"
in_place_array_reverse(efficiency_test, 6)
print "After: [6, 5, 4, 3, 2, 1]"
print ""

print "=== End of Memory Management Demo ==="
