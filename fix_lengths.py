#!/usr/bin/env python3
"""
Quick script to fix LLVM string length mismatches
"""

import re
import sys

def count_llvm_bytes(llvm_string):
    """Count actual bytes in LLVM string representation"""
    count = 0
    i = 0
    while i < len(llvm_string):
        if llvm_string[i] == '\\' and i + 1 < len(llvm_string):
            next_char = llvm_string[i + 1]
            if next_char == '\\':
                count += 1  # \\ = one backslash byte
                i += 2
            elif next_char.isdigit() and i + 2 < len(llvm_string):
                # \XX hex escape = one byte
                count += 1
                i += 3
            elif next_char in 'ABCDEFabcdef' and i + 2 < len(llvm_string):
                # \XX hex escape = one byte  
                count += 1
                i += 3
            else:
                count += 1  # unknown escape = one byte
                i += 2
        elif llvm_string[i] == '%' and i + 1 < len(llvm_string) and llvm_string[i + 1] == '%':
            count += 2  # %% = two % bytes for printf
            i += 2
        else:
            count += 1  # regular character
            i += 1
    return count

def fix_llvm_file(filename):
    """Fix string length mismatches in LLVM file"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match string constants
    pattern = r'(@\w+) = private unnamed_addr constant \[(\d+) x i8\] c"([^"]*)"'
    
    def fix_match(match):
        var_name = match.group(1)
        declared_length = int(match.group(2))
        string_content = match.group(3)
        
        actual_length = count_llvm_bytes(string_content)
        
        if actual_length != declared_length:
            print(f"Fixing {var_name}: {declared_length} -> {actual_length}")
            return f'{var_name} = private unnamed_addr constant [{actual_length} x i8] c"{string_content}"'
        else:
            return match.group(0)
    
    fixed_content = re.sub(pattern, fix_match, content)
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("Fixed all length mismatches!")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python fix_lengths.py <llvm_file>")
        sys.exit(1)
    
    fix_llvm_file(sys.argv[1])
