# 28. Performance Features - Optimization and Efficiency
# This file demonstrates performance optimization features in Dolet

print "=== Performance Features Demo ==="
print ""

# Algorithm complexity analysis
print "Algorithm Complexity Analysis:"

fun linear_search(arr, size, target) {
    print "Linear Search - O(n) complexity:"
    print "  Searching for " + target + " in array of size " + size
    
    int comparisons = 0
    
    for i = 0 to size - 1
        set comparisons = comparisons + 1
        print "    Comparison " + comparisons + ": arr[" + i + "] = " + arr[i]
        
        if arr[i] == target
            print "  Found at index " + i + " after " + comparisons + " comparisons"
            return i
    
    print "  Not found after " + comparisons + " comparisons"
    return -1
}

fun binary_search(arr, size, target) {
    print "Binary Search - O(log n) complexity:"
    print "  Searching for " + target + " in sorted array of size " + size
    
    int left = 0
    int right = size - 1
    int comparisons = 0
    
    while left <= right
        set comparisons = comparisons + 1
        int mid = (left + right) / 2
        
        print "    Comparison " + comparisons + ": arr[" + mid + "] = " + arr[mid]
        
        if arr[mid] == target
            print "  Found at index " + mid + " after " + comparisons + " comparisons"
            return mid
        elif arr[mid] < target
            set left = mid + 1
        else
            set right = mid - 1
    
    print "  Not found after " + comparisons + " comparisons"
    return -1
}

array int sorted_data = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
print "Searching for 13 in [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]:"

int linear_result = linear_search(sorted_data, 10, 13)
int binary_result = binary_search(sorted_data, 10, 13)
print ""

# Caching and memoization
print "Caching and Memoization:"

array int fib_cache = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # Cache for fibonacci
int cache_hits = 0
int cache_misses = 0

fun fibonacci_cached(n) {
    print "fibonacci_cached(" + n + ")"
    
    if n <= 1
        return n
    
    if fib_cache[n] != 0
        print "  Cache HIT for fib(" + n + ") = " + fib_cache[n]
        set cache_hits = cache_hits + 1
        return fib_cache[n]
    else
        print "  Cache MISS for fib(" + n + ") - calculating..."
        set cache_misses = cache_misses + 1
        
        int result = fibonacci_cached(n - 1) + fibonacci_cached(n - 2)
        set fib_cache[n] = result
        
        print "  Cached fib(" + n + ") = " + result
        return result
}

print "Calculating fibonacci(8) with caching:"
int fib_result = fibonacci_cached(8)
print "Result: " + fib_result
print "Cache hits: " + cache_hits
print "Cache misses: " + cache_misses
print ""

# Loop optimization
print "Loop Optimization:"

fun unoptimized_matrix_multiply() {
    print "Unoptimized matrix multiplication (poor cache locality):"
    
    # Simulated 3x3 matrices
    array int a_row1 = [1, 2, 3]
    array int a_row2 = [4, 5, 6]
    array int a_row3 = [7, 8, 9]
    
    array int b_row1 = [9, 8, 7]
    array int b_row2 = [6, 5, 4]
    array int b_row3 = [3, 2, 1]
    
    int operations = 0
    
    for i = 0 to 2
        for j = 0 to 2
            int sum = 0
            for k = 0 to 2
                set operations = operations + 1
                # Simulate accessing matrix elements
                print "    Operation " + operations + ": accessing A[" + i + "][" + k + "] * B[" + k + "][" + j + "]"
                
                int a_val = 0
                int b_val = 0
                
                # Get A[i][k]
                if i == 0
                    set a_val = a_row1[k]
                elif i == 1
                    set a_val = a_row2[k]
                else
                    set a_val = a_row3[k]
                
                # Get B[k][j] (poor cache locality)
                if k == 0
                    set b_val = b_row1[j]
                elif k == 1
                    set b_val = b_row2[j]
                else
                    set b_val = b_row3[j]
                
                set sum = sum + (a_val * b_val)
            
            print "  Result[" + i + "][" + j + "] = " + sum
    
    print "Total operations: " + operations
}

unoptimized_matrix_multiply()
print ""

# Memory access patterns
print "Memory Access Patterns:"

fun demonstrate_cache_friendly_access() {
    print "Cache-friendly sequential access:"
    array int data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    int sum = 0
    for i = 0 to 9
        set sum = sum + data[i]
        print "  Sequential access: data[" + i + "] = " + data[i]
    
    print "  Sum: " + sum + " (good cache locality)"
}

fun demonstrate_cache_unfriendly_access() {
    print "Cache-unfriendly random access:"
    array int data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    array int indices = [9, 2, 7, 1, 8, 3, 6, 0, 5, 4]
    
    int sum = 0
    for i = 0 to 9
        int index = indices[i]
        set sum = sum + data[index]
        print "  Random access: data[" + index + "] = " + data[index]
    
    print "  Sum: " + sum + " (poor cache locality)"
}

demonstrate_cache_friendly_access()
demonstrate_cache_unfriendly_access()
print ""

# Lazy evaluation
print "Lazy Evaluation:"

fun lazy_range_sum(start, end, condition) {
    print "Lazy evaluation: sum of range [" + start + ", " + end + "] where condition is met"
    
    int sum = 0
    int evaluated = 0
    int skipped = 0
    
    for i = start to end
        # Only evaluate expensive condition when needed
        bool meets_condition = false
        
        if condition == "even"
            set meets_condition = (i % 2 == 0)
        elif condition == "odd"
            set meets_condition = (i % 2 == 1)
        elif condition == "divisible_by_3"
            set meets_condition = (i % 3 == 0)
        else
            set meets_condition = true
        
        if meets_condition
            print "  Evaluating: " + i + " (meets condition)"
            set sum = sum + i
            set evaluated = evaluated + 1
        else
            print "  Skipping: " + i + " (doesn't meet condition)"
            set skipped = skipped + 1
    
    print "  Result: " + sum
    print "  Evaluated: " + evaluated + ", Skipped: " + skipped
    return sum
}

int lazy_sum1 = lazy_range_sum(1, 10, "even")
int lazy_sum2 = lazy_range_sum(1, 10, "divisible_by_3")
print ""

# Branch prediction optimization
print "Branch Prediction Optimization:"

fun predictable_branches(arr, size) {
    print "Predictable branch pattern (good for CPU):"
    
    int positive_count = 0
    int negative_count = 0
    
    # First pass: count positives (predictable pattern)
    for i = 0 to size - 1
        if arr[i] > 0
            set positive_count = positive_count + 1
    
    # Second pass: count negatives (predictable pattern)
    for i = 0 to size - 1
        if arr[i] < 0
            set negative_count = negative_count + 1
    
    print "  Positive numbers: " + positive_count
    print "  Negative numbers: " + negative_count
    print "  Branch prediction: GOOD (predictable patterns)"
}

fun unpredictable_branches(arr, size) {
    print "Unpredictable branch pattern (poor for CPU):"
    
    int positive_count = 0
    int negative_count = 0
    
    # Single pass with unpredictable branches
    for i = 0 to size - 1
        if arr[i] > 0
            set positive_count = positive_count + 1
            print "    Positive: " + arr[i]
        elif arr[i] < 0
            set negative_count = negative_count + 1
            print "    Negative: " + arr[i]
        else
            print "    Zero: " + arr[i]
    
    print "  Positive numbers: " + positive_count
    print "  Negative numbers: " + negative_count
    print "  Branch prediction: POOR (unpredictable patterns)"
}

array int mixed_data = [5, -3, 8, -1, 2, -7, 4, -2, 9, -6]
predictable_branches(mixed_data, 10)
unpredictable_branches(mixed_data, 10)
print ""

# Performance profiling
print "Performance Profiling:"

int operation_count = 0

fun profile_operation(operation_name) {
    print "Profiling: " + operation_name
    set operation_count = operation_count + 1
    
    # Simulate timing
    int start_time = operation_count * 100
    
    # Simulate work
    for i = 1 to 1000
        int dummy = i * 2  # Simulated work
    
    int end_time = start_time + (operation_count * 50)
    int duration = end_time - start_time
    
    print "  Start: " + start_time + "ms"
    print "  End: " + end_time + "ms"
    print "  Duration: " + duration + "ms"
    
    return duration
}

print "Performance profile:"
int time1 = profile_operation("Array Sort")
int time2 = profile_operation("String Search")
int time3 = profile_operation("Math Calculation")

int total_time = time1 + time2 + time3
print "Total execution time: " + total_time + "ms"
print ""

# Memory vs CPU trade-offs
print "Memory vs CPU Trade-offs:"

fun space_optimized_fibonacci(n) {
    print "Space-optimized fibonacci (O(1) space, O(n) time):"
    
    if n <= 1
        return n
    
    int prev2 = 0
    int prev1 = 1
    int current = 0
    
    for i = 2 to n
        set current = prev1 + prev2
        print "  Step " + i + ": " + prev2 + " + " + prev1 + " = " + current
        set prev2 = prev1
        set prev1 = current
    
    print "  Memory used: 3 integers (constant space)"
    return current
}

print "Computing fibonacci(8) with space optimization:"
int space_opt_result = space_optimized_fibonacci(8)
print "Result: " + space_opt_result
print ""

print "=== End of Performance Features Demo ==="
