// 11. Advanced Features - match, error handling, control flow (Rust version)
// This file demonstrates advanced language features in Rust for comparison with Dolet

fn main() {
    println!("=== Advanced Features Demo (Rust) ===");
    println!();

    // Match expressions (Rust's switch equivalent)
    println!("Match expressions:");
    let day_number = 3;
    let day_name = match day_number {
        1 => "Monday",
        2 => "Tuesday", 
        3 => "Wednesday",
        4 => "Thursday",
        5 => "Friday",
        6 => "Saturday",
        7 => "Sunday",
        _ => "Invalid day",
    };
    println!("Day {} is {}", day_number, day_name);
    println!();

    // Match with multiple cases and guards
    println!("Grade calculation match:");
    let score = 85;
    let (grade, message) = match score {
        90..=100 => ("A", "Excellent performance!"),
        80..=89 => ("B", "Good performance!"),
        70..=79 => ("C", "Average performance"),
        60..=69 => ("D", "Below average"),
        _ => ("F", "Failed"),
    };
    println!("Score: {}, Grade: {}, {}", score, grade, message);
    println!();

    // Error handling with Result
    println!("Error handling with Result:");
    let dividend = 10;
    let divisor = 0;
    
    match safe_divide(dividend, divisor) {
        Ok(result) => println!("Division result: {}", result),
        Err(error) => println!("Error: {}", error),
    }
    
    match safe_divide(10, 2) {
        Ok(result) => println!("Division result: {}", result),
        Err(error) => println!("Error: {}", error),
    }
    println!();

    // Option handling
    println!("Option handling:");
    let numbers = vec![1, 2, 3, 4, 5];
    
    match find_number(&numbers, 3) {
        Some(index) => println!("Found 3 at index {}", index),
        None => println!("Number 3 not found"),
    }
    
    match find_number(&numbers, 10) {
        Some(index) => println!("Found 10 at index {}", index),
        None => println!("Number 10 not found"),
    }
    println!();

    // Pattern matching with destructuring
    println!("Pattern matching with destructuring:");
    let point = (3, 4);
    match point {
        (0, 0) => println!("Origin point"),
        (0, y) => println!("On Y-axis at {}", y),
        (x, 0) => println!("On X-axis at {}", x),
        (x, y) => println!("Point at ({}, {})", x, y),
    }
    println!();

    // Loop control with break and continue
    println!("Loop control with break and continue:");
    for i in 1..=10 {
        if i == 3 {
            println!("Skipping {}", i);
            continue;
        }
        if i == 7 {
            println!("Breaking at {}", i);
            break;
        }
        println!("Processing {}", i);
    }
    println!();

    // Nested loops with labeled breaks
    println!("Nested loops with labeled breaks:");
    'outer: for i in 1..=3 {
        println!("Outer loop i = {}", i);
        for j in 1..=3 {
            println!("  Inner loop j = {}", j);
            if i == 2 && j == 2 {
                println!("  Breaking outer loop at ({}, {})", i, j);
                break 'outer;
            }
        }
    }
    println!();

    // While let pattern matching
    println!("While let pattern matching:");
    let mut stack = vec![1, 2, 3, 4, 5];
    while let Some(value) = stack.pop() {
        println!("Popped: {}", value);
    }
    println!("Stack is empty");
    println!();

    // If let pattern matching
    println!("If let pattern matching:");
    let maybe_number = Some(42);
    if let Some(number) = maybe_number {
        println!("Got number: {}", number);
    } else {
        println!("No number found");
    }
    println!();

    // Advanced match with guards
    println!("Advanced match with guards:");
    let number = 15;
    match number {
        n if n < 0 => println!("{} is negative", n),
        n if n == 0 => println!("{} is zero", n),
        n if n % 2 == 0 => println!("{} is positive and even", n),
        n => println!("{} is positive and odd", n),
    }
    println!();

    // Closure examples
    println!("Closure examples:");
    let numbers = vec![1, 2, 3, 4, 5];
    
    let doubled: Vec<i32> = numbers.iter().map(|x| x * 2).collect();
    println!("Doubled: {:?}", doubled);
    
    let evens: Vec<i32> = numbers.iter().filter(|&&x| x % 2 == 0).cloned().collect();
    println!("Evens: {:?}", evens);
    
    let sum: i32 = numbers.iter().sum();
    println!("Sum: {}", sum);
    println!();

    // Iterator chaining
    println!("Iterator chaining:");
    let result: Vec<i32> = (1..=10)
        .filter(|&x| x % 2 == 0)
        .map(|x| x * x)
        .collect();
    println!("Even squares from 1-10: {:?}", result);
    println!();

    // Enum with associated data
    println!("Enum with associated data:");
    let messages = vec![
        Message::Text("Hello".to_string()),
        Message::Number(42),
        Message::Quit,
    ];
    
    for msg in messages {
        process_message(msg);
    }
    println!();

    // Advanced error propagation
    println!("Advanced error propagation:");
    match complex_operation() {
        Ok(result) => println!("Complex operation result: {}", result),
        Err(e) => println!("Complex operation failed: {}", e),
    }
    println!();

    println!("=== End of Advanced Features Demo ===");
}

fn safe_divide(a: i32, b: i32) -> Result<i32, String> {
    if b == 0 {
        Err("Division by zero".to_string())
    } else {
        Ok(a / b)
    }
}

fn find_number(numbers: &[i32], target: i32) -> Option<usize> {
    numbers.iter().position(|&x| x == target)
}

#[derive(Debug)]
enum Message {
    Text(String),
    Number(i32),
    Quit,
}

fn process_message(msg: Message) {
    match msg {
        Message::Text(text) => println!("Text message: {}", text),
        Message::Number(num) => println!("Number message: {}", num),
        Message::Quit => println!("Quit message received"),
    }
}

fn complex_operation() -> Result<i32, String> {
    let step1 = safe_divide(20, 4)?;
    let step2 = safe_divide(step1, 2)?;
    Ok(step2 * 3)
}
