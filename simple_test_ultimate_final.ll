; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 13 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"World\0A\00", align 1
@.str_2 = private unnamed_addr constant [13 x i8] c"Line1\0ALine2\0A\00", align 1
@.str_3 = private unnamed_addr constant [11 x i8] c"Col1\09Col2\0A\00", align 1
@.str_4 = private unnamed_addr constant [8 x i8] c"Answer\0A\00", align 1
@.str_expr_0 = private unnamed_addr constant [11 x i8] c"Combined: \00", align 1
@.str_expr_1 = private unnamed_addr constant [15 x i8] c"Newline test: \00", align 1
@.str_expr_2 = private unnamed_addr constant [11 x i8] c"Tab test: \00", align 1
@.str_expr_3 = private unnamed_addr constant [8 x i8] c"Mixed: \00", align 1
@.str_expr_4 = private unnamed_addr constant [5 x i8] c" is \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_fmt_newline = private unnamed_addr constant [4 x i8] c"%s\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1
@str_dyn_0 = private unnamed_addr constant [13 x i8] c"Line1\\nLine2\00", align 1
@str_dyn_1 = private unnamed_addr constant [11 x i8] c"Col1\\tCol2\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_part1 = global i8* null, align 8
@global_part2 = global i8* null, align 8
@global_combined = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_with_newline = global i8* null, align 8
@global_with_tab = global i8* null, align 8
@global_number = global i32 0, align 4
@global_text = global i8* null, align 8

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== SIMPLE TEST ===" (inline)
  %tmp_1_str = alloca [20 x i8], align 1
  store [20 x i8] c"=== SIMPLE TEST ===\00", [20 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [20 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string part1 = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_part1, align 8
  ; string part2 = "World"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_part2, align 8
  ; string combined = part1 + part2
  ; string assignment from variable/expression: combined = part1 + part2
  ; combined = part1 + part2
  %tmp_3 = load i8*, i8** @global_part1, align 8
  %tmp_4 = load i8*, i8** @global_part2, align 8
  %tmp_5 = call i64 @strlen(i8* %tmp_3)
  %tmp_6 = call i64 @strlen(i8* %tmp_4)
  %tmp_7 = add i64 %tmp_5, %tmp_6
  %tmp_8 = add i64 %tmp_7, 1  ; +1 for null terminator
  %tmp_9 = call i8* @malloc(i64 %tmp_8)
  call i8* @strcpy(i8* %tmp_9, i8* %tmp_3)
  call i8* @strcat(i8* %tmp_9, i8* %tmp_4)
  store i8* %tmp_9, i8** @global_combined, align 8
  ; print expression: "Combined: " + combined
  %tmp_10 = alloca i64, align 8
  store i64 1, i64* %tmp_10, align 8  ; Start with 1 for null terminator
  %tmp_11_str = alloca [11 x i8], align 1
  %tmp_12 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 0
  store i8 67, i8* %tmp_12, align 1
  %tmp_13 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 1
  store i8 111, i8* %tmp_13, align 1
  %tmp_14 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 2
  store i8 109, i8* %tmp_14, align 1
  %tmp_15 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 3
  store i8 98, i8* %tmp_15, align 1
  %tmp_16 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 4
  store i8 105, i8* %tmp_16, align 1
  %tmp_17 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 5
  store i8 110, i8* %tmp_17, align 1
  %tmp_18 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 6
  store i8 101, i8* %tmp_18, align 1
  %tmp_19 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 7
  store i8 100, i8* %tmp_19, align 1
  %tmp_20 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 8
  store i8 58, i8* %tmp_20, align 1
  %tmp_21 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 9
  store i8 32, i8* %tmp_21, align 1
  %tmp_22 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_11_str, i64 0, i64 10
  store i8 0, i8* %tmp_22, align 1
  %tmp_23 = bitcast [11 x i8]* %tmp_11_str to i8*
  %tmp_24 = load i64, i64* %tmp_10, align 8
  %tmp_25 = add i64 %tmp_24, 10
  store i64 %tmp_25, i64* %tmp_10, align 8
  %tmp_26 = load i8*, i8** @global_combined, align 8
  %tmp_27 = call i64 @strlen(i8* %tmp_26)
  %tmp_28 = load i64, i64* %tmp_10, align 8
  %tmp_29 = add i64 %tmp_28, %tmp_27
  store i64 %tmp_29, i64* %tmp_10, align 8
  %tmp_30 = load i64, i64* %tmp_10, align 8
  %tmp_31 = call i8* @malloc(i64 %tmp_30)
  store i8 0, i8* %tmp_31, align 1  ; Initialize with empty string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_23)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_26)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string with_newline = "Line1\nLine2"
  store i8* getelementptr inbounds ([13 x i8], [13 x i8]* @str_dyn_0, i64 0, i64 0), i8** @global_with_newline, align 8
  ; string with_tab = "Col1\tCol2"
  store i8* getelementptr inbounds ([11 x i8], [11 x i8]* @str_dyn_1, i64 0, i64 0), i8** @global_with_tab, align 8
  ; print expression: "Newline test: " + with_newline
  %tmp_32 = alloca i64, align 8
  store i64 1, i64* %tmp_32, align 8  ; Start with 1 for null terminator
  %tmp_33_str = alloca [15 x i8], align 1
  %tmp_34 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 0
  store i8 78, i8* %tmp_34, align 1
  %tmp_35 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 1
  store i8 101, i8* %tmp_35, align 1
  %tmp_36 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 2
  store i8 119, i8* %tmp_36, align 1
  %tmp_37 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 3
  store i8 108, i8* %tmp_37, align 1
  %tmp_38 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 4
  store i8 105, i8* %tmp_38, align 1
  %tmp_39 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 5
  store i8 110, i8* %tmp_39, align 1
  %tmp_40 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 6
  store i8 101, i8* %tmp_40, align 1
  %tmp_41 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 7
  store i8 32, i8* %tmp_41, align 1
  %tmp_42 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 8
  store i8 116, i8* %tmp_42, align 1
  %tmp_43 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 9
  store i8 101, i8* %tmp_43, align 1
  %tmp_44 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 10
  store i8 115, i8* %tmp_44, align 1
  %tmp_45 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 11
  store i8 116, i8* %tmp_45, align 1
  %tmp_46 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 12
  store i8 58, i8* %tmp_46, align 1
  %tmp_47 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 13
  store i8 32, i8* %tmp_47, align 1
  %tmp_48 = getelementptr inbounds [15 x i8], [15 x i8]* %tmp_33_str, i64 0, i64 14
  store i8 0, i8* %tmp_48, align 1
  %tmp_49 = bitcast [15 x i8]* %tmp_33_str to i8*
  %tmp_50 = load i64, i64* %tmp_32, align 8
  %tmp_51 = add i64 %tmp_50, 14
  store i64 %tmp_51, i64* %tmp_32, align 8
  %tmp_52 = load i8*, i8** @global_with_newline, align 8
  %tmp_53 = call i64 @strlen(i8* %tmp_52)
  %tmp_54 = load i64, i64* %tmp_32, align 8
  %tmp_55 = add i64 %tmp_54, %tmp_53
  store i64 %tmp_55, i64* %tmp_32, align 8
  %tmp_56 = load i64, i64* %tmp_32, align 8
  %tmp_57 = call i8* @malloc(i64 %tmp_56)
  store i8 0, i8* %tmp_57, align 1  ; Initialize with empty string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_49)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_52)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Tab test: " + with_tab
  %tmp_58 = alloca i64, align 8
  store i64 1, i64* %tmp_58, align 8  ; Start with 1 for null terminator
  %tmp_59_str = alloca [11 x i8], align 1
  %tmp_60 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 0
  store i8 84, i8* %tmp_60, align 1
  %tmp_61 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 1
  store i8 97, i8* %tmp_61, align 1
  %tmp_62 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 2
  store i8 98, i8* %tmp_62, align 1
  %tmp_63 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 3
  store i8 32, i8* %tmp_63, align 1
  %tmp_64 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 4
  store i8 116, i8* %tmp_64, align 1
  %tmp_65 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 5
  store i8 101, i8* %tmp_65, align 1
  %tmp_66 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 6
  store i8 115, i8* %tmp_66, align 1
  %tmp_67 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 7
  store i8 116, i8* %tmp_67, align 1
  %tmp_68 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 8
  store i8 58, i8* %tmp_68, align 1
  %tmp_69 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 9
  store i8 32, i8* %tmp_69, align 1
  %tmp_70 = getelementptr inbounds [11 x i8], [11 x i8]* %tmp_59_str, i64 0, i64 10
  store i8 0, i8* %tmp_70, align 1
  %tmp_71 = bitcast [11 x i8]* %tmp_59_str to i8*
  %tmp_72 = load i64, i64* %tmp_58, align 8
  %tmp_73 = add i64 %tmp_72, 10
  store i64 %tmp_73, i64* %tmp_58, align 8
  %tmp_74 = load i8*, i8** @global_with_tab, align 8
  %tmp_75 = call i64 @strlen(i8* %tmp_74)
  %tmp_76 = load i64, i64* %tmp_58, align 8
  %tmp_77 = add i64 %tmp_76, %tmp_75
  store i64 %tmp_77, i64* %tmp_58, align 8
  %tmp_78 = load i64, i64* %tmp_58, align 8
  %tmp_79 = call i8* @malloc(i64 %tmp_78)
  store i8 0, i8* %tmp_79, align 1  ; Initialize with empty string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_71)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_74)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int number = 42
  store i32 42, i32* @global_number, align 4
  ; string text = "Answer"
  store i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_4, i64 0, i64 0), i8** @global_text, align 8
  ; print expression: "Mixed: " + text + " is " + number
  %tmp_80 = alloca i64, align 8
  store i64 1, i64* %tmp_80, align 8  ; Start with 1 for null terminator
  %tmp_81_str = alloca [8 x i8], align 1
  %tmp_82 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 0
  store i8 77, i8* %tmp_82, align 1
  %tmp_83 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 1
  store i8 105, i8* %tmp_83, align 1
  %tmp_84 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 2
  store i8 120, i8* %tmp_84, align 1
  %tmp_85 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 3
  store i8 101, i8* %tmp_85, align 1
  %tmp_86 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 4
  store i8 100, i8* %tmp_86, align 1
  %tmp_87 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 5
  store i8 58, i8* %tmp_87, align 1
  %tmp_88 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 6
  store i8 32, i8* %tmp_88, align 1
  %tmp_89 = getelementptr inbounds [8 x i8], [8 x i8]* %tmp_81_str, i64 0, i64 7
  store i8 0, i8* %tmp_89, align 1
  %tmp_90 = bitcast [8 x i8]* %tmp_81_str to i8*
  %tmp_91 = load i64, i64* %tmp_80, align 8
  %tmp_92 = add i64 %tmp_91, 7
  store i64 %tmp_92, i64* %tmp_80, align 8
  %tmp_93 = load i8*, i8** @global_text, align 8
  %tmp_94 = call i64 @strlen(i8* %tmp_93)
  %tmp_95 = load i64, i64* %tmp_80, align 8
  %tmp_96 = add i64 %tmp_95, %tmp_94
  store i64 %tmp_96, i64* %tmp_80, align 8
  %tmp_97_str = alloca [5 x i8], align 1
  %tmp_98 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_97_str, i64 0, i64 0
  store i8 32, i8* %tmp_98, align 1
  %tmp_99 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_97_str, i64 0, i64 1
  store i8 105, i8* %tmp_99, align 1
  %tmp_100 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_97_str, i64 0, i64 2
  store i8 115, i8* %tmp_100, align 1
  %tmp_101 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_97_str, i64 0, i64 3
  store i8 32, i8* %tmp_101, align 1
  %tmp_102 = getelementptr inbounds [5 x i8], [5 x i8]* %tmp_97_str, i64 0, i64 4
  store i8 0, i8* %tmp_102, align 1
  %tmp_103 = bitcast [5 x i8]* %tmp_97_str to i8*
  %tmp_104 = load i64, i64* %tmp_80, align 8
  %tmp_105 = add i64 %tmp_104, 4
  store i64 %tmp_105, i64* %tmp_80, align 8
  %tmp_106 = load i32, i32* @global_number, align 4
  %tmp_107 = load i64, i64* %tmp_80, align 8
  %tmp_108 = add i64 %tmp_107, 12
  store i64 %tmp_108, i64* %tmp_80, align 8
  %tmp_109 = load i64, i64* %tmp_80, align 8
  %tmp_110 = call i8* @malloc(i64 %tmp_109)
  store i8 0, i8* %tmp_110, align 1  ; Initialize with empty string
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_90)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_fmt, i32 0, i32 0), i8* %tmp_103)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_106)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== END TEST ===" (inline)
  %tmp_111_str = alloca [17 x i8], align 1
  store [17 x i8] c"=== END TEST ===\00", [17 x i8]* %tmp_111_str, align 1
  %tmp_112 = bitcast [17 x i8]* %tmp_111_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_112)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
