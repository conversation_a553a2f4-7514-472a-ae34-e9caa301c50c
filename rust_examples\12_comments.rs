// 12. Comments - single line, multi-line, documentation (Rust version)
// This file demonstrates all comment types in Rust for comparison with Dolet

fn main() {
    println!("=== Comments Demo (Rust) ===");
    println!();

    // This is a single-line comment
    // Comments help explain what the code does
    println!("Single-line comments are marked with //");

    /*
     * This is a multi-line comment
     * It can span multiple lines
     * Useful for longer explanations
     */
    println!("Multi-line comments use /* */");
    println!();

    // Variable declarations with comments
    let name = "<PERSON>"; // This stores the user's name
    let age = 25; // User's age in years
    let salary = 5000.0; // Monthly salary in currency units
    
    println!("User information:");
    println!("Name: {}", name);
    println!("Age: {}", age);
    println!("Salary: {}", salary);
    println!();

    // Mathematical operations with explanatory comments
    let a = 10; // First number
    let b = 5;  // Second number
    
    // Perform basic arithmetic operations
    let sum = a + b;        // Addition
    let difference = a - b; // Subtraction
    let product = a * b;    // Multiplication
    let quotient = a / b;   // Division
    
    println!("Mathematical operations:");
    println!("{} + {} = {}", a, b, sum);
    println!("{} - {} = {}", a, b, difference);
    println!("{} * {} = {}", a, b, product);
    println!("{} / {} = {}", a, b, quotient);
    println!();

    /*
     * Array operations section
     * This section demonstrates array usage
     * with detailed comments
     */
    let numbers = [1, 2, 3, 4, 5]; // Initialize array with 5 elements
    
    // Access array elements
    println!("Array operations:");
    println!("First element: {}", numbers[0]); // Index 0
    println!("Last element: {}", numbers[4]);  // Index 4
    
    // Loop through array with comments
    println!("All elements:");
    for (index, value) in numbers.iter().enumerate() {
        // Print each element with its index
        println!("  numbers[{}] = {}", index, value);
    }
    println!();

    // Conditional statements with comments
    let score = 85; // Student's test score
    
    /*
     * Grade calculation logic:
     * A: 90-100
     * B: 80-89
     * C: 70-79
     * D: 60-69
     * F: Below 60
     */
    if score >= 90 {
        println!("Grade: A (Excellent)"); // Top grade
    } else if score >= 80 {
        println!("Grade: B (Good)"); // Good performance
    } else if score >= 70 {
        println!("Grade: C (Average)"); // Average performance
    } else if score >= 60 {
        println!("Grade: D (Below Average)"); // Needs improvement
    } else {
        println!("Grade: F (Failed)"); // Failed
    }
    println!();

    // Function call with comments
    let area = calculate_rectangle_area(12, 8); // Calculate area of 12x8 rectangle
    println!("Rectangle area: {} square units", area);
    println!();

    // Loop examples with comments
    println!("Loop examples:");
    
    // For loop with range
    println!("Counting from 1 to 5:");
    for i in 1..=5 {
        println!("  Count: {}", i); // Print current count
    }
    
    // While loop example
    let mut counter = 1; // Initialize counter
    println!("While loop countdown:");
    while counter <= 3 {
        println!("  Counter: {}", counter); // Display counter value
        counter += 1; // Increment counter
    }
    println!();

    /*
     * String operations section
     * Demonstrates various string manipulations
     * with comprehensive comments
     */
    let first_name = "Ahmed";    // First name
    let last_name = "Hassan";    // Last name
    let full_name = format!("{} {}", first_name, last_name); // Combine names
    
    println!("String operations:");
    println!("Full name: {}", full_name);
    
    // String properties
    let text = "Hello, World!";
    println!("Text: '{}'", text);
    println!("Length: {}", text.len()); // Get string length
    println!("Contains 'World': {}", text.contains("World")); // Check substring
    println!();

    // Boolean operations with comments
    let is_adult = age >= 18;     // Check if person is adult
    let has_income = salary > 0.0; // Check if person has income
    let is_eligible = is_adult && has_income; // Combine conditions
    
    println!("Boolean operations:");
    println!("Is adult: {}", is_adult);
    println!("Has income: {}", has_income);
    println!("Is eligible: {}", is_eligible);
    println!();

    // Complex calculation with step-by-step comments
    let principal = 1000.0_f64;  // Initial amount
    let rate = 0.05_f64;         // Interest rate (5%)
    let time = 2.0_f64;          // Time in years

    // Calculate compound interest: A = P(1 + r)^t
    let amount = principal * (1.0 + rate).powf(time);
    let interest = amount - principal; // Interest earned
    
    println!("Compound interest calculation:");
    println!("Principal: ${:.2}", principal);
    println!("Rate: {:.1}%", rate * 100.0);
    println!("Time: {:.0} years", time);
    println!("Final amount: ${:.2}", amount);
    println!("Interest earned: ${:.2}", interest);
    println!();

    // TODO: Add more examples later
    // FIXME: This section needs optimization
    // NOTE: Remember to test edge cases
    
    /*
     * End of demonstration
     * This concludes the comments example
     * showing various comment styles and uses
     */
    println!("=== End of Comments Demo ===");
}

/// This is a documentation comment for the function
/// It calculates the area of a rectangle
/// 
/// # Arguments
/// * `length` - The length of the rectangle
/// * `width` - The width of the rectangle
/// 
/// # Returns
/// The area as an integer
fn calculate_rectangle_area(length: i32, width: i32) -> i32 {
    // Simple multiplication to get area
    length * width // Return the result
}

/*
 * Additional utility functions
 * These functions demonstrate different
 * commenting styles and documentation
 */

/// Calculates the perimeter of a rectangle
fn calculate_perimeter(length: i32, width: i32) -> i32 {
    // Perimeter formula: 2 * (length + width)
    2 * (length + width)
}

// Helper function to check if number is even
fn is_even(number: i32) -> bool {
    number % 2 == 0 // Return true if divisible by 2
}

/*
 * Function to demonstrate nested comments
 * /* This is a nested comment inside a block comment */
 * Rust allows nested block comments
 */
fn nested_comment_example() {
    println!("This function demonstrates nested comments");
    // Single line comment inside function
    /* Block comment inside function */
}
