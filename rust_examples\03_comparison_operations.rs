// 3. Comparison Operations - All comparison operators (Rust version)
// This file demonstrates all comparison operations in Rust for comparison with Dolet

fn main() {
    println!("=== Comparison Operations Demo (Rust) ===");
    println!();

    // Basic comparison variables
    let a: i32 = 10;
    let b: i32 = 5;
    let c: i32 = 10;
    println!("Test values: a=10, b=5, c=10");
    println!();

    // Greater than (>)
    println!("Greater Than (>) Tests:");
    if a > b {
        println!("✓ {} > {} is TRUE", a, b);
    } else {
        println!("✗ {} > {} is FALSE", a, b);
    }

    if b > a {
        println!("✓ {} > {} is TRUE", b, a);
    } else {
        println!("✗ {} > {} is FALSE", b, a);
    }
    println!();

    // Greater than or equal (>=)
    println!("Greater Than or Equal (>=) Tests:");
    if a >= c {
        println!("✓ {} >= {} is TRUE", a, c);
    } else {
        println!("✗ {} >= {} is FALSE", a, c);
    }

    if a >= b {
        println!("✓ {} >= {} is TRUE", a, b);
    } else {
        println!("✗ {} >= {} is FALSE", a, b);
    }

    if b >= a {
        println!("✓ {} >= {} is TRUE", b, a);
    } else {
        println!("✗ {} >= {} is FALSE", b, a);
    }
    println!();

    // Less than (<)
    println!("Less Than (<) Tests:");
    if b < a {
        println!("✓ {} < {} is TRUE", b, a);
    } else {
        println!("✗ {} < {} is FALSE", b, a);
    }

    if a < b {
        println!("✓ {} < {} is TRUE", a, b);
    } else {
        println!("✗ {} < {} is FALSE", a, b);
    }
    println!();

    // Less than or equal (<=)
    println!("Less Than or Equal (<=) Tests:");
    if a <= c {
        println!("✓ {} <= {} is TRUE", a, c);
    } else {
        println!("✗ {} <= {} is FALSE", a, c);
    }

    if b <= a {
        println!("✓ {} <= {} is TRUE", b, a);
    } else {
        println!("✗ {} <= {} is FALSE", b, a);
    }

    if a <= b {
        println!("✓ {} <= {} is TRUE", a, b);
    } else {
        println!("✗ {} <= {} is FALSE", a, b);
    }
    println!();

    // Equal to (==)
    println!("Equal To (==) Tests:");
    if a == c {
        println!("✓ {} == {} is TRUE", a, c);
    } else {
        println!("✗ {} == {} is FALSE", a, c);
    }

    if a == b {
        println!("✓ {} == {} is TRUE", a, b);
    } else {
        println!("✗ {} == {} is FALSE", a, b);
    }
    println!();

    // Not equal to (!=)
    println!("Not Equal To (!=) Tests:");
    if a != b {
        println!("✓ {} != {} is TRUE", a, b);
    } else {
        println!("✗ {} != {} is FALSE", a, b);
    }

    if a != c {
        println!("✓ {} != {} is TRUE", a, c);
    } else {
        println!("✗ {} != {} is FALSE", a, c);
    }
    println!();

    // String comparisons
    let name1 = "Ahmed";
    let name2 = "Ali";
    let name3 = "Ahmed";
    println!("String Comparison Tests:");
    println!("Strings: name1='Ahmed', name2='Ali', name3='Ahmed'");

    if name1 == name3 {
        println!("✓ name1 == name3 is TRUE");
    } else {
        println!("✗ name1 == name3 is FALSE");
    }

    if name1 != name2 {
        println!("✓ name1 != name2 is TRUE");
    } else {
        println!("✗ name1 != name2 is FALSE");
    }
    println!();

    // Boolean comparisons
    let flag1: bool = true;
    let flag2: bool = false;
    let flag3: bool = true;
    println!("Boolean Comparison Tests:");
    println!("Booleans: flag1=true, flag2=false, flag3=true");

    if flag1 == flag3 {
        println!("✓ flag1 == flag3 is TRUE");
    } else {
        println!("✗ flag1 == flag3 is FALSE");
    }

    if flag1 != flag2 {
        println!("✓ flag1 != flag2 is TRUE");
    } else {
        println!("✗ flag1 != flag2 is FALSE");
    }
    println!();

    // Float comparisons
    let x: f32 = 5.5;
    let y: f32 = 3.2;
    let z: f32 = 5.5;
    println!("Float Comparison Tests:");
    println!("Floats: x=5.5, y=3.2, z=5.5");

    if x > y {
        println!("✓ x > y is TRUE");
    }

    if (x - z).abs() < f32::EPSILON {  // Better float comparison
        println!("✓ x == z is TRUE (approximately)");
    }

    if y < x {
        println!("✓ y < x is TRUE");
    }
    println!();

    // Complex comparisons with expressions
    println!("Complex Expression Comparisons:");
    if (a + b) > (c * 2) {
        println!("✓ (a + b) > (c * 2) is TRUE");
    } else {
        println!("✗ (a + b) > (c * 2) is FALSE");
    }

    if (a * 2) == (c + a) {
        println!("✓ (a * 2) == (c + a) is TRUE");
    } else {
        println!("✗ (a * 2) == (c + a) is FALSE");
    }

    println!();
    println!("=== End of Comparison Operations Demo ===");
}
