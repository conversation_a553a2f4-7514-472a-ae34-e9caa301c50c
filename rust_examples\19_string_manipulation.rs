// 19. String Manipulation - advanced string operations (Rust version)
// This file demonstrates string manipulation in Rust for comparison with Dolet

fn main() {
    println!("=== String Manipulation Demo (Rust) ===");
    println!();

    // Basic string operations
    println!("Basic string operations:");
    let text = "Hello, World!";
    println!("Original: '{}'", text);
    println!("Length: {}", text.len());
    println!("Is empty: {}", text.is_empty());
    println!("Uppercase: '{}'", text.to_uppercase());
    println!("Lowercase: '{}'", text.to_lowercase());
    println!();

    // String trimming
    println!("String trimming:");
    let padded = "   Hello, World!   ";
    println!("Original: '{}'", padded);
    println!("Trimmed: '{}'", padded.trim());
    println!("Trim start: '{}'", padded.trim_start());
    println!("Trim end: '{}'", padded.trim_end());
    
    let custom_trim = "...Hello, World!...";
    println!("Custom trim: '{}'", custom_trim.trim_matches('.'));
    println!();

    // String searching
    println!("String searching:");
    let search_text = "The quick brown fox jumps over the lazy dog";
    println!("Text: '{}'", search_text);
    println!("Contains 'fox': {}", search_text.contains("fox"));
    println!("Contains 'cat': {}", search_text.contains("cat"));
    println!("Starts with 'The': {}", search_text.starts_with("The"));
    println!("Ends with 'dog': {}", search_text.ends_with("dog"));
    
    if let Some(pos) = search_text.find("fox") {
        println!("'fox' found at position: {}", pos);
    }
    
    if let Some(pos) = search_text.rfind("the") {
        println!("Last 'the' found at position: {}", pos);
    }
    println!();

    // String replacement
    println!("String replacement:");
    let original = "Hello World Hello Universe";
    println!("Original: '{}'", original);
    println!("Replace 'Hello' with 'Hi': '{}'", original.replace("Hello", "Hi"));
    println!("Replace first 'Hello': '{}'", original.replacen("Hello", "Hi", 1));
    println!();

    // String splitting
    println!("String splitting:");
    let csv_data = "apple,banana,orange,grape";
    let words_data = "one two three four five";
    let multiline = "line1\nline2\nline3";
    
    println!("CSV data: '{}'", csv_data);
    let csv_parts: Vec<&str> = csv_data.split(',').collect();
    println!("Split by comma: {:?}", csv_parts);
    
    println!("Words data: '{}'", words_data);
    let word_parts: Vec<&str> = words_data.split_whitespace().collect();
    println!("Split by whitespace: {:?}", word_parts);
    
    println!("Multiline: '{}'", multiline.replace('\n', "\\n"));
    let line_parts: Vec<&str> = multiline.lines().collect();
    println!("Split by lines: {:?}", line_parts);
    println!();

    // String joining
    println!("String joining:");
    let words = vec!["Hello", "beautiful", "world"];
    let numbers = vec!["1", "2", "3", "4", "5"];
    
    println!("Words: {:?}", words);
    println!("Joined with spaces: '{}'", words.join(" "));
    println!("Joined with dashes: '{}'", words.join("-"));
    
    println!("Numbers: {:?}", numbers);
    println!("Joined with commas: '{}'", numbers.join(","));
    println!();

    // String slicing and indexing
    println!("String slicing:");
    let slice_text = "Programming";
    println!("Original: '{}'", slice_text);
    println!("First 4 chars: '{}'", &slice_text[0..4]);
    println!("Last 4 chars: '{}'", &slice_text[slice_text.len()-4..]);
    println!("Middle chars [2..8]: '{}'", &slice_text[2..8]);
    
    // Character access
    let chars: Vec<char> = slice_text.chars().collect();
    println!("Characters: {:?}", chars);
    println!("First char: '{}'", chars[0]);
    println!("Last char: '{}'", chars[chars.len()-1]);
    println!();

    // String formatting
    println!("String formatting:");
    let name = "Ahmed";
    let age = 25;
    let salary = 5000.50;
    
    println!("Basic format: '{}'", format!("Name: {}, Age: {}", name, age));
    println!("Positional: '{}'", format!("{1} is {0} years old", age, name));
    println!("Named: '{}'", format!("{name} earns ${salary:.2}", name=name, salary=salary));
    println!("Padding: '{}'", format!("{:>10} {:^10} {:<10}", "Right", "Center", "Left"));
    println!("Numbers: '{}'", format!("Hex: {:x}, Octal: {:o}, Binary: {:b}", 255, 255, 255));
    println!();

    // String case operations
    println!("String case operations:");
    let mixed_case = "hELLo WoRLd";
    println!("Original: '{}'", mixed_case);
    println!("Title case: '{}'", to_title_case(mixed_case));
    println!("Sentence case: '{}'", to_sentence_case(mixed_case));
    println!("Toggle case: '{}'", toggle_case(mixed_case));
    println!();

    // String validation
    println!("String validation:");
    let test_strings = ["hello123", "HELLO", "123456", "Hello World", ""];
    
    for s in test_strings {
        println!("String: '{}'", s);
        println!("  Is alphabetic: {}", s.chars().all(|c| c.is_alphabetic()));
        println!("  Is numeric: {}", s.chars().all(|c| c.is_numeric()));
        println!("  Is alphanumeric: {}", s.chars().all(|c| c.is_alphanumeric()));
        println!("  Is uppercase: {}", s.chars().all(|c| c.is_uppercase() || !c.is_alphabetic()));
        println!("  Is lowercase: {}", s.chars().all(|c| c.is_lowercase() || !c.is_alphabetic()));
    }
    println!();

    // String reversal
    println!("String reversal:");
    let reverse_tests = ["hello", "racecar", "12345", "A man a plan a canal Panama"];
    
    for s in reverse_tests {
        let reversed = reverse_string(s);
        println!("Original: '{}' -> Reversed: '{}'", s, reversed);
    }
    println!();

    // Palindrome checking
    println!("Palindrome checking:");
    let palindrome_tests = ["racecar", "hello", "A man a plan a canal Panama", "race a car", "madam"];
    
    for s in palindrome_tests {
        let is_palindrome = is_palindrome(s);
        println!("'{}' is palindrome: {}", s, is_palindrome);
    }
    println!();

    // String counting operations
    println!("String counting operations:");
    let count_text = "Hello World! How are you today? Hello again!";
    println!("Text: '{}'", count_text);
    println!("Character count: {}", count_text.len());
    println!("Word count: {}", count_words(count_text));
    println!("Sentence count: {}", count_sentences(count_text));
    println!("Count 'Hello': {}", count_substring(count_text, "Hello"));
    println!("Count 'o': {}", count_char(count_text, 'o'));
    println!();

    // String encoding/decoding simulation
    println!("String encoding/decoding simulation:");
    let plain_text = "Hello World";
    let encoded = simple_encode(plain_text, 3); // Caesar cipher with shift 3
    let decoded = simple_decode(&encoded, 3);
    
    println!("Original: '{}'", plain_text);
    println!("Encoded (Caesar +3): '{}'", encoded);
    println!("Decoded: '{}'", decoded);
    println!();

    // String comparison
    println!("String comparison:");
    let str1 = "apple";
    let str2 = "banana";
    let str3 = "Apple";
    
    println!("Comparing strings:");
    println!("'{}' == '{}': {}", str1, str2, str1 == str2);
    println!("'{}' == '{}': {}", str1, str3, str1 == str3);
    println!("'{}' == '{}' (case insensitive): {}", str1, str3, str1.to_lowercase() == str3.to_lowercase());
    println!("'{}' < '{}': {}", str1, str2, str1 < str2);
    println!("'{}' > '{}': {}", str2, str1, str2 > str1);
    println!();

    // String pattern matching
    println!("String pattern matching:");
    let email = "<EMAIL>";
    let phone = "************";
    let url = "https://www.example.com";
    
    println!("Email '{}' is valid: {}", email, is_valid_email(email));
    println!("Phone '{}' is valid: {}", phone, is_valid_phone(phone));
    println!("URL '{}' is valid: {}", url, is_valid_url(url));
    println!();

    // String manipulation chains
    println!("String manipulation chains:");
    let messy_text = "  hELLo,    WoRLd!   ";
    let cleaned = messy_text
        .trim()
        .to_lowercase()
        .replace(",", "")
        .replace("!", "");
    
    println!("Messy: '{}'", messy_text);
    println!("Cleaned: '{}'", cleaned);
    
    let sentence = "the quick brown fox";
    let processed = sentence
        .split_whitespace()
        .map(|word| capitalize_first_letter(word))
        .collect::<Vec<_>>()
        .join(" ");
    
    println!("Original sentence: '{}'", sentence);
    println!("Processed: '{}'", processed);
    println!();

    // String statistics
    println!("String statistics:");
    let stats_text = "The Quick Brown Fox Jumps Over The Lazy Dog! 123";
    let stats = analyze_string(stats_text);
    
    println!("Text: '{}'", stats_text);
    println!("Total characters: {}", stats.total_chars);
    println!("Letters: {}", stats.letters);
    println!("Digits: {}", stats.digits);
    println!("Spaces: {}", stats.spaces);
    println!("Punctuation: {}", stats.punctuation);
    println!("Words: {}", stats.words);
    println!("Uppercase letters: {}", stats.uppercase);
    println!("Lowercase letters: {}", stats.lowercase);
    println!();

    println!("=== End of String Manipulation Demo ===");
}

// Helper functions

fn to_title_case(s: &str) -> String {
    s.split_whitespace()
        .map(|word| capitalize_first_letter(word))
        .collect::<Vec<_>>()
        .join(" ")
}

fn to_sentence_case(s: &str) -> String {
    let lower = s.to_lowercase();
    capitalize_first_letter(&lower)
}

fn toggle_case(s: &str) -> String {
    s.chars()
        .map(|c| {
            if c.is_uppercase() {
                c.to_lowercase().collect::<String>()
            } else {
                c.to_uppercase().collect::<String>()
            }
        })
        .collect()
}

fn reverse_string(s: &str) -> String {
    s.chars().rev().collect()
}

fn is_palindrome(s: &str) -> bool {
    let cleaned: String = s.chars()
        .filter(|c| c.is_alphanumeric())
        .map(|c| c.to_lowercase().next().unwrap())
        .collect();
    
    cleaned == cleaned.chars().rev().collect::<String>()
}

fn count_words(s: &str) -> usize {
    s.split_whitespace().count()
}

fn count_sentences(s: &str) -> usize {
    s.matches(&['.', '!', '?'][..]).count()
}

fn count_substring(s: &str, pattern: &str) -> usize {
    s.matches(pattern).count()
}

fn count_char(s: &str, ch: char) -> usize {
    s.chars().filter(|&c| c == ch).count()
}

fn simple_encode(s: &str, shift: u8) -> String {
    s.chars()
        .map(|c| {
            if c.is_ascii_alphabetic() {
                let base = if c.is_ascii_lowercase() { b'a' } else { b'A' };
                let shifted = ((c as u8 - base + shift) % 26) + base;
                shifted as char
            } else {
                c
            }
        })
        .collect()
}

fn simple_decode(s: &str, shift: u8) -> String {
    simple_encode(s, 26 - shift)
}

fn is_valid_email(email: &str) -> bool {
    email.contains('@') && email.contains('.') && email.len() > 5
}

fn is_valid_phone(phone: &str) -> bool {
    phone.len() >= 10 && phone.chars().any(|c| c.is_numeric())
}

fn is_valid_url(url: &str) -> bool {
    url.starts_with("http://") || url.starts_with("https://")
}

fn capitalize_first_letter(s: &str) -> String {
    let mut chars: Vec<char> = s.chars().collect();
    if !chars.is_empty() {
        chars[0] = chars[0].to_uppercase().next().unwrap_or(chars[0]);
    }
    chars.into_iter().collect()
}

struct StringStats {
    total_chars: usize,
    letters: usize,
    digits: usize,
    spaces: usize,
    punctuation: usize,
    words: usize,
    uppercase: usize,
    lowercase: usize,
}

fn analyze_string(s: &str) -> StringStats {
    let total_chars = s.len();
    let letters = s.chars().filter(|c| c.is_alphabetic()).count();
    let digits = s.chars().filter(|c| c.is_numeric()).count();
    let spaces = s.chars().filter(|c| c.is_whitespace()).count();
    let punctuation = s.chars().filter(|c| c.is_ascii_punctuation()).count();
    let words = s.split_whitespace().count();
    let uppercase = s.chars().filter(|c| c.is_uppercase()).count();
    let lowercase = s.chars().filter(|c| c.is_lowercase()).count();
    
    StringStats {
        total_chars,
        letters,
        digits,
        spaces,
        punctuation,
        words,
        uppercase,
        lowercase,
    }
}
