// 7. Arrays - declaration, access, modification (Rust version)
// This file demonstrates all array operations in Rust for comparison with Dolet

fn main() {
    println!("=== Arrays Demo (Rust) ===");
    println!();

    // Integer array declaration and access
    let mut numbers = [1, 2, 3, 4, 5];
    println!("Integer array: [1, 2, 3, 4, 5]");
    println!("First element: {}", numbers[0]);
    println!("Second element: {}", numbers[1]);
    println!("Last element: {}", numbers[4]);
    println!();

    // String array declaration and access
    let mut names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"];
    println!("String array: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>']");
    println!("First name: {}", names[0]);
    println!("Second name: {}", names[1]);
    println!("Third name: {}", names[2]);
    println!("Fourth name: {}", names[3]);
    println!();

    // Array modification
    println!("Array modification:");
    println!("Original numbers[0]: {}", numbers[0]);
    numbers[0] = 10;
    println!("Modified numbers[0]: {}", numbers[0]);

    println!("Original names[1]: {}", names[1]);
    names[1] = "Layla";
    println!("Modified names[1]: {}", names[1]);
    println!();

    // Float array
    let prices = [19.99, 25.50, 12.75, 8.25];
    println!("Float array: [19.99, 25.50, 12.75, 8.25]");
    println!("Price 1: {}", prices[0]);
    println!("Price 2: {}", prices[1]);
    println!("Price 3: {}", prices[2]);
    println!("Price 4: {}", prices[3]);
    println!();

    // Array iteration with for loop
    println!("Array iteration with for loop:");
    println!("Iterating through numbers array:");
    for (i, value) in numbers.iter().enumerate() {
        println!("numbers[{}] = {}", i, value);
    }
    println!();

    println!("Iterating through names array:");
    for (i, value) in names.iter().enumerate() {
        println!("names[{}] = {}", i, value);
    }
    println!();

    // Array information
    println!("Array information:");
    print_array_info("numbers", numbers.len());
    print_array_info("names", names.len());
    print_array_info("prices", prices.len());
    println!();

    // Array calculations
    println!("Array calculations:");
    let mut sum = 0;
    for (i, value) in numbers.iter().enumerate() {
        sum += value;
        println!("Adding numbers[{}] = {}, sum = {}", i, value, sum);
    }
    println!("Total sum: {}", sum);
    println!();

    // Finding maximum in array
    println!("Finding maximum in array:");
    let mut max_value = numbers[0];
    for value in &numbers[1..] {
        if *value > max_value {
            max_value = *value;
            println!("New maximum found: {}", max_value);
        }
    }
    println!("Final maximum: {}", max_value);
    println!();

    // Array search
    println!("Array search (looking for value 3):");
    let search_value = 3;
    let mut found = false;
    let mut found_index = None;
    
    for (i, value) in numbers.iter().enumerate() {
        if *value == search_value {
            found = true;
            found_index = Some(i);
            println!("Found {} at index {}", search_value, i);
            break;
        }
    }

    if found {
        println!("Search successful!");
    } else {
        println!("Value not found");
    }
    println!();

    // Boolean array
    let flags = [true, false, true, false, true];
    println!("Boolean array: [true, false, true, false, true]");
    for (i, value) in flags.iter().enumerate() {
        println!("flags[{}] = {}", i, value);
    }
    println!();

    // Array copying
    println!("Array copying:");
    let original = [1, 2, 3];
    let mut copy = [0; 3];
    println!("Original: [1, 2, 3]");
    println!("Copy before: [0, 0, 0]");

    copy.copy_from_slice(&original);

    println!("Copy after:");
    for (i, value) in copy.iter().enumerate() {
        println!("copy[{}] = {}", i, value);
    }
    println!();

    // Array reversal
    println!("Array reversal:");
    let mut to_reverse = [1, 2, 3, 4, 5];
    println!("Original array:");
    for (i, value) in to_reverse.iter().enumerate() {
        println!("to_reverse[{}] = {}", i, value);
    }

    to_reverse.reverse();

    println!("After reversal:");
    for (i, value) in to_reverse.iter().enumerate() {
        println!("to_reverse[{}] = {}", i, value);
    }
    println!();

    // Array sorting
    println!("Array sorting:");
    let mut to_sort = [64, 34, 25, 12, 22];
    println!("Original array:");
    for (i, value) in to_sort.iter().enumerate() {
        println!("to_sort[{}] = {}", i, value);
    }

    to_sort.sort();

    println!("Sorted array:");
    for (i, value) in to_sort.iter().enumerate() {
        println!("to_sort[{}] = {}", i, value);
    }
    println!();

    // Array filtering (using Vec for dynamic sizing)
    println!("Array filtering (even numbers):");
    let source = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    println!("Source array: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]");
    
    let evens: Vec<i32> = source.iter().filter(|&&x| x % 2 == 0).cloned().collect();
    
    println!("Even numbers:");
    for (i, value) in evens.iter().enumerate() {
        println!("evens[{}] = {}", i, value);
    }
    println!();

    // Array transformation
    println!("Array transformation (doubling values):");
    let original_values = [1, 2, 3, 4, 5];
    println!("Original values:");
    for (i, value) in original_values.iter().enumerate() {
        println!("original_values[{}] = {}", i, value);
    }

    let doubled: Vec<i32> = original_values.iter().map(|x| x * 2).collect();

    println!("Doubled values:");
    for (i, value) in doubled.iter().enumerate() {
        println!("doubled[{}] = {}", i, value);
    }
    println!();

    // Array comparison
    println!("Array comparison:");
    let array1 = [1, 2, 3, 4, 5];
    let array2 = [1, 2, 3, 4, 5];
    let array3 = [1, 2, 3, 4, 6];

    println!("array1 equals array2: {}", array1 == array2);
    println!("array1 equals array3: {}", array1 == array3);
    println!();

    // Array statistics
    println!("Array statistics:");
    let data = [85, 92, 78, 96, 88, 73, 89, 94];
    let data_sum: i32 = data.iter().sum();
    let data_average = data_sum / data.len() as i32;
    let data_min = *data.iter().min().unwrap();
    let data_max = *data.iter().max().unwrap();

    println!("Data: {:?}", data);
    println!("Sum: {}", data_sum);
    println!("Average: {}", data_average);
    println!("Minimum: {}", data_min);
    println!("Maximum: {}", data_max);
    println!("Range: {}", data_max - data_min);
    println!();

    println!("=== End of Arrays Demo ===");
}

fn print_array_info(arr_name: &str, size: usize) {
    println!("Array '{}' has {} elements", arr_name, size);
}
