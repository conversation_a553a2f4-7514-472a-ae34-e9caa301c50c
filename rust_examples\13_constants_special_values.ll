; ModuleID = '13_constants_special_values.efcf4a2701fc3eed-cgu.0'
source_filename = "13_constants_special_values.efcf4a2701fc3eed-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
%"core::fmt::rt::Placeholder" = type { %"core::fmt::rt::Count", %"core::fmt::rt::Count", i64, i32, [1 x i32] }
%"core::fmt::rt::Count" = type { i16, [7 x i16] }

@alloc_f81c2c1bd0b99ce84e80a9469274ae18 = private unnamed_addr constant [91 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\adapters\\enumerate.rs", align 1
@alloc_67987f2acd65c96a8c883ff2ecae97af = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f81c2c1bd0b99ce84e80a9469274ae18, [16 x i8] c"[\00\00\00\00\00\00\001\00\00\00\09\00\00\00" }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0d8c91870ec03b69E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hec99c300eae038e9E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hec99c300eae038e9E" }>, align 8
@anon.44654cb6d92dd0161721fca6bf3bcb7a.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_ddfafc16d9537004fabc45527b35b0cf = private unnamed_addr constant [49 x i8] c"=== Constants and Special Values Demo (Rust) ===\0A", align 1
@alloc_b8d55bc849a6a33d53dac688cf4d3c06 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ddfafc16d9537004fabc45527b35b0cf, [8 x i8] c"1\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_bb842493a6f1fbc6d9988b0d3fe7cccc = private unnamed_addr constant [18 x i8] c"Global constants:\0A", align 1
@alloc_07843771c0250c901fa0d11390d35f2b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_bb842493a6f1fbc6d9988b0d3fe7cccc, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_f8689ecea798906b42826bb02266ec5c = private unnamed_addr constant [5 x i8] c"PI = ", align 1
@alloc_1c6678482269421fd6d03f1590d7fbf5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f8689ecea798906b42826bb02266ec5c, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0a8fc1f3882cff48f8c6b0974f2edfb2 = private unnamed_addr constant [8 x i8] c"\EA.DT\FB!\09@", align 8
@alloc_f982206087ae4045bd853b778cec445d = private unnamed_addr constant [12 x i8] c"MAX_USERS = ", align 1
@alloc_0fe0909a7db5f8e6026eeada27b6f263 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f982206087ae4045bd853b778cec445d, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_03c996aabbc9ab62051e986fb92cba9c = private unnamed_addr constant [4 x i8] c"\E8\03\00\00", align 4
@alloc_1a70497d0b7fab210a80e5075146656d = private unnamed_addr constant [11 x i8] c"APP_NAME = ", align 1
@alloc_421f615f12d1b4228495ec26aaaf3a3b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1a70497d0b7fab210a80e5075146656d, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c7ef0c6937c0cb7161f459ced493cc50 = private unnamed_addr constant [13 x i8] c"Rust Demo App", align 1
@alloc_5f87537df43b8aac634c07eb42895206 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c7ef0c6937c0cb7161f459ced493cc50, [8 x i8] c"\0D\00\00\00\00\00\00\00" }>, align 8
@alloc_0507fc209560a8334887001165cead7f = private unnamed_addr constant [10 x i8] c"VERSION = ", align 1
@alloc_fffd3565fa5898bf951510e8d42b4413 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0507fc209560a8334887001165cead7f, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_80e90391465d7c52efc83fcf07c59e63 = private unnamed_addr constant [5 x i8] c"1.0.0", align 1
@alloc_da92151caaeb84c5c1f76b28dacc1473 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_80e90391465d7c52efc83fcf07c59e63, [8 x i8] c"\05\00\00\00\00\00\00\00" }>, align 8
@alloc_d5a30b479e3e3ef387a80df2e2e95b62 = private unnamed_addr constant [24 x i8] c"Mathematical constants:\0A", align 1
@alloc_0e180ec3c8f453ded99202ba432176d8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d5a30b479e3e3ef387a80df2e2e95b62, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_9428dfbeea6535217ef7e81bdfcb9e71 = private unnamed_addr constant [10 x i8] c"\CF\80 (PI) = ", align 1
@alloc_3bf5c39e8f0101c26de409f7eff5cfc2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9428dfbeea6535217ef7e81bdfcb9e71, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_11115e3051de94f9949805ff4b747bff = private unnamed_addr constant [21 x i8] c"e (Euler's number) = ", align 1
@alloc_8833b243aff017acc5586bbceed83924 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_11115e3051de94f9949805ff4b747bff, [8 x i8] c"\15\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_97141a5f2da28e32746b2e87e7a7f02b = private unnamed_addr constant [8 x i8] c"iW\14\8B\0A\BF\05@", align 8
@alloc_80c12f784cce3d28ecb63c3c7d88c7a3 = private unnamed_addr constant [7 x i8] c"\E2\88\9A2 = ", align 1
@alloc_34ab63eff4a60651a28bb4e8ffca4db2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_80c12f784cce3d28ecb63c3c7d88c7a3, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_097e2602a9650474e7c1161152ec752d = private unnamed_addr constant [8 x i8] c"\CD;\7Ff\9E\A0\F6?", align 8
@alloc_3b65460bf42ffcb795913fba77e80888 = private unnamed_addr constant [8 x i8] c"ln(2) = ", align 1
@alloc_8cf9ed33f9adcff0cf5c95113952c4ed = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3b65460bf42ffcb795913fba77e80888, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a6f9eb080cec2b391d6e8a8179f5772a = private unnamed_addr constant [8 x i8] c"\EF9\FA\FEB.\E6?", align 8
@alloc_dc62f2a0ccba17a894c4128a308f23d9 = private unnamed_addr constant [9 x i8] c"ln(10) = ", align 1
@alloc_35b30c57354b60f1fee559c306f28469 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_dc62f2a0ccba17a894c4128a308f23d9, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4de7c80f515ccd653a5d590e6d349f5f = private unnamed_addr constant [8 x i8] c"\16U\B5\BB\B1k\02@", align 8
@alloc_70d2fae24f6d11f138722baf86e1e88c = private unnamed_addr constant [19 x i8] c"Boolean constants:\0A", align 1
@alloc_12f7dc37d00775fc0f7b3525bb894284 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_70d2fae24f6d11f138722baf86e1e88c, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_5ef5da735d0977508340a0ca781dab80 = private unnamed_addr constant [12 x i8] c"IS_ACTIVE = ", align 1
@alloc_f7827304264a57248e0c662b2bb5eead = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5ef5da735d0977508340a0ca781dab80, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8821998f047ca62cad40e6bc4e4d87c4 = private unnamed_addr constant [1 x i8] c"\01", align 1
@alloc_12a57c6d32c04d56ad515af23e03de6c = private unnamed_addr constant [11 x i8] c"IS_DEBUG = ", align 1
@alloc_63695d543b4971803e1701759578d116 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_12a57c6d32c04d56ad515af23e03de6c, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_914b2c69d7eca30497b9feaf15ac92f1 = private unnamed_addr constant [1 x i8] zeroinitializer, align 1
@alloc_3d5a064744ac3a8619809c6cc62e965a = private unnamed_addr constant [19 x i8] c"Numeric constants:\0A", align 1
@alloc_7ce2b533d7b36b7eff5a1164f8d9cdf1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3d5a064744ac3a8619809c6cc62e965a, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_bc66f5d83e1e48617f22184e3ed3e901 = private unnamed_addr constant [7 x i8] c"ZERO = ", align 1
@alloc_4679ae6520501df1335d69e22d43c97e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bc66f5d83e1e48617f22184e3ed3e901, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0 = private unnamed_addr constant [4 x i8] zeroinitializer, align 4
@alloc_fe984b137340efecda5afa190407f167 = private unnamed_addr constant [6 x i8] c"ONE = ", align 1
@alloc_38b6fd4add01f3813f81b4de5c32fcff = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_fe984b137340efecda5afa190407f167, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_66e0d902dc5512250b9d4edb026cbbeb = private unnamed_addr constant [4 x i8] c"\01\00\00\00", align 4
@alloc_d2b6310ff6a80ded9de34d09d7048310 = private unnamed_addr constant [15 x i8] c"NEGATIVE_ONE = ", align 1
@alloc_d505b210a6837045e27d198653b9bc23 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d2b6310ff6a80ded9de34d09d7048310, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_47c8330ffe7a09bc00d165d03facdeb6 = private unnamed_addr constant [4 x i8] c"\FF\FF\FF\FF", align 4
@alloc_66f1c8302efa9a24f214b3b5492776f3 = private unnamed_addr constant [12 x i8] c"MAX_SCORE = ", align 1
@alloc_50c952892abd07a35022b35dccf7d8a3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_66f1c8302efa9a24f214b3b5492776f3, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_af995923a0f2cfedcdb19bb6362b5829 = private unnamed_addr constant [4 x i8] c"d\00\00\00", align 4
@alloc_ad3fabe2baf024573b0a2b2606e13165 = private unnamed_addr constant [12 x i8] c"MIN_SCORE = ", align 1
@alloc_5a21eae34823e3b7852dc4fb9ea1485e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ad3fabe2baf024573b0a2b2606e13165, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e15596352db5233098a1b2be544bcacd = private unnamed_addr constant [18 x i8] c"String constants:\0A", align 1
@alloc_ad360cc943354615ff7c5ccd5734d26e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e15596352db5233098a1b2be544bcacd, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_30531f10c1b4a1ef35a86270d1b6fbf6 = private unnamed_addr constant [16 x i8] c"EMPTY_STRING = '", align 1
@alloc_12a9d76f5dbcbafc68e14c1df740ed24 = private unnamed_addr constant [2 x i8] c"'\0A", align 1
@alloc_77ad9436238e4853bc231c69186d262b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_30531f10c1b4a1ef35a86270d1b6fbf6, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_eab5d04767146d7d9b93b60d28ef530a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer }>, align 8
@alloc_c255a309e85589e533b67c616c1bcfc8 = private unnamed_addr constant [9 x i8] c"SPACE = '", align 1
@alloc_7d80103df6071399fd41b805e0952740 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c255a309e85589e533b67c616c1bcfc8, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_1fae48e79ef589f304d5eacb66de4239 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0974058db2022fe7550605da9cb9d2c0 = private unnamed_addr constant [12 x i8] c"GREETING = '", align 1
@alloc_3d4efc5076f4bdbd0d3f07b1747474da = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0974058db2022fe7550605da9cb9d2c0, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_7099dfc5bf30ed4602dad14f59e74cfe = private unnamed_addr constant [13 x i8] c"Hello, World!", align 1
@alloc_0f2021b59b47cc3bcb9eda22808aa158 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7099dfc5bf30ed4602dad14f59e74cfe, [8 x i8] c"\0D\00\00\00\00\00\00\00" }>, align 8
@alloc_2df051669820269f7971a756f4a36bd5 = private unnamed_addr constant [24 x i8] c"String with TAB: 'Before", align 1
@alloc_7c670937a6e836ec64de2cd64a18ecab = private unnamed_addr constant [7 x i8] c"After'\0A", align 1
@alloc_27dba32f246936f7ba8808d83fa2e62b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2df051669820269f7971a756f4a36bd5, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_7c670937a6e836ec64de2cd64a18ecab, [8 x i8] c"\07\00\00\00\00\00\00\00" }>, align 8
@alloc_fbc3c4d1c945512837ab310c7a740873 = private unnamed_addr constant [1 x i8] c"\09", align 1
@alloc_19a1846de6c3fc15a5d237e93f872958 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fbc3c4d1c945512837ab310c7a740873, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9c2031c5f702103d4a1c151dac14b7f0 = private unnamed_addr constant [17 x i8] c"Array constants:\0A", align 1
@alloc_2fed207b42d30098c4574ea6ed29229e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9c2031c5f702103d4a1c151dac14b7f0, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_395289c38b7079a2d74bb6b55b34e811 = private unnamed_addr constant [15 x i8] c"DAYS_IN_WEEK = ", align 1
@alloc_ee98fb8e3a9f311326b095a44a228ccd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_395289c38b7079a2d74bb6b55b34e811, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_fc18870ebd3bffa18e161a5a63775070 = private unnamed_addr constant [4 x i8] c"\07\00\00\00", align 4
@alloc_1fdcc531b2aa74e0a46c8367979b945a = private unnamed_addr constant [17 x i8] c"MONTHS_IN_YEAR = ", align 1
@alloc_20580277cc3463853cb4d0cdf316ee6e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1fdcc531b2aa74e0a46c8367979b945a, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_63738555cfef41d17c63452561b7eb27 = private unnamed_addr constant [4 x i8] c"\0C\00\00\00", align 4
@alloc_8ac0727f67eef6d32551acc063b0f18a = private unnamed_addr constant [15 x i8] c"HOURS_IN_DAY = ", align 1
@alloc_ca26fdaa79b02cc7cd13e966384b55a2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8ac0727f67eef6d32551acc063b0f18a, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e7ecca184d1801ae8ba4577ecc1a7822 = private unnamed_addr constant [4 x i8] c"\18\00\00\00", align 4
@alloc_4c07eb6c079ae1d35629ec286d32e3f4 = private unnamed_addr constant [18 x i8] c"MINUTES_IN_HOUR = ", align 1
@alloc_80e3df80c9d9d79364fad22329cc6dbd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4c07eb6c079ae1d35629ec286d32e3f4, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9348a6d0135e4bb8777d851374adfb84 = private unnamed_addr constant [4 x i8] c"<\00\00\00", align 4
@alloc_4ccd6c873d131b5b20a96bd1ee96fd7b = private unnamed_addr constant [20 x i8] c"SECONDS_IN_MINUTE = ", align 1
@alloc_9f1409cd3e102279004b638d4e6233cb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4ccd6c873d131b5b20a96bd1ee96fd7b, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4d2b389b0d8a0dda31e81d910a416298 = private unnamed_addr constant [24 x i8] c"Special numeric values:\0A", align 1
@alloc_0dd91a4c7fd9c03fc68d1ebd68710ebe = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4d2b389b0d8a0dda31e81d910a416298, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_e3f802bf4b85d899b39ccaee2c42e865 = private unnamed_addr constant [19 x i8] c"Positive infinity: ", align 1
@alloc_c749254c68d6cda80c1b52a96ea13e5f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e3f802bf4b85d899b39ccaee2c42e865, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_fa5717932054f126c01f920a6fadedb3 = private unnamed_addr constant [19 x i8] c"Negative infinity: ", align 1
@alloc_3c988b7cb6d568e088772895f7d00d4a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_fa5717932054f126c01f920a6fadedb3, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9ac33d86323c6b03432c2f5ab408da4f = private unnamed_addr constant [20 x i8] c"Not a number (NaN): ", align 1
@alloc_7a69bc728950ddba0adabc30bc778f1a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9ac33d86323c6b03432c2f5ab408da4f, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9b45659334be2a7393184ad4f5d44a88 = private unnamed_addr constant [21 x i8] c"Is NaN actually NaN? ", align 1
@alloc_8077090583980922edf56c55b2eb164d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9b45659334be2a7393184ad4f5d44a88, [8 x i8] c"\15\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_18e0225ad8cf0b66953e3ba6a9089605 = private unnamed_addr constant [16 x i8] c"Integer limits:\0A", align 1
@alloc_e99af7a4e8842ccdb4dc3f9ef7d89914 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_18e0225ad8cf0b66953e3ba6a9089605, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_d2c0bd821a0ec3d206d23e167d6e2fd8 = private unnamed_addr constant [9 x i8] c"i32 MIN: ", align 1
@alloc_2c72478184ed55adb3b435d705d264d9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d2c0bd821a0ec3d206d23e167d6e2fd8, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c8f6fdd84450dae01461435b8eda5097 = private unnamed_addr constant [4 x i8] c"\00\00\00\80", align 4
@alloc_aa267b12cc2da3d6732debc1e2f0bc3a = private unnamed_addr constant [9 x i8] c"i32 MAX: ", align 1
@alloc_8567b72d512fca41c8cb3aed588686cd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aa267b12cc2da3d6732debc1e2f0bc3a, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4afdb120a236013e0c29be80b15d8aee = private unnamed_addr constant [4 x i8] c"\FF\FF\FF\7F", align 4
@alloc_97e75422adf66479eb68fe0aa872d589 = private unnamed_addr constant [9 x i8] c"u32 MIN: ", align 1
@alloc_29e9434f39a2e46aa70bd5ef85d89397 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_97e75422adf66479eb68fe0aa872d589, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_bcd26a6d1cb6c9d786073b5620763200 = private unnamed_addr constant [9 x i8] c"u32 MAX: ", align 1
@alloc_710be66bee20a51084dea6cd476ca5fa = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bcd26a6d1cb6c9d786073b5620763200, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_98268e8e016f5120cd1f45368aade58b = private unnamed_addr constant [14 x i8] c"Float limits:\0A", align 1
@alloc_f06c6d6b15b079ba3278f67db350802c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_98268e8e016f5120cd1f45368aade58b, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_ec19281d11547b29861b80d5cc30a835 = private unnamed_addr constant [9 x i8] c"f64 MIN: ", align 1
@alloc_99f210fab252da2abf653ae25044b8dc = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ec19281d11547b29861b80d5cc30a835, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7a86e1999471ac922481c120e60756f8 = private unnamed_addr constant [8 x i8] c"\FF\FF\FF\FF\FF\FF\EF\FF", align 8
@alloc_321280a015cd43bffd9559e2ebf888c0 = private unnamed_addr constant [9 x i8] c"f64 MAX: ", align 1
@alloc_0566dbe9b4b0958b93a966e348d1739b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_321280a015cd43bffd9559e2ebf888c0, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_86fe2e14e8aee3cbc4747be45abbb75c = private unnamed_addr constant [8 x i8] c"\FF\FF\FF\FF\FF\FF\EF\7F", align 8
@alloc_beee50f0350f3dae1e426a18190e1248 = private unnamed_addr constant [13 x i8] c"f64 EPSILON: ", align 1
@alloc_d4f845388026782be51d3da3b5a4a69b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_beee50f0350f3dae1e426a18190e1248, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e9e55e98553b87bb6aaa9418d9398720 = private unnamed_addr constant [8 x i8] c"\00\00\00\00\00\00\B0<", align 8
@alloc_f841a66ff312b570733f07e299506b2d = private unnamed_addr constant [33 x i8] c"Using constants in calculations:\0A", align 1
@alloc_ab00968d0b88e8d5880311e810c8507a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f841a66ff312b570733f07e299506b2d, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_2af53c07ade8ebf72291d79f9d1866c8 = private unnamed_addr constant [19 x i8] c"Circle with radius ", align 1
@alloc_2c871cb4eca8f760225510900b094559 = private unnamed_addr constant [2 x i8] c":\0A", align 1
@alloc_45b6e4e458c1f17851a447773c1ae114 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2af53c07ade8ebf72291d79f9d1866c8, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_2c871cb4eca8f760225510900b094559, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_f37b3f1301e27ac3b61cac92875d3d5e = private unnamed_addr constant [19 x i8] c"Area = \CF\80 \C3\97 r\C2\B2 = ", align 1
@alloc_583d7ec95c707b7ad7707bfdce6893bb = private unnamed_addr constant [4 x i8] c" \C3\97 ", align 1
@alloc_dd034cb4c22df66364de3026aaedfce5 = private unnamed_addr constant [5 x i8] c"\C2\B2 = ", align 1
@alloc_4953eb20e1db771248b79fb7b3d8fdce = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f37b3f1301e27ac3b61cac92875d3d5e, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_583d7ec95c707b7ad7707bfdce6893bb, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_dd034cb4c22df66364de3026aaedfce5, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_436db56b30a6be897f83e04658926dd8 = private unnamed_addr constant [32 x i8] c"Circumference = 2\CF\80 \C3\97 r = 2 \C3\97 ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_1a6f814103102aece8549d7c8117a215 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_436db56b30a6be897f83e04658926dd8, [8 x i8] c" \00\00\00\00\00\00\00", ptr @alloc_583d7ec95c707b7ad7707bfdce6893bb, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3785f5331fc28ac57d9ae4768879780b = private unnamed_addr constant [19 x i8] c"Time calculations:\0A", align 1
@alloc_428caaff346f2213e8b72d760c386123 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3785f5331fc28ac57d9ae4768879780b, [8 x i8] c"\13\00\00\00\00\00\00\00" }>, align 8
@alloc_640fd10a2b437910cacaafb127639f92 = private unnamed_addr constant [30 x i8] c"13_constants_special_values.rs", align 1
@alloc_4a1e53d290dbba4fe349169ecd8aaf28 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00~\00\00\00!\00\00\00" }>, align 8
@alloc_11423f29e344a922ef72359cf9d746a0 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00~\00\00\00\11\00\00\00" }>, align 8
@alloc_d1278d77d0fb95a90322dc7d536cd662 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00\7F\00\00\001\00\00\00" }>, align 8
@alloc_0fe6bb1f6f714428f53b2906a274819c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00\7F\00\00\00!\00\00\00" }>, align 8
@alloc_d36cb40d2b5352a8d1fc115064552fdf = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00\80\00\00\00\13\00\00\00" }>, align 8
@alloc_75ebd049d9dd536b2a50c2f29e529bae = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00\81\00\00\00\13\00\00\00" }>, align 8
@alloc_487cb6f6956aacf43781382aefa3ff0a = private unnamed_addr constant [11 x i8] c"Converting ", align 1
@alloc_7ee2ee58ebf1602a2784afc78ab60712 = private unnamed_addr constant [10 x i8] c" seconds:\0A", align 1
@alloc_011bc8cbb50683cb46ad66ea4c9fa8ae = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_487cb6f6956aacf43781382aefa3ff0a, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_7ee2ee58ebf1602a2784afc78ab60712, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_952f2a8796d3af26369ecae9c5b88e47 = private unnamed_addr constant [7 x i8] c"Hours: ", align 1
@alloc_1c532211cab671e14ac457bf2c719883 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_952f2a8796d3af26369ecae9c5b88e47, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0cdc32f1e4203f130f851c59f371b814 = private unnamed_addr constant [9 x i8] c"Minutes: ", align 1
@alloc_39fde975601ef2ec9b9907b54530ffb5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0cdc32f1e4203f130f851c59f371b814, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2dd1e77eb2273b419a54cbae9f6cdaae = private unnamed_addr constant [9 x i8] c"Seconds: ", align 1
@alloc_305da04dc7245cbf21e76ef5e88f4a2b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2dd1e77eb2273b419a54cbae9f6cdaae, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f1cc465be68921186d39024eef1b2218 = private unnamed_addr constant [8 x i8] c"Format: ", align 1
@alloc_3b8c95b91c663a5a9387bddd44e7b465 = private unnamed_addr constant [1 x i8] c":", align 1
@alloc_0eed1429722b7ffe76da138fe5290fdf = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f1cc465be68921186d39024eef1b2218, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_3b8c95b91c663a5a9387bddd44e7b465, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_3b8c95b91c663a5a9387bddd44e7b465, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2567c9787ba81ca753e6b65ef5a4fb98 = private unnamed_addr constant [25 x i8] c"Configuration constants:\0A", align 1
@alloc_592f32e35e392548e75ab9188b8382c5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2567c9787ba81ca753e6b65ef5a4fb98, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_8e1ccc4081fe2ea46a85bdce21a5f640 = private unnamed_addr constant [22 x i8] c"Server configuration:\0A", align 1
@alloc_0092bd81a21f49b74bc2425d9ce204fe = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8e1ccc4081fe2ea46a85bdce21a5f640, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_6b171df364b7ff9294f9c916fe710100 = private unnamed_addr constant [8 x i8] c"  Host: ", align 1
@alloc_f970787d7fe41bd84ebd138cc7ebf6c2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6b171df364b7ff9294f9c916fe710100, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c90ec3bf467d50d0ebd3b13492a33757 = private unnamed_addr constant [9 x i8] c"localhost", align 1
@alloc_1329e2565db6d32f3e74e7ab8e4870d5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c90ec3bf467d50d0ebd3b13492a33757, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_faf815f90e16a58522cd011cb41ae1c7 = private unnamed_addr constant [8 x i8] c"  Port: ", align 1
@alloc_58adc591cee224c2f65e482d19982e6d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_faf815f90e16a58522cd011cb41ae1c7, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_10b869d7795ff893f363d37885041c0c = private unnamed_addr constant [2 x i8] c"\90\1F", align 2
@alloc_82729a4841d98663f6229b9955fe6eb2 = private unnamed_addr constant [19 x i8] c"  Max connections: ", align 1
@alloc_e6b4443d34cef5fd5d598a0b0e23539d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_82729a4841d98663f6229b9955fe6eb2, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5c1706a91efd66c84b75f836cf1f4a9d = private unnamed_addr constant [11 x i8] c"  Timeout: ", align 1
@alloc_41bac06a7674978c8867bab93af34022 = private unnamed_addr constant [9 x i8] c" seconds\0A", align 1
@alloc_878a508c2d9091aa8492f82a294f536a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5c1706a91efd66c84b75f836cf1f4a9d, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_41bac06a7674978c8867bab93af34022, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_0e3505805f4905816c1bd644811a7869 = private unnamed_addr constant [4 x i8] c"\1E\00\00\00", align 4
@alloc_a5b376b396f4e0783298f30a7b5fd189 = private unnamed_addr constant [15 x i8] c"  Buffer size: ", align 1
@alloc_c9586a14ffc4d74325207a9b18d4ba17 = private unnamed_addr constant [7 x i8] c" bytes\0A", align 1
@alloc_0523402689d58e9f1b54fa554728f1c2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a5b376b396f4e0783298f30a7b5fd189, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_c9586a14ffc4d74325207a9b18d4ba17, [8 x i8] c"\07\00\00\00\00\00\00\00" }>, align 8
@alloc_8da287e0d9a7604047d53e42ae527590 = private unnamed_addr constant [8 x i8] c"\00\04\00\00\00\00\00\00", align 8
@alloc_29242712bd528d515e6c11a4b5f68e22 = private unnamed_addr constant [18 x i8] c"Status constants:\0A", align 1
@alloc_26a5f1cccb2063574c45746f28e26d3e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_29242712bd528d515e6c11a4b5f68e22, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_bfaeabfbe9f2ad920ba3829446ecb21b = private unnamed_addr constant [12 x i8] c"Status: OK (", align 1
@alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b = private unnamed_addr constant [2 x i8] c")\0A", align 1
@alloc_9302a4cff701b26f53f764cc9c54189c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_bfaeabfbe9f2ad920ba3829446ecb21b, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_9e25808f472fc2b820d660613b73af30 = private unnamed_addr constant [4 x i8] c"\C8\00\00\00", align 4
@alloc_0dbc9a61c51dc231e160875f3d8bc0e5 = private unnamed_addr constant [17 x i8] c"Color constants:\0A", align 1
@alloc_9f41fe8245510942a85a5f1602127ed8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0dbc9a61c51dc231e160875f3d8bc0e5, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_3a68242da27dcf155cc138f0985de237 = private unnamed_addr constant [10 x i8] c"RED = RGB(", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_a069eb5353b3aea82e6fab5147fa5cca = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3a68242da27dcf155cc138f0985de237, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_45fce9df1beaeebc2e036763a2b33fd8 = private unnamed_addr constant [3 x i8] c"\FF\00\00", align 1
@alloc_496b7798290ca2db19e24a991d819544 = private unnamed_addr constant [12 x i8] c"GREEN = RGB(", align 1
@alloc_65fb4bd123f01a27a3b1f1c3e7a49663 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_496b7798290ca2db19e24a991d819544, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_925e40e5a12fd6a49d3ce2c1801a7aa7 = private unnamed_addr constant [3 x i8] c"\00\FF\00", align 1
@alloc_2a9c185059e8677504cb59b33bea497b = private unnamed_addr constant [11 x i8] c"BLUE = RGB(", align 1
@alloc_e825019b9d00916699241fdc37c2f854 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2a9c185059e8677504cb59b33bea497b, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_68b1f460579f7a37d63efd3d48ac0953 = private unnamed_addr constant [3 x i8] c"\00\00\FF", align 1
@alloc_e35206809278a75a38390df16ab755c9 = private unnamed_addr constant [12 x i8] c"WHITE = RGB(", align 1
@alloc_dfe5cb7c4d235d4244d87a879e4f0f8a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e35206809278a75a38390df16ab755c9, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_d12b77d5e35485f63b33d884d82bf500 = private unnamed_addr constant [3 x i8] c"\FF\FF\FF", align 1
@alloc_4ab160d3c5c5037764f966a87319f1a8 = private unnamed_addr constant [12 x i8] c"BLACK = RGB(", align 1
@alloc_60955c5d06c988d7d8f3741375eb0d19 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4ab160d3c5c5037764f966a87319f1a8, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_655dbaa176364e4aa81017fd797799fb = private unnamed_addr constant [3 x i8] zeroinitializer, align 1
@alloc_5c81780ea805b90e4bd35a4da74d80f3 = private unnamed_addr constant [23 x i8] c"File system constants:\0A", align 1
@alloc_db39dd8aa9a3b1d52760e91a48735ddd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5c81780ea805b90e4bd35a4da74d80f3, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_ab5be7d521fbd4e38fffa4606dd6dc1b = private unnamed_addr constant [22 x i8] c"MAX_FILENAME_LENGTH = ", align 1
@alloc_71194e1f6e658fabe10f217f2d3a8628 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ab5be7d521fbd4e38fffa4606dd6dc1b, [8 x i8] c"\16\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_396d2afaa81902be59ec9eb1c744d98a = private unnamed_addr constant [8 x i8] c"\FF\00\00\00\00\00\00\00", align 8
@alloc_1228df1a3ea4af8d4037926fc6a183fb = private unnamed_addr constant [18 x i8] c"MAX_PATH_LENGTH = ", align 1
@alloc_7ff366ce9a7c4851d7fc35fa09d34949 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1228df1a3ea4af8d4037926fc6a183fb, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f53e7b75db1583e44fb3acdf703196c9 = private unnamed_addr constant [8 x i8] c"\00\10\00\00\00\00\00\00", align 8
@alloc_ffa058b8a7ab6b4daa142f1f8eab1a54 = private unnamed_addr constant [27 x i8] c"DEFAULT_FILE_PERMISSIONS = ", align 1
@alloc_aad384c730c022174acc3a8ab5434cf7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ffa058b8a7ab6b4daa142f1f8eab1a54, [8 x i8] c"\1B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c8093cc7c4b69cf6e8b5785d79462716 = private unnamed_addr constant [4 x i8] c"\A4\01\00\00", align 4
@alloc_b23a5102281e3dc9256d9237cbeed939 = private unnamed_addr constant [27 x i8] c"Using constants in arrays:\0A", align 1
@alloc_4af9e4151ea00e06c0d4468af073ce91 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b23a5102281e3dc9256d9237cbeed939, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_fb309e7aab750c01f9e55b8f43d774e0 = private unnamed_addr constant [7 x i8] c"Tuesday", align 1
@alloc_e3976b92482fb4550470b093759847a6 = private unnamed_addr constant [9 x i8] c"Wednesday", align 1
@alloc_edb650fdf204f96ecf65cf6a36209271 = private unnamed_addr constant [8 x i8] c"Thursday", align 1
@alloc_bf1b3fef127dec54993dc7b162dd88f0 = private unnamed_addr constant [6 x i8] c"Friday", align 1
@alloc_c5c4869492c5f7d7d8eb50adae4cd7f1 = private unnamed_addr constant [8 x i8] c"Saturday", align 1
@alloc_b482e652b9e23eeebfff6ad2b8bde4ae = private unnamed_addr constant [6 x i8] c"Sunday", align 1
@alloc_fc226fb9a476ea06f815767c1b7d7136 = private unnamed_addr constant [6 x i8] c"Monday", align 1
@alloc_70e62007b12f6eb479711b3045e7514c = private unnamed_addr constant [10 x i8] c"There are ", align 1
@alloc_d0955bc76e44ecaa461d42a4ee711945 = private unnamed_addr constant [17 x i8] c" days in a week:\0A", align 1
@alloc_29fb70a501d7929dba9d82cfe25d54d6 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_70e62007b12f6eb479711b3045e7514c, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_d0955bc76e44ecaa461d42a4ee711945, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@alloc_2bed7856f0dd0328de8fd44ba5f65103 = private unnamed_addr constant [28 x i8] c"Validation using constants:\0A", align 1
@alloc_86e81aef664bda8d0a6207ac93cbeaf3 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2bed7856f0dd0328de8fd44ba5f65103, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_f98a07556c052c2f5245e1f8d87ad9e9 = private unnamed_addr constant [6 x i8] c"Score ", align 1
@alloc_4e3e63e5842ae14cb9b36fc7aa1794a5 = private unnamed_addr constant [20 x i8] c" is invalid (range: ", align 1
@alloc_3bb2650aa074fcfb8c10a9c40791bfbc = private unnamed_addr constant [1 x i8] c"-", align 1
@alloc_7f0207a35788b66e2b4ba8b58c4136ef = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f98a07556c052c2f5245e1f8d87ad9e9, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_4e3e63e5842ae14cb9b36fc7aa1794a5, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_3bb2650aa074fcfb8c10a9c40791bfbc, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_df78d298e30076bc4e8157b1afc919e8 = private unnamed_addr constant [18 x i8] c" is valid (range: ", align 1
@alloc_ff43b11a9a015a9e079d1ce771d8b057 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f98a07556c052c2f5245e1f8d87ad9e9, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_df78d298e30076bc4e8157b1afc919e8, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_3bb2650aa074fcfb8c10a9c40791bfbc, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_d45c1a5da483c52b50b1aa0a11417818 = private unnamed_addr constant [26 x i8] c"Constants in expressions:\0A", align 1
@alloc_3cf1a08e96b26a26d3e230b2b8df4a99 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d45c1a5da483c52b50b1aa0a11417818, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_a8ddcb241b5dc2935110d10378cf2a2b = private unnamed_addr constant [18 x i8] c"Seconds in a day: ", align 1
@alloc_2ed217bf448babc042e39c7bf471145e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a8ddcb241b5dc2935110d10378cf2a2b, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9c3baa4c7d946dbf786ac3cfdafa8cd9 = private unnamed_addr constant [4 x i8] c"\80Q\01\00", align 4
@alloc_49d271783cfcdc0011e8521c022042ba = private unnamed_addr constant [19 x i8] c"Seconds in a week: ", align 1
@alloc_146feca2cd7bd345354696f9c2ffb6a4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_49d271783cfcdc0011e8521c022042ba, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_dc3e4f124eb94e7c4d273a63286e5e16 = private unnamed_addr constant [4 x i8] c"\80:\09\00", align 4
@alloc_7ca41a828524683b17f5371a2de7c58d = private unnamed_addr constant [49 x i8] c"=== End of Constants and Special Values Demo ===\0A", align 1
@alloc_81698f0a245fb48aef062ccffef12a0d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7ca41a828524683b17f5371a2de7c58d, [8 x i8] c"1\00\00\00\00\00\00\00" }>, align 8
@alloc_935da28a29c1194803db32325bd13ace = private unnamed_addr constant [6 x i8] c"  Day ", align 1
@alloc_556e4180596b5b612bb6ed6c0cbb55e1 = private unnamed_addr constant [2 x i8] c": ", align 1
@alloc_d6ac99c7d7059c2e53fbec18f33d1233 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_935da28a29c1194803db32325bd13ace, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_556e4180596b5b612bb6ed6c0cbb55e1, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_730080e9ed391f604674ad5459e95229 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_640fd10a2b437910cacaafb127639f92, [16 x i8] c"\1E\00\00\00\00\00\00\00\C8\00\00\00\22\00\00\00" }>, align 8

; <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h97cdc9e101a27176E"(ptr align 8 %self) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self1 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h80ab5034ce6b221eE"(ptr align 8 %self)
  store ptr %0, ptr %self1, align 8
  %1 = load ptr, ptr %self1, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_11 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_11 to i1
  br i1 %4, label %bb8, label %bb7

bb8:                                              ; preds = %start
  %v = load ptr, ptr %self1, align 8
  store ptr %v, ptr %_3, align 8
  %val = load ptr, ptr %_3, align 8
  %5 = getelementptr inbounds i8, ptr %self, i64 16
  %i = load i64, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  %7 = load i64, ptr %6, align 8
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %7, i64 1)
  %_8.0 = extractvalue { i64, i1 } %8, 0
  %_8.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_8.1, label %panic, label %bb3

bb7:                                              ; preds = %start
  %9 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr null, ptr %9, align 8
  br label %bb4

bb4:                                              ; preds = %bb3, %bb7
  %10 = load i64, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  %12 = load ptr, ptr %11, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %10, 0
  %14 = insertvalue { i64, ptr } %13, ptr %12, 1
  ret { i64, ptr } %14

bb3:                                              ; preds = %bb8
  %15 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %_8.0, ptr %15, align 8
  store i64 %i, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %val, ptr %16, align 8
  br label %bb4

panic:                                            ; preds = %bb8
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_67987f2acd65c96a8c883ff2ecae97af) #7
          to label %unreachable unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind to caller

funclet_bb5:                                      ; preds = %panic
  %cleanuppad = cleanuppad within none []
  br label %bb5

unreachable:                                      ; preds = %panic
  unreachable

bb2:                                              ; No predecessors!
  unreachable
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hbf49e816df1279d8E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #1 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hec99c300eae038e9E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h05b1e7fc06bd83e5E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h55b7daaeeffe4f32E"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h05b1e7fc06bd83e5E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h45e552b5e540b741E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h25840d89f9aec763E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_3 = load ptr, ptr %self, align 8
; call <&T as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h91f2c3c7392dfa53E"(ptr align 8 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h91f2c3c7392dfa53E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; core::f64::<impl f64>::is_nan
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3f6421_$LT$impl$u20$f64$GT$6is_nan17h06f71d4cc4464519E"(double %self) unnamed_addr #0 {
start:
  %_0 = fcmp une double %self, %self
  ret i1 %_0
}

; core::fmt::rt::Placeholder::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_0, i64 %position, i32 %flags, ptr align 8 %precision, ptr align 8 %width) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 %position, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 40
  store i32 %flags, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %precision, i64 16, i1 false)
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %width, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp51_$LT$impl$u20$core..fmt..Display$u20$for$u20$u8$GT$3fmt17ha43daf509c3f71a2E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h28191d94ec00639fE(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hdf87e42f0895a2dfE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h4a4b25b25e057d19E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h25840d89f9aec763E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h8b4137f3eb9f928cE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h91f2c3c7392dfa53E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hf319fdc427149568E(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hf8083b5ecf2c5c30E(ptr sret([16 x i8]) align 8 %_0, ptr align 2 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u16$GT$3fmt17hf1c2edc43d81176bE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_octal
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_octal17h45e442a1eda29950E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Octal$u20$for$u20$u32$GT$3fmt17hd5615923773e8e87E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::UnsafeArg::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE() unnamed_addr #0 {
start:
  ret void
}

; core::fmt::Arguments::new_v1_formatted
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces.0, i64 %pieces.1, ptr align 8 %args.0, i64 %args.1, ptr align 8 %fmt.0, i64 %fmt.1) unnamed_addr #0 {
start:
  %_5 = alloca [16 x i8], align 8
  store ptr %fmt.0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %fmt.1, ptr %0, align 8
  store ptr %pieces.0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pieces.1, ptr %1, align 8
  %2 = load ptr, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args.0, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  store i64 %args.1, ptr %8, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hb4c5bb4155e0955eE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.44654cb6d92dd0161721fca6bf3bcb7a.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0d8c91870ec03b69E"(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17he61c324bb68c8298E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h45e552b5e540b741E(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17he61c324bb68c8298E(ptr %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hec99c300eae038e9E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h27e12e7cf6a8e817E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  ret void
}

; core::iter::traits::iterator::Iterator::enumerate
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h45e23aabbec32c44E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
  store ptr %self.0, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.1, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %1, align 8
  ret void
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdf3b581bbec498bcE"(ptr align 8 %self.0, i64 %self.1) unnamed_addr #0 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h8b32b5f83cd99dedE"(ptr align 8 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h8b32b5f83cd99dedE"(ptr align 8 %slice.0, i64 %slice.1) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw { ptr, i64 }, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h55b7daaeeffe4f32E"() unnamed_addr #0 {
start:
  ret i32 0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3a36c84724bc4f92E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h80ab5034ce6b221eE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw { ptr, i64 }, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; _13_constants_special_values::main
; Function Attrs: uwtable
define internal void @_ZN28_13_constants_special_values4main17hcbcf42cabfb4e780E() unnamed_addr #1 {
start:
  %_763 = alloca [48 x i8], align 8
  %_760 = alloca [48 x i8], align 8
  %_757 = alloca [16 x i8], align 8
  %_756 = alloca [16 x i8], align 8
  %_753 = alloca [48 x i8], align 8
  %_750 = alloca [16 x i8], align 8
  %_749 = alloca [16 x i8], align 8
  %_746 = alloca [48 x i8], align 8
  %_743 = alloca [48 x i8], align 8
  %_740 = alloca [48 x i8], align 8
  %_737 = alloca [16 x i8], align 8
  %_735 = alloca [16 x i8], align 8
  %_733 = alloca [16 x i8], align 8
  %_732 = alloca [48 x i8], align 8
  %_729 = alloca [48 x i8], align 8
  %_726 = alloca [16 x i8], align 8
  %_724 = alloca [16 x i8], align 8
  %_722 = alloca [16 x i8], align 8
  %_721 = alloca [48 x i8], align 8
  %_718 = alloca [48 x i8], align 8
  %user_score = alloca [4 x i8], align 4
  %_712 = alloca [48 x i8], align 8
  %_709 = alloca [48 x i8], align 8
  %_706 = alloca [16 x i8], align 8
  %_704 = alloca [8 x i8], align 8
  %_702 = alloca [16 x i8], align 8
  %_701 = alloca [32 x i8], align 8
  %_698 = alloca [48 x i8], align 8
  %day = alloca [8 x i8], align 8
  %_692 = alloca [16 x i8], align 8
  %iter = alloca [24 x i8], align 8
  %_687 = alloca [24 x i8], align 8
  %_686 = alloca [24 x i8], align 8
  %_684 = alloca [16 x i8], align 8
  %_683 = alloca [16 x i8], align 8
  %_680 = alloca [48 x i8], align 8
  %weekdays = alloca [112 x i8], align 8
  %_670 = alloca [48 x i8], align 8
  %_667 = alloca [48 x i8], align 8
  %_664 = alloca [16 x i8], align 8
  %_663 = alloca [16 x i8], align 8
  %_660 = alloca [48 x i8], align 8
  %_657 = alloca [16 x i8], align 8
  %_656 = alloca [16 x i8], align 8
  %_653 = alloca [48 x i8], align 8
  %_650 = alloca [16 x i8], align 8
  %_649 = alloca [16 x i8], align 8
  %_646 = alloca [48 x i8], align 8
  %_643 = alloca [48 x i8], align 8
  %_640 = alloca [48 x i8], align 8
  %_637 = alloca [16 x i8], align 8
  %_635 = alloca [16 x i8], align 8
  %_633 = alloca [16 x i8], align 8
  %_632 = alloca [48 x i8], align 8
  %_629 = alloca [48 x i8], align 8
  %_626 = alloca [16 x i8], align 8
  %_624 = alloca [16 x i8], align 8
  %_622 = alloca [16 x i8], align 8
  %_621 = alloca [48 x i8], align 8
  %_618 = alloca [48 x i8], align 8
  %_615 = alloca [16 x i8], align 8
  %_613 = alloca [16 x i8], align 8
  %_611 = alloca [16 x i8], align 8
  %_610 = alloca [48 x i8], align 8
  %_607 = alloca [48 x i8], align 8
  %_604 = alloca [16 x i8], align 8
  %_602 = alloca [16 x i8], align 8
  %_600 = alloca [16 x i8], align 8
  %_599 = alloca [48 x i8], align 8
  %_596 = alloca [48 x i8], align 8
  %_593 = alloca [16 x i8], align 8
  %_591 = alloca [16 x i8], align 8
  %_589 = alloca [16 x i8], align 8
  %_588 = alloca [48 x i8], align 8
  %_585 = alloca [48 x i8], align 8
  %_582 = alloca [48 x i8], align 8
  %_579 = alloca [48 x i8], align 8
  %_555 = alloca [16 x i8], align 8
  %_554 = alloca [16 x i8], align 8
  %_551 = alloca [48 x i8], align 8
  %_547 = alloca [48 x i8], align 8
  %_544 = alloca [48 x i8], align 8
  %_541 = alloca [16 x i8], align 8
  %_540 = alloca [16 x i8], align 8
  %_537 = alloca [48 x i8], align 8
  %_534 = alloca [16 x i8], align 8
  %_533 = alloca [16 x i8], align 8
  %_530 = alloca [48 x i8], align 8
  %_527 = alloca [16 x i8], align 8
  %_526 = alloca [16 x i8], align 8
  %_523 = alloca [48 x i8], align 8
  %_520 = alloca [16 x i8], align 8
  %_519 = alloca [16 x i8], align 8
  %_516 = alloca [48 x i8], align 8
  %_513 = alloca [16 x i8], align 8
  %_512 = alloca [16 x i8], align 8
  %_509 = alloca [48 x i8], align 8
  %_506 = alloca [48 x i8], align 8
  %_503 = alloca [48 x i8], align 8
  %_500 = alloca [48 x i8], align 8
  %_497 = alloca [16 x i8], align 8
  %_495 = alloca [16 x i8], align 8
  %_493 = alloca [16 x i8], align 8
  %_492 = alloca [48 x i8], align 8
  %_489 = alloca [48 x i8], align 8
  %_486 = alloca [16 x i8], align 8
  %_485 = alloca [16 x i8], align 8
  %_482 = alloca [48 x i8], align 8
  %_479 = alloca [16 x i8], align 8
  %_478 = alloca [16 x i8], align 8
  %_475 = alloca [48 x i8], align 8
  %_472 = alloca [16 x i8], align 8
  %_471 = alloca [16 x i8], align 8
  %_468 = alloca [48 x i8], align 8
  %_465 = alloca [16 x i8], align 8
  %_464 = alloca [16 x i8], align 8
  %_461 = alloca [48 x i8], align 8
  %seconds = alloca [4 x i8], align 4
  %minutes = alloca [4 x i8], align 4
  %hours = alloca [4 x i8], align 4
  %total_seconds = alloca [4 x i8], align 4
  %_433 = alloca [48 x i8], align 8
  %_430 = alloca [48 x i8], align 8
  %_427 = alloca [16 x i8], align 8
  %_426 = alloca [16 x i8], align 8
  %_425 = alloca [48 x i8], align 8
  %_424 = alloca [16 x i8], align 8
  %_423 = alloca [16 x i8], align 8
  %_422 = alloca [48 x i8], align 8
  %_421 = alloca [16 x i8], align 8
  %_420 = alloca [16 x i8], align 8
  %_419 = alloca [48 x i8], align 8
  %_418 = alloca [144 x i8], align 8
  %_414 = alloca [16 x i8], align 8
  %_412 = alloca [16 x i8], align 8
  %_410 = alloca [16 x i8], align 8
  %_409 = alloca [48 x i8], align 8
  %_404 = alloca [48 x i8], align 8
  %_401 = alloca [16 x i8], align 8
  %_400 = alloca [16 x i8], align 8
  %_399 = alloca [48 x i8], align 8
  %_398 = alloca [16 x i8], align 8
  %_397 = alloca [16 x i8], align 8
  %_396 = alloca [48 x i8], align 8
  %_395 = alloca [16 x i8], align 8
  %_394 = alloca [16 x i8], align 8
  %_393 = alloca [48 x i8], align 8
  %_392 = alloca [144 x i8], align 8
  %_388 = alloca [16 x i8], align 8
  %_386 = alloca [16 x i8], align 8
  %_384 = alloca [16 x i8], align 8
  %_383 = alloca [48 x i8], align 8
  %_378 = alloca [48 x i8], align 8
  %_375 = alloca [16 x i8], align 8
  %_374 = alloca [16 x i8], align 8
  %_371 = alloca [48 x i8], align 8
  %circumference = alloca [8 x i8], align 8
  %area = alloca [8 x i8], align 8
  %radius = alloca [8 x i8], align 8
  %_363 = alloca [48 x i8], align 8
  %_360 = alloca [48 x i8], align 8
  %_357 = alloca [16 x i8], align 8
  %_356 = alloca [16 x i8], align 8
  %_353 = alloca [48 x i8], align 8
  %_350 = alloca [16 x i8], align 8
  %_349 = alloca [16 x i8], align 8
  %_346 = alloca [48 x i8], align 8
  %_343 = alloca [16 x i8], align 8
  %_342 = alloca [16 x i8], align 8
  %_339 = alloca [48 x i8], align 8
  %_336 = alloca [48 x i8], align 8
  %_333 = alloca [48 x i8], align 8
  %_330 = alloca [16 x i8], align 8
  %_329 = alloca [16 x i8], align 8
  %_326 = alloca [48 x i8], align 8
  %_323 = alloca [16 x i8], align 8
  %_322 = alloca [16 x i8], align 8
  %_319 = alloca [48 x i8], align 8
  %_316 = alloca [16 x i8], align 8
  %_315 = alloca [16 x i8], align 8
  %_312 = alloca [48 x i8], align 8
  %_309 = alloca [16 x i8], align 8
  %_308 = alloca [16 x i8], align 8
  %_305 = alloca [48 x i8], align 8
  %_302 = alloca [48 x i8], align 8
  %_299 = alloca [48 x i8], align 8
  %_297 = alloca [1 x i8], align 1
  %_295 = alloca [16 x i8], align 8
  %_294 = alloca [16 x i8], align 8
  %_291 = alloca [48 x i8], align 8
  %_288 = alloca [16 x i8], align 8
  %_287 = alloca [16 x i8], align 8
  %_284 = alloca [48 x i8], align 8
  %_281 = alloca [16 x i8], align 8
  %_280 = alloca [16 x i8], align 8
  %_277 = alloca [48 x i8], align 8
  %_274 = alloca [16 x i8], align 8
  %_273 = alloca [16 x i8], align 8
  %_270 = alloca [48 x i8], align 8
  %not_a_number = alloca [8 x i8], align 8
  %negative_infinity = alloca [8 x i8], align 8
  %positive_infinity = alloca [8 x i8], align 8
  %_264 = alloca [48 x i8], align 8
  %_261 = alloca [48 x i8], align 8
  %_258 = alloca [16 x i8], align 8
  %_257 = alloca [16 x i8], align 8
  %_254 = alloca [48 x i8], align 8
  %_251 = alloca [16 x i8], align 8
  %_250 = alloca [16 x i8], align 8
  %_247 = alloca [48 x i8], align 8
  %_244 = alloca [16 x i8], align 8
  %_243 = alloca [16 x i8], align 8
  %_240 = alloca [48 x i8], align 8
  %_237 = alloca [16 x i8], align 8
  %_236 = alloca [16 x i8], align 8
  %_233 = alloca [48 x i8], align 8
  %_230 = alloca [16 x i8], align 8
  %_229 = alloca [16 x i8], align 8
  %_226 = alloca [48 x i8], align 8
  %_223 = alloca [48 x i8], align 8
  %_220 = alloca [48 x i8], align 8
  %_217 = alloca [16 x i8], align 8
  %_216 = alloca [16 x i8], align 8
  %_213 = alloca [48 x i8], align 8
  %_210 = alloca [16 x i8], align 8
  %_209 = alloca [16 x i8], align 8
  %_206 = alloca [48 x i8], align 8
  %_203 = alloca [16 x i8], align 8
  %_202 = alloca [16 x i8], align 8
  %_199 = alloca [48 x i8], align 8
  %_196 = alloca [16 x i8], align 8
  %_195 = alloca [16 x i8], align 8
  %_192 = alloca [48 x i8], align 8
  %_189 = alloca [48 x i8], align 8
  %_186 = alloca [48 x i8], align 8
  %_183 = alloca [16 x i8], align 8
  %_182 = alloca [16 x i8], align 8
  %_179 = alloca [48 x i8], align 8
  %_176 = alloca [16 x i8], align 8
  %_175 = alloca [16 x i8], align 8
  %_172 = alloca [48 x i8], align 8
  %_169 = alloca [16 x i8], align 8
  %_168 = alloca [16 x i8], align 8
  %_165 = alloca [48 x i8], align 8
  %_162 = alloca [16 x i8], align 8
  %_161 = alloca [16 x i8], align 8
  %_158 = alloca [48 x i8], align 8
  %_155 = alloca [16 x i8], align 8
  %_154 = alloca [16 x i8], align 8
  %_151 = alloca [48 x i8], align 8
  %_148 = alloca [48 x i8], align 8
  %_145 = alloca [48 x i8], align 8
  %_142 = alloca [16 x i8], align 8
  %_141 = alloca [16 x i8], align 8
  %_138 = alloca [48 x i8], align 8
  %_135 = alloca [16 x i8], align 8
  %_134 = alloca [16 x i8], align 8
  %_131 = alloca [48 x i8], align 8
  %_128 = alloca [48 x i8], align 8
  %_125 = alloca [48 x i8], align 8
  %_122 = alloca [16 x i8], align 8
  %_121 = alloca [16 x i8], align 8
  %_120 = alloca [48 x i8], align 8
  %_119 = alloca [48 x i8], align 8
  %_115 = alloca [16 x i8], align 8
  %_114 = alloca [16 x i8], align 8
  %_109 = alloca [48 x i8], align 8
  %_106 = alloca [16 x i8], align 8
  %_105 = alloca [16 x i8], align 8
  %_104 = alloca [48 x i8], align 8
  %_103 = alloca [48 x i8], align 8
  %_99 = alloca [16 x i8], align 8
  %_98 = alloca [16 x i8], align 8
  %_93 = alloca [48 x i8], align 8
  %_90 = alloca [16 x i8], align 8
  %_89 = alloca [16 x i8], align 8
  %_88 = alloca [48 x i8], align 8
  %_87 = alloca [48 x i8], align 8
  %_83 = alloca [16 x i8], align 8
  %_82 = alloca [16 x i8], align 8
  %_77 = alloca [48 x i8], align 8
  %_74 = alloca [16 x i8], align 8
  %_73 = alloca [16 x i8], align 8
  %_72 = alloca [48 x i8], align 8
  %_71 = alloca [48 x i8], align 8
  %_67 = alloca [16 x i8], align 8
  %_66 = alloca [16 x i8], align 8
  %_61 = alloca [48 x i8], align 8
  %_58 = alloca [16 x i8], align 8
  %_57 = alloca [16 x i8], align 8
  %_56 = alloca [48 x i8], align 8
  %_55 = alloca [48 x i8], align 8
  %_51 = alloca [16 x i8], align 8
  %_50 = alloca [16 x i8], align 8
  %_45 = alloca [48 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %_39 = alloca [48 x i8], align 8
  %_36 = alloca [16 x i8], align 8
  %_35 = alloca [16 x i8], align 8
  %_32 = alloca [48 x i8], align 8
  %_29 = alloca [16 x i8], align 8
  %_28 = alloca [16 x i8], align 8
  %_25 = alloca [48 x i8], align 8
  %_22 = alloca [16 x i8], align 8
  %_21 = alloca [16 x i8], align 8
  %_18 = alloca [48 x i8], align 8
  %_15 = alloca [16 x i8], align 8
  %_14 = alloca [16 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_b8d55bc849a6a33d53dac688cf4d3c06)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_07843771c0250c901fa0d11390d35f2b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_15, ptr align 8 @alloc_0a8fc1f3882cff48f8c6b0974f2edfb2)
  %0 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_14, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 8 %_15, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_1c6678482269421fd6d03f1590d7fbf5, ptr align 8 %_14)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_22, ptr align 4 @alloc_03c996aabbc9ab62051e986fb92cba9c)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_21, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_22, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_18, ptr align 8 @alloc_0fe0909a7db5f8e6026eeada27b6f263, ptr align 8 %_21)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_18)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_29, ptr align 8 @alloc_5f87537df43b8aac634c07eb42895206)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_28, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_29, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_25, ptr align 8 @alloc_421f615f12d1b4228495ec26aaaf3a3b, ptr align 8 %_28)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_25)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_36, ptr align 8 @alloc_da92151caaeb84c5c1f76b28dacc1473)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_35, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_36, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_32, ptr align 8 @alloc_fffd3565fa5898bf951510e8d42b4413, ptr align 8 %_35)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_32)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_39, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_39)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_42, ptr align 8 @alloc_0e180ec3c8f453ded99202ba432176d8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_42)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_51, ptr align 8 @alloc_0a8fc1f3882cff48f8c6b0974f2edfb2)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_50, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_51, i64 16, i1 false)
  %5 = getelementptr inbounds i8, ptr %_57, i64 2
  store i16 10, ptr %5, align 2
  store i16 0, ptr %_57, align 8
  store i16 2, ptr %_58, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_56, i64 0, i32 -268435424, ptr align 8 %_57, ptr align 8 %_58)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_55, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_56, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_45, ptr align 8 @alloc_3bf5c39e8f0101c26de409f7eff5cfc2, i64 2, ptr align 8 %_50, i64 1, ptr align 8 %_55, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_45)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_67, ptr align 8 @alloc_97141a5f2da28e32746b2e87e7a7f02b)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_66, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_67, i64 16, i1 false)
  %8 = getelementptr inbounds i8, ptr %_73, i64 2
  store i16 10, ptr %8, align 2
  store i16 0, ptr %_73, align 8
  store i16 2, ptr %_74, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_72, i64 0, i32 -268435424, ptr align 8 %_73, ptr align 8 %_74)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_71, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_72, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_61, ptr align 8 @alloc_8833b243aff017acc5586bbceed83924, i64 2, ptr align 8 %_66, i64 1, ptr align 8 %_71, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_61)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_83, ptr align 8 @alloc_097e2602a9650474e7c1161152ec752d)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_82, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_83, i64 16, i1 false)
  %11 = getelementptr inbounds i8, ptr %_89, i64 2
  store i16 10, ptr %11, align 2
  store i16 0, ptr %_89, align 8
  store i16 2, ptr %_90, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_88, i64 0, i32 -268435424, ptr align 8 %_89, ptr align 8 %_90)
  %12 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_87, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %12, ptr align 8 %_88, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_77, ptr align 8 @alloc_34ab63eff4a60651a28bb4e8ffca4db2, i64 2, ptr align 8 %_82, i64 1, ptr align 8 %_87, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_77)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_99, ptr align 8 @alloc_a6f9eb080cec2b391d6e8a8179f5772a)
  %13 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_98, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %13, ptr align 8 %_99, i64 16, i1 false)
  %14 = getelementptr inbounds i8, ptr %_105, i64 2
  store i16 10, ptr %14, align 2
  store i16 0, ptr %_105, align 8
  store i16 2, ptr %_106, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_104, i64 0, i32 -268435424, ptr align 8 %_105, ptr align 8 %_106)
  %15 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_103, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %15, ptr align 8 %_104, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_93, ptr align 8 @alloc_8cf9ed33f9adcff0cf5c95113952c4ed, i64 2, ptr align 8 %_98, i64 1, ptr align 8 %_103, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_93)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_115, ptr align 8 @alloc_4de7c80f515ccd653a5d590e6d349f5f)
  %16 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_114, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %16, ptr align 8 %_115, i64 16, i1 false)
  %17 = getelementptr inbounds i8, ptr %_121, i64 2
  store i16 10, ptr %17, align 2
  store i16 0, ptr %_121, align 8
  store i16 2, ptr %_122, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_120, i64 0, i32 -268435424, ptr align 8 %_121, ptr align 8 %_122)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_119, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_120, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_109, ptr align 8 @alloc_35b30c57354b60f1fee559c306f28469, i64 2, ptr align 8 %_114, i64 1, ptr align 8 %_119, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_109)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_125, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_125)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_128, ptr align 8 @alloc_12f7dc37d00775fc0f7b3525bb894284)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_128)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf319fdc427149568E(ptr sret([16 x i8]) align 8 %_135, ptr align 1 @alloc_8821998f047ca62cad40e6bc4e4d87c4)
  %19 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_134, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %19, ptr align 8 %_135, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_131, ptr align 8 @alloc_f7827304264a57248e0c662b2bb5eead, ptr align 8 %_134)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_131)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf319fdc427149568E(ptr sret([16 x i8]) align 8 %_142, ptr align 1 @alloc_914b2c69d7eca30497b9feaf15ac92f1)
  %20 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_141, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %20, ptr align 8 %_142, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_138, ptr align 8 @alloc_63695d543b4971803e1701759578d116, ptr align 8 %_141)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_138)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_145, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_145)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_148, ptr align 8 @alloc_7ce2b533d7b36b7eff5a1164f8d9cdf1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_148)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_155, ptr align 4 @alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_154, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_155, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_151, ptr align 8 @alloc_4679ae6520501df1335d69e22d43c97e, ptr align 8 %_154)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_151)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_162, ptr align 4 @alloc_66e0d902dc5512250b9d4edb026cbbeb)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_161, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_162, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_158, ptr align 8 @alloc_38b6fd4add01f3813f81b4de5c32fcff, ptr align 8 %_161)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_158)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_169, ptr align 4 @alloc_47c8330ffe7a09bc00d165d03facdeb6)
  %23 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_168, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %23, ptr align 8 %_169, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_165, ptr align 8 @alloc_d505b210a6837045e27d198653b9bc23, ptr align 8 %_168)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_165)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_176, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
  %24 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_175, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %24, ptr align 8 %_176, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_172, ptr align 8 @alloc_50c952892abd07a35022b35dccf7d8a3, ptr align 8 %_175)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_172)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_183, ptr align 4 @alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0)
  %25 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_182, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %25, ptr align 8 %_183, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_179, ptr align 8 @alloc_5a21eae34823e3b7852dc4fb9ea1485e, ptr align 8 %_182)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_179)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_186, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_186)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_189, ptr align 8 @alloc_ad360cc943354615ff7c5ccd5734d26e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_189)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_196, ptr align 8 @alloc_eab5d04767146d7d9b93b60d28ef530a)
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_195, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_196, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_192, ptr align 8 @alloc_77ad9436238e4853bc231c69186d262b, ptr align 8 %_195)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_192)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_203, ptr align 8 @alloc_1fae48e79ef589f304d5eacb66de4239)
  %27 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_202, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %27, ptr align 8 %_203, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_199, ptr align 8 @alloc_7d80103df6071399fd41b805e0952740, ptr align 8 %_202)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_199)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_210, ptr align 8 @alloc_0f2021b59b47cc3bcb9eda22808aa158)
  %28 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_209, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %28, ptr align 8 %_210, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_206, ptr align 8 @alloc_3d4efc5076f4bdbd0d3f07b1747474da, ptr align 8 %_209)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_206)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_217, ptr align 8 @alloc_19a1846de6c3fc15a5d237e93f872958)
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_216, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_217, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_213, ptr align 8 @alloc_27dba32f246936f7ba8808d83fa2e62b, ptr align 8 %_216)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_213)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_220, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_220)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_223, ptr align 8 @alloc_2fed207b42d30098c4574ea6ed29229e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_223)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_230, ptr align 4 @alloc_fc18870ebd3bffa18e161a5a63775070)
  %30 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_229, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %30, ptr align 8 %_230, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_226, ptr align 8 @alloc_ee98fb8e3a9f311326b095a44a228ccd, ptr align 8 %_229)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_226)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_237, ptr align 4 @alloc_63738555cfef41d17c63452561b7eb27)
  %31 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_236, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %31, ptr align 8 %_237, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_233, ptr align 8 @alloc_20580277cc3463853cb4d0cdf316ee6e, ptr align 8 %_236)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_233)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_244, ptr align 4 @alloc_e7ecca184d1801ae8ba4577ecc1a7822)
  %32 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_243, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %32, ptr align 8 %_244, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_240, ptr align 8 @alloc_ca26fdaa79b02cc7cd13e966384b55a2, ptr align 8 %_243)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_240)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_251, ptr align 4 @alloc_9348a6d0135e4bb8777d851374adfb84)
  %33 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_250, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %33, ptr align 8 %_251, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_247, ptr align 8 @alloc_80e3df80c9d9d79364fad22329cc6dbd, ptr align 8 %_250)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_247)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_258, ptr align 4 @alloc_9348a6d0135e4bb8777d851374adfb84)
  %34 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_257, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %34, ptr align 8 %_258, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_254, ptr align 8 @alloc_9f1409cd3e102279004b638d4e6233cb, ptr align 8 %_257)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_254)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_261, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_261)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_264, ptr align 8 @alloc_0dd91a4c7fd9c03fc68d1ebd68710ebe)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_264)
  store double 0x7FF0000000000000, ptr %positive_infinity, align 8
  store double 0xFFF0000000000000, ptr %negative_infinity, align 8
  store double 0x7FF8000000000000, ptr %not_a_number, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_274, ptr align 8 %positive_infinity)
  %35 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_273, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %35, ptr align 8 %_274, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_270, ptr align 8 @alloc_c749254c68d6cda80c1b52a96ea13e5f, ptr align 8 %_273)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_270)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_281, ptr align 8 %negative_infinity)
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_280, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_281, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_277, ptr align 8 @alloc_3c988b7cb6d568e088772895f7d00d4a, ptr align 8 %_280)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_277)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_288, ptr align 8 %not_a_number)
  %37 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_287, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %37, ptr align 8 %_288, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_284, ptr align 8 @alloc_7a69bc728950ddba0adabc30bc778f1a, ptr align 8 %_287)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_284)
  %38 = load double, ptr %not_a_number, align 8
; call core::f64::<impl f64>::is_nan
  %39 = call zeroext i1 @"_ZN4core3f6421_$LT$impl$u20$f64$GT$6is_nan17h06f71d4cc4464519E"(double %38)
  %40 = zext i1 %39 to i8
  store i8 %40, ptr %_297, align 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf319fdc427149568E(ptr sret([16 x i8]) align 8 %_295, ptr align 1 %_297)
  %41 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_294, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %41, ptr align 8 %_295, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_291, ptr align 8 @alloc_8077090583980922edf56c55b2eb164d, ptr align 8 %_294)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_291)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_299, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_299)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_302, ptr align 8 @alloc_e99af7a4e8842ccdb4dc3f9ef7d89914)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_302)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_309, ptr align 4 @alloc_c8f6fdd84450dae01461435b8eda5097)
  %42 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_308, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %42, ptr align 8 %_309, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_305, ptr align 8 @alloc_2c72478184ed55adb3b435d705d264d9, ptr align 8 %_308)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_305)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_316, ptr align 4 @alloc_4afdb120a236013e0c29be80b15d8aee)
  %43 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_315, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %43, ptr align 8 %_316, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_312, ptr align 8 @alloc_8567b72d512fca41c8cb3aed588686cd, ptr align 8 %_315)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_312)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h28191d94ec00639fE(ptr sret([16 x i8]) align 8 %_323, ptr align 4 @alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_322, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_323, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_319, ptr align 8 @alloc_29e9434f39a2e46aa70bd5ef85d89397, ptr align 8 %_322)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_319)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h28191d94ec00639fE(ptr sret([16 x i8]) align 8 %_330, ptr align 4 @alloc_47c8330ffe7a09bc00d165d03facdeb6)
  %45 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_329, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %45, ptr align 8 %_330, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_326, ptr align 8 @alloc_710be66bee20a51084dea6cd476ca5fa, ptr align 8 %_329)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_326)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_333, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_333)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_336, ptr align 8 @alloc_f06c6d6b15b079ba3278f67db350802c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_336)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_343, ptr align 8 @alloc_7a86e1999471ac922481c120e60756f8)
  %46 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_342, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %46, ptr align 8 %_343, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_339, ptr align 8 @alloc_99f210fab252da2abf653ae25044b8dc, ptr align 8 %_342)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_339)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_350, ptr align 8 @alloc_86fe2e14e8aee3cbc4747be45abbb75c)
  %47 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_349, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %47, ptr align 8 %_350, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_346, ptr align 8 @alloc_0566dbe9b4b0958b93a966e348d1739b, ptr align 8 %_349)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_346)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_357, ptr align 8 @alloc_e9e55e98553b87bb6aaa9418d9398720)
  %48 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_356, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %48, ptr align 8 %_357, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_353, ptr align 8 @alloc_d4f845388026782be51d3da3b5a4a69b, ptr align 8 %_356)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_353)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_360, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_360)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_363, ptr align 8 @alloc_ab00968d0b88e8d5880311e810c8507a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_363)
  store double 5.000000e+00, ptr %radius, align 8
  %49 = load double, ptr %radius, align 8
  %_367 = fmul double 0x400921FB54442EEA, %49
  %50 = load double, ptr %radius, align 8
  %51 = fmul double %_367, %50
  store double %51, ptr %area, align 8
  %52 = load double, ptr %radius, align 8
  %53 = fmul double 0x401921FB54442EEA, %52
  store double %53, ptr %circumference, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_375, ptr align 8 %radius)
  %54 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_374, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %54, ptr align 8 %_375, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_371, ptr align 8 @alloc_45b6e4e458c1f17851a447773c1ae114, ptr align 8 %_374)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_371)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_384, ptr align 8 @alloc_0a8fc1f3882cff48f8c6b0974f2edfb2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_386, ptr align 8 %radius)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_388, ptr align 8 %area)
  %55 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_383, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %55, ptr align 8 %_384, i64 16, i1 false)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_383, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_386, i64 16, i1 false)
  %57 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_383, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %57, ptr align 8 %_388, i64 16, i1 false)
  store i16 2, ptr %_394, align 8
  store i16 2, ptr %_395, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_393, i64 0, i32 -536870880, ptr align 8 %_394, ptr align 8 %_395)
  store i16 2, ptr %_397, align 8
  store i16 2, ptr %_398, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_396, i64 1, i32 -536870880, ptr align 8 %_397, ptr align 8 %_398)
  %58 = getelementptr inbounds i8, ptr %_400, i64 2
  store i16 2, ptr %58, align 2
  store i16 0, ptr %_400, align 8
  store i16 2, ptr %_401, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_399, i64 2, i32 -268435424, ptr align 8 %_400, ptr align 8 %_401)
  %59 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_392, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %59, ptr align 8 %_393, i64 48, i1 false)
  %60 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_392, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %60, ptr align 8 %_396, i64 48, i1 false)
  %61 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_392, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %61, ptr align 8 %_399, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_378, ptr align 8 @alloc_4953eb20e1db771248b79fb7b3d8fdce, i64 4, ptr align 8 %_383, i64 3, ptr align 8 %_392, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_378)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_410, ptr align 8 @alloc_0a8fc1f3882cff48f8c6b0974f2edfb2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_412, ptr align 8 %radius)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h06a83072489afc99E(ptr sret([16 x i8]) align 8 %_414, ptr align 8 %circumference)
  %62 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_409, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %62, ptr align 8 %_410, i64 16, i1 false)
  %63 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_409, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %63, ptr align 8 %_412, i64 16, i1 false)
  %64 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_409, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %64, ptr align 8 %_414, i64 16, i1 false)
  store i16 2, ptr %_420, align 8
  store i16 2, ptr %_421, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_419, i64 0, i32 -536870880, ptr align 8 %_420, ptr align 8 %_421)
  store i16 2, ptr %_423, align 8
  store i16 2, ptr %_424, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_422, i64 1, i32 -536870880, ptr align 8 %_423, ptr align 8 %_424)
  %65 = getelementptr inbounds i8, ptr %_426, i64 2
  store i16 2, ptr %65, align 2
  store i16 0, ptr %_426, align 8
  store i16 2, ptr %_427, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17hcd6779991ac7a2adE(ptr sret([48 x i8]) align 8 %_425, i64 2, i32 -268435424, ptr align 8 %_426, ptr align 8 %_427)
  %66 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_418, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %66, ptr align 8 %_419, i64 48, i1 false)
  %67 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_418, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %67, ptr align 8 %_422, i64 48, i1 false)
  %68 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_418, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %68, ptr align 8 %_425, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h833d61e54a7fe2dcE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17hb5b80426df581c79E(ptr sret([48 x i8]) align 8 %_404, ptr align 8 @alloc_1a6f814103102aece8549d7c8117a215, i64 4, ptr align 8 %_409, i64 3, ptr align 8 %_418, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_404)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_430, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_430)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_433, ptr align 8 @alloc_428caaff346f2213e8b72d760c386123)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_433)
  store i32 3661, ptr %total_seconds, align 4
  %69 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 60, i32 60)
  %_438.0 = extractvalue { i32, i1 } %69, 0
  %_438.1 = extractvalue { i32, i1 } %69, 1
  br i1 %_438.1, label %panic, label %bb187

bb187:                                            ; preds = %start
  %_439 = icmp eq i32 %_438.0, 0
  br i1 %_439, label %panic1, label %bb188

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_4a1e53d290dbba4fe349169ecd8aaf28) #7
  unreachable

bb188:                                            ; preds = %bb187
  %_440 = icmp eq i32 %_438.0, -1
  %70 = load i32, ptr %total_seconds, align 4
  %_441 = icmp eq i32 %70, -2147483648
  %_442 = and i1 %_440, %_441
  br i1 %_442, label %panic2, label %bb189

panic1:                                           ; preds = %bb187
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_11423f29e344a922ef72359cf9d746a0) #7
  unreachable

bb189:                                            ; preds = %bb188
  %71 = load i32, ptr %total_seconds, align 4
  %72 = sdiv i32 %71, %_438.0
  store i32 %72, ptr %hours, align 4
  %73 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 60, i32 60)
  %_445.0 = extractvalue { i32, i1 } %73, 0
  %_445.1 = extractvalue { i32, i1 } %73, 1
  br i1 %_445.1, label %panic3, label %bb190

panic2:                                           ; preds = %bb188
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_11423f29e344a922ef72359cf9d746a0) #7
  unreachable

bb190:                                            ; preds = %bb189
  %_446 = icmp eq i32 %_445.0, 0
  br i1 %_446, label %panic4, label %bb191

panic3:                                           ; preds = %bb189
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_d1278d77d0fb95a90322dc7d536cd662) #7
  unreachable

bb191:                                            ; preds = %bb190
  %_447 = icmp eq i32 %_445.0, -1
  %74 = load i32, ptr %total_seconds, align 4
  %_448 = icmp eq i32 %74, -2147483648
  %_449 = and i1 %_447, %_448
  br i1 %_449, label %panic5, label %bb192

panic4:                                           ; preds = %bb190
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 @alloc_0fe6bb1f6f714428f53b2906a274819c) #7
  unreachable

bb192:                                            ; preds = %bb191
  %75 = load i32, ptr %total_seconds, align 4
  %remaining_after_hours = srem i32 %75, %_445.0
  %_453 = icmp eq i32 %remaining_after_hours, -2147483648
  %_454 = and i1 false, %_453
  br i1 %_454, label %panic6, label %bb194

panic5:                                           ; preds = %bb191
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_0fe6bb1f6f714428f53b2906a274819c) #7
  unreachable

bb194:                                            ; preds = %bb192
  %76 = sdiv i32 %remaining_after_hours, 60
  store i32 %76, ptr %minutes, align 4
  %_458 = icmp eq i32 %remaining_after_hours, -2147483648
  %_459 = and i1 false, %_458
  br i1 %_459, label %panic7, label %bb196

panic6:                                           ; preds = %bb192
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_d36cb40d2b5352a8d1fc115064552fdf) #7
  unreachable

bb196:                                            ; preds = %bb194
  %77 = srem i32 %remaining_after_hours, 60
  store i32 %77, ptr %seconds, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_465, ptr align 4 %total_seconds)
  %78 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_464, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %78, ptr align 8 %_465, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_461, ptr align 8 @alloc_011bc8cbb50683cb46ad66ea4c9fa8ae, ptr align 8 %_464)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_461)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_472, ptr align 4 %hours)
  %79 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_471, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %79, ptr align 8 %_472, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_468, ptr align 8 @alloc_1c532211cab671e14ac457bf2c719883, ptr align 8 %_471)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_468)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_479, ptr align 4 %minutes)
  %80 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_478, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %80, ptr align 8 %_479, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_475, ptr align 8 @alloc_39fde975601ef2ec9b9907b54530ffb5, ptr align 8 %_478)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_475)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_486, ptr align 4 %seconds)
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_485, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_486, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_482, ptr align 8 @alloc_305da04dc7245cbf21e76ef5e88f4a2b, ptr align 8 %_485)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_482)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_493, ptr align 4 %hours)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_495, ptr align 4 %minutes)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_497, ptr align 4 %seconds)
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_492, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_493, i64 16, i1 false)
  %83 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_492, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %83, ptr align 8 %_495, i64 16, i1 false)
  %84 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_492, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %84, ptr align 8 %_497, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_489, ptr align 8 @alloc_0eed1429722b7ffe76da138fe5290fdf, ptr align 8 %_492)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_489)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_500, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_500)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_503, ptr align 8 @alloc_592f32e35e392548e75ab9188b8382c5)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_503)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_506, ptr align 8 @alloc_0092bd81a21f49b74bc2425d9ce204fe)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_506)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h952a17f1852c41e9E(ptr sret([16 x i8]) align 8 %_513, ptr align 8 @alloc_1329e2565db6d32f3e74e7ab8e4870d5)
  %85 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_512, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %85, ptr align 8 %_513, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_509, ptr align 8 @alloc_f970787d7fe41bd84ebd138cc7ebf6c2, ptr align 8 %_512)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_509)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf8083b5ecf2c5c30E(ptr sret([16 x i8]) align 8 %_520, ptr align 2 @alloc_10b869d7795ff893f363d37885041c0c)
  %86 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_519, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %86, ptr align 8 %_520, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_516, ptr align 8 @alloc_58adc591cee224c2f65e482d19982e6d, ptr align 8 %_519)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_516)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_527, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
  %87 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_526, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %87, ptr align 8 %_527, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_523, ptr align 8 @alloc_e6b4443d34cef5fd5d598a0b0e23539d, ptr align 8 %_526)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_523)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_534, ptr align 4 @alloc_0e3505805f4905816c1bd644811a7869)
  %88 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_533, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %88, ptr align 8 %_534, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_530, ptr align 8 @alloc_878a508c2d9091aa8492f82a294f536a, ptr align 8 %_533)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_530)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8b4137f3eb9f928cE(ptr sret([16 x i8]) align 8 %_541, ptr align 8 @alloc_8da287e0d9a7604047d53e42ae527590)
  %89 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_540, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %89, ptr align 8 %_541, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_537, ptr align 8 @alloc_0523402689d58e9f1b54fa554728f1c2, ptr align 8 %_540)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_537)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_544, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_544)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_547, ptr align 8 @alloc_26a5f1cccb2063574c45746f28e26d3e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_547)
  br label %bb242

panic7:                                           ; preds = %bb194
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 @alloc_75ebd049d9dd536b2a50c2f29e529bae) #7
  unreachable

bb242:                                            ; preds = %bb196
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_555, ptr align 4 @alloc_9e25808f472fc2b820d660613b73af30)
  %90 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_554, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %90, ptr align 8 %_555, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_551, ptr align 8 @alloc_9302a4cff701b26f53f764cc9c54189c, ptr align 8 %_554)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_551)
  br label %bb251

bb251:                                            ; preds = %bb242
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_579, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_579)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_582, ptr align 8 @alloc_9f41fe8245510942a85a5f1602127ed8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_582)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_589, ptr align 1 @alloc_45fce9df1beaeebc2e036763a2b33fd8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_591, ptr align 1 getelementptr inbounds (i8, ptr @alloc_45fce9df1beaeebc2e036763a2b33fd8, i64 1))
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_593, ptr align 1 getelementptr inbounds (i8, ptr @alloc_45fce9df1beaeebc2e036763a2b33fd8, i64 2))
  %91 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_588, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %91, ptr align 8 %_589, i64 16, i1 false)
  %92 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_588, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %92, ptr align 8 %_591, i64 16, i1 false)
  %93 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_588, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %93, ptr align 8 %_593, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_585, ptr align 8 @alloc_a069eb5353b3aea82e6fab5147fa5cca, ptr align 8 %_588)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_585)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_600, ptr align 1 @alloc_925e40e5a12fd6a49d3ce2c1801a7aa7)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_602, ptr align 1 getelementptr inbounds (i8, ptr @alloc_925e40e5a12fd6a49d3ce2c1801a7aa7, i64 1))
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_604, ptr align 1 getelementptr inbounds (i8, ptr @alloc_925e40e5a12fd6a49d3ce2c1801a7aa7, i64 2))
  %94 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_599, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %94, ptr align 8 %_600, i64 16, i1 false)
  %95 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_599, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %95, ptr align 8 %_602, i64 16, i1 false)
  %96 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_599, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %96, ptr align 8 %_604, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_596, ptr align 8 @alloc_65fb4bd123f01a27a3b1f1c3e7a49663, ptr align 8 %_599)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_596)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_611, ptr align 1 @alloc_68b1f460579f7a37d63efd3d48ac0953)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_613, ptr align 1 getelementptr inbounds (i8, ptr @alloc_68b1f460579f7a37d63efd3d48ac0953, i64 1))
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_615, ptr align 1 getelementptr inbounds (i8, ptr @alloc_68b1f460579f7a37d63efd3d48ac0953, i64 2))
  %97 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_610, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %97, ptr align 8 %_611, i64 16, i1 false)
  %98 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_610, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %98, ptr align 8 %_613, i64 16, i1 false)
  %99 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_610, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %99, ptr align 8 %_615, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_607, ptr align 8 @alloc_e825019b9d00916699241fdc37c2f854, ptr align 8 %_610)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_607)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_622, ptr align 1 @alloc_d12b77d5e35485f63b33d884d82bf500)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_624, ptr align 1 getelementptr inbounds (i8, ptr @alloc_d12b77d5e35485f63b33d884d82bf500, i64 1))
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_626, ptr align 1 getelementptr inbounds (i8, ptr @alloc_d12b77d5e35485f63b33d884d82bf500, i64 2))
  %100 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_621, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %100, ptr align 8 %_622, i64 16, i1 false)
  %101 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_621, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %101, ptr align 8 %_624, i64 16, i1 false)
  %102 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_621, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %102, ptr align 8 %_626, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_618, ptr align 8 @alloc_dfe5cb7c4d235d4244d87a879e4f0f8a, ptr align 8 %_621)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_618)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_633, ptr align 1 @alloc_655dbaa176364e4aa81017fd797799fb)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_635, ptr align 1 getelementptr inbounds (i8, ptr @alloc_655dbaa176364e4aa81017fd797799fb, i64 1))
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h14604c4c868a9e62E(ptr sret([16 x i8]) align 8 %_637, ptr align 1 getelementptr inbounds (i8, ptr @alloc_655dbaa176364e4aa81017fd797799fb, i64 2))
  %103 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %103, ptr align 8 %_633, i64 16, i1 false)
  %104 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %104, ptr align 8 %_635, i64 16, i1 false)
  %105 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_632, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %105, ptr align 8 %_637, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_629, ptr align 8 @alloc_60955c5d06c988d7d8f3741375eb0d19, ptr align 8 %_632)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_629)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_640, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_640)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_643, ptr align 8 @alloc_db39dd8aa9a3b1d52760e91a48735ddd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_643)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8b4137f3eb9f928cE(ptr sret([16 x i8]) align 8 %_650, ptr align 8 @alloc_396d2afaa81902be59ec9eb1c744d98a)
  %106 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_649, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %106, ptr align 8 %_650, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_646, ptr align 8 @alloc_71194e1f6e658fabe10f217f2d3a8628, ptr align 8 %_649)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_646)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8b4137f3eb9f928cE(ptr sret([16 x i8]) align 8 %_657, ptr align 8 @alloc_f53e7b75db1583e44fb3acdf703196c9)
  %107 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_656, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %107, ptr align 8 %_657, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_653, ptr align 8 @alloc_7ff366ce9a7c4851d7fc35fa09d34949, ptr align 8 %_656)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_653)
; call core::fmt::rt::Argument::new_octal
  call void @_ZN4core3fmt2rt8Argument9new_octal17h45e442a1eda29950E(ptr sret([16 x i8]) align 8 %_664, ptr align 4 @alloc_c8093cc7c4b69cf6e8b5785d79462716)
  %108 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_663, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %108, ptr align 8 %_664, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_660, ptr align 8 @alloc_aad384c730c022174acc3a8ab5434cf7, ptr align 8 %_663)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_660)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_667, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_667)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_670, ptr align 8 @alloc_4af9e4151ea00e06c0d4468af073ce91)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_670)
  %109 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 0
  store ptr @alloc_fc226fb9a476ea06f815767c1b7d7136, ptr %109, align 8
  %110 = getelementptr inbounds i8, ptr %109, i64 8
  store i64 6, ptr %110, align 8
  %111 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 1
  store ptr @alloc_fb309e7aab750c01f9e55b8f43d774e0, ptr %111, align 8
  %112 = getelementptr inbounds i8, ptr %111, i64 8
  store i64 7, ptr %112, align 8
  %113 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 2
  store ptr @alloc_e3976b92482fb4550470b093759847a6, ptr %113, align 8
  %114 = getelementptr inbounds i8, ptr %113, i64 8
  store i64 9, ptr %114, align 8
  %115 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 3
  store ptr @alloc_edb650fdf204f96ecf65cf6a36209271, ptr %115, align 8
  %116 = getelementptr inbounds i8, ptr %115, i64 8
  store i64 8, ptr %116, align 8
  %117 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 4
  store ptr @alloc_bf1b3fef127dec54993dc7b162dd88f0, ptr %117, align 8
  %118 = getelementptr inbounds i8, ptr %117, i64 8
  store i64 6, ptr %118, align 8
  %119 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 5
  store ptr @alloc_c5c4869492c5f7d7d8eb50adae4cd7f1, ptr %119, align 8
  %120 = getelementptr inbounds i8, ptr %119, i64 8
  store i64 8, ptr %120, align 8
  %121 = getelementptr inbounds nuw { ptr, i64 }, ptr %weekdays, i64 6
  store ptr @alloc_b482e652b9e23eeebfff6ad2b8bde4ae, ptr %121, align 8
  %122 = getelementptr inbounds i8, ptr %121, i64 8
  store i64 6, ptr %122, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_684, ptr align 4 @alloc_fc18870ebd3bffa18e161a5a63775070)
  %123 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_683, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %123, ptr align 8 %_684, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_680, ptr align 8 @alloc_29fb70a501d7929dba9d82cfe25d54d6, ptr align 8 %_683)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_680)
; call core::slice::<impl [T]>::iter
  %124 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hdf3b581bbec498bcE"(ptr align 8 %weekdays, i64 7)
  %_688.0 = extractvalue { ptr, ptr } %124, 0
  %_688.1 = extractvalue { ptr, ptr } %124, 1
; call core::iter::traits::iterator::Iterator::enumerate
  call void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h45e23aabbec32c44E(ptr sret([24 x i8]) align 8 %_687, ptr %_688.0, ptr %_688.1)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3a36c84724bc4f92E"(ptr sret([24 x i8]) align 8 %_686, ptr align 8 %_687)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %_686, i64 24, i1 false)
  br label %bb304

bb304:                                            ; preds = %bb309, %bb251
; call <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
  %125 = call { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h97cdc9e101a27176E"(ptr align 8 %iter)
  %126 = extractvalue { i64, ptr } %125, 0
  %127 = extractvalue { i64, ptr } %125, 1
  store i64 %126, ptr %_692, align 8
  %128 = getelementptr inbounds i8, ptr %_692, i64 8
  store ptr %127, ptr %128, align 8
  %129 = load i64, ptr %_692, align 8
  %130 = getelementptr inbounds i8, ptr %_692, i64 8
  %131 = load ptr, ptr %130, align 8
  %132 = ptrtoint ptr %131 to i64
  %133 = icmp eq i64 %132, 0
  %_694 = select i1 %133, i64 0, i64 1
  %134 = trunc nuw i64 %_694 to i1
  br i1 %134, label %bb307, label %bb308

bb307:                                            ; preds = %bb304
  %i = load i64, ptr %_692, align 8
  %135 = getelementptr inbounds i8, ptr %_692, i64 8
  %136 = load ptr, ptr %135, align 8
  store ptr %136, ptr %day, align 8
  %137 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %i, i64 1)
  %_705.0 = extractvalue { i64, i1 } %137, 0
  %_705.1 = extractvalue { i64, i1 } %137, 1
  br i1 %_705.1, label %panic8, label %bb309

bb308:                                            ; preds = %bb304
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_709, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_709)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_712, ptr align 8 @alloc_86e81aef664bda8d0a6207ac93cbeaf3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_712)
  store i32 85, ptr %user_score, align 4
  %138 = load i32, ptr %user_score, align 4
  %_715 = icmp sge i32 %138, 0
  br i1 %_715, label %bb317, label %bb323

bb323:                                            ; preds = %bb317, %bb308
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_733, ptr align 4 %user_score)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_735, ptr align 4 @alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_737, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
  %139 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_732, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %139, ptr align 8 %_733, i64 16, i1 false)
  %140 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_732, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %140, ptr align 8 %_735, i64 16, i1 false)
  %141 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_732, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %141, ptr align 8 %_737, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_729, ptr align 8 @alloc_7f0207a35788b66e2b4ba8b58c4136ef, ptr align 8 %_732)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_729)
  br label %bb328

bb317:                                            ; preds = %bb308
  %142 = load i32, ptr %user_score, align 4
  %_716 = icmp sle i32 %142, 100
  br i1 %_716, label %bb318, label %bb323

bb318:                                            ; preds = %bb317
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_722, ptr align 4 %user_score)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_724, ptr align 4 @alloc_83ea17bf0c4f4a5a5a13d3ae7955acd0)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_726, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
  %143 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_721, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %143, ptr align 8 %_722, i64 16, i1 false)
  %144 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_721, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %144, ptr align 8 %_724, i64 16, i1 false)
  %145 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_721, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %145, ptr align 8 %_726, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h9f8db40acf95d19eE(ptr sret([48 x i8]) align 8 %_718, ptr align 8 @alloc_ff43b11a9a015a9e079d1ce771d8b057, ptr align 8 %_721)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_718)
  br label %bb328

bb328:                                            ; preds = %bb318, %bb323
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_740, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_740)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_743, ptr align 8 @alloc_3cf1a08e96b26a26d3e230b2b8df4a99)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_743)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_750, ptr align 4 @alloc_9c3baa4c7d946dbf786ac3cfdafa8cd9)
  %146 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_749, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %146, ptr align 8 %_750, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_746, ptr align 8 @alloc_2ed217bf448babc042e39c7bf471145e, ptr align 8 %_749)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_746)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8e3f1cea43d3a777E(ptr sret([16 x i8]) align 8 %_757, ptr align 4 @alloc_dc3e4f124eb94e7c4d273a63286e5e16)
  %147 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_756, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %147, ptr align 8 %_757, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hceee748a40444bfbE(ptr sret([48 x i8]) align 8 %_753, ptr align 8 @alloc_146feca2cd7bd345354696f9c2ffb6a4, ptr align 8 %_756)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_753)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_760, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_760)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hae6f50b01b843325E(ptr sret([48 x i8]) align 8 %_763, ptr align 8 @alloc_81698f0a245fb48aef062ccffef12a0d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_763)
  ret void

bb309:                                            ; preds = %bb307
  store i64 %_705.0, ptr %_704, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8b4137f3eb9f928cE(ptr sret([16 x i8]) align 8 %_702, ptr align 8 %_704)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h4a4b25b25e057d19E(ptr sret([16 x i8]) align 8 %_706, ptr align 8 %day)
  %148 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_701, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %148, ptr align 8 %_702, i64 16, i1 false)
  %149 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_701, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %149, ptr align 8 %_706, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hb4c5bb4155e0955eE(ptr sret([48 x i8]) align 8 %_698, ptr align 8 @alloc_d6ac99c7d7059c2e53fbec18f33d1233, ptr align 8 %_701)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_698)
  br label %bb304

panic8:                                           ; preds = %bb307
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_730080e9ed391f604674ad5459e95229) #7
  unreachable

bb306:                                            ; No predecessors!
  unreachable

bb239:                                            ; No predecessors!
  unreachable

bb240:                                            ; No predecessors!
  unreachable

bb241:                                            ; No predecessors!
  unreachable

bb245:                                            ; No predecessors!
  unreachable

bb246:                                            ; No predecessors!
  unreachable

bb247:                                            ; No predecessors!
  unreachable

bb248:                                            ; No predecessors!
  unreachable

bb249:                                            ; No predecessors!
  unreachable

bb250:                                            ; No predecessors!
  unreachable
}

declare i32 @__CxxFrameHandler3(...) unnamed_addr #3

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #4

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #5

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #1

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #6

; core::fmt::float::<impl core::fmt::Display for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E"(ptr align 8, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for u8>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp51_$LT$impl$u20$core..fmt..Display$u20$for$u20$u8$GT$3fmt17ha43daf509c3f71a2E"(ptr align 1, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for u32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hdf87e42f0895a2dfE"(ptr align 4, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for usize>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE"(ptr align 8, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #1

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #1

; core::fmt::num::imp::<impl core::fmt::Display for u16>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u16$GT$3fmt17hf1c2edc43d81176bE"(ptr align 2, ptr align 8) unnamed_addr #1

; core::fmt::num::<impl core::fmt::Octal for u32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Octal$u20$for$u20$u32$GT$3fmt17hd5615923773e8e87E"(ptr align 4, ptr align 8) unnamed_addr #1

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #4

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #5

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #5

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #5

; core::panicking::panic_const::panic_const_rem_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8) unnamed_addr #5

; core::panicking::panic_const::panic_const_rem_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8) unnamed_addr #5

define i32 @main(i32 %0, ptr %1) unnamed_addr #3 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hbf49e816df1279d8E(ptr @_ZN28_13_constants_special_values4main17hcbcf42cabfb4e780E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { "target-cpu"="x86-64" }
attributes #4 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #5 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #7 = { noreturn }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 14046402782029566}
