# 05. Conditional Statements - Control Flow with if/elif/else
# This file demonstrates conditional statements in Dolet

print "=== Conditional Statements Demo ==="
print ""

# Simple if statement
print "Simple if Statement:"
int age = 18

if age >= 18
    print "You are an adult"
print ""

# if-else statement
print "if-else Statement:"
int score = 85

if score >= 90
    print "Grade: A"
else
    print "Grade: B or lower"
print ""

# if-elif-else chain
print "if-elif-else Chain:"
int marks = 75

if marks >= 90
    print "Excellent! Grade A"
elif marks >= 80
    print "Very Good! Grade B"
elif marks >= 70
    print "Good! Grade C"
elif marks >= 60
    print "Pass! Grade D"
else
    print "Fail! Grade F"
print ""

# Multiple conditions
print "Multiple Conditions:"
int temperature = 25
bool sunny = true

if temperature > 20
    if sunny == true
        print "Perfect weather for outdoor activities!"
    else
        print "Warm but cloudy"
else
    print "Too cold for outdoor activities"
print ""

# Complex boolean expressions
print "Complex Boolean Expressions:"
int x = 10
int y = 20

if x > 5 and y < 25
    print "Both conditions are true"

if x < 5 or y > 15
    print "At least one condition is true"
print ""

# String comparisons in conditionals
print "String Comparisons:"
string username = "admin"
string password = "secret123"

if username == "admin"
    if password == "secret123"
        print "Login successful!"
    else
        print "Wrong password"
else
    print "User not found"
print ""

# Nested conditionals
print "Nested Conditionals:"
int hour = 14

if hour < 12
    print "Good morning!"
else
    if hour < 18
        print "Good afternoon!"
    else
        print "Good evening!"
print ""

# Boolean variable conditions
print "Boolean Variable Conditions:"
bool is_logged_in = true
bool has_permission = false

if is_logged_in
    if has_permission
        print "Access granted"
    else
        print "Access denied - insufficient permissions"
else
    print "Please log in first"
print ""

# Comparison with different data types
print "Different Data Type Comparisons:"
float price = 19.99
char grade = 'A'
bool available = true

if price < 20.0
    print "Item is affordable"

if grade == 'A'
    print "Excellent grade!"

if available
    print "Item is in stock"
print ""

# Multiple elif statements
print "Multiple elif Statements:"
string day = "Monday"

if day == "Monday"
    print "Start of the work week"
elif day == "Tuesday"
    print "Tuesday blues"
elif day == "Wednesday"
    print "Hump day"
elif day == "Thursday"
    print "Almost there"
elif day == "Friday"
    print "TGIF!"
elif day == "Saturday"
    print "Weekend fun"
elif day == "Sunday"
    print "Rest day"
else
    print "Invalid day"
print ""

# Conditional with arithmetic
print "Conditional with Arithmetic:"
int a = 15
int b = 10

if (a + b) > 20
    print "Sum is greater than 20"

if (a * b) == 150
    print "Product equals 150"

if (a - b) < 10
    print "Difference is less than 10"
print ""

# Character comparisons
print "Character Comparisons:"
char letter = 'M'

if letter >= 'A' and letter <= 'Z'
    print "Uppercase letter"
elif letter >= 'a' and letter <= 'z'
    print "Lowercase letter"
else
    print "Not a letter"
print ""

print "=== End of Conditional Statements Demo ==="
