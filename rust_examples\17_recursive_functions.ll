; ModuleID = '17_recursive_functions.797e217226ed0f7d-cgu.0'
source_filename = "17_recursive_functions.797e217226ed0f7d-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::mem::maybe_uninit::MaybeUninit<(i32, u32)>" = type { [2 x i32] }
%"core::mem::maybe_uninit::MaybeUninit<(u32, u32)>" = type { [2 x i32] }
%"core::mem::maybe_uninit::MaybeUninit<&str>" = type { [2 x i64] }
%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@alloc_5a8fdd84b3281310cbf6b74bb6bf0065 = private unnamed_addr constant [218 x i8] c"unsafe precondition(s) violated: slice::get_unchecked_mut requires that the index is within the slice\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hc5cb834cde4b4f1dE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17he58b19c6c9f6198eE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17he58b19c6c9f6198eE" }>, align 8
@alloc_dd79dfae92e8fdc23813c4c7a1b7cf72 = private unnamed_addr constant [228 x i8] c"unsafe precondition(s) violated: ptr::write_bytes requires that the destination pointer is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@anon.bcddbdd9e49de95895ea092998e0184f.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@vtable.1 = private unnamed_addr constant <{ [24 x i8], ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h8607b37ae0a5ab7cE" }>, align 8
@alloc_a6a0cc8156fe455996de64a9d05b1dfe = private unnamed_addr constant [184 x i8] c"unsafe precondition(s) violated: u32::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fc445f6abf67cf4b683577fd3aaed336 = private unnamed_addr constant [184 x i8] c"unsafe precondition(s) violated: u64::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@anon.bcddbdd9e49de95895ea092998e0184f.1 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_4dc07e69e5d34e9d1484dfbbef0bb9b1 = private unnamed_addr constant [174 x i8] c"unsafe precondition(s) violated: invalid value for `char`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_c04ff4390ab75406df106611c778f0bd = private unnamed_addr constant [80 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\char\\methods.rs", align 1
@alloc_25406cc0e964b7161e3da8e3e3db2f6c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_c04ff4390ab75406df106611c778f0bd, [16 x i8] c"P\00\00\00\00\00\00\00%\07\00\00\0D\00\00\00" }>, align 8
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_299852982c5db63901a556a4e2fe0f7e = private unnamed_addr constant [88 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\iterator.rs", align 1
@alloc_7277ca8d30122b7203dfd66509383767 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_299852982c5db63901a556a4e2fe0f7e, [16 x i8] c"X\00\00\00\00\00\00\00\C1\07\00\00\09\00\00\00" }>, align 8
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_5c1a2f972552229672fc942406cfc298 = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts_mut requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_4bf8a7a4ce5b229f48ea69b79d51d2a6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\0B\02\00\00\17\00\00\00" }>, align 8
@alloc_e4656fac386fc9fc89c4fce8069d94fd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\0E\02\00\00\0D\00\00\00" }>, align 8
@alloc_31365cfefba383c4d2bf6b6a04cc10aa = private unnamed_addr constant [17 x i8] c"capacity overflow", align 1
@alloc_7d58e95f420f4f6740270666b024c4e6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\0A\02\00\002\00\00\00" }>, align 8
@alloc_d13cf662b812251b7a571ea4ee21d60d = private unnamed_addr constant [75 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\string.rs", align 1
@alloc_de1d28ae0ee49c8ef98856a2b8d3d72c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_d13cf662b812251b7a571ea4ee21d60d, [16 x i8] c"K\00\00\00\00\00\00\00\7F\05\00\00\1A\00\00\00" }>, align 8
@alloc_bc744aeb92fce05e521863a414e27269 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_d13cf662b812251b7a571ea4ee21d60d, [16 x i8] c"K\00\00\00\00\00\00\00}\05\00\00\1B\00\00\00" }>, align 8
@anon.bcddbdd9e49de95895ea092998e0184f.2 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] c"\01\00\00\00\00\00\00\80", [8 x i8] undef }>, align 8
@alloc_00e5a13bfec3eaffacf28cad02b1dee1 = private unnamed_addr constant [80 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\raw_vec\\mod.rs", align 1
@alloc_3d99a694a639f36d512dfe286335fb89 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_00e5a13bfec3eaffacf28cad02b1dee1, [16 x i8] c"P\00\00\00\00\00\00\00.\02\00\00\11\00\00\00" }>, align 8
@alloc_97d92cbf2a68a6ac45a1b13da79836e4 = private unnamed_addr constant [214 x i8] c"unsafe precondition(s) violated: slice::get_unchecked requires that the index is within the slice\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_caafbd3b6efc4fcf9b9faf4a56b98b1b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_d13cf662b812251b7a571ea4ee21d60d, [16 x i8] c"K\00\00\00\00\00\00\00\BB\04\00\00\12\00\00\00" }>, align 8
@alloc_f37fbc75e17c86e635d3eea7963e38ca = private unnamed_addr constant [40 x i8] c"=== Recursive Functions Demo (Rust) ===\0A", align 1
@alloc_64b4e0d406e0ca8c31ed4f02f8879fab = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f37fbc75e17c86e635d3eea7963e38ca, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_254e53376d9a3d12795cbeb474a36fad = private unnamed_addr constant [21 x i8] c"Factorial recursion:\0A", align 1
@alloc_a44dba70e82c2cc0fd33084e346830dc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_254e53376d9a3d12795cbeb474a36fad, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_c3a97d3d886e01933fd7c9ffca7ea006 = private unnamed_addr constant [30 x i8] c"Fibonacci sequence recursion:\0A", align 1
@alloc_61a49013bdcdb2f49625df5ab8e6dec9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c3a97d3d886e01933fd7c9ffca7ea006, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_5fad70da6cb69f91662cf4af6fc92c19 = private unnamed_addr constant [26 x i8] c"Power function recursion:\0A", align 1
@alloc_1ce3868f9729bcfcc5d9a8b506a67e61 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5fad70da6cb69f91662cf4af6fc92c19, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_d30bb78f5c69d1f262854ede69778f4b = private unnamed_addr constant [25 x i8] c"Sum of digits recursion:\0A", align 1
@alloc_705877947e7cebd5f272309701403661 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d30bb78f5c69d1f262854ede69778f4b, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_aa44c2676b415110b195cada06fbdcf3 = private unnamed_addr constant [21 x i8] c"Countdown recursion:\0A", align 1
@alloc_fdf02ba109da0885ec27c954c9dd2a45 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_aa44c2676b415110b195cada06fbdcf3, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_03cf37f7dba5eae0648c46ab21629307 = private unnamed_addr constant [20 x i8] c"Count up recursion:\0A", align 1
@alloc_9b67f4a17accd1eca87f0b39c86ced54 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_03cf37f7dba5eae0648c46ab21629307, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_efac8044d3e7d95d354e0d51307a1959 = private unnamed_addr constant [21 x i8] c"Array sum recursion:\0A", align 1
@alloc_eccd77dbb1a2e86790a0b7f1e77977b4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_efac8044d3e7d95d354e0d51307a1959, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_175080a46f3a116ebea23460f01ffcea = private unnamed_addr constant [25 x i8] c"17_recursive_functions.rs", align 1
@alloc_f8cffa1741cdc4aef3f502adbb177e9e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\006\00\00\00\10\00\00\00" }>, align 8
@alloc_1ec47609f61d896dcd8c05bd1cc92c03 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\007\00\00\00\10\00\00\00" }>, align 8
@alloc_f0ecbe073f1733bdf56f0c94effd1317 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\008\00\00\00\10\00\00\00" }>, align 8
@alloc_e1f512007ace5e57d7001cc978a9a8cc = private unnamed_addr constant [10 x i8] c"array_sum(", align 1
@alloc_12470cffb0ed9f3cc12c6066ef64d57b = private unnamed_addr constant [4 x i8] c") = ", align 1
@alloc_be4207bbf8d66e1a5d67360dd68642db = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e1f512007ace5e57d7001cc978a9a8cc, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d9151c58d67b5cd0d2eb145d7ee9712f = private unnamed_addr constant [15 x i8] c"GCD recursion:\0A", align 1
@alloc_d38248cc105dde06c01a8906bfa7d26a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d9151c58d67b5cd0d2eb145d7ee9712f, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_f172265c4bc8143ae9c362f741e4a08d = private unnamed_addr constant [25 x i8] c"Binary search recursion:\0A", align 1
@alloc_56c074e90d70bf6e71cf4d7dd670b972 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f172265c4bc8143ae9c362f741e4a08d, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_d5e35d4623f1fa94b2d1aa54a9c77a1d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00J\00\00\00\18\00\00\00" }>, align 8
@alloc_2bd212b52a631860ec6209a1be0ff572 = private unnamed_addr constant [20 x i8] c"Searching in array: ", align 1
@alloc_5eb73c5548ea3e84d9d0be2033891f24 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2bd212b52a631860ec6209a1be0ff572, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_efaf91c68d48e08ad62c04ab266c4265 = private unnamed_addr constant [27 x i8] c"String reversal recursion:\0A", align 1
@alloc_db99fc5e0adab0a98b16186ef439222b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_efaf91c68d48e08ad62c04ab266c4265, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_7c732fad6c650313539f89243f1f4d8e = private unnamed_addr constant [5 x i8] c"world", align 1
@alloc_d233ed356d827862a215c86f4113316d = private unnamed_addr constant [4 x i8] c"rust", align 1
@alloc_1b9326da9a3db45a9d373b3046406a76 = private unnamed_addr constant [9 x i8] c"recursion", align 1
@alloc_3827bbb606bbdb0f0c926011c14ccc34 = private unnamed_addr constant [1 x i8] c"a", align 1
@alloc_1a2b9f3efbe1a8edd339fa75af2334ed = private unnamed_addr constant [5 x i8] c"hello", align 1
@alloc_6fa8f33a2d92c2b2a3e13b3b0dbb85f4 = private unnamed_addr constant [28 x i8] c"Palindrome check recursion:\0A", align 1
@alloc_41d2655ec8882a42263885fcc9f24a8b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6fa8f33a2d92c2b2a3e13b3b0dbb85f4, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_6a15c540cefe4c6587e04d65caef7aa0 = private unnamed_addr constant [5 x i8] c"madam", align 1
@alloc_0111fe86b05ecb68d77beaa0646d3caa = private unnamed_addr constant [5 x i8] c"level", align 1
@alloc_191c37045ef96d59740c75ae643e5db1 = private unnamed_addr constant [7 x i8] c"racecar", align 1
@alloc_99eea555b5df5980bbe1a63aac7646b3 = private unnamed_addr constant [27 x i8] c"Tree traversal simulation:\0A", align 1
@alloc_f0511044a6651f6750f11d280e801c63 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_99eea555b5df5980bbe1a63aac7646b3, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_7b6a5662531be83abbac27b3c0d5a913 = private unnamed_addr constant [34 x i8] c"Hanoi towers recursion (3 disks):\0A", align 1
@alloc_003ad12aefee4d69adef1eb46f04345e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_7b6a5662531be83abbac27b3c0d5a913, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_bb29b711881b6e19df2f012c18399711 = private unnamed_addr constant [39 x i8] c"Nested recursion (Ackermann function):\0A", align 1
@alloc_a494a1f0877ec3d3a256ebdd65271b27 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_bb29b711881b6e19df2f012c18399711, [8 x i8] c"'\00\00\00\00\00\00\00" }>, align 8
@alloc_4bd9c1948407373a60282a07421e0a66 = private unnamed_addr constant [35 x i8] c"Mutual recursion (even/odd check):\0A", align 1
@alloc_2e9c7c2afd1acf724eab8fbd301888c6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4bd9c1948407373a60282a07421e0a66, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_056c76d224a32fd8b4c5664730ca2e99 = private unnamed_addr constant [40 x i8] c"=== End of Recursive Functions Demo ===\0A", align 1
@alloc_b1c7eddc56bd7db5575cd5ad8b2094c4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_056c76d224a32fd8b4c5664730ca2e99, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_4924c72fd2ef9d1b07320c5e5e786b50 = private unnamed_addr constant [15 x i8] c"is_even_mutual(", align 1
@alloc_c71c45d962961e1023fc02b723cfc07f = private unnamed_addr constant [16 x i8] c", is_odd_mutual(", align 1
@alloc_5ed072b1be6621704702a5ae35738efe = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4924c72fd2ef9d1b07320c5e5e786b50, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_c71c45d962961e1023fc02b723cfc07f, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_49468de30bc6e46cba3832cda26aef5d = private unnamed_addr constant [10 x i8] c"ackermann(", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_ede1707ef0f484e2941bcc042780d668 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_49468de30bc6e46cba3832cda26aef5d, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b3db7c043f0fcf977c22b51b746f689b = private unnamed_addr constant [15 x i8] c"is_palindrome('", align 1
@alloc_7148c98db227d577ac0f27140490c8b3 = private unnamed_addr constant [5 x i8] c"') = ", align 1
@alloc_09b805f8faac1ab4caaa972084f924c4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b3db7c043f0fcf977c22b51b746f689b, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_7148c98db227d577ac0f27140490c8b3, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0f3767967eb877f74806634b937e2ba1 = private unnamed_addr constant [16 x i8] c"reverse_string('", align 1
@alloc_e4a86325c146fac4d74f7955ebe85ba3 = private unnamed_addr constant [6 x i8] c"') = '", align 1
@alloc_12a9d76f5dbcbafc68e14c1df740ed24 = private unnamed_addr constant [2 x i8] c"'\0A", align 1
@alloc_b379d42aa66d04767471318a97b55db7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0f3767967eb877f74806634b937e2ba1, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_e4a86325c146fac4d74f7955ebe85ba3, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_12a9d76f5dbcbafc68e14c1df740ed24, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_79b239743907a87469f0ecba537fedcc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00O\00\00\006\00\00\00" }>, align 8
@alloc_c19874177af27670db90baea263b3948 = private unnamed_addr constant [14 x i8] c"binary_search(", align 1
@alloc_de8bf13eb2a239728eb3531ddc349e6a = private unnamed_addr constant [14 x i8] c") = not found\0A", align 1
@alloc_fb69082d1ea7125e2b0811b8a9f81b92 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c19874177af27670db90baea263b3948, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_de8bf13eb2a239728eb3531ddc349e6a, [8 x i8] c"\0E\00\00\00\00\00\00\00" }>, align 8
@alloc_de62d87e208e584fe4af57c99c809392 = private unnamed_addr constant [19 x i8] c") = found at index ", align 1
@alloc_e5f1a1511db0fd5e30967b04b9b68f64 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c19874177af27670db90baea263b3948, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_de62d87e208e584fe4af57c99c809392, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b1768594da88a00b49760efac379c4ba = private unnamed_addr constant [4 x i8] c"gcd(", align 1
@alloc_07cb76ecca8e1aa156f072cc2d146651 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b1768594da88a00b49760efac379c4ba, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_22af958295fccce5496bebeb45fd0f04 = private unnamed_addr constant [14 x i8] c"sum_of_digits(", align 1
@alloc_39f2698779bf62590e50a5c459d2e7f4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_22af958295fccce5496bebeb45fd0f04, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1302b087b851c4b693625de4fc94d7ec = private unnamed_addr constant [6 x i8] c"power(", align 1
@alloc_86a2165cff3a77f599eab9643e2208d0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1302b087b851c4b693625de4fc94d7ec, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_035256de1812efbb9a84946e393bbe8e = private unnamed_addr constant [10 x i8] c"fibonacci(", align 1
@alloc_e3c8132c8b135d9d43572e3bb9140baf = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_035256de1812efbb9a84946e393bbe8e, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a641db5190d74a010c171c4b04f85d0c = private unnamed_addr constant [10 x i8] c"factorial(", align 1
@alloc_95c9d1d8c902af68a1b8a497369a019d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a641db5190d74a010c171c4b04f85d0c, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0113dd7f9c1cf13543958923caa8e4fb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\8B\00\00\00\17\00\00\00" }>, align 8
@alloc_718fcb6f22c2861106a7114952a201ad = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\8B\00\00\00\09\00\00\00" }>, align 8
@alloc_6e5e50876a382ffadf3a4d043f83bdf2 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\94\00\00\00\18\00\00\00" }>, align 8
@alloc_640aeb1c6689c71be24aeb2af148a12d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\94\00\00\00+\00\00\00" }>, align 8
@alloc_e6b4867eafdfa198491e240bc4f47096 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\94\00\00\00\0E\00\00\00" }>, align 8
@alloc_c1f0796db8def95df74e4560a5c8b19f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\9D\00\00\00\1C\00\00\00" }>, align 8
@alloc_1c305380add1f2fe76f46ff2ee475d8c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\9D\00\00\00\09\00\00\00" }>, align 8
@alloc_d519354ffa0e410ff74d7413ba741452 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\A6\00\00\00\09\00\00\00" }>, align 8
@alloc_ce120d832bcddfcaa352e59d092bf849 = private unnamed_addr constant [22 x i8] c"  Countdown finished!\0A", align 1
@alloc_6a3925fbd3809c89959ae12a2b6708af = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ce120d832bcddfcaa352e59d092bf849, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_6f7145fb55c18fac02ee19ffbfc0948d = private unnamed_addr constant [13 x i8] c"  Countdown: ", align 1
@alloc_297da3bc62bdd64fe8d471f72592f9a5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6f7145fb55c18fac02ee19ffbfc0948d, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_564530bae0fe4666106227118442ad16 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\AE\00\00\00\13\00\00\00" }>, align 8
@alloc_2b5db1393c0228754888c5020d3f1569 = private unnamed_addr constant [21 x i8] c"  Count up finished!\0A", align 1
@alloc_dfbbf380f23c0db6cd43dc9a443ac1d8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2b5db1393c0228754888c5020d3f1569, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_852f42b72d6cb8b16d38ded4d4e642bf = private unnamed_addr constant [12 x i8] c"  Count up: ", align 1
@alloc_e2de23853e963fac850c6cf76dea7de7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_852f42b72d6cb8b16d38ded4d4e642bf, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_dca679042b1f4e5dbe81467678e96ccf = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\B8\00\00\00\12\00\00\00" }>, align 8
@alloc_3e3a5f47fd32fd61f670c15bb51935de = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\C3\00\00\00\09\00\00\00" }>, align 8
@alloc_f07c07076c6a606ea456881b010e99e5 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\C3\00\00\00%\00\00\00" }>, align 8
@alloc_7e25478eab46d53058b79dd0afa7b915 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\CC\00\00\00\10\00\00\00" }>, align 8
@alloc_0c2b227309ce0ebba6b5b490188a067b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\D6\00\00\00\16\00\00\00" }>, align 8
@alloc_06b6d1bae5667081f1b66caa9e04d600 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\D6\00\00\00\0F\00\00\00" }>, align 8
@alloc_69e409a5ad668a7a87b7c737e2b2dee6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\D8\00\00\00\08\00\00\00" }>, align 8
@alloc_1fa34c670e55d0dbb5ad0a56cbc99734 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\DA\00\00\00\0F\00\00\00" }>, align 8
@alloc_b3830c14bc86a1700c2ddadf9d0bb34d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\E1\00\00\00$\00\00\00" }>, align 8
@alloc_506f4e0036e3d57433c2073cbbc89268 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\DE\00\00\00.\00\00\00" }>, align 8
@alloc_951f62198ca129b96d53ae30a55fa068 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\EB\00\00\00\1F\00\00\00" }>, align 8
@alloc_5b3955e00fee21a400912ec48a8c010d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\EB\00\00\00\1E\00\00\00" }>, align 8
@alloc_bf8d193f36445f3f20fd24696f56c0a3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\EC\00\00\00$\00\00\00" }>, align 8
@alloc_01a5805d15aca57b623d353a5f5bd8b4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\EC\00\00\00!\00\00\00" }>, align 8
@alloc_bd1be8c7a73e5392e9c02e2643330a1c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer }>, align 8
@alloc_2517f977ba6594f07dd4cc8a239dd270 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\F4\00\00\00%\00\00\00" }>, align 8
@alloc_b5387875f27eb0cdddcc435e20e76119 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\FA\00\00\00\0F\00\00\00" }>, align 8
@alloc_a4824f785cce21a0d0e93c2e80ccb6c3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\FA\00\00\00\1E\00\00\00" }>, align 8
@alloc_2a3f396d9430f7062b8aca74a305ac66 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\FD\00\00\00%\00\00\00" }>, align 8
@alloc_9fa2689b590f9447e4b9f5a35f1e1a8b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\FD\00\00\00/\00\00\00" }>, align 8
@alloc_34310cabf6554cc6123dbb21242f43b8 = private unnamed_addr constant [2 x i8] c"  ", align 1
@alloc_da6dc6342c20f02b1ec275ba36e6456d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\04\01\00\00\22\00\00\00" }>, align 8
@alloc_1d843e677215e28704cc1bf93c8199aa = private unnamed_addr constant [6 x i8] c"Level ", align 1
@alloc_eb68e4306e26e7c43562c187f0b70d5a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_1d843e677215e28704cc1bf93c8199aa, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_81da280dd8e0ed907ba6f3007be14736 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\06\01\00\00\1B\00\00\00" }>, align 8
@alloc_9c6d4e420ad0c5e89322e50ca7661d11 = private unnamed_addr constant [19 x i8] c"  Move disk 1 from ", align 1
@alloc_f3d1c370466b0ffc821b7031a6d613f4 = private unnamed_addr constant [4 x i8] c" to ", align 1
@alloc_d7cc52af69a872c3e20f995d75075d4d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9c6d4e420ad0c5e89322e50ca7661d11, [8 x i8] c"\13\00\00\00\00\00\00\00", ptr @alloc_f3d1c370466b0ffc821b7031a6d613f4, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c153f61d407b6c73ad2f5f60ec14c0a6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\0F\01\00\00\0F\00\00\00" }>, align 8
@alloc_51a68709f220fdebe7ea643876769d21 = private unnamed_addr constant [12 x i8] c"  Move disk ", align 1
@alloc_39c58f5794dad07da32780d4894e6b2f = private unnamed_addr constant [6 x i8] c" from ", align 1
@alloc_4a3195e5f4bcb3f99768b1b295930f4a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_51a68709f220fdebe7ea643876769d21, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_39c58f5794dad07da32780d4894e6b2f, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_f3d1c370466b0ffc821b7031a6d613f4, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_539284d65c9eed23448004dec9ca96cc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\11\01\00\00\0F\00\00\00" }>, align 8
@alloc_ffb8b440afd214261f508cfb9fa31fc9 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\18\01\00\00\13\00\00\00" }>, align 8
@alloc_6d64d7825a301a49ca1d8e43e23c199c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\19\01\00\00\1D\00\00\00" }>, align 8
@alloc_81861d80a9682a30573e7c02b4d210cb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\1A\01\00\00\1D\00\00\00" }>, align 8
@alloc_3ac0073a3129e30c98e9e98ea35a1e4e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00\1A\01\00\001\00\00\00" }>, align 8
@alloc_77e61a8320ca4c31d86951c2a64a391a = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00#\01\00\00\17\00\00\00" }>, align 8
@alloc_e6f5e33d1a69d1ee46e70c3be92ea8ec = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_175080a46f3a116ebea23460f01ffcea, [16 x i8] c"\19\00\00\00\00\00\00\00+\01\00\00\18\00\00\00" }>, align 8

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: uwtable
define internal void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h225282b811881e78E"(ptr %self.0, ptr %self.1, ptr align 8 %f) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_9 = alloca [1 x i8], align 1
  %_8 = alloca [1 x i8], align 1
  store i8 1, ptr %_9, align 1
  store i8 1, ptr %_8, align 1
; invoke core::iter::traits::iterator::Iterator::map
  %0 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator3map17h1e967219ff17bd0aE(ptr %self.0, ptr %self.1)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  %1 = load i8, ptr %_8, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb4, label %bb2

funclet_bb5:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  %self.01 = extractvalue { ptr, ptr } %0, 0
  %self.12 = extractvalue { ptr, ptr } %0, 1
  store i8 0, ptr %_9, align 1
  store i8 0, ptr %_8, align 1
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  invoke void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17he921c1ce2fae337fE"(ptr %self.01, ptr %self.12, ptr align 8 %f)
          to label %bb7 unwind label %funclet_bb5

bb7:                                              ; preds = %bb1
  ret void

bb2:                                              ; preds = %bb4, %bb5
  %3 = load i8, ptr %_9, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb6, label %bb3

bb4:                                              ; preds = %bb5
  br label %bb2

bb3:                                              ; preds = %bb6, %bb2
  cleanupret from %cleanuppad unwind to caller

bb6:                                              ; preds = %bb2
  br label %bb3
}

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: uwtable
define internal void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h0e0ee5abb1edea66E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17hcdc0980d9d3439b9E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self)
  ret void
}

; <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %end, i64 %len) unnamed_addr #1 {
start:
  %_3 = icmp ule i64 %end, %len
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_5a8fdd84b3281310cbf6b74bb6bf0065, i64 218) #20
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h3020b3a0fa807f30E"(ptr align 4 %self) unnamed_addr #2 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp ule i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp ult i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <u32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$u32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hb4897f5aebe37fb2E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hc0c7b3d4c9020ccaE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %_6 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %1 = load i8, ptr %0, align 8
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_13, align 8
  %_0.i = icmp ule i64 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i1 = load i64, ptr %self, align 8
  %_4.i2 = load i64, ptr %_5, align 8
  %_0.i3 = icmp ult i64 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i64 0, ptr %_0, align 8
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 16
  store i8 1, ptr %2, align 8
  %3 = load i64, ptr %self, align 8
  store i64 %3, ptr %_6, align 8
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %self, align 8
; call <u64 as core::iter::range::Step>::forward_unchecked
  %n = call i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h9f2a472aa30d0c81E"(i64 %_8, i64 1)
  %4 = load i64, ptr %self, align 8
  store i64 %4, ptr %_6, align 8
  store i64 %n, ptr %self, align 8
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i64, ptr %_6, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  store i64 1, ptr %_0, align 8
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i64, ptr %_0, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = insertvalue { i64, i64 } poison, i64 %7, 0
  %11 = insertvalue { i64, i64 } %10, i64 %9, 1
  ret { i64, i64 } %11
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h0a7e5a1cfa4fcec8E"(ptr sret([24 x i8]) align 8 %_0, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_20 = alloca [1 x i8], align 1
  %vector1 = alloca [24 x i8], align 8
  %_8 = alloca [24 x i8], align 8
  %_3 = alloca [4 x i8], align 4
  %vector = alloca [24 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  store i8 1, ptr %_20, align 1
; invoke <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::next
  %5 = invoke i32 @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha375933c48a1900cE"(ptr align 8 %iterator)
          to label %bb1 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %6 = load i8, ptr %_20, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb10, label %bb8

funclet_bb11:                                     ; preds = %bb9, %bb7, %start
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb1:                                              ; preds = %start
  store i32 %5, ptr %_3, align 4
  %8 = load i32, ptr %_3, align 4
  %9 = icmp eq i32 %8, 1114112
  %_5 = select i1 %9, i64 0, i64 1
  %10 = trunc nuw i64 %_5 to i1
  br i1 %10, label %bb3, label %bb12

bb3:                                              ; preds = %bb1
  %element = load i32, ptr %_3, align 4
; invoke <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17ha9306b1fc11bff0bE"(ptr sret([24 x i8]) align 8 %_8, ptr align 8 %iterator)
          to label %bb4 unwind label %funclet_bb9

bb12:                                             ; preds = %bb1
  store i64 0, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr getelementptr (i8, ptr null, i64 4), ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %12, align 8
  br label %bb6

bb6:                                              ; preds = %bb5, %bb12
  ret void

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad2 unwind label %funclet_bb11

funclet_bb9:                                      ; preds = %bb14, %bb4, %bb3
  %cleanuppad2 = cleanuppad within none []
  br label %bb9

bb4:                                              ; preds = %bb3
  %lower = load i64, ptr %_8, align 8
  %13 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %13, ptr %3, align 8
  %v2 = load i64, ptr %3, align 8
; invoke core::cmp::Ord::max
  %initial_capacity = invoke i64 @_ZN4core3cmp3Ord3max17h3396a908c279b812E(i64 4, i64 %v2)
          to label %bb14 unwind label %funclet_bb9

bb14:                                             ; preds = %bb4
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %14 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h0a8e554283839faeE"(i64 %initial_capacity, i64 4, i64 4, ptr align 8 %2)
          to label %bb15 unwind label %funclet_bb9

bb15:                                             ; preds = %bb14
  %_26.0 = extractvalue { i64, ptr } %14, 0
  %_26.1 = extractvalue { i64, ptr } %14, 1
  store i64 %_26.0, ptr %vector1, align 8
  %15 = getelementptr inbounds i8, ptr %vector1, i64 8
  store ptr %_26.1, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 0, ptr %16, align 8
  %17 = getelementptr inbounds i8, ptr %vector1, i64 8
  %_27 = load ptr, ptr %17, align 8
  store i32 %element, ptr %_27, align 4
  %18 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 1, ptr %18, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %vector, ptr align 8 %vector1, i64 24, i1 false)
  store i8 0, ptr %_20, align 1
  %_19.0 = load ptr, ptr %iterator, align 8
  %19 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_19.1 = load ptr, ptr %19, align 8
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h3bb44010def19216E"(ptr align 8 %vector, ptr %_19.0, ptr %_19.1, ptr align 8 %2)
          to label %bb5 unwind label %funclet_bb7

bb7:                                              ; preds = %funclet_bb7
; call core::ptr::drop_in_place<alloc::vec::Vec<char>>
  call void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %vector) #21 [ "funclet"(token %cleanuppad3) ]
  cleanupret from %cleanuppad3 unwind label %funclet_bb11

funclet_bb7:                                      ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb7

bb5:                                              ; preds = %bb15
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  br label %bb6

bb2:                                              ; No predecessors!
  unreachable

bb8:                                              ; preds = %bb10, %bb11
  cleanupret from %cleanuppad unwind to caller

bb10:                                             ; preds = %bb11
  br label %bb8
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<&T,core::slice::iter::Iter<T>>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN132_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$$RF$T$C$core..slice..iter..Iter$LT$T$GT$$GT$$GT$11spec_extend17he1e8d3f9266f5698E"(ptr align 8 %self, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 {
start:
  %len = alloca [8 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %3 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %3, align 8
  %self1 = load ptr, ptr %iterator, align 8
  br label %bb3

bb3:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_11 = load ptr, ptr %4, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %5 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h66aed26f988d01d0E"(ptr %_11, ptr %self1)
  store i64 %5, ptr %len, align 8
  br label %bb4

bb4:                                              ; preds = %bb3
  br label %bb5

bb5:                                              ; preds = %bb4
  %6 = load i64, ptr %len, align 8
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %self1, i64 1, i64 1, i64 %6) #22
  br label %bb7

bb7:                                              ; preds = %bb5
  %slice.1 = load i64, ptr %len, align 8
; call alloc::vec::Vec<T,A>::append_elements
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$15append_elements17h03a761ec8e5fcf11E"(ptr align 8 %self, ptr %self1, i64 %slice.1, ptr align 8 %2)
  ret void

bb2:                                              ; No predecessors!
  unreachable
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17hc500bcec324e4b2cE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17he58b19c6c9f6198eE"(ptr align 8 %_1) unnamed_addr #2 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17he0df61b86db84de3E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hb0ef32e964c8e2fbE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17he0df61b86db84de3E(ptr %f) unnamed_addr #3 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h929a882a278b70c8E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h8607b37ae0a5ab7cE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::num::<impl core::fmt::Debug for i32>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h5a040d9448637ff8E"(ptr align 4 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17he01afb12f1229b50E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <T as alloc::string::ToString>::to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17h4da18e2ad1423aafE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
; call <str as alloc::string::SpecToString>::spec_to_string
  call void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17had25128acdde41d6E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1)
  ret void
}

; <u32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$u32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hb4897f5aebe37fb2E"(i32 %start1, i64 %n) unnamed_addr #2 {
start:
  %rhs = trunc i64 %n to i32
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl u32>::unchecked_add::precondition_check
  call void @"_ZN4core3num21_$LT$impl$u20$u32$GT$13unchecked_add18precondition_check17h7c27feb953c82e87E"(i32 %start1, i32 %rhs) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = add nuw i32 %start1, %rhs
  ret i32 %_0
}

; <u64 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h9f2a472aa30d0c81E"(i64 %start1, i64 %n) unnamed_addr #2 {
start:
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl u64>::unchecked_add::precondition_check
  call void @"_ZN4core3num21_$LT$impl$u20$u64$GT$13unchecked_add18precondition_check17heb88b33f1f18869bE"(i64 %start1, i64 %n) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = add nuw i64 %start1, %n
  ret i64 %_0
}

; <[T] as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h9628d4aef0a0a95aE"(ptr align 4 %self.0, i64 %self.1, ptr align 8 %f) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %_5 = alloca [16 x i8], align 8
; call core::fmt::Formatter::debug_list
  call void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8 %_5, ptr align 8 %f)
  br label %bb5

bb5:                                              ; preds = %start
  %_11 = getelementptr inbounds nuw i32, ptr %self.0, i64 %self.1
  store ptr %_11, ptr %end_or_len, align 8
  br label %bb6

bb6:                                              ; preds = %bb5
  %_13 = load ptr, ptr %end_or_len, align 8
; call core::fmt::builders::DebugList::entries
  %_3 = call align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h20b6555830902070E(ptr align 8 %_5, ptr %self.0, ptr %_13)
; call core::fmt::builders::DebugList::finish
  %_0 = call zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8 %_3)
  ret i1 %_0

bb4:                                              ; No predecessors!
  unreachable
}

; core::intrinsics::write_bytes::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics11write_bytes18precondition_check17ha5b10650f23cb0adE(ptr %addr, i64 %align, i1 zeroext %zero_size) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %zero_size, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_dd79dfae92e8fdc23813c4c7a1b7cf72, i64 228) #20
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h3765eba5702206d6E(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #20
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17ha1c0eeb4c3ca30d4E() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::Ord::max
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core3cmp3Ord3max17h3396a908c279b812E(i64 %0, i64 %1) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %other = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  store i64 %0, ptr %self, align 8
  store i64 %1, ptr %other, align 8
  store i8 1, ptr %_6, align 1
  %_3.i = load i64, ptr %other, align 8
  %_4.i = load i64, ptr %self, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load i64, ptr %other, align 8
  store i64 %2, ptr %_0, align 8
  %3 = load i8, ptr %_6, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %5 = load i64, ptr %self, align 8
  store i64 %5, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load i64, ptr %_0, align 8
  ret i64 %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$char$u20$as$u20$core..fmt..Display$GT$3fmt17h51cc1da6341ff44fE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h91f4a535cacdb1caE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h9ffeb419df46888aE(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hbba4d01832b4100aE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hfa1097c3fbf8a383E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hdf87e42f0895a2dfE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hc1e393a05464dbd2E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17he01afb12f1229b50E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hd1f71f5107d3da0dE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h2e45205524cc218aE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_debug
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_debug17h1ba06b3616f00608E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h839449b19c56ec6dE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::num::<impl core::fmt::Debug for i32>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h5a040d9448637ff8E"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #2 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = getelementptr inbounds i8, ptr %f, i64 16
  %_4 = load i32, ptr %0, align 8
  %_3 = and i32 %_4, 33554432
  %1 = icmp eq i32 %_3, 0
  br i1 %1, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %f, i64 16
  %_6 = load i32, ptr %2, align 8
  %_5 = and i32 %_6, 67108864
  %3 = icmp eq i32 %_5, 0
  br i1 %3, label %bb4, label %bb3

bb1:                                              ; preds = %start
; call core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
  %4 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4 %self, ptr align 8 %f)
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb6

bb4:                                              ; preds = %bb2
; call core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
  %6 = call zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4 %self, ptr align 8 %f)
  %7 = zext i1 %6 to i8
  store i8 %7, ptr %_0, align 1
  br label %bb5

bb3:                                              ; preds = %bb2
; call core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
  %8 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4 %self, ptr align 8 %f)
  %9 = zext i1 %8 to i8
  store i8 %9, ptr %_0, align 1
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  br label %bb6

bb6:                                              ; preds = %bb1, %bb5
  %10 = load i8, ptr %_0, align 1
  %11 = trunc nuw i8 %10 to i1
  ret i1 %11
}

; core::fmt::builders::DebugList::entries
; Function Attrs: uwtable
define internal align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h20b6555830902070E(ptr align 8 %self, ptr %entries.0, ptr %entries.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %entry = alloca [8 x i8], align 8
  %_5 = alloca [8 x i8], align 8
  %iter = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd0f2f2f9e23623a5E"(ptr %entries.0, ptr %entries.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
  store ptr %_3.0, ptr %iter, align 8
  %1 = getelementptr inbounds i8, ptr %iter, i64 8
  store ptr %_3.1, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %bb8, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h62a60b735bef66c8E"(ptr align 8 %iter)
          to label %bb3 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb10, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb3:                                              ; preds = %bb2
  store ptr %2, ptr %_5, align 8
  %3 = load ptr, ptr %_5, align 8
  %4 = ptrtoint ptr %3 to i64
  %5 = icmp eq i64 %4, 0
  %_7 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_7 to i1
  br i1 %6, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %7 = load ptr, ptr %_5, align 8
  store ptr %7, ptr %entry, align 8
; invoke core::fmt::builders::DebugList::entry
  %_9 = invoke align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8 %self, ptr align 1 %entry, ptr align 8 @vtable.1)
          to label %bb7 unwind label %funclet_bb10

bb6:                                              ; preds = %bb3
  ret ptr %self

bb10:                                             ; preds = %funclet_bb10
  cleanupret from %cleanuppad1 unwind label %funclet_bb11

funclet_bb10:                                     ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb10

bb7:                                              ; preds = %bb5
  br label %bb8

bb8:                                              ; preds = %bb7
  br label %bb2

bb4:                                              ; No predecessors!
  unreachable
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h6b8f50bbcd2996bfE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h71e372ae6819746eE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h9ecf983bf86ff6fbE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hdcf55d5e1a81c2adE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 5, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 4, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #2 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl u32>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num21_$LT$impl$u20$u32$GT$13unchecked_add18precondition_check17h7c27feb953c82e87E"(i32 %lhs, i32 %rhs) unnamed_addr #1 {
start:
  %0 = call { i32, i1 } @llvm.uadd.with.overflow.i32(i32 %lhs, i32 %rhs)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a6a0cc8156fe455996de64a9d05b1dfe, i64 184) #20
  unreachable
}

; core::num::<impl u64>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num21_$LT$impl$u20$u64$GT$13unchecked_add18precondition_check17heb88b33f1f18869bE"(i64 %lhs, i64 %rhs) unnamed_addr #1 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_fc445f6abf67cf4b683577fd3aaed336, i64 184) #20
  unreachable
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %lhs, i64 %rhs) unnamed_addr #1 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #20
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h40008f4ebd339039E"(i64 %lhs, i64 %rhs) unnamed_addr #1 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #20
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h7f8488775b7370d5E"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #2 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hbdbbbebcd6b92a30E"(ptr sret([24 x i8]) align 8 %_0, i64 %start1, i64 %end) unnamed_addr #2 {
start:
  store i64 %start1, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %end, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i8 0, ptr %1, align 8
  ret void
}

; core::ops::function::FnMut::call_mut
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function5FnMut8call_mut17h70421ce8ed52e21fE(ptr align 1 %_1, ptr align 4 %0) unnamed_addr #2 {
start:
  %_2 = alloca [8 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %1 = load ptr, ptr %_2, align 8
  %_0.i = load i32, ptr %1, align 4
  ret i32 %_0.i
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hc5cb834cde4b4f1dE"(ptr %_1) unnamed_addr #2 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h05310100a9a48ef5E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h05310100a9a48ef5E(ptr %0) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17he58b19c6c9f6198eE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h4b7658aa07787b52E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #2 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load i64, ptr %4, align 8
; call alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
  call void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h3cc15629e130bb9fE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %3, i64 %5)
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h929a882a278b70c8E(ptr %_1) unnamed_addr #2 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17he734ac42d3466b08E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #20
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<&i32>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr28drop_in_place$LT$$RF$i32$GT$17h3d31c58112c1f7e3E"(ptr align 8 %_1) unnamed_addr #2 {
start:
  ret void
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17haac22ac4d7054c6eE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17haac22ac4d7054c6eE"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9173b56814e08f4aE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h21b2cbe9e4804f0cE"(ptr align 8 %_1) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h21b2cbe9e4804f0cE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h1bf952cc210f6fbdE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17hd0ad20730e98aebcE"(ptr align 8 %_1) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17hd0ad20730e98aebcE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<char>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2c36e0c0c845543eE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<char>>
  call void @"_ZN4core3ptr55drop_in_place$LT$alloc..raw_vec..RawVec$LT$char$GT$$GT$17h3bcc05ee51a3e6b3E"(ptr align 8 %_1) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<char>>
  call void @"_ZN4core3ptr55drop_in_place$LT$alloc..raw_vec..RawVec$LT$char$GT$$GT$17h3bcc05ee51a3e6b3E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h21b2cbe9e4804f0cE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hace49651a0e0c42dE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17hd0ad20730e98aebcE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9c870d60e9d64f76E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<char>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr55drop_in_place$LT$alloc..raw_vec..RawVec$LT$char$GT$$GT$17h3bcc05ee51a3e6b3E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd9eca76d20fccb42E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<i32,5_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$i32$C$5_usize$GT$$GT$17h55d98016f7bf68efE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h85e8c6f7149a91eeE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<u32,5_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$u32$C$5_usize$GT$$GT$17hba2bc5985fc56f32E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb65ef98c7deb33abE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<&str,5_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr73drop_in_place$LT$core..array..iter..IntoIter$LT$$RF$str$C$5_usize$GT$$GT$17hf8cf676cfdb4f768E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h40a9790f4a87f792E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<(i32,u32),5_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$i32$C$u32$RP$$C$5_usize$GT$$GT$17hc8ee7abb4530aca2E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h78a036c96c8a1b15E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),4_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$4_usize$GT$$GT$17hb613f0a3fb8fdb8dE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha5a1fc8299fa8149E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),6_usize>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$6_usize$GT$$GT$17hcd1090275dc54866E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
  call void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h1976cd7f52d2d320E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hda4b5af1158ef0b0E"(ptr align 8 %_1) unnamed_addr #2 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %ptr) unnamed_addr #1 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #20
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h0f4405aaac6c40d2E"(ptr %self, ptr %subtracted) unnamed_addr #2 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hfb4cff25886033ecE"(ptr %self, ptr %subtracted) #22
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 4
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #23
  unreachable
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h66aed26f988d01d0E"(ptr %self, ptr %subtracted) unnamed_addr #2 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hfb4cff25886033ecE"(ptr %self, ptr %subtracted) #22
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 1
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #23
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hfb4cff25886033ecE"(ptr %this, ptr %origin) unnamed_addr #1 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #20
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::str::validations::next_code_point
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @_ZN4core3str11validations15next_code_point17h494d6243edf83fdfE(ptr align 8 %bytes) unnamed_addr #2 {
start:
  %self3 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %ch = alloca [4 x i8], align 4
  %self1 = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 4
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd528ceca61c41839E"(ptr align 8 %bytes)
  store ptr %0, ptr %self, align 8
  %1 = load ptr, ptr %self, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_29 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_29 to i1
  br i1 %4, label %bb14, label %bb13

bb14:                                             ; preds = %start
  %v = load ptr, ptr %self, align 8
  store ptr %v, ptr %_3, align 8
  %val = load ptr, ptr %_3, align 8
  %x = load i8, ptr %val, align 1
  %_6 = icmp ult i8 %x, -128
  br i1 %_6, label %bb3, label %bb4

bb13:                                             ; preds = %start
  %5 = load i32, ptr @anon.bcddbdd9e49de95895ea092998e0184f.1, align 4
  %6 = load i32, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.1, i64 4), align 4
  store i32 %5, ptr %_0, align 4
  %7 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %6, ptr %7, align 4
  br label %bb12

bb12:                                             ; preds = %bb3, %bb11, %bb13
  %8 = load i32, ptr %_0, align 4
  %9 = getelementptr inbounds i8, ptr %_0, i64 4
  %10 = load i32, ptr %9, align 4
  %11 = insertvalue { i32, i32 } poison, i32 %8, 0
  %12 = insertvalue { i32, i32 } %11, i32 %10, 1
  ret { i32, i32 } %12

bb4:                                              ; preds = %bb14
  %_31 = and i8 %x, 31
  %init = zext i8 %_31 to i32
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %13 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd528ceca61c41839E"(ptr align 8 %bytes)
  store ptr %13, ptr %self1, align 8
  %14 = load ptr, ptr %self1, align 8
  %15 = ptrtoint ptr %14 to i64
  %16 = icmp eq i64 %15, 0
  %_32 = select i1 %16, i64 0, i64 1
  %17 = trunc nuw i64 %_32 to i1
  br i1 %17, label %bb16, label %bb15

bb3:                                              ; preds = %bb14
  %_7 = zext i8 %x to i32
  %18 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_7, ptr %18, align 4
  store i32 1, ptr %_0, align 4
  br label %bb12

bb16:                                             ; preds = %bb4
  %val4 = load ptr, ptr %self1, align 8
  %y = load i8, ptr %val4, align 1
  %_35 = shl i32 %init, 6
  %_37 = and i8 %y, 63
  %_36 = zext i8 %_37 to i32
  %19 = or i32 %_35, %_36
  store i32 %19, ptr %ch, align 4
  %_13 = icmp uge i8 %x, -32
  br i1 %_13, label %bb6, label %bb11

bb15:                                             ; preds = %bb4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17ha396df6ff8b4b0d9E() #22
  br label %bb2

bb2:                                              ; preds = %bb19, %bb17, %bb15
  unreachable

bb11:                                             ; preds = %bb10, %bb16
  %_28 = load i32, ptr %ch, align 4
  %20 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_28, ptr %20, align 4
  store i32 1, ptr %_0, align 4
  br label %bb12

bb6:                                              ; preds = %bb16
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %21 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd528ceca61c41839E"(ptr align 8 %bytes)
  store ptr %21, ptr %self2, align 8
  %22 = load ptr, ptr %self2, align 8
  %23 = ptrtoint ptr %22 to i64
  %24 = icmp eq i64 %23, 0
  %_38 = select i1 %24, i64 0, i64 1
  %25 = trunc nuw i64 %_38 to i1
  br i1 %25, label %bb18, label %bb17

bb18:                                             ; preds = %bb6
  %val5 = load ptr, ptr %self2, align 8
  %z = load i8, ptr %val5, align 1
  %_19 = and i8 %y, 63
  %ch6 = zext i8 %_19 to i32
  %_41 = shl i32 %ch6, 6
  %_43 = and i8 %z, 63
  %_42 = zext i8 %_43 to i32
  %y_z = or i32 %_41, %_42
  %_20 = shl i32 %init, 12
  %26 = or i32 %_20, %y_z
  store i32 %26, ptr %ch, align 4
  %_21 = icmp uge i8 %x, -16
  br i1 %_21, label %bb8, label %bb10

bb17:                                             ; preds = %bb6
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17ha396df6ff8b4b0d9E() #22
  br label %bb2

bb10:                                             ; preds = %bb20, %bb18
  br label %bb11

bb8:                                              ; preds = %bb18
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %27 = call align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd528ceca61c41839E"(ptr align 8 %bytes)
  store ptr %27, ptr %self3, align 8
  %28 = load ptr, ptr %self3, align 8
  %29 = ptrtoint ptr %28 to i64
  %30 = icmp eq i64 %29, 0
  %_44 = select i1 %30, i64 0, i64 1
  %31 = trunc nuw i64 %_44 to i1
  br i1 %31, label %bb20, label %bb19

bb20:                                             ; preds = %bb8
  %val7 = load ptr, ptr %self3, align 8
  %w = load i8, ptr %val7, align 1
  %_26 = and i32 %init, 7
  %_25 = shl i32 %_26, 18
  %_47 = shl i32 %y_z, 6
  %_49 = and i8 %w, 63
  %_48 = zext i8 %_49 to i32
  %_27 = or i32 %_47, %_48
  %32 = or i32 %_25, %_27
  store i32 %32, ptr %ch, align 4
  br label %bb10

bb19:                                             ; preds = %bb8
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17ha396df6ff8b4b0d9E() #22
  br label %bb2
}

; core::str::<impl str>::len
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3str21_$LT$impl$u20$str$GT$3len17h6591b20b988cccc7E"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  ret i64 %self.1
}

; core::str::<impl str>::chars
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core3str21_$LT$impl$u20$str$GT$5chars17h2253b3769080311cE"(ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  %_7 = getelementptr inbounds nuw i8, ptr %self.0, i64 %self.1
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_7, 1
  ret { ptr, ptr } %1
}

; core::char::convert::from_u32_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4char7convert18from_u32_unchecked18precondition_check17h9b41c7ea47190efdE(i32 %i) unnamed_addr #1 {
start:
  %_3 = alloca [4 x i8], align 4
  %self = xor i32 %i, 55296
  %_6 = sub i32 %self, 2048
  %_5 = icmp uge i32 %_6, 1112064
  br i1 %_5, label %bb3, label %bb4

bb4:                                              ; preds = %start
  store i32 %i, ptr %_3, align 4
  br label %bb5

bb3:                                              ; preds = %start
  store i32 1114112, ptr %_3, align 4
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %0 = load i32, ptr %_3, align 4
  %1 = icmp eq i32 %0, 1114112
  %_9 = select i1 %1, i64 1, i64 0
  %2 = icmp eq i64 %_9, 0
  br i1 %2, label %bb1, label %bb2

bb1:                                              ; preds = %bb5
  ret void

bb2:                                              ; preds = %bb5
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_4dc07e69e5d34e9d1484dfbbef0bb9b1, i64 174) #20
  unreachable
}

; core::char::methods::encode_utf8_raw
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN4core4char7methods15encode_utf8_raw17h6785ef5ca8b2df70E(i32 %code, ptr align 1 %dst.0, i64 %dst.1) unnamed_addr #2 {
start:
  %len = alloca [8 x i8], align 8
  %_51 = icmp ult i32 %code, 128
  br i1 %_51, label %bb12, label %bb7

bb7:                                              ; preds = %start
  %_52 = icmp ult i32 %code, 2048
  br i1 %_52, label %bb11, label %bb8

bb12:                                             ; preds = %start
  store i64 1, ptr %len, align 8
  %_5 = icmp uge i64 %dst.1, 1
  br i1 %_5, label %bb5, label %bb1

bb8:                                              ; preds = %bb7
  %_53 = icmp ult i32 %code, 65536
  br i1 %_53, label %bb10, label %bb9

bb11:                                             ; preds = %bb7
  store i64 2, ptr %len, align 8
  %_7 = icmp uge i64 %dst.1, 2
  br i1 %_7, label %bb4, label %bb1

bb9:                                              ; preds = %bb8
  store i64 4, ptr %len, align 8
  %_11 = icmp uge i64 %dst.1, 4
  br i1 %_11, label %bb2, label %bb1

bb10:                                             ; preds = %bb8
  store i64 3, ptr %len, align 8
  %_9 = icmp uge i64 %dst.1, 3
  br i1 %_9, label %bb3, label %bb1

bb1:                                              ; preds = %bb12, %bb11, %bb10, %bb9
  %len6 = load i64, ptr %len, align 8
; call core::char::methods::encode_utf8_raw::do_panic::runtime
  call void @_ZN4core4char7methods15encode_utf8_raw8do_panic7runtime17h15f309f4b7afddfdE(i32 %code, i64 %len6, i64 %dst.1, ptr align 8 @alloc_25406cc0e964b7161e3da8e3e3db2f6c) #23
  unreachable

bb2:                                              ; preds = %bb9
  %a = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %b = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %c = getelementptr inbounds nuw i8, ptr %dst.0, i64 2
  %d = getelementptr inbounds nuw i8, ptr %dst.0, i64 3
  %_37 = lshr i32 %code, 18
  %_36 = and i32 %_37, 7
  %_35 = trunc i32 %_36 to i8
  %0 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %1 = or i8 %_35, -16
  store i8 %1, ptr %0, align 1
  %_40 = lshr i32 %code, 12
  %_39 = and i32 %_40, 63
  %_38 = trunc i32 %_39 to i8
  %2 = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %3 = or i8 %_38, -128
  store i8 %3, ptr %2, align 1
  %_43 = lshr i32 %code, 6
  %_42 = and i32 %_43, 63
  %_41 = trunc i32 %_42 to i8
  %4 = getelementptr inbounds nuw i8, ptr %dst.0, i64 2
  %5 = or i8 %_41, -128
  store i8 %5, ptr %4, align 1
  %_45 = and i32 %code, 63
  %_44 = trunc i32 %_45 to i8
  %6 = getelementptr inbounds nuw i8, ptr %dst.0, i64 3
  %7 = or i8 %_44, -128
  store i8 %7, ptr %6, align 1
  br label %bb6

bb6:                                              ; preds = %bb5, %bb4, %bb3, %bb2
  %len8 = load i64, ptr %len, align 8
  br label %bb13

bb3:                                              ; preds = %bb10
  %a1 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %b2 = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %c3 = getelementptr inbounds nuw i8, ptr %dst.0, i64 2
  %_25 = lshr i32 %code, 12
  %_24 = and i32 %_25, 15
  %_23 = trunc i32 %_24 to i8
  %8 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %9 = or i8 %_23, -32
  store i8 %9, ptr %8, align 1
  %_28 = lshr i32 %code, 6
  %_27 = and i32 %_28, 63
  %_26 = trunc i32 %_27 to i8
  %10 = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %11 = or i8 %_26, -128
  store i8 %11, ptr %10, align 1
  %_30 = and i32 %code, 63
  %_29 = trunc i32 %_30 to i8
  %12 = getelementptr inbounds nuw i8, ptr %dst.0, i64 2
  %13 = or i8 %_29, -128
  store i8 %13, ptr %12, align 1
  br label %bb6

bb4:                                              ; preds = %bb11
  %a4 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %b5 = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %_17 = lshr i32 %code, 6
  %_16 = and i32 %_17, 31
  %_15 = trunc i32 %_16 to i8
  %14 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %15 = or i8 %_15, -64
  store i8 %15, ptr %14, align 1
  %_19 = and i32 %code, 63
  %_18 = trunc i32 %_19 to i8
  %16 = getelementptr inbounds nuw i8, ptr %dst.0, i64 1
  %17 = or i8 %_18, -128
  store i8 %17, ptr %16, align 1
  br label %bb6

bb5:                                              ; preds = %bb12
  %a7 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %18 = getelementptr inbounds nuw i8, ptr %dst.0, i64 0
  %19 = trunc i32 %code to i8
  store i8 %19, ptr %18, align 1
  br label %bb6

bb13:                                             ; preds = %bb6
; call core::slice::raw::from_raw_parts_mut::precondition_check
  call void @_ZN4core5slice3raw18from_raw_parts_mut18precondition_check17hd77e18ae935f0245E(ptr %dst.0, i64 1, i64 1, i64 %len8) #22
  br label %bb15

bb15:                                             ; preds = %bb13
  %20 = insertvalue { ptr, i64 } poison, ptr %dst.0, 0
  %21 = insertvalue { ptr, i64 } %20, i64 %len8, 1
  ret { ptr, i64 } %21
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17hd081ccf0f8eba0a9E(i1 zeroext %cond) unnamed_addr #1 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #20
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17ha396df6ff8b4b0d9E() unnamed_addr #1 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #20
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h44bd7d420bfde20cE"(ptr align 4 %self) unnamed_addr #2 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h3020b3a0fa807f30E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17ha17962aca932964cE"(ptr align 8 %self) unnamed_addr #2 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i64, i64 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17hc0c7b3d4c9020ccaE"(ptr align 8 %self)
  %_0.0 = extractvalue { i64, i64 } %0, 0
  %_0.1 = extractvalue { i64, i64 } %0, 1
  %1 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %2 = insertvalue { i64, i64 } %1, i64 %_0.1, 1
  ret { i64, i64 } %2
}

; core::iter::traits::iterator::Iterator::map
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator3map17h1e967219ff17bd0aE(ptr %self.0, ptr %self.1) unnamed_addr #2 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::cloned
; Function Attrs: uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17h3f3b466495a519fbE(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17h64743d9f49c99a9eE(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::string::String as core::iter::traits::collect::FromIterator<&char>>::from_iter
  invoke void @"_ZN99_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..FromIterator$LT$$RF$char$GT$$GT$9from_iter17hce120f7ae35480cfE"(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17hbf5b83a71a414ff3E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h196358220e7d48e2E"(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::for_each
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator8for_each17hfd388ef2174d08f5E(ptr %self.0, ptr %self.1, ptr align 8 %f) unnamed_addr #2 {
start:
; call <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::fold
  call void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h225282b811881e78E"(ptr %self.0, ptr %self.1, ptr align 8 %f)
  ret void
}

; core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h78494fdcbe7f4963E"(ptr align 8 %_1, i32 %item) unnamed_addr #2 {
start:
; call <alloc::string::String as core::iter::traits::collect::Extend<char>>::extend::{{closure}}
  call void @"_ZN89_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$char$GT$$GT$6extend28_$u7b$$u7b$closure$u7d$$u7d$17he7250bb066f0f6cdE"(ptr align 8 %_1, i32 %item)
  ret void
}

; core::iter::adapters::map::map_fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17ha8d6c1a3bdc9e956E"(ptr align 8 %_1, ptr align 4 %elt) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  store i8 1, ptr %_10, align 1
  %_8 = getelementptr inbounds i8, ptr %_1, i64 8
; invoke core::ops::function::FnMut::call_mut
  %_7 = invoke i32 @_ZN4core3ops8function5FnMut8call_mut17h70421ce8ed52e21fE(ptr align 1 %_8, ptr align 4 %elt)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  %0 = load i8, ptr %_10, align 1
  %1 = trunc nuw i8 %0 to i1
  br i1 %1, label %bb4, label %bb3

funclet_bb5:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
  invoke void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h78494fdcbe7f4963E"(ptr align 8 %_1, i32 %_7)
          to label %bb2 unwind label %funclet_bb5

bb2:                                              ; preds = %bb1
  ret void

bb3:                                              ; preds = %bb4, %bb5
  cleanupret from %cleanuppad unwind to caller

bb4:                                              ; preds = %bb5
  br label %bb3
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hff2a08233d74029cE(ptr align 8 %self, i64 %n) unnamed_addr #2 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hda1d6466b31030f1E(i64 %size, i64 %align) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #20
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h03fe9825c0b15508E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #2 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hda1d6466b31030f1E(i64 %new_size, i64 %_23) #22
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17hff2a08233d74029cE(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h2231a232585a0018E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %_3 = getelementptr inbounds i8, ptr %self, i64 16
  %offset = load i64, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_7 = load i64, ptr %0, align 8
  store i64 %offset, ptr %index, align 8
  %1 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 5) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw i32, ptr %_3, i64 %offset
  %2 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h36c4e3a8522cf35cE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %_3 = getelementptr inbounds i8, ptr %self, i64 16
  %offset = load i64, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_7 = load i64, ptr %0, align 8
  store i64 %offset, ptr %index, align 8
  %1 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 5) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(i32, u32)>", ptr %_3, i64 %offset
  %2 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h54f42508a6113c5dE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %_3 = getelementptr inbounds i8, ptr %self, i64 16
  %offset = load i64, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_7 = load i64, ptr %0, align 8
  store i64 %offset, ptr %index, align 8
  %1 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 5) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw i32, ptr %_3, i64 %offset
  %2 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h56f7878b07fafbfaE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %self1 = getelementptr inbounds i8, ptr %self, i64 32
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  %offset = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 32
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7 = load i64, ptr %2, align 8
  store i64 %offset, ptr %index, align 8
  %3 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %3, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 4) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(u32, u32)>", ptr %self, i64 %offset
  %4 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %5 = insertvalue { ptr, i64 } %4, i64 %len, 1
  ret { ptr, i64 } %5
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h63b0eb53cdd163aaE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %self1 = getelementptr inbounds i8, ptr %self, i64 48
  %0 = getelementptr inbounds i8, ptr %self, i64 48
  %offset = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 48
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7 = load i64, ptr %2, align 8
  store i64 %offset, ptr %index, align 8
  %3 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %3, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 6) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(u32, u32)>", ptr %self, i64 %offset
  %4 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %5 = insertvalue { ptr, i64 } %4, i64 %len, 1
  ret { ptr, i64 } %5
}

; core::array::iter::IntoIter<T,_>::as_mut_slice
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h8d10b8c553e0c2aaE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %index = alloca [16 x i8], align 8
  %self1 = getelementptr inbounds i8, ptr %self, i64 80
  %0 = getelementptr inbounds i8, ptr %self, i64 80
  %offset = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 80
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7 = load i64, ptr %2, align 8
  store i64 %offset, ptr %index, align 8
  %3 = getelementptr inbounds i8, ptr %index, i64 8
  store i64 %_7, ptr %3, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call <core::ops::index_range::IndexRange as core::slice::index::SliceIndex<[T]>>::get_unchecked_mut::precondition_check
  call void @"_ZN104_$LT$core..ops..index_range..IndexRange$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$17get_unchecked_mut18precondition_check17heb062dfd373e4571E"(i64 %_7, i64 5) #22
  br label %bb2

bb2:                                              ; preds = %bb1
  %len = sub nuw i64 %_7, %offset
  %_16 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<&str>", ptr %self, i64 %offset
  %4 = insertvalue { ptr, i64 } poison, ptr %_16, 0
  %5 = insertvalue { ptr, i64 } %4, i64 %len, 1
  ret { ptr, i64 } %5
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h17c2f2b69436e0a6E"(ptr sret([40 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  %_2 = alloca [20 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_2, ptr align 4 %self, i64 20, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 4 %_2, i64 20, i1 false)
  store i64 0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 5, ptr %1, align 8
  ret void
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h206f1e51676d31b1E"(ptr sret([48 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  %_2 = alloca [32 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_2, ptr align 4 %self, i64 32, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 4 %_2, i64 32, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  store i64 4, ptr %1, align 8
  ret void
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha477cfae2ad9af39E"(ptr sret([64 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  %_2 = alloca [48 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_2, ptr align 4 %self, i64 48, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 4 %_2, i64 48, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 48
  store i64 0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  store i64 6, ptr %1, align 8
  ret void
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17hc7bf19b4a03d2b71E"(ptr sret([40 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  %_2 = alloca [20 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_2, ptr align 4 %self, i64 20, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 4 %_2, i64 20, i1 false)
  store i64 0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 5, ptr %1, align 8
  ret void
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17heff75b2b8d4a4dfaE"(ptr sret([96 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
  %_2 = alloca [80 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %self, i64 80, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_2, i64 80, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 80
  store i64 0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  store i64 5, ptr %1, align 8
  ret void
}

; core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
; Function Attrs: uwtable
define internal void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17hf472d7d761f11a4aE"(ptr sret([56 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  %_2 = alloca [40 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_2, ptr align 4 %self, i64 40, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 4 %_2, i64 40, i1 false)
  store i64 0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 5, ptr %1, align 8
  ret void
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h6bd93caef2eba87bE"(ptr align 4 %self.0, i64 %self.1) unnamed_addr #2 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h661de08c0de1c54dE"(ptr align 4 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #23
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #20
  unreachable
}

; core::slice::raw::from_raw_parts_mut::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw18from_raw_parts_mut18precondition_check17hd77e18ae935f0245E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #23
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #23
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #24 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_5c1a2f972552229672fc942406cfc298, i64 283) #20
  unreachable
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h661de08c0de1c54dE"(ptr align 4 %slice.0, i64 %slice.1) unnamed_addr #2 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::option::Option<T>::map_or_else
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17h34e3538c9e63815aE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 8 %default) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_9 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  store i8 1, ptr %_10, align 1
  store i8 1, ptr %_9, align 1
  %3 = load ptr, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = ptrtoint ptr %3 to i64
  %7 = icmp eq i64 %6, 0
  %_4 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_4 to i1
  br i1 %8, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %t.0 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %9, align 8
  store i8 0, ptr %_9, align 1
; invoke core::ops::function::FnOnce::call_once
  invoke void @_ZN4core3ops8function6FnOnce9call_once17h4b7658aa07787b52E(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %t.0, i64 %t.1)
          to label %bb4 unwind label %funclet_bb11

bb2:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke alloc::fmt::format::{{closure}}
  invoke void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17hc3820c0d2c55f117E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %default)
          to label %bb5 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %10 = load i8, ptr %_9, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb10, label %bb11_cleanup_trampoline_bb7

funclet_bb11:                                     ; preds = %bb3, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb2
  br label %bb6

bb6:                                              ; preds = %bb9, %bb4, %bb5
  ret void

bb4:                                              ; preds = %bb3
  %12 = load i8, ptr %_10, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

bb9:                                              ; preds = %bb4
  br label %bb6

bb7:                                              ; preds = %funclet_bb7
  %14 = load i8, ptr %_10, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb12, label %bb8

funclet_bb7:                                      ; preds = %bb10, %bb11_cleanup_trampoline_bb7
  %cleanuppad1 = cleanuppad within none []
  br label %bb7

bb11_cleanup_trampoline_bb7:                      ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb10:                                             ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb8:                                              ; preds = %bb12, %bb7
  cleanupret from %cleanuppad1 unwind to caller

bb12:                                             ; preds = %bb7
  br label %bb8

bb1:                                              ; No predecessors!
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17h3765eba5702206d6E(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #2 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #20
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <str as alloc::string::SpecToString>::spec_to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17had25128acdde41d6E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h71cc703152257c2cE"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17hb0ef32e964c8e2fbE"() unnamed_addr #2 {
start:
  ret i32 0
}

; alloc::fmt::format
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc3fmt6format17h9bd992477706aa0cE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %args) unnamed_addr #2 {
start:
  %_2 = alloca [16 x i8], align 8
  %_6.0 = load ptr, ptr %args, align 8
  %0 = getelementptr inbounds i8, ptr %args, i64 8
  %_6.1 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %args, i64 16
  %_7.0 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7.1 = load i64, ptr %2, align 8
  %3 = icmp eq i64 %_6.1, 0
  br i1 %3, label %bb4, label %bb5

bb4:                                              ; preds = %start
  %4 = icmp eq i64 %_7.1, 0
  br i1 %4, label %bb8, label %bb3

bb5:                                              ; preds = %start
  %5 = icmp eq i64 %_6.1, 1
  br i1 %5, label %bb6, label %bb3

bb8:                                              ; preds = %bb4
  store ptr inttoptr (i64 1 to ptr), ptr %_2, align 8
  %6 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 0, ptr %6, align 8
  br label %bb2

bb3:                                              ; preds = %bb6, %bb5, %bb4
  %7 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %8 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store ptr %7, ptr %_2, align 8
  %9 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb2

bb2:                                              ; preds = %bb3, %bb7, %bb8
  %10 = load ptr, ptr %_2, align 8
  %11 = getelementptr inbounds i8, ptr %_2, i64 8
  %12 = load i64, ptr %11, align 8
; call core::option::Option<T>::map_or_else
  call void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17h34e3538c9e63815aE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %10, i64 %12, ptr align 8 %args)
  ret void

bb6:                                              ; preds = %bb5
  %13 = icmp eq i64 %_7.1, 0
  br i1 %13, label %bb7, label %bb3

bb7:                                              ; preds = %bb6
  %s = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %14 = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %_13.0 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  %_13.1 = load i64, ptr %15, align 8
  store ptr %_13.0, ptr %_2, align 8
  %16 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %_13.1, ptr %16, align 8
  br label %bb2
}

; alloc::fmt::format::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17hc3820c0d2c55f117E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_1) unnamed_addr #2 {
start:
  %_2 = alloca [48 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %_1, i64 48, i1 false)
; call alloc::fmt::format::format_inner
  call void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_2)
  ret void
}

; alloc::str::<impl str>::repeat
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$6repeat17ha5fae8653069c8e4E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1, i64 %n) unnamed_addr #2 {
start:
  %bytes = alloca [24 x i8], align 8
; call alloc::slice::<impl [T]>::repeat
  call void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$6repeat17hd581d66808d25fa8E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1, i64 %n)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17h3cc15629e130bb9fE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h71cc703152257c2cE"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::vec::Vec<T,A>::append_elements
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$15append_elements17h03a761ec8e5fcf11E"(ptr align 8 %self, ptr %other.0, i64 %other.1, ptr align 8 %0) unnamed_addr #2 {
start:
; call alloc::vec::Vec<T,A>::reserve
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h5d1120a0901398e3E"(ptr align 8 %self, i64 %other.1, ptr align 8 %0)
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  %_9 = icmp ule i64 %len, 9223372036854775807
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  %_10 = load ptr, ptr %2, align 8
  %dst = getelementptr inbounds nuw i8, ptr %_10, i64 %len
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %other.0, ptr %dst, i64 1, i64 1, i64 %other.1) #22
  br label %bb4

bb4:                                              ; preds = %bb2
  %3 = mul i64 %other.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %dst, ptr align 1 %other.0, i64 %3, i1 false)
  %4 = getelementptr inbounds i8, ptr %self, i64 16
  %5 = getelementptr inbounds i8, ptr %self, i64 16
  %6 = load i64, ptr %5, align 8
  %7 = add i64 %6, %other.1
  store i64 %7, ptr %4, align 8
  ret void
}

; alloc::vec::Vec<T,A>::extend_desugared
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17h3ac8be5002639311E"(ptr align 8 %self, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_11 = alloca [24 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %_3 = alloca [4 x i8], align 4
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  br label %bb1

bb1:                                              ; preds = %bb8, %start
; invoke <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::next
  %5 = invoke i32 @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha375933c48a1900cE"(ptr align 8 %iterator)
          to label %bb2 unwind label %funclet_bb12

bb12:                                             ; preds = %funclet_bb12
  cleanupret from %cleanuppad unwind to caller

funclet_bb12:                                     ; preds = %bb14, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb12

bb2:                                              ; preds = %bb1
  store i32 %5, ptr %_3, align 4
  %6 = load i32, ptr %_3, align 4
  %7 = icmp eq i32 %6, 1114112
  %_5 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_5 to i1
  br i1 %8, label %bb3, label %bb9

bb3:                                              ; preds = %bb2
  %element = load i32, ptr %_3, align 4
  %9 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %9, align 8
  %_19 = icmp ule i64 %len, 2305843009213693951
  br label %bb17

bb9:                                              ; preds = %bb2
  br label %bb10

bb17:                                             ; preds = %bb3
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_9, align 8
  br label %bb15

bb16:                                             ; No predecessors!
  store i64 -1, ptr %_9, align 8
  br label %bb15

bb15:                                             ; preds = %bb17, %bb16
  %10 = load i64, ptr %_9, align 8
  %_8 = icmp eq i64 %len, %10
  br i1 %_8, label %bb4, label %bb7

bb7:                                              ; preds = %bb15
  br label %bb8

bb4:                                              ; preds = %bb15
; invoke <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17ha9306b1fc11bff0bE"(ptr sret([24 x i8]) align 8 %_11, ptr align 8 %iterator)
          to label %bb5 unwind label %funclet_bb14

bb8:                                              ; preds = %bb6, %bb7
  %11 = getelementptr inbounds i8, ptr %self, i64 8
  %_24 = load ptr, ptr %11, align 8
  %dst = getelementptr inbounds nuw i32, ptr %_24, i64 %len
  store i32 %element, ptr %dst, align 4
  %new_len = add i64 %len, 1
  %12 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %new_len, ptr %12, align 8
  br label %bb1

bb14:                                             ; preds = %funclet_bb14
  cleanupret from %cleanuppad2 unwind label %funclet_bb12

funclet_bb14:                                     ; preds = %bb5, %bb4
  %cleanuppad2 = cleanuppad within none []
  br label %bb14

bb5:                                              ; preds = %bb4
  %lower = load i64, ptr %_11, align 8
  %13 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %13, ptr %3, align 8
  %_14 = load i64, ptr %3, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17hab4f5f0351b50cb6E"(ptr align 8 %self, i64 %_14, ptr align 8 %2)
          to label %bb6 unwind label %funclet_bb14

bb6:                                              ; preds = %bb5
  br label %bb8

bb10:                                             ; preds = %bb9
  ret void

bb19:                                             ; No predecessors!
  unreachable
}

; alloc::vec::Vec<T,A>::len
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17h053e695db16c8d8fE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %_0 = load i64, ptr %0, align 8
  %_2 = icmp ule i64 %_0, 2305843009213693951
  ret i64 %_0
}

; alloc::vec::Vec<T,A>::len
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17hde1483eea24f77b2E"(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %_0 = load i64, ptr %0, align 8
  %_2 = icmp ule i64 %_0, 2305843009213693951
  ret i64 %_0
}

; alloc::vec::Vec<T,A>::push
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$4push17h58f3e793a6a1fe5cE"(ptr align 8 %self, i8 %value, ptr align 8 %0) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_5 = alloca [8 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb9

bb9:                                              ; preds = %start
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_5, align 8
  br label %bb7

bb8:                                              ; No predecessors!
  store i64 -1, ptr %_5, align 8
  br label %bb7

bb7:                                              ; preds = %bb9, %bb8
  %2 = load i64, ptr %_5, align 8
  %_4 = icmp eq i64 %len, %2
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb7
  br label %bb4

bb1:                                              ; preds = %bb7
; invoke alloc::raw_vec::RawVec<T,A>::grow_one
  invoke void @"_ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17hc9a9ed72510f0abfE"(ptr align 8 %self, ptr align 8 %0)
          to label %bb2 unwind label %funclet_bb6

bb4:                                              ; preds = %bb2, %bb3
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_15 = load ptr, ptr %3, align 8
  %end = getelementptr inbounds nuw i8, ptr %_15, i64 %len
  store i8 %value, ptr %end, align 1
  %4 = getelementptr inbounds i8, ptr %self, i64 16
  %5 = add i64 %len, 1
  store i64 %5, ptr %4, align 8
  ret void

bb6:                                              ; preds = %funclet_bb6
  cleanupret from %cleanuppad unwind to caller

funclet_bb6:                                      ; preds = %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb6

bb2:                                              ; preds = %bb1
  br label %bb4
}

; alloc::vec::Vec<T,A>::reserve
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h5d1120a0901398e3E"(ptr align 8 %self, i64 %additional, ptr align 8 %0) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  store i64 1, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 1, ptr %2, align 8
  br label %bb6

bb6:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  store i64 %self2, ptr %self1, align 8
  br label %bb4

bb5:                                              ; No predecessors!
  store i64 -1, ptr %self1, align 8
  br label %bb4

bb4:                                              ; preds = %bb6, %bb5
  %3 = load i64, ptr %self1, align 8
  %_10 = sub i64 %3, %len
  %_7 = icmp ugt i64 %additional, %_10
  br i1 %_7, label %bb1, label %bb2

bb2:                                              ; preds = %bb4
  br label %bb3

bb1:                                              ; preds = %bb4
; call alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h8e00b51662294a58E"(ptr align 8 %self, i64 %len, i64 %additional, i64 1, i64 1)
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; alloc::vec::Vec<T,A>::reserve
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17hab4f5f0351b50cb6E"(ptr align 8 %self, i64 %additional, ptr align 8 %0) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  store i64 4, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 4, ptr %2, align 8
  br label %bb6

bb6:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  store i64 %self2, ptr %self1, align 8
  br label %bb4

bb5:                                              ; No predecessors!
  store i64 -1, ptr %self1, align 8
  br label %bb4

bb4:                                              ; preds = %bb6, %bb5
  %3 = load i64, ptr %self1, align 8
  %_10 = sub i64 %3, %len
  %_7 = icmp ugt i64 %additional, %_10
  br i1 %_7, label %bb1, label %bb2

bb2:                                              ; preds = %bb4
  br label %bb3

bb1:                                              ; preds = %bb4
; call alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h8e00b51662294a58E"(ptr align 8 %self, i64 %len, i64 %additional, i64 4, i64 4)
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; alloc::vec::Vec<T,A>::as_slice
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17h71dd92dc61d2e4cfE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_4, i64 4, i64 4, i64 %len) #22
  br label %bb3

bb3:                                              ; preds = %bb1
  %2 = insertvalue { ptr, i64 } poison, ptr %_4, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; alloc::vec::Vec<T,A>::as_slice
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17hbbded356a252a9cdE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_4, i64 4, i64 4, i64 %len) #22
  br label %bb3

bb3:                                              ; preds = %bb1
  %2 = insertvalue { ptr, i64 } poison, ptr %_4, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17h15ce2814b2e1f992E(i64 %0, i64 %1) unnamed_addr #2 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17he734ac42d3466b08E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #22
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #22
  ret ptr %_0
}

; alloc::alloc::exchange_malloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc15exchange_malloc17h00f394e30c08faa1E(i64 %size, i64 %align) unnamed_addr #2 {
start:
  %_4 = alloca [16 x i8], align 8
  br label %bb4

bb4:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hda1d6466b31030f1E(i64 %size, i64 %align) #22
  br label %bb5

bb5:                                              ; preds = %bb4
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 inttoptr (i64 1 to ptr), i64 %align, i64 %size, i1 zeroext false)
  %1 = extractvalue { ptr, i64 } %0, 0
  %2 = extractvalue { ptr, i64 } %0, 1
  store ptr %1, ptr %_4, align 8
  %3 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %2, ptr %3, align 8
  %4 = load ptr, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = ptrtoint ptr %4 to i64
  %8 = icmp eq i64 %7, 0
  %_5 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_5 to i1
  br i1 %9, label %bb2, label %bb3

bb2:                                              ; preds = %bb5
; call alloc::alloc::handle_alloc_error
  call void @_ZN5alloc5alloc18handle_alloc_error17h786143be1fde6527E(i64 %align, i64 %size) #23
  unreachable

bb3:                                              ; preds = %bb5
  %ptr.0 = load ptr, ptr %_4, align 8
  %10 = getelementptr inbounds i8, ptr %_4, i64 8
  %ptr.1 = load i64, ptr %10, align 8
  ret ptr %ptr.0

bb1:                                              ; No predecessors!
  unreachable
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h683b617ea21b0a70E(i64 %0, i64 %1) unnamed_addr #2 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17he734ac42d3466b08E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #22
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #22
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #2 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %_23) #22
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h683b617ea21b0a70E(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17h15ce2814b2e1f992E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %ptr) #22
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %ptr4) #22
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::alloc::Global::grow_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17h1ce4f368f7465561E(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1, i64 %2, i64 %3, i1 zeroext %zeroed) unnamed_addr #2 {
start:
  %layout5 = alloca [16 x i8], align 8
  %_60 = alloca [8 x i8], align 8
  %layout4 = alloca [16 x i8], align 8
  %self3 = alloca [16 x i8], align 8
  %_35 = alloca [16 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_25 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %old_size = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %old_layout, align 8
  %4 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %1, ptr %4, align 8
  store i64 %2, ptr %new_layout, align 8
  %5 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %3, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %7, ptr %old_size, align 8
  %8 = load i64, ptr %old_size, align 8
  %9 = icmp eq i64 %8, 0
  br i1 %9, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %10 = load i64, ptr %new_layout, align 8
  %11 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %12 = load i64, ptr %11, align 8
; call alloc::alloc::Global::alloc_impl
  %13 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 %self, i64 %10, i64 %12, i1 zeroext %zeroed)
  %14 = extractvalue { ptr, i64 } %13, 0
  %15 = extractvalue { ptr, i64 } %13, 1
  store ptr %14, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %15, ptr %16, align 8
  br label %bb9

bb1:                                              ; preds = %start
  %_43 = load i64, ptr %old_layout, align 8
  %_46 = icmp uge i64 %_43, 1
  %_47 = icmp ule i64 %_43, -9223372036854775808
  %_48 = and i1 %_46, %_47
  %_49 = load i64, ptr %new_layout, align 8
  %_52 = icmp uge i64 %_49, 1
  %_53 = icmp ule i64 %_49, -9223372036854775808
  %_54 = and i1 %_52, %_53
  %_11 = icmp eq i64 %_43, %_49
  br i1 %_11, label %bb3, label %bb4

bb9:                                              ; preds = %bb22, %bb30, %bb31, %bb37, %bb2
  %17 = load ptr, ptr %_0, align 8
  %18 = getelementptr inbounds i8, ptr %_0, i64 8
  %19 = load i64, ptr %18, align 8
  %20 = insertvalue { ptr, i64 } poison, ptr %17, 0
  %21 = insertvalue { ptr, i64 } %20, i64 %19, 1
  ret { ptr, i64 } %21

bb4:                                              ; preds = %bb1
  %22 = load i64, ptr %new_layout, align 8
  %23 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %24 = load i64, ptr %23, align 8
; call alloc::alloc::Global::alloc_impl
  %25 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 %self, i64 %22, i64 %24, i1 zeroext %zeroed)
  %26 = extractvalue { ptr, i64 } %25, 0
  %27 = extractvalue { ptr, i64 } %25, 1
  store ptr %26, ptr %self3, align 8
  %28 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %27, ptr %28, align 8
  %29 = load ptr, ptr %self3, align 8
  %30 = getelementptr inbounds i8, ptr %self3, i64 8
  %31 = load i64, ptr %30, align 8
  %32 = ptrtoint ptr %29 to i64
  %33 = icmp eq i64 %32, 0
  %_76 = select i1 %33, i64 1, i64 0
  %34 = trunc nuw i64 %_76 to i1
  br i1 %34, label %bb31, label %bb32

bb3:                                              ; preds = %bb1
  %35 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %new_size = load i64, ptr %35, align 8
  %36 = load i64, ptr %old_size, align 8
  %cond = icmp uge i64 %new_size, %36
  br label %bb10

bb31:                                             ; preds = %bb4
  %37 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %38 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store ptr %37, ptr %_0, align 8
  %39 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %38, ptr %39, align 8
  br label %bb9

bb32:                                             ; preds = %bb4
  %v.0 = load ptr, ptr %self3, align 8
  %40 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.1 = load i64, ptr %40, align 8
  store ptr %v.0, ptr %_35, align 8
  %41 = getelementptr inbounds i8, ptr %_35, i64 8
  store i64 %v.1, ptr %41, align 8
  %new_ptr.0 = load ptr, ptr %_35, align 8
  %42 = getelementptr inbounds i8, ptr %_35, i64 8
  %new_ptr.1 = load i64, ptr %42, align 8
  br label %bb33

bb33:                                             ; preds = %bb32
  %43 = load i64, ptr %old_size, align 8
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %ptr, ptr %new_ptr.0, i64 1, i64 1, i64 %43) #22
  br label %bb35

bb35:                                             ; preds = %bb33
  %44 = load i64, ptr %old_size, align 8
  %45 = mul i64 %44, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %new_ptr.0, ptr align 1 %ptr, i64 %45, i1 false)
  %46 = load i64, ptr %old_layout, align 8
  %47 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %48 = load i64, ptr %47, align 8
  store i64 %46, ptr %layout4, align 8
  %49 = getelementptr inbounds i8, ptr %layout4, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr %old_size, align 8
  %51 = icmp eq i64 %50, 0
  br i1 %51, label %bb37, label %bb36

bb37:                                             ; preds = %bb36, %bb35
  store ptr %new_ptr.0, ptr %_0, align 8
  %52 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_ptr.1, ptr %52, align 8
  br label %bb9

bb36:                                             ; preds = %bb35
  %53 = load i64, ptr %old_layout, align 8
  %54 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %55 = load i64, ptr %54, align 8
  store i64 %53, ptr %layout5, align 8
  %56 = getelementptr inbounds i8, ptr %layout5, i64 8
  store i64 %55, ptr %56, align 8
  %57 = load i64, ptr %old_size, align 8
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %57, i64 %_43) #22
  br label %bb37

bb10:                                             ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hd081ccf0f8eba0a9E(i1 zeroext %cond) #22
  %58 = load i64, ptr %old_layout, align 8
  %59 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %60 = load i64, ptr %59, align 8
  store i64 %58, ptr %layout, align 8
  %61 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %60, ptr %61, align 8
  %62 = load i64, ptr %old_size, align 8
; call __rustc::__rust_realloc
  %63 = call ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr %ptr, i64 %62, i64 %_43, i64 %new_size) #22
  store ptr %63, ptr %raw_ptr, align 8
  %ptr6 = load ptr, ptr %raw_ptr, align 8
  %64 = load ptr, ptr %raw_ptr, align 8
  store ptr %64, ptr %_60, align 8
  %65 = load ptr, ptr %raw_ptr, align 8
  %_61 = ptrtoint ptr %65 to i64
  %66 = icmp eq i64 %_61, 0
  br i1 %66, label %bb14, label %bb41

bb14:                                             ; preds = %bb10
  store ptr null, ptr %self2, align 8
  br label %bb13

bb41:                                             ; preds = %bb10
  br label %bb16

bb13:                                             ; preds = %bb18, %bb14
  %67 = load ptr, ptr %self2, align 8
  %68 = ptrtoint ptr %67 to i64
  %69 = icmp eq i64 %68, 0
  %_64 = select i1 %69, i64 0, i64 1
  %70 = trunc nuw i64 %_64 to i1
  br i1 %70, label %bb21, label %bb20

bb16:                                             ; preds = %bb41
  %_63 = load ptr, ptr %raw_ptr, align 8
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %_63) #22
  br label %bb18

bb18:                                             ; preds = %bb16
  %_59 = load ptr, ptr %_60, align 8
  store ptr %_59, ptr %self2, align 8
  br label %bb13

bb21:                                             ; preds = %bb13
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  br label %bb19

bb20:                                             ; preds = %bb13
  store ptr null, ptr %self1, align 8
  br label %bb19

bb19:                                             ; preds = %bb21, %bb20
  %71 = load ptr, ptr %self1, align 8
  %72 = ptrtoint ptr %71 to i64
  %73 = icmp eq i64 %72, 0
  %_66 = select i1 %73, i64 1, i64 0
  %74 = trunc nuw i64 %_66 to i1
  br i1 %74, label %bb22, label %bb23

bb22:                                             ; preds = %bb19
  %75 = load ptr, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %76 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store ptr %75, ptr %_0, align 8
  %77 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %76, ptr %77, align 8
  br label %bb9

bb23:                                             ; preds = %bb19
  %v7 = load ptr, ptr %self1, align 8
  store ptr %v7, ptr %_25, align 8
  %ptr8 = load ptr, ptr %_25, align 8
  br i1 %zeroed, label %bb6, label %bb7

bb7:                                              ; preds = %bb27, %bb23
  br label %bb28

bb6:                                              ; preds = %bb23
  %self9 = load ptr, ptr %raw_ptr, align 8
  %78 = load ptr, ptr %raw_ptr, align 8
  %79 = load i64, ptr %old_size, align 8
  %self10 = getelementptr inbounds nuw i8, ptr %78, i64 %79
  %80 = load i64, ptr %old_size, align 8
  %count = sub i64 %new_size, %80
  br label %bb25

bb25:                                             ; preds = %bb6
  %_70 = icmp eq i64 %count, 0
; call core::intrinsics::write_bytes::precondition_check
  call void @_ZN4core10intrinsics11write_bytes18precondition_check17ha5b10650f23cb0adE(ptr %self10, i64 1, i1 zeroext %_70) #22
  br label %bb27

bb27:                                             ; preds = %bb25
  %81 = mul i64 1, %count
  call void @llvm.memset.p0.i64(ptr align 1 %self10, i8 0, i64 %81, i1 false)
  br label %bb7

bb28:                                             ; preds = %bb7
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %ptr8) #22
  br label %bb30

bb30:                                             ; preds = %bb28
  store ptr %ptr8, ptr %_0, align 8
  %82 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_size, ptr %82, align 8
  br label %bb9

bb5:                                              ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable

bb12:                                             ; No predecessors!
  unreachable

bb15:                                             ; No predecessors!
  unreachable
}

; alloc::slice::<impl [T]>::repeat
; Function Attrs: uwtable
define internal void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$6repeat17hd581d66808d25fa8E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1, i64 %n) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %m = alloca [8 x i8], align 8
  %buf = alloca [24 x i8], align 8
  %self = alloca [16 x i8], align 8
  %0 = icmp eq i64 %n, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr getelementptr (i8, ptr null, i64 1), ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %2, align 8
  br label %bb8

bb2:                                              ; preds = %start
  %3 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self.1, i64 %n)
  %_43.0 = extractvalue { i64, i1 } %3, 0
  %_43.1 = extractvalue { i64, i1 } %3, 1
  br i1 %_43.1, label %bb11, label %bb13

bb8:                                              ; preds = %bb7, %bb1
  ret void

bb13:                                             ; preds = %bb2
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %_43.0, ptr %4, align 8
  store i64 1, ptr %self, align 8
  %5 = getelementptr inbounds i8, ptr %self, i64 8
  %capacity = load i64, ptr %5, align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %6 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h0a8e554283839faeE"(i64 %capacity, i64 1, i64 1, ptr align 8 @alloc_4bf8a7a4ce5b229f48ea69b79d51d2a6)
  %_50.0 = extractvalue { i64, ptr } %6, 0
  %_50.1 = extractvalue { i64, ptr } %6, 1
  store i64 %_50.0, ptr %buf, align 8
  %7 = getelementptr inbounds i8, ptr %buf, i64 8
  store ptr %_50.1, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %buf, i64 16
  store i64 0, ptr %8, align 8
  br label %bb17

bb11:                                             ; preds = %bb2
  %9 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %10 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %9, ptr %self, align 8
  %11 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %10, ptr %11, align 8
; call core::option::expect_failed
  call void @_ZN4core6option13expect_failed17h3c22898af43fbfd1E(ptr align 1 @alloc_31365cfefba383c4d2bf6b6a04cc10aa, i64 17, ptr align 8 @alloc_7d58e95f420f4f6740270666b024c4e6) #23
  unreachable

bb17:                                             ; preds = %bb13
  %_55 = getelementptr inbounds nuw i8, ptr %self.0, i64 %self.1
  store ptr %_55, ptr %end_or_len, align 8
  br label %bb18

bb18:                                             ; preds = %bb17
  %_57 = load ptr, ptr %end_or_len, align 8
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<&T,core::slice::iter::Iter<T>>>::spec_extend
  invoke void @"_ZN132_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$$RF$T$C$core..slice..iter..Iter$LT$T$GT$$GT$$GT$11spec_extend17he1e8d3f9266f5698E"(ptr align 8 %buf, ptr %self.0, ptr %_57, ptr align 8 @alloc_e4656fac386fc9fc89c4fce8069d94fd)
          to label %bb15 unwind label %funclet_bb9

bb9:                                              ; preds = %funclet_bb9
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17haac22ac4d7054c6eE"(ptr align 8 %buf) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb9:                                      ; preds = %bb18
  %cleanuppad = cleanuppad within none []
  br label %bb9

bb15:                                             ; preds = %bb18
  %12 = lshr i64 %n, 1
  store i64 %12, ptr %m, align 8
  br label %bb3

bb3:                                              ; preds = %bb21, %bb15
  %_11 = load i64, ptr %m, align 8
  %_10 = icmp ugt i64 %_11, 0
  br i1 %_10, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  %13 = getelementptr inbounds i8, ptr %buf, i64 16
  %_26 = load i64, ptr %13, align 8
  %_71 = icmp ule i64 %_26, 9223372036854775807
  %rem_len = sub i64 %capacity, %_26
  %_28 = icmp ugt i64 %rem_len, 0
  br i1 %_28, label %bb6, label %bb7

bb4:                                              ; preds = %bb3
  %14 = getelementptr inbounds i8, ptr %buf, i64 8
  %_60 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %buf, i64 8
  %_61 = load ptr, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %buf, i64 16
  %count1 = load i64, ptr %16, align 8
  %_62 = icmp ule i64 %count1, 9223372036854775807
  %dst2 = getelementptr inbounds nuw i8, ptr %_61, i64 %count1
  %17 = getelementptr inbounds i8, ptr %buf, i64 16
  %count3 = load i64, ptr %17, align 8
  %_63 = icmp ule i64 %count3, 9223372036854775807
  br label %bb19

bb7:                                              ; preds = %bb24, %bb5
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %buf, i64 24, i1 false)
  br label %bb8

bb6:                                              ; preds = %bb5
  %18 = getelementptr inbounds i8, ptr %buf, i64 8
  %_72 = load ptr, ptr %18, align 8
  %19 = getelementptr inbounds i8, ptr %buf, i64 8
  %_73 = load ptr, ptr %19, align 8
  %20 = getelementptr inbounds i8, ptr %buf, i64 16
  %count = load i64, ptr %20, align 8
  %_74 = icmp ule i64 %count, 9223372036854775807
  %dst = getelementptr inbounds nuw i8, ptr %_73, i64 %count
  br label %bb22

bb22:                                             ; preds = %bb6
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %_72, ptr %dst, i64 1, i64 1, i64 %rem_len) #22
  br label %bb24

bb24:                                             ; preds = %bb22
  %21 = mul i64 %rem_len, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %dst, ptr align 1 %_72, i64 %21, i1 false)
  %22 = getelementptr inbounds i8, ptr %buf, i64 16
  store i64 %capacity, ptr %22, align 8
  br label %bb7

bb19:                                             ; preds = %bb4
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %_60, ptr %dst2, i64 1, i64 1, i64 %count3) #22
  br label %bb21

bb21:                                             ; preds = %bb19
  %23 = mul i64 %count3, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %dst2, ptr align 1 %_60, i64 %23, i1 false)
  %24 = getelementptr inbounds i8, ptr %buf, i64 16
  %buf_len = load i64, ptr %24, align 8
  %_70 = icmp ule i64 %buf_len, 9223372036854775807
  %new_len = mul i64 %buf_len, 2
  %25 = getelementptr inbounds i8, ptr %buf, i64 16
  store i64 %new_len, ptr %25, align 8
  %26 = load i64, ptr %m, align 8
  %27 = lshr i64 %26, 1
  store i64 %27, ptr %m, align 8
  br label %bb3

bb16:                                             ; No predecessors!
  unreachable
}

; alloc::slice::<impl [T]>::into_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h200db025701b5ee1E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self.0, i64 %self.1) unnamed_addr #2 {
start:
  %capacity = alloca [8 x i8], align 8
  br label %bb3

bb3:                                              ; preds = %start
  store i64 %self.1, ptr %capacity, align 8
  br label %bb1

bb1:                                              ; preds = %bb3
  %cap = load i64, ptr %capacity, align 8
  br label %bb4

bb4:                                              ; preds = %bb1
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hcb2ac6ab1b2f432fE"(ptr %self.0) #22
  br label %bb6

bb6:                                              ; preds = %bb4
  store i64 %cap, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 %self.1, ptr %1, align 8
  ret void

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::string::String::push
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc6string6String4push17hebc040763151cfe8E(ptr align 8 %self, i32 %ch) unnamed_addr #2 {
start:
  %_10 = alloca [4 x i8], align 1
  %_12 = icmp ult i32 %ch, 128
  br i1 %_12, label %bb9, label %bb4

bb4:                                              ; preds = %start
  %_13 = icmp ult i32 %ch, 2048
  br i1 %_13, label %bb8, label %bb5

bb9:                                              ; preds = %start
  %_4 = trunc i32 %ch to i8
; call alloc::vec::Vec<T,A>::push
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$4push17h58f3e793a6a1fe5cE"(ptr align 8 %self, i8 %_4, ptr align 8 @alloc_bc744aeb92fce05e521863a414e27269)
  br label %bb3

bb5:                                              ; preds = %bb4
  %_14 = icmp ult i32 %ch, 65536
  br i1 %_14, label %bb7, label %bb6

bb8:                                              ; preds = %bb4
  br label %bb1

bb6:                                              ; preds = %bb5
  br label %bb1

bb7:                                              ; preds = %bb5
  br label %bb1

bb1:                                              ; preds = %bb8, %bb7, %bb6
  call void @llvm.memset.p0.i64(ptr align 1 %_10, i8 0, i64 4, i1 false)
; call core::char::methods::encode_utf8_raw
  %0 = call { ptr, i64 } @_ZN4core4char7methods15encode_utf8_raw17h6785ef5ca8b2df70E(i32 %ch, ptr align 1 %_10, i64 4)
  %v.0 = extractvalue { ptr, i64 } %0, 0
  %v.1 = extractvalue { ptr, i64 } %0, 1
  %_23 = getelementptr inbounds nuw i8, ptr %v.0, i64 %v.1
; call <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<&T,core::slice::iter::Iter<T>>>::spec_extend
  call void @"_ZN132_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$$RF$T$C$core..slice..iter..Iter$LT$T$GT$$GT$$GT$11spec_extend17he1e8d3f9266f5698E"(ptr align 8 %self, ptr %v.0, ptr %_23, ptr align 8 @alloc_de1d28ae0ee49c8ef98856a2b8d3d72c)
  br label %bb3

bb3:                                              ; preds = %bb9, %bb1
  ret void
}

; alloc::string::String::as_str
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc6string6String6as_str17h12cf33577ab53daeE(ptr align 8 %self) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_6, i64 1, i64 1, i64 %len) #22
  br label %bb3

bb3:                                              ; preds = %bb1
  %2 = insertvalue { ptr, i64 } poison, ptr %_6, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; alloc::raw_vec::finish_grow
; Function Attrs: cold uwtable
define internal void @_ZN5alloc7raw_vec11finish_grow17haf773a86effb1c58E(ptr sret([24 x i8]) align 8 %_0, i64 %0, i64 %1, ptr align 8 %current_memory, ptr align 1 %alloc) unnamed_addr #5 {
start:
  %_43 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  %memory = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %new_layout, align 8
  %2 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %alloc_size = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = icmp eq i64 %5, 0
  %_9 = select i1 %6, i64 0, i64 1
  %7 = trunc nuw i64 %_9 to i1
  br i1 %7, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %ptr = load ptr, ptr %current_memory, align 8
  %8 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = getelementptr inbounds i8, ptr %8, i64 8
  %11 = load i64, ptr %10, align 8
  store i64 %9, ptr %old_layout, align 8
  %12 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %11, ptr %12, align 8
  %_26 = load i64, ptr %old_layout, align 8
  %_29 = icmp uge i64 %_26, 1
  %_30 = icmp ule i64 %_26, -9223372036854775808
  %_31 = and i1 %_29, %_30
  %_32 = load i64, ptr %new_layout, align 8
  %_35 = icmp uge i64 %_32, 1
  %_36 = icmp ule i64 %_32, -9223372036854775808
  %_37 = and i1 %_35, %_36
  %cond = icmp eq i64 %_26, %_32
  br label %bb7

bb2:                                              ; preds = %start
  %13 = load i64, ptr %new_layout, align 8
  %14 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %15 = load i64, ptr %14, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %16 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7fd4441b64e9513cE"(ptr align 1 %alloc, i64 %13, i64 %15)
  %17 = extractvalue { ptr, i64 } %16, 0
  %18 = extractvalue { ptr, i64 } %16, 1
  store ptr %17, ptr %memory, align 8
  %19 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %18, ptr %19, align 8
  br label %bb6

bb7:                                              ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hd081ccf0f8eba0a9E(i1 zeroext %cond) #22
  br label %bb8

bb8:                                              ; preds = %bb7
  %20 = load i64, ptr %old_layout, align 8
  %21 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %22 = load i64, ptr %21, align 8
  %23 = load i64, ptr %new_layout, align 8
  %24 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %25 = load i64, ptr %24, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::grow
  %26 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17hd407ee156f065826E"(ptr align 1 %alloc, ptr %ptr, i64 %20, i64 %22, i64 %23, i64 %25)
  %27 = extractvalue { ptr, i64 } %26, 0
  %28 = extractvalue { ptr, i64 } %26, 1
  store ptr %27, ptr %memory, align 8
  %29 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %28, ptr %29, align 8
  br label %bb6

bb6:                                              ; preds = %bb2, %bb8
  %30 = load ptr, ptr %memory, align 8
  %31 = getelementptr inbounds i8, ptr %memory, i64 8
  %32 = load i64, ptr %31, align 8
  store ptr %30, ptr %self, align 8
  %33 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %32, ptr %33, align 8
  %34 = load ptr, ptr %self, align 8
  %35 = getelementptr inbounds i8, ptr %self, i64 8
  %36 = load i64, ptr %35, align 8
  %37 = ptrtoint ptr %34 to i64
  %38 = icmp eq i64 %37, 0
  %_40 = select i1 %38, i64 1, i64 0
  %39 = trunc nuw i64 %_40 to i1
  br i1 %39, label %bb10, label %bb11

bb10:                                             ; preds = %bb6
  %_44.0 = load i64, ptr %new_layout, align 8
  %40 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %_44.1 = load i64, ptr %40, align 8
  store i64 %_44.0, ptr %_43, align 8
  %41 = getelementptr inbounds i8, ptr %_43, i64 8
  store i64 %_44.1, ptr %41, align 8
  %_42.0 = load i64, ptr %_43, align 8
  %42 = getelementptr inbounds i8, ptr %_43, i64 8
  %_42.1 = load i64, ptr %42, align 8
  %43 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_42.0, ptr %43, align 8
  %44 = getelementptr inbounds i8, ptr %43, i64 8
  store i64 %_42.1, ptr %44, align 8
  store i64 1, ptr %_0, align 8
  br label %bb9

bb11:                                             ; preds = %bb6
  %t.0 = load ptr, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %t.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %t.1, ptr %47, align 8
  store i64 0, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb10, %bb11
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h05cd39d1e2789981E"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hb4e3276b8c9786feE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h2bd6e64d6ca8b839E"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hb4e3276b8c9786feE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #2 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h40008f4ebd339039E"(i64 %self1, i64 %self3) #22
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17hda1d6466b31030f1E(i64 %8, i64 %9) #22
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::grow_amortized
; Function Attrs: uwtable
define internal { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17h430152f38b3fe907E"(ptr align 8 %self, i64 %len, i64 %additional, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_56 = alloca [16 x i8], align 8
  %_51 = alloca [16 x i8], align 8
  %self9 = alloca [24 x i8], align 8
  %self8 = alloca [16 x i8], align 8
  %_38 = alloca [16 x i8], align 8
  %residual7 = alloca [16 x i8], align 8
  %_26 = alloca [24 x i8], align 8
  %self6 = alloca [24 x i8], align 8
  %_24 = alloca [24 x i8], align 8
  %residual5 = alloca [16 x i8], align 8
  %elem_layout4 = alloca [16 x i8], align 8
  %self3 = alloca [24 x i8], align 8
  %_19 = alloca [24 x i8], align 8
  %v1 = alloca [8 x i8], align 8
  %residual = alloca [16 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb1, label %bb2

bb1:                                              ; preds = %start
  %5 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %6 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %5, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %6, ptr %7, align 8
  br label %bb8

bb2:                                              ; preds = %start
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %len, i64 %additional)
  %_32.0 = extractvalue { i64, i1 } %8, 0
  %_32.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_32.1, label %bb9, label %bb11

bb8:                                              ; preds = %bb7, %bb24, %bb1
  %9 = load i64, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  %11 = load i64, ptr %10, align 8
  %12 = insertvalue { i64, i64 } poison, i64 %9, 0
  %13 = insertvalue { i64, i64 } %12, i64 %11, 1
  ret { i64, i64 } %13

bb11:                                             ; preds = %bb2
  %_33 = add nuw i64 %len, %additional
  %14 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %_33, ptr %14, align 8
  store i64 1, ptr %self2, align 8
  %15 = getelementptr inbounds i8, ptr %self2, i64 8
  %v = load i64, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %v, ptr %16, align 8
  store i64 -9223372036854775807, ptr %self1, align 8
  %17 = getelementptr inbounds i8, ptr %self1, i64 8
  %v10 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %v10, ptr %18, align 8
  store i64 -9223372036854775807, ptr %_7, align 8
  %19 = getelementptr inbounds i8, ptr %_7, i64 8
  %required_cap = load i64, ptr %19, align 8
  %self11 = load i64, ptr %self, align 8
  %v112 = mul i64 %self11, 2
; call core::cmp::Ord::max
  %cap = call i64 @_ZN4core3cmp3Ord3max17h3396a908c279b812E(i64 %v112, i64 %required_cap)
  %20 = icmp eq i64 %size, 1
  br i1 %20, label %bb14, label %bb15

bb9:                                              ; preds = %bb2
  %21 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %22 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %21, ptr %self2, align 8
  %23 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %22, ptr %23, align 8
  %24 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %25 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %24, ptr %self1, align 8
  %26 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %25, ptr %26, align 8
  %e.024 = load i64, ptr %self1, align 8
  %27 = getelementptr inbounds i8, ptr %self1, i64 8
  %e.125 = load i64, ptr %27, align 8
  store i64 %e.024, ptr %_38, align 8
  %28 = getelementptr inbounds i8, ptr %_38, i64 8
  store i64 %e.125, ptr %28, align 8
  %29 = load i64, ptr %_38, align 8
  %30 = getelementptr inbounds i8, ptr %_38, i64 8
  %31 = load i64, ptr %30, align 8
  store i64 %29, ptr %_7, align 8
  %32 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %31, ptr %32, align 8
  %33 = load i64, ptr %_7, align 8
  %34 = getelementptr inbounds i8, ptr %_7, i64 8
  %35 = load i64, ptr %34, align 8
  store i64 %33, ptr %residual, align 8
  %36 = getelementptr inbounds i8, ptr %residual, i64 8
  store i64 %35, ptr %36, align 8
  %e.026 = load i64, ptr %residual, align 8
  %37 = getelementptr inbounds i8, ptr %residual, i64 8
  %e.127 = load i64, ptr %37, align 8
  store i64 %e.026, ptr %_0, align 8
  %38 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.127, ptr %38, align 8
  br label %bb7

bb14:                                             ; preds = %bb11
  store i64 8, ptr %v1, align 8
  br label %bb13

bb15:                                             ; preds = %bb11
  %_41 = icmp ule i64 %size, 1024
  br i1 %_41, label %bb16, label %bb17

bb13:                                             ; preds = %bb18, %bb14
  %39 = load i64, ptr %v1, align 8
; call core::cmp::Ord::max
  %cap13 = call i64 @_ZN4core3cmp3Ord3max17h3396a908c279b812E(i64 %39, i64 %cap)
  %40 = load i64, ptr %elem_layout, align 8
  %41 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %42 = load i64, ptr %41, align 8
  store i64 %40, ptr %elem_layout4, align 8
  %43 = getelementptr inbounds i8, ptr %elem_layout4, i64 8
  store i64 %42, ptr %43, align 8
; call core::alloc::layout::Layout::repeat
  call void @_ZN4core5alloc6layout6Layout6repeat17h03fe9825c0b15508E(ptr sret([24 x i8]) align 8 %self9, ptr align 8 %elem_layout4, i64 %cap13)
  %44 = load i64, ptr %self9, align 8
  %45 = icmp eq i64 %44, 0
  %_45 = select i1 %45, i64 1, i64 0
  %46 = trunc nuw i64 %_45 to i1
  br i1 %46, label %bb21, label %bb22

bb17:                                             ; preds = %bb15
  store i64 1, ptr %v1, align 8
  br label %bb18

bb16:                                             ; preds = %bb15
  store i64 4, ptr %v1, align 8
  br label %bb18

bb18:                                             ; preds = %bb16, %bb17
  br label %bb13

bb21:                                             ; preds = %bb13
  %47 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %48 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %47, ptr %self8, align 8
  %49 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %51 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %52 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %52, i64 8
  store i64 %51, ptr %53, align 8
  store i64 1, ptr %self3, align 8
  %54 = getelementptr inbounds i8, ptr %self3, i64 8
  %e.020 = load i64, ptr %54, align 8
  %55 = getelementptr inbounds i8, ptr %54, i64 8
  %e.121 = load i64, ptr %55, align 8
  store i64 %e.020, ptr %_51, align 8
  %56 = getelementptr inbounds i8, ptr %_51, i64 8
  store i64 %e.121, ptr %56, align 8
  %57 = load i64, ptr %_51, align 8
  %58 = getelementptr inbounds i8, ptr %_51, i64 8
  %59 = load i64, ptr %58, align 8
  %60 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %57, ptr %60, align 8
  %61 = getelementptr inbounds i8, ptr %60, i64 8
  store i64 %59, ptr %61, align 8
  store i64 1, ptr %_19, align 8
  %62 = getelementptr inbounds i8, ptr %_19, i64 8
  %63 = load i64, ptr %62, align 8
  %64 = getelementptr inbounds i8, ptr %62, i64 8
  %65 = load i64, ptr %64, align 8
  store i64 %63, ptr %residual5, align 8
  %66 = getelementptr inbounds i8, ptr %residual5, i64 8
  store i64 %65, ptr %66, align 8
  %e.022 = load i64, ptr %residual5, align 8
  %67 = getelementptr inbounds i8, ptr %residual5, i64 8
  %e.123 = load i64, ptr %67, align 8
  store i64 %e.022, ptr %_0, align 8
  %68 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.123, ptr %68, align 8
  br label %bb6

bb22:                                             ; preds = %bb13
  %t.0 = load i64, ptr %self9, align 8
  %69 = getelementptr inbounds i8, ptr %self9, i64 8
  %t.1 = load i64, ptr %69, align 8
  %70 = getelementptr inbounds i8, ptr %self9, i64 16
  %t = load i64, ptr %70, align 8
  store i64 %t.0, ptr %self8, align 8
  %71 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %t.1, ptr %71, align 8
  %t.014 = load i64, ptr %self8, align 8
  %72 = getelementptr inbounds i8, ptr %self8, i64 8
  %t.115 = load i64, ptr %72, align 8
  %73 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %t.014, ptr %73, align 8
  %74 = getelementptr inbounds i8, ptr %73, i64 8
  store i64 %t.115, ptr %74, align 8
  store i64 0, ptr %self3, align 8
  %75 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.0 = load i64, ptr %75, align 8
  %76 = getelementptr inbounds i8, ptr %75, i64 8
  %v.1 = load i64, ptr %76, align 8
  %77 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %v.0, ptr %77, align 8
  %78 = getelementptr inbounds i8, ptr %77, i64 8
  store i64 %v.1, ptr %78, align 8
  store i64 0, ptr %_19, align 8
  %79 = getelementptr inbounds i8, ptr %_19, i64 8
  %new_layout.0 = load i64, ptr %79, align 8
  %80 = getelementptr inbounds i8, ptr %79, i64 8
  %new_layout.1 = load i64, ptr %80, align 8
  %81 = load i64, ptr %elem_layout, align 8
  %82 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %83 = load i64, ptr %82, align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17hb4e3276b8c9786feE"(ptr sret([24 x i8]) align 8 %_26, ptr align 8 %self, i64 %81, i64 %83)
  %_28 = getelementptr inbounds i8, ptr %self, i64 16
; call alloc::raw_vec::finish_grow
  call void @_ZN5alloc7raw_vec11finish_grow17haf773a86effb1c58E(ptr sret([24 x i8]) align 8 %self6, i64 %new_layout.0, i64 %new_layout.1, ptr align 8 %_26, ptr align 1 %_28)
  %_53 = load i64, ptr %self6, align 8
  %84 = trunc nuw i64 %_53 to i1
  br i1 %84, label %bb23, label %bb24

bb23:                                             ; preds = %bb22
  %85 = getelementptr inbounds i8, ptr %self6, i64 8
  %e.0 = load i64, ptr %85, align 8
  %86 = getelementptr inbounds i8, ptr %85, i64 8
  %e.1 = load i64, ptr %86, align 8
  store i64 %e.0, ptr %_56, align 8
  %87 = getelementptr inbounds i8, ptr %_56, i64 8
  store i64 %e.1, ptr %87, align 8
  %88 = load i64, ptr %_56, align 8
  %89 = getelementptr inbounds i8, ptr %_56, i64 8
  %90 = load i64, ptr %89, align 8
  %91 = getelementptr inbounds i8, ptr %_24, i64 8
  store i64 %88, ptr %91, align 8
  %92 = getelementptr inbounds i8, ptr %91, i64 8
  store i64 %90, ptr %92, align 8
  store i64 1, ptr %_24, align 8
  %93 = getelementptr inbounds i8, ptr %_24, i64 8
  %94 = load i64, ptr %93, align 8
  %95 = getelementptr inbounds i8, ptr %93, i64 8
  %96 = load i64, ptr %95, align 8
  store i64 %94, ptr %residual7, align 8
  %97 = getelementptr inbounds i8, ptr %residual7, i64 8
  store i64 %96, ptr %97, align 8
  %e.018 = load i64, ptr %residual7, align 8
  %98 = getelementptr inbounds i8, ptr %residual7, i64 8
  %e.119 = load i64, ptr %98, align 8
  store i64 %e.018, ptr %_0, align 8
  %99 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.119, ptr %99, align 8
  br label %bb6

bb24:                                             ; preds = %bb22
  %100 = getelementptr inbounds i8, ptr %self6, i64 8
  %v.016 = load ptr, ptr %100, align 8
  %101 = getelementptr inbounds i8, ptr %100, i64 8
  %v.117 = load i64, ptr %101, align 8
  %102 = getelementptr inbounds i8, ptr %_24, i64 8
  store ptr %v.016, ptr %102, align 8
  %103 = getelementptr inbounds i8, ptr %102, i64 8
  store i64 %v.117, ptr %103, align 8
  store i64 0, ptr %_24, align 8
  %104 = getelementptr inbounds i8, ptr %_24, i64 8
  %ptr.0 = load ptr, ptr %104, align 8
  %105 = getelementptr inbounds i8, ptr %104, i64 8
  %ptr.1 = load i64, ptr %105, align 8
  %106 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %ptr.0, ptr %106, align 8
  store i64 %cap13, ptr %self, align 8
  %107 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.2, align 8
  %108 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.2, i64 8), align 8
  store i64 %107, ptr %_0, align 8
  %109 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %108, ptr %109, align 8
  br label %bb8

bb6:                                              ; preds = %bb21, %bb23
  br label %bb7

bb7:                                              ; preds = %bb9, %bb6
  br label %bb8

bb3:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17hf5d40c43f47d0347E"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h03fe9825c0b15508E(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h6f03f7456da44a2bE"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7fd4441b64e9513cE"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h0a8e554283839faeE"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #2 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17hf5d40c43f47d0347E"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #23
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hd081ccf0f8eba0a9E(i1 zeroext %cond) #22
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
; Function Attrs: cold uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h8e00b51662294a58E"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #5 {
start:
  %_5 = alloca [16 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::grow_amortized
  %0 = call { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17h430152f38b3fe907E"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1)
  %1 = extractvalue { i64, i64 } %0, 0
  %2 = extractvalue { i64, i64 } %0, 1
  store i64 %1, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %2, ptr %3, align 8
  %4 = load i64, ptr %_5, align 8
  %5 = getelementptr inbounds i8, ptr %_5, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = icmp eq i64 %4, -9223372036854775807
  %_6 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_6 to i1
  br i1 %8, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %err.0 = load i64, ptr %_5, align 8
  %9 = getelementptr inbounds i8, ptr %_5, i64 8
  %err.1 = load i64, ptr %9, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 @alloc_3d99a694a639f36d512dfe286335fb89) #23
  unreachable

bb3:                                              ; preds = %start
  ret void

bb4:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hfa1097c3fbf8a383E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #2 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_8, i64 1, i64 1, i64 %len) #22
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3818be7521a90061E"(ptr %self.0, ptr %self.1) unnamed_addr #2 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h466c87a5dc422df4E"(ptr %self.0, ptr %self.1) unnamed_addr #2 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h78f7f0bd72935a17E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #2 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h9fef76169fc4766aE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #2 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hc3129de6205ce232E"(ptr %self.0, ptr %self.1) unnamed_addr #2 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd0f2f2f9e23623a5E"(ptr %self.0, ptr %self.1) unnamed_addr #2 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h2bd6e64d6ca8b839E"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #2 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #22
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h6f03f7456da44a2bE"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #2 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::grow
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17hd407ee156f065826E"(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1) unnamed_addr #2 {
start:
; call alloc::alloc::Global::grow_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17h1ce4f368f7465561E(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7fd4441b64e9513cE"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #2 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17hf9151f682f89bd0eE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::string::String as core::ops::deref::Deref>::deref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN65_$LT$alloc..string..String$u20$as$u20$core..ops..deref..Deref$GT$5deref17he6a45051d77d775eE"(ptr align 8 %self) unnamed_addr #2 {
start:
; call alloc::string::String::as_str
  %0 = call { ptr, i64 } @_ZN5alloc6string6String6as_str17h12cf33577ab53daeE(ptr align 8 %self)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h839449b19c56ec6dE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_6, i64 4, i64 4, i64 %len) #22
  br label %bb4

bb4:                                              ; preds = %bb2
; call <[T] as core::fmt::Debug>::fmt
  %_0 = call zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h9628d4aef0a0a95aE"(ptr align 4 %_6, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h1bf952cc210f6fbdE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2c36e0c0c845543eE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9173b56814e08f4aE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h5e73e56dd156b027E"(ptr align 8 %self) unnamed_addr #2 {
start:
; call alloc::vec::Vec<T,A>::as_slice
  %0 = call { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17h71dd92dc61d2e4cfE"(ptr align 8 %self)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17he7827c0f7f4741e1E"(ptr align 8 %self) unnamed_addr #2 {
start:
; call alloc::vec::Vec<T,A>::as_slice
  %0 = call { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17hbbded356a252a9cdE"(ptr align 8 %self)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %this, i64 %len) unnamed_addr #1 {
start:
  %_3 = icmp ult i64 %this, %len
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_97d92cbf2a68a6ac45a1b13da79836e4, i64 214) #20
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; <usize as core::slice::index::SliceIndex<[T]>>::index
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17h1d2656f585904e1bE"(i64 %self, ptr align 4 %slice.0, i64 %slice.1, ptr align 8 %0) unnamed_addr #2 {
start:
  %_4 = icmp ult i64 %self, %slice.1
  br i1 %_4, label %bb1, label %panic

bb1:                                              ; preds = %start
  %_0 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %self
  ret ptr %_0

panic:                                            ; preds = %start
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %self, i64 %slice.1, ptr align 8 %0) #23
  unreachable
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h9c870d60e9d64f76E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h05cd39d1e2789981E"(ptr align 8 %self, i64 4, i64 4)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hace49651a0e0c42dE"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h05cd39d1e2789981E"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd9eca76d20fccb42E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h05cd39d1e2789981E"(ptr align 8 %self, i64 4, i64 4)
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::index::Index<I>>::index
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN81_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..index..Index$LT$I$GT$$GT$5index17h29eedab289ccc192E"(ptr align 8 %self, i64 %index, ptr align 8 %0) unnamed_addr #2 {
start:
  %_5.i.i = alloca [16 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %2, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_6, i64 4, i64 4, i64 %len) #22
  br label %bb3

bb3:                                              ; preds = %bb1
  %3 = getelementptr inbounds i8, ptr %_5.i.i, i64 8
  store i64 %index, ptr %3, align 8
  store i64 1, ptr %_5.i.i, align 8
  %4 = getelementptr inbounds i8, ptr %_5.i.i, i64 8
  %new_len.i.i = load i64, ptr %4, align 8
  %_8.i.i = icmp ugt i64 %index, %len
  br i1 %_8.i.i, label %bb1.i.i, label %"_ZN108_$LT$core..ops..range..RangeTo$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hd105decad9707392E.exit"

bb1.i.i:                                          ; preds = %bb3
; call core::slice::index::slice_end_index_len_fail
  call void @_ZN4core5slice5index24slice_end_index_len_fail17ha318ba5d7b15d3edE(i64 %index, i64 %len, ptr align 8 %0) #23
  unreachable

"_ZN108_$LT$core..ops..range..RangeTo$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17hd105decad9707392E.exit": ; preds = %bb3
  %5 = insertvalue { ptr, i64 } poison, ptr %_6, 0
  %6 = insertvalue { ptr, i64 } %5, i64 %new_len.i.i, 1
  %7 = insertvalue { ptr, i64 } poison, ptr %_6, 0
  %8 = insertvalue { ptr, i64 } %7, i64 %new_len.i.i, 1
  %_0.0 = extractvalue { ptr, i64 } %8, 0
  %_0.1 = extractvalue { ptr, i64 } %8, 1
  %9 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %_0.1, 1
  ret { ptr, i64 } %10
}

; <alloc::vec::Vec<T,A> as core::ops::index::Index<I>>::index
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN81_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..index..Index$LT$I$GT$$GT$5index17h35e058321cab3535E"(ptr align 8 %self, i64 %index, ptr align 8 %0) unnamed_addr #2 {
start:
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %2, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h6b33bd755c81a38fE(ptr %_6, i64 4, i64 4, i64 %len) #22
  br label %bb3

bb3:                                              ; preds = %bb1
; call <usize as core::slice::index::SliceIndex<[T]>>::index
  %_0 = call align 4 ptr @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17h1d2656f585904e1bE"(i64 %index, ptr align 4 %_6, i64 %len, ptr align 8 %0)
  ret ptr %_0
}

; <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha375933c48a1900cE"(ptr align 8 %self) unnamed_addr #2 {
start:
  %self1 = alloca [8 x i8], align 4
  %_0 = alloca [4 x i8], align 4
; call core::str::validations::next_code_point
  %0 = call { i32, i32 } @_ZN4core3str11validations15next_code_point17h494d6243edf83fdfE(ptr align 8 %self)
  %1 = extractvalue { i32, i32 } %0, 0
  %2 = extractvalue { i32, i32 } %0, 1
  store i32 %1, ptr %self1, align 4
  %3 = getelementptr inbounds i8, ptr %self1, i64 4
  store i32 %2, ptr %3, align 4
  %4 = load i32, ptr %self1, align 4
  %5 = getelementptr inbounds i8, ptr %self1, i64 4
  %6 = load i32, ptr %5, align 4
  %_4 = zext i32 %4 to i64
  %7 = trunc nuw i64 %_4 to i1
  br i1 %7, label %bb5, label %bb4

bb5:                                              ; preds = %start
  %8 = getelementptr inbounds i8, ptr %self1, i64 4
  %x = load i32, ptr %8, align 4
  br label %bb6

bb4:                                              ; preds = %start
  store i32 1114112, ptr %_0, align 4
  br label %bb2

bb2:                                              ; preds = %bb7, %bb4
  %9 = load i32, ptr %_0, align 4
  ret i32 %9

bb6:                                              ; preds = %bb5
; call core::char::convert::from_u32_unchecked::precondition_check
  call void @_ZN4core4char7convert18from_u32_unchecked18precondition_check17h9b41c7ea47190efdE(i32 %x) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  store i32 %x, ptr %_0, align 4
  br label %bb2

bb3:                                              ; No predecessors!
  unreachable
}

; <core::str::iter::Chars as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN81_$LT$core..str..iter..Chars$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17ha9306b1fc11bff0bE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #2 {
start:
  %_6 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_10 = load ptr, ptr %0, align 8
  %_11 = load ptr, ptr %self, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %len = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h66aed26f988d01d0E"(ptr %_10, ptr %_11)
  %_5 = add i64 %len, 3
  %_4 = udiv i64 %_5, 4
  %1 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %len, ptr %1, align 8
  store i64 1, ptr %_6, align 8
  store i64 %_4, ptr %_0, align 8
  %2 = load i64, ptr %_6, align 8
  %3 = getelementptr inbounds i8, ptr %_6, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h1976cd7f52d2d320E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h63b0eb53cdd163aaE"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h40a9790f4a87f792E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h8d10b8c553e0c2aaE"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h78a036c96c8a1b15E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h36c4e3a8522cf35cE"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h85e8c6f7149a91eeE"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h54f42508a6113c5dE"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17ha5a1fc8299fa8149E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h56f7878b07fafbfaE"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN82_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hb65ef98c7deb33abE"(ptr align 8 %self) unnamed_addr #0 {
start:
; call core::array::iter::IntoIter<T,_>::as_mut_slice
  %0 = call { ptr, i64 } @"_ZN4core5array4iter21IntoIter$LT$T$C$_$GT$12as_mut_slice17h2231a232585a0018E"(ptr align 8 %self)
  %_3.0 = extractvalue { ptr, i64 } %0, 0
  %_3.1 = extractvalue { ptr, i64 } %0, 1
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h71cc703152257c2cE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #2 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h0a8e554283839faeE"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h2feaf86f53ec4d63E(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #22
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; <alloc::string::String as core::iter::traits::collect::Extend<char>>::extend
; Function Attrs: uwtable
define internal void @"_ZN89_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$char$GT$$GT$6extend17h407377343a168ddaE"(ptr align 8 %self, ptr %iter.0, ptr %iter.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_11 = alloca [1 x i8], align 1
  %_5 = alloca [24 x i8], align 8
  %iterator = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hc3129de6205ce232E"(ptr %iter.0, ptr %iter.1)
  %1 = extractvalue { ptr, ptr } %0, 0
  %2 = extractvalue { ptr, ptr } %0, 1
  store ptr %1, ptr %iterator, align 8
  %3 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %2, ptr %3, align 8
  store i8 1, ptr %_11, align 1
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h0e0ee5abb1edea66E"(ptr sret([24 x i8]) align 8 %_5, ptr align 8 %iterator)
          to label %bb2 unwind label %funclet_bb6

bb6:                                              ; preds = %funclet_bb6
  %4 = load i8, ptr %_11, align 1
  %5 = trunc nuw i8 %4 to i1
  br i1 %5, label %bb5, label %bb4

funclet_bb6:                                      ; preds = %bb7, %bb2, %start
  %cleanuppad = cleanuppad within none []
  br label %bb6

bb2:                                              ; preds = %start
  %lower_bound = load i64, ptr %_5, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17h5d1120a0901398e3E"(ptr align 8 %self, i64 %lower_bound, ptr align 8 @alloc_caafbd3b6efc4fcf9b9faf4a56b98b1b)
          to label %bb7 unwind label %funclet_bb6

bb7:                                              ; preds = %bb2
  store i8 0, ptr %_11, align 1
  %_9.0 = load ptr, ptr %iterator, align 8
  %6 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_9.1 = load ptr, ptr %6, align 8
; invoke core::iter::traits::iterator::Iterator::for_each
  invoke void @_ZN4core4iter6traits8iterator8Iterator8for_each17hfd388ef2174d08f5E(ptr %_9.0, ptr %_9.1, ptr align 8 %self)
          to label %bb3 unwind label %funclet_bb6

bb3:                                              ; preds = %bb7
  ret void

bb4:                                              ; preds = %bb5, %bb6
  cleanupret from %cleanuppad unwind to caller

bb5:                                              ; preds = %bb6
  br label %bb4
}

; <alloc::string::String as core::iter::traits::collect::Extend<char>>::extend::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN89_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$char$GT$$GT$6extend28_$u7b$$u7b$closure$u7d$$u7d$17he7250bb066f0f6cdE"(ptr align 8 %_1, i32 %c) unnamed_addr #2 {
start:
  %_3 = load ptr, ptr %_1, align 8
; call alloc::string::String::push
  call void @_ZN5alloc6string6String4push17hebc040763151cfe8E(ptr align 8 %_3, i32 %c)
  ret void
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17he921c1ce2fae337fE"(ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #2 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %f = alloca [8 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %3, align 8
  store ptr %2, ptr %f, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %4, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %5 = icmp eq ptr %_36, %_37
  %6 = zext i1 %5 to i8
  store i8 %6, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %7 = load i8, ptr %_4, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  br label %bb14

bb7:                                              ; preds = %bb5
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %9, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %10 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h0f4405aaac6c40d2E"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %11 = load i8, ptr %_33, align 1
  %12 = trunc nuw i8 %11 to i1
  br i1 %12, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %10, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke core::iter::adapters::map::map_fold::{{closure}}
  invoke void @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17ha8d6c1a3bdc9e956E"(ptr align 8 %f, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %self2, i64 1) #22
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  ret void

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %13 = load i8, ptr %_34, align 1
  %14 = trunc nuw i8 %13 to i1
  br i1 %14, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h62a60b735bef66c8E"(ptr align 8 %self) unnamed_addr #2 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i32, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 1 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd528ceca61c41839E"(ptr align 8 %self) unnamed_addr #2 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i8, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17hcdc0980d9d3439b9E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #2 {
start:
  %_9 = alloca [16 x i8], align 8
  %exact = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %_7 = load ptr, ptr %self, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %1 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h0f4405aaac6c40d2E"(ptr %_6, ptr %_7)
  store i64 %1, ptr %exact, align 8
  br label %bb4

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %exact, align 8
  %_10 = load i64, ptr %exact, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_10, ptr %2, align 8
  store i64 1, ptr %_9, align 8
  store i64 %_8, ptr %_0, align 8
  %3 = load i64, ptr %_9, align 8
  %4 = getelementptr inbounds i8, ptr %_9, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 %5, ptr %7, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::iter::traits::collect::Extend<&char>>::extend
; Function Attrs: uwtable
define internal void @"_ZN93_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$$RF$char$GT$$GT$6extend17h920eb0198fc4a7cfE"(ptr align 8 %self, ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h466c87a5dc422df4E"(ptr %iter.0, ptr %iter.1)
  %_5.0 = extractvalue { ptr, ptr } %0, 0
  %_5.1 = extractvalue { ptr, ptr } %0, 1
; call core::iter::traits::iterator::Iterator::cloned
  %1 = call { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17h3f3b466495a519fbE(ptr %_5.0, ptr %_5.1)
  %_4.0 = extractvalue { ptr, ptr } %1, 0
  %_4.1 = extractvalue { ptr, ptr } %1, 1
; call <alloc::string::String as core::iter::traits::collect::Extend<char>>::extend
  call void @"_ZN89_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$char$GT$$GT$6extend17h407377343a168ddaE"(ptr align 8 %self, ptr %_4.0, ptr %_4.1)
  ret void
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h196358220e7d48e2E"(ptr sret([24 x i8]) align 8 %_0, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #2 {
start:
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %1 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h3818be7521a90061E"(ptr %iter.0, ptr %iter.1)
  %_2.0 = extractvalue { ptr, ptr } %1, 0
  %_2.1 = extractvalue { ptr, ptr } %1, 1
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hd8c09771b4bc9c81E"(ptr sret([24 x i8]) align 8 %_0, ptr %_2.0, ptr %_2.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h3bb44010def19216E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_desugared
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17h3ac8be5002639311E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17hd8c09771b4bc9c81E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h0a7e5a1cfa4fcec8E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0)
  ret void
}

; <alloc::string::String as core::iter::traits::collect::FromIterator<&char>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN99_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..FromIterator$LT$$RF$char$GT$$GT$9from_iter17hce120f7ae35480cfE"(ptr sret([24 x i8]) align 8 %_0, ptr %iter.0, ptr %iter.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_5 = alloca [24 x i8], align 8
  %buf = alloca [24 x i8], align 8
  store i64 0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store ptr inttoptr (i64 1 to ptr), ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_5, i64 16
  store i64 0, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %buf, ptr align 8 %_5, i64 24, i1 false)
; invoke <alloc::string::String as core::iter::traits::collect::Extend<&char>>::extend
  invoke void @"_ZN93_$LT$alloc..string..String$u20$as$u20$core..iter..traits..collect..Extend$LT$$RF$char$GT$$GT$6extend17h920eb0198fc4a7cfE"(ptr align 8 %buf, ptr %iter.0, ptr %iter.1)
          to label %bb1 unwind label %funclet_bb2

bb2:                                              ; preds = %funclet_bb2
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %buf) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb2:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb2

bb1:                                              ; preds = %start
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %buf, i64 24, i1 false)
  ret void
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h0807c0786a1c0b87E"(ptr sret([12 x i8]) align 4 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  store ptr %0, ptr %self2, align 8
  %1 = load ptr, ptr %self2, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_9 = load i64, ptr %2, align 8
  %3 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %3, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %4, ptr %self1, align 8
  %6 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %5, ptr %6, align 8
  store ptr %self, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store i32 0, ptr %_0, align 4
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  ret void

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %9 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %10, align 8
  store i64 1, ptr %self1, align 8
  store ptr %self, ptr %_4, align 8
  %11 = load ptr, ptr %_4, align 8
  store ptr %11, ptr %f, align 8
  %12 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %12, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 4) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 4
  %self3 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(u32, u32)>", ptr %self.0, i64 %x
  %_15.0 = load i32, ptr %self3, align 4
  %13 = getelementptr inbounds i8, ptr %self3, i64 4
  %_15.1 = load i32, ptr %13, align 4
  %14 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_15.0, ptr %14, align 4
  %15 = getelementptr inbounds i8, ptr %14, i64 4
  store i32 %_15.1, ptr %15, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal { i32, i32 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h63ed107f9f8e1fa8E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_0 = alloca [8 x i8], align 4
  store ptr %self, ptr %self2, align 8
  %0 = load ptr, ptr %self2, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %_9 = load i64, ptr %1, align 8
  %2 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %2, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %3 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %4 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %3, ptr %self1, align 8
  %5 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %4, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %6, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store i32 0, ptr %_0, align 4
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  %9 = load i32, ptr %_0, align 4
  %10 = getelementptr inbounds i8, ptr %_0, i64 4
  %11 = load i32, ptr %10, align 4
  %12 = insertvalue { i32, i32 } poison, i32 %9, 0
  %13 = insertvalue { i32, i32 } %12, i32 %11, 1
  ret { i32, i32 } %13

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %14 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %15, align 8
  store i64 1, ptr %self1, align 8
  %16 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %16, ptr %_4, align 8
  %17 = load ptr, ptr %_4, align 8
  store ptr %17, ptr %f, align 8
  %18 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %18, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 5) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 5
  %self3 = getelementptr inbounds nuw i32, ptr %self.0, i64 %x
  %_15 = load i32, ptr %self3, align 4
  %19 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_15, ptr %19, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal { i32, i32 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h7f2a77edb0ea368fE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_0 = alloca [8 x i8], align 4
  store ptr %self, ptr %self2, align 8
  %0 = load ptr, ptr %self2, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %_9 = load i64, ptr %1, align 8
  %2 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %2, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %3 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %4 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %3, ptr %self1, align 8
  %5 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %4, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %6, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store i32 0, ptr %_0, align 4
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  %9 = load i32, ptr %_0, align 4
  %10 = getelementptr inbounds i8, ptr %_0, i64 4
  %11 = load i32, ptr %10, align 4
  %12 = insertvalue { i32, i32 } poison, i32 %9, 0
  %13 = insertvalue { i32, i32 } %12, i32 %11, 1
  ret { i32, i32 } %13

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %14 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %15, align 8
  store i64 1, ptr %self1, align 8
  %16 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %16, ptr %_4, align 8
  %17 = load ptr, ptr %_4, align 8
  store ptr %17, ptr %f, align 8
  %18 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %18, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 5) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 5
  %self3 = getelementptr inbounds nuw i32, ptr %self.0, i64 %x
  %_15 = load i32, ptr %self3, align 4
  %19 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_15, ptr %19, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha967bcb7d2693914E"(ptr sret([12 x i8]) align 4 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 48
  store ptr %0, ptr %self2, align 8
  %1 = load ptr, ptr %self2, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_9 = load i64, ptr %2, align 8
  %3 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %3, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %4, ptr %self1, align 8
  %6 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %5, ptr %6, align 8
  store ptr %self, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store i32 0, ptr %_0, align 4
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  ret void

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %9 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %10, align 8
  store i64 1, ptr %self1, align 8
  store ptr %self, ptr %_4, align 8
  %11 = load ptr, ptr %_4, align 8
  store ptr %11, ptr %f, align 8
  %12 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %12, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 6) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 6
  %self3 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(u32, u32)>", ptr %self.0, i64 %x
  %_15.0 = load i32, ptr %self3, align 4
  %13 = getelementptr inbounds i8, ptr %self3, i64 4
  %_15.1 = load i32, ptr %13, align 4
  %14 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_15.0, ptr %14, align 4
  %15 = getelementptr inbounds i8, ptr %14, i64 4
  store i32 %_15.1, ptr %15, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hc2e328b6e25d217dE"(ptr sret([12 x i8]) align 4 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  store ptr %self, ptr %self2, align 8
  %0 = load ptr, ptr %self2, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %_9 = load i64, ptr %1, align 8
  %2 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %2, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %3 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %4 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %3, ptr %self1, align 8
  %5 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %4, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %6, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store i32 0, ptr %_0, align 4
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  ret void

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %9 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %10, align 8
  store i64 1, ptr %self1, align 8
  %11 = getelementptr inbounds i8, ptr %self, i64 16
  store ptr %11, ptr %_4, align 8
  %12 = load ptr, ptr %_4, align 8
  store ptr %12, ptr %f, align 8
  %13 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %13, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 5) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 5
  %self3 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<(i32, u32)>", ptr %self.0, i64 %x
  %_15.0 = load i32, ptr %self3, align 4
  %14 = getelementptr inbounds i8, ptr %self3, i64 4
  %_15.1 = load i32, ptr %14, align 4
  %15 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_15.0, ptr %15, align 4
  %16 = getelementptr inbounds i8, ptr %15, i64 4
  store i32 %_15.1, ptr %16, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5
}

; <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal { ptr, i64 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hcfe0a8bc35bac675E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %f = alloca [8 x i8], align 8
  %_4 = alloca [8 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 80
  store ptr %0, ptr %self2, align 8
  %1 = load ptr, ptr %self2, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_9 = load i64, ptr %2, align 8
  %3 = load ptr, ptr %self2, align 8
  %_10 = load i64, ptr %3, align 8
  %_6 = sub nuw i64 %_9, %_10
  %_5 = icmp ugt i64 %_6, 0
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.bcddbdd9e49de95895ea092998e0184f.0, i64 8), align 8
  store i64 %4, ptr %self1, align 8
  %6 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %5, ptr %6, align 8
  store ptr %self, ptr %_4, align 8
  %7 = load ptr, ptr %_4, align 8
  store ptr %7, ptr %f, align 8
  store ptr null, ptr %_0, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %8 = load ptr, ptr %self2, align 8
  %value = load i64, ptr %8, align 8
  br label %bb3

bb5:                                              ; preds = %bb7, %bb2
  %9 = load ptr, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  %11 = load i64, ptr %10, align 8
  %12 = insertvalue { ptr, i64 } poison, ptr %9, 0
  %13 = insertvalue { ptr, i64 } %12, i64 %11, 1
  ret { ptr, i64 } %13

bb3:                                              ; preds = %bb1
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h2e64ef16815b0621E"(i64 %value, i64 1) #22
  br label %bb4

bb4:                                              ; preds = %bb3
  %_11 = add nuw i64 %value, 1
  %14 = load ptr, ptr %self2, align 8
  store i64 %_11, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %value, ptr %15, align 8
  store i64 1, ptr %self1, align 8
  store ptr %self, ptr %_4, align 8
  %16 = load ptr, ptr %_4, align 8
  store ptr %16, ptr %f, align 8
  %17 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %17, align 8
  %_25 = load ptr, ptr %_4, align 8
  %self.0 = load ptr, ptr %_4, align 8
  br label %bb6

bb6:                                              ; preds = %bb4
; call <usize as core::slice::index::SliceIndex<[T]>>::get_unchecked::precondition_check
  call void @"_ZN75_$LT$usize$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$13get_unchecked18precondition_check17h4845bff5b0ee8e21E"(i64 %x, i64 5) #22
  br label %bb7

bb7:                                              ; preds = %bb6
  %_21 = icmp ult i64 %x, 5
  %self3 = getelementptr inbounds nuw %"core::mem::maybe_uninit::MaybeUninit<&str>", ptr %self.0, i64 %x
  %_15.0 = load ptr, ptr %self3, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 8
  %_15.1 = load i64, ptr %18, align 8
  store ptr %_15.0, ptr %_0, align 8
  %19 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_15.1, ptr %19, align 8
  br label %bb5
}

; _17_recursive_functions::main
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions4main17hd602de5f8643e1c9E() unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [48 x i8], align 4
  %1 = alloca [80 x i8], align 8
  %2 = alloca [80 x i8], align 8
  %3 = alloca [20 x i8], align 4
  %4 = alloca [32 x i8], align 4
  %5 = alloca [20 x i8], align 4
  %6 = alloca [40 x i8], align 4
  %_391 = alloca [48 x i8], align 8
  %_388 = alloca [48 x i8], align 8
  %_386 = alloca [1 x i8], align 1
  %_384 = alloca [16 x i8], align 8
  %_382 = alloca [16 x i8], align 8
  %_381 = alloca [1 x i8], align 1
  %_379 = alloca [16 x i8], align 8
  %_377 = alloca [16 x i8], align 8
  %_376 = alloca [64 x i8], align 8
  %_373 = alloca [48 x i8], align 8
  %i19 = alloca [4 x i8], align 4
  %_368 = alloca [8 x i8], align 4
  %iter18 = alloca [12 x i8], align 4
  %_366 = alloca [12 x i8], align 4
  %_365 = alloca [12 x i8], align 4
  %_363 = alloca [48 x i8], align 8
  %_360 = alloca [48 x i8], align 8
  %_357 = alloca [16 x i8], align 8
  %_355 = alloca [16 x i8], align 8
  %_353 = alloca [16 x i8], align 8
  %_352 = alloca [48 x i8], align 8
  %_349 = alloca [48 x i8], align 8
  %result17 = alloca [4 x i8], align 4
  %n = alloca [4 x i8], align 4
  %m = alloca [4 x i8], align 4
  %_342 = alloca [12 x i8], align 4
  %iter16 = alloca [64 x i8], align 8
  %_340 = alloca [64 x i8], align 8
  %ackermann_cases = alloca [48 x i8], align 4
  %_331 = alloca [48 x i8], align 8
  %_328 = alloca [48 x i8], align 8
  %_324 = alloca [48 x i8], align 8
  %_321 = alloca [48 x i8], align 8
  %_317 = alloca [48 x i8], align 8
  %_314 = alloca [48 x i8], align 8
  %_311 = alloca [16 x i8], align 8
  %_309 = alloca [16 x i8], align 8
  %_308 = alloca [32 x i8], align 8
  %_305 = alloca [48 x i8], align 8
  %result15 = alloca [1 x i8], align 1
  %s14 = alloca [16 x i8], align 8
  %_299 = alloca [16 x i8], align 8
  %iter13 = alloca [96 x i8], align 8
  %_297 = alloca [96 x i8], align 8
  %test_strings = alloca [80 x i8], align 8
  %_290 = alloca [48 x i8], align 8
  %_287 = alloca [48 x i8], align 8
  %_284 = alloca [16 x i8], align 8
  %_282 = alloca [16 x i8], align 8
  %_281 = alloca [32 x i8], align 8
  %_278 = alloca [48 x i8], align 8
  %result12 = alloca [24 x i8], align 8
  %s = alloca [16 x i8], align 8
  %_272 = alloca [16 x i8], align 8
  %iter11 = alloca [96 x i8], align 8
  %_270 = alloca [96 x i8], align 8
  %strings = alloca [80 x i8], align 8
  %_263 = alloca [48 x i8], align 8
  %_260 = alloca [48 x i8], align 8
  %_257 = alloca [16 x i8], align 8
  %_256 = alloca [16 x i8], align 8
  %_253 = alloca [48 x i8], align 8
  %_250 = alloca [16 x i8], align 8
  %_248 = alloca [16 x i8], align 8
  %_247 = alloca [32 x i8], align 8
  %_244 = alloca [48 x i8], align 8
  %index = alloca [8 x i8], align 8
  %_234 = alloca [16 x i8], align 8
  %value = alloca [4 x i8], align 4
  %_230 = alloca [8 x i8], align 4
  %iter10 = alloca [40 x i8], align 8
  %_228 = alloca [40 x i8], align 8
  %_226 = alloca [16 x i8], align 8
  %_225 = alloca [16 x i8], align 8
  %_222 = alloca [48 x i8], align 8
  %search_values = alloca [20 x i8], align 4
  %sorted_array = alloca [24 x i8], align 8
  %_212 = alloca [48 x i8], align 8
  %_209 = alloca [48 x i8], align 8
  %_206 = alloca [16 x i8], align 8
  %_204 = alloca [16 x i8], align 8
  %_202 = alloca [16 x i8], align 8
  %_201 = alloca [48 x i8], align 8
  %_198 = alloca [48 x i8], align 8
  %result9 = alloca [4 x i8], align 4
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_191 = alloca [12 x i8], align 4
  %iter8 = alloca [48 x i8], align 8
  %_189 = alloca [48 x i8], align 8
  %gcd_cases = alloca [32 x i8], align 4
  %_182 = alloca [48 x i8], align 8
  %_179 = alloca [48 x i8], align 8
  %_175 = alloca [4 x i8], align 4
  %_173 = alloca [16 x i8], align 8
  %_171 = alloca [16 x i8], align 8
  %_170 = alloca [32 x i8], align 8
  %_167 = alloca [48 x i8], align 8
  %_163 = alloca [4 x i8], align 4
  %_161 = alloca [16 x i8], align 8
  %_159 = alloca [16 x i8], align 8
  %_158 = alloca [32 x i8], align 8
  %_155 = alloca [48 x i8], align 8
  %_151 = alloca [4 x i8], align 4
  %_149 = alloca [16 x i8], align 8
  %_147 = alloca [16 x i8], align 8
  %_146 = alloca [32 x i8], align 8
  %_143 = alloca [48 x i8], align 8
  %arr3 = alloca [24 x i8], align 8
  %arr2 = alloca [24 x i8], align 8
  %arr1 = alloca [24 x i8], align 8
  %_122 = alloca [48 x i8], align 8
  %_119 = alloca [48 x i8], align 8
  %_115 = alloca [48 x i8], align 8
  %_112 = alloca [48 x i8], align 8
  %_108 = alloca [48 x i8], align 8
  %_105 = alloca [48 x i8], align 8
  %_102 = alloca [16 x i8], align 8
  %_100 = alloca [16 x i8], align 8
  %_99 = alloca [32 x i8], align 8
  %_96 = alloca [48 x i8], align 8
  %result7 = alloca [4 x i8], align 4
  %num = alloca [4 x i8], align 4
  %_90 = alloca [8 x i8], align 4
  %iter6 = alloca [40 x i8], align 8
  %_88 = alloca [40 x i8], align 8
  %numbers = alloca [20 x i8], align 4
  %_85 = alloca [48 x i8], align 8
  %_82 = alloca [48 x i8], align 8
  %_79 = alloca [16 x i8], align 8
  %_77 = alloca [16 x i8], align 8
  %_75 = alloca [16 x i8], align 8
  %_74 = alloca [48 x i8], align 8
  %_71 = alloca [48 x i8], align 8
  %result5 = alloca [4 x i8], align 4
  %exp = alloca [4 x i8], align 4
  %base = alloca [4 x i8], align 4
  %_64 = alloca [12 x i8], align 4
  %iter4 = alloca [56 x i8], align 8
  %_62 = alloca [56 x i8], align 8
  %test_cases = alloca [40 x i8], align 4
  %_54 = alloca [48 x i8], align 8
  %_51 = alloca [48 x i8], align 8
  %_48 = alloca [16 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %_45 = alloca [32 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %result3 = alloca [8 x i8], align 8
  %i2 = alloca [4 x i8], align 4
  %_36 = alloca [8 x i8], align 4
  %iter1 = alloca [12 x i8], align 4
  %_34 = alloca [12 x i8], align 4
  %_33 = alloca [12 x i8], align 4
  %_31 = alloca [48 x i8], align 8
  %_28 = alloca [48 x i8], align 8
  %_25 = alloca [16 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %_22 = alloca [32 x i8], align 8
  %_19 = alloca [48 x i8], align 8
  %result = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %_13 = alloca [16 x i8], align 8
  %iter = alloca [24 x i8], align 8
  %_11 = alloca [24 x i8], align 8
  %_10 = alloca [24 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_64b4e0d406e0ca8c31ed4f02f8879fab)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_a44dba70e82c2cc0fd33084e346830dc)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hbdbbbebcd6b92a30E"(ptr sret([24 x i8]) align 8 %_11, i64 0, i64 6)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h78f7f0bd72935a17E"(ptr sret([24 x i8]) align 8 %_10, ptr align 8 %_11)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %_10, i64 24, i1 false)
  br label %bb9

bb9:                                              ; preds = %bb12, %start
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %7 = call { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17ha17962aca932964cE"(ptr align 8 %iter)
  %8 = extractvalue { i64, i64 } %7, 0
  %9 = extractvalue { i64, i64 } %7, 1
  store i64 %8, ptr %_13, align 8
  %10 = getelementptr inbounds i8, ptr %_13, i64 8
  store i64 %9, ptr %10, align 8
  %_15 = load i64, ptr %_13, align 8
  %11 = getelementptr inbounds i8, ptr %_13, i64 8
  %12 = load i64, ptr %11, align 8
  %13 = trunc nuw i64 %_15 to i1
  br i1 %13, label %bb12, label %bb13

bb12:                                             ; preds = %bb9
  %14 = getelementptr inbounds i8, ptr %_13, i64 8
  %15 = load i64, ptr %14, align 8
  store i64 %15, ptr %i, align 8
  %16 = load i64, ptr %i, align 8
; call _17_recursive_functions::factorial
  %17 = call i64 @_ZN23_17_recursive_functions9factorial17h75d35075a2f2f839E(i64 %16)
  store i64 %17, ptr %result, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hd1f71f5107d3da0dE(ptr sret([16 x i8]) align 8 %_23, ptr align 8 %i)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hd1f71f5107d3da0dE(ptr sret([16 x i8]) align 8 %_25, ptr align 8 %result)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_23, i64 16, i1 false)
  %19 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_22, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %19, ptr align 8 %_25, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_19, ptr align 8 @alloc_95c9d1d8c902af68a1b8a497369a019d, ptr align 8 %_22)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_19)
  br label %bb9

bb13:                                             ; preds = %bb9
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_28, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_28)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_31, ptr align 8 @alloc_61a49013bdcdb2f49625df5ab8e6dec9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_31)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h7f8488775b7370d5E"(ptr sret([12 x i8]) align 4 %_34, i32 0, i32 10)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h9fef76169fc4766aE"(ptr sret([12 x i8]) align 4 %_33, ptr align 4 %_34)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter1, ptr align 4 %_33, i64 12, i1 false)
  br label %bb24

bb24:                                             ; preds = %bb26, %bb13
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %20 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h44bd7d420bfde20cE"(ptr align 4 %iter1)
  %21 = extractvalue { i32, i32 } %20, 0
  %22 = extractvalue { i32, i32 } %20, 1
  store i32 %21, ptr %_36, align 4
  %23 = getelementptr inbounds i8, ptr %_36, i64 4
  store i32 %22, ptr %23, align 4
  %24 = load i32, ptr %_36, align 4
  %25 = getelementptr inbounds i8, ptr %_36, i64 4
  %26 = load i32, ptr %25, align 4
  %_38 = zext i32 %24 to i64
  %27 = trunc nuw i64 %_38 to i1
  br i1 %27, label %bb26, label %bb27

bb26:                                             ; preds = %bb24
  %28 = getelementptr inbounds i8, ptr %_36, i64 4
  %29 = load i32, ptr %28, align 4
  store i32 %29, ptr %i2, align 4
  %30 = load i32, ptr %i2, align 4
; call _17_recursive_functions::fibonacci
  %31 = call i64 @_ZN23_17_recursive_functions9fibonacci17hb8979044c6a84d19E(i32 %30)
  store i64 %31, ptr %result3, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_46, ptr align 4 %i2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hd1f71f5107d3da0dE(ptr sret([16 x i8]) align 8 %_48, ptr align 8 %result3)
  %32 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_45, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %32, ptr align 8 %_46, i64 16, i1 false)
  %33 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_45, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %33, ptr align 8 %_48, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_42, ptr align 8 @alloc_e3c8132c8b135d9d43572e3bb9140baf, ptr align 8 %_45)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_42)
  br label %bb24

bb27:                                             ; preds = %bb24
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_51, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_51)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_54, ptr align 8 @alloc_1ce3868f9729bcfcc5d9a8b506a67e61)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_54)
  %34 = getelementptr inbounds nuw { i32, i32 }, ptr %test_cases, i64 0
  store i32 2, ptr %34, align 4
  %35 = getelementptr inbounds i8, ptr %34, i64 4
  store i32 0, ptr %35, align 4
  %36 = getelementptr inbounds nuw { i32, i32 }, ptr %test_cases, i64 1
  store i32 2, ptr %36, align 4
  %37 = getelementptr inbounds i8, ptr %36, i64 4
  store i32 1, ptr %37, align 4
  %38 = getelementptr inbounds nuw { i32, i32 }, ptr %test_cases, i64 2
  store i32 2, ptr %38, align 4
  %39 = getelementptr inbounds i8, ptr %38, i64 4
  store i32 3, ptr %39, align 4
  %40 = getelementptr inbounds nuw { i32, i32 }, ptr %test_cases, i64 3
  store i32 3, ptr %40, align 4
  %41 = getelementptr inbounds i8, ptr %40, i64 4
  store i32 4, ptr %41, align 4
  %42 = getelementptr inbounds nuw { i32, i32 }, ptr %test_cases, i64 4
  store i32 5, ptr %42, align 4
  %43 = getelementptr inbounds i8, ptr %42, i64 4
  store i32 2, ptr %43, align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %6, ptr align 4 %test_cases, i64 40, i1 false)
; call core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  call void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17hf472d7d761f11a4aE"(ptr sret([56 x i8]) align 8 %_62, ptr align 4 %6)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter4, ptr align 8 %_62, i64 56, i1 false)
  br label %bb37

bb37:                                             ; preds = %bb240, %bb27
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  invoke void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hc2e328b6e25d217dE"(ptr sret([12 x i8]) align 4 %_64, ptr align 8 %iter4)
          to label %bb38 unwind label %funclet_bb230

bb230:                                            ; preds = %funclet_bb230
; call core::ptr::drop_in_place<core::array::iter::IntoIter<(i32,u32),5_usize>>
  call void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$i32$C$u32$RP$$C$5_usize$GT$$GT$17hc8ee7abb4530aca2E"(ptr align 8 %iter4) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb231

funclet_bb230:                                    ; preds = %bb45, %bb44, %bb43, %bb42, %bb41, %bb39, %bb37
  %cleanuppad = cleanuppad within none []
  br label %bb230

bb38:                                             ; preds = %bb37
  %44 = load i32, ptr %_64, align 4
  %_66 = zext i32 %44 to i64
  %45 = trunc nuw i64 %_66 to i1
  br i1 %45, label %bb39, label %bb40

bb39:                                             ; preds = %bb38
  %46 = getelementptr inbounds i8, ptr %_64, i64 4
  %47 = load i32, ptr %46, align 4
  store i32 %47, ptr %base, align 4
  %48 = getelementptr inbounds i8, ptr %_64, i64 4
  %49 = getelementptr inbounds i8, ptr %48, i64 4
  %50 = load i32, ptr %49, align 4
  store i32 %50, ptr %exp, align 4
  %51 = load i32, ptr %base, align 4
  %52 = load i32, ptr %exp, align 4
; invoke _17_recursive_functions::power
  %53 = invoke i32 @_ZN23_17_recursive_functions5power17h557517e276411f78E(i32 %51, i32 %52)
          to label %bb41 unwind label %funclet_bb230

bb40:                                             ; preds = %bb38
; call core::ptr::drop_in_place<core::array::iter::IntoIter<(i32,u32),5_usize>>
  call void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$i32$C$u32$RP$$C$5_usize$GT$$GT$17hc8ee7abb4530aca2E"(ptr align 8 %iter4)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_82, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_82)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_85, ptr align 8 @alloc_705877947e7cebd5f272309701403661)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_85)
  %54 = getelementptr inbounds nuw i32, ptr %numbers, i64 0
  store i32 123, ptr %54, align 4
  %55 = getelementptr inbounds nuw i32, ptr %numbers, i64 1
  store i32 456, ptr %55, align 4
  %56 = getelementptr inbounds nuw i32, ptr %numbers, i64 2
  store i32 789, ptr %56, align 4
  %57 = getelementptr inbounds nuw i32, ptr %numbers, i64 3
  store i32 1000, ptr %57, align 4
  %58 = getelementptr inbounds nuw i32, ptr %numbers, i64 4
  store i32 9876, ptr %58, align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %5, ptr align 4 %numbers, i64 20, i1 false)
; call core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  call void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17hc7bf19b4a03d2b71E"(ptr sret([40 x i8]) align 8 %_88, ptr align 4 %5)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter6, ptr align 8 %_88, i64 40, i1 false)
  br label %bb52

bb52:                                             ; preds = %bb241, %bb40
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  %59 = invoke { i32, i32 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h63ed107f9f8e1fa8E"(ptr align 8 %iter6)
          to label %bb53 unwind label %funclet_bb229

bb229:                                            ; preds = %funclet_bb229
; call core::ptr::drop_in_place<core::array::iter::IntoIter<u32,5_usize>>
  call void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$u32$C$5_usize$GT$$GT$17hba2bc5985fc56f32E"(ptr align 8 %iter6) #21 [ "funclet"(token %cleanuppad20) ]
  cleanupret from %cleanuppad20 unwind label %funclet_bb231

funclet_bb229:                                    ; preds = %bb59, %bb58, %bb57, %bb56, %bb54, %bb52
  %cleanuppad20 = cleanuppad within none []
  br label %bb229

bb53:                                             ; preds = %bb52
  %60 = extractvalue { i32, i32 } %59, 0
  %61 = extractvalue { i32, i32 } %59, 1
  store i32 %60, ptr %_90, align 4
  %62 = getelementptr inbounds i8, ptr %_90, i64 4
  store i32 %61, ptr %62, align 4
  %63 = load i32, ptr %_90, align 4
  %64 = getelementptr inbounds i8, ptr %_90, i64 4
  %65 = load i32, ptr %64, align 4
  %_92 = zext i32 %63 to i64
  %66 = trunc nuw i64 %_92 to i1
  br i1 %66, label %bb54, label %bb55

bb54:                                             ; preds = %bb53
  %67 = getelementptr inbounds i8, ptr %_90, i64 4
  %68 = load i32, ptr %67, align 4
  store i32 %68, ptr %num, align 4
  %69 = load i32, ptr %num, align 4
; invoke _17_recursive_functions::sum_of_digits
  %70 = invoke i32 @_ZN23_17_recursive_functions13sum_of_digits17hc48efa6256f88e98E(i32 %69)
          to label %bb56 unwind label %funclet_bb229

bb55:                                             ; preds = %bb53
; call core::ptr::drop_in_place<core::array::iter::IntoIter<u32,5_usize>>
  call void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$u32$C$5_usize$GT$$GT$17hba2bc5985fc56f32E"(ptr align 8 %iter6)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_105, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_105)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_108, ptr align 8 @alloc_fdf02ba109da0885ec27c954c9dd2a45)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_108)
; call _17_recursive_functions::countdown
  call void @_ZN23_17_recursive_functions9countdown17h14f0c8fadc911e8fE(i32 5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_112, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_112)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_115, ptr align 8 @alloc_9b67f4a17accd1eca87f0b39c86ced54)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_115)
; call _17_recursive_functions::count_up
  call void @_ZN23_17_recursive_functions8count_up17hba5cd4502e815708E(i32 1, i32 5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_119, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_119)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_122, ptr align 8 @alloc_eccd77dbb1a2e86790a0b7f1e77977b4)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_122)
; call alloc::alloc::exchange_malloc
  %_128 = call ptr @_ZN5alloc5alloc15exchange_malloc17h00f394e30c08faa1E(i64 20, i64 4)
  %_416 = ptrtoint ptr %_128 to i64
  %_419 = and i64 %_416, 3
  %_420 = icmp eq i64 %_419, 0
  br i1 %_420, label %bb235, label %panic

bb235:                                            ; preds = %bb55
  %_422 = ptrtoint ptr %_128 to i64
  %_425 = icmp eq i64 %_422, 0
  %_426 = and i1 %_425, true
  %_427 = xor i1 %_426, true
  br i1 %_427, label %bb236, label %panic21

panic:                                            ; preds = %bb55
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_416, ptr align 8 @alloc_f8cffa1741cdc4aef3f502adbb177e9e) #20
  unreachable

bb236:                                            ; preds = %bb235
  %71 = getelementptr inbounds nuw i32, ptr %_128, i64 0
  store i32 1, ptr %71, align 4
  %72 = getelementptr inbounds nuw i32, ptr %_128, i64 1
  store i32 2, ptr %72, align 4
  %73 = getelementptr inbounds nuw i32, ptr %_128, i64 2
  store i32 3, ptr %73, align 4
  %74 = getelementptr inbounds nuw i32, ptr %_128, i64 3
  store i32 4, ptr %74, align 4
  %75 = getelementptr inbounds nuw i32, ptr %_128, i64 4
  store i32 5, ptr %75, align 4
; call alloc::slice::<impl [T]>::into_vec
  call void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h200db025701b5ee1E"(ptr sret([24 x i8]) align 8 %arr1, ptr align 4 %_128, i64 5)
; invoke alloc::alloc::exchange_malloc
  %_134 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h00f394e30c08faa1E(i64 12, i64 4)
          to label %bb77 unwind label %funclet_bb228

panic21:                                          ; preds = %bb235
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_f8cffa1741cdc4aef3f502adbb177e9e) #20
  unreachable

bb228:                                            ; preds = %funclet_bb228
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr1) #21 [ "funclet"(token %cleanuppad22) ]
  cleanupret from %cleanuppad22 unwind label %funclet_bb231

funclet_bb228:                                    ; preds = %bb227, %bb216, %bb237, %bb236
  %cleanuppad22 = cleanuppad within none []
  br label %bb228

bb77:                                             ; preds = %bb236
  %_410 = ptrtoint ptr %_134 to i64
  %_413 = and i64 %_410, 3
  %_414 = icmp eq i64 %_413, 0
  br i1 %_414, label %bb234, label %panic23

bb234:                                            ; preds = %bb77
  %_429 = ptrtoint ptr %_134 to i64
  %_432 = icmp eq i64 %_429, 0
  %_433 = and i1 %_432, true
  %_434 = xor i1 %_433, true
  br i1 %_434, label %bb237, label %panic24

panic23:                                          ; preds = %bb77
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_410, ptr align 8 @alloc_1ec47609f61d896dcd8c05bd1cc92c03) #20
  unreachable

bb237:                                            ; preds = %bb234
  %76 = getelementptr inbounds nuw i32, ptr %_134, i64 0
  store i32 10, ptr %76, align 4
  %77 = getelementptr inbounds nuw i32, ptr %_134, i64 1
  store i32 20, ptr %77, align 4
  %78 = getelementptr inbounds nuw i32, ptr %_134, i64 2
  store i32 30, ptr %78, align 4
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h200db025701b5ee1E"(ptr sret([24 x i8]) align 8 %arr2, ptr align 4 %_134, i64 3)
          to label %bb78 unwind label %funclet_bb228

panic24:                                          ; preds = %bb234
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_1ec47609f61d896dcd8c05bd1cc92c03) #20
  unreachable

bb78:                                             ; preds = %bb237
; invoke alloc::alloc::exchange_malloc
  %_140 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h00f394e30c08faa1E(i64 4, i64 4)
          to label %bb79 unwind label %funclet_bb227

bb227:                                            ; preds = %funclet_bb227
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr2) #21 [ "funclet"(token %cleanuppad25) ]
  cleanupret from %cleanuppad25 unwind label %funclet_bb228

funclet_bb227:                                    ; preds = %bb226, %bb215, %bb238, %bb78
  %cleanuppad25 = cleanuppad within none []
  br label %bb227

bb79:                                             ; preds = %bb78
  %_404 = ptrtoint ptr %_140 to i64
  %_407 = and i64 %_404, 3
  %_408 = icmp eq i64 %_407, 0
  br i1 %_408, label %bb233, label %panic26

bb233:                                            ; preds = %bb79
  %_436 = ptrtoint ptr %_140 to i64
  %_439 = icmp eq i64 %_436, 0
  %_440 = and i1 %_439, true
  %_441 = xor i1 %_440, true
  br i1 %_441, label %bb238, label %panic27

panic26:                                          ; preds = %bb79
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_404, ptr align 8 @alloc_f0ecbe073f1733bdf56f0c94effd1317) #20
  unreachable

bb238:                                            ; preds = %bb233
  %79 = getelementptr inbounds nuw i32, ptr %_140, i64 0
  store i32 7, ptr %79, align 4
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h200db025701b5ee1E"(ptr sret([24 x i8]) align 8 %arr3, ptr align 4 %_140, i64 1)
          to label %bb80 unwind label %funclet_bb227

panic27:                                          ; preds = %bb233
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_f0ecbe073f1733bdf56f0c94effd1317) #20
  unreachable

bb80:                                             ; preds = %bb238
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17h1ba06b3616f00608E(ptr sret([16 x i8]) align 8 %_147, ptr align 8 %arr1)
          to label %bb81 unwind label %funclet_bb226

bb226:                                            ; preds = %funclet_bb226
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr3) #21 [ "funclet"(token %cleanuppad28) ]
  cleanupret from %cleanuppad28 unwind label %funclet_bb227

funclet_bb226:                                    ; preds = %bb225, %bb224, %bb214, %bb239, %bb117, %bb116, %bb115, %bb114, %bb113, %bb107, %bb102, %bb101, %bb100, %bb99, %bb98, %bb97, %bb96, %bb95, %bb94, %bb93, %bb92, %bb91, %bb90, %bb89, %bb88, %bb87, %bb86, %bb85, %bb84, %bb83, %bb82, %bb81, %bb80
  %cleanuppad28 = cleanuppad within none []
  br label %bb226

bb81:                                             ; preds = %bb80
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %80 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17he7827c0f7f4741e1E"(ptr align 8 %arr1)
          to label %bb82 unwind label %funclet_bb226

bb82:                                             ; preds = %bb81
  %_152.0 = extractvalue { ptr, i64 } %80, 0
  %_152.1 = extractvalue { ptr, i64 } %80, 1
; invoke _17_recursive_functions::array_sum
  %81 = invoke i32 @_ZN23_17_recursive_functions9array_sum17ha2e841e9912cddcbE(ptr align 4 %_152.0, i64 %_152.1, i64 0)
          to label %bb83 unwind label %funclet_bb226

bb83:                                             ; preds = %bb82
  store i32 %81, ptr %_151, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_149, ptr align 4 %_151)
          to label %bb84 unwind label %funclet_bb226

bb84:                                             ; preds = %bb83
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_146, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_147, i64 16, i1 false)
  %83 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_146, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %83, ptr align 8 %_149, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_143, ptr align 8 @alloc_be4207bbf8d66e1a5d67360dd68642db, ptr align 8 %_146)
          to label %bb85 unwind label %funclet_bb226

bb85:                                             ; preds = %bb84
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_143)
          to label %bb86 unwind label %funclet_bb226

bb86:                                             ; preds = %bb85
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17h1ba06b3616f00608E(ptr sret([16 x i8]) align 8 %_159, ptr align 8 %arr2)
          to label %bb87 unwind label %funclet_bb226

bb87:                                             ; preds = %bb86
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %84 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17he7827c0f7f4741e1E"(ptr align 8 %arr2)
          to label %bb88 unwind label %funclet_bb226

bb88:                                             ; preds = %bb87
  %_164.0 = extractvalue { ptr, i64 } %84, 0
  %_164.1 = extractvalue { ptr, i64 } %84, 1
; invoke _17_recursive_functions::array_sum
  %85 = invoke i32 @_ZN23_17_recursive_functions9array_sum17ha2e841e9912cddcbE(ptr align 4 %_164.0, i64 %_164.1, i64 0)
          to label %bb89 unwind label %funclet_bb226

bb89:                                             ; preds = %bb88
  store i32 %85, ptr %_163, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_161, ptr align 4 %_163)
          to label %bb90 unwind label %funclet_bb226

bb90:                                             ; preds = %bb89
  %86 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %86, ptr align 8 %_159, i64 16, i1 false)
  %87 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %87, ptr align 8 %_161, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_155, ptr align 8 @alloc_be4207bbf8d66e1a5d67360dd68642db, ptr align 8 %_158)
          to label %bb91 unwind label %funclet_bb226

bb91:                                             ; preds = %bb90
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_155)
          to label %bb92 unwind label %funclet_bb226

bb92:                                             ; preds = %bb91
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17h1ba06b3616f00608E(ptr sret([16 x i8]) align 8 %_171, ptr align 8 %arr3)
          to label %bb93 unwind label %funclet_bb226

bb93:                                             ; preds = %bb92
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %88 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17he7827c0f7f4741e1E"(ptr align 8 %arr3)
          to label %bb94 unwind label %funclet_bb226

bb94:                                             ; preds = %bb93
  %_176.0 = extractvalue { ptr, i64 } %88, 0
  %_176.1 = extractvalue { ptr, i64 } %88, 1
; invoke _17_recursive_functions::array_sum
  %89 = invoke i32 @_ZN23_17_recursive_functions9array_sum17ha2e841e9912cddcbE(ptr align 4 %_176.0, i64 %_176.1, i64 0)
          to label %bb95 unwind label %funclet_bb226

bb95:                                             ; preds = %bb94
  store i32 %89, ptr %_175, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_173, ptr align 4 %_175)
          to label %bb96 unwind label %funclet_bb226

bb96:                                             ; preds = %bb95
  %90 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_170, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %90, ptr align 8 %_171, i64 16, i1 false)
  %91 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_170, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %91, ptr align 8 %_173, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_167, ptr align 8 @alloc_be4207bbf8d66e1a5d67360dd68642db, ptr align 8 %_170)
          to label %bb97 unwind label %funclet_bb226

bb97:                                             ; preds = %bb96
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_167)
          to label %bb98 unwind label %funclet_bb226

bb98:                                             ; preds = %bb97
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_179, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb99 unwind label %funclet_bb226

bb99:                                             ; preds = %bb98
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_179)
          to label %bb100 unwind label %funclet_bb226

bb100:                                            ; preds = %bb99
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_182, ptr align 8 @alloc_d38248cc105dde06c01a8906bfa7d26a)
          to label %bb101 unwind label %funclet_bb226

bb101:                                            ; preds = %bb100
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_182)
          to label %bb102 unwind label %funclet_bb226

bb102:                                            ; preds = %bb101
  %92 = getelementptr inbounds nuw { i32, i32 }, ptr %gcd_cases, i64 0
  store i32 48, ptr %92, align 4
  %93 = getelementptr inbounds i8, ptr %92, i64 4
  store i32 18, ptr %93, align 4
  %94 = getelementptr inbounds nuw { i32, i32 }, ptr %gcd_cases, i64 1
  store i32 100, ptr %94, align 4
  %95 = getelementptr inbounds i8, ptr %94, i64 4
  store i32 25, ptr %95, align 4
  %96 = getelementptr inbounds nuw { i32, i32 }, ptr %gcd_cases, i64 2
  store i32 17, ptr %96, align 4
  %97 = getelementptr inbounds i8, ptr %96, i64 4
  store i32 13, ptr %97, align 4
  %98 = getelementptr inbounds nuw { i32, i32 }, ptr %gcd_cases, i64 3
  store i32 56, ptr %98, align 4
  %99 = getelementptr inbounds i8, ptr %98, i64 4
  store i32 42, ptr %99, align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %4, ptr align 4 %gcd_cases, i64 32, i1 false)
; invoke core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  invoke void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h206f1e51676d31b1E"(ptr sret([48 x i8]) align 8 %_189, ptr align 4 %4)
          to label %bb103 unwind label %funclet_bb226

bb103:                                            ; preds = %bb102
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter8, ptr align 8 %_189, i64 48, i1 false)
  br label %bb104

bb104:                                            ; preds = %bb242, %bb103
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  invoke void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h0807c0786a1c0b87E"(ptr sret([12 x i8]) align 4 %_191, ptr align 8 %iter8)
          to label %bb105 unwind label %funclet_bb225

bb225:                                            ; preds = %funclet_bb225
; call core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),4_usize>>
  call void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$4_usize$GT$$GT$17hb613f0a3fb8fdb8dE"(ptr align 8 %iter8) #21 [ "funclet"(token %cleanuppad29) ]
  cleanupret from %cleanuppad29 unwind label %funclet_bb226

funclet_bb225:                                    ; preds = %bb112, %bb111, %bb110, %bb109, %bb108, %bb106, %bb104
  %cleanuppad29 = cleanuppad within none []
  br label %bb225

bb105:                                            ; preds = %bb104
  %100 = load i32, ptr %_191, align 4
  %_193 = zext i32 %100 to i64
  %101 = trunc nuw i64 %_193 to i1
  br i1 %101, label %bb106, label %bb107

bb106:                                            ; preds = %bb105
  %102 = getelementptr inbounds i8, ptr %_191, i64 4
  %103 = load i32, ptr %102, align 4
  store i32 %103, ptr %a, align 4
  %104 = getelementptr inbounds i8, ptr %_191, i64 4
  %105 = getelementptr inbounds i8, ptr %104, i64 4
  %106 = load i32, ptr %105, align 4
  store i32 %106, ptr %b, align 4
  %107 = load i32, ptr %a, align 4
  %108 = load i32, ptr %b, align 4
; invoke _17_recursive_functions::gcd
  %109 = invoke i32 @_ZN23_17_recursive_functions3gcd17h6cf7ef094f441d45E(i32 %107, i32 %108)
          to label %bb108 unwind label %funclet_bb225

bb107:                                            ; preds = %bb105
; invoke core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),4_usize>>
  invoke void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$4_usize$GT$$GT$17hb613f0a3fb8fdb8dE"(ptr align 8 %iter8)
          to label %bb113 unwind label %funclet_bb226

bb113:                                            ; preds = %bb107
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_209, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb114 unwind label %funclet_bb226

bb114:                                            ; preds = %bb113
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_209)
          to label %bb115 unwind label %funclet_bb226

bb115:                                            ; preds = %bb114
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_212, ptr align 8 @alloc_56c074e90d70bf6e71cf4d7dd670b972)
          to label %bb116 unwind label %funclet_bb226

bb116:                                            ; preds = %bb115
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_212)
          to label %bb117 unwind label %funclet_bb226

bb117:                                            ; preds = %bb116
; invoke alloc::alloc::exchange_malloc
  %_218 = invoke ptr @_ZN5alloc5alloc15exchange_malloc17h00f394e30c08faa1E(i64 40, i64 4)
          to label %bb118 unwind label %funclet_bb226

bb118:                                            ; preds = %bb117
  %_398 = ptrtoint ptr %_218 to i64
  %_401 = and i64 %_398, 3
  %_402 = icmp eq i64 %_401, 0
  br i1 %_402, label %bb232, label %panic30

bb232:                                            ; preds = %bb118
  %_443 = ptrtoint ptr %_218 to i64
  %_446 = icmp eq i64 %_443, 0
  %_447 = and i1 %_446, true
  %_448 = xor i1 %_447, true
  br i1 %_448, label %bb239, label %panic31

panic30:                                          ; preds = %bb118
; call core::panicking::panic_misaligned_pointer_dereference
  call void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64 4, i64 %_398, ptr align 8 @alloc_d5e35d4623f1fa94b2d1aa54a9c77a1d) #20
  unreachable

bb239:                                            ; preds = %bb232
  %110 = getelementptr inbounds nuw i32, ptr %_218, i64 0
  store i32 1, ptr %110, align 4
  %111 = getelementptr inbounds nuw i32, ptr %_218, i64 1
  store i32 3, ptr %111, align 4
  %112 = getelementptr inbounds nuw i32, ptr %_218, i64 2
  store i32 5, ptr %112, align 4
  %113 = getelementptr inbounds nuw i32, ptr %_218, i64 3
  store i32 7, ptr %113, align 4
  %114 = getelementptr inbounds nuw i32, ptr %_218, i64 4
  store i32 9, ptr %114, align 4
  %115 = getelementptr inbounds nuw i32, ptr %_218, i64 5
  store i32 11, ptr %115, align 4
  %116 = getelementptr inbounds nuw i32, ptr %_218, i64 6
  store i32 13, ptr %116, align 4
  %117 = getelementptr inbounds nuw i32, ptr %_218, i64 7
  store i32 15, ptr %117, align 4
  %118 = getelementptr inbounds nuw i32, ptr %_218, i64 8
  store i32 17, ptr %118, align 4
  %119 = getelementptr inbounds nuw i32, ptr %_218, i64 9
  store i32 19, ptr %119, align 4
; invoke alloc::slice::<impl [T]>::into_vec
  invoke void @"_ZN5alloc5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$8into_vec17h200db025701b5ee1E"(ptr sret([24 x i8]) align 8 %sorted_array, ptr align 4 %_218, i64 10)
          to label %bb119 unwind label %funclet_bb226

panic31:                                          ; preds = %bb232
; call core::panicking::panic_null_pointer_dereference
  call void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8 @alloc_d5e35d4623f1fa94b2d1aa54a9c77a1d) #20
  unreachable

bb119:                                            ; preds = %bb239
  %120 = getelementptr inbounds nuw i32, ptr %search_values, i64 0
  store i32 7, ptr %120, align 4
  %121 = getelementptr inbounds nuw i32, ptr %search_values, i64 1
  store i32 15, ptr %121, align 4
  %122 = getelementptr inbounds nuw i32, ptr %search_values, i64 2
  store i32 2, ptr %122, align 4
  %123 = getelementptr inbounds nuw i32, ptr %search_values, i64 3
  store i32 19, ptr %123, align 4
  %124 = getelementptr inbounds nuw i32, ptr %search_values, i64 4
  store i32 20, ptr %124, align 4
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17h1ba06b3616f00608E(ptr sret([16 x i8]) align 8 %_226, ptr align 8 %sorted_array)
          to label %bb120 unwind label %funclet_bb224

bb224:                                            ; preds = %funclet_bb224
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %sorted_array) #21 [ "funclet"(token %cleanuppad32) ]
  cleanupret from %cleanuppad32 unwind label %funclet_bb226

funclet_bb224:                                    ; preds = %bb223, %bb222, %bb220, %bb219, %bb210, %bb209, %bb208, %bb207, %bb206, %bb205, %bb204, %bb202, %bb213, %bb212, %bb211, %bb203, %bb200, %bb198, %bb197, %bb196, %bb195, %bb194, %bb193, %bb187, %bb182, %bb181, %bb180, %bb179, %bb178, %bb177, %bb176, %bb175, %bb174, %bb173, %bb172, %bb171, %bb170, %bb169, %bb168, %bb163, %bb158, %bb157, %bb156, %bb155, %bb154, %bb148, %bb143, %bb142, %bb141, %bb140, %bb139, %bb127, %bb122, %bb121, %bb120, %bb119
  %cleanuppad32 = cleanuppad within none []
  br label %bb224

bb120:                                            ; preds = %bb119
  %125 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_225, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %125, ptr align 8 %_226, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h6b8f50bbcd2996bfE(ptr sret([48 x i8]) align 8 %_222, ptr align 8 @alloc_5eb73c5548ea3e84d9d0be2033891f24, ptr align 8 %_225)
          to label %bb121 unwind label %funclet_bb224

bb121:                                            ; preds = %bb120
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_222)
          to label %bb122 unwind label %funclet_bb224

bb122:                                            ; preds = %bb121
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %3, ptr align 4 %search_values, i64 20, i1 false)
; invoke core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  invoke void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h17c2f2b69436e0a6E"(ptr sret([40 x i8]) align 8 %_228, ptr align 4 %3)
          to label %bb123 unwind label %funclet_bb224

bb123:                                            ; preds = %bb122
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter10, ptr align 8 %_228, i64 40, i1 false)
  br label %bb124

bb124:                                            ; preds = %bb243, %bb244, %bb123
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  %126 = invoke { i32, i32 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h7f2a77edb0ea368fE"(ptr align 8 %iter10)
          to label %bb125 unwind label %funclet_bb223

bb223:                                            ; preds = %funclet_bb223
; call core::ptr::drop_in_place<core::array::iter::IntoIter<i32,5_usize>>
  call void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$i32$C$5_usize$GT$$GT$17h55d98016f7bf68efE"(ptr align 8 %iter10) #21 [ "funclet"(token %cleanuppad33) ]
  cleanupret from %cleanuppad33 unwind label %funclet_bb224

funclet_bb223:                                    ; preds = %bb136, %bb135, %bb134, %bb133, %bb138, %bb137, %bb132, %bb130, %panic38, %bb128, %bb126, %bb124
  %cleanuppad33 = cleanuppad within none []
  br label %bb223

bb125:                                            ; preds = %bb124
  %127 = extractvalue { i32, i32 } %126, 0
  %128 = extractvalue { i32, i32 } %126, 1
  store i32 %127, ptr %_230, align 4
  %129 = getelementptr inbounds i8, ptr %_230, i64 4
  store i32 %128, ptr %129, align 4
  %130 = load i32, ptr %_230, align 4
  %131 = getelementptr inbounds i8, ptr %_230, i64 4
  %132 = load i32, ptr %131, align 4
  %_232 = zext i32 %130 to i64
  %133 = trunc nuw i64 %_232 to i1
  br i1 %133, label %bb126, label %bb127

bb126:                                            ; preds = %bb125
  %134 = getelementptr inbounds i8, ptr %_230, i64 4
  %135 = load i32, ptr %134, align 4
  store i32 %135, ptr %value, align 4
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %136 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17he7827c0f7f4741e1E"(ptr align 8 %sorted_array)
          to label %bb128 unwind label %funclet_bb223

bb127:                                            ; preds = %bb125
; invoke core::ptr::drop_in_place<core::array::iter::IntoIter<i32,5_usize>>
  invoke void @"_ZN4core3ptr69drop_in_place$LT$core..array..iter..IntoIter$LT$i32$C$5_usize$GT$$GT$17h55d98016f7bf68efE"(ptr align 8 %iter10)
          to label %bb139 unwind label %funclet_bb224

bb139:                                            ; preds = %bb127
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_260, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb140 unwind label %funclet_bb224

bb140:                                            ; preds = %bb139
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_260)
          to label %bb141 unwind label %funclet_bb224

bb141:                                            ; preds = %bb140
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_263, ptr align 8 @alloc_db99fc5e0adab0a98b16186ef439222b)
          to label %bb142 unwind label %funclet_bb224

bb142:                                            ; preds = %bb141
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_263)
          to label %bb143 unwind label %funclet_bb224

bb143:                                            ; preds = %bb142
  %137 = getelementptr inbounds nuw { ptr, i64 }, ptr %strings, i64 0
  store ptr @alloc_1a2b9f3efbe1a8edd339fa75af2334ed, ptr %137, align 8
  %138 = getelementptr inbounds i8, ptr %137, i64 8
  store i64 5, ptr %138, align 8
  %139 = getelementptr inbounds nuw { ptr, i64 }, ptr %strings, i64 1
  store ptr @alloc_7c732fad6c650313539f89243f1f4d8e, ptr %139, align 8
  %140 = getelementptr inbounds i8, ptr %139, i64 8
  store i64 5, ptr %140, align 8
  %141 = getelementptr inbounds nuw { ptr, i64 }, ptr %strings, i64 2
  store ptr @alloc_d233ed356d827862a215c86f4113316d, ptr %141, align 8
  %142 = getelementptr inbounds i8, ptr %141, i64 8
  store i64 4, ptr %142, align 8
  %143 = getelementptr inbounds nuw { ptr, i64 }, ptr %strings, i64 3
  store ptr @alloc_1b9326da9a3db45a9d373b3046406a76, ptr %143, align 8
  %144 = getelementptr inbounds i8, ptr %143, i64 8
  store i64 9, ptr %144, align 8
  %145 = getelementptr inbounds nuw { ptr, i64 }, ptr %strings, i64 4
  store ptr @alloc_3827bbb606bbdb0f0c926011c14ccc34, ptr %145, align 8
  %146 = getelementptr inbounds i8, ptr %145, i64 8
  store i64 1, ptr %146, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %strings, i64 80, i1 false)
; invoke core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  invoke void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17heff75b2b8d4a4dfaE"(ptr sret([96 x i8]) align 8 %_270, ptr align 8 %2)
          to label %bb144 unwind label %funclet_bb224

bb144:                                            ; preds = %bb143
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter11, ptr align 8 %_270, i64 96, i1 false)
  br label %bb145

bb145:                                            ; preds = %bb153, %bb144
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  %147 = invoke { ptr, i64 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hcfe0a8bc35bac675E"(ptr align 8 %iter11)
          to label %bb146 unwind label %funclet_bb222

bb222:                                            ; preds = %funclet_bb222
; call core::ptr::drop_in_place<core::array::iter::IntoIter<&str,5_usize>>
  call void @"_ZN4core3ptr73drop_in_place$LT$core..array..iter..IntoIter$LT$$RF$str$C$5_usize$GT$$GT$17hf8cf676cfdb4f768E"(ptr align 8 %iter11) #21 [ "funclet"(token %cleanuppad34) ]
  cleanupret from %cleanuppad34 unwind label %funclet_bb224

funclet_bb222:                                    ; preds = %bb221, %bb153, %bb147, %bb145
  %cleanuppad34 = cleanuppad within none []
  br label %bb222

bb146:                                            ; preds = %bb145
  %148 = extractvalue { ptr, i64 } %147, 0
  %149 = extractvalue { ptr, i64 } %147, 1
  store ptr %148, ptr %_272, align 8
  %150 = getelementptr inbounds i8, ptr %_272, i64 8
  store i64 %149, ptr %150, align 8
  %151 = load ptr, ptr %_272, align 8
  %152 = getelementptr inbounds i8, ptr %_272, i64 8
  %153 = load i64, ptr %152, align 8
  %154 = ptrtoint ptr %151 to i64
  %155 = icmp eq i64 %154, 0
  %_274 = select i1 %155, i64 0, i64 1
  %156 = trunc nuw i64 %_274 to i1
  br i1 %156, label %bb147, label %bb148

bb147:                                            ; preds = %bb146
  %157 = load ptr, ptr %_272, align 8
  %158 = getelementptr inbounds i8, ptr %_272, i64 8
  %159 = load i64, ptr %158, align 8
  store ptr %157, ptr %s, align 8
  %160 = getelementptr inbounds i8, ptr %s, i64 8
  store i64 %159, ptr %160, align 8
  %161 = load ptr, ptr %s, align 8
  %162 = getelementptr inbounds i8, ptr %s, i64 8
  %163 = load i64, ptr %162, align 8
; invoke _17_recursive_functions::reverse_string
  invoke void @_ZN23_17_recursive_functions14reverse_string17hb01d0e55cd09b5abE(ptr sret([24 x i8]) align 8 %result12, ptr align 1 %161, i64 %163)
          to label %bb149 unwind label %funclet_bb222

bb148:                                            ; preds = %bb146
; invoke core::ptr::drop_in_place<core::array::iter::IntoIter<&str,5_usize>>
  invoke void @"_ZN4core3ptr73drop_in_place$LT$core..array..iter..IntoIter$LT$$RF$str$C$5_usize$GT$$GT$17hf8cf676cfdb4f768E"(ptr align 8 %iter11)
          to label %bb154 unwind label %funclet_bb224

bb154:                                            ; preds = %bb148
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_287, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb155 unwind label %funclet_bb224

bb155:                                            ; preds = %bb154
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_287)
          to label %bb156 unwind label %funclet_bb224

bb156:                                            ; preds = %bb155
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_290, ptr align 8 @alloc_41d2655ec8882a42263885fcc9f24a8b)
          to label %bb157 unwind label %funclet_bb224

bb157:                                            ; preds = %bb156
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_290)
          to label %bb158 unwind label %funclet_bb224

bb158:                                            ; preds = %bb157
  %164 = getelementptr inbounds nuw { ptr, i64 }, ptr %test_strings, i64 0
  store ptr @alloc_191c37045ef96d59740c75ae643e5db1, ptr %164, align 8
  %165 = getelementptr inbounds i8, ptr %164, i64 8
  store i64 7, ptr %165, align 8
  %166 = getelementptr inbounds nuw { ptr, i64 }, ptr %test_strings, i64 1
  store ptr @alloc_1a2b9f3efbe1a8edd339fa75af2334ed, ptr %166, align 8
  %167 = getelementptr inbounds i8, ptr %166, i64 8
  store i64 5, ptr %167, align 8
  %168 = getelementptr inbounds nuw { ptr, i64 }, ptr %test_strings, i64 2
  store ptr @alloc_6a15c540cefe4c6587e04d65caef7aa0, ptr %168, align 8
  %169 = getelementptr inbounds i8, ptr %168, i64 8
  store i64 5, ptr %169, align 8
  %170 = getelementptr inbounds nuw { ptr, i64 }, ptr %test_strings, i64 3
  store ptr @alloc_d233ed356d827862a215c86f4113316d, ptr %170, align 8
  %171 = getelementptr inbounds i8, ptr %170, i64 8
  store i64 4, ptr %171, align 8
  %172 = getelementptr inbounds nuw { ptr, i64 }, ptr %test_strings, i64 4
  store ptr @alloc_0111fe86b05ecb68d77beaa0646d3caa, ptr %172, align 8
  %173 = getelementptr inbounds i8, ptr %172, i64 8
  store i64 5, ptr %173, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %test_strings, i64 80, i1 false)
; invoke core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  invoke void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17heff75b2b8d4a4dfaE"(ptr sret([96 x i8]) align 8 %_297, ptr align 8 %1)
          to label %bb159 unwind label %funclet_bb224

bb159:                                            ; preds = %bb158
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter13, ptr align 8 %_297, i64 96, i1 false)
  br label %bb160

bb160:                                            ; preds = %bb245, %bb159
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  %174 = invoke { ptr, i64 } @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hcfe0a8bc35bac675E"(ptr align 8 %iter13)
          to label %bb161 unwind label %funclet_bb220

bb220:                                            ; preds = %funclet_bb220
; call core::ptr::drop_in_place<core::array::iter::IntoIter<&str,5_usize>>
  call void @"_ZN4core3ptr73drop_in_place$LT$core..array..iter..IntoIter$LT$$RF$str$C$5_usize$GT$$GT$17hf8cf676cfdb4f768E"(ptr align 8 %iter13) #21 [ "funclet"(token %cleanuppad35) ]
  cleanupret from %cleanuppad35 unwind label %funclet_bb224

funclet_bb220:                                    ; preds = %bb167, %bb166, %bb165, %bb164, %bb162, %bb160
  %cleanuppad35 = cleanuppad within none []
  br label %bb220

bb161:                                            ; preds = %bb160
  %175 = extractvalue { ptr, i64 } %174, 0
  %176 = extractvalue { ptr, i64 } %174, 1
  store ptr %175, ptr %_299, align 8
  %177 = getelementptr inbounds i8, ptr %_299, i64 8
  store i64 %176, ptr %177, align 8
  %178 = load ptr, ptr %_299, align 8
  %179 = getelementptr inbounds i8, ptr %_299, i64 8
  %180 = load i64, ptr %179, align 8
  %181 = ptrtoint ptr %178 to i64
  %182 = icmp eq i64 %181, 0
  %_301 = select i1 %182, i64 0, i64 1
  %183 = trunc nuw i64 %_301 to i1
  br i1 %183, label %bb162, label %bb163

bb162:                                            ; preds = %bb161
  %184 = load ptr, ptr %_299, align 8
  %185 = getelementptr inbounds i8, ptr %_299, i64 8
  %186 = load i64, ptr %185, align 8
  store ptr %184, ptr %s14, align 8
  %187 = getelementptr inbounds i8, ptr %s14, i64 8
  store i64 %186, ptr %187, align 8
  %188 = load ptr, ptr %s14, align 8
  %189 = getelementptr inbounds i8, ptr %s14, i64 8
  %190 = load i64, ptr %189, align 8
; invoke _17_recursive_functions::is_palindrome
  %191 = invoke zeroext i1 @_ZN23_17_recursive_functions13is_palindrome17h34ae266b97157ee9E(ptr align 1 %188, i64 %190)
          to label %bb164 unwind label %funclet_bb220

bb163:                                            ; preds = %bb161
; invoke core::ptr::drop_in_place<core::array::iter::IntoIter<&str,5_usize>>
  invoke void @"_ZN4core3ptr73drop_in_place$LT$core..array..iter..IntoIter$LT$$RF$str$C$5_usize$GT$$GT$17hf8cf676cfdb4f768E"(ptr align 8 %iter13)
          to label %bb168 unwind label %funclet_bb224

bb168:                                            ; preds = %bb163
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_314, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb169 unwind label %funclet_bb224

bb169:                                            ; preds = %bb168
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_314)
          to label %bb170 unwind label %funclet_bb224

bb170:                                            ; preds = %bb169
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_317, ptr align 8 @alloc_f0511044a6651f6750f11d280e801c63)
          to label %bb171 unwind label %funclet_bb224

bb171:                                            ; preds = %bb170
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_317)
          to label %bb172 unwind label %funclet_bb224

bb172:                                            ; preds = %bb171
; invoke _17_recursive_functions::print_tree_levels
  invoke void @_ZN23_17_recursive_functions17print_tree_levels17hbd803eb1c62b2b87E(i32 1, i32 4)
          to label %bb173 unwind label %funclet_bb224

bb173:                                            ; preds = %bb172
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_321, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb174 unwind label %funclet_bb224

bb174:                                            ; preds = %bb173
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_321)
          to label %bb175 unwind label %funclet_bb224

bb175:                                            ; preds = %bb174
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_324, ptr align 8 @alloc_003ad12aefee4d69adef1eb46f04345e)
          to label %bb176 unwind label %funclet_bb224

bb176:                                            ; preds = %bb175
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_324)
          to label %bb177 unwind label %funclet_bb224

bb177:                                            ; preds = %bb176
; invoke _17_recursive_functions::hanoi
  invoke void @_ZN23_17_recursive_functions5hanoi17heb75ee7238955714E(i32 3, i32 65, i32 67, i32 66)
          to label %bb178 unwind label %funclet_bb224

bb178:                                            ; preds = %bb177
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_328, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb179 unwind label %funclet_bb224

bb179:                                            ; preds = %bb178
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_328)
          to label %bb180 unwind label %funclet_bb224

bb180:                                            ; preds = %bb179
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_331, ptr align 8 @alloc_a494a1f0877ec3d3a256ebdd65271b27)
          to label %bb181 unwind label %funclet_bb224

bb181:                                            ; preds = %bb180
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_331)
          to label %bb182 unwind label %funclet_bb224

bb182:                                            ; preds = %bb181
  %192 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 0
  store i32 0, ptr %192, align 4
  %193 = getelementptr inbounds i8, ptr %192, i64 4
  store i32 0, ptr %193, align 4
  %194 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 1
  store i32 0, ptr %194, align 4
  %195 = getelementptr inbounds i8, ptr %194, i64 4
  store i32 1, ptr %195, align 4
  %196 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 2
  store i32 1, ptr %196, align 4
  %197 = getelementptr inbounds i8, ptr %196, i64 4
  store i32 0, ptr %197, align 4
  %198 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 3
  store i32 1, ptr %198, align 4
  %199 = getelementptr inbounds i8, ptr %198, i64 4
  store i32 1, ptr %199, align 4
  %200 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 4
  store i32 2, ptr %200, align 4
  %201 = getelementptr inbounds i8, ptr %200, i64 4
  store i32 2, ptr %201, align 4
  %202 = getelementptr inbounds nuw { i32, i32 }, ptr %ackermann_cases, i64 5
  store i32 3, ptr %202, align 4
  %203 = getelementptr inbounds i8, ptr %202, i64 4
  store i32 1, ptr %203, align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %0, ptr align 4 %ackermann_cases, i64 48, i1 false)
; invoke core::array::iter::<impl core::iter::traits::collect::IntoIterator for [T; N]>::into_iter
  invoke void @"_ZN4core5array4iter94_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha477cfae2ad9af39E"(ptr sret([64 x i8]) align 8 %_340, ptr align 4 %0)
          to label %bb183 unwind label %funclet_bb224

bb183:                                            ; preds = %bb182
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter16, ptr align 8 %_340, i64 64, i1 false)
  br label %bb184

bb184:                                            ; preds = %bb246, %bb183
; invoke <core::array::iter::IntoIter<T,_> as core::iter::traits::iterator::Iterator>::next
  invoke void @"_ZN99_$LT$core..array..iter..IntoIter$LT$T$C$_$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha967bcb7d2693914E"(ptr sret([12 x i8]) align 4 %_342, ptr align 8 %iter16)
          to label %bb185 unwind label %funclet_bb219

bb219:                                            ; preds = %funclet_bb219
; call core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),6_usize>>
  call void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$6_usize$GT$$GT$17hcd1090275dc54866E"(ptr align 8 %iter16) #21 [ "funclet"(token %cleanuppad36) ]
  cleanupret from %cleanuppad36 unwind label %funclet_bb224

funclet_bb219:                                    ; preds = %bb192, %bb191, %bb190, %bb189, %bb188, %bb186, %bb184
  %cleanuppad36 = cleanuppad within none []
  br label %bb219

bb185:                                            ; preds = %bb184
  %204 = load i32, ptr %_342, align 4
  %_344 = zext i32 %204 to i64
  %205 = trunc nuw i64 %_344 to i1
  br i1 %205, label %bb186, label %bb187

bb186:                                            ; preds = %bb185
  %206 = getelementptr inbounds i8, ptr %_342, i64 4
  %207 = load i32, ptr %206, align 4
  store i32 %207, ptr %m, align 4
  %208 = getelementptr inbounds i8, ptr %_342, i64 4
  %209 = getelementptr inbounds i8, ptr %208, i64 4
  %210 = load i32, ptr %209, align 4
  store i32 %210, ptr %n, align 4
  %211 = load i32, ptr %m, align 4
  %212 = load i32, ptr %n, align 4
; invoke _17_recursive_functions::ackermann
  %213 = invoke i32 @_ZN23_17_recursive_functions9ackermann17h102b3040c3f90c0eE(i32 %211, i32 %212)
          to label %bb188 unwind label %funclet_bb219

bb187:                                            ; preds = %bb185
; invoke core::ptr::drop_in_place<core::array::iter::IntoIter<(u32,u32),6_usize>>
  invoke void @"_ZN4core3ptr83drop_in_place$LT$core..array..iter..IntoIter$LT$$LP$u32$C$u32$RP$$C$6_usize$GT$$GT$17hcd1090275dc54866E"(ptr align 8 %iter16)
          to label %bb193 unwind label %funclet_bb224

bb193:                                            ; preds = %bb187
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_360, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb194 unwind label %funclet_bb224

bb194:                                            ; preds = %bb193
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_360)
          to label %bb195 unwind label %funclet_bb224

bb195:                                            ; preds = %bb194
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_363, ptr align 8 @alloc_2e9c7c2afd1acf724eab8fbd301888c6)
          to label %bb196 unwind label %funclet_bb224

bb196:                                            ; preds = %bb195
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_363)
          to label %bb197 unwind label %funclet_bb224

bb197:                                            ; preds = %bb196
; invoke core::ops::range::RangeInclusive<Idx>::new
  invoke void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h7f8488775b7370d5E"(ptr sret([12 x i8]) align 4 %_366, i32 0, i32 6)
          to label %bb198 unwind label %funclet_bb224

bb198:                                            ; preds = %bb197
; invoke <I as core::iter::traits::collect::IntoIterator>::into_iter
  invoke void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h9fef76169fc4766aE"(ptr sret([12 x i8]) align 4 %_365, ptr align 4 %_366)
          to label %bb199 unwind label %funclet_bb224

bb199:                                            ; preds = %bb198
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter18, ptr align 4 %_365, i64 12, i1 false)
  br label %bb200

bb200:                                            ; preds = %bb247, %bb199
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %214 = invoke { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h44bd7d420bfde20cE"(ptr align 4 %iter18)
          to label %bb201 unwind label %funclet_bb224

bb201:                                            ; preds = %bb200
  %215 = extractvalue { i32, i32 } %214, 0
  %216 = extractvalue { i32, i32 } %214, 1
  store i32 %215, ptr %_368, align 4
  %217 = getelementptr inbounds i8, ptr %_368, i64 4
  store i32 %216, ptr %217, align 4
  %218 = load i32, ptr %_368, align 4
  %219 = getelementptr inbounds i8, ptr %_368, i64 4
  %220 = load i32, ptr %219, align 4
  %_370 = zext i32 %218 to i64
  %221 = trunc nuw i64 %_370 to i1
  br i1 %221, label %bb202, label %bb203

bb202:                                            ; preds = %bb201
  %222 = getelementptr inbounds i8, ptr %_368, i64 4
  %223 = load i32, ptr %222, align 4
  store i32 %223, ptr %i19, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_377, ptr align 4 %i19)
          to label %bb204 unwind label %funclet_bb224

bb203:                                            ; preds = %bb201
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_388, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb211 unwind label %funclet_bb224

bb211:                                            ; preds = %bb203
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_388)
          to label %bb212 unwind label %funclet_bb224

bb212:                                            ; preds = %bb211
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_391, ptr align 8 @alloc_b1c7eddc56bd7db5575cd5ad8b2094c4)
          to label %bb213 unwind label %funclet_bb224

bb213:                                            ; preds = %bb212
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_391)
          to label %bb214 unwind label %funclet_bb224

bb214:                                            ; preds = %bb213
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %sorted_array)
          to label %bb215 unwind label %funclet_bb226

bb215:                                            ; preds = %bb214
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr3)
          to label %bb216 unwind label %funclet_bb227

bb216:                                            ; preds = %bb215
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr2)
          to label %bb217 unwind label %funclet_bb228

bb217:                                            ; preds = %bb216
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hc1655e02fb5fe42fE"(ptr align 8 %arr1)
  ret void

bb204:                                            ; preds = %bb202
  %224 = load i32, ptr %i19, align 4
; invoke _17_recursive_functions::is_even_mutual
  %225 = invoke zeroext i1 @_ZN23_17_recursive_functions14is_even_mutual17h65d7f8ae3e1b9eb9E(i32 %224)
          to label %bb205 unwind label %funclet_bb224

bb205:                                            ; preds = %bb204
  %226 = zext i1 %225 to i8
  store i8 %226, ptr %_381, align 1
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h9ffeb419df46888aE(ptr sret([16 x i8]) align 8 %_379, ptr align 1 %_381)
          to label %bb206 unwind label %funclet_bb224

bb206:                                            ; preds = %bb205
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_382, ptr align 4 %i19)
          to label %bb207 unwind label %funclet_bb224

bb207:                                            ; preds = %bb206
  %227 = load i32, ptr %i19, align 4
; invoke _17_recursive_functions::is_odd_mutual
  %228 = invoke zeroext i1 @_ZN23_17_recursive_functions13is_odd_mutual17h4ec99e8d08200a2eE(i32 %227)
          to label %bb208 unwind label %funclet_bb224

bb208:                                            ; preds = %bb207
  %229 = zext i1 %228 to i8
  store i8 %229, ptr %_386, align 1
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h9ffeb419df46888aE(ptr sret([16 x i8]) align 8 %_384, ptr align 1 %_386)
          to label %bb209 unwind label %funclet_bb224

bb209:                                            ; preds = %bb208
  %230 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_376, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %230, ptr align 8 %_377, i64 16, i1 false)
  %231 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_376, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %231, ptr align 8 %_379, i64 16, i1 false)
  %232 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_376, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %232, ptr align 8 %_382, i64 16, i1 false)
  %233 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_376, i64 3
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %233, ptr align 8 %_384, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hdcf55d5e1a81c2adE(ptr sret([48 x i8]) align 8 %_373, ptr align 8 @alloc_5ed072b1be6621704702a5ae35738efe, ptr align 8 %_376)
          to label %bb210 unwind label %funclet_bb224

bb210:                                            ; preds = %bb209
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_373)
          to label %bb247 unwind label %funclet_bb224

bb247:                                            ; preds = %bb210
  br label %bb200

bb188:                                            ; preds = %bb186
  store i32 %213, ptr %result17, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_353, ptr align 4 %m)
          to label %bb189 unwind label %funclet_bb219

bb189:                                            ; preds = %bb188
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_355, ptr align 4 %n)
          to label %bb190 unwind label %funclet_bb219

bb190:                                            ; preds = %bb189
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_357, ptr align 4 %result17)
          to label %bb191 unwind label %funclet_bb219

bb191:                                            ; preds = %bb190
  %234 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_352, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %234, ptr align 8 %_353, i64 16, i1 false)
  %235 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_352, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %235, ptr align 8 %_355, i64 16, i1 false)
  %236 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_352, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %236, ptr align 8 %_357, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h71e372ae6819746eE(ptr sret([48 x i8]) align 8 %_349, ptr align 8 @alloc_ede1707ef0f484e2941bcc042780d668, ptr align 8 %_352)
          to label %bb192 unwind label %funclet_bb219

bb192:                                            ; preds = %bb191
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_349)
          to label %bb246 unwind label %funclet_bb219

bb246:                                            ; preds = %bb192
  br label %bb184

bb164:                                            ; preds = %bb162
  %237 = zext i1 %191 to i8
  store i8 %237, ptr %result15, align 1
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hc1e393a05464dbd2E(ptr sret([16 x i8]) align 8 %_309, ptr align 8 %s14)
          to label %bb165 unwind label %funclet_bb220

bb165:                                            ; preds = %bb164
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h9ffeb419df46888aE(ptr sret([16 x i8]) align 8 %_311, ptr align 1 %result15)
          to label %bb166 unwind label %funclet_bb220

bb166:                                            ; preds = %bb165
  %238 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_308, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %238, ptr align 8 %_309, i64 16, i1 false)
  %239 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_308, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %239, ptr align 8 %_311, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_305, ptr align 8 @alloc_09b805f8faac1ab4caaa972084f924c4, ptr align 8 %_308)
          to label %bb167 unwind label %funclet_bb220

bb167:                                            ; preds = %bb166
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_305)
          to label %bb245 unwind label %funclet_bb220

bb245:                                            ; preds = %bb167
  br label %bb160

bb149:                                            ; preds = %bb147
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hc1e393a05464dbd2E(ptr sret([16 x i8]) align 8 %_282, ptr align 8 %s)
          to label %bb150 unwind label %funclet_bb221

bb221:                                            ; preds = %funclet_bb221
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %result12) #21 [ "funclet"(token %cleanuppad37) ]
  cleanupret from %cleanuppad37 unwind label %funclet_bb222

funclet_bb221:                                    ; preds = %bb152, %bb151, %bb150, %bb149
  %cleanuppad37 = cleanuppad within none []
  br label %bb221

bb150:                                            ; preds = %bb149
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbba4d01832b4100aE(ptr sret([16 x i8]) align 8 %_284, ptr align 8 %result12)
          to label %bb151 unwind label %funclet_bb221

bb151:                                            ; preds = %bb150
  %240 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_281, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %240, ptr align 8 %_282, i64 16, i1 false)
  %241 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_281, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %241, ptr align 8 %_284, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_278, ptr align 8 @alloc_b379d42aa66d04767471318a97b55db7, ptr align 8 %_281)
          to label %bb152 unwind label %funclet_bb221

bb152:                                            ; preds = %bb151
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_278)
          to label %bb153 unwind label %funclet_bb221

bb153:                                            ; preds = %bb152
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %result12)
          to label %bb145 unwind label %funclet_bb222

bb128:                                            ; preds = %bb126
  %_235.0 = extractvalue { ptr, i64 } %136, 0
  %_235.1 = extractvalue { ptr, i64 } %136, 1
; invoke alloc::vec::Vec<T,A>::len
  %_238 = invoke i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17h053e695db16c8d8fE"(ptr align 8 %sorted_array)
          to label %bb129 unwind label %funclet_bb223

bb129:                                            ; preds = %bb128
  %_240.0 = sub i64 %_238, 1
  %_240.1 = icmp ult i64 %_238, 1
  br i1 %_240.1, label %panic38, label %bb130

bb130:                                            ; preds = %bb129
  %242 = load i32, ptr %value, align 4
; invoke _17_recursive_functions::binary_search
  %243 = invoke { i64, i64 } @_ZN23_17_recursive_functions13binary_search17h4da70433afe785dfE(ptr align 4 %_235.0, i64 %_235.1, i32 %242, i64 0, i64 %_240.0)
          to label %bb131 unwind label %funclet_bb223

panic38:                                          ; preds = %bb129
; invoke core::panicking::panic_const::panic_const_sub_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_79b239743907a87469f0ecba537fedcc) #23
          to label %unreachable unwind label %funclet_bb223

unreachable:                                      ; preds = %panic38
  unreachable

bb131:                                            ; preds = %bb130
  %244 = extractvalue { i64, i64 } %243, 0
  %245 = extractvalue { i64, i64 } %243, 1
  store i64 %244, ptr %_234, align 8
  %246 = getelementptr inbounds i8, ptr %_234, i64 8
  store i64 %245, ptr %246, align 8
  %_241 = load i64, ptr %_234, align 8
  %247 = getelementptr inbounds i8, ptr %_234, i64 8
  %248 = load i64, ptr %247, align 8
  %249 = trunc nuw i64 %_241 to i1
  br i1 %249, label %bb133, label %bb132

bb133:                                            ; preds = %bb131
  %250 = getelementptr inbounds i8, ptr %_234, i64 8
  %251 = load i64, ptr %250, align 8
  store i64 %251, ptr %index, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_248, ptr align 4 %value)
          to label %bb134 unwind label %funclet_bb223

bb132:                                            ; preds = %bb131
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_257, ptr align 4 %value)
          to label %bb137 unwind label %funclet_bb223

bb137:                                            ; preds = %bb132
  %252 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_256, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %252, ptr align 8 %_257, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h6b8f50bbcd2996bfE(ptr sret([48 x i8]) align 8 %_253, ptr align 8 @alloc_fb69082d1ea7125e2b0811b8a9f81b92, ptr align 8 %_256)
          to label %bb138 unwind label %funclet_bb223

bb138:                                            ; preds = %bb137
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_253)
          to label %bb244 unwind label %funclet_bb223

bb244:                                            ; preds = %bb138
  br label %bb124

bb134:                                            ; preds = %bb133
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h91f4a535cacdb1caE(ptr sret([16 x i8]) align 8 %_250, ptr align 8 %index)
          to label %bb135 unwind label %funclet_bb223

bb135:                                            ; preds = %bb134
  %253 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %253, ptr align 8 %_248, i64 16, i1 false)
  %254 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %254, ptr align 8 %_250, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_244, ptr align 8 @alloc_e5f1a1511db0fd5e30967b04b9b68f64, ptr align 8 %_247)
          to label %bb136 unwind label %funclet_bb223

bb136:                                            ; preds = %bb135
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_244)
          to label %bb243 unwind label %funclet_bb223

bb243:                                            ; preds = %bb136
  br label %bb124

bb108:                                            ; preds = %bb106
  store i32 %109, ptr %result9, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_202, ptr align 4 %a)
          to label %bb109 unwind label %funclet_bb225

bb109:                                            ; preds = %bb108
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_204, ptr align 4 %b)
          to label %bb110 unwind label %funclet_bb225

bb110:                                            ; preds = %bb109
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_206, ptr align 4 %result9)
          to label %bb111 unwind label %funclet_bb225

bb111:                                            ; preds = %bb110
  %255 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_201, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %255, ptr align 8 %_202, i64 16, i1 false)
  %256 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_201, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %256, ptr align 8 %_204, i64 16, i1 false)
  %257 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_201, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %257, ptr align 8 %_206, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h71e372ae6819746eE(ptr sret([48 x i8]) align 8 %_198, ptr align 8 @alloc_07cb76ecca8e1aa156f072cc2d146651, ptr align 8 %_201)
          to label %bb112 unwind label %funclet_bb225

bb112:                                            ; preds = %bb111
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_198)
          to label %bb242 unwind label %funclet_bb225

bb242:                                            ; preds = %bb112
  br label %bb104

bb231:                                            ; preds = %funclet_bb231
  cleanupret from %cleanuppad39 unwind to caller

funclet_bb231:                                    ; preds = %bb230, %bb229, %bb228
  %cleanuppad39 = cleanuppad within none []
  br label %bb231

bb56:                                             ; preds = %bb54
  store i32 %70, ptr %result7, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_100, ptr align 4 %num)
          to label %bb57 unwind label %funclet_bb229

bb57:                                             ; preds = %bb56
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_102, ptr align 4 %result7)
          to label %bb58 unwind label %funclet_bb229

bb58:                                             ; preds = %bb57
  %258 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %258, ptr align 8 %_100, i64 16, i1 false)
  %259 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %259, ptr align 8 %_102, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_96, ptr align 8 @alloc_39f2698779bf62590e50a5c459d2e7f4, ptr align 8 %_99)
          to label %bb59 unwind label %funclet_bb229

bb59:                                             ; preds = %bb58
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_96)
          to label %bb241 unwind label %funclet_bb229

bb241:                                            ; preds = %bb59
  br label %bb52

bb41:                                             ; preds = %bb39
  store i32 %53, ptr %result5, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_75, ptr align 4 %base)
          to label %bb42 unwind label %funclet_bb230

bb42:                                             ; preds = %bb41
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbeedcdfe640846a6E(ptr sret([16 x i8]) align 8 %_77, ptr align 4 %exp)
          to label %bb43 unwind label %funclet_bb230

bb43:                                             ; preds = %bb42
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_79, ptr align 4 %result5)
          to label %bb44 unwind label %funclet_bb230

bb44:                                             ; preds = %bb43
  %260 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_74, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %260, ptr align 8 %_75, i64 16, i1 false)
  %261 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_74, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %261, ptr align 8 %_77, i64 16, i1 false)
  %262 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_74, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %262, ptr align 8 %_79, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h71e372ae6819746eE(ptr sret([48 x i8]) align 8 %_71, ptr align 8 @alloc_86a2165cff3a77f599eab9643e2208d0, ptr align 8 %_74)
          to label %bb45 unwind label %funclet_bb230

bb45:                                             ; preds = %bb44
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_71)
          to label %bb240 unwind label %funclet_bb230

bb240:                                            ; preds = %bb45
  br label %bb37

bb11:                                             ; No predecessors!
  unreachable
}

; _17_recursive_functions::factorial
; Function Attrs: uwtable
define internal i64 @_ZN23_17_recursive_functions9factorial17h75d35075a2f2f839E(i64 %n) unnamed_addr #0 {
start:
  %_0 = alloca [8 x i8], align 8
  %_2 = icmp ule i64 %n, 1
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_5.0 = sub i64 %n, 1
  %_5.1 = icmp ult i64 %n, 1
  br i1 %_5.1, label %panic, label %bb3

bb1:                                              ; preds = %start
  store i64 1, ptr %_0, align 8
  br label %bb6

bb3:                                              ; preds = %bb2
; call _17_recursive_functions::factorial
  %_3 = call i64 @_ZN23_17_recursive_functions9factorial17h75d35075a2f2f839E(i64 %_5.0)
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %n, i64 %_3)
  %_6.0 = extractvalue { i64, i1 } %0, 0
  %_6.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_6.1, label %panic1, label %bb5

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_0113dd7f9c1cf13543958923caa8e4fb) #23
  unreachable

bb5:                                              ; preds = %bb3
  store i64 %_6.0, ptr %_0, align 8
  br label %bb6

panic1:                                           ; preds = %bb3
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_718fcb6f22c2861106a7114952a201ad) #23
  unreachable

bb6:                                              ; preds = %bb1, %bb5
  %1 = load i64, ptr %_0, align 8
  ret i64 %1
}

; _17_recursive_functions::fibonacci
; Function Attrs: uwtable
define internal i64 @_ZN23_17_recursive_functions9fibonacci17hb8979044c6a84d19E(i32 %n) unnamed_addr #0 {
start:
  %_0 = alloca [8 x i8], align 8
  switch i32 %n, label %bb1 [
    i32 0, label %bb3
    i32 1, label %bb2
  ]

bb1:                                              ; preds = %start
  %_4.0 = sub i32 %n, 1
  %_4.1 = icmp ult i32 %n, 1
  br i1 %_4.1, label %panic, label %bb4

bb3:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb9

bb2:                                              ; preds = %start
  store i64 1, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb2, %bb3
  %0 = load i64, ptr %_0, align 8
  ret i64 %0

bb4:                                              ; preds = %bb1
; call _17_recursive_functions::fibonacci
  %_2 = call i64 @_ZN23_17_recursive_functions9fibonacci17hb8979044c6a84d19E(i32 %_4.0)
  %_7.0 = sub i32 %n, 2
  %_7.1 = icmp ult i32 %n, 2
  br i1 %_7.1, label %panic1, label %bb6

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_6e5e50876a382ffadf3a4d043f83bdf2) #23
  unreachable

bb6:                                              ; preds = %bb4
; call _17_recursive_functions::fibonacci
  %_5 = call i64 @_ZN23_17_recursive_functions9fibonacci17hb8979044c6a84d19E(i32 %_7.0)
  %1 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %_2, i64 %_5)
  %_8.0 = extractvalue { i64, i1 } %1, 0
  %_8.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_8.1, label %panic2, label %bb8

panic1:                                           ; preds = %bb4
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_640aeb1c6689c71be24aeb2af148a12d) #23
  unreachable

bb8:                                              ; preds = %bb6
  store i64 %_8.0, ptr %_0, align 8
  br label %bb9

panic2:                                           ; preds = %bb6
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_e6b4867eafdfa198491e240bc4f47096) #23
  unreachable
}

; _17_recursive_functions::power
; Function Attrs: uwtable
define internal i32 @_ZN23_17_recursive_functions5power17h557517e276411f78E(i32 %base, i32 %exp) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %0 = icmp eq i32 %exp, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i32 1, ptr %_0, align 4
  br label %bb6

bb2:                                              ; preds = %start
  %_5.0 = sub i32 %exp, 1
  %_5.1 = icmp ult i32 %exp, 1
  br i1 %_5.1, label %panic, label %bb3

bb6:                                              ; preds = %bb5, %bb1
  %1 = load i32, ptr %_0, align 4
  ret i32 %1

bb3:                                              ; preds = %bb2
; call _17_recursive_functions::power
  %_3 = call i32 @_ZN23_17_recursive_functions5power17h557517e276411f78E(i32 %base, i32 %_5.0)
  %2 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %base, i32 %_3)
  %_6.0 = extractvalue { i32, i1 } %2, 0
  %_6.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_6.1, label %panic1, label %bb5

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_c1f0796db8def95df74e4560a5c8b19f) #23
  unreachable

bb5:                                              ; preds = %bb3
  store i32 %_6.0, ptr %_0, align 4
  br label %bb6

panic1:                                           ; preds = %bb3
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_1c305380add1f2fe76f46ff2ee475d8c) #23
  unreachable
}

; _17_recursive_functions::sum_of_digits
; Function Attrs: uwtable
define internal i32 @_ZN23_17_recursive_functions13sum_of_digits17hc48efa6256f88e98E(i32 %n) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %_2 = icmp ult i32 %n, 10
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_3 = urem i32 %n, 10
  %_6 = udiv i32 %n, 10
; call _17_recursive_functions::sum_of_digits
  %_5 = call i32 @_ZN23_17_recursive_functions13sum_of_digits17hc48efa6256f88e98E(i32 %_6)
  %0 = call { i32, i1 } @llvm.uadd.with.overflow.i32(i32 %_3, i32 %_5)
  %_8.0 = extractvalue { i32, i1 } %0, 0
  %_8.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_8.1, label %panic, label %bb6

bb1:                                              ; preds = %start
  store i32 %n, ptr %_0, align 4
  br label %bb7

bb6:                                              ; preds = %bb2
  store i32 %_8.0, ptr %_0, align 4
  br label %bb7

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_d519354ffa0e410ff74d7413ba741452) #23
  unreachable

bb7:                                              ; preds = %bb1, %bb6
  %1 = load i32, ptr %_0, align 4
  ret i32 %1
}

; _17_recursive_functions::countdown
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions9countdown17h14f0c8fadc911e8fE(i32 %0) unnamed_addr #0 {
start:
  %_14 = alloca [48 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_4 = alloca [48 x i8], align 8
  %n = alloca [4 x i8], align 4
  store i32 %0, ptr %n, align 4
  %1 = load i32, ptr %n, align 4
  %_2 = icmp sgt i32 %1, 0
  br i1 %_2, label %bb1, label %bb6

bb6:                                              ; preds = %start
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_6a3925fbd3809c89959ae12a2b6708af)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
  br label %bb8

bb1:                                              ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_8, ptr align 4 %n)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_7, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_8, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h6b8f50bbcd2996bfE(ptr sret([48 x i8]) align 8 %_4, ptr align 8 @alloc_297da3bc62bdd64fe8d471f72592f9a5, ptr align 8 %_7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_4)
  %3 = load i32, ptr %n, align 4
  %4 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %3, i32 1)
  %_12.0 = extractvalue { i32, i1 } %4, 0
  %_12.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_12.1, label %panic, label %bb5

bb8:                                              ; preds = %bb5, %bb6
  ret void

bb5:                                              ; preds = %bb1
; call _17_recursive_functions::countdown
  call void @_ZN23_17_recursive_functions9countdown17h14f0c8fadc911e8fE(i32 %_12.0)
  br label %bb8

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_564530bae0fe4666106227118442ad16) #23
  unreachable
}

; _17_recursive_functions::count_up
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions8count_up17hba5cd4502e815708E(i32 %0, i32 %target) unnamed_addr #0 {
start:
  %_15 = alloca [48 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %current = alloca [4 x i8], align 4
  store i32 %0, ptr %current, align 4
  %1 = load i32, ptr %current, align 4
  %_3 = icmp sle i32 %1, %target
  br i1 %_3, label %bb1, label %bb6

bb6:                                              ; preds = %start
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17hd7f076fae6347c76E(ptr sret([48 x i8]) align 8 %_15, ptr align 8 @alloc_dfbbf380f23c0db6cd43dc9a443ac1d8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_15)
  br label %bb8

bb1:                                              ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_9, ptr align 4 %current)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_9, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h6b8f50bbcd2996bfE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_e2de23853e963fac850c6cf76dea7de7, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  %3 = load i32, ptr %current, align 4
  %4 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %3, i32 1)
  %_13.0 = extractvalue { i32, i1 } %4, 0
  %_13.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_13.1, label %panic, label %bb5

bb8:                                              ; preds = %bb5, %bb6
  ret void

bb5:                                              ; preds = %bb1
; call _17_recursive_functions::count_up
  call void @_ZN23_17_recursive_functions8count_up17hba5cd4502e815708E(i32 %_13.0, i32 %target)
  br label %bb8

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_dca679042b1f4e5dbe81467678e96ccf) #23
  unreachable
}

; _17_recursive_functions::array_sum
; Function Attrs: uwtable
define internal i32 @_ZN23_17_recursive_functions9array_sum17ha2e841e9912cddcbE(ptr align 4 %arr.0, i64 %arr.1, i64 %index) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %_3 = icmp uge i64 %index, %arr.1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_7 = icmp ult i64 %index, %arr.1
  br i1 %_7, label %bb3, label %panic

bb1:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb7

bb3:                                              ; preds = %bb2
  %0 = getelementptr inbounds nuw i32, ptr %arr.0, i64 %index
  %_5 = load i32, ptr %0, align 4
  %1 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %index, i64 1)
  %_10.0 = extractvalue { i64, i1 } %1, 0
  %_10.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_10.1, label %panic1, label %bb4

panic:                                            ; preds = %bb2
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %index, i64 %arr.1, ptr align 8 @alloc_3e3a5f47fd32fd61f670c15bb51935de) #23
  unreachable

bb4:                                              ; preds = %bb3
; call _17_recursive_functions::array_sum
  %_8 = call i32 @_ZN23_17_recursive_functions9array_sum17ha2e841e9912cddcbE(ptr align 4 %arr.0, i64 %arr.1, i64 %_10.0)
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_5, i32 %_8)
  %_11.0 = extractvalue { i32, i1 } %2, 0
  %_11.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_11.1, label %panic2, label %bb6

panic1:                                           ; preds = %bb3
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f07c07076c6a606ea456881b010e99e5) #23
  unreachable

bb6:                                              ; preds = %bb4
  store i32 %_11.0, ptr %_0, align 4
  br label %bb7

panic2:                                           ; preds = %bb4
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_3e3a5f47fd32fd61f670c15bb51935de) #23
  unreachable

bb7:                                              ; preds = %bb1, %bb6
  %3 = load i32, ptr %_0, align 4
  ret i32 %3
}

; _17_recursive_functions::gcd
; Function Attrs: uwtable
define internal i32 @_ZN23_17_recursive_functions3gcd17h6cf7ef094f441d45E(i32 %a, i32 %b) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %0 = icmp eq i32 %b, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i32 %a, ptr %_0, align 4
  br label %bb4

bb2:                                              ; preds = %start
  %_4 = icmp eq i32 %b, 0
  br i1 %_4, label %panic, label %bb3

bb4:                                              ; preds = %bb3, %bb1
  %1 = load i32, ptr %_0, align 4
  ret i32 %1

bb3:                                              ; preds = %bb2
  %_3 = urem i32 %a, %b
; call _17_recursive_functions::gcd
  %2 = call i32 @_ZN23_17_recursive_functions3gcd17h6cf7ef094f441d45E(i32 %b, i32 %_3)
  store i32 %2, ptr %_0, align 4
  br label %bb4

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 @alloc_7e25478eab46d53058b79dd0afa7b915) #23
  unreachable
}

; _17_recursive_functions::binary_search
; Function Attrs: uwtable
define internal { i64, i64 } @_ZN23_17_recursive_functions13binary_search17h4da70433afe785dfE(ptr align 4 %arr.0, i64 %arr.1, i32 %target, i64 %left, i64 %right) unnamed_addr #0 {
start:
  %_0 = alloca [16 x i8], align 8
  %_5 = icmp ugt i64 %left, %right
  br i1 %_5, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_9.0 = sub i64 %right, %left
  %_9.1 = icmp ult i64 %right, %left
  br i1 %_9.1, label %panic, label %bb3

bb1:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb16

bb3:                                              ; preds = %bb2
  %_7 = udiv i64 %_9.0, 2
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %left, i64 %_7)
  %_11.0 = extractvalue { i64, i1 } %0, 0
  %_11.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_11.1, label %panic1, label %bb5

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_0c2b227309ce0ebba6b5b490188a067b) #23
  unreachable

bb5:                                              ; preds = %bb3
  %_15 = icmp ult i64 %_11.0, %arr.1
  br i1 %_15, label %bb6, label %panic2

panic1:                                           ; preds = %bb3
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_06b6d1bae5667081f1b66caa9e04d600) #23
  unreachable

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds nuw i32, ptr %arr.0, i64 %_11.0
  %_13 = load i32, ptr %1, align 4
  %_12 = icmp eq i32 %_13, %target
  br i1 %_12, label %bb7, label %bb8

panic2:                                           ; preds = %bb5
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_11.0, i64 %arr.1, ptr align 8 @alloc_69e409a5ad668a7a87b7c737e2b2dee6) #23
  unreachable

bb8:                                              ; preds = %bb6
  %_19 = icmp ult i64 %_11.0, %arr.1
  br i1 %_19, label %bb9, label %panic3

bb7:                                              ; preds = %bb6
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_11.0, ptr %2, align 8
  store i64 1, ptr %_0, align 8
  br label %bb16

bb9:                                              ; preds = %bb8
  %3 = getelementptr inbounds nuw i32, ptr %arr.0, i64 %_11.0
  %_17 = load i32, ptr %3, align 4
  %_16 = icmp sgt i32 %_17, %target
  br i1 %_16, label %bb10, label %bb14

panic3:                                           ; preds = %bb8
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %_11.0, i64 %arr.1, ptr align 8 @alloc_1fa34c670e55d0dbb5ad0a56cbc99734) #23
  unreachable

bb14:                                             ; preds = %bb9
  %4 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %_11.0, i64 1)
  %_23.0 = extractvalue { i64, i1 } %4, 0
  %_23.1 = extractvalue { i64, i1 } %4, 1
  br i1 %_23.1, label %panic4, label %bb15

bb10:                                             ; preds = %bb9
  %5 = icmp eq i64 %_11.0, 0
  br i1 %5, label %bb11, label %bb12

bb15:                                             ; preds = %bb14
; call _17_recursive_functions::binary_search
  %6 = call { i64, i64 } @_ZN23_17_recursive_functions13binary_search17h4da70433afe785dfE(ptr align 4 %arr.0, i64 %arr.1, i32 %target, i64 %_23.0, i64 %right)
  %7 = extractvalue { i64, i64 } %6, 0
  %8 = extractvalue { i64, i64 } %6, 1
  store i64 %7, ptr %_0, align 8
  %9 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb16

panic4:                                           ; preds = %bb14
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_b3830c14bc86a1700c2ddadf9d0bb34d) #23
  unreachable

bb16:                                             ; preds = %bb1, %bb7, %bb13, %bb11, %bb15
  %10 = load i64, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  %12 = load i64, ptr %11, align 8
  %13 = insertvalue { i64, i64 } poison, i64 %10, 0
  %14 = insertvalue { i64, i64 } %13, i64 %12, 1
  ret { i64, i64 } %14

bb11:                                             ; preds = %bb10
  store i64 0, ptr %_0, align 8
  br label %bb16

bb12:                                             ; preds = %bb10
  %_21.0 = sub i64 %_11.0, 1
  %_21.1 = icmp ult i64 %_11.0, 1
  br i1 %_21.1, label %panic5, label %bb13

bb13:                                             ; preds = %bb12
; call _17_recursive_functions::binary_search
  %15 = call { i64, i64 } @_ZN23_17_recursive_functions13binary_search17h4da70433afe785dfE(ptr align 4 %arr.0, i64 %arr.1, i32 %target, i64 %left, i64 %_21.0)
  %16 = extractvalue { i64, i64 } %15, 0
  %17 = extractvalue { i64, i64 } %15, 1
  store i64 %16, ptr %_0, align 8
  %18 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %17, ptr %18, align 8
  br label %bb16

panic5:                                           ; preds = %bb12
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_506f4e0036e3d57433c2073cbbc89268) #23
  unreachable
}

; _17_recursive_functions::reverse_string
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions14reverse_string17hb01d0e55cd09b5abE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_32 = alloca [24 x i8], align 8
  %_30 = alloca [16 x i8], align 8
  %_28 = alloca [16 x i8], align 8
  %_27 = alloca [32 x i8], align 8
  %_24 = alloca [48 x i8], align 8
  %res = alloca [24 x i8], align 8
  %_22 = alloca [24 x i8], align 8
  %rest = alloca [24 x i8], align 8
  %last_char = alloca [4 x i8], align 4
  %chars = alloca [24 x i8], align 8
; call core::str::<impl str>::len
  %_3 = call i64 @"_ZN4core3str21_$LT$impl$u20$str$GT$3len17h6591b20b988cccc7E"(ptr align 1 %s.0, i64 %s.1)
  %_2 = icmp ule i64 %_3, 1
  br i1 %_2, label %bb2, label %bb3

bb3:                                              ; preds = %start
; call core::str::<impl str>::chars
  %0 = call { ptr, ptr } @"_ZN4core3str21_$LT$impl$u20$str$GT$5chars17h2253b3769080311cE"(ptr align 1 %s.0, i64 %s.1)
  %_5.0 = extractvalue { ptr, ptr } %0, 0
  %_5.1 = extractvalue { ptr, ptr } %0, 1
; call core::iter::traits::iterator::Iterator::collect
  call void @_ZN4core4iter6traits8iterator8Iterator7collect17hbf5b83a71a414ff3E(ptr sret([24 x i8]) align 8 %chars, ptr %_5.0, ptr %_5.1)
; invoke alloc::vec::Vec<T,A>::len
  %_10 = invoke i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17hde1483eea24f77b2E"(ptr align 8 %chars)
          to label %bb6 unwind label %funclet_bb27

bb2:                                              ; preds = %start
; call <T as alloc::string::ToString>::to_string
  call void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17h4da18e2ad1423aafE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1)
  br label %bb23

bb27:                                             ; preds = %funclet_bb27
; call core::ptr::drop_in_place<alloc::vec::Vec<char>>
  call void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %chars) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb27:                                     ; preds = %bb26, %bb21, %bb12, %bb11, %bb10, %panic1, %bb8, %bb7, %panic, %bb3
  %cleanuppad = cleanuppad within none []
  br label %bb27

bb6:                                              ; preds = %bb3
  %_12.0 = sub i64 %_10, 1
  %_12.1 = icmp ult i64 %_10, 1
  br i1 %_12.1, label %panic, label %bb7

bb7:                                              ; preds = %bb6
; invoke <alloc::vec::Vec<T,A> as core::ops::index::Index<I>>::index
  %_7 = invoke align 4 ptr @"_ZN81_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..index..Index$LT$I$GT$$GT$5index17h35e058321cab3535E"(ptr align 8 %chars, i64 %_12.0, ptr align 8 @alloc_5b3955e00fee21a400912ec48a8c010d)
          to label %bb8 unwind label %funclet_bb27

panic:                                            ; preds = %bb6
; invoke core::panicking::panic_const::panic_const_sub_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_951f62198ca129b96d53ae30a55fa068) #23
          to label %unreachable unwind label %funclet_bb27

unreachable:                                      ; preds = %panic1, %panic
  unreachable

bb8:                                              ; preds = %bb7
  %1 = load i32, ptr %_7, align 4
  store i32 %1, ptr %last_char, align 4
; invoke alloc::vec::Vec<T,A>::len
  %_19 = invoke i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17hde1483eea24f77b2E"(ptr align 8 %chars)
          to label %bb9 unwind label %funclet_bb27

bb9:                                              ; preds = %bb8
  %_21.0 = sub i64 %_19, 1
  %_21.1 = icmp ult i64 %_19, 1
  br i1 %_21.1, label %panic1, label %bb10

bb10:                                             ; preds = %bb9
; invoke <alloc::vec::Vec<T,A> as core::ops::index::Index<I>>::index
  %2 = invoke { ptr, i64 } @"_ZN81_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..index..Index$LT$I$GT$$GT$5index17h29eedab289ccc192E"(ptr align 8 %chars, i64 %_21.0, ptr align 8 @alloc_01a5805d15aca57b623d353a5f5bd8b4)
          to label %bb11 unwind label %funclet_bb27

panic1:                                           ; preds = %bb9
; invoke core::panicking::panic_const::panic_const_sub_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_bf8d193f36445f3f20fd24696f56c0a3) #23
          to label %unreachable unwind label %funclet_bb27

bb11:                                             ; preds = %bb10
  %_15.0 = extractvalue { ptr, i64 } %2, 0
  %_15.1 = extractvalue { ptr, i64 } %2, 1
; invoke core::slice::<impl [T]>::iter
  %3 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h6bd93caef2eba87bE"(ptr align 4 %_15.0, i64 %_15.1)
          to label %bb12 unwind label %funclet_bb27

bb12:                                             ; preds = %bb11
  %_14.0 = extractvalue { ptr, ptr } %3, 0
  %_14.1 = extractvalue { ptr, ptr } %3, 1
; invoke core::iter::traits::iterator::Iterator::collect
  invoke void @_ZN4core4iter6traits8iterator8Iterator7collect17h64743d9f49c99a9eE(ptr sret([24 x i8]) align 8 %rest, ptr %_14.0, ptr %_14.1)
          to label %bb13 unwind label %funclet_bb27

bb13:                                             ; preds = %bb12
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_28, ptr align 4 %last_char)
          to label %bb14 unwind label %funclet_bb26

bb26:                                             ; preds = %funclet_bb26
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %rest) #21 [ "funclet"(token %cleanuppad2) ]
  cleanupret from %cleanuppad2 unwind label %funclet_bb27

funclet_bb26:                                     ; preds = %bb25, %bb24, %bb15, %bb14, %bb13
  %cleanuppad2 = cleanuppad within none []
  br label %bb26

bb14:                                             ; preds = %bb13
; invoke <alloc::string::String as core::ops::deref::Deref>::deref
  %4 = invoke { ptr, i64 } @"_ZN65_$LT$alloc..string..String$u20$as$u20$core..ops..deref..Deref$GT$5deref17he6a45051d77d775eE"(ptr align 8 %rest)
          to label %bb15 unwind label %funclet_bb26

bb15:                                             ; preds = %bb14
  %_33.0 = extractvalue { ptr, i64 } %4, 0
  %_33.1 = extractvalue { ptr, i64 } %4, 1
; invoke _17_recursive_functions::reverse_string
  invoke void @_ZN23_17_recursive_functions14reverse_string17hb01d0e55cd09b5abE(ptr sret([24 x i8]) align 8 %_32, ptr align 1 %_33.0, i64 %_33.1)
          to label %bb16 unwind label %funclet_bb26

bb16:                                             ; preds = %bb15
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbba4d01832b4100aE(ptr sret([16 x i8]) align 8 %_30, ptr align 8 %_32)
          to label %bb17 unwind label %funclet_bb25

bb25:                                             ; preds = %funclet_bb25
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %_32) #21 [ "funclet"(token %cleanuppad3) ]
  cleanupret from %cleanuppad3 unwind label %funclet_bb26

funclet_bb25:                                     ; preds = %bb18, %bb17, %bb16
  %cleanuppad3 = cleanuppad within none []
  br label %bb25

bb17:                                             ; preds = %bb16
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_27, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_28, i64 16, i1 false)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_27, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_30, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h9ecf983bf86ff6fbE(ptr sret([48 x i8]) align 8 %_24, ptr align 8 @alloc_bd1be8c7a73e5392e9c02e2643330a1c, ptr align 8 %_27)
          to label %bb18 unwind label %funclet_bb25

bb18:                                             ; preds = %bb17
; invoke alloc::fmt::format
  invoke void @_ZN5alloc3fmt6format17h9bd992477706aa0cE(ptr sret([24 x i8]) align 8 %res, ptr align 8 %_24)
          to label %bb19 unwind label %funclet_bb25

bb19:                                             ; preds = %bb18
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %_32)
          to label %bb20 unwind label %funclet_bb24

bb24:                                             ; preds = %funclet_bb24
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %res) #21 [ "funclet"(token %cleanuppad4) ]
  cleanupret from %cleanuppad4 unwind label %funclet_bb26

funclet_bb24:                                     ; preds = %bb19
  %cleanuppad4 = cleanuppad within none []
  br label %bb24

bb20:                                             ; preds = %bb19
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_22, ptr align 8 %res, i64 24, i1 false)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_22, i64 24, i1 false)
  br label %bb21

bb21:                                             ; preds = %bb20
; invoke core::ptr::drop_in_place<alloc::string::String>
  invoke void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %rest)
          to label %bb22 unwind label %funclet_bb27

bb22:                                             ; preds = %bb21
; call core::ptr::drop_in_place<alloc::vec::Vec<char>>
  call void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %chars)
  br label %bb23

bb23:                                             ; preds = %bb2, %bb22
  ret void
}

; _17_recursive_functions::is_palindrome
; Function Attrs: uwtable
define internal zeroext i1 @_ZN23_17_recursive_functions13is_palindrome17h34ae266b97157ee9E(ptr align 1 %s.0, i64 %s.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %chars = alloca [24 x i8], align 8
; call core::str::<impl str>::chars
  %0 = call { ptr, ptr } @"_ZN4core3str21_$LT$impl$u20$str$GT$5chars17h2253b3769080311cE"(ptr align 1 %s.0, i64 %s.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
; call core::iter::traits::iterator::Iterator::collect
  call void @_ZN4core4iter6traits8iterator8Iterator7collect17hbf5b83a71a414ff3E(ptr sret([24 x i8]) align 8 %chars, ptr %_3.0, ptr %_3.1)
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %1 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h5e73e56dd156b027E"(ptr align 8 %chars)
          to label %bb3 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
; call core::ptr::drop_in_place<alloc::vec::Vec<char>>
  call void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %chars) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb8:                                      ; preds = %bb5, %panic, %bb3, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb3:                                              ; preds = %start
  %_4.0 = extractvalue { ptr, i64 } %1, 0
  %_4.1 = extractvalue { ptr, i64 } %1, 1
; invoke alloc::vec::Vec<T,A>::len
  %_7 = invoke i64 @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$3len17hde1483eea24f77b2E"(ptr align 8 %chars)
          to label %bb4 unwind label %funclet_bb8

bb4:                                              ; preds = %bb3
  %_9.0 = sub i64 %_7, 1
  %_9.1 = icmp ult i64 %_7, 1
  br i1 %_9.1, label %panic, label %bb5

bb5:                                              ; preds = %bb4
; invoke _17_recursive_functions::is_palindrome_helper
  %_0 = invoke zeroext i1 @_ZN23_17_recursive_functions20is_palindrome_helper17h7a9cb8193bae2a26E(ptr align 4 %_4.0, i64 %_4.1, i64 0, i64 %_9.0)
          to label %bb6 unwind label %funclet_bb8

panic:                                            ; preds = %bb4
; invoke core::panicking::panic_const::panic_const_sub_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_2517f977ba6594f07dd4cc8a239dd270) #23
          to label %unreachable unwind label %funclet_bb8

unreachable:                                      ; preds = %panic
  unreachable

bb6:                                              ; preds = %bb5
; call core::ptr::drop_in_place<alloc::vec::Vec<char>>
  call void @"_ZN4core3ptr48drop_in_place$LT$alloc..vec..Vec$LT$char$GT$$GT$17ha772b36f4db7c3c5E"(ptr align 8 %chars)
  ret i1 %_0
}

; _17_recursive_functions::is_palindrome_helper
; Function Attrs: uwtable
define internal zeroext i1 @_ZN23_17_recursive_functions20is_palindrome_helper17h7a9cb8193bae2a26E(ptr align 4 %chars.0, i64 %chars.1, i64 %left, i64 %right) unnamed_addr #0 {
start:
  %_0 = alloca [1 x i8], align 1
  %_4 = icmp uge i64 %left, %right
  br i1 %_4, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %_8 = icmp ult i64 %left, %chars.1
  br i1 %_8, label %bb3, label %panic

bb1:                                              ; preds = %start
  store i8 1, ptr %_0, align 1
  br label %bb9

bb3:                                              ; preds = %bb2
  %0 = getelementptr inbounds nuw i32, ptr %chars.0, i64 %left
  %_6 = load i32, ptr %0, align 4
  %_11 = icmp ult i64 %right, %chars.1
  br i1 %_11, label %bb4, label %panic1

panic:                                            ; preds = %bb2
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %left, i64 %chars.1, ptr align 8 @alloc_b5387875f27eb0cdddcc435e20e76119) #23
  unreachable

bb4:                                              ; preds = %bb3
  %1 = getelementptr inbounds nuw i32, ptr %chars.0, i64 %right
  %_9 = load i32, ptr %1, align 4
  %_5 = icmp ne i32 %_6, %_9
  br i1 %_5, label %bb5, label %bb6

panic1:                                           ; preds = %bb3
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %right, i64 %chars.1, ptr align 8 @alloc_a4824f785cce21a0d0e93c2e80ccb6c3) #23
  unreachable

bb6:                                              ; preds = %bb4
  %2 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %left, i64 1)
  %_13.0 = extractvalue { i64, i1 } %2, 0
  %_13.1 = extractvalue { i64, i1 } %2, 1
  br i1 %_13.1, label %panic2, label %bb7

bb5:                                              ; preds = %bb4
  store i8 0, ptr %_0, align 1
  br label %bb9

bb7:                                              ; preds = %bb6
  %_15.0 = sub i64 %right, 1
  %_15.1 = icmp ult i64 %right, 1
  br i1 %_15.1, label %panic3, label %bb8

panic2:                                           ; preds = %bb6
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2a3f396d9430f7062b8aca74a305ac66) #23
  unreachable

bb8:                                              ; preds = %bb7
; call _17_recursive_functions::is_palindrome_helper
  %3 = call zeroext i1 @_ZN23_17_recursive_functions20is_palindrome_helper17h7a9cb8193bae2a26E(ptr align 4 %chars.0, i64 %chars.1, i64 %_13.0, i64 %_15.0)
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %_0, align 1
  br label %bb9

panic3:                                           ; preds = %bb7
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_9fa2689b590f9447e4b9f5a35f1e1a8b) #23
  unreachable

bb9:                                              ; preds = %bb1, %bb5, %bb8
  %5 = load i8, ptr %_0, align 1
  %6 = trunc nuw i8 %5 to i1
  ret i1 %6
}

; _17_recursive_functions::print_tree_levels
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions17print_tree_levels17hbd803eb1c62b2b87E(i32 %0, i32 %max_level) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_16 = alloca [16 x i8], align 8
  %_14 = alloca [16 x i8], align 8
  %_13 = alloca [32 x i8], align 8
  %_10 = alloca [48 x i8], align 8
  %indent = alloca [24 x i8], align 8
  %level = alloca [4 x i8], align 4
  store i32 %0, ptr %level, align 4
  %1 = load i32, ptr %level, align 4
  %_3 = icmp sle i32 %1, %max_level
  br i1 %_3, label %bb1, label %bb10

bb10:                                             ; preds = %bb9, %start
  ret void

bb1:                                              ; preds = %start
  %2 = load i32, ptr %level, align 4
  %3 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %2, i32 1)
  %_8.0 = extractvalue { i32, i1 } %3, 0
  %_8.1 = extractvalue { i32, i1 } %3, 1
  br i1 %_8.1, label %panic, label %bb2

bb2:                                              ; preds = %bb1
  %_6 = sext i32 %_8.0 to i64
; call alloc::str::<impl str>::repeat
  call void @"_ZN5alloc3str21_$LT$impl$u20$str$GT$6repeat17ha5fae8653069c8e4E"(ptr sret([24 x i8]) align 8 %indent, ptr align 1 @alloc_34310cabf6554cc6123dbb21242f43b8, i64 2, i64 %_6)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hbba4d01832b4100aE(ptr sret([16 x i8]) align 8 %_14, ptr align 8 %indent)
          to label %bb4 unwind label %funclet_bb11

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_da6dc6342c20f02b1ec275ba36e6456d) #23
  unreachable

bb11:                                             ; preds = %funclet_bb11
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %indent) #21 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb8, %panic1, %bb6, %bb5, %bb4, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb4:                                              ; preds = %bb2
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_16, ptr align 4 %level)
          to label %bb5 unwind label %funclet_bb11

bb5:                                              ; preds = %bb4
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_13, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_14, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_13, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_16, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_10, ptr align 8 @alloc_eb68e4306e26e7c43562c187f0b70d5a, ptr align 8 %_13)
          to label %bb6 unwind label %funclet_bb11

bb6:                                              ; preds = %bb5
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_10)
          to label %bb7 unwind label %funclet_bb11

bb7:                                              ; preds = %bb6
  %6 = load i32, ptr %level, align 4
  %7 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %6, i32 1)
  %_20.0 = extractvalue { i32, i1 } %7, 0
  %_20.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_20.1, label %panic1, label %bb8

bb8:                                              ; preds = %bb7
; invoke _17_recursive_functions::print_tree_levels
  invoke void @_ZN23_17_recursive_functions17print_tree_levels17hbd803eb1c62b2b87E(i32 %_20.0, i32 %max_level)
          to label %bb9 unwind label %funclet_bb11

panic1:                                           ; preds = %bb7
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_81da280dd8e0ed907ba6f3007be14736) #23
          to label %unreachable unwind label %funclet_bb11

unreachable:                                      ; preds = %panic1
  unreachable

bb9:                                              ; preds = %bb8
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h779fd3846bd18c33E"(ptr align 8 %indent)
  br label %bb10
}

; _17_recursive_functions::hanoi
; Function Attrs: uwtable
define internal void @_ZN23_17_recursive_functions5hanoi17heb75ee7238955714E(i32 %0, i32 %1, i32 %2, i32 %aux) unnamed_addr #0 {
start:
  %_26 = alloca [16 x i8], align 8
  %_24 = alloca [16 x i8], align 8
  %_22 = alloca [16 x i8], align 8
  %_21 = alloca [48 x i8], align 8
  %_18 = alloca [48 x i8], align 8
  %_12 = alloca [16 x i8], align 8
  %_10 = alloca [16 x i8], align 8
  %_9 = alloca [32 x i8], align 8
  %_6 = alloca [48 x i8], align 8
  %to = alloca [4 x i8], align 4
  %from = alloca [4 x i8], align 4
  %n = alloca [4 x i8], align 4
  store i32 %0, ptr %n, align 4
  store i32 %1, ptr %from, align 4
  store i32 %2, ptr %to, align 4
  %3 = load i32, ptr %n, align 4
  %4 = icmp eq i32 %3, 1
  br i1 %4, label %bb1, label %bb5

bb1:                                              ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_10, ptr align 4 %from)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_12, ptr align 4 %to)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_9, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_10, i64 16, i1 false)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_9, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_12, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hd62a4a202c04693fE(ptr sret([48 x i8]) align 8 %_6, ptr align 8 @alloc_d7cc52af69a872c3e20f995d75075d4d, ptr align 8 %_9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_6)
  br label %bb14

bb5:                                              ; preds = %start
  %7 = load i32, ptr %n, align 4
  %8 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %7, i32 1)
  %_16.0 = extractvalue { i32, i1 } %8, 0
  %_16.1 = extractvalue { i32, i1 } %8, 1
  br i1 %_16.1, label %panic, label %bb6

bb14:                                             ; preds = %bb13, %bb1
  ret void

bb6:                                              ; preds = %bb5
  %9 = load i32, ptr %from, align 4
  %10 = load i32, ptr %to, align 4
; call _17_recursive_functions::hanoi
  call void @_ZN23_17_recursive_functions5hanoi17heb75ee7238955714E(i32 %_16.0, i32 %9, i32 %aux, i32 %10)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h47923c14966af81cE(ptr sret([16 x i8]) align 8 %_22, ptr align 4 %n)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_24, ptr align 4 %from)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h522c048fcb58e091E(ptr sret([16 x i8]) align 8 %_26, ptr align 4 %to)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_21, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_22, i64 16, i1 false)
  %12 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_21, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %12, ptr align 8 %_24, i64 16, i1 false)
  %13 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_21, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %13, ptr align 8 %_26, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h71e372ae6819746eE(ptr sret([48 x i8]) align 8 %_18, ptr align 8 @alloc_4a3195e5f4bcb3f99768b1b295930f4a, ptr align 8 %_21)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_18)
  %14 = load i32, ptr %n, align 4
  %15 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %14, i32 1)
  %_30.0 = extractvalue { i32, i1 } %15, 0
  %_30.1 = extractvalue { i32, i1 } %15, 1
  br i1 %_30.1, label %panic1, label %bb13

panic:                                            ; preds = %bb5
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_c153f61d407b6c73ad2f5f60ec14c0a6) #23
  unreachable

bb13:                                             ; preds = %bb6
  %16 = load i32, ptr %to, align 4
  %17 = load i32, ptr %from, align 4
; call _17_recursive_functions::hanoi
  call void @_ZN23_17_recursive_functions5hanoi17heb75ee7238955714E(i32 %_30.0, i32 %aux, i32 %16, i32 %17)
  br label %bb14

panic1:                                           ; preds = %bb6
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_539284d65c9eed23448004dec9ca96cc) #23
  unreachable
}

; _17_recursive_functions::ackermann
; Function Attrs: uwtable
define internal i32 @_ZN23_17_recursive_functions9ackermann17h102b3040c3f90c0eE(i32 %m, i32 %n) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %0 = icmp eq i32 %m, 0
  br i1 %0, label %bb4, label %bb1

bb4:                                              ; preds = %start
  %1 = call { i32, i1 } @llvm.uadd.with.overflow.i32(i32 %n, i32 1)
  %_5.0 = extractvalue { i32, i1 } %1, 0
  %_5.1 = extractvalue { i32, i1 } %1, 1
  br i1 %_5.1, label %panic, label %bb5

bb1:                                              ; preds = %start
  %2 = icmp eq i32 %n, 0
  br i1 %2, label %bb3, label %bb2

bb5:                                              ; preds = %bb4
  store i32 %_5.0, ptr %_0, align 4
  br label %bb10

panic:                                            ; preds = %bb4
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_ffb8b440afd214261f508cfb9fa31fc9) #23
  unreachable

bb10:                                             ; preds = %bb8, %bb6, %bb5
  %3 = load i32, ptr %_0, align 4
  ret i32 %3

bb3:                                              ; preds = %bb1
  %_8.0 = sub i32 %m, 1
  %_8.1 = icmp ult i32 %m, 1
  br i1 %_8.1, label %panic1, label %bb6

bb2:                                              ; preds = %bb1
  %_12.0 = sub i32 %m, 1
  %_12.1 = icmp ult i32 %m, 1
  br i1 %_12.1, label %panic2, label %bb7

bb6:                                              ; preds = %bb3
; call _17_recursive_functions::ackermann
  %4 = call i32 @_ZN23_17_recursive_functions9ackermann17h102b3040c3f90c0eE(i32 %_8.0, i32 1)
  store i32 %4, ptr %_0, align 4
  br label %bb10

panic1:                                           ; preds = %bb3
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_6d64d7825a301a49ca1d8e43e23c199c) #23
  unreachable

bb7:                                              ; preds = %bb2
  %_15.0 = sub i32 %n, 1
  %_15.1 = icmp ult i32 %n, 1
  br i1 %_15.1, label %panic3, label %bb8

panic2:                                           ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_81861d80a9682a30573e7c02b4d210cb) #23
  unreachable

bb8:                                              ; preds = %bb7
; call _17_recursive_functions::ackermann
  %_13 = call i32 @_ZN23_17_recursive_functions9ackermann17h102b3040c3f90c0eE(i32 %m, i32 %_15.0)
; call _17_recursive_functions::ackermann
  %5 = call i32 @_ZN23_17_recursive_functions9ackermann17h102b3040c3f90c0eE(i32 %_12.0, i32 %_13)
  store i32 %5, ptr %_0, align 4
  br label %bb10

panic3:                                           ; preds = %bb7
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_3ac0073a3129e30c98e9e98ea35a1e4e) #23
  unreachable
}

; _17_recursive_functions::is_even_mutual
; Function Attrs: uwtable
define internal zeroext i1 @_ZN23_17_recursive_functions14is_even_mutual17h65d7f8ae3e1b9eb9E(i32 %n) unnamed_addr #0 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = icmp eq i32 %n, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %_0, align 1
  br label %bb4

bb2:                                              ; preds = %start
  %_3.0 = sub i32 %n, 1
  %_3.1 = icmp ult i32 %n, 1
  br i1 %_3.1, label %panic, label %bb3

bb4:                                              ; preds = %bb3, %bb1
  %1 = load i8, ptr %_0, align 1
  %2 = trunc nuw i8 %1 to i1
  ret i1 %2

bb3:                                              ; preds = %bb2
; call _17_recursive_functions::is_odd_mutual
  %3 = call zeroext i1 @_ZN23_17_recursive_functions13is_odd_mutual17h4ec99e8d08200a2eE(i32 %_3.0)
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %_0, align 1
  br label %bb4

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_77e61a8320ca4c31d86951c2a64a391a) #23
  unreachable
}

; _17_recursive_functions::is_odd_mutual
; Function Attrs: uwtable
define internal zeroext i1 @_ZN23_17_recursive_functions13is_odd_mutual17h4ec99e8d08200a2eE(i32 %n) unnamed_addr #0 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = icmp eq i32 %n, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb2:                                              ; preds = %start
  %_3.0 = sub i32 %n, 1
  %_3.1 = icmp ult i32 %n, 1
  br i1 %_3.1, label %panic, label %bb3

bb4:                                              ; preds = %bb3, %bb1
  %1 = load i8, ptr %_0, align 1
  %2 = trunc nuw i8 %1 to i1
  ret i1 %2

bb3:                                              ; preds = %bb2
; call _17_recursive_functions::is_even_mutual
  %3 = call zeroext i1 @_ZN23_17_recursive_functions14is_even_mutual17h65d7f8ae3e1b9eb9E(i32 %_3.0)
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %_0, align 1
  br label %bb4

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_e6f5e33d1a69d1ee46e70c3be92ea8ec) #23
  unreachable
}

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #7

; core::slice::index::slice_end_index_len_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core5slice5index24slice_end_index_len_fail17ha318ba5d7b15d3edE(i64, i64, ptr align 8) unnamed_addr #8

; core::slice::index::slice_index_order_fail
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core5slice5index22slice_index_order_fail17hdf994c7f82cfe787E(i64, i64, ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.uadd.sat.i64(i64, i64) #9

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #10

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #0

; core::fmt::Formatter::debug_list
; Function Attrs: uwtable
declare void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::finish
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #9

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #11

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; <char as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$char$u20$as$u20$core..fmt..Display$GT$3fmt17h51cc1da6341ff44fE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for usize>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp54_$LT$impl$u20$core..fmt..Display$u20$for$u20$usize$GT$3fmt17hddc54fe7c9ab81ddE"(ptr align 8, ptr align 8) unnamed_addr #0

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for u32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hdf87e42f0895a2dfE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for u64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h2e45205524cc218aE"(ptr align 8, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::entry
; Function Attrs: uwtable
declare align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8, ptr align 1, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.uadd.with.overflow.i32(i32, i32) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #9

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; core::char::methods::encode_utf8_raw::do_panic::runtime
; Function Attrs: noreturn uwtable
declare void @_ZN4core4char7methods15encode_utf8_raw8do_panic7runtime17h15f309f4b7afddfdE(i32, i64, i64, ptr align 8) unnamed_addr #12

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #0

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; alloc::fmt::format::format_inner
; Function Attrs: uwtable
declare void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8, ptr align 8) unnamed_addr #0

; alloc::raw_vec::RawVec<T,A>::grow_one
; Function Attrs: noinline uwtable
declare void @"_ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17hc9a9ed72510f0abfE"(ptr align 8, ptr align 8) unnamed_addr #3

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #13

; alloc::alloc::handle_alloc_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc5alloc18handle_alloc_error17h786143be1fde6527E(i64, i64) unnamed_addr #14

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #15

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #16

; __rustc::__rust_realloc
; Function Attrs: nounwind allockind("realloc,aligned") allocsize(3) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr allocptr, i64, i64 allocalign, i64) unnamed_addr #17

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: write)
declare void @llvm.memset.p0.i64(ptr nocapture writeonly, i8, i64, i1 immarg) #18

; core::option::expect_failed
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core6option13expect_failed17h3c22898af43fbfd1E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #14

; core::panicking::panic_bounds_check
; Function Attrs: cold minsize noinline noreturn optsize uwtable
declare void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64, i64, ptr align 8) unnamed_addr #19

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; core::panicking::panic_misaligned_pointer_dereference
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking36panic_misaligned_pointer_dereference17hb44f1541da1bf2fcE(i64, i64, ptr align 8) unnamed_addr #11

; core::panicking::panic_null_pointer_dereference
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking30panic_null_pointer_dereference17hc4b91efa6a96503bE(ptr align 8) unnamed_addr #11

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_rem_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8) unnamed_addr #8

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17hc500bcec324e4b2cE(ptr @_ZN23_17_recursive_functions4main17hd602de5f8643e1c9E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { cold uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #10 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #11 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #16 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #17 = { nounwind allockind("realloc,aligned") allocsize(3) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #18 = { nocallback nofree nounwind willreturn memory(argmem: write) }
attributes #19 = { cold minsize noinline noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #20 = { noreturn nounwind }
attributes #21 = { cold }
attributes #22 = { nounwind }
attributes #23 = { noreturn }
attributes #24 = { cold noreturn nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 7622179677738034}
