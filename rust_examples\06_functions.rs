// 6. Functions - definition, parameters, return values (Rust version)
// This file demonstrates all function patterns in Rust for comparison with Dolet

fn main() {
    println!("=== Functions Demo (Rust) ===");
    println!();

    // Simple function without parameters
    println!("Calling simple function:");
    greet();
    println!();

    // Function with single parameter
    println!("Function with single parameter:");
    greet_person("<PERSON>");
    greet_person("<PERSON>");
    println!();

    // Function with multiple parameters
    println!("Function with multiple parameters:");
    introduce("Ali", 25, "Cairo");
    introduce("Fatima", 30, "Alexandria");
    println!();

    // Function with return value
    println!("Function with return value:");
    let result1 = add_numbers(5, 3);
    let result2 = add_numbers(10, 7);
    println!("5 + 3 = {}", result1);
    println!("10 + 7 = {}", result2);
    println!();

    // Function with calculations and return
    println!("Function calculating area:");
    let room_area = calculate_area(12, 8);
    println!("Room area: {} square meters", room_area);
    println!();

    // Function with string operations
    println!("Function with string operations:");
    let name1 = create_full_name("<PERSON>", "<PERSON>");
    let name2 = create_full_name("Sara", "Ali");
    println!("Full name 1: {}", name1);
    println!("Full name 2: {}", name2);
    println!();

    // Function with conditional logic
    println!("Function with conditional logic:");
    let grade1 = check_grade(95);
    let grade2 = check_grade(82);
    let grade3 = check_grade(67);
    let grade4 = check_grade(45);
    println!("Score 95: Grade {}", grade1);
    println!("Score 82: Grade {}", grade2);
    println!("Score 67: Grade {}", grade3);
    println!("Score 45: Grade {}", grade4);
    println!();

    // Function with loop
    println!("Function with loop:");
    count_to_n(5);
    println!();

    // Function that calls another function
    println!("Function calling another function:");
    let rect_area = calculate_rectangle_area(6, 4);
    println!("Rectangle area: {}", rect_area);
    println!();

    // Function with complex parameters
    println!("Function with complex parameters:");
    let order_total = process_order("Laptop", 2, 1500);
    println!("Order total: {}", order_total);
    println!();

    // Function with boolean return
    println!("Function with boolean return:");
    let check1 = is_even(8);
    let check2 = is_even(7);
    println!("8 is even: {}", check1);
    println!("7 is even: {}", check2);
    println!();

    // Function with multiple calculations
    println!("Function with multiple calculations:");
    let total = calculate_stats(10, 15, 8);
    println!("Returned sum: {}", total);
    println!();

    // Recursive-style function
    println!("Recursive function:");
    countdown_function(5);
    println!();

    println!("=== End of Functions Demo ===");
}

// Simple function without parameters
fn greet() {
    println!("Hello from a function!");
    println!("This is a simple function");
}

// Function with single parameter
fn greet_person(name: &str) {
    println!("Hello, {}!", name);
    println!("Nice to meet you!");
}

// Function with multiple parameters
fn introduce(name: &str, age: i32, city: &str) {
    println!("Name: {}", name);
    println!("Age: {}", age);
    println!("City: {}", city);
    println!("---");
}

// Function with return value
fn add_numbers(a: i32, b: i32) -> i32 {
    a + b
}

// Function with calculations and return
fn calculate_area(length: i32, width: i32) -> i32 {
    println!("Calculating area of {} x {}", length, width);
    length * width
}

// Function with string operations
fn create_full_name(first: &str, last: &str) -> String {
    format!("{} {}", first, last)
}

// Function with conditional logic
fn check_grade(score: i32) -> &'static str {
    if score >= 90 {
        "A"
    } else if score >= 80 {
        "B"
    } else if score >= 70 {
        "C"
    } else if score >= 60 {
        "D"
    } else {
        "F"
    }
}

// Function with loop
fn count_to_n(n: i32) {
    println!("Counting to {}:", n);
    for i in 1..=n {
        println!("  {}", i);
    }
    println!("Done counting!");
}

// Function that calls another function
fn multiply(a: i32, b: i32) -> i32 {
    a * b
}

fn calculate_rectangle_area(length: i32, width: i32) -> i32 {
    println!("Rectangle dimensions: {} x {}", length, width);
    multiply(length, width)
}

// Function with complex parameters
fn process_order(item: &str, quantity: i32, price: i32) -> i32 {
    println!("Processing order:");
    println!("  Item: {}", item);
    println!("  Quantity: {}", quantity);
    println!("  Unit price: {}", price);
    let total = multiply(quantity, price);
    println!("  Total: {}", total);
    total
}

// Function with boolean return
fn is_even(number: i32) -> bool {
    number % 2 == 0
}

// Function with multiple calculations
fn calculate_stats(num1: i32, num2: i32, num3: i32) -> i32 {
    let sum = num1 + num2 + num3;
    let average = sum / 3;
    let max_val = num1.max(num2).max(num3);
    
    println!("Numbers: {}, {}, {}", num1, num2, num3);
    println!("Sum: {}", sum);
    println!("Average: {}", average);
    println!("Maximum: {}", max_val);
    sum
}

// Recursive function
fn countdown_function(n: i32) {
    if n > 0 {
        println!("Countdown: {}", n);
        countdown_function(n - 1);
    } else {
        println!("Countdown finished!");
    }
}
