# 04. Comparison Operators - Relational Operations
# This file demonstrates all comparison operators in Dolet

print "=== Comparison Operators Demo ==="
print ""

# Test values
int a = 10
int b = 5
int c = 10

print "Test values: a = " + a + ", b = " + b + ", c = " + c
print ""

# Greater than (>)
print "Greater Than (>) Tests:"
if a > b
    print "✓ " + a + " > " + b + " is TRUE"
else
    print "✗ " + a + " > " + b + " is FALSE"

if b > a
    print "✓ " + b + " > " + a + " is TRUE"
else
    print "✗ " + b + " > " + a + " is FALSE"
print ""

# Greater than or equal (>=)
print "Greater Than or Equal (>=) Tests:"
if a >= c
    print "✓ " + a + " >= " + c + " is TRUE"
else
    print "✗ " + a + " >= " + c + " is FALSE"

if a >= b
    print "✓ " + a + " >= " + b + " is TRUE"
else
    print "✗ " + a + " >= " + b + " is FALSE"

if b >= a
    print "✓ " + b + " >= " + a + " is TRUE"
else
    print "✗ " + b + " >= " + a + " is FALSE"
print ""

# Less than (<)
print "Less Than (<) Tests:"
if b < a
    print "✓ " + b + " < " + a + " is TRUE"
else
    print "✗ " + b + " < " + a + " is FALSE"

if a < b
    print "✓ " + a + " < " + b + " is TRUE"
else
    print "✗ " + a + " < " + b + " is FALSE"
print ""

# Less than or equal (<=)
print "Less Than or Equal (<=) Tests:"
if a <= c
    print "✓ " + a + " <= " + c + " is TRUE"
else
    print "✗ " + a + " <= " + c + " is FALSE"

if b <= a
    print "✓ " + b + " <= " + a + " is TRUE"
else
    print "✗ " + b + " <= " + a + " is FALSE"

if a <= b
    print "✓ " + a + " <= " + b + " is TRUE"
else
    print "✗ " + a + " <= " + b + " is FALSE"
print ""

# Equal to (==)
print "Equal To (==) Tests:"
if a == c
    print "✓ " + a + " == " + c + " is TRUE"
else
    print "✗ " + a + " == " + c + " is FALSE"

if a == b
    print "✓ " + a + " == " + b + " is TRUE"
else
    print "✗ " + a + " == " + b + " is FALSE"
print ""

# Not equal to (!=)
print "Not Equal To (!=) Tests:"
if a != b
    print "✓ " + a + " != " + b + " is TRUE"
else
    print "✗ " + a + " != " + b + " is FALSE"

if a != c
    print "✓ " + a + " != " + c + " is TRUE"
else
    print "✗ " + a + " != " + c + " is FALSE"
print ""

# String comparisons
print "String Comparison Tests:"
string name1 = "Ahmed"
string name2 = "Ali"
string name3 = "Ahmed"

print "Strings: name1 = '" + name1 + "', name2 = '" + name2 + "', name3 = '" + name3 + "'"

if name1 == name3
    print "✓ name1 == name3 is TRUE"
else
    print "✗ name1 == name3 is FALSE"

if name1 != name2
    print "✓ name1 != name2 is TRUE"
else
    print "✗ name1 != name2 is FALSE"
print ""

# Boolean comparisons
print "Boolean Comparison Tests:"
bool flag1 = true
bool flag2 = false
bool flag3 = true

print "Booleans: flag1 = " + flag1 + ", flag2 = " + flag2 + ", flag3 = " + flag3

if flag1 == flag3
    print "✓ flag1 == flag3 is TRUE"
else
    print "✗ flag1 == flag3 is FALSE"

if flag1 != flag2
    print "✓ flag1 != flag2 is TRUE"
else
    print "✗ flag1 != flag2 is FALSE"
print ""

# Float comparisons
print "Float Comparison Tests:"
float x = 5.5
float y = 3.2
float z = 5.5

print "Floats: x = " + x + ", y = " + y + ", z = " + z

if x > y
    print "✓ x > y is TRUE"

if x == z
    print "✓ x == z is TRUE"

if y < x
    print "✓ y < x is TRUE"
print ""

# Complex expression comparisons
print "Complex Expression Comparisons:"
if (a + b) > (c * 2)
    print "✓ (a + b) > (c * 2) is TRUE"
else
    print "✗ (a + b) > (c * 2) is FALSE"

if (a * 2) == (c + a)
    print "✓ (a * 2) == (c + a) is TRUE"
else
    print "✗ (a * 2) == (c + a) is FALSE"
print ""

print "=== End of Comparison Operators Demo ==="
