; ModuleID = '03_comparison_operations.92838cc27404037e-cgu.0'
source_filename = "03_comparison_operations.92838cc27404037e-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0db93af02de05d19E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h363e22ea09ad4de1E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h363e22ea09ad4de1E" }>, align 8
@anon.aea462503587b62557321f00ac1abbfc.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_a8e0cf7ba94aed69eebe0921ea19e23e = private unnamed_addr constant [42 x i8] c"=== Comparison Operations Demo (Rust) ===\0A", align 1
@alloc_169186e85cdd7c5c664a3b96d288584d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a8e0cf7ba94aed69eebe0921ea19e23e, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2239fe02c1cb27fe1ef0b12054ed72d1 = private unnamed_addr constant [29 x i8] c"Test values: a=10, b=5, c=10\0A", align 1
@alloc_5474e71d55e321226eda2a3b4dc3b4dc = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2239fe02c1cb27fe1ef0b12054ed72d1, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_76a16a176c85e93959a2356b06035156 = private unnamed_addr constant [24 x i8] c"Greater Than (>) Tests:\0A", align 1
@alloc_b15e7a99efad64eb1c7e1f6b9efb644f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_76a16a176c85e93959a2356b06035156, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_2419e0c9e38cb8ce3e88322a8fdd9f03 = private unnamed_addr constant [4 x i8] c"\E2\9C\97 ", align 1
@alloc_a1fc54a3d1c1b0e65200d44550a0c47c = private unnamed_addr constant [3 x i8] c" > ", align 1
@alloc_46d3c92b6191e6bd907de2e3c3383167 = private unnamed_addr constant [10 x i8] c" is FALSE\0A", align 1
@alloc_2fbc56f4d26008881524e876f2fa35e6 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_a1fc54a3d1c1b0e65200d44550a0c47c, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_f9d3eb904d836656d19751e96e52db00 = private unnamed_addr constant [4 x i8] c"\E2\9C\93 ", align 1
@alloc_972ab34e1e4eddf594b6c725899e1ef2 = private unnamed_addr constant [9 x i8] c" is TRUE\0A", align 1
@alloc_61b917cbc4ed038481f15bbf0023211e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_a1fc54a3d1c1b0e65200d44550a0c47c, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_17e7092dfc6f5e8b58aae2f5767c24d4 = private unnamed_addr constant [34 x i8] c"Greater Than or Equal (>=) Tests:\0A", align 1
@alloc_b4cacfa4d0f8962d3c4bc772254c359c = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_17e7092dfc6f5e8b58aae2f5767c24d4, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_3d5f084549f1b6ffc93d5cdb66afa2e4 = private unnamed_addr constant [4 x i8] c" >= ", align 1
@alloc_d2e7485aabc217762c9afbbd897eeec4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3d5f084549f1b6ffc93d5cdb66afa2e4, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_7530b4cb6ae6b00bb3de6bd811c201bd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3d5f084549f1b6ffc93d5cdb66afa2e4, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_a0e486c91a0120231625f624757b9c76 = private unnamed_addr constant [21 x i8] c"Less Than (<) Tests:\0A", align 1
@alloc_0f8f72261f3b5354c00b994adc68d488 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a0e486c91a0120231625f624757b9c76, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_485aece92251293aba303622a598e846 = private unnamed_addr constant [3 x i8] c" < ", align 1
@alloc_228bae72e1267a2e36b014c93c2a97e7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_485aece92251293aba303622a598e846, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_42d64bd266701d0ddff554ba6418a6e5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_485aece92251293aba303622a598e846, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_b06a1f6df21a4163fd34b2c2836322c2 = private unnamed_addr constant [31 x i8] c"Less Than or Equal (<=) Tests:\0A", align 1
@alloc_2ae81a225c1adbc0e69dc384406433b5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b06a1f6df21a4163fd34b2c2836322c2, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_0d8f95680cb954489259f79349b0e35b = private unnamed_addr constant [4 x i8] c" <= ", align 1
@alloc_d730f16ffbc7d567401bf9998be66c73 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0d8f95680cb954489259f79349b0e35b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_3aae30c3010ac8a9c22ba8255cc9bb82 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0d8f95680cb954489259f79349b0e35b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_51e8655b35c8dc8798eb68e95774f542 = private unnamed_addr constant [21 x i8] c"Equal To (==) Tests:\0A", align 1
@alloc_32f2e3b84dbf8d7cf3d674b7a1ff5ad8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_51e8655b35c8dc8798eb68e95774f542, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_277014ab52c48b087481792d8fd8deda = private unnamed_addr constant [4 x i8] c" == ", align 1
@alloc_219841f71ca5342dd2504727bc487bc5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_277014ab52c48b087481792d8fd8deda, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_a3ec9e620430b2be55da89adbad333b2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_277014ab52c48b087481792d8fd8deda, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_cf3f560695dc1f5828fcf76fb1506c55 = private unnamed_addr constant [25 x i8] c"Not Equal To (!=) Tests:\0A", align 1
@alloc_732fc984de884acb26178538d89ac2a1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_cf3f560695dc1f5828fcf76fb1506c55, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_69f4ff80c83c2ae2fedbb9e034f16bdc = private unnamed_addr constant [4 x i8] c" != ", align 1
@alloc_9accddbd48f6dc302d0351ffdc6bd58b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2419e0c9e38cb8ce3e88322a8fdd9f03, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_69f4ff80c83c2ae2fedbb9e034f16bdc, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_46d3c92b6191e6bd907de2e3c3383167, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_b04174623dccbde1ff56a4f2df4517cf = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f9d3eb904d836656d19751e96e52db00, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_69f4ff80c83c2ae2fedbb9e034f16bdc, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_972ab34e1e4eddf594b6c725899e1ef2, [8 x i8] c"\09\00\00\00\00\00\00\00" }>, align 8
@alloc_b468ef00fa5916b1335583d25c8252ad = private unnamed_addr constant [5 x i8] c"Ahmed", align 1
@alloc_a118b2b99663e794e1f4a92820d83446 = private unnamed_addr constant [3 x i8] c"Ali", align 1
@alloc_2f9a31f129c87a2f4750d0cb8f98091d = private unnamed_addr constant [25 x i8] c"String Comparison Tests:\0A", align 1
@alloc_c9c3b3f6c5255219bcf29567ca56c3e5 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2f9a31f129c87a2f4750d0cb8f98091d, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_226f98dfc9b7f26b87e67e95f92ebcc5 = private unnamed_addr constant [51 x i8] c"Strings: name1='Ahmed', name2='Ali', name3='Ahmed'\0A", align 1
@alloc_e22e67e860ad8638af95d248d09aad48 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_226f98dfc9b7f26b87e67e95f92ebcc5, [8 x i8] c"3\00\00\00\00\00\00\00" }>, align 8
@alloc_935ad992fc7a6303d4ccb2f516385171 = private unnamed_addr constant [28 x i8] c"\E2\9C\97 name1 == name3 is FALSE\0A", align 1
@alloc_ed3bd2f5f0555dea9196068f53ba5afb = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_935ad992fc7a6303d4ccb2f516385171, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_2ce09d3dc2a536b7ec6092dcb25e44f7 = private unnamed_addr constant [27 x i8] c"\E2\9C\93 name1 == name3 is TRUE\0A", align 1
@alloc_a9247dc6d8a08e9f5e55e24b14bb1ab9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2ce09d3dc2a536b7ec6092dcb25e44f7, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_a9ada332f37bdcd85038dba9cae57918 = private unnamed_addr constant [28 x i8] c"\E2\9C\97 name1 != name2 is FALSE\0A", align 1
@alloc_9bef02e1c9b4619e086284f65e1b2a53 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a9ada332f37bdcd85038dba9cae57918, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_4b9e1e8db5bec9f4a94487f88b2dad4c = private unnamed_addr constant [27 x i8] c"\E2\9C\93 name1 != name2 is TRUE\0A", align 1
@alloc_ff942cb3b964db93e96e8cb43d123019 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4b9e1e8db5bec9f4a94487f88b2dad4c, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_0dfdbf6f5131667c05ee3c4c0ec19d3d = private unnamed_addr constant [26 x i8] c"Boolean Comparison Tests:\0A", align 1
@alloc_81ae713f5877fe6d78cddf1ddc025410 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_0dfdbf6f5131667c05ee3c4c0ec19d3d, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_aa34bae5e865a57645708e8ce36062b6 = private unnamed_addr constant [46 x i8] c"Booleans: flag1=true, flag2=false, flag3=true\0A", align 1
@alloc_b16a2882c18c4fdbe1ee279c809c76a9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_aa34bae5e865a57645708e8ce36062b6, [8 x i8] c".\00\00\00\00\00\00\00" }>, align 8
@alloc_495d5a68fe846c000fdad912a6da4cc0 = private unnamed_addr constant [28 x i8] c"\E2\9C\97 flag1 == flag3 is FALSE\0A", align 1
@alloc_4933f8bd3fb81b8a4328d089f8b7f860 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_495d5a68fe846c000fdad912a6da4cc0, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_47443cc73cd23a8f009765a1e419ee38 = private unnamed_addr constant [27 x i8] c"\E2\9C\93 flag1 == flag3 is TRUE\0A", align 1
@alloc_38534158e99fb328b1bfa55296ee49ee = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_47443cc73cd23a8f009765a1e419ee38, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_3a4954b193fa34410024ef955a26bbd7 = private unnamed_addr constant [28 x i8] c"\E2\9C\97 flag1 != flag2 is FALSE\0A", align 1
@alloc_eec862bf8f1ff7d51c48978d943e237d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_3a4954b193fa34410024ef955a26bbd7, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_b4d9bcae44c593cb9af9a0c1153495ef = private unnamed_addr constant [27 x i8] c"\E2\9C\93 flag1 != flag2 is TRUE\0A", align 1
@alloc_b3c2043b836d7896e026a1c893db82a9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b4d9bcae44c593cb9af9a0c1153495ef, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_03d2fc416d08552e8a9247ca26d83ee0 = private unnamed_addr constant [24 x i8] c"Float Comparison Tests:\0A", align 1
@alloc_0db9d9b454a38aadd91a9fb4e31d4f5b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_03d2fc416d08552e8a9247ca26d83ee0, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_81920b6864abc44ab4adbc6751e60952 = private unnamed_addr constant [28 x i8] c"Floats: x=5.5, y=3.2, z=5.5\0A", align 1
@alloc_70be47fc514a54d9ae04d3381995593e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_81920b6864abc44ab4adbc6751e60952, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_c035068ce56e21206cd679947e8fc5f5 = private unnamed_addr constant [18 x i8] c"\E2\9C\93 x > y is TRUE\0A", align 1
@alloc_cef4553833a0d4ada65b9a1d6be809a7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c035068ce56e21206cd679947e8fc5f5, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_b59b11e9ebd2928c54e6037e671244fe = private unnamed_addr constant [35 x i8] c"\E2\9C\93 x == z is TRUE (approximately)\0A", align 1
@alloc_a0b79a941a76204a15d9c8e7304a4fd0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b59b11e9ebd2928c54e6037e671244fe, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_f20a33e2028ab24b833eee439c32a697 = private unnamed_addr constant [18 x i8] c"\E2\9C\93 y < x is TRUE\0A", align 1
@alloc_53df38fdfd218ac7c2ac8d036f516b95 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f20a33e2028ab24b833eee439c32a697, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_226ba3c641a6b9b2f2e14f6cb67f273f = private unnamed_addr constant [32 x i8] c"Complex Expression Comparisons:\0A", align 1
@alloc_f4906823c19b697bd029dfac67aadb02 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_226ba3c641a6b9b2f2e14f6cb67f273f, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_1031fa8fac96c9ef740d0e79d0ad85af = private unnamed_addr constant [27 x i8] c"03_comparison_operations.rs", align 1
@alloc_0df0fc388ea26fde28a7ac7e9c7b996c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1031fa8fac96c9ef740d0e79d0ad85af, [16 x i8] c"\1B\00\00\00\00\00\00\00\B3\00\00\00\08\00\00\00" }>, align 8
@alloc_0f49c5132e8dc8a73710d43f3a347460 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1031fa8fac96c9ef740d0e79d0ad85af, [16 x i8] c"\1B\00\00\00\00\00\00\00\B3\00\00\00\12\00\00\00" }>, align 8
@alloc_d618b887c17015744eb9e7f8e845db8b = private unnamed_addr constant [31 x i8] c"\E2\9C\97 (a + b) > (c * 2) is FALSE\0A", align 1
@alloc_7cd9c9d8865358b7a6bcaf42c35711b0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d618b887c17015744eb9e7f8e845db8b, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_c52247812b1f0dee3422e39b615d3b34 = private unnamed_addr constant [30 x i8] c"\E2\9C\93 (a + b) > (c * 2) is TRUE\0A", align 1
@alloc_9fc147be84f93bb5c148984cb47fcc50 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c52247812b1f0dee3422e39b615d3b34, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_0ab1546e0068083f065039f394d74e5c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1031fa8fac96c9ef740d0e79d0ad85af, [16 x i8] c"\1B\00\00\00\00\00\00\00\B9\00\00\00\08\00\00\00" }>, align 8
@alloc_cba5c27c47119a53b217726ef7304c2d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1031fa8fac96c9ef740d0e79d0ad85af, [16 x i8] c"\1B\00\00\00\00\00\00\00\B9\00\00\00\13\00\00\00" }>, align 8
@alloc_763a71ccedb724943d742b36c92de111 = private unnamed_addr constant [32 x i8] c"\E2\9C\97 (a * 2) == (c + a) is FALSE\0A", align 1
@alloc_c99a803bf9d75c8e48634e0d6f21a5de = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_763a71ccedb724943d742b36c92de111, [8 x i8] c" \00\00\00\00\00\00\00" }>, align 8
@alloc_ebe7ef88fd3427dcec1625018289b829 = private unnamed_addr constant [31 x i8] c"\E2\9C\93 (a * 2) == (c + a) is TRUE\0A", align 1
@alloc_a7f5c7f833e449233e34f46d59debc31 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ebe7ef88fd3427dcec1625018289b829, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_a68cfa88583a4ee58f228f09750ac7da = private unnamed_addr constant [42 x i8] c"=== End of Comparison Operations Demo ===\0A", align 1
@alloc_598b11318113cb8adbe10f5974d54fd6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a68cfa88583a4ee58f228f09750ac7da, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h75288183b87c09c5E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h363e22ea09ad4de1E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hd06515fddf229345E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7fd0225416400bdbE"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hd06515fddf229345E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17hac6ede395b01a132E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h3243b3e7cdbe163aE"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
  %_4.0 = load ptr, ptr %other, align 8
  %1 = getelementptr inbounds i8, ptr %other, i64 8
  %_4.1 = load i64, ptr %1, align 8
; call core::str::traits::<impl core::cmp::PartialEq for str>::eq
  %_0 = call zeroext i1 @"_ZN4core3str6traits54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$str$GT$2eq17hfc7f081021e81599E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 1 %_4.0, i64 %_4.1)
  ret i1 %_0
}

; core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::ne
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2ne17h946bbdee7235e12cE"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
  %_4.0 = load ptr, ptr %other, align 8
  %1 = getelementptr inbounds i8, ptr %other, i64 8
  %_4.1 = load i64, ptr %1, align 8
; call core::cmp::PartialEq::ne
  %_0 = call zeroext i1 @_ZN4core3cmp9PartialEq2ne17haeaa2f929564c19cE(ptr align 1 %_3.0, i64 %_3.1, ptr align 1 %_4.0, i64 %_4.1)
  ret i1 %_0
}

; core::cmp::PartialEq::ne
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core3cmp9PartialEq2ne17haeaa2f929564c19cE(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #1 {
start:
; call core::str::traits::<impl core::cmp::PartialEq for str>::eq
  %_3 = call zeroext i1 @"_ZN4core3str6traits54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$str$GT$2eq17hfc7f081021e81599E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1)
  %_0 = xor i1 %_3, true
  ret i1 %_0
}

; core::f32::<impl f32>::abs
; Function Attrs: inlinehint uwtable
define internal float @"_ZN4core3f3221_$LT$impl$u20$f32$GT$3abs17h871d375d49a27b82E"(float %self) unnamed_addr #1 {
start:
  %0 = alloca [4 x i8], align 4
  %1 = call float @llvm.fabs.f32(float %self)
  store float %1, ptr %0, align 4
  %_0 = load float, ptr %0, align 4
  ret float %_0
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.aea462503587b62557321f00ac1abbfc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.aea462503587b62557321f00ac1abbfc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.aea462503587b62557321f00ac1abbfc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.aea462503587b62557321f00ac1abbfc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h0db93af02de05d19E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17he1a35ed35eed674cE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17hac6ede395b01a132E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17he1a35ed35eed674cE(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h363e22ea09ad4de1E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hb3e708f6443db3bdE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::str::traits::<impl core::cmp::PartialEq for str>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3str6traits54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$str$GT$2eq17hfc7f081021e81599E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #1 {
start:
  %other = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %self.0, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %self.1, ptr %0, align 8
  store ptr %other.0, ptr %other, align 8
  %1 = getelementptr inbounds i8, ptr %other, i64 8
  store i64 %other.1, ptr %1, align 8
  %2 = load ptr, ptr %self, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = load ptr, ptr %other, align 8
  %6 = getelementptr inbounds i8, ptr %other, i64 8
  %7 = load i64, ptr %6, align 8
; call <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
  %_0 = call zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17hdf2a02966b4ca049E"(ptr align 1 %2, i64 %4, ptr align 1 %5, i64 %7)
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7fd0225416400bdbE"() unnamed_addr #1 {
start:
  ret i32 0
}

; <[A] as core::slice::cmp::SlicePartialEq<B>>::equal
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN73_$LT$$u5b$A$u5d$$u20$as$u20$core..slice..cmp..SlicePartialEq$LT$B$GT$$GT$5equal17hdf2a02966b4ca049E"(ptr align 1 %self.0, i64 %self.1, ptr align 1 %other.0, i64 %other.1) unnamed_addr #0 {
start:
  %0 = alloca [4 x i8], align 4
  %1 = alloca [8 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %_3 = icmp ne i64 %self.1, %other.1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %2 = mul nuw nsw i64 %self.1, 1
  store i64 %2, ptr %1, align 8
  %size = load i64, ptr %1, align 8
  %3 = call i32 @memcmp(ptr %self.0, ptr %other.0, i64 %size)
  store i32 %3, ptr %0, align 4
  %_7 = load i32, ptr %0, align 4
  %4 = icmp eq i32 %_7, 0
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb4

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb4

bb4:                                              ; preds = %bb1, %bb2
  %6 = load i8, ptr %_0, align 1
  %7 = trunc nuw i8 %6 to i1
  ret i1 %7
}

; _03_comparison_operations::main
; Function Attrs: uwtable
define internal void @_ZN25_03_comparison_operations4main17hf115dce921cb2e11E() unnamed_addr #0 {
start:
  %_429 = alloca [48 x i8], align 8
  %_426 = alloca [48 x i8], align 8
  %_423 = alloca [48 x i8], align 8
  %_420 = alloca [48 x i8], align 8
  %_412 = alloca [48 x i8], align 8
  %_409 = alloca [48 x i8], align 8
  %_401 = alloca [48 x i8], align 8
  %_398 = alloca [48 x i8], align 8
  %_395 = alloca [48 x i8], align 8
  %_391 = alloca [48 x i8], align 8
  %_384 = alloca [48 x i8], align 8
  %_380 = alloca [48 x i8], align 8
  %_377 = alloca [48 x i8], align 8
  %_372 = alloca [48 x i8], align 8
  %_369 = alloca [48 x i8], align 8
  %_366 = alloca [48 x i8], align 8
  %_361 = alloca [48 x i8], align 8
  %_358 = alloca [48 x i8], align 8
  %_353 = alloca [48 x i8], align 8
  %_350 = alloca [48 x i8], align 8
  %_346 = alloca [48 x i8], align 8
  %_343 = alloca [48 x i8], align 8
  %_340 = alloca [48 x i8], align 8
  %_334 = alloca [48 x i8], align 8
  %_331 = alloca [48 x i8], align 8
  %_325 = alloca [48 x i8], align 8
  %_322 = alloca [48 x i8], align 8
  %name3 = alloca [16 x i8], align 8
  %name2 = alloca [16 x i8], align 8
  %name1 = alloca [16 x i8], align 8
  %_316 = alloca [48 x i8], align 8
  %_313 = alloca [16 x i8], align 8
  %_311 = alloca [16 x i8], align 8
  %_310 = alloca [32 x i8], align 8
  %_307 = alloca [48 x i8], align 8
  %_304 = alloca [16 x i8], align 8
  %_302 = alloca [16 x i8], align 8
  %_301 = alloca [32 x i8], align 8
  %_298 = alloca [48 x i8], align 8
  %_294 = alloca [16 x i8], align 8
  %_292 = alloca [16 x i8], align 8
  %_291 = alloca [32 x i8], align 8
  %_288 = alloca [48 x i8], align 8
  %_285 = alloca [16 x i8], align 8
  %_283 = alloca [16 x i8], align 8
  %_282 = alloca [32 x i8], align 8
  %_279 = alloca [48 x i8], align 8
  %_275 = alloca [48 x i8], align 8
  %_272 = alloca [48 x i8], align 8
  %_269 = alloca [16 x i8], align 8
  %_267 = alloca [16 x i8], align 8
  %_266 = alloca [32 x i8], align 8
  %_263 = alloca [48 x i8], align 8
  %_260 = alloca [16 x i8], align 8
  %_258 = alloca [16 x i8], align 8
  %_257 = alloca [32 x i8], align 8
  %_254 = alloca [48 x i8], align 8
  %_250 = alloca [16 x i8], align 8
  %_248 = alloca [16 x i8], align 8
  %_247 = alloca [32 x i8], align 8
  %_244 = alloca [48 x i8], align 8
  %_241 = alloca [16 x i8], align 8
  %_239 = alloca [16 x i8], align 8
  %_238 = alloca [32 x i8], align 8
  %_235 = alloca [48 x i8], align 8
  %_231 = alloca [48 x i8], align 8
  %_228 = alloca [48 x i8], align 8
  %_225 = alloca [16 x i8], align 8
  %_223 = alloca [16 x i8], align 8
  %_222 = alloca [32 x i8], align 8
  %_219 = alloca [48 x i8], align 8
  %_216 = alloca [16 x i8], align 8
  %_214 = alloca [16 x i8], align 8
  %_213 = alloca [32 x i8], align 8
  %_210 = alloca [48 x i8], align 8
  %_206 = alloca [16 x i8], align 8
  %_204 = alloca [16 x i8], align 8
  %_203 = alloca [32 x i8], align 8
  %_200 = alloca [48 x i8], align 8
  %_197 = alloca [16 x i8], align 8
  %_195 = alloca [16 x i8], align 8
  %_194 = alloca [32 x i8], align 8
  %_191 = alloca [48 x i8], align 8
  %_187 = alloca [16 x i8], align 8
  %_185 = alloca [16 x i8], align 8
  %_184 = alloca [32 x i8], align 8
  %_181 = alloca [48 x i8], align 8
  %_178 = alloca [16 x i8], align 8
  %_176 = alloca [16 x i8], align 8
  %_175 = alloca [32 x i8], align 8
  %_172 = alloca [48 x i8], align 8
  %_168 = alloca [48 x i8], align 8
  %_165 = alloca [48 x i8], align 8
  %_162 = alloca [16 x i8], align 8
  %_160 = alloca [16 x i8], align 8
  %_159 = alloca [32 x i8], align 8
  %_156 = alloca [48 x i8], align 8
  %_153 = alloca [16 x i8], align 8
  %_151 = alloca [16 x i8], align 8
  %_150 = alloca [32 x i8], align 8
  %_147 = alloca [48 x i8], align 8
  %_143 = alloca [16 x i8], align 8
  %_141 = alloca [16 x i8], align 8
  %_140 = alloca [32 x i8], align 8
  %_137 = alloca [48 x i8], align 8
  %_134 = alloca [16 x i8], align 8
  %_132 = alloca [16 x i8], align 8
  %_131 = alloca [32 x i8], align 8
  %_128 = alloca [48 x i8], align 8
  %_124 = alloca [48 x i8], align 8
  %_121 = alloca [48 x i8], align 8
  %_118 = alloca [16 x i8], align 8
  %_116 = alloca [16 x i8], align 8
  %_115 = alloca [32 x i8], align 8
  %_112 = alloca [48 x i8], align 8
  %_109 = alloca [16 x i8], align 8
  %_107 = alloca [16 x i8], align 8
  %_106 = alloca [32 x i8], align 8
  %_103 = alloca [48 x i8], align 8
  %_99 = alloca [16 x i8], align 8
  %_97 = alloca [16 x i8], align 8
  %_96 = alloca [32 x i8], align 8
  %_93 = alloca [48 x i8], align 8
  %_90 = alloca [16 x i8], align 8
  %_88 = alloca [16 x i8], align 8
  %_87 = alloca [32 x i8], align 8
  %_84 = alloca [48 x i8], align 8
  %_80 = alloca [16 x i8], align 8
  %_78 = alloca [16 x i8], align 8
  %_77 = alloca [32 x i8], align 8
  %_74 = alloca [48 x i8], align 8
  %_71 = alloca [16 x i8], align 8
  %_69 = alloca [16 x i8], align 8
  %_68 = alloca [32 x i8], align 8
  %_65 = alloca [48 x i8], align 8
  %_61 = alloca [48 x i8], align 8
  %_58 = alloca [48 x i8], align 8
  %_55 = alloca [16 x i8], align 8
  %_53 = alloca [16 x i8], align 8
  %_52 = alloca [32 x i8], align 8
  %_49 = alloca [48 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %_44 = alloca [16 x i8], align 8
  %_43 = alloca [32 x i8], align 8
  %_40 = alloca [48 x i8], align 8
  %_36 = alloca [16 x i8], align 8
  %_34 = alloca [16 x i8], align 8
  %_33 = alloca [32 x i8], align 8
  %_30 = alloca [48 x i8], align 8
  %_27 = alloca [16 x i8], align 8
  %_25 = alloca [16 x i8], align 8
  %_24 = alloca [32 x i8], align 8
  %_21 = alloca [48 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %_14 = alloca [48 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %c = alloca [4 x i8], align 4
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_169186e85cdd7c5c664a3b96d288584d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  store i32 10, ptr %a, align 4
  store i32 5, ptr %b, align 4
  store i32 10, ptr %c, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_5474e71d55e321226eda2a3b4dc3b4dc)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_17, ptr align 8 @alloc_b15e7a99efad64eb1c7e1f6b9efb644f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_17)
  %0 = load i32, ptr %a, align 4
  %1 = load i32, ptr %b, align 4
  %_19 = icmp sgt i32 %0, %1
  br i1 %_19, label %bb11, label %bb15

bb15:                                             ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_34, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_36, ptr align 4 %b)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_33, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_34, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_33, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_36, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_30, ptr align 8 @alloc_2fbc56f4d26008881524e876f2fa35e6, ptr align 8 %_33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_30)
  br label %bb19

bb11:                                             ; preds = %start
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_25, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_27, ptr align 4 %b)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_24, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_25, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_24, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_27, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_21, ptr align 8 @alloc_61b917cbc4ed038481f15bbf0023211e, ptr align 8 %_24)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_21)
  br label %bb19

bb19:                                             ; preds = %bb11, %bb15
  %6 = load i32, ptr %b, align 4
  %7 = load i32, ptr %a, align 4
  %_38 = icmp sgt i32 %6, %7
  br i1 %_38, label %bb20, label %bb24

bb24:                                             ; preds = %bb19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_53, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_55, ptr align 4 %a)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_52, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_53, i64 16, i1 false)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_52, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_55, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_49, ptr align 8 @alloc_2fbc56f4d26008881524e876f2fa35e6, ptr align 8 %_52)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_49)
  br label %bb28

bb20:                                             ; preds = %bb19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_44, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_46, ptr align 4 %a)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_43, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_44, i64 16, i1 false)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_43, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_46, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_40, ptr align 8 @alloc_61b917cbc4ed038481f15bbf0023211e, ptr align 8 %_43)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_40)
  br label %bb28

bb28:                                             ; preds = %bb20, %bb24
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_58, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_58)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_61, ptr align 8 @alloc_b4cacfa4d0f8962d3c4bc772254c359c)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_61)
  %12 = load i32, ptr %a, align 4
  %13 = load i32, ptr %c, align 4
  %_63 = icmp sge i32 %12, %13
  br i1 %_63, label %bb33, label %bb37

bb37:                                             ; preds = %bb28
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_78, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_80, ptr align 4 %c)
  %14 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_77, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %14, ptr align 8 %_78, i64 16, i1 false)
  %15 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_77, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %15, ptr align 8 %_80, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_74, ptr align 8 @alloc_d2e7485aabc217762c9afbbd897eeec4, ptr align 8 %_77)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_74)
  br label %bb41

bb33:                                             ; preds = %bb28
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_69, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_71, ptr align 4 %c)
  %16 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_68, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %16, ptr align 8 %_69, i64 16, i1 false)
  %17 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_68, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %17, ptr align 8 %_71, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_65, ptr align 8 @alloc_7530b4cb6ae6b00bb3de6bd811c201bd, ptr align 8 %_68)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_65)
  br label %bb41

bb41:                                             ; preds = %bb33, %bb37
  %18 = load i32, ptr %a, align 4
  %19 = load i32, ptr %b, align 4
  %_82 = icmp sge i32 %18, %19
  br i1 %_82, label %bb42, label %bb46

bb46:                                             ; preds = %bb41
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_97, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_99, ptr align 4 %b)
  %20 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_96, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %20, ptr align 8 %_97, i64 16, i1 false)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_96, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_99, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_93, ptr align 8 @alloc_d2e7485aabc217762c9afbbd897eeec4, ptr align 8 %_96)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_93)
  br label %bb50

bb42:                                             ; preds = %bb41
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_88, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_90, ptr align 4 %b)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_87, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_88, i64 16, i1 false)
  %23 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_87, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %23, ptr align 8 %_90, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_84, ptr align 8 @alloc_7530b4cb6ae6b00bb3de6bd811c201bd, ptr align 8 %_87)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_84)
  br label %bb50

bb50:                                             ; preds = %bb42, %bb46
  %24 = load i32, ptr %b, align 4
  %25 = load i32, ptr %a, align 4
  %_101 = icmp sge i32 %24, %25
  br i1 %_101, label %bb51, label %bb55

bb55:                                             ; preds = %bb50
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_116, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_118, ptr align 4 %a)
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_115, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_116, i64 16, i1 false)
  %27 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_115, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %27, ptr align 8 %_118, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_112, ptr align 8 @alloc_d2e7485aabc217762c9afbbd897eeec4, ptr align 8 %_115)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_112)
  br label %bb59

bb51:                                             ; preds = %bb50
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_107, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_109, ptr align 4 %a)
  %28 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_106, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %28, ptr align 8 %_107, i64 16, i1 false)
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_106, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_109, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_103, ptr align 8 @alloc_7530b4cb6ae6b00bb3de6bd811c201bd, ptr align 8 %_106)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_103)
  br label %bb59

bb59:                                             ; preds = %bb51, %bb55
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_121, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_121)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_124, ptr align 8 @alloc_0f8f72261f3b5354c00b994adc68d488)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_124)
  %30 = load i32, ptr %b, align 4
  %31 = load i32, ptr %a, align 4
  %_126 = icmp slt i32 %30, %31
  br i1 %_126, label %bb64, label %bb68

bb68:                                             ; preds = %bb59
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_141, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_143, ptr align 4 %a)
  %32 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_140, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %32, ptr align 8 %_141, i64 16, i1 false)
  %33 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_140, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %33, ptr align 8 %_143, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_137, ptr align 8 @alloc_228bae72e1267a2e36b014c93c2a97e7, ptr align 8 %_140)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_137)
  br label %bb72

bb64:                                             ; preds = %bb59
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_132, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_134, ptr align 4 %a)
  %34 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_131, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %34, ptr align 8 %_132, i64 16, i1 false)
  %35 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_131, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %35, ptr align 8 %_134, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_128, ptr align 8 @alloc_42d64bd266701d0ddff554ba6418a6e5, ptr align 8 %_131)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_128)
  br label %bb72

bb72:                                             ; preds = %bb64, %bb68
  %36 = load i32, ptr %a, align 4
  %37 = load i32, ptr %b, align 4
  %_145 = icmp slt i32 %36, %37
  br i1 %_145, label %bb73, label %bb77

bb77:                                             ; preds = %bb72
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_160, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_162, ptr align 4 %b)
  %38 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_159, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %38, ptr align 8 %_160, i64 16, i1 false)
  %39 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_159, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %39, ptr align 8 %_162, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_156, ptr align 8 @alloc_228bae72e1267a2e36b014c93c2a97e7, ptr align 8 %_159)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_156)
  br label %bb81

bb73:                                             ; preds = %bb72
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_151, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_153, ptr align 4 %b)
  %40 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_150, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %40, ptr align 8 %_151, i64 16, i1 false)
  %41 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_150, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %41, ptr align 8 %_153, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_147, ptr align 8 @alloc_42d64bd266701d0ddff554ba6418a6e5, ptr align 8 %_150)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_147)
  br label %bb81

bb81:                                             ; preds = %bb73, %bb77
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_165, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_165)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_168, ptr align 8 @alloc_2ae81a225c1adbc0e69dc384406433b5)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_168)
  %42 = load i32, ptr %a, align 4
  %43 = load i32, ptr %c, align 4
  %_170 = icmp sle i32 %42, %43
  br i1 %_170, label %bb86, label %bb90

bb90:                                             ; preds = %bb81
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_185, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_187, ptr align 4 %c)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_184, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_185, i64 16, i1 false)
  %45 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_184, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %45, ptr align 8 %_187, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_181, ptr align 8 @alloc_d730f16ffbc7d567401bf9998be66c73, ptr align 8 %_184)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_181)
  br label %bb94

bb86:                                             ; preds = %bb81
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_176, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_178, ptr align 4 %c)
  %46 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_175, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %46, ptr align 8 %_176, i64 16, i1 false)
  %47 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_175, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %47, ptr align 8 %_178, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_172, ptr align 8 @alloc_3aae30c3010ac8a9c22ba8255cc9bb82, ptr align 8 %_175)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_172)
  br label %bb94

bb94:                                             ; preds = %bb86, %bb90
  %48 = load i32, ptr %b, align 4
  %49 = load i32, ptr %a, align 4
  %_189 = icmp sle i32 %48, %49
  br i1 %_189, label %bb95, label %bb99

bb99:                                             ; preds = %bb94
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_204, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_206, ptr align 4 %a)
  %50 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_203, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %50, ptr align 8 %_204, i64 16, i1 false)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_203, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_206, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_200, ptr align 8 @alloc_d730f16ffbc7d567401bf9998be66c73, ptr align 8 %_203)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_200)
  br label %bb103

bb95:                                             ; preds = %bb94
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_195, ptr align 4 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_197, ptr align 4 %a)
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_194, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_195, i64 16, i1 false)
  %53 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_194, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %53, ptr align 8 %_197, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_191, ptr align 8 @alloc_3aae30c3010ac8a9c22ba8255cc9bb82, ptr align 8 %_194)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_191)
  br label %bb103

bb103:                                            ; preds = %bb95, %bb99
  %54 = load i32, ptr %a, align 4
  %55 = load i32, ptr %b, align 4
  %_208 = icmp sle i32 %54, %55
  br i1 %_208, label %bb104, label %bb108

bb108:                                            ; preds = %bb103
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_223, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_225, ptr align 4 %b)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_222, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_223, i64 16, i1 false)
  %57 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_222, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %57, ptr align 8 %_225, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_219, ptr align 8 @alloc_d730f16ffbc7d567401bf9998be66c73, ptr align 8 %_222)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_219)
  br label %bb112

bb104:                                            ; preds = %bb103
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_214, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_216, ptr align 4 %b)
  %58 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %58, ptr align 8 %_214, i64 16, i1 false)
  %59 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_213, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %59, ptr align 8 %_216, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_210, ptr align 8 @alloc_3aae30c3010ac8a9c22ba8255cc9bb82, ptr align 8 %_213)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_210)
  br label %bb112

bb112:                                            ; preds = %bb104, %bb108
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_228, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_228)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_231, ptr align 8 @alloc_32f2e3b84dbf8d7cf3d674b7a1ff5ad8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_231)
  %60 = load i32, ptr %a, align 4
  %61 = load i32, ptr %c, align 4
  %_233 = icmp eq i32 %60, %61
  br i1 %_233, label %bb117, label %bb121

bb121:                                            ; preds = %bb112
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_248, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_250, ptr align 4 %c)
  %62 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %62, ptr align 8 %_248, i64 16, i1 false)
  %63 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %63, ptr align 8 %_250, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_244, ptr align 8 @alloc_219841f71ca5342dd2504727bc487bc5, ptr align 8 %_247)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_244)
  br label %bb125

bb117:                                            ; preds = %bb112
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_239, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_241, ptr align 4 %c)
  %64 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_238, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %64, ptr align 8 %_239, i64 16, i1 false)
  %65 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_238, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %65, ptr align 8 %_241, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_235, ptr align 8 @alloc_a3ec9e620430b2be55da89adbad333b2, ptr align 8 %_238)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_235)
  br label %bb125

bb125:                                            ; preds = %bb117, %bb121
  %66 = load i32, ptr %a, align 4
  %67 = load i32, ptr %b, align 4
  %_252 = icmp eq i32 %66, %67
  br i1 %_252, label %bb126, label %bb130

bb130:                                            ; preds = %bb125
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_267, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_269, ptr align 4 %b)
  %68 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_266, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %68, ptr align 8 %_267, i64 16, i1 false)
  %69 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_266, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %69, ptr align 8 %_269, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_263, ptr align 8 @alloc_219841f71ca5342dd2504727bc487bc5, ptr align 8 %_266)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_263)
  br label %bb134

bb126:                                            ; preds = %bb125
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_258, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_260, ptr align 4 %b)
  %70 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_257, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %70, ptr align 8 %_258, i64 16, i1 false)
  %71 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_257, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %71, ptr align 8 %_260, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_254, ptr align 8 @alloc_a3ec9e620430b2be55da89adbad333b2, ptr align 8 %_257)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_254)
  br label %bb134

bb134:                                            ; preds = %bb126, %bb130
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_272, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_272)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_275, ptr align 8 @alloc_732fc984de884acb26178538d89ac2a1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_275)
  %72 = load i32, ptr %a, align 4
  %73 = load i32, ptr %b, align 4
  %_277 = icmp ne i32 %72, %73
  br i1 %_277, label %bb139, label %bb143

bb143:                                            ; preds = %bb134
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_292, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_294, ptr align 4 %b)
  %74 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_291, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %74, ptr align 8 %_292, i64 16, i1 false)
  %75 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_291, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %75, ptr align 8 %_294, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_288, ptr align 8 @alloc_9accddbd48f6dc302d0351ffdc6bd58b, ptr align 8 %_291)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_288)
  br label %bb147

bb139:                                            ; preds = %bb134
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_283, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_285, ptr align 4 %b)
  %76 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_282, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %76, ptr align 8 %_283, i64 16, i1 false)
  %77 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_282, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %77, ptr align 8 %_285, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_279, ptr align 8 @alloc_b04174623dccbde1ff56a4f2df4517cf, ptr align 8 %_282)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_279)
  br label %bb147

bb147:                                            ; preds = %bb139, %bb143
  %78 = load i32, ptr %a, align 4
  %79 = load i32, ptr %c, align 4
  %_296 = icmp ne i32 %78, %79
  br i1 %_296, label %bb148, label %bb152

bb152:                                            ; preds = %bb147
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_311, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_313, ptr align 4 %c)
  %80 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_310, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %80, ptr align 8 %_311, i64 16, i1 false)
  %81 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_310, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %81, ptr align 8 %_313, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_307, ptr align 8 @alloc_9accddbd48f6dc302d0351ffdc6bd58b, ptr align 8 %_310)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_307)
  br label %bb156

bb148:                                            ; preds = %bb147
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_302, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h44530ab321235b5fE(ptr sret([16 x i8]) align 8 %_304, ptr align 4 %c)
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_301, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_302, i64 16, i1 false)
  %83 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_301, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %83, ptr align 8 %_304, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h5ca4677936bc1abfE(ptr sret([48 x i8]) align 8 %_298, ptr align 8 @alloc_b04174623dccbde1ff56a4f2df4517cf, ptr align 8 %_301)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_298)
  br label %bb156

bb156:                                            ; preds = %bb148, %bb152
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_316, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_316)
  store ptr @alloc_b468ef00fa5916b1335583d25c8252ad, ptr %name1, align 8
  %84 = getelementptr inbounds i8, ptr %name1, i64 8
  store i64 5, ptr %84, align 8
  store ptr @alloc_a118b2b99663e794e1f4a92820d83446, ptr %name2, align 8
  %85 = getelementptr inbounds i8, ptr %name2, i64 8
  store i64 3, ptr %85, align 8
  store ptr @alloc_b468ef00fa5916b1335583d25c8252ad, ptr %name3, align 8
  %86 = getelementptr inbounds i8, ptr %name3, i64 8
  store i64 5, ptr %86, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_322, ptr align 8 @alloc_c9c3b3f6c5255219bcf29567ca56c3e5)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_322)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_325, ptr align 8 @alloc_e22e67e860ad8638af95d248d09aad48)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_325)
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::eq
  %_327 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2eq17h3243b3e7cdbe163aE"(ptr align 8 %name1, ptr align 8 %name3)
  br i1 %_327, label %bb164, label %bb166

bb166:                                            ; preds = %bb156
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_334, ptr align 8 @alloc_ed3bd2f5f0555dea9196068f53ba5afb)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_334)
  br label %bb168

bb164:                                            ; preds = %bb156
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_331, ptr align 8 @alloc_a9247dc6d8a08e9f5e55e24b14bb1ab9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_331)
  br label %bb168

bb168:                                            ; preds = %bb164, %bb166
; call core::cmp::impls::<impl core::cmp::PartialEq<&B> for &A>::ne
  %_336 = call zeroext i1 @"_ZN4core3cmp5impls69_$LT$impl$u20$core..cmp..PartialEq$LT$$RF$B$GT$$u20$for$u20$$RF$A$GT$2ne17h946bbdee7235e12cE"(ptr align 8 %name1, ptr align 8 %name2)
  br i1 %_336, label %bb170, label %bb172

bb172:                                            ; preds = %bb168
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_343, ptr align 8 @alloc_9bef02e1c9b4619e086284f65e1b2a53)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_343)
  br label %bb174

bb170:                                            ; preds = %bb168
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_340, ptr align 8 @alloc_ff942cb3b964db93e96e8cb43d123019)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_340)
  br label %bb174

bb174:                                            ; preds = %bb170, %bb172
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_346, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_346)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_350, ptr align 8 @alloc_81ae713f5877fe6d78cddf1ddc025410)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_350)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_353, ptr align 8 @alloc_b16a2882c18c4fdbe1ee279c809c76a9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_353)
  br label %bb181

bb181:                                            ; preds = %bb174
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_358, ptr align 8 @alloc_38534158e99fb328b1bfa55296ee49ee)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_358)
  br label %bb185

bb183:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_361, ptr align 8 @alloc_4933f8bd3fb81b8a4328d089f8b7f860)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_361)
  br label %bb185

bb185:                                            ; preds = %bb181, %bb183
  br label %bb186

bb186:                                            ; preds = %bb185
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_366, ptr align 8 @alloc_b3c2043b836d7896e026a1c893db82a9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_366)
  br label %bb190

bb188:                                            ; No predecessors!
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_369, ptr align 8 @alloc_eec862bf8f1ff7d51c48978d943e237d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_369)
  br label %bb190

bb190:                                            ; preds = %bb186, %bb188
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_372, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_372)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_377, ptr align 8 @alloc_0db9d9b454a38aadd91a9fb4e31d4f5b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_377)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_380, ptr align 8 @alloc_70be47fc514a54d9ae04d3381995593e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_380)
  br label %bb197

bb197:                                            ; preds = %bb190
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_384, ptr align 8 @alloc_cef4553833a0d4ada65b9a1d6be809a7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_384)
  br label %bb199

bb199:                                            ; preds = %bb197
; call core::f32::<impl f32>::abs
  %_387 = call float @"_ZN4core3f3221_$LT$impl$u20$f32$GT$3abs17h871d375d49a27b82E"(float 0.000000e+00)
  %_386 = fcmp olt float %_387, 0x3E80000000000000
  br i1 %_386, label %bb201, label %bb203

bb203:                                            ; preds = %bb201, %bb199
  br label %bb204

bb201:                                            ; preds = %bb199
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_391, ptr align 8 @alloc_a0b79a941a76204a15d9c8e7304a4fd0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_391)
  br label %bb203

bb204:                                            ; preds = %bb203
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_395, ptr align 8 @alloc_53df38fdfd218ac7c2ac8d036f516b95)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_395)
  br label %bb206

bb206:                                            ; preds = %bb204
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_398, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_398)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_401, ptr align 8 @alloc_f4906823c19b697bd029dfac67aadb02)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_401)
  %87 = load i32, ptr %a, align 4
  %88 = load i32, ptr %b, align 4
  %89 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %87, i32 %88)
  %_405.0 = extractvalue { i32, i1 } %89, 0
  %_405.1 = extractvalue { i32, i1 } %89, 1
  br i1 %_405.1, label %panic, label %bb211

bb211:                                            ; preds = %bb206
  %90 = load i32, ptr %c, align 4
  %91 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %90, i32 2)
  %_407.0 = extractvalue { i32, i1 } %91, 0
  %_407.1 = extractvalue { i32, i1 } %91, 1
  br i1 %_407.1, label %panic1, label %bb212

panic:                                            ; preds = %bb206
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_0df0fc388ea26fde28a7ac7e9c7b996c) #7
  unreachable

bb212:                                            ; preds = %bb211
  %_403 = icmp sgt i32 %_405.0, %_407.0
  br i1 %_403, label %bb213, label %bb215

panic1:                                           ; preds = %bb211
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_0f49c5132e8dc8a73710d43f3a347460) #7
  unreachable

bb215:                                            ; preds = %bb212
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_412, ptr align 8 @alloc_7cd9c9d8865358b7a6bcaf42c35711b0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_412)
  br label %bb217

bb213:                                            ; preds = %bb212
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_409, ptr align 8 @alloc_9fc147be84f93bb5c148984cb47fcc50)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_409)
  br label %bb217

bb217:                                            ; preds = %bb213, %bb215
  %92 = load i32, ptr %a, align 4
  %93 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %92, i32 2)
  %_416.0 = extractvalue { i32, i1 } %93, 0
  %_416.1 = extractvalue { i32, i1 } %93, 1
  br i1 %_416.1, label %panic2, label %bb218

bb218:                                            ; preds = %bb217
  %94 = load i32, ptr %c, align 4
  %95 = load i32, ptr %a, align 4
  %96 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %94, i32 %95)
  %_418.0 = extractvalue { i32, i1 } %96, 0
  %_418.1 = extractvalue { i32, i1 } %96, 1
  br i1 %_418.1, label %panic3, label %bb219

panic2:                                           ; preds = %bb217
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_0ab1546e0068083f065039f394d74e5c) #7
  unreachable

bb219:                                            ; preds = %bb218
  %_414 = icmp eq i32 %_416.0, %_418.0
  br i1 %_414, label %bb220, label %bb222

panic3:                                           ; preds = %bb218
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_cba5c27c47119a53b217726ef7304c2d) #7
  unreachable

bb222:                                            ; preds = %bb219
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_423, ptr align 8 @alloc_c99a803bf9d75c8e48634e0d6f21a5de)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_423)
  br label %bb224

bb220:                                            ; preds = %bb219
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_420, ptr align 8 @alloc_a7f5c7f833e449233e34f46d59debc31)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_420)
  br label %bb224

bb224:                                            ; preds = %bb220, %bb222
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_426, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_426)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h7151db40fc683ab6E(ptr sret([48 x i8]) align 8 %_429, ptr align 8 @alloc_598b11318113cb8adbe10f5974d54fd6)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_429)
  ret void
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare float @llvm.fabs.f32(float) #3

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #4

declare i32 @__CxxFrameHandler3(...) unnamed_addr #5

declare i32 @memcmp(ptr, ptr, i64)

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #3

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #3

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #6

define i32 @main(i32 %0, ptr %1) unnamed_addr #5 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h75288183b87c09c5E(ptr @_ZN25_03_comparison_operations4main17hf115dce921cb2e11E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #4 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #5 = { "target-cpu"="x86-64" }
attributes #6 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #7 = { noreturn }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 10105959265593134}
