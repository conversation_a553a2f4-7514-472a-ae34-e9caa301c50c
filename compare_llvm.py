#!/usr/bin/env python3
"""
LLVM IR Comparison Tool
Compares LLVM IR generated from Dolet vs Rust for the same functionality
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def compile_dolet_to_llvm(dolet_file, output_file):
    """Compile Dolet file to LLVM IR"""
    print(f"🐍 Compiling Dolet: {dolet_file}")
    cmd = f"python dolet_to_llvm.py {dolet_file} {output_file}"
    success, stdout, stderr = run_command(cmd)
    
    if success:
        print(f"✅ Dolet compilation successful: {output_file}")
        return True
    else:
        print(f"❌ Dolet compilation failed:")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return False

def compile_rust_to_llvm(rust_file, output_file):
    """Compile Rust file to LLVM IR"""
    print(f"🦀 Compiling Rust: {rust_file}")
    cmd = f"rustc --emit=llvm-ir -O {rust_file} -o {output_file}"
    success, stdout, stderr = run_command(cmd)
    
    if success:
        print(f"✅ Rust compilation successful: {output_file}")
        return True
    else:
        print(f"❌ Rust compilation failed:")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return False

def analyze_llvm_ir(file_path):
    """Analyze LLVM IR file and extract key metrics"""
    if not os.path.exists(file_path):
        return None
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    metrics = {
        'total_lines': len(lines),
        'functions': len([line for line in lines if line.strip().startswith('define ')]),
        'basic_blocks': len([line for line in lines if line.strip().endswith(':') and not line.strip().startswith(';')]),
        'instructions': len([line for line in lines if line.strip() and not line.strip().startswith(';') and not line.strip().startswith('define') and not line.strip().endswith(':')]),
        'allocas': len([line for line in lines if 'alloca' in line]),
        'stores': len([line for line in lines if 'store' in line]),
        'loads': len([line for line in lines if 'load' in line]),
        'calls': len([line for line in lines if 'call' in line]),
        'branches': len([line for line in lines if 'br ' in line]),
        'returns': len([line for line in lines if 'ret ' in line]),
    }
    
    return metrics

def compare_files(example_num):
    """Compare Dolet and Rust compilation for a specific example"""
    print(f"\n{'='*60}")
    print(f"📊 COMPARING EXAMPLE {example_num:02d}")
    print(f"{'='*60}")
    
    # File paths
    dolet_file = f"{example_num:02d}_*.dolet"
    rust_file = f"rust_examples/{example_num:02d}_*.rs"
    
    # Find actual files
    dolet_files = list(Path('.').glob(dolet_file))
    rust_files = list(Path('.').glob(rust_file))
    
    if not dolet_files:
        print(f"❌ No Dolet file found for example {example_num}")
        return
    
    if not rust_files:
        print(f"❌ No Rust file found for example {example_num}")
        return
    
    dolet_path = str(dolet_files[0])
    rust_path = str(rust_files[0])
    
    print(f"Dolet file: {dolet_path}")
    print(f"Rust file: {rust_path}")
    
    # Output files
    dolet_ll = f"output/dolet_{example_num:02d}.ll"
    rust_ll = f"output/rust_{example_num:02d}.ll"
    
    # Create output directory
    os.makedirs("output", exist_ok=True)
    
    # Compile both
    dolet_success = compile_dolet_to_llvm(dolet_path, dolet_ll)
    rust_success = compile_rust_to_llvm(rust_path, rust_ll)
    
    if not dolet_success or not rust_success:
        print("❌ Compilation failed, skipping analysis")
        return
    
    # Analyze LLVM IR
    dolet_metrics = analyze_llvm_ir(dolet_ll)
    rust_metrics = analyze_llvm_ir(rust_ll)
    
    if not dolet_metrics or not rust_metrics:
        print("❌ Analysis failed")
        return
    
    # Print comparison
    print(f"\n📈 LLVM IR COMPARISON:")
    print(f"{'Metric':<15} {'Dolet':<10} {'Rust':<10} {'Difference':<12}")
    print("-" * 50)
    
    for metric in dolet_metrics:
        dolet_val = dolet_metrics[metric]
        rust_val = rust_metrics[metric]
        diff = dolet_val - rust_val
        diff_str = f"{diff:+d}" if diff != 0 else "0"
        print(f"{metric:<15} {dolet_val:<10} {rust_val:<10} {diff_str:<12}")
    
    # Calculate efficiency ratios
    if rust_metrics['total_lines'] > 0:
        efficiency = (dolet_metrics['total_lines'] / rust_metrics['total_lines']) * 100
        print(f"\n📊 Dolet IR size: {efficiency:.1f}% of Rust IR size")
    
    print(f"\n💾 Generated files:")
    print(f"  Dolet LLVM IR: {dolet_ll}")
    print(f"  Rust LLVM IR: {rust_ll}")

def main():
    print("🔍 DOLET vs RUST LLVM IR COMPARISON TOOL")
    print("=" * 50)
    
    # Check if rustc is available
    success, _, _ = run_command("rustc --version")
    if not success:
        print("❌ rustc not found. Please install Rust compiler.")
        sys.exit(1)
    
    # Check if dolet compiler exists
    if not os.path.exists("dolet_to_llvm.py"):
        print("❌ dolet_to_llvm.py not found. Please run from the correct directory.")
        sys.exit(1)
    
    # Get examples to compare
    if len(sys.argv) > 1:
        try:
            example_nums = [int(arg) for arg in sys.argv[1:]]
        except ValueError:
            print("❌ Invalid example numbers. Use integers only.")
            sys.exit(1)
    else:
        # Compare first 5 examples by default
        example_nums = [1, 2, 3, 4, 5]
    
    print(f"Comparing examples: {example_nums}")
    
    # Compare each example
    for num in example_nums:
        try:
            compare_files(num)
        except Exception as e:
            print(f"❌ Error comparing example {num}: {e}")
    
    print(f"\n🎉 Comparison complete!")
    print(f"Check the 'output/' directory for generated LLVM IR files.")

if __name__ == "__main__":
    main()
