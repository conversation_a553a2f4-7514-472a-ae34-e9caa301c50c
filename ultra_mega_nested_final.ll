; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 372 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)
; Cross-platform console UTF-8 support
declare i32 @SetConsoleOutputCP(i32)  ; Windows API - will be ignored on Linux

; String constants
@.str_0 = private unnamed_addr constant [7 x i8] c"Ahmed\0A\00", align 1
@.str_1 = private unnamed_addr constant [7 x i8] c"Admin\0A\00", align 1
@.str_2 = private unnamed_addr constant [7 x i8] c"Guest\0A\00", align 1
@.str_3 = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@.str_4 = private unnamed_addr constant [49 x i8] c"This is a very long string for testing purposes\0A\00", align 1
@.str_5 = private unnamed_addr constant [6 x i8] c"\F0\9F\8E\89\0A\00", align 1
@.str_6 = private unnamed_addr constant [6 x i8] c"\F0\9F\9A\80\0A\00", align 1
@.str_7 = private unnamed_addr constant [6 x i8] c"\F0\9F\8E\89\0A\00", align 1
@.str_8 = private unnamed_addr constant [12 x i8] c"\D9\85\D8\B1\D8\AD\D8\A8\D8\A7\0A\00", align 1
@.str_9 = private unnamed_addr constant [7 x i8] c"Hello\0A\00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_score = global i32 0, align 4
@global_maxScore = global i32 0, align 4
@global_minScore = global i32 0, align 4
@global_passingGrade = global i32 0, align 4
@global_perfectScore = global i32 0, align 4
@global_pi = global float 0.0, align 4
@global_e = global float 0.0, align 4
@global_goldenRatio = global float 0.0, align 4
@global_zero = global float 0.0, align 4
@global_negativeOne = global float 0.0, align 4
@global_playerName = global i8* null, align 8
@global_adminName = global i8* null, align 8
@global_guestName = global i8* null, align 8
@global_emptyString = global i8* getelementptr inbounds ([1 x i8], [1 x i8]* @empty_string, i64 0, i64 0), align 8
@global_longString = global i8* null, align 8
@global_isActive = global i1 0, align 1
@global_isExpired = global i1 0, align 1
@global_hasPermission = global i1 0, align 1
@global_isGuest = global i1 0, align 1
@global_debugMode = global i1 0, align 1
@global_fib1 = global i32 0, align 4
@global_fib2 = global i32 0, align 4
@global_fib3 = global i32 0, align 4
@global_fib4 = global i32 0, align 4
@global_fib5 = global i32 0, align 4
@global_prime1 = global i32 0, align 4
@global_prime2 = global i32 0, align 4
@global_prime3 = global i32 0, align 4
@global_prime4 = global i32 0, align 4
@global_emoji1 = global i8* null, align 8
@global_emoji2 = global i8* null, align 8
@global_emoji3 = global i8* null, align 8
@global_arabic = global i8* null, align 8
@global_english = global i8* null, align 8
@global_arr1 = global i32 0, align 4
@global_arr2 = global i32 0, align 4
@global_arr3 = global i32 0, align 4
@global_arr4 = global i32 0, align 4
@global_arr5 = global i32 0, align 4
@global_large1 = global float 0.0, align 4
@global_large2 = global float 0.0, align 4
@global_small1 = global float 0.0, align 4
@global_small2 = global float 0.0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Cross-platform UTF-8 console setup
  ; This will work on Windows and be ignored on Linux
  %1 = call i32 @SetConsoleOutputCP(i32 65001)
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "🎯 === ULTIMATE COMPARISON CHALLENGE === 🎯" (inline)
  %tmp_1_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\AF === ULTIMATE COMPARISON CHALLENGE === \F0\9F\8E\AF\00", [48 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [48 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 Setting up test variables..." (inline)
  %tmp_3_str = alloca [34 x i8], align 1
  store [34 x i8] c"\F0\9F\93\8A Setting up test variables...\00", [34 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [34 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int score = 85
  store i32 85, i32* @global_score, align 4
  ; int maxScore = 100
  store i32 100, i32* @global_maxScore, align 4
  ; int minScore = 0
  store i32 0, i32* @global_minScore, align 4
  ; int passingGrade = 60
  store i32 60, i32* @global_passingGrade, align 4
  ; int perfectScore = 100
  store i32 100, i32* @global_perfectScore, align 4
  ; float pi = 3.14159
  %tmp_5 = bitcast i32 1078530000 to float
  store float %tmp_5, float* @global_pi, align 4
  ; float e = 2.71828
  %tmp_6 = bitcast i32 1076754509 to float
  store float %tmp_6, float* @global_e, align 4
  ; float goldenRatio = 1.61803
  %tmp_7 = bitcast i32 1070537627 to float
  store float %tmp_7, float* @global_goldenRatio, align 4
  ; float zero = 0.0
  %tmp_8 = bitcast i32 0 to float
  store float %tmp_8, float* @global_zero, align 4
  ; float negativeOne = -1.0
  %tmp_9 = bitcast i32 3212836864 to float
  store float %tmp_9, float* @global_negativeOne, align 4
  ; string playerName = "Ahmed"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_0, i64 0, i64 0), i8** @global_playerName, align 8
  ; string adminName = "Admin"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_1, i64 0, i64 0), i8** @global_adminName, align 8
  ; string guestName = "Guest"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_2, i64 0, i64 0), i8** @global_guestName, align 8
  ; string emptyString = ""
  store i8* getelementptr inbounds ([1 x i8], [1 x i8]* @.str_3, i64 0, i64 0), i8** @global_emptyString, align 8
  ; string longString = "This is a very long string for testing purposes"
  store i8* getelementptr inbounds ([48 x i8], [48 x i8]* @.str_4, i64 0, i64 0), i8** @global_longString, align 8
  ; bool isActive = true
  store i1 1, i1* @global_isActive, align 1
  ; bool isExpired = false
  store i1 0, i1* @global_isExpired, align 1
  ; bool hasPermission = true
  store i1 1, i1* @global_hasPermission, align 1
  ; bool isGuest = false
  store i1 0, i1* @global_isGuest, align 1
  ; bool debugMode = true
  store i1 1, i1* @global_debugMode, align 1
  ; print "✅ Variables initialized successfully!" (inline)
  %tmp_10_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9C\85 Variables initialized successfully!\00", [40 x i8]* %tmp_10_str, align 1
  %tmp_11 = bitcast [40 x i8]* %tmp_10_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔢 === EXTREME INTEGER COMPARISONS === 🔢" (inline)
  %tmp_12_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\94\A2 === EXTREME INTEGER COMPARISONS === \F0\9F\94\A2\00", [46 x i8]* %tmp_12_str, align 1
  %tmp_13 = bitcast [46 x i8]* %tmp_12_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score + 15) > (maxScore - 10)
  %tmp_14 = load i32, i32* @global_score, align 4
  %tmp_15 = add i32 %tmp_14, 15
  %tmp_16 = load i32, i32* @global_maxScore, align 4
  %tmp_17 = sub i32 %tmp_16, 10
  %tmp_18 = icmp sgt i32 %tmp_15, %tmp_17
  br i1 %tmp_18, label %if_true_1, label %if_else_3
if_true_1:
  ; print "🎉 PASS: Complex addition comparison works!" (inline)
  %tmp_19_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Complex addition comparison works!\00", [46 x i8]* %tmp_19_str, align 1
  %tmp_20 = bitcast [46 x i8]* %tmp_19_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_else_3:
  ; print "❌ FAIL: Complex addition comparison failed!" (inline)
  %tmp_21_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Complex addition comparison failed!\00", [46 x i8]* %tmp_21_str, align 1
  %tmp_22 = bitcast [46 x i8]* %tmp_21_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_22)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_2
if_end_2:
  ; if (score * 2) >= (maxScore + passingGrade)
  %tmp_23 = load i32, i32* @global_score, align 4
  %tmp_24 = mul i32 %tmp_23, 2
  %tmp_25 = load i32, i32* @global_maxScore, align 4
  %tmp_26 = load i32, i32* @global_passingGrade, align 4
  %tmp_27 = add i32 %tmp_25, %tmp_26
  %tmp_28 = icmp sge i32 %tmp_24, %tmp_27
  br i1 %tmp_28, label %if_true_4, label %if_else_6
if_true_4:
  ; print "🎉 PASS: Complex multiplication comparison works!" (inline)
  %tmp_29_str = alloca [52 x i8], align 1
  store [52 x i8] c"\F0\9F\8E\89 PASS: Complex multiplication comparison works!\00", [52 x i8]* %tmp_29_str, align 1
  %tmp_30 = bitcast [52 x i8]* %tmp_29_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_else_6:
  ; print "❌ FAIL: Complex multiplication comparison failed!" (inline)
  %tmp_31_str = alloca [52 x i8], align 1
  store [52 x i8] c"\E2\9D\8C FAIL: Complex multiplication comparison failed!\00", [52 x i8]* %tmp_31_str, align 1
  %tmp_32 = bitcast [52 x i8]* %tmp_31_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_32)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_5
if_end_5:
  ; if ((score + passingGrade) / 2) > ((maxScore - minScore) / 3)
  %tmp_33 = load i32, i32* @global_score, align 4
  %tmp_34 = load i32, i32* @global_passingGrade, align 4
  %tmp_35 = add i32 %tmp_33, %tmp_34
  %tmp_36 = sdiv i32 %tmp_35, 2
  %tmp_37 = load i32, i32* @global_maxScore, align 4
  %tmp_38 = load i32, i32* @global_minScore, align 4
  %tmp_39 = sub i32 %tmp_37, %tmp_38
  %tmp_40 = sdiv i32 %tmp_39, 3
  %tmp_41 = icmp sgt i32 %tmp_36, %tmp_40
  br i1 %tmp_41, label %if_true_7, label %if_else_9
if_true_7:
  ; print "🎉 PASS: Triple nested division comparison works!" (inline)
  %tmp_42_str = alloca [52 x i8], align 1
  store [52 x i8] c"\F0\9F\8E\89 PASS: Triple nested division comparison works!\00", [52 x i8]* %tmp_42_str, align 1
  %tmp_43 = bitcast [52 x i8]* %tmp_42_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_else_9:
  ; print "❌ FAIL: Triple nested division comparison failed!" (inline)
  %tmp_44_str = alloca [52 x i8], align 1
  store [52 x i8] c"\E2\9D\8C FAIL: Triple nested division comparison failed!\00", [52 x i8]* %tmp_44_str, align 1
  %tmp_45 = bitcast [52 x i8]* %tmp_44_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_45)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_8
if_end_8:
  ; if (((score * 2) + (passingGrade * 3)) - (minScore * 5)) > ((maxScore * 2) + (perfectScore / 4))
  %tmp_46 = load i32, i32* @global_score, align 4
  %tmp_47 = mul i32 %tmp_46, 2
  %tmp_48 = load i32, i32* @global_passingGrade, align 4
  %tmp_49 = mul i32 %tmp_48, 3
  %tmp_50 = add i32 %tmp_47, %tmp_49
  %tmp_51 = load i32, i32* @global_minScore, align 4
  %tmp_52 = mul i32 %tmp_51, 5
  %tmp_53 = sub i32 %tmp_50, %tmp_52
  %tmp_54 = load i32, i32* @global_maxScore, align 4
  %tmp_55 = mul i32 %tmp_54, 2
  %tmp_56 = load i32, i32* @global_perfectScore, align 4
  %tmp_57 = sdiv i32 %tmp_56, 4
  %tmp_58 = add i32 %tmp_55, %tmp_57
  %tmp_59 = icmp sgt i32 %tmp_53, %tmp_58
  br i1 %tmp_59, label %if_true_10, label %if_else_12
if_true_10:
  ; print "🎉 PASS: Extreme expression comparison works!" (inline)
  %tmp_60_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\89 PASS: Extreme expression comparison works!\00", [48 x i8]* %tmp_60_str, align 1
  %tmp_61 = bitcast [48 x i8]* %tmp_60_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_61)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_else_12:
  ; print "❌ FAIL: Extreme expression comparison failed!" (inline)
  %tmp_62_str = alloca [48 x i8], align 1
  store [48 x i8] c"\E2\9D\8C FAIL: Extreme expression comparison failed!\00", [48 x i8]* %tmp_62_str, align 1
  %tmp_63 = bitcast [48 x i8]* %tmp_62_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_63)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_11
if_end_11:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌊 === FLOATING POINT PRECISION TESTS === 🌊" (inline)
  %tmp_64_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\8C\8A === FLOATING POINT PRECISION TESTS === \F0\9F\8C\8A\00", [49 x i8]* %tmp_64_str, align 1
  %tmp_65 = bitcast [49 x i8]* %tmp_64_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_65)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if pi > e
  %tmp_66 = load float, float* @global_pi, align 4
  %tmp_67 = load float, float* @global_e, align 4
  %tmp_68 = fcmp ogt float %tmp_66, %tmp_67
  br i1 %tmp_68, label %if_true_13, label %if_else_15
if_true_13:
  ; print "🎉 PASS: π > e comparison works!" (inline)
  %tmp_69_str = alloca [36 x i8], align 1
  store [36 x i8] c"\F0\9F\8E\89 PASS: \CF\80 > e comparison works!\00", [36 x i8]* %tmp_69_str, align 1
  %tmp_70 = bitcast [36 x i8]* %tmp_69_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_70)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_else_15:
  ; print "❌ FAIL: π > e comparison failed!" (inline)
  %tmp_71_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9D\8C FAIL: \CF\80 > e comparison failed!\00", [36 x i8]* %tmp_71_str, align 1
  %tmp_72 = bitcast [36 x i8]* %tmp_71_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_72)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_14
if_end_14:
  ; if (pi * e) > (goldenRatio * 5)
  %tmp_73 = load float, float* @global_pi, align 4
  %tmp_74 = load float, float* @global_e, align 4
  %tmp_75 = fmul float %tmp_73, %tmp_74
  %tmp_76 = load float, float* @global_goldenRatio, align 4
  %tmp_77 = sitofp i32 5 to float
  %tmp_78 = fmul float %tmp_76, %tmp_77
  %tmp_79 = fcmp ogt float %tmp_75, %tmp_78
  br i1 %tmp_79, label %if_true_16, label %if_else_18
if_true_16:
  ; print "🎉 PASS: Complex float multiplication works!" (inline)
  %tmp_80_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\8E\89 PASS: Complex float multiplication works!\00", [47 x i8]* %tmp_80_str, align 1
  %tmp_81 = bitcast [47 x i8]* %tmp_80_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_81)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_else_18:
  ; print "❌ FAIL: Complex float multiplication failed!" (inline)
  %tmp_82_str = alloca [47 x i8], align 1
  store [47 x i8] c"\E2\9D\8C FAIL: Complex float multiplication failed!\00", [47 x i8]* %tmp_82_str, align 1
  %tmp_83 = bitcast [47 x i8]* %tmp_82_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_83)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_17
if_end_17:
  ; if (pi + e + goldenRatio) >= (zero + 7.4)
  %tmp_84 = load float, float* @global_pi, align 4
  %tmp_85 = load float, float* @global_e, align 4
  %tmp_86 = fadd float %tmp_84, %tmp_85
  %tmp_87 = load float, float* @global_goldenRatio, align 4
  %tmp_88 = fadd float %tmp_86, %tmp_87
  %tmp_89 = load float, float* @global_zero, align 4
  %tmp_90 = bitcast i32 1089260749 to float
  %tmp_91 = fadd float %tmp_89, %tmp_90
  %tmp_92 = fcmp oge float %tmp_88, %tmp_91
  br i1 %tmp_92, label %if_true_19, label %if_else_21
if_true_19:
  ; print "🎉 PASS: Multi-float addition comparison works!" (inline)
  %tmp_93_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Multi-float addition comparison works!\00", [50 x i8]* %tmp_93_str, align 1
  %tmp_94 = bitcast [50 x i8]* %tmp_93_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_else_21:
  ; print "❌ FAIL: Multi-float addition comparison failed!" (inline)
  %tmp_95_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Multi-float addition comparison failed!\00", [50 x i8]* %tmp_95_str, align 1
  %tmp_96 = bitcast [50 x i8]* %tmp_95_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_96)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_20
if_end_20:
  ; if negativeOne < zero
  %tmp_97 = load float, float* @global_negativeOne, align 4
  %tmp_98 = load float, float* @global_zero, align 4
  %tmp_99 = fcmp olt float %tmp_97, %tmp_98
  br i1 %tmp_99, label %if_true_22, label %if_else_24
if_true_22:
  ; print "🎉 PASS: Negative number comparison works!" (inline)
  %tmp_100_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Negative number comparison works!\00", [45 x i8]* %tmp_100_str, align 1
  %tmp_101 = bitcast [45 x i8]* %tmp_100_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_else_24:
  ; print "❌ FAIL: Negative number comparison failed!" (inline)
  %tmp_102_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Negative number comparison failed!\00", [45 x i8]* %tmp_102_str, align 1
  %tmp_103 = bitcast [45 x i8]* %tmp_102_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_103)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_23
if_end_23:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📝 === STRING COMPARISON MADNESS === 📝" (inline)
  %tmp_104_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\93\9D === STRING COMPARISON MADNESS === \F0\9F\93\9D\00", [44 x i8]* %tmp_104_str, align 1
  %tmp_105 = bitcast [44 x i8]* %tmp_104_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_105)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if playerName == "Ahmed"
  %tmp_106 = load i8*, i8** @global_playerName, align 8
  %tmp_108 = getelementptr inbounds [6 x i8], [6 x i8]* @str_cond_106, i64 0, i64 0
  %tmp_110 = call i32 @strcmp(i8* %tmp_106, i8* %tmp_108)
  %tmp_109 = icmp eq i32 %tmp_110, 0
  br i1 %tmp_109, label %if_true_25, label %if_else_27
if_true_25:
  ; print "🎉 PASS: Basic string equality works!" (inline)
  %tmp_111_str = alloca [40 x i8], align 1
  store [40 x i8] c"\F0\9F\8E\89 PASS: Basic string equality works!\00", [40 x i8]* %tmp_111_str, align 1
  %tmp_112 = bitcast [40 x i8]* %tmp_111_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_112)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_else_27:
  ; print "❌ FAIL: Basic string equality failed!" (inline)
  %tmp_113_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9D\8C FAIL: Basic string equality failed!\00", [40 x i8]* %tmp_113_str, align 1
  %tmp_114 = bitcast [40 x i8]* %tmp_113_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_114)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_26
if_end_26:
  ; if playerName != adminName
  %tmp_115 = load i8*, i8** @global_playerName, align 8
  %tmp_116 = load i8*, i8** @global_adminName, align 8
  %tmp_118 = call i32 @strcmp(i8* %tmp_115, i8* %tmp_116)
  %tmp_117 = icmp ne i32 %tmp_118, 0
  br i1 %tmp_117, label %if_true_28, label %if_else_30
if_true_28:
  ; print "🎉 PASS: String inequality works!" (inline)
  %tmp_119_str = alloca [36 x i8], align 1
  store [36 x i8] c"\F0\9F\8E\89 PASS: String inequality works!\00", [36 x i8]* %tmp_119_str, align 1
  %tmp_120 = bitcast [36 x i8]* %tmp_119_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_120)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_else_30:
  ; print "❌ FAIL: String inequality failed!" (inline)
  %tmp_121_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9D\8C FAIL: String inequality failed!\00", [36 x i8]* %tmp_121_str, align 1
  %tmp_122 = bitcast [36 x i8]* %tmp_121_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_122)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_29
if_end_29:
  ; if emptyString == ""
  %tmp_123 = load i8*, i8** @global_emptyString, align 8
  %tmp_125 = getelementptr inbounds [1 x i8], [1 x i8]* @str_cond_123, i64 0, i64 0
  %tmp_127 = call i32 @strcmp(i8* %tmp_123, i8* %tmp_125)
  %tmp_126 = icmp eq i32 %tmp_127, 0
  br i1 %tmp_126, label %if_true_31, label %if_else_33
if_true_31:
  ; print "🎉 PASS: Empty string comparison works!" (inline)
  %tmp_128_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Empty string comparison works!\00", [42 x i8]* %tmp_128_str, align 1
  %tmp_129 = bitcast [42 x i8]* %tmp_128_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_else_33:
  ; print "❌ FAIL: Empty string comparison failed!" (inline)
  %tmp_130_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Empty string comparison failed!\00", [42 x i8]* %tmp_130_str, align 1
  %tmp_131 = bitcast [42 x i8]* %tmp_130_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_32
if_end_32:
  ; if longString != emptyString
  %tmp_132 = load i8*, i8** @global_longString, align 8
  %tmp_133 = load i8*, i8** @global_emptyString, align 8
  %tmp_135 = call i32 @strcmp(i8* %tmp_132, i8* %tmp_133)
  %tmp_134 = icmp ne i32 %tmp_135, 0
  br i1 %tmp_134, label %if_true_34, label %if_else_36
if_true_34:
  ; print "🎉 PASS: Long vs empty string comparison works!" (inline)
  %tmp_136_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Long vs empty string comparison works!\00", [50 x i8]* %tmp_136_str, align 1
  %tmp_137 = bitcast [50 x i8]* %tmp_136_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_137)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_else_36:
  ; print "❌ FAIL: Long vs empty string comparison failed!" (inline)
  %tmp_138_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Long vs empty string comparison failed!\00", [50 x i8]* %tmp_138_str, align 1
  %tmp_139 = bitcast [50 x i8]* %tmp_138_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_139)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_35
if_end_35:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔘 === BOOLEAN LOGIC EXTREMES === 🔘" (inline)
  %tmp_140_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\94\98 === BOOLEAN LOGIC EXTREMES === \F0\9F\94\98\00", [41 x i8]* %tmp_140_str, align 1
  %tmp_141 = bitcast [41 x i8]* %tmp_140_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_141)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if isActive == true
  %tmp_142 = load i1, i1* @global_isActive, align 1
  %tmp_143 = zext i1 %tmp_142 to i32
  %tmp_144 = icmp eq i32 %tmp_143, 1
  br i1 %tmp_144, label %if_true_37, label %if_else_39
if_true_37:
  ; print "🎉 PASS: Boolean true comparison works!" (inline)
  %tmp_145_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Boolean true comparison works!\00", [42 x i8]* %tmp_145_str, align 1
  %tmp_146 = bitcast [42 x i8]* %tmp_145_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_else_39:
  ; print "❌ FAIL: Boolean true comparison failed!" (inline)
  %tmp_147_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Boolean true comparison failed!\00", [42 x i8]* %tmp_147_str, align 1
  %tmp_148 = bitcast [42 x i8]* %tmp_147_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_148)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_38
if_end_38:
  ; if isExpired == false
  %tmp_149 = load i1, i1* @global_isExpired, align 1
  %tmp_150 = zext i1 %tmp_149 to i32
  %tmp_151 = icmp eq i32 %tmp_150, 0
  br i1 %tmp_151, label %if_true_40, label %if_else_42
if_true_40:
  ; print "🎉 PASS: Boolean false comparison works!" (inline)
  %tmp_152_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\89 PASS: Boolean false comparison works!\00", [43 x i8]* %tmp_152_str, align 1
  %tmp_153 = bitcast [43 x i8]* %tmp_152_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_153)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_else_42:
  ; print "❌ FAIL: Boolean false comparison failed!" (inline)
  %tmp_154_str = alloca [43 x i8], align 1
  store [43 x i8] c"\E2\9D\8C FAIL: Boolean false comparison failed!\00", [43 x i8]* %tmp_154_str, align 1
  %tmp_155 = bitcast [43 x i8]* %tmp_154_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_155)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_41
if_end_41:
  ; if hasPermission != isGuest
  %tmp_156 = load i1, i1* @global_hasPermission, align 1
  %tmp_157 = zext i1 %tmp_156 to i32
  %tmp_158 = load i1, i1* @global_isGuest, align 1
  %tmp_159 = zext i1 %tmp_158 to i32
  %tmp_160 = icmp ne i32 %tmp_157, %tmp_159
  br i1 %tmp_160, label %if_true_43, label %if_else_45
if_true_43:
  ; print "🎉 PASS: Boolean inequality works!" (inline)
  %tmp_161_str = alloca [37 x i8], align 1
  store [37 x i8] c"\F0\9F\8E\89 PASS: Boolean inequality works!\00", [37 x i8]* %tmp_161_str, align 1
  %tmp_162 = bitcast [37 x i8]* %tmp_161_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_162)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_else_45:
  ; print "❌ FAIL: Boolean inequality failed!" (inline)
  %tmp_163_str = alloca [37 x i8], align 1
  store [37 x i8] c"\E2\9D\8C FAIL: Boolean inequality failed!\00", [37 x i8]* %tmp_163_str, align 1
  %tmp_164 = bitcast [37 x i8]* %tmp_163_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_164)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_44
if_end_44:
  ; if (isActive == hasPermission) == (debugMode == true)
  %tmp_165 = icmp eq i32 1, 1  ; Invalid comparison
  br i1 %tmp_165, label %if_true_46, label %if_else_48
if_true_46:
  ; print "🎉 PASS: Complex boolean comparison works!" (inline)
  %tmp_166_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Complex boolean comparison works!\00", [45 x i8]* %tmp_166_str, align 1
  %tmp_167 = bitcast [45 x i8]* %tmp_166_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_167)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_else_48:
  ; print "❌ FAIL: Complex boolean comparison failed!" (inline)
  %tmp_168_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Complex boolean comparison failed!\00", [45 x i8]* %tmp_168_str, align 1
  %tmp_169 = bitcast [45 x i8]* %tmp_168_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_169)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_47
if_end_47:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌈 === MIXED TYPE COMPARISON CHALLENGES === 🌈" (inline)
  %tmp_170_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\8C\88 === MIXED TYPE COMPARISON CHALLENGES === \F0\9F\8C\88\00", [51 x i8]* %tmp_170_str, align 1
  %tmp_171 = bitcast [51 x i8]* %tmp_170_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_171)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > pi
  %tmp_172 = load i32, i32* @global_score, align 4
  %tmp_173 = load float, float* @global_pi, align 4
  %tmp_175 = sitofp i32 %tmp_172 to float
  %tmp_174 = fcmp ogt float %tmp_175, %tmp_173
  br i1 %tmp_174, label %if_true_49, label %if_else_51
if_true_49:
  ; print "🎉 PASS: Integer vs Float comparison works!" (inline)
  %tmp_176_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Integer vs Float comparison works!\00", [46 x i8]* %tmp_176_str, align 1
  %tmp_177 = bitcast [46 x i8]* %tmp_176_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_177)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_else_51:
  ; print "❌ FAIL: Integer vs Float comparison failed!" (inline)
  %tmp_178_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Integer vs Float comparison failed!\00", [46 x i8]* %tmp_178_str, align 1
  %tmp_179 = bitcast [46 x i8]* %tmp_178_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_179)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_50
if_end_50:
  ; if (score + 15) > (pi * 25)
  %tmp_180 = load i32, i32* @global_score, align 4
  %tmp_181 = add i32 %tmp_180, 15
  %tmp_182 = load float, float* @global_pi, align 4
  %tmp_183 = sitofp i32 25 to float
  %tmp_184 = fmul float %tmp_182, %tmp_183
  %tmp_186 = sitofp i32 %tmp_181 to float
  %tmp_185 = fcmp ogt float %tmp_186, %tmp_184
  br i1 %tmp_185, label %if_true_52, label %if_else_54
if_true_52:
  ; print "🎉 PASS: Complex mixed type comparison works!" (inline)
  %tmp_187_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8E\89 PASS: Complex mixed type comparison works!\00", [48 x i8]* %tmp_187_str, align 1
  %tmp_188 = bitcast [48 x i8]* %tmp_187_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_188)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_else_54:
  ; print "❌ FAIL: Complex mixed type comparison failed!" (inline)
  %tmp_189_str = alloca [48 x i8], align 1
  store [48 x i8] c"\E2\9D\8C FAIL: Complex mixed type comparison failed!\00", [48 x i8]* %tmp_189_str, align 1
  %tmp_190 = bitcast [48 x i8]* %tmp_189_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_190)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_53
if_end_53:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "⚡ === EDGE CASE SCENARIOS === ⚡" (inline)
  %tmp_191_str = alloca [36 x i8], align 1
  store [36 x i8] c"\E2\9A\A1 === EDGE CASE SCENARIOS === \E2\9A\A1\00", [36 x i8]* %tmp_191_str, align 1
  %tmp_192 = bitcast [36 x i8]* %tmp_191_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_192)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if minScore == 0
  %tmp_193 = load i32, i32* @global_minScore, align 4
  %tmp_194 = icmp eq i32 %tmp_193, 0
  br i1 %tmp_194, label %if_true_55, label %if_else_57
if_true_55:
  ; print "🎉 PASS: Zero equality works!" (inline)
  %tmp_195_str = alloca [32 x i8], align 1
  store [32 x i8] c"\F0\9F\8E\89 PASS: Zero equality works!\00", [32 x i8]* %tmp_195_str, align 1
  %tmp_196 = bitcast [32 x i8]* %tmp_195_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_196)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_else_57:
  ; print "❌ FAIL: Zero equality failed!" (inline)
  %tmp_197_str = alloca [32 x i8], align 1
  store [32 x i8] c"\E2\9D\8C FAIL: Zero equality failed!\00", [32 x i8]* %tmp_197_str, align 1
  %tmp_198 = bitcast [32 x i8]* %tmp_197_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_198)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_56
if_end_56:
  ; if zero == 0.0
  %tmp_199 = load float, float* @global_zero, align 4
  %tmp_200 = bitcast i32 0 to float
  %tmp_201 = fcmp oeq float %tmp_199, %tmp_200
  br i1 %tmp_201, label %if_true_58, label %if_else_60
if_true_58:
  ; print "🎉 PASS: Float zero comparison works!" (inline)
  %tmp_202_str = alloca [40 x i8], align 1
  store [40 x i8] c"\F0\9F\8E\89 PASS: Float zero comparison works!\00", [40 x i8]* %tmp_202_str, align 1
  %tmp_203 = bitcast [40 x i8]* %tmp_202_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_203)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_59
if_else_60:
  ; print "❌ FAIL: Float zero comparison failed!" (inline)
  %tmp_204_str = alloca [40 x i8], align 1
  store [40 x i8] c"\E2\9D\8C FAIL: Float zero comparison failed!\00", [40 x i8]* %tmp_204_str, align 1
  %tmp_205 = bitcast [40 x i8]* %tmp_204_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_205)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_59
if_end_59:
  ; if maxScore >= perfectScore
  %tmp_206 = load i32, i32* @global_maxScore, align 4
  %tmp_207 = load i32, i32* @global_perfectScore, align 4
  %tmp_208 = icmp sge i32 %tmp_206, %tmp_207
  br i1 %tmp_208, label %if_true_61, label %if_else_63
if_true_61:
  ; print "🎉 PASS: Maximum value comparison works!" (inline)
  %tmp_209_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\89 PASS: Maximum value comparison works!\00", [43 x i8]* %tmp_209_str, align 1
  %tmp_210 = bitcast [43 x i8]* %tmp_209_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_210)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_else_63:
  ; print "❌ FAIL: Maximum value comparison failed!" (inline)
  %tmp_211_str = alloca [43 x i8], align 1
  store [43 x i8] c"\E2\9D\8C FAIL: Maximum value comparison failed!\00", [43 x i8]* %tmp_211_str, align 1
  %tmp_212 = bitcast [43 x i8]* %tmp_211_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_212)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_62
if_end_62:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏃‍♂️ === PERFORMANCE STRESS TEST === 🏃‍♂️" (inline)
  %tmp_213_str = alloca [60 x i8], align 1
  store [60 x i8] c"\F0\9F\8F\83\E2\80\8D\E2\99\82\EF\B8\8F === PERFORMANCE STRESS TEST === \F0\9F\8F\83\E2\80\8D\E2\99\82\EF\B8\8F\00", [60 x i8]* %tmp_213_str, align 1
  %tmp_214 = bitcast [60 x i8]* %tmp_213_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_214)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > passingGrade
  %tmp_215 = load i32, i32* @global_score, align 4
  %tmp_216 = load i32, i32* @global_passingGrade, align 4
  %tmp_217 = icmp sgt i32 %tmp_215, %tmp_216
  br i1 %tmp_217, label %if_true_64, label %if_else_66
if_true_64:
  ; if maxScore > score
  %tmp_218 = load i32, i32* @global_maxScore, align 4
  %tmp_219 = load i32, i32* @global_score, align 4
  %tmp_220 = icmp sgt i32 %tmp_218, %tmp_219
  br i1 %tmp_220, label %if_true_67, label %if_else_69
if_true_67:
  ; if pi > e
  %tmp_221 = load float, float* @global_pi, align 4
  %tmp_222 = load float, float* @global_e, align 4
  %tmp_223 = fcmp ogt float %tmp_221, %tmp_222
  br i1 %tmp_223, label %if_true_70, label %if_else_72
if_true_70:
  ; if playerName != adminName
  %tmp_224 = load i8*, i8** @global_playerName, align 8
  %tmp_225 = load i8*, i8** @global_adminName, align 8
  %tmp_227 = call i32 @strcmp(i8* %tmp_224, i8* %tmp_225)
  %tmp_226 = icmp ne i32 %tmp_227, 0
  br i1 %tmp_226, label %if_true_73, label %if_else_75
if_true_73:
  ; if isActive == true
  %tmp_228 = load i1, i1* @global_isActive, align 1
  %tmp_229 = zext i1 %tmp_228 to i32
  %tmp_230 = icmp eq i32 %tmp_229, 1
  br i1 %tmp_230, label %if_true_76, label %if_else_78
if_true_76:
  ; print "🎉 PASS: Nested comparison chain works!" (inline)
  %tmp_231_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Nested comparison chain works!\00", [42 x i8]* %tmp_231_str, align 1
  %tmp_232 = bitcast [42 x i8]* %tmp_231_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_232)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_77
if_else_78:
  ; print "❌ FAIL: Nested comparison chain failed at level 5!" (inline)
  %tmp_233_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 5!\00", [53 x i8]* %tmp_233_str, align 1
  %tmp_234 = bitcast [53 x i8]* %tmp_233_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_234)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_77
if_end_77:
  br label %if_end_74
if_else_75:
  ; print "❌ FAIL: Nested comparison chain failed at level 4!" (inline)
  %tmp_235_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 4!\00", [53 x i8]* %tmp_235_str, align 1
  %tmp_236 = bitcast [53 x i8]* %tmp_235_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_236)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_74
if_end_74:
  br label %if_end_71
if_else_72:
  ; print "❌ FAIL: Nested comparison chain failed at level 3!" (inline)
  %tmp_237_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 3!\00", [53 x i8]* %tmp_237_str, align 1
  %tmp_238 = bitcast [53 x i8]* %tmp_237_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_238)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_71
if_end_71:
  br label %if_end_68
if_else_69:
  ; print "❌ FAIL: Nested comparison chain failed at level 2!" (inline)
  %tmp_239_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 2!\00", [53 x i8]* %tmp_239_str, align 1
  %tmp_240 = bitcast [53 x i8]* %tmp_239_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_240)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_68
if_end_68:
  br label %if_end_65
if_else_66:
  ; print "❌ FAIL: Nested comparison chain failed at level 1!" (inline)
  %tmp_241_str = alloca [53 x i8], align 1
  store [53 x i8] c"\E2\9D\8C FAIL: Nested comparison chain failed at level 1!\00", [53 x i8]* %tmp_241_str, align 1
  %tmp_242 = bitcast [53 x i8]* %tmp_241_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_242)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_65
if_end_65:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 === MATHEMATICAL EXPRESSION OLYMPICS === 🏆" (inline)
  %tmp_243_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\8F\86 === MATHEMATICAL EXPRESSION OLYMPICS === \F0\9F\8F\86\00", [51 x i8]* %tmp_243_str, align 1
  %tmp_244 = bitcast [51 x i8]* %tmp_243_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_244)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int fib1 = 1
  store i32 1, i32* @global_fib1, align 4
  ; int fib2 = 1
  store i32 1, i32* @global_fib2, align 4
  ; int fib3 = 2
  store i32 2, i32* @global_fib3, align 4
  ; int fib4 = 3
  store i32 3, i32* @global_fib4, align 4
  ; int fib5 = 5
  store i32 5, i32* @global_fib5, align 4
  ; if (fib1 + fib2) == fib3
  %tmp_245 = load i32, i32* @global_fib1, align 4
  %tmp_246 = load i32, i32* @global_fib2, align 4
  %tmp_247 = add i32 %tmp_245, %tmp_246
  %tmp_248 = load i32, i32* @global_fib3, align 4
  %tmp_249 = icmp eq i32 %tmp_247, %tmp_248
  br i1 %tmp_249, label %if_true_79, label %if_else_81
if_true_79:
  ; print "🎉 PASS: Fibonacci sequence comparison 1 works!" (inline)
  %tmp_250_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Fibonacci sequence comparison 1 works!\00", [50 x i8]* %tmp_250_str, align 1
  %tmp_251 = bitcast [50 x i8]* %tmp_250_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_251)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_80
if_else_81:
  ; print "❌ FAIL: Fibonacci sequence comparison 1 failed!" (inline)
  %tmp_252_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Fibonacci sequence comparison 1 failed!\00", [50 x i8]* %tmp_252_str, align 1
  %tmp_253 = bitcast [50 x i8]* %tmp_252_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_253)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_80
if_end_80:
  ; if (fib3 + fib4) == (fib5 + 0)
  %tmp_254 = load i32, i32* @global_fib3, align 4
  %tmp_255 = load i32, i32* @global_fib4, align 4
  %tmp_256 = add i32 %tmp_254, %tmp_255
  %tmp_257 = load i32, i32* @global_fib5, align 4
  %tmp_258 = add i32 %tmp_257, 0
  %tmp_259 = icmp eq i32 %tmp_256, %tmp_258
  br i1 %tmp_259, label %if_true_82, label %if_else_84
if_true_82:
  ; print "🎉 PASS: Fibonacci sequence comparison 2 works!" (inline)
  %tmp_260_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Fibonacci sequence comparison 2 works!\00", [50 x i8]* %tmp_260_str, align 1
  %tmp_261 = bitcast [50 x i8]* %tmp_260_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_261)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_83
if_else_84:
  ; print "❌ FAIL: Fibonacci sequence comparison 2 failed!" (inline)
  %tmp_262_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Fibonacci sequence comparison 2 failed!\00", [50 x i8]* %tmp_262_str, align 1
  %tmp_263 = bitcast [50 x i8]* %tmp_262_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_263)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_83
if_end_83:
  ; int prime1 = 2
  store i32 2, i32* @global_prime1, align 4
  ; int prime2 = 3
  store i32 3, i32* @global_prime2, align 4
  ; int prime3 = 5
  store i32 5, i32* @global_prime3, align 4
  ; int prime4 = 7
  store i32 7, i32* @global_prime4, align 4
  ; if (prime1 * prime2) < (prime3 * prime4)
  %tmp_264 = load i32, i32* @global_prime1, align 4
  %tmp_265 = load i32, i32* @global_prime2, align 4
  %tmp_266 = mul i32 %tmp_264, %tmp_265
  %tmp_267 = load i32, i32* @global_prime3, align 4
  %tmp_268 = load i32, i32* @global_prime4, align 4
  %tmp_269 = mul i32 %tmp_267, %tmp_268
  %tmp_270 = icmp slt i32 %tmp_266, %tmp_269
  br i1 %tmp_270, label %if_true_85, label %if_else_87
if_true_85:
  ; print "🎉 PASS: Prime multiplication comparison works!" (inline)
  %tmp_271_str = alloca [50 x i8], align 1
  store [50 x i8] c"\F0\9F\8E\89 PASS: Prime multiplication comparison works!\00", [50 x i8]* %tmp_271_str, align 1
  %tmp_272 = bitcast [50 x i8]* %tmp_271_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_272)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_86
if_else_87:
  ; print "❌ FAIL: Prime multiplication comparison failed!" (inline)
  %tmp_273_str = alloca [50 x i8], align 1
  store [50 x i8] c"\E2\9D\8C FAIL: Prime multiplication comparison failed!\00", [50 x i8]* %tmp_273_str, align 1
  %tmp_274 = bitcast [50 x i8]* %tmp_273_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_274)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_86
if_end_86:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "👑 === FINAL BOSS CHALLENGE === 👑" (inline)
  %tmp_275_str = alloca [39 x i8], align 1
  store [39 x i8] c"\F0\9F\91\91 === FINAL BOSS CHALLENGE === \F0\9F\91\91\00", [39 x i8]* %tmp_275_str, align 1
  %tmp_276 = bitcast [39 x i8]* %tmp_275_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_276)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if ((score * 2) + (fib5 * prime1)) >= ((maxScore + passingGrade) - (minScore * 2))
  %tmp_277 = load i32, i32* @global_score, align 4
  %tmp_278 = mul i32 %tmp_277, 2
  %tmp_279 = load i32, i32* @global_fib5, align 4
  %tmp_280 = load i32, i32* @global_prime1, align 4
  %tmp_281 = mul i32 %tmp_279, %tmp_280
  %tmp_282 = add i32 %tmp_278, %tmp_281
  %tmp_283 = load i32, i32* @global_maxScore, align 4
  %tmp_284 = load i32, i32* @global_passingGrade, align 4
  %tmp_285 = add i32 %tmp_283, %tmp_284
  %tmp_286 = load i32, i32* @global_minScore, align 4
  %tmp_287 = mul i32 %tmp_286, 2
  %tmp_288 = sub i32 %tmp_285, %tmp_287
  %tmp_289 = icmp sge i32 %tmp_282, %tmp_288
  br i1 %tmp_289, label %if_true_88, label %if_else_90
if_true_88:
  ; print "🎉🎉🎉 ULTIMATE VICTORY! The most complex comparison ..." (inline)
  %tmp_290_str = alloca [96 x i8], align 1
  store [96 x i8] c"\F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89 ULTIMATE VICTORY! The most complex comparison in Dolet history works! \F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89\00", [96 x i8]* %tmp_290_str, align 1
  %tmp_291 = bitcast [96 x i8]* %tmp_290_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_291)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 Dolet has achieved LEGENDARY status in compariso..." (inline)
  %tmp_292_str = alloca [72 x i8], align 1
  store [72 x i8] c"\F0\9F\8F\86 Dolet has achieved LEGENDARY status in comparison operations! \F0\9F\8F\86\00", [72 x i8]* %tmp_292_str, align 1
  %tmp_293 = bitcast [72 x i8]* %tmp_292_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_293)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_89
if_else_90:
  ; print "💀 ULTIMATE DEFEAT! The final boss comparison faile..." (inline)
  %tmp_294_str = alloca [56 x i8], align 1
  store [56 x i8] c"\F0\9F\92\80 ULTIMATE DEFEAT! The final boss comparison failed!\00", [56 x i8]* %tmp_294_str, align 1
  %tmp_295 = bitcast [56 x i8]* %tmp_294_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_295)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔧 Time to debug the most complex expression ever c..." (inline)
  %tmp_296_str = alloca [61 x i8], align 1
  store [61 x i8] c"\F0\9F\94\A7 Time to debug the most complex expression ever created!\00", [61 x i8]* %tmp_296_str, align 1
  %tmp_297 = bitcast [61 x i8]* %tmp_296_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_297)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_89
if_end_89:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌪️ === ULTRA MEGA NESTED CHAOS === 🌪️" (inline)
  %tmp_298_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8C\AA\EF\B8\8F === ULTRA MEGA NESTED CHAOS === \F0\9F\8C\AA\EF\B8\8F\00", [48 x i8]* %tmp_298_str, align 1
  %tmp_299 = bitcast [48 x i8]* %tmp_298_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_299)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔥 Preparing for the most insane nested comparison ..." (inline)
  %tmp_300_str = alloca [77 x i8], align 1
  store [77 x i8] c"\F0\9F\94\A5 Preparing for the most insane nested comparison challenge ever created!\00", [77 x i8]* %tmp_300_str, align 1
  %tmp_301 = bitcast [77 x i8]* %tmp_300_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_301)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > 50
  %tmp_302 = load i32, i32* @global_score, align 4
  %tmp_303 = icmp sgt i32 %tmp_302, 50
  br i1 %tmp_303, label %if_true_91, label %if_else_93
if_true_91:
  ; print "✅ Level 1: Score check passed" (inline)
  %tmp_304_str = alloca [32 x i8], align 1
  store [32 x i8] c"\E2\9C\85 Level 1: Score check passed\00", [32 x i8]* %tmp_304_str, align 1
  %tmp_305 = bitcast [32 x i8]* %tmp_304_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_305)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if maxScore > 90
  %tmp_306 = load i32, i32* @global_maxScore, align 4
  %tmp_307 = icmp sgt i32 %tmp_306, 90
  br i1 %tmp_307, label %if_true_94, label %if_else_96
if_true_94:
  ; print "✅ Level 2: Range validation passed" (inline)
  %tmp_308_str = alloca [37 x i8], align 1
  store [37 x i8] c"\E2\9C\85 Level 2: Range validation passed\00", [37 x i8]* %tmp_308_str, align 1
  %tmp_309 = bitcast [37 x i8]* %tmp_308_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_309)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if pi > 3.0
  %tmp_310 = load float, float* @global_pi, align 4
  %tmp_311 = bitcast i32 1077936128 to float
  %tmp_312 = fcmp ogt float %tmp_310, %tmp_311
  br i1 %tmp_312, label %if_true_97, label %if_else_99
if_true_97:
  ; print "✅ Level 3: Mathematical constants validated" (inline)
  %tmp_313_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9C\85 Level 3: Mathematical constants validated\00", [46 x i8]* %tmp_313_str, align 1
  %tmp_314 = bitcast [46 x i8]* %tmp_313_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_314)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if playerName == "Ahmed"
  %tmp_315 = load i8*, i8** @global_playerName, align 8
  %tmp_317 = getelementptr inbounds [6 x i8], [6 x i8]* @str_cond_315, i64 0, i64 0
  %tmp_319 = call i32 @strcmp(i8* %tmp_315, i8* %tmp_317)
  %tmp_318 = icmp eq i32 %tmp_319, 0
  br i1 %tmp_318, label %if_true_100, label %if_else_102
if_true_100:
  ; print "✅ Level 4: String validation passed" (inline)
  %tmp_320_str = alloca [38 x i8], align 1
  store [38 x i8] c"\E2\9C\85 Level 4: String validation passed\00", [38 x i8]* %tmp_320_str, align 1
  %tmp_321 = bitcast [38 x i8]* %tmp_320_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_321)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if isActive == true
  %tmp_322 = load i1, i1* @global_isActive, align 1
  %tmp_323 = zext i1 %tmp_322 to i32
  %tmp_324 = icmp eq i32 %tmp_323, 1
  br i1 %tmp_324, label %if_true_103, label %if_else_105
if_true_103:
  ; print "✅ Level 5: Boolean logic validated" (inline)
  %tmp_325_str = alloca [37 x i8], align 1
  store [37 x i8] c"\E2\9C\85 Level 5: Boolean logic validated\00", [37 x i8]* %tmp_325_str, align 1
  %tmp_326 = bitcast [37 x i8]* %tmp_325_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_326)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (fib1 + fib2) == fib3
  %tmp_327 = load i32, i32* @global_fib1, align 4
  %tmp_328 = load i32, i32* @global_fib2, align 4
  %tmp_329 = add i32 %tmp_327, %tmp_328
  %tmp_330 = load i32, i32* @global_fib3, align 4
  %tmp_331 = icmp eq i32 %tmp_329, %tmp_330
  br i1 %tmp_331, label %if_true_106, label %if_else_108
if_true_106:
  ; print "✅ Level 6: Mathematical sequences validated" (inline)
  %tmp_332_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9C\85 Level 6: Mathematical sequences validated\00", [46 x i8]* %tmp_332_str, align 1
  %tmp_333 = bitcast [46 x i8]* %tmp_332_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_333)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > pi
  %tmp_334 = load i32, i32* @global_score, align 4
  %tmp_335 = load float, float* @global_pi, align 4
  %tmp_337 = sitofp i32 %tmp_334 to float
  %tmp_336 = fcmp ogt float %tmp_337, %tmp_335
  br i1 %tmp_336, label %if_true_109, label %if_else_111
if_true_109:
  ; print "✅ Level 7: Mixed type comparisons passed" (inline)
  %tmp_338_str = alloca [43 x i8], align 1
  store [43 x i8] c"\E2\9C\85 Level 7: Mixed type comparisons passed\00", [43 x i8]* %tmp_338_str, align 1
  %tmp_339 = bitcast [43 x i8]* %tmp_338_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_339)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if emoji1 == "🎉"
  %tmp_340 = load i8*, i8** @global_emoji1, align 8
  %tmp_342 = getelementptr inbounds [2 x i8], [2 x i8]* @str_cond_340, i64 0, i64 0
  %tmp_344 = call i32 @strcmp(i8* %tmp_340, i8* %tmp_342)
  %tmp_343 = icmp eq i32 %tmp_344, 0
  br i1 %tmp_343, label %if_true_112, label %if_else_114
if_true_112:
  ; print "✅ Level 8: Unicode validation passed" (inline)
  %tmp_345_str = alloca [39 x i8], align 1
  store [39 x i8] c"\E2\9C\85 Level 8: Unicode validation passed\00", [39 x i8]* %tmp_345_str, align 1
  %tmp_346 = bitcast [39 x i8]* %tmp_345_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_346)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if arr1 < arr2
  %tmp_347 = load i32, i32* @global_arr1, align 4
  %tmp_348 = load i32, i32* @global_arr2, align 4
  %tmp_349 = icmp slt i32 %tmp_347, %tmp_348
  br i1 %tmp_349, label %if_true_115, label %if_else_117
if_true_115:
  ; print "✅ Level 9: Sequential data validation passed" (inline)
  %tmp_350_str = alloca [47 x i8], align 1
  store [47 x i8] c"\E2\9C\85 Level 9: Sequential data validation passed\00", [47 x i8]* %tmp_350_str, align 1
  %tmp_351 = bitcast [47 x i8]* %tmp_350_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_351)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if large1 < large2
  %tmp_352 = load float, float* @global_large1, align 4
  %tmp_353 = load float, float* @global_large2, align 4
  %tmp_354 = fcmp olt float %tmp_352, %tmp_353
  br i1 %tmp_354, label %if_true_118, label %if_else_120
if_true_118:
  ; print "✅ Level 10: Scientific precision validated" (inline)
  %tmp_355_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9C\85 Level 10: Scientific precision validated\00", [45 x i8]* %tmp_355_str, align 1
  %tmp_356 = bitcast [45 x i8]* %tmp_355_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_356)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score + fib5) > (maxScore - passingGrade)
  %tmp_357 = load i32, i32* @global_score, align 4
  %tmp_358 = load i32, i32* @global_fib5, align 4
  %tmp_359 = add i32 %tmp_357, %tmp_358
  %tmp_360 = load i32, i32* @global_maxScore, align 4
  %tmp_361 = load i32, i32* @global_passingGrade, align 4
  %tmp_362 = sub i32 %tmp_360, %tmp_361
  %tmp_363 = icmp sgt i32 %tmp_359, %tmp_362
  br i1 %tmp_363, label %if_true_121, label %if_else_123
if_true_121:
  ; print "✅ Level 11: MEGA COMPLEX validation passed" (inline)
  %tmp_364_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9C\85 Level 11: MEGA COMPLEX validation passed\00", [45 x i8]* %tmp_364_str, align 1
  %tmp_365 = bitcast [45 x i8]* %tmp_364_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_365)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score * 2) > (maxScore + passingGrade)
  %tmp_366 = load i32, i32* @global_score, align 4
  %tmp_367 = mul i32 %tmp_366, 2
  %tmp_368 = load i32, i32* @global_maxScore, align 4
  %tmp_369 = load i32, i32* @global_passingGrade, align 4
  %tmp_370 = add i32 %tmp_368, %tmp_369
  %tmp_371 = icmp sgt i32 %tmp_367, %tmp_370
  br i1 %tmp_371, label %if_true_124, label %if_else_126
if_true_124:
  ; print "🔥🔥🔥🔥🔥 LEGENDARY ACHIEVEMENT UNLOCKED! 🔥🔥🔥🔥🔥" (inline)
  %tmp_372_str = alloca [74 x i8], align 1
  store [74 x i8] c"\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5 LEGENDARY ACHIEVEMENT UNLOCKED! \F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\00", [74 x i8]* %tmp_372_str, align 1
  %tmp_373 = bitcast [74 x i8]* %tmp_372_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_373)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 DOLET HAS TRANSCENDED TO GODLIKE STATUS! 🏆" (inline)
  %tmp_374_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\8F\86 DOLET HAS TRANSCENDED TO GODLIKE STATUS! \F0\9F\8F\86\00", [51 x i8]* %tmp_374_str, align 1
  %tmp_375 = bitcast [51 x i8]* %tmp_374_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_375)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌟 12-LEVEL NESTED COMPARISON MASTERY ACHIEVED! 🌟" (inline)
  %tmp_376_str = alloca [55 x i8], align 1
  store [55 x i8] c"\F0\9F\8C\9F 12-LEVEL NESTED COMPARISON MASTERY ACHIEVED! \F0\9F\8C\9F\00", [55 x i8]* %tmp_376_str, align 1
  %tmp_377 = bitcast [55 x i8]* %tmp_376_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_377)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🚀 This is the most complex nested comparison ever ..." (inline)
  %tmp_378_str = alloca [68 x i8], align 1
  store [68 x i8] c"\F0\9F\9A\80 This is the most complex nested comparison ever executed! \F0\9F\9A\80\00", [68 x i8]* %tmp_378_str, align 1
  %tmp_379 = bitcast [68 x i8]* %tmp_378_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_379)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "💎 Dolet is now DIAMOND TIER compiler! 💎" (inline)
  %tmp_380_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\92\8E Dolet is now DIAMOND TIER compiler! \F0\9F\92\8E\00", [46 x i8]* %tmp_380_str, align 1
  %tmp_381 = bitcast [46 x i8]* %tmp_380_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_381)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_125
if_else_126:
  ; print "💀 Failed at Level 12: ULTIMATE NESTED MADNESS" (inline)
  %tmp_382_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\92\80 Failed at Level 12: ULTIMATE NESTED MADNESS\00", [49 x i8]* %tmp_382_str, align 1
  %tmp_383 = bitcast [49 x i8]* %tmp_382_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_383)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_125
if_end_125:
  br label %if_end_122
if_else_123:
  ; print "💀 Failed at Level 11: MEGA COMPLEX validation" (inline)
  %tmp_384_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\92\80 Failed at Level 11: MEGA COMPLEX validation\00", [49 x i8]* %tmp_384_str, align 1
  %tmp_385 = bitcast [49 x i8]* %tmp_384_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_385)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_122
if_end_122:
  br label %if_end_119
if_else_120:
  ; print "💀 Failed at Level 10: Scientific precision" (inline)
  %tmp_386_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\92\80 Failed at Level 10: Scientific precision\00", [46 x i8]* %tmp_386_str, align 1
  %tmp_387 = bitcast [46 x i8]* %tmp_386_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_387)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_119
if_end_119:
  br label %if_end_116
if_else_117:
  ; print "💀 Failed at Level 9: Sequential data validation" (inline)
  %tmp_388_str = alloca [51 x i8], align 1
  store [51 x i8] c"\F0\9F\92\80 Failed at Level 9: Sequential data validation\00", [51 x i8]* %tmp_388_str, align 1
  %tmp_389 = bitcast [51 x i8]* %tmp_388_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_389)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_116
if_end_116:
  br label %if_end_113
if_else_114:
  ; print "💀 Failed at Level 8: Unicode validation" (inline)
  %tmp_390_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\92\80 Failed at Level 8: Unicode validation\00", [43 x i8]* %tmp_390_str, align 1
  %tmp_391 = bitcast [43 x i8]* %tmp_390_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_391)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_113
if_end_113:
  br label %if_end_110
if_else_111:
  ; print "💀 Failed at Level 7: Mixed type comparisons" (inline)
  %tmp_392_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\92\80 Failed at Level 7: Mixed type comparisons\00", [47 x i8]* %tmp_392_str, align 1
  %tmp_393 = bitcast [47 x i8]* %tmp_392_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_393)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_110
if_end_110:
  br label %if_end_107
if_else_108:
  ; print "💀 Failed at Level 6: Mathematical sequences" (inline)
  %tmp_394_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\92\80 Failed at Level 6: Mathematical sequences\00", [47 x i8]* %tmp_394_str, align 1
  %tmp_395 = bitcast [47 x i8]* %tmp_394_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_395)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_107
if_end_107:
  br label %if_end_104
if_else_105:
  ; print "💀 Failed at Level 5: Boolean logic" (inline)
  %tmp_396_str = alloca [38 x i8], align 1
  store [38 x i8] c"\F0\9F\92\80 Failed at Level 5: Boolean logic\00", [38 x i8]* %tmp_396_str, align 1
  %tmp_397 = bitcast [38 x i8]* %tmp_396_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_397)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_104
if_end_104:
  br label %if_end_101
if_else_102:
  ; print "💀 Failed at Level 4: String validation" (inline)
  %tmp_398_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\92\80 Failed at Level 4: String validation\00", [42 x i8]* %tmp_398_str, align 1
  %tmp_399 = bitcast [42 x i8]* %tmp_398_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_399)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_101
if_end_101:
  br label %if_end_98
if_else_99:
  ; print "💀 Failed at Level 3: Mathematical constants" (inline)
  %tmp_400_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\92\80 Failed at Level 3: Mathematical constants\00", [47 x i8]* %tmp_400_str, align 1
  %tmp_401 = bitcast [47 x i8]* %tmp_400_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_401)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_98
if_end_98:
  br label %if_end_95
if_else_96:
  ; print "💀 Failed at Level 2: Range validation" (inline)
  %tmp_402_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at Level 2: Range validation\00", [41 x i8]* %tmp_402_str, align 1
  %tmp_403 = bitcast [41 x i8]* %tmp_402_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_403)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_95
if_end_95:
  br label %if_end_92
if_else_93:
  ; print "💀 Failed at Level 1: Score check" (inline)
  %tmp_404_str = alloca [36 x i8], align 1
  store [36 x i8] c"\F0\9F\92\80 Failed at Level 1: Score check\00", [36 x i8]* %tmp_404_str, align 1
  %tmp_405 = bitcast [36 x i8]* %tmp_404_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_405)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_92
if_end_92:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎯 === CHALLENGE COMPLETE === 🎯" (inline)
  %tmp_406_str = alloca [37 x i8], align 1
  store [37 x i8] c"\F0\9F\8E\AF === CHALLENGE COMPLETE === \F0\9F\8E\AF\00", [37 x i8]* %tmp_406_str, align 1
  %tmp_407 = bitcast [37 x i8]* %tmp_406_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_407)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 If you see this message, Dolet has survived the ..." (inline)
  %tmp_408_str = alloca [84 x i8], align 1
  store [84 x i8] c"\F0\9F\93\8A If you see this message, Dolet has survived the ultimate comparison challenge!\00", [84 x i8]* %tmp_408_str, align 1
  %tmp_409 = bitcast [84 x i8]* %tmp_408_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_409)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🚀 Ready for production use with confidence!" (inline)
  %tmp_410_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\9A\80 Ready for production use with confidence!\00", [47 x i8]* %tmp_410_str, align 1
  %tmp_411 = bitcast [47 x i8]* %tmp_410_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_411)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎉 Thank you for testing Dolet's comparison capabil..." (inline)
  %tmp_412_str = alloca [65 x i8], align 1
  store [65 x i8] c"\F0\9F\8E\89 Thank you for testing Dolet's comparison capabilities! \F0\9F\8E\89\00", [65 x i8]* %tmp_412_str, align 1
  %tmp_413 = bitcast [65 x i8]* %tmp_412_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_413)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌟 === BONUS: UNICODE & EMOJI TESTS === 🌟" (inline)
  %tmp_414_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\8C\9F === BONUS: UNICODE & EMOJI TESTS === \F0\9F\8C\9F\00", [47 x i8]* %tmp_414_str, align 1
  %tmp_415 = bitcast [47 x i8]* %tmp_414_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_415)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; string emoji1 = "🎉"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_5, i64 0, i64 0), i8** @global_emoji1, align 8
  ; string emoji2 = "🚀"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_6, i64 0, i64 0), i8** @global_emoji2, align 8
  ; string emoji3 = "🎉"
  store i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_5, i64 0, i64 0), i8** @global_emoji3, align 8
  ; string arabic = "مرحبا"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_8, i64 0, i64 0), i8** @global_arabic, align 8
  ; string english = "Hello"
  store i8* getelementptr inbounds ([6 x i8], [6 x i8]* @.str_9, i64 0, i64 0), i8** @global_english, align 8
  ; if emoji1 == emoji3
  %tmp_416 = load i8*, i8** @global_emoji1, align 8
  %tmp_417 = load i8*, i8** @global_emoji3, align 8
  %tmp_419 = call i32 @strcmp(i8* %tmp_416, i8* %tmp_417)
  %tmp_418 = icmp eq i32 %tmp_419, 0
  br i1 %tmp_418, label %if_true_127, label %if_else_129
if_true_127:
  ; print "🎉 PASS: Emoji equality comparison works!" (inline)
  %tmp_420_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\8E\89 PASS: Emoji equality comparison works!\00", [44 x i8]* %tmp_420_str, align 1
  %tmp_421 = bitcast [44 x i8]* %tmp_420_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_421)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_128
if_else_129:
  ; print "❌ FAIL: Emoji equality comparison failed!" (inline)
  %tmp_422_str = alloca [44 x i8], align 1
  store [44 x i8] c"\E2\9D\8C FAIL: Emoji equality comparison failed!\00", [44 x i8]* %tmp_422_str, align 1
  %tmp_423 = bitcast [44 x i8]* %tmp_422_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_423)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_128
if_end_128:
  ; if emoji1 != emoji2
  %tmp_424 = load i8*, i8** @global_emoji1, align 8
  %tmp_425 = load i8*, i8** @global_emoji2, align 8
  %tmp_427 = call i32 @strcmp(i8* %tmp_424, i8* %tmp_425)
  %tmp_426 = icmp ne i32 %tmp_427, 0
  br i1 %tmp_426, label %if_true_130, label %if_else_132
if_true_130:
  ; print "🎉 PASS: Different emoji comparison works!" (inline)
  %tmp_428_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\8E\89 PASS: Different emoji comparison works!\00", [45 x i8]* %tmp_428_str, align 1
  %tmp_429 = bitcast [45 x i8]* %tmp_428_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_429)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_131
if_else_132:
  ; print "❌ FAIL: Different emoji comparison failed!" (inline)
  %tmp_430_str = alloca [45 x i8], align 1
  store [45 x i8] c"\E2\9D\8C FAIL: Different emoji comparison failed!\00", [45 x i8]* %tmp_430_str, align 1
  %tmp_431 = bitcast [45 x i8]* %tmp_430_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_431)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_131
if_end_131:
  ; if arabic != english
  %tmp_432 = load i8*, i8** @global_arabic, align 8
  %tmp_433 = load i8*, i8** @global_english, align 8
  %tmp_435 = call i32 @strcmp(i8* %tmp_432, i8* %tmp_433)
  %tmp_434 = icmp ne i32 %tmp_435, 0
  br i1 %tmp_434, label %if_true_133, label %if_else_135
if_true_133:
  ; print "🎉 PASS: Unicode language comparison works!" (inline)
  %tmp_436_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Unicode language comparison works!\00", [46 x i8]* %tmp_436_str, align 1
  %tmp_437 = bitcast [46 x i8]* %tmp_436_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_437)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_134
if_else_135:
  ; print "❌ FAIL: Unicode language comparison failed!" (inline)
  %tmp_438_str = alloca [46 x i8], align 1
  store [46 x i8] c"\E2\9D\8C FAIL: Unicode language comparison failed!\00", [46 x i8]* %tmp_438_str, align 1
  %tmp_439 = bitcast [46 x i8]* %tmp_438_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_439)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_134
if_end_134:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔥 === EXTREME NESTING CHALLENGE === 🔥" (inline)
  %tmp_440_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\94\A5 === EXTREME NESTING CHALLENGE === \F0\9F\94\A5\00", [44 x i8]* %tmp_440_str, align 1
  %tmp_441 = bitcast [44 x i8]* %tmp_440_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_441)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if score > 50
  %tmp_442 = load i32, i32* @global_score, align 4
  %tmp_443 = icmp sgt i32 %tmp_442, 50
  br i1 %tmp_443, label %if_true_136, label %if_else_138
if_true_136:
  ; if maxScore > 90
  %tmp_444 = load i32, i32* @global_maxScore, align 4
  %tmp_445 = icmp sgt i32 %tmp_444, 90
  br i1 %tmp_445, label %if_true_139, label %if_else_141
if_true_139:
  ; if pi > 3.0
  %tmp_446 = load float, float* @global_pi, align 4
  %tmp_447 = bitcast i32 1077936128 to float
  %tmp_448 = fcmp ogt float %tmp_446, %tmp_447
  br i1 %tmp_448, label %if_true_142, label %if_else_144
if_true_142:
  ; if playerName == "Ahmed"
  %tmp_449 = load i8*, i8** @global_playerName, align 8
  %tmp_451 = getelementptr inbounds [6 x i8], [6 x i8]* @str_cond_449, i64 0, i64 0
  %tmp_453 = call i32 @strcmp(i8* %tmp_449, i8* %tmp_451)
  %tmp_452 = icmp eq i32 %tmp_453, 0
  br i1 %tmp_452, label %if_true_145, label %if_else_147
if_true_145:
  ; if isActive == true
  %tmp_454 = load i1, i1* @global_isActive, align 1
  %tmp_455 = zext i1 %tmp_454 to i32
  %tmp_456 = icmp eq i32 %tmp_455, 1
  br i1 %tmp_456, label %if_true_148, label %if_else_150
if_true_148:
  ; if fib5 == 5
  %tmp_457 = load i32, i32* @global_fib5, align 4
  %tmp_458 = icmp eq i32 %tmp_457, 5
  br i1 %tmp_458, label %if_true_151, label %if_else_153
if_true_151:
  ; if prime4 > prime3
  %tmp_459 = load i32, i32* @global_prime4, align 4
  %tmp_460 = load i32, i32* @global_prime3, align 4
  %tmp_461 = icmp sgt i32 %tmp_459, %tmp_460
  br i1 %tmp_461, label %if_true_154, label %if_else_156
if_true_154:
  ; if emoji1 == "🎉"
  %tmp_462 = load i8*, i8** @global_emoji1, align 8
  %tmp_464 = getelementptr inbounds [2 x i8], [2 x i8]* @str_cond_462, i64 0, i64 0
  %tmp_466 = call i32 @strcmp(i8* %tmp_462, i8* %tmp_464)
  %tmp_465 = icmp eq i32 %tmp_466, 0
  br i1 %tmp_465, label %if_true_157, label %if_else_159
if_true_157:
  ; if (score + 10) > 90
  %tmp_467 = load i32, i32* @global_score, align 4
  %tmp_468 = add i32 %tmp_467, 10
  %tmp_469 = icmp sgt i32 %tmp_468, 90
  br i1 %tmp_469, label %if_true_160, label %if_else_162
if_true_160:
  ; if (pi * 2) > 6.0
  %tmp_470 = load float, float* @global_pi, align 4
  %tmp_471 = sitofp i32 2 to float
  %tmp_472 = fmul float %tmp_470, %tmp_471
  %tmp_473 = bitcast i32 1086324736 to float
  %tmp_474 = fcmp ogt float %tmp_472, %tmp_473
  br i1 %tmp_474, label %if_true_163, label %if_else_165
if_true_163:
  ; print "🔥🔥🔥 LEGENDARY! 10-level nested comparison SUCCESS!..." (inline)
  %tmp_475_str = alloca [73 x i8], align 1
  store [73 x i8] c"\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5 LEGENDARY! 10-level nested comparison SUCCESS! \F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5\00", [73 x i8]* %tmp_475_str, align 1
  %tmp_476 = bitcast [73 x i8]* %tmp_475_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_476)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 Dolet has achieved GODLIKE comparison nesting! 🏆" (inline)
  %tmp_477_str = alloca [57 x i8], align 1
  store [57 x i8] c"\F0\9F\8F\86 Dolet has achieved GODLIKE comparison nesting! \F0\9F\8F\86\00", [57 x i8]* %tmp_477_str, align 1
  %tmp_478 = bitcast [57 x i8]* %tmp_477_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_478)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_164
if_else_165:
  ; print "💀 Failed at level 10: Float multiplication" (inline)
  %tmp_479_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\92\80 Failed at level 10: Float multiplication\00", [46 x i8]* %tmp_479_str, align 1
  %tmp_480 = bitcast [46 x i8]* %tmp_479_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_480)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_164
if_end_164:
  br label %if_end_161
if_else_162:
  ; print "💀 Failed at level 9: Integer addition" (inline)
  %tmp_481_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 9: Integer addition\00", [41 x i8]* %tmp_481_str, align 1
  %tmp_482 = bitcast [41 x i8]* %tmp_481_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_482)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_161
if_end_161:
  br label %if_end_158
if_else_159:
  ; print "💀 Failed at level 8: Emoji comparison" (inline)
  %tmp_483_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 8: Emoji comparison\00", [41 x i8]* %tmp_483_str, align 1
  %tmp_484 = bitcast [41 x i8]* %tmp_483_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_484)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_158
if_end_158:
  br label %if_end_155
if_else_156:
  ; print "💀 Failed at level 7: Prime comparison" (inline)
  %tmp_485_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 7: Prime comparison\00", [41 x i8]* %tmp_485_str, align 1
  %tmp_486 = bitcast [41 x i8]* %tmp_485_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_486)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_155
if_end_155:
  br label %if_end_152
if_else_153:
  ; print "💀 Failed at level 6: Fibonacci comparison" (inline)
  %tmp_487_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\92\80 Failed at level 6: Fibonacci comparison\00", [45 x i8]* %tmp_487_str, align 1
  %tmp_488 = bitcast [45 x i8]* %tmp_487_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_488)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_152
if_end_152:
  br label %if_end_149
if_else_150:
  ; print "💀 Failed at level 5: Boolean comparison" (inline)
  %tmp_489_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\92\80 Failed at level 5: Boolean comparison\00", [43 x i8]* %tmp_489_str, align 1
  %tmp_490 = bitcast [43 x i8]* %tmp_489_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_490)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_149
if_end_149:
  br label %if_end_146
if_else_147:
  ; print "💀 Failed at level 4: String comparison" (inline)
  %tmp_491_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\92\80 Failed at level 4: String comparison\00", [42 x i8]* %tmp_491_str, align 1
  %tmp_492 = bitcast [42 x i8]* %tmp_491_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_492)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_146
if_end_146:
  br label %if_end_143
if_else_144:
  ; print "💀 Failed at level 3: Pi comparison" (inline)
  %tmp_493_str = alloca [38 x i8], align 1
  store [38 x i8] c"\F0\9F\92\80 Failed at level 3: Pi comparison\00", [38 x i8]* %tmp_493_str, align 1
  %tmp_494 = bitcast [38 x i8]* %tmp_493_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_494)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_143
if_end_143:
  br label %if_end_140
if_else_141:
  ; print "💀 Failed at level 2: MaxScore comparison" (inline)
  %tmp_495_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\92\80 Failed at level 2: MaxScore comparison\00", [44 x i8]* %tmp_495_str, align 1
  %tmp_496 = bitcast [44 x i8]* %tmp_495_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_496)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_140
if_end_140:
  br label %if_end_137
if_else_138:
  ; print "💀 Failed at level 1: Score comparison" (inline)
  %tmp_497_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at level 1: Score comparison\00", [41 x i8]* %tmp_497_str, align 1
  %tmp_498 = bitcast [41 x i8]* %tmp_497_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_498)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_137
if_end_137:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "📊 === ARRAY-LIKE COMPARISONS === 📊" (inline)
  %tmp_499_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\93\8A === ARRAY-LIKE COMPARISONS === \F0\9F\93\8A\00", [41 x i8]* %tmp_499_str, align 1
  %tmp_500 = bitcast [41 x i8]* %tmp_499_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_500)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int arr1 = 10
  store i32 10, i32* @global_arr1, align 4
  ; int arr2 = 20
  store i32 20, i32* @global_arr2, align 4
  ; int arr3 = 30
  store i32 30, i32* @global_arr3, align 4
  ; int arr4 = 40
  store i32 40, i32* @global_arr4, align 4
  ; int arr5 = 50
  store i32 50, i32* @global_arr5, align 4
  ; if arr1 < arr2
  %tmp_501 = load i32, i32* @global_arr1, align 4
  %tmp_502 = load i32, i32* @global_arr2, align 4
  %tmp_503 = icmp slt i32 %tmp_501, %tmp_502
  br i1 %tmp_503, label %if_true_166, label %if_else_168
if_true_166:
  ; if arr2 < arr3
  %tmp_504 = load i32, i32* @global_arr2, align 4
  %tmp_505 = load i32, i32* @global_arr3, align 4
  %tmp_506 = icmp slt i32 %tmp_504, %tmp_505
  br i1 %tmp_506, label %if_true_169, label %if_else_171
if_true_169:
  ; if arr3 < arr4
  %tmp_507 = load i32, i32* @global_arr3, align 4
  %tmp_508 = load i32, i32* @global_arr4, align 4
  %tmp_509 = icmp slt i32 %tmp_507, %tmp_508
  br i1 %tmp_509, label %if_true_172, label %if_else_174
if_true_172:
  ; if arr4 < arr5
  %tmp_510 = load i32, i32* @global_arr4, align 4
  %tmp_511 = load i32, i32* @global_arr5, align 4
  %tmp_512 = icmp slt i32 %tmp_510, %tmp_511
  br i1 %tmp_512, label %if_true_175, label %if_else_177
if_true_175:
  ; print "🎉 PASS: Sequential array comparison works!" (inline)
  %tmp_513_str = alloca [46 x i8], align 1
  store [46 x i8] c"\F0\9F\8E\89 PASS: Sequential array comparison works!\00", [46 x i8]* %tmp_513_str, align 1
  %tmp_514 = bitcast [46 x i8]* %tmp_513_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_514)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_176
if_else_177:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_515_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 4-5!\00", [62 x i8]* %tmp_515_str, align 1
  %tmp_516 = bitcast [62 x i8]* %tmp_515_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_516)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_176
if_end_176:
  br label %if_end_173
if_else_174:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_517_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 3-4!\00", [62 x i8]* %tmp_517_str, align 1
  %tmp_518 = bitcast [62 x i8]* %tmp_517_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_518)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_173
if_end_173:
  br label %if_end_170
if_else_171:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_519_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 2-3!\00", [62 x i8]* %tmp_519_str, align 1
  %tmp_520 = bitcast [62 x i8]* %tmp_519_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_520)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_170
if_end_170:
  br label %if_end_167
if_else_168:
  ; print "❌ FAIL: Sequential array comparison failed at posi..." (inline)
  %tmp_521_str = alloca [62 x i8], align 1
  store [62 x i8] c"\E2\9D\8C FAIL: Sequential array comparison failed at position 1-2!\00", [62 x i8]* %tmp_521_str, align 1
  %tmp_522 = bitcast [62 x i8]* %tmp_521_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_522)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_167
if_end_167:
  ; if (arr1 + arr2) < (arr4 + arr5)
  %tmp_523 = load i32, i32* @global_arr1, align 4
  %tmp_524 = load i32, i32* @global_arr2, align 4
  %tmp_525 = add i32 %tmp_523, %tmp_524
  %tmp_526 = load i32, i32* @global_arr4, align 4
  %tmp_527 = load i32, i32* @global_arr5, align 4
  %tmp_528 = add i32 %tmp_526, %tmp_527
  %tmp_529 = icmp slt i32 %tmp_525, %tmp_528
  br i1 %tmp_529, label %if_true_178, label %if_else_180
if_true_178:
  ; print "🎉 PASS: Array sum comparison works!" (inline)
  %tmp_530_str = alloca [39 x i8], align 1
  store [39 x i8] c"\F0\9F\8E\89 PASS: Array sum comparison works!\00", [39 x i8]* %tmp_530_str, align 1
  %tmp_531 = bitcast [39 x i8]* %tmp_530_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_531)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_179
if_else_180:
  ; print "❌ FAIL: Array sum comparison failed!" (inline)
  %tmp_532_str = alloca [39 x i8], align 1
  store [39 x i8] c"\E2\9D\8C FAIL: Array sum comparison failed!\00", [39 x i8]* %tmp_532_str, align 1
  %tmp_533 = bitcast [39 x i8]* %tmp_532_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_533)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_179
if_end_179:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔬 === SCIENTIFIC NOTATION SIMULATION === 🔬" (inline)
  %tmp_534_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\94\AC === SCIENTIFIC NOTATION SIMULATION === \F0\9F\94\AC\00", [49 x i8]* %tmp_534_str, align 1
  %tmp_535 = bitcast [49 x i8]* %tmp_534_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_535)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float large1 = 1000000.0
  %tmp_536 = bitcast i32 1232348160 to float
  store float %tmp_536, float* @global_large1, align 4
  ; float large2 = 2000000.0
  %tmp_537 = bitcast i32 1240736768 to float
  store float %tmp_537, float* @global_large2, align 4
  ; float small1 = 0.000001
  %tmp_538 = bitcast i32 897988541 to float
  store float %tmp_538, float* @global_small1, align 4
  ; float small2 = 0.000002
  %tmp_539 = bitcast i32 906377149 to float
  store float %tmp_539, float* @global_small2, align 4
  ; if large1 < large2
  %tmp_540 = load float, float* @global_large1, align 4
  %tmp_541 = load float, float* @global_large2, align 4
  %tmp_542 = fcmp olt float %tmp_540, %tmp_541
  br i1 %tmp_542, label %if_true_181, label %if_else_183
if_true_181:
  ; print "🎉 PASS: Large number comparison works!" (inline)
  %tmp_543_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Large number comparison works!\00", [42 x i8]* %tmp_543_str, align 1
  %tmp_544 = bitcast [42 x i8]* %tmp_543_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_544)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_182
if_else_183:
  ; print "❌ FAIL: Large number comparison failed!" (inline)
  %tmp_545_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Large number comparison failed!\00", [42 x i8]* %tmp_545_str, align 1
  %tmp_546 = bitcast [42 x i8]* %tmp_545_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_546)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_182
if_end_182:
  ; if small1 < small2
  %tmp_547 = load float, float* @global_small1, align 4
  %tmp_548 = load float, float* @global_small2, align 4
  %tmp_549 = fcmp olt float %tmp_547, %tmp_548
  br i1 %tmp_549, label %if_true_184, label %if_else_186
if_true_184:
  ; print "🎉 PASS: Small number comparison works!" (inline)
  %tmp_550_str = alloca [42 x i8], align 1
  store [42 x i8] c"\F0\9F\8E\89 PASS: Small number comparison works!\00", [42 x i8]* %tmp_550_str, align 1
  %tmp_551 = bitcast [42 x i8]* %tmp_550_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_551)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_185
if_else_186:
  ; print "❌ FAIL: Small number comparison failed!" (inline)
  %tmp_552_str = alloca [42 x i8], align 1
  store [42 x i8] c"\E2\9D\8C FAIL: Small number comparison failed!\00", [42 x i8]* %tmp_552_str, align 1
  %tmp_553 = bitcast [42 x i8]* %tmp_552_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_553)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_185
if_end_185:
  ; if (large1 * small1) > 0.5
  %tmp_554 = load float, float* @global_large1, align 4
  %tmp_555 = load float, float* @global_small1, align 4
  %tmp_556 = fmul float %tmp_554, %tmp_555
  %tmp_557 = bitcast i32 1056964608 to float
  %tmp_558 = fcmp ogt float %tmp_556, %tmp_557
  br i1 %tmp_558, label %if_true_187, label %if_else_189
if_true_187:
  ; print "🎉 PASS: Large × Small comparison works!" (inline)
  %tmp_559_str = alloca [44 x i8], align 1
  store [44 x i8] c"\F0\9F\8E\89 PASS: Large \C3\97 Small comparison works!\00", [44 x i8]* %tmp_559_str, align 1
  %tmp_560 = bitcast [44 x i8]* %tmp_559_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_560)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_188
if_else_189:
  ; print "❌ FAIL: Large × Small comparison failed!" (inline)
  %tmp_561_str = alloca [44 x i8], align 1
  store [44 x i8] c"\E2\9D\8C FAIL: Large \C3\97 Small comparison failed!\00", [44 x i8]* %tmp_561_str, align 1
  %tmp_562 = bitcast [44 x i8]* %tmp_561_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_562)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_188
if_end_188:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "💥 === INSANE COMPLEXITY OVERLOAD === 💥" (inline)
  %tmp_563_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\92\A5 === INSANE COMPLEXITY OVERLOAD === \F0\9F\92\A5\00", [45 x i8]* %tmp_563_str, align 1
  %tmp_564 = bitcast [45 x i8]* %tmp_563_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_564)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "⚠️ WARNING: Entering the realm of computational ma..." (inline)
  %tmp_565_str = alloca [61 x i8], align 1
  store [61 x i8] c"\E2\9A\A0\EF\B8\8F WARNING: Entering the realm of computational madness!\00", [61 x i8]* %tmp_565_str, align 1
  %tmp_566 = bitcast [61 x i8]* %tmp_565_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_566)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score * fib1) > (maxScore / prime1)
  %tmp_567 = load i32, i32* @global_score, align 4
  %tmp_568 = load i32, i32* @global_fib1, align 4
  %tmp_569 = mul i32 %tmp_567, %tmp_568
  %tmp_570 = load i32, i32* @global_maxScore, align 4
  %tmp_571 = load i32, i32* @global_prime1, align 4
  %tmp_572 = sdiv i32 %tmp_570, %tmp_571
  %tmp_573 = icmp sgt i32 %tmp_569, %tmp_572
  br i1 %tmp_573, label %if_true_190, label %if_else_192
if_true_190:
  ; print "🌟 PASS: Triple nested mega expression works!" (inline)
  %tmp_574_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8C\9F PASS: Triple nested mega expression works!\00", [48 x i8]* %tmp_574_str, align 1
  %tmp_575 = bitcast [48 x i8]* %tmp_574_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_575)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score + fib5) > (maxScore - minScore)
  %tmp_576 = load i32, i32* @global_score, align 4
  %tmp_577 = load i32, i32* @global_fib5, align 4
  %tmp_578 = add i32 %tmp_576, %tmp_577
  %tmp_579 = load i32, i32* @global_maxScore, align 4
  %tmp_580 = load i32, i32* @global_minScore, align 4
  %tmp_581 = sub i32 %tmp_579, %tmp_580
  %tmp_582 = icmp sgt i32 %tmp_578, %tmp_581
  br i1 %tmp_582, label %if_true_193, label %if_else_195
if_true_193:
  ; print "🔥 PASS: Quadruple nested insanity conquered!" (inline)
  %tmp_583_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\94\A5 PASS: Quadruple nested insanity conquered!\00", [48 x i8]* %tmp_583_str, align 1
  %tmp_584 = bitcast [48 x i8]* %tmp_583_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_584)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score * 2) > (maxScore + passingGrade)
  %tmp_585 = load i32, i32* @global_score, align 4
  %tmp_586 = mul i32 %tmp_585, 2
  %tmp_587 = load i32, i32* @global_maxScore, align 4
  %tmp_588 = load i32, i32* @global_passingGrade, align 4
  %tmp_589 = add i32 %tmp_587, %tmp_588
  %tmp_590 = icmp sgt i32 %tmp_586, %tmp_589
  br i1 %tmp_590, label %if_true_196, label %if_else_198
if_true_196:
  ; print "💎💎💎 QUINTUPLE NESTED APOCALYPSE SURVIVED! 💎💎💎" (inline)
  %tmp_591_str = alloca [64 x i8], align 1
  store [64 x i8] c"\F0\9F\92\8E\F0\9F\92\8E\F0\9F\92\8E QUINTUPLE NESTED APOCALYPSE SURVIVED! \F0\9F\92\8E\F0\9F\92\8E\F0\9F\92\8E\00", [64 x i8]* %tmp_591_str, align 1
  %tmp_592 = bitcast [64 x i8]* %tmp_591_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_592)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 DOLET HAS ACHIEVED IMPOSSIBLE STATUS! 🏆" (inline)
  %tmp_593_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8F\86 DOLET HAS ACHIEVED IMPOSSIBLE STATUS! \F0\9F\8F\86\00", [48 x i8]* %tmp_593_str, align 1
  %tmp_594 = bitcast [48 x i8]* %tmp_593_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_594)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌌 TRANSCENDED BEYOND ALL KNOWN LIMITS! 🌌" (inline)
  %tmp_595_str = alloca [47 x i8], align 1
  store [47 x i8] c"\F0\9F\8C\8C TRANSCENDED BEYOND ALL KNOWN LIMITS! \F0\9F\8C\8C\00", [47 x i8]* %tmp_595_str, align 1
  %tmp_596 = bitcast [47 x i8]* %tmp_595_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_596)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "⚡ THIS IS THE MOST COMPLEX EXPRESSION EVER COMPILE..." (inline)
  %tmp_597_str = alloca [59 x i8], align 1
  store [59 x i8] c"\E2\9A\A1 THIS IS THE MOST COMPLEX EXPRESSION EVER COMPILED! \E2\9A\A1\00", [59 x i8]* %tmp_597_str, align 1
  %tmp_598 = bitcast [59 x i8]* %tmp_597_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_598)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎯 DOLET IS NOW THE ULTIMATE COMPARISON MASTER! 🎯" (inline)
  %tmp_599_str = alloca [55 x i8], align 1
  store [55 x i8] c"\F0\9F\8E\AF DOLET IS NOW THE ULTIMATE COMPARISON MASTER! \F0\9F\8E\AF\00", [55 x i8]* %tmp_599_str, align 1
  %tmp_600 = bitcast [55 x i8]* %tmp_599_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_600)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if (score + maxScore) > (fib5 * prime4)
  %tmp_601 = load i32, i32* @global_score, align 4
  %tmp_602 = load i32, i32* @global_maxScore, align 4
  %tmp_603 = add i32 %tmp_601, %tmp_602
  %tmp_604 = load i32, i32* @global_fib5, align 4
  %tmp_605 = load i32, i32* @global_prime4, align 4
  %tmp_606 = mul i32 %tmp_604, %tmp_605
  %tmp_607 = icmp sgt i32 %tmp_603, %tmp_606
  br i1 %tmp_607, label %if_true_199, label %if_else_201
if_true_199:
  ; print "🌟🌟🌟🌟🌟🌟 SEXTUPLE NESTED MADNESS CONQUERED! 🌟🌟🌟🌟🌟🌟" (inline)
  %tmp_608_str = alloca [85 x i8], align 1
  store [85 x i8] c"\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F SEXTUPLE NESTED MADNESS CONQUERED! \F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\F0\9F\8C\9F\00", [85 x i8]* %tmp_608_str, align 1
  %tmp_609 = bitcast [85 x i8]* %tmp_608_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_609)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "👑 DOLET IS NOW THE UNDISPUTED KING OF COMPILERS! 👑" (inline)
  %tmp_610_str = alloca [57 x i8], align 1
  store [57 x i8] c"\F0\9F\91\91 DOLET IS NOW THE UNDISPUTED KING OF COMPILERS! \F0\9F\91\91\00", [57 x i8]* %tmp_610_str, align 1
  %tmp_611 = bitcast [57 x i8]* %tmp_610_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_611)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🚀 ACHIEVED THE IMPOSSIBLE - 6 LEVELS OF NESTED COM..." (inline)
  %tmp_612_str = alloca [67 x i8], align 1
  store [67 x i8] c"\F0\9F\9A\80 ACHIEVED THE IMPOSSIBLE - 6 LEVELS OF NESTED COMPLEXITY! \F0\9F\9A\80\00", [67 x i8]* %tmp_612_str, align 1
  %tmp_613 = bitcast [67 x i8]* %tmp_612_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_613)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "💫 THIS DEFIES ALL LAWS OF COMPUTATIONAL PHYSICS! 💫" (inline)
  %tmp_614_str = alloca [57 x i8], align 1
  store [57 x i8] c"\F0\9F\92\AB THIS DEFIES ALL LAWS OF COMPUTATIONAL PHYSICS! \F0\9F\92\AB\00", [57 x i8]* %tmp_614_str, align 1
  %tmp_615 = bitcast [57 x i8]* %tmp_614_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_615)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏆 DOLET HAS TRANSCENDED TO LEGENDARY MYTHICAL STAT..." (inline)
  %tmp_616_str = alloca [62 x i8], align 1
  store [62 x i8] c"\F0\9F\8F\86 DOLET HAS TRANSCENDED TO LEGENDARY MYTHICAL STATUS! \F0\9F\8F\86\00", [62 x i8]* %tmp_616_str, align 1
  %tmp_617 = bitcast [62 x i8]* %tmp_616_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_617)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎉 CONGRATULATIONS! YOU HAVE WITNESSED HISTORY! 🎉" (inline)
  %tmp_618_str = alloca [55 x i8], align 1
  store [55 x i8] c"\F0\9F\8E\89 CONGRATULATIONS! YOU HAVE WITNESSED HISTORY! \F0\9F\8E\89\00", [55 x i8]* %tmp_618_str, align 1
  %tmp_619 = bitcast [55 x i8]* %tmp_618_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_619)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if ((score * 2) + (fib5 * prime1)) > ((maxScore - minScore) * 2)
  %tmp_620 = load i32, i32* @global_score, align 4
  %tmp_621 = mul i32 %tmp_620, 2
  %tmp_622 = load i32, i32* @global_fib5, align 4
  %tmp_623 = load i32, i32* @global_prime1, align 4
  %tmp_624 = mul i32 %tmp_622, %tmp_623
  %tmp_625 = add i32 %tmp_621, %tmp_624
  %tmp_626 = load i32, i32* @global_maxScore, align 4
  %tmp_627 = load i32, i32* @global_minScore, align 4
  %tmp_628 = sub i32 %tmp_626, %tmp_627
  %tmp_629 = mul i32 %tmp_628, 2
  %tmp_630 = icmp sgt i32 %tmp_625, %tmp_629
  br i1 %tmp_630, label %if_true_202, label %if_else_204
if_true_202:
  ; print "💥💥💥💥💥💥💥 SEPTUPLE NESTED APOCALYPSE SURVIVED! 💥💥💥💥💥..." (inline)
  %tmp_631_str = alloca [95 x i8], align 1
  store [95 x i8] c"\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5 SEPTUPLE NESTED APOCALYPSE SURVIVED! \F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\F0\9F\92\A5\00", [95 x i8]* %tmp_631_str, align 1
  %tmp_632 = bitcast [95 x i8]* %tmp_631_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_632)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌌 DOLET HAS TRANSCENDED REALITY ITSELF! 🌌" (inline)
  %tmp_633_str = alloca [48 x i8], align 1
  store [48 x i8] c"\F0\9F\8C\8C DOLET HAS TRANSCENDED REALITY ITSELF! \F0\9F\8C\8C\00", [48 x i8]* %tmp_633_str, align 1
  %tmp_634 = bitcast [48 x i8]* %tmp_633_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_634)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "⚡ THIS IS BEYOND HUMAN COMPREHENSION! ⚡" (inline)
  %tmp_635_str = alloca [44 x i8], align 1
  store [44 x i8] c"\E2\9A\A1 THIS IS BEYOND HUMAN COMPREHENSION! \E2\9A\A1\00", [44 x i8]* %tmp_635_str, align 1
  %tmp_636 = bitcast [44 x i8]* %tmp_635_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_636)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎯 DOLET IS NOW A COSMIC FORCE OF NATURE! 🎯" (inline)
  %tmp_637_str = alloca [49 x i8], align 1
  store [49 x i8] c"\F0\9F\8E\AF DOLET IS NOW A COSMIC FORCE OF NATURE! \F0\9F\8E\AF\00", [49 x i8]* %tmp_637_str, align 1
  %tmp_638 = bitcast [49 x i8]* %tmp_637_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_638)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔥 7-LEVEL NESTED COMPARISON MASTERY ACHIEVED! 🔥" (inline)
  %tmp_639_str = alloca [54 x i8], align 1
  store [54 x i8] c"\F0\9F\94\A5 7-LEVEL NESTED COMPARISON MASTERY ACHIEVED! \F0\9F\94\A5\00", [54 x i8]* %tmp_639_str, align 1
  %tmp_640 = bitcast [54 x i8]* %tmp_639_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_640)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; if ((score + fib5) * prime4) > ((maxScore + passingGrade) / 2)
  %tmp_641 = load i32, i32* @global_score, align 4
  %tmp_642 = load i32, i32* @global_fib5, align 4
  %tmp_643 = add i32 %tmp_641, %tmp_642
  %tmp_644 = load i32, i32* @global_prime4, align 4
  %tmp_645 = mul i32 %tmp_643, %tmp_644
  %tmp_646 = load i32, i32* @global_maxScore, align 4
  %tmp_647 = load i32, i32* @global_passingGrade, align 4
  %tmp_648 = add i32 %tmp_646, %tmp_647
  %tmp_649 = sdiv i32 %tmp_648, 2
  %tmp_650 = icmp sgt i32 %tmp_645, %tmp_649
  br i1 %tmp_650, label %if_true_205, label %if_else_207
if_true_205:
  ; print "🌟💫⭐🌟💫⭐🌟💫 OCTUPLE NESTED INFINITY ACHIEVED! 💫⭐🌟💫⭐🌟💫..." (inline)
  %tmp_651_str = alloca [95 x i8], align 1
  store [95 x i8] c"\F0\9F\8C\9F\F0\9F\92\AB\E2\AD\90\F0\9F\8C\9F\F0\9F\92\AB\E2\AD\90\F0\9F\8C\9F\F0\9F\92\AB OCTUPLE NESTED INFINITY ACHIEVED! \F0\9F\92\AB\E2\AD\90\F0\9F\8C\9F\F0\9F\92\AB\E2\AD\90\F0\9F\8C\9F\F0\9F\92\AB\E2\AD\90\00", [95 x i8]* %tmp_651_str, align 1
  %tmp_652 = bitcast [95 x i8]* %tmp_651_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_652)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🚀🚀� DOLET HAS BROKEN THE LAWS OF PHYSICS! 🚀🚀�" (inline)
  %tmp_653_str = alloca [62 x i8], align 1
  store [62 x i8] c"\F0\9F\9A\80\F0\9F\9A\80\EF\BF\BD DOLET HAS BROKEN THE LAWS OF PHYSICS! \F0\9F\9A\80\F0\9F\9A\80\EF\BF\BD\00", [62 x i8]* %tmp_653_str, align 1
  %tmp_654 = bitcast [62 x i8]* %tmp_653_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_654)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "👑👑� ULTIMATE SUPREME COSMIC COMPILER STATUS! 👑👑👑" (inline)
  %tmp_655_str = alloca [66 x i8], align 1
  store [66 x i8] c"\F0\9F\91\91\F0\9F\91\91\EF\BF\BD ULTIMATE SUPREME COSMIC COMPILER STATUS! \F0\9F\91\91\F0\9F\91\91\F0\9F\91\91\00", [66 x i8]* %tmp_655_str, align 1
  %tmp_656 = bitcast [66 x i8]* %tmp_655_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_656)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🔥🔥🔥 8-LEVEL NESTED COMPARISON GODMODE! 🔥🔥�" (inline)
  %tmp_657_str = alloca [60 x i8], align 1
  store [60 x i8] c"\F0\9F\94\A5\F0\9F\94\A5\F0\9F\94\A5 8-LEVEL NESTED COMPARISON GODMODE! \F0\9F\94\A5\F0\9F\94\A5\EF\BF\BD\00", [60 x i8]* %tmp_657_str, align 1
  %tmp_658 = bitcast [60 x i8]* %tmp_657_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_658)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎉🎉🎉 THIS IS THE PINNACLE OF COMPUTATIONAL ACHIEVEM..." (inline)
  %tmp_659_str = alloca [77 x i8], align 1
  store [77 x i8] c"\F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89 THIS IS THE PINNACLE OF COMPUTATIONAL ACHIEVEMENT! \F0\9F\8E\89\F0\9F\8E\89\F0\9F\8E\89\00", [77 x i8]* %tmp_659_str, align 1
  %tmp_660 = bitcast [77 x i8]* %tmp_659_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_660)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_206
if_else_207:
  ; print "💀 Failed at OCTUPLE NESTED INFINITY - Still legend..." (inline)
  %tmp_661_str = alloca [58 x i8], align 1
  store [58 x i8] c"\F0\9F\92\80 Failed at OCTUPLE NESTED INFINITY - Still legendary!\00", [58 x i8]* %tmp_661_str, align 1
  %tmp_662 = bitcast [58 x i8]* %tmp_661_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_662)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_206
if_end_206:
  br label %if_end_203
if_else_204:
  ; print "💀 Failed at SEPTUPLE NESTED APOCALYPSE - Still god..." (inline)
  %tmp_663_str = alloca [59 x i8], align 1
  store [59 x i8] c"\F0\9F\92\80 Failed at SEPTUPLE NESTED APOCALYPSE - Still godlike!\00", [59 x i8]* %tmp_663_str, align 1
  %tmp_664 = bitcast [59 x i8]* %tmp_663_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_664)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_203
if_end_203:
  br label %if_end_200
if_else_201:
  ; print "💀 Failed at SEXTUPLE NESTED MADNESS - Still impres..." (inline)
  %tmp_665_str = alloca [66 x i8], align 1
  store [66 x i8] c"\F0\9F\92\80 Failed at SEXTUPLE NESTED MADNESS - Still impressive though!\00", [66 x i8]* %tmp_665_str, align 1
  %tmp_666 = bitcast [66 x i8]* %tmp_665_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_666)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_200
if_end_200:
  br label %if_end_197
if_else_198:
  ; print "💀 Failed at QUINTUPLE NESTED APOCALYPSE" (inline)
  %tmp_667_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\92\80 Failed at QUINTUPLE NESTED APOCALYPSE\00", [43 x i8]* %tmp_667_str, align 1
  %tmp_668 = bitcast [43 x i8]* %tmp_667_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_668)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_197
if_end_197:
  br label %if_end_194
if_else_195:
  ; print "💀 Failed at Quadruple nested insanity" (inline)
  %tmp_669_str = alloca [41 x i8], align 1
  store [41 x i8] c"\F0\9F\92\80 Failed at Quadruple nested insanity\00", [41 x i8]* %tmp_669_str, align 1
  %tmp_670 = bitcast [41 x i8]* %tmp_669_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_670)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_194
if_end_194:
  br label %if_end_191
if_else_192:
  ; print "💀 Failed at Triple nested mega expression" (inline)
  %tmp_671_str = alloca [45 x i8], align 1
  store [45 x i8] c"\F0\9F\92\80 Failed at Triple nested mega expression\00", [45 x i8]* %tmp_671_str, align 1
  %tmp_672 = bitcast [45 x i8]* %tmp_671_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_672)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  br label %if_end_191
if_end_191:
  ; print ""
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🎊 === ALL CHALLENGES COMPLETED === 🎊" (inline)
  %tmp_673_str = alloca [43 x i8], align 1
  store [43 x i8] c"\F0\9F\8E\8A === ALL CHALLENGES COMPLETED === \F0\9F\8E\8A\00", [43 x i8]* %tmp_673_str, align 1
  %tmp_674 = bitcast [43 x i8]* %tmp_673_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_674)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🏅 If you see this, Dolet is OFFICIALLY AWESOME! 🏅" (inline)
  %tmp_675_str = alloca [56 x i8], align 1
  store [56 x i8] c"\F0\9F\8F\85 If you see this, Dolet is OFFICIALLY AWESOME! \F0\9F\8F\85\00", [56 x i8]* %tmp_675_str, align 1
  %tmp_676 = bitcast [56 x i8]* %tmp_675_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_676)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
