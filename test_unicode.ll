; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 9 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_expr_0 = private unnamed_addr constant [8 x i8] c" Math: \00", align 1
@.str_expr_1 = private unnamed_addr constant [4 x i8] c" + \00", align 1
@.str_expr_2 = private unnamed_addr constant [4 x i8] c" = \00", align 1
@.str_expr_3 = private unnamed_addr constant [2 x i8] c" \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_x = global i32 0, align 4
@global_y = global i32 0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "🎉 Hello World! 🚀" (inline)
  %tmp_1_str = alloca [15 x i8], align 1
  store [15 x i8] c" Hello World! \00", [15 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [15 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "✅ This has emojis ❌ but will work fine 💯" (inline)
  %tmp_3_str = alloca [38 x i8], align 1
  store [38 x i8] c" This has emojis  but will work fine \00", [38 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [38 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Arabic: مرحبا بالعالم" (inline)
  %tmp_5_str = alloca [10 x i8], align 1
  store [10 x i8] c"Arabic:  \00", [10 x i8]* %tmp_5_str, align 1
  %tmp_6 = bitcast [10 x i8]* %tmp_5_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Chinese: 你好世界" (inline)
  %tmp_7_str = alloca [10 x i8], align 1
  store [10 x i8] c"Chinese: \00", [10 x i8]* %tmp_7_str, align 1
  %tmp_8 = bitcast [10 x i8]* %tmp_7_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Regular text works fine" (inline)
  %tmp_9_str = alloca [24 x i8], align 1
  store [24 x i8] c"Regular text works fine\00", [24 x i8]* %tmp_9_str, align 1
  %tmp_10 = bitcast [24 x i8]* %tmp_9_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int x = 10
  store i32 10, i32* @global_x, align 4
  ; int y = 20
  store i32 20, i32* @global_y, align 4
  ; print expression: "🔢 Math: " + x + " + " + y + " = " + (x + y) + " 🎯...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_11 = load i32, i32* @global_x, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_11)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_12 = load i32, i32* @global_y, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_12)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_13 = load i32, i32* @global_x, align 4
  %tmp_14 = load i32, i32* @global_y, align 4
  %tmp_15 = add i32 %tmp_13, %tmp_14
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_expr_3, i32 0, i32 0))
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "🌟 Unicode will be removed but text remains! 🌟" (inline)
  %tmp_16_str = alloca [44 x i8], align 1
  store [44 x i8] c" Unicode will be removed but text remains! \00", [44 x i8]* %tmp_16_str, align 1
  %tmp_17 = bitcast [44 x i8]* %tmp_16_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_17)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
