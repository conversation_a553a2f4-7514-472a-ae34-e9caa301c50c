// 1. Variables & Data Types - All supported types (Rust version)
// This file demonstrates all variable types in Rust for comparison with Dolet

fn main() {
    println!("=== Variables & Data Types Demo (Rust) ===");
    println!();

    // Integer variables
    let age: i32 = 25;
    let year: i32 = 2024;
    let negative: i32 = -100;
    println!("Integer variables:");
    println!("Age: {}", age);
    println!("Year: {}", year);
    println!("Negative: {}", negative);
    println!();

    // String variables
    let name: &str = "Ahmed";
    let city: String = String::from("Cairo");
    let empty: String = String::new();
    let special: &str = "Hello, World! 123 @#$";
    println!("String variables:");
    println!("Name: {}", name);
    println!("City: {}", city);
    println!("Empty: '{}'", empty);
    println!("Special: {}", special);
    println!();

    // Float variables
    let price: f32 = 19.99;
    let pi: f32 = 3.14159;
    let negative_float: f32 = -2.5;
    println!("Float variables:");
    println!("Price: {}", price);
    println!("Pi: {}", pi);
    println!("Negative float: {}", negative_float);
    println!();

    // Double variables (f64 in Rust)
    let precise: f64 = 3.141592653589793;
    let large: f64 = 1234567.89012345;
    println!("Double variables:");
    println!("Precise: {}", precise);
    println!("Large: {}", large);
    println!();

    // Character variables
    let letter: char = 'A';
    let digit: char = '5';
    let symbol: char = '@';
    println!("Character variables:");
    println!("Letter: {}", letter);
    println!("Digit: {}", digit);
    println!("Symbol: {}", symbol);
    println!();

    // Boolean variables
    let active: bool = true;
    let finished: bool = false;
    let result: bool = true;
    println!("Boolean variables:");
    println!("Active: {}", active);
    println!("Finished: {}", finished);
    println!("Result: {}", result);
    println!();

    // Variable modification (mutable variables)
    println!("=== Variable Modification ===");
    let mut age_mut = age;
    let mut name_mut = String::from(name);
    let mut price_mut = price;
    let mut active_mut = active;
    
    age_mut = 26;
    name_mut = String::from("Ali");
    price_mut = 29.99;
    active_mut = false;
    
    println!("After modification:");
    println!("New age: {}", age_mut);
    println!("New name: {}", name_mut);
    println!("New price: {}", price_mut);
    println!("New active: {}", active_mut);
    println!();

    // Multiple variables of same type
    let x: i32 = 10;
    let y: i32 = 20;
    let z: i32 = 30;
    println!("Multiple integers: {}, {}, {}", x, y, z);

    let first: &str = "Hello";
    let second: &str = "Beautiful";
    let third: &str = "World";
    println!("Multiple strings: {} {} {}", first, second, third);

    println!();
    println!("=== End of Variables & Data Types Demo ===");
}
