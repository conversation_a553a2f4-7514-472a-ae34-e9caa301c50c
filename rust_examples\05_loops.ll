; ModuleID = '05_loops.ff7ade9c07cda235-cgu.0'
source_filename = "05_loops.ff7ade9c07cda235-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@alloc_31365cfefba383c4d2bf6b6a04cc10aa = private unnamed_addr constant [17 x i8] c"capacity overflow", align 1
@alloc_11d257f5ed6cc7fc38feaa801053bac6 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_31365cfefba383c4d2bf6b6a04cc10aa, [8 x i8] c"\11\00\00\00\00\00\00\00" }>, align 8
@anon.a8415ff6e4c3370c8d8c02329ada54cc.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h5b81b2aeb508600eE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h488a8ad91c5f3a4dE", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h488a8ad91c5f3a4dE" }>, align 8
@anon.a8415ff6e4c3370c8d8c02329ada54cc.1 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_d9f760e35047ce9b69375ccc90650d26 = private unnamed_addr constant [78 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\range.rs", align 1
@alloc_dda2737f33a4b1c1d39fa523d13a220a = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_d9f760e35047ce9b69375ccc90650d26, [16 x i8] c"N\00\00\00\00\00\00\00\AA\01\00\00\01\00\00\00" }>, align 8
@alloc_dd79dfae92e8fdc23813c4c7a1b7cf72 = private unnamed_addr constant [228 x i8] c"unsafe precondition(s) violated: ptr::write_bytes requires that the destination pointer is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@vtable.1 = private unnamed_addr constant <{ [24 x i8], ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h38ff3bfe970276caE" }>, align 8
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@anon.a8415ff6e4c3370c8d8c02329ada54cc.2 = private unnamed_addr constant [16 x i8] c"\01\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00", align 8
@alloc_299852982c5db63901a556a4e2fe0f7e = private unnamed_addr constant [88 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\iterator.rs", align 1
@alloc_7277ca8d30122b7203dfd66509383767 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_299852982c5db63901a556a4e2fe0f7e, [16 x i8] c"X\00\00\00\00\00\00\00\C1\07\00\00\09\00\00\00" }>, align 8
@alloc_4aead6e2018a46d0df208d5729447de7 = private unnamed_addr constant [27 x i8] c"assertion failed: step != 0", align 1
@alloc_bdb8a431bd51e87cc946baf5a9712b61 = private unnamed_addr constant [89 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\adapters\\step_by.rs", align 1
@alloc_1d19af47b700b7580386eededda18b7b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_bdb8a431bd51e87cc946baf5a9712b61, [16 x i8] c"Y\00\00\00\00\00\00\00#\00\00\00\09\00\00\00" }>, align 8
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@anon.a8415ff6e4c3370c8d8c02329ada54cc.3 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] c"\01\00\00\00\00\00\00\80", [8 x i8] undef }>, align 8
@alloc_00e5a13bfec3eaffacf28cad02b1dee1 = private unnamed_addr constant [80 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\raw_vec\\mod.rs", align 1
@alloc_3d99a694a639f36d512dfe286335fb89 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_00e5a13bfec3eaffacf28cad02b1dee1, [16 x i8] c"P\00\00\00\00\00\00\00.\02\00\00\11\00\00\00" }>, align 8
@alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8 = private unnamed_addr constant [85 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\accum.rs", align 1
@alloc_2ff564f83739a041825038989c62f69d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8, [16 x i8] c"U\00\00\00\00\00\00\00\95\00\00\00\01\00\00\00" }>, align 8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@alloc_38bd6a3b9bf7e43e415142adb3afe53c = private unnamed_addr constant [26 x i8] c"=== Loops Demo (Rust) ===\0A", align 1
@alloc_6e52b505a51406609023bcebfd1fca2b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_38bd6a3b9bf7e43e415142adb3afe53c, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9eea0ab353f745b23e98a4ca3bd7f5cb = private unnamed_addr constant [37 x i8] c"Simple while loop (counting 1 to 5):\0A", align 1
@alloc_6760f45ebbe0fa7de952b2d501781225 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9eea0ab353f745b23e98a4ca3bd7f5cb, [8 x i8] c"%\00\00\00\00\00\00\00" }>, align 8
@alloc_ec87f13ce8f16fa6dce1e765a6b8d239 = private unnamed_addr constant [47 x i8] c"While loop with condition (countdown from 10):\0A", align 1
@alloc_9a8f9864ba7eb3002bddca72bb5ac004 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ec87f13ce8f16fa6dce1e765a6b8d239, [8 x i8] c"/\00\00\00\00\00\00\00" }>, align 8
@alloc_07d85731e386b10589e2e90f3b9e2cab = private unnamed_addr constant [11 x i8] c"Blast off!\0A", align 1
@alloc_48f68c82c0a8f1e86b727a82ab78a744 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_07d85731e386b10589e2e90f3b9e2cab, [8 x i8] c"\0B\00\00\00\00\00\00\00" }>, align 8
@alloc_2e2dfd72e41bf3e7ccd4e021437e1a21 = private unnamed_addr constant [30 x i8] c"For loop with range (0 to 4):\0A", align 1
@alloc_f04dd6a3deeff2fa3d370bdeb8488bdd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2e2dfd72e41bf3e7ccd4e021437e1a21, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_9da7fe6aa6038fbe2626375cd51570c1 = private unnamed_addr constant [31 x i8] c"For loop with range (1 to 10):\0A", align 1
@alloc_d81bf84857e314d51972dcfbd89fdab8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9da7fe6aa6038fbe2626375cd51570c1, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_1aeba9cb890a85bbc05fc6ba1f1769af = private unnamed_addr constant [42 x i8] c"For loop counting by 2s (2, 4, 6, 8, 10):\0A", align 1
@alloc_4b644afd3b2d7bf138ebcff9a7522c01 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1aeba9cb890a85bbc05fc6ba1f1769af, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@alloc_4ff7fd283ba2c541e125497c4d6f2c9b = private unnamed_addr constant [47 x i8] c"Nested while loops (multiplication table 1-3):\0A", align 1
@alloc_8c21df12b0f05eafb2c669d6959c6be7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4ff7fd283ba2c541e125497c4d6f2c9b, [8 x i8] c"/\00\00\00\00\00\00\00" }>, align 8
@alloc_99b468ffc06e605ca645b00cc671a52b = private unnamed_addr constant [33 x i8] c"Nested for loops (grid pattern):\0A", align 1
@alloc_86706144e054f4abce370fdbc482cf4b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_99b468ffc06e605ca645b00cc671a52b, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_18897d2ec50d8bfbd682f4ae2f8b215f = private unnamed_addr constant [35 x i8] c"While loop with complex condition:\0A", align 1
@alloc_84704fa8740720bd5fd5fec22ce2b617 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_18897d2ec50d8bfbd682f4ae2f8b215f, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_9d6aea1215e152e3a1207e8e84b9a898 = private unnamed_addr constant [11 x i8] c"Final sum: ", align 1
@alloc_a5213196877d3a44725d0f5190d7c0de = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9d6aea1215e152e3a1207e8e84b9a898, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ea2aa10f81eed193d285db8a5775d3b9 = private unnamed_addr constant [38 x i8] c"For loop with calculations (squares):\0A", align 1
@alloc_4c334eb58b7b61dfa11577ff45d06830 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ea2aa10f81eed193d285db8a5775d3b9, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_33cdd57b7cde4aefce9cb1f663e52531 = private unnamed_addr constant [40 x i8] c"While loop simulation (finding target):\0A", align 1
@alloc_eeaaa0fbe8f7fa656378185f1b88656f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_33cdd57b7cde4aefce9cb1f663e52531, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_e110bf147c90be30ed2cd1599d716a73 = private unnamed_addr constant [14 x i8] c"Found target: ", align 1
@alloc_e6a643577c5a7fc17b4d668be1720af4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e110bf147c90be30ed2cd1599d716a73, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5ca28249dd2c3924e875f1ecabf9d31f = private unnamed_addr constant [30 x i8] c"Nested loops with conditions:\0A", align 1
@alloc_309fdb2dffcc123ef582b2c35b476cb7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5ca28249dd2c3924e875f1ecabf9d31f, [8 x i8] c"\1E\00\00\00\00\00\00\00" }>, align 8
@alloc_da16f1c8ad01782c31c90e83d109b3f8 = private unnamed_addr constant [36 x i8] c"While loop with early exit (break):\0A", align 1
@alloc_82dc11428f9c4f9b0d1ec1ba23d86ace = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_da16f1c8ad01782c31c90e83d109b3f8, [8 x i8] c"$\00\00\00\00\00\00\00" }>, align 8
@alloc_a81dd1517ad2f5f40f167de2bcaea372 = private unnamed_addr constant [26 x i8] c"Found target at position: ", align 1
@alloc_555e600324efd390c30e2d72a0a9e702 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a81dd1517ad2f5f40f167de2bcaea372, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8f32b58b1a022feef72488a044c2ea53 = private unnamed_addr constant [45 x i8] c"For loop with accumulation (factorial of 5):\0A", align 1
@alloc_33d0ecfa5a33cfb6212a5725dea1b18a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8f32b58b1a022feef72488a044c2ea53, [8 x i8] c"-\00\00\00\00\00\00\00" }>, align 8
@alloc_a0461c6ba301bbf8fdcb006c0e3d44f8 = private unnamed_addr constant [22 x i8] c"Final factorial of 5: ", align 1
@alloc_71fa40ebab23c1c6653dacffb3f013e9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a0461c6ba301bbf8fdcb006c0e3d44f8, [8 x i8] c"\16\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_eaa5ebfff8882218dc6d4b8da705f436 = private unnamed_addr constant [40 x i8] c"Complex nested loop (pattern printing):\0A", align 1
@alloc_3448d66ec8d3b571366ffcbfc5278139 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_eaa5ebfff8882218dc6d4b8da705f436, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_4e82ce4af826ea2ab5e063a094210a9d = private unnamed_addr constant [36 x i8] c"While loop with multiple variables:\0A", align 1
@alloc_26ac04c73b91fa604361572276cbceb9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4e82ce4af826ea2ab5e063a094210a9d, [8 x i8] c"$\00\00\00\00\00\00\00" }>, align 8
@alloc_a1c4264237be63c866838cc063fb1613 = private unnamed_addr constant [9 x i8] c"Final: a=", align 1
@alloc_1bf56508c9d6ee26fef6c59b492dd8c3 = private unnamed_addr constant [4 x i8] c", b=", align 1
@alloc_3f78c67e50371bc27a576567dba254dd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a1c4264237be63c866838cc063fb1613, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_1bf56508c9d6ee26fef6c59b492dd8c3, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_239e024d23f720ec1fdca17db66ea8c9 = private unnamed_addr constant [33 x i8] c"For loop with string operations:\0A", align 1
@alloc_356194ee78976e0164244f86aaf7c5a4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_239e024d23f720ec1fdca17db66ea8c9, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_3edef0b68cfa9c8c95e6d4fe1a68842b = private unnamed_addr constant [5 x i8] c"Hello", align 1
@alloc_fc5223d82bf94cff9d1f424cd29f1aad = private unnamed_addr constant [34 x i8] c"Iterator methods (Rust-specific):\0A", align 1
@alloc_20604c4be05940d6c2566401e2c9f83d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fc5223d82bf94cff9d1f424cd29f1aad, [8 x i8] c"\22\00\00\00\00\00\00\00" }>, align 8
@alloc_97923f1fbe574e6a4d2a7ae4141260f0 = private unnamed_addr constant [13 x i8] c"Sum of 1-10: ", align 1
@alloc_0eb58bfbd806c8d45695a5721ce4b7d2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_97923f1fbe574e6a4d2a7ae4141260f0, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_649c6f5263df1b673345a6db8983a4c9 = private unnamed_addr constant [14 x i8] c"Even numbers: ", align 1
@alloc_3dca2e8af32c5067fd0f53ddd35ea51d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_649c6f5263df1b673345a6db8983a4c9, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_750e7dd1bc04abed1d598dd9ba6e9758 = private unnamed_addr constant [26 x i8] c"=== End of Loops Demo ===\0A", align 1
@alloc_b80cbda912480b37d5696acf4f3509e8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_750e7dd1bc04abed1d598dd9ba6e9758, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_a0434d70b15bcc9ff1a7b717be16ed72 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9771be2481f51be410bd2ac520d18601 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9a0ea554a36753cc5c46d3665723d339 = private unnamed_addr constant [2 x i8] c"a=", align 1
@alloc_195bbaf1e8c8ea9f10b1914eb4ca4efb = private unnamed_addr constant [13 x i8] c", difference=", align 1
@alloc_fe98b5f9324d6badf224de6d68fc2a26 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9a0ea554a36753cc5c46d3665723d339, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_1bf56508c9d6ee26fef6c59b492dd8c3, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_195bbaf1e8c8ea9f10b1914eb4ca4efb, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1581a0e88ded8f1c1c8221547cf4e46e = private unnamed_addr constant [11 x i8] c"05_loops.rs", align 1
@alloc_34705104e3cf75e7772685e41ac6f1f1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\9F\00\00\005\00\00\00" }>, align 8
@alloc_f559d2581e904f6e48b8f1c2bf2b18ab = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\A0\00\00\00\09\00\00\00" }>, align 8
@alloc_7bf6baa429897ec84b4126bfe3868d25 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\A1\00\00\00\09\00\00\00" }>, align 8
@alloc_09f68c7ae2e79d4408ae53b7c9133001 = private unnamed_addr constant [1 x i8] c"*", align 1
@alloc_56173ae41eedeb97a9658f94134753cd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_09f68c7ae2e79d4408ae53b7c9133001, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_30edf13ca54f324c7427a6208ccfbf30 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\8A\00\00\00\09\00\00\00" }>, align 8
@alloc_1abac032049c0da80ef1239201ffaef4 = private unnamed_addr constant [5 x i8] c"Step ", align 1
@alloc_f8b505dea1a2260528a099ab0c82af1c = private unnamed_addr constant [14 x i8] c": factorial = ", align 1
@alloc_a916c854665ddb9c4d8c9c3cd1554989 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1abac032049c0da80ef1239201ffaef4, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_f8b505dea1a2260528a099ab0c82af1c, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_92f7c8ea11c6e7901966825e61503390 = private unnamed_addr constant [20 x i8] c"Searching position: ", align 1
@alloc_1396c82186ed518c11ba45476efd85ea = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_92f7c8ea11c6e7901966825e61503390, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0c8119fda4534d9098b9bd5c51d37762 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\81\00\00\00\0D\00\00\00" }>, align 8
@alloc_ee67017e98d33295474a38c183c12377 = private unnamed_addr constant [13 x i8] c"Outer loop i=", align 1
@alloc_ff764d85c05e8fa0e78b421c9beddd21 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ee67017e98d33295474a38c183c12377, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_878fa686fc9e04ed465dd0d19f50b1e5 = private unnamed_addr constant [4 x i8] c"  i=", align 1
@alloc_3699b88b7cd53d20ed6c740ee712a86a = private unnamed_addr constant [4 x i8] c", j=", align 1
@alloc_1abde274a31b2a907398fe0dc7a7643f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_878fa686fc9e04ed465dd0d19f50b1e5, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_3699b88b7cd53d20ed6c740ee712a86a, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b5dabf4be92109a649fbc5be2f58c327 = private unnamed_addr constant [14 x i8] c"  i equals j: ", align 1
@alloc_e5b8a53e9f757f2b8fafb9122aa0467e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b5dabf4be92109a649fbc5be2f58c327, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d4c84f6a99b02bfad63e2071d5dcc1f5 = private unnamed_addr constant [7 x i8] c"Guess: ", align 1
@alloc_2bfd310045d32a90701ca8f5a92be701 = private unnamed_addr constant [18 x i8] c" (not the target)\0A", align 1
@alloc_53fc6a9fa23c657a081886c302e8c85b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d4c84f6a99b02bfad63e2071d5dcc1f5, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_2bfd310045d32a90701ca8f5a92be701, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_24887bce5ff1e7fdc2fc2f44a27180bc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00c\00\00\00\09\00\00\00" }>, align 8
@alloc_83a8188b77a5e612a8a06f7ee687af46 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00X\00\00\00\16\00\00\00" }>, align 8
@alloc_610486cce0679439c82fba119c52c9c5 = private unnamed_addr constant [11 x i8] c" squared = ", align 1
@alloc_0f2a4eb68444eb0bab98623b123a3365 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_610486cce0679439c82fba119c52c9c5, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d2ddd6360ce42f02d4bf15160e82afd3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00N\00\00\00\09\00\00\00" }>, align 8
@alloc_00b1c969fb21a4bf72eb45ccfaf4da80 = private unnamed_addr constant [2 x i8] c"x=", align 1
@alloc_cb5db4124fca05b51a56e3fd53120be7 = private unnamed_addr constant [6 x i8] c", sum=", align 1
@alloc_110e04e039bd3134eb101a41472378a9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_00b1c969fb21a4bf72eb45ccfaf4da80, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cb5db4124fca05b51a56e3fd53120be7, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6b6f0d6ba334f3a47613d8f7c41013c6 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00P\00\00\00\09\00\00\00" }>, align 8
@alloc_1ff25e6ad58d770003043bae3c4113bf = private unnamed_addr constant [1 x i8] c"(", align 1
@alloc_41a776d8d06dc2b9b0480e88fa257ffe = private unnamed_addr constant [1 x i8] c",", align 1
@alloc_ab24a6d73674488ebf5aab77ee934ad5 = private unnamed_addr constant [2 x i8] c") ", align 1
@alloc_66e3d1a9098f9c73b7441dfc8f07b36d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1ff25e6ad58d770003043bae3c4113bf, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_41a776d8d06dc2b9b0480e88fa257ffe, [8 x i8] c"\01\00\00\00\00\00\00\00", ptr @alloc_ab24a6d73674488ebf5aab77ee934ad5, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_103432b7f284626e3d535a993a6745b6 = private unnamed_addr constant [10 x i8] c"Table for ", align 1
@alloc_2c871cb4eca8f760225510900b094559 = private unnamed_addr constant [2 x i8] c":\0A", align 1
@alloc_f82224d350f7b7e724a4242c07bd6e94 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_103432b7f284626e3d535a993a6745b6, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_2c871cb4eca8f760225510900b094559, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_678427feec378691a572e2c78065244b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00;\00\00\00\09\00\00\00" }>, align 8
@alloc_828235f0e43732bae15495b87d3009f1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\007\00\00\00\1A\00\00\00" }>, align 8
@alloc_34310cabf6554cc6123dbb21242f43b8 = private unnamed_addr constant [2 x i8] c"  ", align 1
@alloc_512d341499b7f53469cfbe4313f7e34a = private unnamed_addr constant [3 x i8] c" x ", align 1
@alloc_998113dda678cc10281d5123c32c9b27 = private unnamed_addr constant [3 x i8] c" = ", align 1
@alloc_7577c04eb2bd819bb55f3535d5d42496 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_34310cabf6554cc6123dbb21242f43b8, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_512d341499b7f53469cfbe4313f7e34a, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_998113dda678cc10281d5123c32c9b27, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_be54450a55e2af8b7732e9c07bbf66af = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\009\00\00\00\0D\00\00\00" }>, align 8
@alloc_22f5e75d688a3d57d7df3384ddeff491 = private unnamed_addr constant [7 x i8] c"Value: ", align 1
@alloc_a37200dd26e9374fe4498e5684023e1f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_22f5e75d688a3d57d7df3384ddeff491, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a990e9968006a1fc6efbda6453519c1e = private unnamed_addr constant [8 x i8] c"Number: ", align 1
@alloc_e63551567d31bfd937c248be61fc4a17 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a990e9968006a1fc6efbda6453519c1e, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6a2726f4e02960fb7acceee414176320 = private unnamed_addr constant [11 x i8] c"Iteration: ", align 1
@alloc_f93362d57036eb6e3e6272ae74459296 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6a2726f4e02960fb7acceee414176320, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_052a2038c5f165e5d575887b9f967efd = private unnamed_addr constant [11 x i8] c"Countdown: ", align 1
@alloc_10015caba44e6f8e925ee543e933f28f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_052a2038c5f165e5d575887b9f967efd, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_38f405957f5b34ca827ae9c5cc4492fc = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\16\00\00\00\09\00\00\00" }>, align 8
@alloc_e781fe43f8d466a248fc82d6e0d04fb6 = private unnamed_addr constant [7 x i8] c"Count: ", align 1
@alloc_44ed4d9eee8b86b35581a8355e36756e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e781fe43f8d466a248fc82d6e0d04fb6, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_309940334c56c09cb5e621e20eec2456 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\0D\00\00\00\09\00\00\00" }>, align 8
@alloc_c43cc71148b1ec2ec05bff68521a8cdd = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_1581a0e88ded8f1c1c8221547cf4e46e, [16 x i8] c"\0B\00\00\00\00\00\00\00\B5\00\00\006\00\00\00" }>, align 8

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: uwtable
define internal { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h00c051d61eabf906E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 4
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 4 ptr @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h135975ed0343a08dE"(ptr align 8 %self)
  store ptr %0, ptr %self1, align 8
  %1 = load ptr, ptr %self1, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_4 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_4 to i1
  br i1 %4, label %bb5, label %bb4

bb5:                                              ; preds = %start
  %t = load ptr, ptr %self1, align 8
  %_0.i = load i32, ptr %t, align 4
  %5 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_0.i, ptr %5, align 4
  store i32 1, ptr %_0, align 4
  br label %bb2

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb2

bb2:                                              ; preds = %bb5, %bb4
  %6 = load i32, ptr %_0, align 4
  %7 = getelementptr inbounds i8, ptr %_0, i64 4
  %8 = load i32, ptr %7, align 4
  %9 = insertvalue { i32, i32 } poison, i32 %6, 0
  %10 = insertvalue { i32, i32 } %9, i32 %8, 1
  ret { i32, i32 } %10

bb3:                                              ; No predecessors!
  unreachable
}

; <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: uwtable
define internal void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h160c25b23dcd7145E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #0 {
start:
; call <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h8e55e0427d592aefE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self)
  ret void
}

; <core::iter::adapters::step_by::StepBy<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN105_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h9f0b43dc2fb29dbbE"(ptr align 8 %self) unnamed_addr #1 {
start:
; call <core::iter::adapters::step_by::StepBy<I> as core::iter::adapters::step_by::StepByImpl<I>>::spec_next
  %0 = call { i32, i32 } @"_ZN117_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..adapters..step_by..StepByImpl$LT$I$GT$$GT$9spec_next17h987eed14667b59baE"(ptr align 8 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
; Function Attrs: inlinehint uwtable
define internal void @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h099cb55cbda1a0fbE"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_36 = alloca [1 x i8], align 1
  %_35 = alloca [1 x i8], align 1
  store i8 1, ptr %_36, align 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_37 = trunc nuw i8 %1 to i1
  br i1 %_37, label %bb34, label %bb35

bb35:                                             ; preds = %start
  %_40 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_40, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  br label %bb36

bb34:                                             ; preds = %start
  br label %bb1

bb28:                                             ; preds = %funclet_bb28
; call core::ptr::drop_in_place<core::ops::try_trait::NeverShortCircuit<()>::wrap_mut_2<(),i32,core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>::{{closure}}>
  call void @"_ZN4core3ptr361drop_in_place$LT$core..ops..try_trait..NeverShortCircuit$LT$$LP$$RP$$GT$..wrap_mut_2$LT$$LP$$RP$$C$i32$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hb5f4e205859d09d7E"(ptr align 8 %f) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb33

funclet_bb28:                                     ; preds = %bb1, %bb30, %bb31_cleanup_trampoline_bb28
  %cleanuppad = cleanuppad within none []
  br label %bb28

bb36:                                             ; preds = %bb35
  %_4 = xor i1 %_0.i, true
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb36
  store i8 0, ptr %_36, align 1
  store i8 1, ptr %_35, align 1
  br label %bb4

bb1:                                              ; preds = %bb34, %bb36
  store i8 0, ptr %_36, align 1
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
  invoke void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h319393141526dfe3E"()
          to label %bb2 unwind label %funclet_bb28

bb4:                                              ; preds = %bb11, %bb3
  %_9 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i4 = load i32, ptr %self, align 4
  %_4.i5 = load i32, ptr %_9, align 4
  %_0.i6 = icmp slt i32 %_3.i4, %_4.i5
  br label %bb5

bb31:                                             ; preds = %funclet_bb31
  %2 = load i8, ptr %_35, align 1
  %3 = trunc nuw i8 %2 to i1
  br i1 %3, label %bb30, label %bb31_cleanup_trampoline_bb28

funclet_bb31:                                     ; preds = %bb12, %bb8, %bb7, %bb6, %bb20, %bb23, %bb17, %bb16, %bb14
  %cleanuppad1 = cleanuppad within none []
  br label %bb31

bb5:                                              ; preds = %bb4
  br i1 %_0.i6, label %bb6, label %bb14

bb14:                                             ; preds = %bb5
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %4, align 4
  %_24 = getelementptr inbounds i8, ptr %self, i64 4
; invoke core::cmp::impls::<impl core::cmp::PartialEq for i32>::eq
  %_22 = invoke zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$i32$GT$2eq17hb8e96aa969ad282cE"(ptr align 4 %self, ptr align 4 %_24)
          to label %bb15 unwind label %funclet_bb31

bb6:                                              ; preds = %bb5
  %_11 = load i32, ptr %self, align 4
; invoke <i32 as core::iter::range::Step>::forward_unchecked
  %n = invoke i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h32c4ff1e670f4fb3E"(i32 %_11, i64 1)
          to label %bb7 unwind label %funclet_bb31

bb15:                                             ; preds = %bb14
  br i1 %_22, label %bb16, label %bb22

bb22:                                             ; preds = %bb15
  br label %bb23

bb16:                                             ; preds = %bb15
  store i8 0, ptr %_35, align 1
  %_30 = load i32, ptr %self, align 4
; invoke core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
  invoke void @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h11fcd6ffaf1721aeE"(ptr align 8 %f, i32 %_30)
          to label %bb17 unwind label %funclet_bb31

bb23:                                             ; preds = %bb19, %bb22
  store i8 0, ptr %_35, align 1
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
  invoke void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h319393141526dfe3E"()
          to label %bb24 unwind label %funclet_bb31

bb17:                                             ; preds = %bb16
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
  invoke void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h0954e1ae90ab4e29E"()
          to label %bb18 unwind label %funclet_bb31

bb18:                                             ; preds = %bb17
  br label %bb19

bb19:                                             ; preds = %bb18
  br label %bb23

bb24:                                             ; preds = %bb23
; invoke core::ptr::drop_in_place<core::ops::try_trait::NeverShortCircuit<()>::wrap_mut_2<(),i32,core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>::{{closure}}>
  invoke void @"_ZN4core3ptr361drop_in_place$LT$core..ops..try_trait..NeverShortCircuit$LT$$LP$$RP$$GT$..wrap_mut_2$LT$$LP$$RP$$C$i32$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hb5f4e205859d09d7E"(ptr align 8 %f)
          to label %bb27 unwind label %funclet_bb33

bb33:                                             ; preds = %funclet_bb33
  %5 = load i8, ptr %_36, align 1
  %6 = trunc nuw i8 %5 to i1
  br i1 %6, label %bb32, label %bb29

funclet_bb33:                                     ; preds = %bb28, %bb26, %bb24
  %cleanuppad2 = cleanuppad within none []
  br label %bb33

bb27:                                             ; preds = %bb26, %bb24
  ret void

bb20:                                             ; No predecessors!
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
  invoke void @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h388f4e7c9fb920d6E"()
          to label %bb21 unwind label %funclet_bb31

bb21:                                             ; preds = %bb20
  br label %bb25

bb25:                                             ; preds = %bb13, %bb21
  br label %bb26

bb7:                                              ; preds = %bb6
  %n3 = load i32, ptr %self, align 4
  store i32 %n, ptr %self, align 4
  store i8 0, ptr %_35, align 1
; invoke core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
  invoke void @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h11fcd6ffaf1721aeE"(ptr align 8 %f, i32 %n3)
          to label %bb8 unwind label %funclet_bb31

bb8:                                              ; preds = %bb7
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
  invoke void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h0954e1ae90ab4e29E"()
          to label %bb9 unwind label %funclet_bb31

bb9:                                              ; preds = %bb8
  br label %bb11

bb11:                                             ; preds = %bb9
  store i8 1, ptr %_35, align 1
  br label %bb4

bb12:                                             ; No predecessors!
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
  invoke void @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h388f4e7c9fb920d6E"()
          to label %bb13 unwind label %funclet_bb31

bb13:                                             ; preds = %bb12
  br label %bb25

bb26:                                             ; preds = %bb2, %bb25
; invoke core::ptr::drop_in_place<core::ops::try_trait::NeverShortCircuit<()>::wrap_mut_2<(),i32,core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>::{{closure}}>
  invoke void @"_ZN4core3ptr361drop_in_place$LT$core..ops..try_trait..NeverShortCircuit$LT$$LP$$RP$$GT$..wrap_mut_2$LT$$LP$$RP$$C$i32$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hb5f4e205859d09d7E"(ptr align 8 %f)
          to label %bb27 unwind label %funclet_bb33

bb10:                                             ; No predecessors!
  unreachable

bb31_cleanup_trampoline_bb28:                     ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb30:                                             ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb2:                                              ; preds = %bb1
  br label %bb26

bb29:                                             ; preds = %bb32, %bb33
  cleanupret from %cleanuppad2 unwind to caller

bb32:                                             ; preds = %bb33
  br label %bb29
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h715fa2162fd29596E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h32c4ff1e670f4fb3E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h135975ed0343a08dE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_3 = getelementptr inbounds i8, ptr %self, i64 16
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::find
  %_0 = call align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4find17h4a5ffe3a9411d794E"(ptr align 8 %self, ptr align 1 %_3)
  ret ptr %_0
}

; <core::iter::adapters::filter::Filter<I,P> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN108_$LT$core..iter..adapters..filter..Filter$LT$I$C$P$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h8e55e0427d592aefE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
  call void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h48d5d8e6c7800f1cE"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %upper.0 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %upper.1 = load i64, ptr %1, align 8
  store i64 0, ptr %_0, align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %upper.0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  store i64 %upper.1, ptr %3, align 8
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h173dacefec502728E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_12 = alloca [1 x i8], align 1
  %_11 = alloca [12 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %_3 = alloca [24 x i8], align 8
  %vector = alloca [24 x i8], align 8
  store i8 1, ptr %_12, align 1
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::size_hint
  invoke void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$9size_hint17hd4c885ea8f9dc950E"(ptr sret([24 x i8]) align 8 %_3, ptr align 4 %iterator)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %1 = load i8, ptr %_12, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb7, label %bb6

funclet_bb8:                                      ; preds = %bb2, %bb5, %bb3, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %_5 = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = trunc nuw i64 %_5 to i1
  br i1 %6, label %bb3, label %bb2

bb3:                                              ; preds = %bb1
  %7 = getelementptr inbounds i8, ptr %_3, i64 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  %upper = load i64, ptr %8, align 8
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %9 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17he0e056f9ac547be1E"(i64 %upper, i64 4, i64 4, ptr align 8 %0)
          to label %bb9 unwind label %funclet_bb8

bb2:                                              ; preds = %bb1
  store ptr @alloc_11d257f5ed6cc7fc38feaa801053bac6, ptr %_8, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %10, align 8
  %11 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %12 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %11, ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %13, i64 8
  store i64 %12, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 0, ptr %16, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 %0) #19
          to label %unreachable unwind label %funclet_bb8

bb9:                                              ; preds = %bb3
  %_16.0 = extractvalue { i64, ptr } %9, 0
  %_16.1 = extractvalue { i64, ptr } %9, 1
  store i64 %_16.0, ptr %vector, align 8
  %17 = getelementptr inbounds i8, ptr %vector, i64 8
  store ptr %_16.1, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %vector, i64 16
  store i64 0, ptr %18, align 8
  store i8 0, ptr %_12, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_11, ptr align 4 %iterator, i64 12, i1 false)
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h2d7125b8c0b6bcb3E"(ptr align 8 %vector, ptr align 4 %_11, ptr align 8 %0)
          to label %bb4 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %vector) #18 [ "funclet"(token %cleanuppad1) ]
  cleanupret from %cleanuppad1 unwind label %funclet_bb8

funclet_bb5:                                      ; preds = %bb9
  %cleanuppad1 = cleanuppad within none []
  br label %bb5

bb4:                                              ; preds = %bb9
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  ret void

unreachable:                                      ; preds = %bb2
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb6:                                              ; preds = %bb7, %bb8
  cleanupret from %cleanuppad unwind to caller

bb7:                                              ; preds = %bb8
  br label %bb6
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17hcd22e163a9162da4E"(ptr sret([24 x i8]) align 8 %_0, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_20 = alloca [1 x i8], align 1
  %vector1 = alloca [24 x i8], align 8
  %_8 = alloca [24 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  %vector = alloca [24 x i8], align 8
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  store i8 1, ptr %_20, align 1
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
  %5 = invoke { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h00c051d61eabf906E"(ptr align 8 %iterator)
          to label %bb1 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %6 = load i8, ptr %_20, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb10, label %bb8

funclet_bb11:                                     ; preds = %bb9, %bb7, %start
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb1:                                              ; preds = %start
  %8 = extractvalue { i32, i32 } %5, 0
  %9 = extractvalue { i32, i32 } %5, 1
  store i32 %8, ptr %_3, align 4
  %10 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %9, ptr %10, align 4
  %11 = load i32, ptr %_3, align 4
  %12 = getelementptr inbounds i8, ptr %_3, i64 4
  %13 = load i32, ptr %12, align 4
  %_5 = zext i32 %11 to i64
  %14 = trunc nuw i64 %_5 to i1
  br i1 %14, label %bb3, label %bb12

bb3:                                              ; preds = %bb1
  %15 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %15, align 4
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h160c25b23dcd7145E"(ptr sret([24 x i8]) align 8 %_8, ptr align 8 %iterator)
          to label %bb4 unwind label %funclet_bb9

bb12:                                             ; preds = %bb1
  store i64 0, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr getelementptr (i8, ptr null, i64 4), ptr %16, align 8
  %17 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %17, align 8
  br label %bb6

bb6:                                              ; preds = %bb5, %bb12
  ret void

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad2 unwind label %funclet_bb11

funclet_bb9:                                      ; preds = %bb14, %bb4, %bb3
  %cleanuppad2 = cleanuppad within none []
  br label %bb9

bb4:                                              ; preds = %bb3
  %lower = load i64, ptr %_8, align 8
  %18 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %18, ptr %3, align 8
  %v2 = load i64, ptr %3, align 8
; invoke core::cmp::Ord::max
  %initial_capacity = invoke i64 @_ZN4core3cmp3Ord3max17h780fdb583315c03bE(i64 4, i64 %v2)
          to label %bb14 unwind label %funclet_bb9

bb14:                                             ; preds = %bb4
; invoke alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %19 = invoke { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17he0e056f9ac547be1E"(i64 %initial_capacity, i64 4, i64 4, ptr align 8 %2)
          to label %bb15 unwind label %funclet_bb9

bb15:                                             ; preds = %bb14
  %_26.0 = extractvalue { i64, ptr } %19, 0
  %_26.1 = extractvalue { i64, ptr } %19, 1
  store i64 %_26.0, ptr %vector1, align 8
  %20 = getelementptr inbounds i8, ptr %vector1, i64 8
  store ptr %_26.1, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 0, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %vector1, i64 8
  %_27 = load ptr, ptr %22, align 8
  store i32 %element, ptr %_27, align 4
  %23 = getelementptr inbounds i8, ptr %vector1, i64 16
  store i64 1, ptr %23, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %vector, ptr align 8 %vector1, i64 24, i1 false)
  store i8 0, ptr %_20, align 1
  %_19.0 = load ptr, ptr %iterator, align 8
  %24 = getelementptr inbounds i8, ptr %iterator, i64 8
  %_19.1 = load ptr, ptr %24, align 8
; invoke <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
  invoke void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h3cbc4e7895ba4720E"(ptr align 8 %vector, ptr %_19.0, ptr %_19.1, ptr align 8 %2)
          to label %bb5 unwind label %funclet_bb7

bb7:                                              ; preds = %funclet_bb7
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %vector) #18 [ "funclet"(token %cleanuppad3) ]
  cleanupret from %cleanuppad3 unwind label %funclet_bb11

funclet_bb7:                                      ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb7

bb5:                                              ; preds = %bb15
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %vector, i64 24, i1 false)
  br label %bb6

bb2:                                              ; No predecessors!
  unreachable

bb8:                                              ; preds = %bb10, %bb11
  cleanupret from %cleanuppad unwind to caller

bb10:                                             ; preds = %bb11
  br label %bb8
}

; <core::iter::adapters::step_by::StepBy<I> as core::iter::adapters::step_by::StepByImpl<I>>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN117_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..adapters..step_by..StepByImpl$LT$I$GT$$GT$9spec_next17h987eed14667b59baE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %step_size = alloca [8 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 20
  %1 = load i8, ptr %0, align 4
  %_3 = trunc nuw i8 %1 to i1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %2 = load i64, ptr %self, align 8
  store i64 %2, ptr %step_size, align 8
  br label %bb3

bb1:                                              ; preds = %start
  store i64 0, ptr %step_size, align 8
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  %3 = getelementptr inbounds i8, ptr %self, i64 20
  store i8 0, ptr %3, align 4
  %_4 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load i64, ptr %step_size, align 8
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::nth
  %4 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$3nth17h9fc9248c48599650E"(ptr align 4 %_4, i64 %_5)
  %_0.0 = extractvalue { i32, i32 } %4, 0
  %_0.1 = extractvalue { i32, i32 } %4, 1
  %5 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %6 = insertvalue { i32, i32 } %5, i32 %_0.1, 1
  ret { i32, i32 } %6
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
; Function Attrs: inlinehint uwtable
define internal void @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h388f4e7c9fb920d6E"() unnamed_addr #1 {
start:
  unreachable
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17ha772066263c9998aE(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h488a8ad91c5f3a4dE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h3030d7df3df363dbE(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7298bf9cde019665E"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h3030d7df3df363dbE(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h07c2c7137715d7a9E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h38ff3bfe970276caE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::num::<impl core::fmt::Debug for i32>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h3b24256f53cdbc78E"(ptr align 4 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha45c12ac98178e30E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::ops::arith::Rem>::rem
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN45_$LT$i32$u20$as$u20$core..ops..arith..Rem$GT$3rem17hf7161259ce26fd9eE"(i32 %self, i32 %other, ptr align 8 %0) unnamed_addr #1 {
start:
  %_3 = icmp eq i32 %other, 0
  br i1 %_3, label %panic, label %bb1

bb1:                                              ; preds = %start
  %_4 = icmp eq i32 %other, -1
  %_5 = icmp eq i32 %self, -2147483648
  %_6 = and i1 %_4, %_5
  br i1 %_6, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 %0) #19
  unreachable

bb2:                                              ; preds = %bb1
  %_0 = srem i32 %self, %other
  ret i32 %_0

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_rem_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8 %0) #19
  unreachable
}

; <i32 as core::iter::range::Step>::steps_between
; Function Attrs: inlinehint uwtable
define internal void @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$13steps_between17h00579b7789a82617E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %start1, ptr align 4 %end) unnamed_addr #1 {
start:
  %_12 = alloca [16 x i8], align 8
  %_4 = load i32, ptr %start1, align 4
  %_5 = load i32, ptr %end, align 4
  %_3 = icmp sle i32 %_4, %_5
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  %0 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %1 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %2 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb1:                                              ; preds = %start
  %_9 = load i32, ptr %end, align 4
  %self = sext i32 %_9 to i64
  %_11 = load i32, ptr %start1, align 4
  %rhs = sext i32 %_11 to i64
  %_7 = sub i64 %self, %rhs
  %4 = getelementptr inbounds i8, ptr %_12, i64 8
  store i64 %_7, ptr %4, align 8
  store i64 1, ptr %_12, align 8
  store i64 %_7, ptr %_0, align 8
  %5 = load i64, ptr %_12, align 8
  %6 = getelementptr inbounds i8, ptr %_12, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; <i32 as core::iter::range::Step>::forward_checked
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$15forward_checked17h4f8a33b8f39d9028E"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %_3 = alloca [8 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %_8 = icmp ugt i64 %n, 4294967295
  br i1 %_8, label %bb5, label %bb6

bb6:                                              ; preds = %start
  %_9 = trunc i64 %n to i32
  %0 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %_9, ptr %0, align 4
  store i32 0, ptr %_3, align 4
  %1 = getelementptr inbounds i8, ptr %_3, i64 4
  %n2 = load i32, ptr %1, align 4
  %wrapped = add i32 %start1, %n2
  %_7 = icmp sge i32 %wrapped, %start1
  br i1 %_7, label %bb1, label %bb2

bb5:                                              ; preds = %start
  %2 = load i32, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, align 4
  %3 = load i32, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, i64 4), align 4
  store i32 %2, ptr %_0, align 4
  %4 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %3, ptr %4, align 4
  br label %bb4

bb2:                                              ; preds = %bb6
  %5 = load i32, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, align 4
  %6 = load i32, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, i64 4), align 4
  store i32 %5, ptr %_0, align 4
  %7 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %6, ptr %7, align 4
  br label %bb3

bb1:                                              ; preds = %bb6
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %wrapped, ptr %8, align 4
  store i32 1, ptr %_0, align 4
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  br label %bb4

bb4:                                              ; preds = %bb5, %bb3
  %9 = load i32, ptr %_0, align 4
  %10 = getelementptr inbounds i8, ptr %_0, i64 4
  %11 = load i32, ptr %10, align 4
  %12 = insertvalue { i32, i32 } poison, i32 %9, 0
  %13 = insertvalue { i32, i32 } %12, i32 %11, 1
  ret { i32, i32 } %13
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h32c4ff1e670f4fb3E"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17hf673f2ac1cbd71ebE() #20
  unreachable
}

; <i32 as core::iter::range::Step>::forward
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$7forward17h94fd2a40d2dedd23E"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %_5 = alloca [8 x i8], align 4
  %_10 = icmp ugt i64 %n, 4294967295
  br i1 %_10, label %bb8, label %bb9

bb9:                                              ; preds = %start
  %n2 = trunc i64 %n to i32
  %wrapped = add i32 %start1, %n2
  %_9 = icmp sge i32 %wrapped, %start1
  br i1 %_9, label %bb4, label %bb5

bb8:                                              ; preds = %start
  %0 = load i32, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, align 4
  %1 = load i32, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, i64 4), align 4
  store i32 %0, ptr %_5, align 4
  %2 = getelementptr inbounds i8, ptr %_5, i64 4
  store i32 %1, ptr %2, align 4
  br label %bb7

bb5:                                              ; preds = %bb9
  %3 = load i32, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.1, i64 4), align 4
  store i32 %3, ptr %_5, align 4
  %5 = getelementptr inbounds i8, ptr %_5, i64 4
  store i32 %4, ptr %5, align 4
  br label %bb6

bb4:                                              ; preds = %bb9
  %6 = getelementptr inbounds i8, ptr %_5, i64 4
  store i32 %wrapped, ptr %6, align 4
  store i32 1, ptr %_5, align 4
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  br label %bb7

bb7:                                              ; preds = %bb8, %bb6
  %7 = load i32, ptr %_5, align 4
  %8 = getelementptr inbounds i8, ptr %_5, i64 4
  %9 = load i32, ptr %8, align 4
  %_13 = zext i32 %7 to i64
  %_12 = icmp eq i64 %_13, 1
  %_3 = xor i1 %_12, true
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %bb7
  br label %bb3

bb1:                                              ; preds = %bb7
  br i1 true, label %panic, label %bb3

bb3:                                              ; preds = %bb1, %bb2
  %rhs = trunc i64 %n to i32
  %_0 = add i32 %start1, %rhs
  ret i32 %_0

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_dda2737f33a4b1c1d39fa523d13a220a) #19
  unreachable
}

; <[T] as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17hd696894a67eb77b3E"(ptr align 4 %self.0, i64 %self.1, ptr align 8 %f) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %_5 = alloca [16 x i8], align 8
; call core::fmt::Formatter::debug_list
  call void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8 %_5, ptr align 8 %f)
  br label %bb5

bb5:                                              ; preds = %start
  %_11 = getelementptr inbounds nuw i32, ptr %self.0, i64 %self.1
  store ptr %_11, ptr %end_or_len, align 8
  br label %bb6

bb6:                                              ; preds = %bb5
  %_13 = load ptr, ptr %end_or_len, align 8
; call core::fmt::builders::DebugList::entries
  %_3 = call align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h429172e12d46763aE(ptr align 8 %_5, ptr %self.0, ptr %_13)
; call core::fmt::builders::DebugList::finish
  %_0 = call zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8 %_3)
  ret i1 %_0

bb4:                                              ; No predecessors!
  unreachable
}

; core::intrinsics::write_bytes::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics11write_bytes18precondition_check17h72ff8af2b98a7773E(ptr %addr, i64 %align, i1 zeroext %zero_size) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %zero_size, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_dd79dfae92e8fdc23813c4c7a1b7cf72, i64 228) #21
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #22 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h833ed7e774297df7E(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17hc831955c108928abE(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #22 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #21
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17h78ab013085bfdce6E() unnamed_addr #4 {
start:
  ret void
}

; core::cmp::Ord::max
; Function Attrs: inlinehint uwtable
define internal i64 @_ZN4core3cmp3Ord3max17h780fdb583315c03bE(i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %other = alloca [8 x i8], align 8
  %self = alloca [8 x i8], align 8
  store i64 %0, ptr %self, align 8
  store i64 %1, ptr %other, align 8
  store i8 1, ptr %_6, align 1
  %_3.i = load i64, ptr %other, align 8
  %_4.i = load i64, ptr %self, align 8
  %_0.i = icmp ult i64 %_3.i, %_4.i
  br label %bb1

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind label %funclet_bb9

funclet_bb5:                                      ; No predecessors!
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  br i1 %_0.i, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
  %2 = load i64, ptr %other, align 8
  store i64 %2, ptr %_0, align 8
  %3 = load i8, ptr %_6, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb7, label %bb4

bb2:                                              ; preds = %bb1
  store i8 0, ptr %_6, align 1
  %5 = load i64, ptr %self, align 8
  store i64 %5, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb2, %bb7, %bb3
  %6 = load i64, ptr %_0, align 8
  ret i64 %6

bb7:                                              ; preds = %bb3
  br label %bb4

bb9:                                              ; preds = %funclet_bb9
  %7 = load i8, ptr %_6, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb8, label %bb6

funclet_bb9:                                      ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb9

bb6:                                              ; preds = %bb8, %bb9
  cleanupret from %cleanuppad1 unwind to caller

bb8:                                              ; preds = %bb9
  br label %bb6
}

; core::cmp::impls::<impl core::cmp::PartialEq for i32>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$i32$GT$2eq17hb8e96aa969ad282cE"(ptr align 4 %self, ptr align 4 %other) unnamed_addr #1 {
start:
  %_3 = load i32, ptr %self, align 4
  %_4 = load i32, ptr %other, align 4
  %_0 = icmp eq i32 %_3, %_4
  ret i1 %_0
}

; core::cmp::impls::<impl core::cmp::PartialOrd for i32>::partial_cmp
; Function Attrs: inlinehint uwtable
define internal i8 @"_ZN4core3cmp5impls55_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$i32$GT$11partial_cmp17hbab3552e7956590eE"(ptr align 4 %self, ptr align 4 %other) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  %_4 = load i32, ptr %self, align 4
  %_5 = load i32, ptr %other, align 4
  %_3 = call i8 @llvm.scmp.i8.i32(i32 %_4, i32 %_5)
  store i8 %_3, ptr %_0, align 1
  %0 = load i8, ptr %_0, align 1
  ret i8 %0
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h0dc9b63ad3e13ac4E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha45c12ac98178e30E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h94f78491c604fe02E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hb0da550b5497749aE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_debug
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_debug17h95088346cbcb6dabE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17hbb1e5ce08ff9406aE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::num::<impl core::fmt::Debug for i32>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h3b24256f53cdbc78E"(ptr align 4 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  %0 = getelementptr inbounds i8, ptr %f, i64 16
  %_4 = load i32, ptr %0, align 8
  %_3 = and i32 %_4, 33554432
  %1 = icmp eq i32 %_3, 0
  br i1 %1, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %f, i64 16
  %_6 = load i32, ptr %2, align 8
  %_5 = and i32 %_6, 67108864
  %3 = icmp eq i32 %_5, 0
  br i1 %3, label %bb4, label %bb3

bb1:                                              ; preds = %start
; call core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
  %4 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4 %self, ptr align 8 %f)
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_0, align 1
  br label %bb6

bb4:                                              ; preds = %bb2
; call core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
  %6 = call zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4 %self, ptr align 8 %f)
  %7 = zext i1 %6 to i8
  store i8 %7, ptr %_0, align 1
  br label %bb5

bb3:                                              ; preds = %bb2
; call core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
  %8 = call zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4 %self, ptr align 8 %f)
  %9 = zext i1 %8 to i8
  store i8 %9, ptr %_0, align 1
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  br label %bb6

bb6:                                              ; preds = %bb1, %bb5
  %10 = load i8, ptr %_0, align 1
  %11 = trunc nuw i8 %10 to i1
  ret i1 %11
}

; core::fmt::builders::DebugList::entries
; Function Attrs: uwtable
define internal align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h429172e12d46763aE(ptr align 8 %self, ptr %entries.0, ptr %entries.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %entry = alloca [8 x i8], align 8
  %_5 = alloca [8 x i8], align 8
  %iter = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h86c1fa940c30bf7dE"(ptr %entries.0, ptr %entries.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
  store ptr %_3.0, ptr %iter, align 8
  %1 = getelementptr inbounds i8, ptr %iter, i64 8
  store ptr %_3.1, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %bb8, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h21296b86c74b4eb6E"(ptr align 8 %iter)
          to label %bb3 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb10, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb3:                                              ; preds = %bb2
  store ptr %2, ptr %_5, align 8
  %3 = load ptr, ptr %_5, align 8
  %4 = ptrtoint ptr %3 to i64
  %5 = icmp eq i64 %4, 0
  %_7 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_7 to i1
  br i1 %6, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %7 = load ptr, ptr %_5, align 8
  store ptr %7, ptr %entry, align 8
; invoke core::fmt::builders::DebugList::entry
  %_9 = invoke align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8 %self, ptr align 1 %entry, ptr align 8 @vtable.1)
          to label %bb7 unwind label %funclet_bb10

bb6:                                              ; preds = %bb3
  ret ptr %self

bb10:                                             ; preds = %funclet_bb10
  cleanupret from %cleanuppad1 unwind label %funclet_bb11

funclet_bb10:                                     ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb10

bb7:                                              ; preds = %bb5
  br label %bb8

bb8:                                              ; preds = %bb7
  br label %bb2

bb4:                                              ; No predecessors!
  unreachable
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h2470d9f342650c59E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h3b8ecced06f7ffe2E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h77e3d0ea9445378dE"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #21
  unreachable
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h46446d71a2b04e96E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #21
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #1 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h1e90f34a31640817E"(ptr align 8 %self, ptr align 8 %0) unnamed_addr #0 {
start:
  %args = alloca [8 x i8], align 8
  store ptr %0, ptr %args, align 8
  %_3 = load ptr, ptr %self, align 8
  %1 = load ptr, ptr %args, align 8
; call _05_loops::main::{{closure}}
  %_0 = call zeroext i1 @"_ZN9_05_loops4main28_$u7b$$u7b$closure$u7d$$u7d$17hd87cced8fa2f6e0fE"(ptr align 1 %_3, ptr align 8 %1)
  ret i1 %_0
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h5b81b2aeb508600eE"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h6b08d653e08139beE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h07c2c7137715d7a9E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h6b08d653e08139beE(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h488a8ad91c5f3a4dE"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17hfb20f7a3f979959eE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  store ptr %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load ptr, ptr %_2, align 8
  %4 = getelementptr inbounds i8, ptr %_2, i64 8
  %5 = load i64, ptr %4, align 8
; call alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
  call void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17hdc15e7bfc7393feaE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %3, i64 %5)
  ret void
}

; core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h11fcd6ffaf1721aeE"(ptr align 8 %_1, i32 %b) unnamed_addr #1 {
start:
; call core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
  call void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h04cf9cfe3b3c0107E"(ptr align 8 %_1, i32 %b)
  ret void
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17h18dd26d79f7b93a2E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #21
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #22 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr143drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17heb7aa37fedc30dd8E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::set_len_on_drop::SetLenOnDrop>
  call void @"_ZN4core3ptr62drop_in_place$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$GT$17h059fcf14c933ca5fE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr240drop_in_place$LT$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hcd739ee13c4ca135E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>
  call void @"_ZN4core3ptr143drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17heb7aa37fedc30dd8E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<&i32>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr28drop_in_place$LT$$RF$i32$GT$17h8e792cfec12e0afeE"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::drop_in_place<core::ops::try_trait::NeverShortCircuit<()>::wrap_mut_2<(),i32,core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>::{{closure}}>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr361drop_in_place$LT$core..ops..try_trait..NeverShortCircuit$LT$$LP$$RP$$GT$..wrap_mut_2$LT$$LP$$RP$$C$i32$C$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hb5f4e205859d09d7E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<core::iter::traits::iterator::Iterator::for_each::call<i32,alloc::vec::Vec<i32>::extend_trusted<core::ops::range::RangeInclusive<i32>>::{{closure}}>::{{closure}}>
  call void @"_ZN4core3ptr240drop_in_place$LT$core..iter..traits..iterator..Iterator..for_each..call$LT$i32$C$alloc..vec..Vec$LT$i32$GT$..extend_trusted$LT$core..ops..range..RangeInclusive$LT$i32$GT$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17hcd739ee13c4ca135E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h4a36589654e632c7E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hbeab58c70a54378bE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hbeab58c70a54378bE"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h81ea4d490ac29f42E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha1c1afb906e39be1E"(ptr align 8 %_1) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha1c1afb906e39be1E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %_1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h323804df6d042ddbE"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h16192b0ee4510217E"(ptr align 8 %_1) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
  call void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h16192b0ee4510217E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17ha1c1afb906e39be1E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17heebfbdf2d6585446E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<i32>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr54drop_in_place$LT$alloc..raw_vec..RawVec$LT$i32$GT$$GT$17h16192b0ee4510217E"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h48810ed9ab43e1b5E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::set_len_on_drop::SetLenOnDrop>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr62drop_in_place$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$GT$17h059fcf14c933ca5fE"(ptr align 8 %_1) unnamed_addr #0 {
start:
; call <alloc::vec::set_len_on_drop::SetLenOnDrop as core::ops::drop::Drop>::drop
  call void @"_ZN83_$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$u20$as$u20$core..ops..drop..Drop$GT$4drop17h39d2f05eb352c7f8E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h278931f52dbeb425E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %ptr) unnamed_addr #3 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #21
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h25d4b84e7cb7566aE"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hb5b5b81c0409a3cdE"(ptr %self, ptr %subtracted) #20
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 4
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #19
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17hb5b5b81c0409a3cdE"(ptr %this, ptr %origin) unnamed_addr #3 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #21
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17hb22b9bc5f4d1c80fE(i1 zeroext %cond) unnamed_addr #3 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #21
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17hf673f2ac1cbd71ebE() unnamed_addr #3 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #21
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h316d8d4b8b19e6b0E"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17ha92c92d6d709d487E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::nth
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$3nth17h9fc9248c48599650E"(ptr align 4 %self, i64 %n) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_9 = alloca [1 x i8], align 1
  %plus_n = alloca [4 x i8], align 4
  %_4 = alloca [8 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_23 = trunc nuw i8 %1 to i1
  br i1 %_23, label %bb29, label %bb30

bb30:                                             ; preds = %start
  %_26 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_26, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_3 = xor i1 %_0.i, true
  br i1 %_3, label %bb1, label %bb2

bb29:                                             ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb30
  %_0.i8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_checked
  %2 = call { i32, i32 } @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$15forward_checked17h4f8a33b8f39d9028E"(i32 %_0.i8, i64 %n)
  %3 = extractvalue { i32, i32 } %2, 0
  %4 = extractvalue { i32, i32 } %2, 1
  store i32 %3, ptr %_4, align 4
  %5 = getelementptr inbounds i8, ptr %_4, i64 4
  store i32 %4, ptr %5, align 4
  %6 = load i32, ptr %_4, align 4
  %7 = getelementptr inbounds i8, ptr %_4, i64 4
  %8 = load i32, ptr %7, align 4
  %_7 = zext i32 %6 to i64
  %9 = trunc nuw i64 %_7 to i1
  br i1 %9, label %bb5, label %bb18

bb1:                                              ; preds = %bb29, %bb30
  store i32 0, ptr %_0, align 4
  br label %bb26

bb5:                                              ; preds = %bb2
  %10 = getelementptr inbounds i8, ptr %_4, i64 4
  %11 = load i32, ptr %10, align 4
  store i32 %11, ptr %plus_n, align 4
  %_11 = getelementptr inbounds i8, ptr %self, i64 4
; invoke core::cmp::impls::<impl core::cmp::PartialOrd for i32>::partial_cmp
  %12 = invoke i8 @"_ZN4core3cmp5impls55_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$i32$GT$11partial_cmp17hbab3552e7956590eE"(ptr align 4 %plus_n, ptr align 4 %_11)
          to label %bb6 unwind label %funclet_bb27

bb18:                                             ; preds = %bb2
  br label %bb21

bb27:                                             ; preds = %funclet_bb27
  cleanupret from %cleanuppad unwind label %funclet_bb28

funclet_bb27:                                     ; preds = %bb17, %bb14, %bb11, %bb5
  %cleanuppad = cleanuppad within none []
  br label %bb27

bb6:                                              ; preds = %bb5
  store i8 %12, ptr %_9, align 1
  %13 = load i8, ptr %_9, align 1
  %14 = icmp eq i8 %13, 2
  %_13 = select i1 %14, i64 0, i64 1
  %15 = trunc nuw i64 %_13 to i1
  br i1 %15, label %bb8, label %bb7

bb8:                                              ; preds = %bb6
  %_12 = load i8, ptr %_9, align 1
  switch i8 %_12, label %bb32 [
    i8 -1, label %bb10
    i8 0, label %bb9
    i8 1, label %bb7
  ]

bb7:                                              ; preds = %bb8, %bb6
  br label %bb21

bb32:                                             ; preds = %bb8
  unreachable

bb10:                                             ; preds = %bb8
  %_0.i7 = load i32, ptr %plus_n, align 4
  br label %bb11

bb9:                                              ; preds = %bb8
  %_0.i6 = load i32, ptr %plus_n, align 4
  br label %bb15

bb11:                                             ; preds = %bb10
; invoke <i32 as core::iter::range::Step>::forward
  %_14 = invoke i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$7forward17h94fd2a40d2dedd23E"(i32 %_0.i7, i64 1)
          to label %bb12 unwind label %funclet_bb27

bb12:                                             ; preds = %bb11
  br label %bb13

bb13:                                             ; preds = %bb12
  store i32 %_14, ptr %self, align 4
  %_17 = load i32, ptr %plus_n, align 4
  %16 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_17, ptr %16, align 4
  store i32 1, ptr %_0, align 4
  br label %bb25

bb25:                                             ; preds = %bb16, %bb13
  br label %bb26

bb14:                                             ; preds = %funclet_bb14
  store i32 %_14, ptr %self, align 4
  cleanupret from %cleanuppad1 unwind label %funclet_bb27

funclet_bb14:                                     ; No predecessors!
  %cleanuppad1 = cleanuppad within none []
  br label %bb14

bb15:                                             ; preds = %bb9
  br label %bb16

bb16:                                             ; preds = %bb15
  store i32 %_0.i6, ptr %self, align 4
  %17 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %17, align 4
  %_20 = load i32, ptr %plus_n, align 4
  %18 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %_20, ptr %18, align 4
  store i32 1, ptr %_0, align 4
  br label %bb25

bb26:                                             ; preds = %bb1, %bb23, %bb25
  %19 = load i32, ptr %_0, align 4
  %20 = getelementptr inbounds i8, ptr %_0, i64 4
  %21 = load i32, ptr %20, align 4
  %22 = insertvalue { i32, i32 } poison, i32 %19, 0
  %23 = insertvalue { i32, i32 } %22, i32 %21, 1
  ret { i32, i32 } %23

bb17:                                             ; preds = %funclet_bb17
  store i32 %_0.i6, ptr %self, align 4
  cleanupret from %cleanuppad2 unwind label %funclet_bb27

funclet_bb17:                                     ; No predecessors!
  %cleanuppad2 = cleanuppad within none []
  br label %bb17

bb21:                                             ; preds = %bb18, %bb7
  %_22 = getelementptr inbounds i8, ptr %self, i64 4
  %_0.i5 = load i32, ptr %_22, align 4
  br label %bb23

bb28:                                             ; preds = %funclet_bb28
  cleanupret from %cleanuppad3 unwind to caller

funclet_bb28:                                     ; preds = %bb24, %bb27
  %cleanuppad3 = cleanuppad within none []
  br label %bb28

bb23:                                             ; preds = %bb21
  store i32 %_0.i5, ptr %self, align 4
  %24 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %24, align 4
  store i32 0, ptr %_0, align 4
  br label %bb26

bb24:                                             ; preds = %funclet_bb24
  store i32 %_0.i5, ptr %self, align 4
  cleanupret from %cleanuppad4 unwind label %funclet_bb28

funclet_bb24:                                     ; No predecessors!
  %cleanuppad4 = cleanuppad within none []
  br label %bb24
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::fold
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4fold17h85154ab4c97f230cE"(ptr align 4 %self, ptr align 8 %fold) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %f = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %f, ptr align 8 %fold, i64 24, i1 false)
; invoke <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
  invoke void @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h099cb55cbda1a0fbE"(ptr align 4 %self, ptr align 8 %f)
          to label %bb4 unwind label %funclet_bb2

bb2:                                              ; preds = %funclet_bb2
  cleanupret from %cleanuppad unwind to caller

funclet_bb2:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb2

bb4:                                              ; preds = %start
  ret void
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h715fa2162fd29596E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$9size_hint17hd4c885ea8f9dc950E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_8 = alloca [16 x i8], align 8
  %hint = alloca [24 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %2 = load i8, ptr %1, align 4
  %_10 = trunc nuw i8 %2 to i1
  br i1 %_10, label %bb5, label %bb6

bb6:                                              ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb5:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb6
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
; call <i32 as core::iter::range::Step>::steps_between
  call void @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$13steps_between17h00579b7789a82617E"(ptr sret([24 x i8]) align 8 %hint, ptr align 4 %self, ptr align 4 %_5)
  %self2 = load i64, ptr %hint, align 8
  %3 = call i64 @llvm.uadd.sat.i64(i64 %self2, i64 1)
  store i64 %3, ptr %0, align 8
  %_6 = load i64, ptr %0, align 8
  %4 = getelementptr inbounds i8, ptr %hint, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %self1, align 8
  %8 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %7, ptr %8, align 8
  %_14 = load i64, ptr %self1, align 8
  %9 = getelementptr inbounds i8, ptr %self1, i64 8
  %10 = load i64, ptr %9, align 8
  %11 = trunc nuw i64 %_14 to i1
  br i1 %11, label %bb12, label %bb11

bb1:                                              ; preds = %bb5, %bb6
  store i64 0, ptr %_0, align 8
  %12 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.2, align 8
  %13 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.2, i64 8), align 8
  %14 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %12, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  store i64 %13, ptr %15, align 8
  br label %bb4

bb12:                                             ; preds = %bb2
  %16 = getelementptr inbounds i8, ptr %self1, i64 8
  %x = load i64, ptr %16, align 8
  %17 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %x, i64 1)
  %_17.0 = extractvalue { i64, i1 } %17, 0
  %_17.1 = extractvalue { i64, i1 } %17, 1
  br i1 %_17.1, label %bb14, label %bb16

bb11:                                             ; preds = %bb2
  %18 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %19 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %18, ptr %_8, align 8
  %20 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %19, ptr %20, align 8
  br label %bb9

bb9:                                              ; preds = %bb13, %bb11
  store i64 %_6, ptr %_0, align 8
  %21 = load i64, ptr %_8, align 8
  %22 = getelementptr inbounds i8, ptr %_8, i64 8
  %23 = load i64, ptr %22, align 8
  %24 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %24, align 8
  %25 = getelementptr inbounds i8, ptr %24, i64 8
  store i64 %23, ptr %25, align 8
  br label %bb4

bb16:                                             ; preds = %bb12
  %_18 = add nuw i64 %x, 1
  %26 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %_18, ptr %26, align 8
  store i64 1, ptr %_8, align 8
  br label %bb13

bb14:                                             ; preds = %bb12
  %27 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %28 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %27, ptr %_8, align 8
  %29 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %28, ptr %29, align 8
  br label %bb13

bb13:                                             ; preds = %bb14, %bb16
  br label %bb9

bb4:                                              ; preds = %bb1, %bb9
  ret void

bb10:                                             ; No predecessors!
  unreachable
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal i32 @_ZN4core4iter6traits8iterator8Iterator3sum17h84ea3891411f6273E(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
; call <i32 as core::iter::traits::accum::Sum<&i32>>::sum
  %_0 = call i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17h763041b5ffb0e734E"(ptr %self.0, ptr %self.1)
  ret i32 %_0
}

; core::iter::traits::iterator::Iterator::cloned
; Function Attrs: uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17he72d9b0779c16f69E(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::filter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6filter17hbb76ff1c87dde395E(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17h80346af0c5fb6b58E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17ha6343f793688bf36E"(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::collect
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7collect17hc088abf1e5f7d622E(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [12 x i8], align 4
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_6, ptr align 4 %self, i64 12, i1 false)
; invoke <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
  invoke void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h2396369265a8c4edE"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %_6, ptr align 8 @alloc_7277ca8d30122b7203dfd66509383767)
          to label %bb1 unwind label %funclet_bb4

bb4:                                              ; preds = %funclet_bb4
  br label %bb2

funclet_bb4:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb4

bb1:                                              ; preds = %start
  ret void

bb2:                                              ; preds = %bb3, %bb4
  cleanupret from %cleanuppad unwind to caller

bb3:                                              ; No predecessors!
  br label %bb2
}

; core::iter::traits::iterator::Iterator::step_by
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7step_by17h88860e30bc8949cdE(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self, i64 %step) unnamed_addr #1 {
start:
; call core::iter::adapters::step_by::StepBy<I>::new
  call void @"_ZN4core4iter8adapters7step_by15StepBy$LT$I$GT$3new17hb829a57f29a46579E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %self, i64 %step)
  ret void
}

; core::iter::traits::iterator::Iterator::for_each
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator8for_each17h5f9666aaf9aedf80E(ptr align 4 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_4 = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_4, ptr align 8 %f, i64 24, i1 false)
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::fold
  call void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4fold17h85154ab4c97f230cE"(ptr align 4 %self, ptr align 8 %_4)
  ret void
}

; core::iter::traits::iterator::Iterator::for_each::call::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter6traits8iterator8Iterator8for_each4call28_$u7b$$u7b$closure$u7d$$u7d$17h04cf9cfe3b3c0107E"(ptr align 8 %_1, i32 %item) unnamed_addr #1 {
start:
; call alloc::vec::Vec<T,A>::extend_trusted::{{closure}}
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted28_$u7b$$u7b$closure$u7d$$u7d$17hbbb792faeabe9478E"(ptr align 8 %_1, i32 %item)
  ret void
}

; core::iter::adapters::step_by::StepBy<I>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter8adapters7step_by15StepBy$LT$I$GT$3new17hb829a57f29a46579E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iter, i64 %step) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_7 = alloca [1 x i8], align 1
  %_5 = alloca [12 x i8], align 4
  %iter1 = alloca [12 x i8], align 4
  store i8 1, ptr %_7, align 1
  %0 = icmp eq i64 %step, 0
  br i1 %0, label %bb2, label %bb1

bb2:                                              ; preds = %start
; invoke core::panicking::panic
  invoke void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_4aead6e2018a46d0df208d5729447de7, i64 27, ptr align 8 @alloc_1d19af47b700b7580386eededda18b7b) #19
          to label %unreachable unwind label %funclet_bb6

bb1:                                              ; preds = %start
  store i8 0, ptr %_7, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_5, ptr align 4 %iter, i64 12, i1 false)
; invoke <T as core::iter::adapters::step_by::SpecRangeSetup<T>>::setup
  invoke void @"_ZN76_$LT$T$u20$as$u20$core..iter..adapters..step_by..SpecRangeSetup$LT$T$GT$$GT$5setup17h4c06d5e1a5efc43fE"(ptr sret([12 x i8]) align 4 %iter1, ptr align 4 %_5, i64 %step)
          to label %bb3 unwind label %funclet_bb6

bb6:                                              ; preds = %funclet_bb6
  %1 = load i8, ptr %_7, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb5, label %bb4

funclet_bb6:                                      ; preds = %bb1, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb6

unreachable:                                      ; preds = %bb2
  unreachable

bb3:                                              ; preds = %bb1
  %_6 = sub i64 %step, 1
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 4 %iter1, i64 12, i1 false)
  store i64 %_6, ptr %_0, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 20
  store i8 1, ptr %4, align 4
  ret void

bb4:                                              ; preds = %bb5, %bb6
  cleanupret from %cleanuppad unwind to caller

bb5:                                              ; preds = %bb6
  br label %bb4
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h2476662a0364f8f8E(ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h3aecff4c27496741E(i64 %size, i64 %align) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #22 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #21
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h28e22ba7723574f9E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #1 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h3aecff4c27496741E(i64 %new_size, i64 %_23) #20
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h2476662a0364f8f8E(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hc6a508531916984bE"(ptr align 4 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hbbc88f62c7739de3E"(ptr align 4 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h65fbcb9212356326E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #19
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #19
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #22 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #21
  unreachable
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17hbbc88f62c7739de3E"(ptr align 4 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i32, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::option::Option<T>::map_or_else
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hf459426a7b8e9cceE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %0, i64 %1, ptr align 8 %default) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_9 = alloca [1 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %1, ptr %2, align 8
  store i8 1, ptr %_10, align 1
  store i8 1, ptr %_9, align 1
  %3 = load ptr, ptr %self, align 8
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = ptrtoint ptr %3 to i64
  %7 = icmp eq i64 %6, 0
  %_4 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_4 to i1
  br i1 %8, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %t.0 = load ptr, ptr %self, align 8
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %9, align 8
  store i8 0, ptr %_9, align 1
; invoke core::ops::function::FnOnce::call_once
  invoke void @_ZN4core3ops8function6FnOnce9call_once17hfb20f7a3f979959eE(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %t.0, i64 %t.1)
          to label %bb4 unwind label %funclet_bb11

bb2:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke alloc::fmt::format::{{closure}}
  invoke void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h53fd32301d41dbfcE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %default)
          to label %bb5 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  %10 = load i8, ptr %_9, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb10, label %bb11_cleanup_trampoline_bb7

funclet_bb11:                                     ; preds = %bb3, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb5:                                              ; preds = %bb2
  br label %bb6

bb6:                                              ; preds = %bb9, %bb4, %bb5
  ret void

bb4:                                              ; preds = %bb3
  %12 = load i8, ptr %_10, align 1
  %13 = trunc nuw i8 %12 to i1
  br i1 %13, label %bb9, label %bb6

bb9:                                              ; preds = %bb4
  br label %bb6

bb7:                                              ; preds = %funclet_bb7
  %14 = load i8, ptr %_10, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb12, label %bb8

funclet_bb7:                                      ; preds = %bb10, %bb11_cleanup_trampoline_bb7
  %cleanuppad1 = cleanuppad within none []
  br label %bb7

bb11_cleanup_trampoline_bb7:                      ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb10:                                             ; preds = %bb11
  cleanupret from %cleanuppad unwind label %funclet_bb7

bb8:                                              ; preds = %bb12, %bb7
  cleanupret from %cleanuppad1 unwind to caller

bb12:                                             ; preds = %bb7
  br label %bb8

bb1:                                              ; No predecessors!
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17hc831955c108928abE(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #1 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #21
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7298bf9cde019665E"() unnamed_addr #1 {
start:
  ret i32 0
}

; alloc::fmt::format
; Function Attrs: inlinehint uwtable
define internal void @_ZN5alloc3fmt6format17h96ae74123e798c1bE(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %args) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  %_6.0 = load ptr, ptr %args, align 8
  %0 = getelementptr inbounds i8, ptr %args, i64 8
  %_6.1 = load i64, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %args, i64 16
  %_7.0 = load ptr, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  %_7.1 = load i64, ptr %2, align 8
  %3 = icmp eq i64 %_6.1, 0
  br i1 %3, label %bb4, label %bb5

bb4:                                              ; preds = %start
  %4 = icmp eq i64 %_7.1, 0
  br i1 %4, label %bb8, label %bb3

bb5:                                              ; preds = %start
  %5 = icmp eq i64 %_6.1, 1
  br i1 %5, label %bb6, label %bb3

bb8:                                              ; preds = %bb4
  store ptr inttoptr (i64 1 to ptr), ptr %_2, align 8
  %6 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 0, ptr %6, align 8
  br label %bb2

bb3:                                              ; preds = %bb6, %bb5, %bb4
  %7 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %8 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store ptr %7, ptr %_2, align 8
  %9 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %8, ptr %9, align 8
  br label %bb2

bb2:                                              ; preds = %bb3, %bb7, %bb8
  %10 = load ptr, ptr %_2, align 8
  %11 = getelementptr inbounds i8, ptr %_2, i64 8
  %12 = load i64, ptr %11, align 8
; call core::option::Option<T>::map_or_else
  call void @"_ZN4core6option15Option$LT$T$GT$11map_or_else17hf459426a7b8e9cceE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %10, i64 %12, ptr align 8 %args)
  ret void

bb6:                                              ; preds = %bb5
  %13 = icmp eq i64 %_7.1, 0
  br i1 %13, label %bb7, label %bb3

bb7:                                              ; preds = %bb6
  %s = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %14 = getelementptr inbounds nuw { ptr, i64 }, ptr %_6.0, i64 0
  %_13.0 = load ptr, ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  %_13.1 = load i64, ptr %15, align 8
  store ptr %_13.0, ptr %_2, align 8
  %16 = getelementptr inbounds i8, ptr %_2, i64 8
  store i64 %_13.1, ptr %16, align 8
  br label %bb2
}

; alloc::fmt::format::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3fmt6format28_$u7b$$u7b$closure$u7d$$u7d$17h53fd32301d41dbfcE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_1) unnamed_addr #1 {
start:
  %_2 = alloca [48 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_2, ptr align 8 %_1, i64 48, i1 false)
; call alloc::fmt::format::format_inner
  call void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %_2)
  ret void
}

; alloc::str::<impl alloc::borrow::ToOwned for str>::to_owned
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3str56_$LT$impl$u20$alloc..borrow..ToOwned$u20$for$u20$str$GT$8to_owned17hdc15e7bfc7393feaE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #1 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h701b6de2cafd7a48E"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; alloc::vec::Vec<T,A>::extend_trusted
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted17hea4515eeb586951cE"(ptr align 8 %self, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_22 = alloca [1 x i8], align 1
  %_21 = alloca [48 x i8], align 8
  %_19 = alloca [24 x i8], align 8
  %_18 = alloca [12 x i8], align 4
  %_5 = alloca [24 x i8], align 8
  %high = alloca [16 x i8], align 8
  store i8 1, ptr %_22, align 1
; invoke core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::size_hint
  invoke void @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$9size_hint17hd4c885ea8f9dc950E"(ptr sret([24 x i8]) align 8 %_5, ptr align 4 %iterator)
          to label %bb1 unwind label %funclet_bb8

bb8:                                              ; preds = %funclet_bb8
  %1 = load i8, ptr %_22, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb7, label %bb6

funclet_bb8:                                      ; preds = %bb5, %bb3, %bb2, %start
  %cleanuppad = cleanuppad within none []
  br label %bb8

bb1:                                              ; preds = %start
  %low = load i64, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %3, i64 8
  %6 = load i64, ptr %5, align 8
  store i64 %4, ptr %high, align 8
  %7 = getelementptr inbounds i8, ptr %high, i64 8
  store i64 %6, ptr %7, align 8
  %_7 = load i64, ptr %high, align 8
  %8 = getelementptr inbounds i8, ptr %high, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = trunc nuw i64 %_7 to i1
  br i1 %10, label %bb2, label %bb5

bb2:                                              ; preds = %bb1
  %11 = getelementptr inbounds i8, ptr %high, i64 8
  %additional = load i64, ptr %11, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17hb41f5159986c1fbbE"(ptr align 8 %self, i64 %additional, ptr align 8 %0)
          to label %bb3 unwind label %funclet_bb8

bb5:                                              ; preds = %bb1
  store ptr @alloc_11d257f5ed6cc7fc38feaa801053bac6, ptr %_21, align 8
  %12 = getelementptr inbounds i8, ptr %_21, i64 8
  store i64 1, ptr %12, align 8
  %13 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_21, i64 32
  store ptr %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  %17 = getelementptr inbounds i8, ptr %_21, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %17, i64 8
  store i64 0, ptr %18, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_21, ptr align 8 %0) #19
          to label %unreachable unwind label %funclet_bb8

bb3:                                              ; preds = %bb2
  %19 = getelementptr inbounds i8, ptr %self, i64 8
  %_23 = load ptr, ptr %19, align 8
  %len = getelementptr inbounds i8, ptr %self, i64 16
  %_24 = load i64, ptr %len, align 8
  store i8 0, ptr %_22, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_18, ptr align 4 %iterator, i64 12, i1 false)
  %20 = getelementptr inbounds i8, ptr %_19, i64 16
  store ptr %_23, ptr %20, align 8
  store ptr %len, ptr %_19, align 8
  %21 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %_24, ptr %21, align 8
; invoke core::iter::traits::iterator::Iterator::for_each
  invoke void @_ZN4core4iter6traits8iterator8Iterator8for_each17h5f9666aaf9aedf80E(ptr align 4 %_18, ptr align 8 %_19)
          to label %bb4 unwind label %funclet_bb8

bb4:                                              ; preds = %bb3
  ret void

unreachable:                                      ; preds = %bb5
  unreachable

bb9:                                              ; No predecessors!
  unreachable

bb6:                                              ; preds = %bb7, %bb8
  cleanupret from %cleanuppad unwind to caller

bb7:                                              ; preds = %bb8
  br label %bb6
}

; alloc::vec::Vec<T,A>::extend_trusted::{{closure}}
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted28_$u7b$$u7b$closure$u7d$$u7d$17hbbb792faeabe9478E"(ptr align 8 %_1, i32 %element) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_1, i64 16
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_1, i64 8
  %_5 = load i64, ptr %1, align 8
  %_3 = getelementptr inbounds nuw i32, ptr %_4, i64 %_5
  store i32 %element, ptr %_3, align 4
  %2 = getelementptr inbounds i8, ptr %_1, i64 8
  %3 = getelementptr inbounds i8, ptr %_1, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = add i64 %4, 1
  store i64 %5, ptr %2, align 8
  ret void
}

; alloc::vec::Vec<T,A>::extend_desugared
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17he412a12feb5f6797E"(ptr align 8 %self, ptr %0, ptr %1, ptr align 8 %2) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %3 = alloca [8 x i8], align 8
  %_11 = alloca [24 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 4
  %iterator = alloca [16 x i8], align 8
  store ptr %0, ptr %iterator, align 8
  %4 = getelementptr inbounds i8, ptr %iterator, i64 8
  store ptr %1, ptr %4, align 8
  br label %bb1

bb1:                                              ; preds = %bb8, %start
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::next
  %5 = invoke { i32, i32 } @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h00c051d61eabf906E"(ptr align 8 %iterator)
          to label %bb2 unwind label %funclet_bb12

bb12:                                             ; preds = %funclet_bb12
  cleanupret from %cleanuppad unwind to caller

funclet_bb12:                                     ; preds = %bb14, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb12

bb2:                                              ; preds = %bb1
  %6 = extractvalue { i32, i32 } %5, 0
  %7 = extractvalue { i32, i32 } %5, 1
  store i32 %6, ptr %_3, align 4
  %8 = getelementptr inbounds i8, ptr %_3, i64 4
  store i32 %7, ptr %8, align 4
  %9 = load i32, ptr %_3, align 4
  %10 = getelementptr inbounds i8, ptr %_3, i64 4
  %11 = load i32, ptr %10, align 4
  %_5 = zext i32 %9 to i64
  %12 = trunc nuw i64 %_5 to i1
  br i1 %12, label %bb3, label %bb9

bb3:                                              ; preds = %bb2
  %13 = getelementptr inbounds i8, ptr %_3, i64 4
  %element = load i32, ptr %13, align 4
  %14 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %14, align 8
  %_19 = icmp ule i64 %len, 2305843009213693951
  br label %bb17

bb9:                                              ; preds = %bb2
  br label %bb10

bb17:                                             ; preds = %bb3
  %self1 = load i64, ptr %self, align 8
  store i64 %self1, ptr %_9, align 8
  br label %bb15

bb16:                                             ; No predecessors!
  store i64 -1, ptr %_9, align 8
  br label %bb15

bb15:                                             ; preds = %bb17, %bb16
  %15 = load i64, ptr %_9, align 8
  %_8 = icmp eq i64 %len, %15
  br i1 %_8, label %bb4, label %bb7

bb7:                                              ; preds = %bb15
  br label %bb8

bb4:                                              ; preds = %bb15
; invoke <core::iter::adapters::cloned::Cloned<I> as core::iter::traits::iterator::Iterator>::size_hint
  invoke void @"_ZN104_$LT$core..iter..adapters..cloned..Cloned$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h160c25b23dcd7145E"(ptr sret([24 x i8]) align 8 %_11, ptr align 8 %iterator)
          to label %bb5 unwind label %funclet_bb14

bb8:                                              ; preds = %bb6, %bb7
  %16 = getelementptr inbounds i8, ptr %self, i64 8
  %_24 = load ptr, ptr %16, align 8
  %dst = getelementptr inbounds nuw i32, ptr %_24, i64 %len
  store i32 %element, ptr %dst, align 4
  %new_len = add i64 %len, 1
  %17 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %new_len, ptr %17, align 8
  br label %bb1

bb14:                                             ; preds = %funclet_bb14
  cleanupret from %cleanuppad2 unwind label %funclet_bb12

funclet_bb14:                                     ; preds = %bb5, %bb4
  %cleanuppad2 = cleanuppad within none []
  br label %bb14

bb5:                                              ; preds = %bb4
  %lower = load i64, ptr %_11, align 8
  %18 = call i64 @llvm.uadd.sat.i64(i64 %lower, i64 1)
  store i64 %18, ptr %3, align 8
  %_14 = load i64, ptr %3, align 8
; invoke alloc::vec::Vec<T,A>::reserve
  invoke void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17hb41f5159986c1fbbE"(ptr align 8 %self, i64 %_14, ptr align 8 %2)
          to label %bb6 unwind label %funclet_bb14

bb6:                                              ; preds = %bb5
  br label %bb8

bb10:                                             ; preds = %bb9
  ret void

bb19:                                             ; No predecessors!
  unreachable
}

; alloc::vec::Vec<T,A>::reserve
; Function Attrs: uwtable
define internal void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$7reserve17hb41f5159986c1fbbE"(ptr align 8 %self, i64 %additional, ptr align 8 %0) unnamed_addr #0 {
start:
  %self1 = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  store i64 4, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 4, ptr %2, align 8
  br label %bb6

bb6:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  store i64 %self2, ptr %self1, align 8
  br label %bb4

bb5:                                              ; No predecessors!
  store i64 -1, ptr %self1, align 8
  br label %bb4

bb4:                                              ; preds = %bb6, %bb5
  %3 = load i64, ptr %self1, align 8
  %_10 = sub i64 %3, %len
  %_7 = icmp ugt i64 %additional, %_10
  br i1 %_7, label %bb1, label %bb2

bb2:                                              ; preds = %bb4
  br label %bb3

bb1:                                              ; preds = %bb4
; call alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hfca6de03fe45f7d4E"(ptr align 8 %self, i64 %len, i64 %additional, i64 4, i64 4)
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  ret void
}

; alloc::vec::Vec<T,A>::as_slice
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17h7eea264a46657507E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb1

bb1:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h65fbcb9212356326E(ptr %_4, i64 4, i64 4, i64 %len) #20
  br label %bb3

bb3:                                              ; preds = %bb1
  %2 = insertvalue { ptr, i64 } poison, ptr %_4, 0
  %3 = insertvalue { ptr, i64 } %2, i64 %len, 1
  ret { ptr, i64 } %3
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17h296ba3b3a9e87520E(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h18dd26d79f7b93a2E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #20
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #20
  ret ptr %_0
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h0d70da1a9e79cb6bE(i64 %0, i64 %1) unnamed_addr #1 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17h18dd26d79f7b93a2E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #20
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #20
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h8c1cdf6b1feee63fE(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %_23) #20
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h0d70da1a9e79cb6bE(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17h296ba3b3a9e87520E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %ptr) #20
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %ptr4) #20
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::alloc::Global::grow_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17h6289ab5973ad728dE(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1, i64 %2, i64 %3, i1 zeroext %zeroed) unnamed_addr #1 {
start:
  %layout5 = alloca [16 x i8], align 8
  %_60 = alloca [8 x i8], align 8
  %layout4 = alloca [16 x i8], align 8
  %self3 = alloca [16 x i8], align 8
  %_35 = alloca [16 x i8], align 8
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_25 = alloca [8 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %old_size = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %old_layout, align 8
  %4 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %1, ptr %4, align 8
  store i64 %2, ptr %new_layout, align 8
  %5 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %3, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %7, ptr %old_size, align 8
  %8 = load i64, ptr %old_size, align 8
  %9 = icmp eq i64 %8, 0
  br i1 %9, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %10 = load i64, ptr %new_layout, align 8
  %11 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %12 = load i64, ptr %11, align 8
; call alloc::alloc::Global::alloc_impl
  %13 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h8c1cdf6b1feee63fE(ptr align 1 %self, i64 %10, i64 %12, i1 zeroext %zeroed)
  %14 = extractvalue { ptr, i64 } %13, 0
  %15 = extractvalue { ptr, i64 } %13, 1
  store ptr %14, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %15, ptr %16, align 8
  br label %bb9

bb1:                                              ; preds = %start
  %_43 = load i64, ptr %old_layout, align 8
  %_46 = icmp uge i64 %_43, 1
  %_47 = icmp ule i64 %_43, -9223372036854775808
  %_48 = and i1 %_46, %_47
  %_49 = load i64, ptr %new_layout, align 8
  %_52 = icmp uge i64 %_49, 1
  %_53 = icmp ule i64 %_49, -9223372036854775808
  %_54 = and i1 %_52, %_53
  %_11 = icmp eq i64 %_43, %_49
  br i1 %_11, label %bb3, label %bb4

bb9:                                              ; preds = %bb22, %bb30, %bb31, %bb37, %bb2
  %17 = load ptr, ptr %_0, align 8
  %18 = getelementptr inbounds i8, ptr %_0, i64 8
  %19 = load i64, ptr %18, align 8
  %20 = insertvalue { ptr, i64 } poison, ptr %17, 0
  %21 = insertvalue { ptr, i64 } %20, i64 %19, 1
  ret { ptr, i64 } %21

bb4:                                              ; preds = %bb1
  %22 = load i64, ptr %new_layout, align 8
  %23 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %24 = load i64, ptr %23, align 8
; call alloc::alloc::Global::alloc_impl
  %25 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h8c1cdf6b1feee63fE(ptr align 1 %self, i64 %22, i64 %24, i1 zeroext %zeroed)
  %26 = extractvalue { ptr, i64 } %25, 0
  %27 = extractvalue { ptr, i64 } %25, 1
  store ptr %26, ptr %self3, align 8
  %28 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %27, ptr %28, align 8
  %29 = load ptr, ptr %self3, align 8
  %30 = getelementptr inbounds i8, ptr %self3, i64 8
  %31 = load i64, ptr %30, align 8
  %32 = ptrtoint ptr %29 to i64
  %33 = icmp eq i64 %32, 0
  %_76 = select i1 %33, i64 1, i64 0
  %34 = trunc nuw i64 %_76 to i1
  br i1 %34, label %bb31, label %bb32

bb3:                                              ; preds = %bb1
  %35 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %new_size = load i64, ptr %35, align 8
  %36 = load i64, ptr %old_size, align 8
  %cond = icmp uge i64 %new_size, %36
  br label %bb10

bb31:                                             ; preds = %bb4
  %37 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %38 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store ptr %37, ptr %_0, align 8
  %39 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %38, ptr %39, align 8
  br label %bb9

bb32:                                             ; preds = %bb4
  %v.0 = load ptr, ptr %self3, align 8
  %40 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.1 = load i64, ptr %40, align 8
  store ptr %v.0, ptr %_35, align 8
  %41 = getelementptr inbounds i8, ptr %_35, i64 8
  store i64 %v.1, ptr %41, align 8
  %new_ptr.0 = load ptr, ptr %_35, align 8
  %42 = getelementptr inbounds i8, ptr %_35, i64 8
  %new_ptr.1 = load i64, ptr %42, align 8
  br label %bb33

bb33:                                             ; preds = %bb32
  %43 = load i64, ptr %old_size, align 8
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h833ed7e774297df7E(ptr %ptr, ptr %new_ptr.0, i64 1, i64 1, i64 %43) #20
  br label %bb35

bb35:                                             ; preds = %bb33
  %44 = load i64, ptr %old_size, align 8
  %45 = mul i64 %44, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %new_ptr.0, ptr align 1 %ptr, i64 %45, i1 false)
  %46 = load i64, ptr %old_layout, align 8
  %47 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %48 = load i64, ptr %47, align 8
  store i64 %46, ptr %layout4, align 8
  %49 = getelementptr inbounds i8, ptr %layout4, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr %old_size, align 8
  %51 = icmp eq i64 %50, 0
  br i1 %51, label %bb37, label %bb36

bb37:                                             ; preds = %bb36, %bb35
  store ptr %new_ptr.0, ptr %_0, align 8
  %52 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_ptr.1, ptr %52, align 8
  br label %bb9

bb36:                                             ; preds = %bb35
  %53 = load i64, ptr %old_layout, align 8
  %54 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %55 = load i64, ptr %54, align 8
  store i64 %53, ptr %layout5, align 8
  %56 = getelementptr inbounds i8, ptr %layout5, i64 8
  store i64 %55, ptr %56, align 8
  %57 = load i64, ptr %old_size, align 8
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %57, i64 %_43) #20
  br label %bb37

bb10:                                             ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hb22b9bc5f4d1c80fE(i1 zeroext %cond) #20
  %58 = load i64, ptr %old_layout, align 8
  %59 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %60 = load i64, ptr %59, align 8
  store i64 %58, ptr %layout, align 8
  %61 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %60, ptr %61, align 8
  %62 = load i64, ptr %old_size, align 8
; call __rustc::__rust_realloc
  %63 = call ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr %ptr, i64 %62, i64 %_43, i64 %new_size) #20
  store ptr %63, ptr %raw_ptr, align 8
  %ptr6 = load ptr, ptr %raw_ptr, align 8
  %64 = load ptr, ptr %raw_ptr, align 8
  store ptr %64, ptr %_60, align 8
  %65 = load ptr, ptr %raw_ptr, align 8
  %_61 = ptrtoint ptr %65 to i64
  %66 = icmp eq i64 %_61, 0
  br i1 %66, label %bb14, label %bb41

bb14:                                             ; preds = %bb10
  store ptr null, ptr %self2, align 8
  br label %bb13

bb41:                                             ; preds = %bb10
  br label %bb16

bb13:                                             ; preds = %bb18, %bb14
  %67 = load ptr, ptr %self2, align 8
  %68 = ptrtoint ptr %67 to i64
  %69 = icmp eq i64 %68, 0
  %_64 = select i1 %69, i64 0, i64 1
  %70 = trunc nuw i64 %_64 to i1
  br i1 %70, label %bb21, label %bb20

bb16:                                             ; preds = %bb41
  %_63 = load ptr, ptr %raw_ptr, align 8
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %_63) #20
  br label %bb18

bb18:                                             ; preds = %bb16
  %_59 = load ptr, ptr %_60, align 8
  store ptr %_59, ptr %self2, align 8
  br label %bb13

bb21:                                             ; preds = %bb13
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  br label %bb19

bb20:                                             ; preds = %bb13
  store ptr null, ptr %self1, align 8
  br label %bb19

bb19:                                             ; preds = %bb21, %bb20
  %71 = load ptr, ptr %self1, align 8
  %72 = ptrtoint ptr %71 to i64
  %73 = icmp eq i64 %72, 0
  %_66 = select i1 %73, i64 1, i64 0
  %74 = trunc nuw i64 %_66 to i1
  br i1 %74, label %bb22, label %bb23

bb22:                                             ; preds = %bb19
  %75 = load ptr, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %76 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store ptr %75, ptr %_0, align 8
  %77 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %76, ptr %77, align 8
  br label %bb9

bb23:                                             ; preds = %bb19
  %v7 = load ptr, ptr %self1, align 8
  store ptr %v7, ptr %_25, align 8
  %ptr8 = load ptr, ptr %_25, align 8
  br i1 %zeroed, label %bb6, label %bb7

bb7:                                              ; preds = %bb27, %bb23
  br label %bb28

bb6:                                              ; preds = %bb23
  %self9 = load ptr, ptr %raw_ptr, align 8
  %78 = load ptr, ptr %raw_ptr, align 8
  %79 = load i64, ptr %old_size, align 8
  %self10 = getelementptr inbounds nuw i8, ptr %78, i64 %79
  %80 = load i64, ptr %old_size, align 8
  %count = sub i64 %new_size, %80
  br label %bb25

bb25:                                             ; preds = %bb6
  %_70 = icmp eq i64 %count, 0
; call core::intrinsics::write_bytes::precondition_check
  call void @_ZN4core10intrinsics11write_bytes18precondition_check17h72ff8af2b98a7773E(ptr %self10, i64 1, i1 zeroext %_70) #20
  br label %bb27

bb27:                                             ; preds = %bb25
  %81 = mul i64 1, %count
  call void @llvm.memset.p0.i64(ptr align 1 %self10, i8 0, i64 %81, i1 false)
  br label %bb7

bb28:                                             ; preds = %bb7
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17hedddf19b12bd631eE"(ptr %ptr8) #20
  br label %bb30

bb30:                                             ; preds = %bb28
  store ptr %ptr8, ptr %_0, align 8
  %82 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %new_size, ptr %82, align 8
  br label %bb9

bb5:                                              ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable

bb12:                                             ; No predecessors!
  unreachable

bb15:                                             ; No predecessors!
  unreachable
}

; alloc::raw_vec::finish_grow
; Function Attrs: cold uwtable
define internal void @_ZN5alloc7raw_vec11finish_grow17h20b6fbcdd5109fecE(ptr sret([24 x i8]) align 8 %_0, i64 %0, i64 %1, ptr align 8 %current_memory, ptr align 1 %alloc) unnamed_addr #5 {
start:
  %_43 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %old_layout = alloca [16 x i8], align 8
  %memory = alloca [16 x i8], align 8
  %new_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %new_layout, align 8
  %2 = getelementptr inbounds i8, ptr %new_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %alloc_size = load i64, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = icmp eq i64 %5, 0
  %_9 = select i1 %6, i64 0, i64 1
  %7 = trunc nuw i64 %_9 to i1
  br i1 %7, label %bb3, label %bb2

bb3:                                              ; preds = %start
  %ptr = load ptr, ptr %current_memory, align 8
  %8 = getelementptr inbounds i8, ptr %current_memory, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = getelementptr inbounds i8, ptr %8, i64 8
  %11 = load i64, ptr %10, align 8
  store i64 %9, ptr %old_layout, align 8
  %12 = getelementptr inbounds i8, ptr %old_layout, i64 8
  store i64 %11, ptr %12, align 8
  %_26 = load i64, ptr %old_layout, align 8
  %_29 = icmp uge i64 %_26, 1
  %_30 = icmp ule i64 %_26, -9223372036854775808
  %_31 = and i1 %_29, %_30
  %_32 = load i64, ptr %new_layout, align 8
  %_35 = icmp uge i64 %_32, 1
  %_36 = icmp ule i64 %_32, -9223372036854775808
  %_37 = and i1 %_35, %_36
  %cond = icmp eq i64 %_26, %_32
  br label %bb7

bb2:                                              ; preds = %start
  %13 = load i64, ptr %new_layout, align 8
  %14 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %15 = load i64, ptr %14, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %16 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7effe1e6d5a532e7E"(ptr align 1 %alloc, i64 %13, i64 %15)
  %17 = extractvalue { ptr, i64 } %16, 0
  %18 = extractvalue { ptr, i64 } %16, 1
  store ptr %17, ptr %memory, align 8
  %19 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %18, ptr %19, align 8
  br label %bb6

bb7:                                              ; preds = %bb3
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hb22b9bc5f4d1c80fE(i1 zeroext %cond) #20
  br label %bb8

bb8:                                              ; preds = %bb7
  %20 = load i64, ptr %old_layout, align 8
  %21 = getelementptr inbounds i8, ptr %old_layout, i64 8
  %22 = load i64, ptr %21, align 8
  %23 = load i64, ptr %new_layout, align 8
  %24 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %25 = load i64, ptr %24, align 8
; call <alloc::alloc::Global as core::alloc::Allocator>::grow
  %26 = call { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17h10ff61ca5d5b951bE"(ptr align 1 %alloc, ptr %ptr, i64 %20, i64 %22, i64 %23, i64 %25)
  %27 = extractvalue { ptr, i64 } %26, 0
  %28 = extractvalue { ptr, i64 } %26, 1
  store ptr %27, ptr %memory, align 8
  %29 = getelementptr inbounds i8, ptr %memory, i64 8
  store i64 %28, ptr %29, align 8
  br label %bb6

bb6:                                              ; preds = %bb2, %bb8
  %30 = load ptr, ptr %memory, align 8
  %31 = getelementptr inbounds i8, ptr %memory, i64 8
  %32 = load i64, ptr %31, align 8
  store ptr %30, ptr %self, align 8
  %33 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %32, ptr %33, align 8
  %34 = load ptr, ptr %self, align 8
  %35 = getelementptr inbounds i8, ptr %self, i64 8
  %36 = load i64, ptr %35, align 8
  %37 = ptrtoint ptr %34 to i64
  %38 = icmp eq i64 %37, 0
  %_40 = select i1 %38, i64 1, i64 0
  %39 = trunc nuw i64 %_40 to i1
  br i1 %39, label %bb10, label %bb11

bb10:                                             ; preds = %bb6
  %_44.0 = load i64, ptr %new_layout, align 8
  %40 = getelementptr inbounds i8, ptr %new_layout, i64 8
  %_44.1 = load i64, ptr %40, align 8
  store i64 %_44.0, ptr %_43, align 8
  %41 = getelementptr inbounds i8, ptr %_43, i64 8
  store i64 %_44.1, ptr %41, align 8
  %_42.0 = load i64, ptr %_43, align 8
  %42 = getelementptr inbounds i8, ptr %_43, i64 8
  %_42.1 = load i64, ptr %42, align 8
  %43 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_42.0, ptr %43, align 8
  %44 = getelementptr inbounds i8, ptr %43, i64 8
  store i64 %_42.1, ptr %44, align 8
  store i64 1, ptr %_0, align 8
  br label %bb9

bb11:                                             ; preds = %bb6
  %t.0 = load ptr, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %t.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %t.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %t.1, ptr %47, align 8
  store i64 0, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb10, %bb11
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h568d7eaca3f3491dE"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h55921b4bece4aa85E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17he1405e4abb0e902eE"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h55921b4bece4aa85E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h46446d71a2b04e96E"(i64 %self1, i64 %self3) #20
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h3aecff4c27496741E(i64 %8, i64 %9) #20
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::grow_amortized
; Function Attrs: uwtable
define internal { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17heccdfb31ae3462ceE"(ptr align 8 %self, i64 %len, i64 %additional, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_56 = alloca [16 x i8], align 8
  %_51 = alloca [16 x i8], align 8
  %self9 = alloca [24 x i8], align 8
  %self8 = alloca [16 x i8], align 8
  %_38 = alloca [16 x i8], align 8
  %residual7 = alloca [16 x i8], align 8
  %_26 = alloca [24 x i8], align 8
  %self6 = alloca [24 x i8], align 8
  %_24 = alloca [24 x i8], align 8
  %residual5 = alloca [16 x i8], align 8
  %elem_layout4 = alloca [16 x i8], align 8
  %self3 = alloca [24 x i8], align 8
  %_19 = alloca [24 x i8], align 8
  %v1 = alloca [8 x i8], align 8
  %residual = alloca [16 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self1 = alloca [16 x i8], align 8
  %_7 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb1, label %bb2

bb1:                                              ; preds = %start
  %5 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %6 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %5, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %6, ptr %7, align 8
  br label %bb8

bb2:                                              ; preds = %start
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %len, i64 %additional)
  %_32.0 = extractvalue { i64, i1 } %8, 0
  %_32.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_32.1, label %bb9, label %bb11

bb8:                                              ; preds = %bb7, %bb24, %bb1
  %9 = load i64, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  %11 = load i64, ptr %10, align 8
  %12 = insertvalue { i64, i64 } poison, i64 %9, 0
  %13 = insertvalue { i64, i64 } %12, i64 %11, 1
  ret { i64, i64 } %13

bb11:                                             ; preds = %bb2
  %_33 = add nuw i64 %len, %additional
  %14 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %_33, ptr %14, align 8
  store i64 1, ptr %self2, align 8
  %15 = getelementptr inbounds i8, ptr %self2, i64 8
  %v = load i64, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %v, ptr %16, align 8
  store i64 -9223372036854775807, ptr %self1, align 8
  %17 = getelementptr inbounds i8, ptr %self1, i64 8
  %v10 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %v10, ptr %18, align 8
  store i64 -9223372036854775807, ptr %_7, align 8
  %19 = getelementptr inbounds i8, ptr %_7, i64 8
  %required_cap = load i64, ptr %19, align 8
  %self11 = load i64, ptr %self, align 8
  %v112 = mul i64 %self11, 2
; call core::cmp::Ord::max
  %cap = call i64 @_ZN4core3cmp3Ord3max17h780fdb583315c03bE(i64 %v112, i64 %required_cap)
  %20 = icmp eq i64 %size, 1
  br i1 %20, label %bb14, label %bb15

bb9:                                              ; preds = %bb2
  %21 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %22 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %21, ptr %self2, align 8
  %23 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %22, ptr %23, align 8
  %24 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %25 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %24, ptr %self1, align 8
  %26 = getelementptr inbounds i8, ptr %self1, i64 8
  store i64 %25, ptr %26, align 8
  %e.024 = load i64, ptr %self1, align 8
  %27 = getelementptr inbounds i8, ptr %self1, i64 8
  %e.125 = load i64, ptr %27, align 8
  store i64 %e.024, ptr %_38, align 8
  %28 = getelementptr inbounds i8, ptr %_38, i64 8
  store i64 %e.125, ptr %28, align 8
  %29 = load i64, ptr %_38, align 8
  %30 = getelementptr inbounds i8, ptr %_38, i64 8
  %31 = load i64, ptr %30, align 8
  store i64 %29, ptr %_7, align 8
  %32 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 %31, ptr %32, align 8
  %33 = load i64, ptr %_7, align 8
  %34 = getelementptr inbounds i8, ptr %_7, i64 8
  %35 = load i64, ptr %34, align 8
  store i64 %33, ptr %residual, align 8
  %36 = getelementptr inbounds i8, ptr %residual, i64 8
  store i64 %35, ptr %36, align 8
  %e.026 = load i64, ptr %residual, align 8
  %37 = getelementptr inbounds i8, ptr %residual, i64 8
  %e.127 = load i64, ptr %37, align 8
  store i64 %e.026, ptr %_0, align 8
  %38 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.127, ptr %38, align 8
  br label %bb7

bb14:                                             ; preds = %bb11
  store i64 8, ptr %v1, align 8
  br label %bb13

bb15:                                             ; preds = %bb11
  %_41 = icmp ule i64 %size, 1024
  br i1 %_41, label %bb16, label %bb17

bb13:                                             ; preds = %bb18, %bb14
  %39 = load i64, ptr %v1, align 8
; call core::cmp::Ord::max
  %cap13 = call i64 @_ZN4core3cmp3Ord3max17h780fdb583315c03bE(i64 %39, i64 %cap)
  %40 = load i64, ptr %elem_layout, align 8
  %41 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %42 = load i64, ptr %41, align 8
  store i64 %40, ptr %elem_layout4, align 8
  %43 = getelementptr inbounds i8, ptr %elem_layout4, i64 8
  store i64 %42, ptr %43, align 8
; call core::alloc::layout::Layout::repeat
  call void @_ZN4core5alloc6layout6Layout6repeat17h28e22ba7723574f9E(ptr sret([24 x i8]) align 8 %self9, ptr align 8 %elem_layout4, i64 %cap13)
  %44 = load i64, ptr %self9, align 8
  %45 = icmp eq i64 %44, 0
  %_45 = select i1 %45, i64 1, i64 0
  %46 = trunc nuw i64 %_45 to i1
  br i1 %46, label %bb21, label %bb22

bb17:                                             ; preds = %bb15
  store i64 1, ptr %v1, align 8
  br label %bb18

bb16:                                             ; preds = %bb15
  store i64 4, ptr %v1, align 8
  br label %bb18

bb18:                                             ; preds = %bb16, %bb17
  br label %bb13

bb21:                                             ; preds = %bb13
  %47 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %48 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %47, ptr %self8, align 8
  %49 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %48, ptr %49, align 8
  %50 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %51 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %52 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %52, i64 8
  store i64 %51, ptr %53, align 8
  store i64 1, ptr %self3, align 8
  %54 = getelementptr inbounds i8, ptr %self3, i64 8
  %e.020 = load i64, ptr %54, align 8
  %55 = getelementptr inbounds i8, ptr %54, i64 8
  %e.121 = load i64, ptr %55, align 8
  store i64 %e.020, ptr %_51, align 8
  %56 = getelementptr inbounds i8, ptr %_51, i64 8
  store i64 %e.121, ptr %56, align 8
  %57 = load i64, ptr %_51, align 8
  %58 = getelementptr inbounds i8, ptr %_51, i64 8
  %59 = load i64, ptr %58, align 8
  %60 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %57, ptr %60, align 8
  %61 = getelementptr inbounds i8, ptr %60, i64 8
  store i64 %59, ptr %61, align 8
  store i64 1, ptr %_19, align 8
  %62 = getelementptr inbounds i8, ptr %_19, i64 8
  %63 = load i64, ptr %62, align 8
  %64 = getelementptr inbounds i8, ptr %62, i64 8
  %65 = load i64, ptr %64, align 8
  store i64 %63, ptr %residual5, align 8
  %66 = getelementptr inbounds i8, ptr %residual5, i64 8
  store i64 %65, ptr %66, align 8
  %e.022 = load i64, ptr %residual5, align 8
  %67 = getelementptr inbounds i8, ptr %residual5, i64 8
  %e.123 = load i64, ptr %67, align 8
  store i64 %e.022, ptr %_0, align 8
  %68 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.123, ptr %68, align 8
  br label %bb6

bb22:                                             ; preds = %bb13
  %t.0 = load i64, ptr %self9, align 8
  %69 = getelementptr inbounds i8, ptr %self9, i64 8
  %t.1 = load i64, ptr %69, align 8
  %70 = getelementptr inbounds i8, ptr %self9, i64 16
  %t = load i64, ptr %70, align 8
  store i64 %t.0, ptr %self8, align 8
  %71 = getelementptr inbounds i8, ptr %self8, i64 8
  store i64 %t.1, ptr %71, align 8
  %t.014 = load i64, ptr %self8, align 8
  %72 = getelementptr inbounds i8, ptr %self8, i64 8
  %t.115 = load i64, ptr %72, align 8
  %73 = getelementptr inbounds i8, ptr %self3, i64 8
  store i64 %t.014, ptr %73, align 8
  %74 = getelementptr inbounds i8, ptr %73, i64 8
  store i64 %t.115, ptr %74, align 8
  store i64 0, ptr %self3, align 8
  %75 = getelementptr inbounds i8, ptr %self3, i64 8
  %v.0 = load i64, ptr %75, align 8
  %76 = getelementptr inbounds i8, ptr %75, i64 8
  %v.1 = load i64, ptr %76, align 8
  %77 = getelementptr inbounds i8, ptr %_19, i64 8
  store i64 %v.0, ptr %77, align 8
  %78 = getelementptr inbounds i8, ptr %77, i64 8
  store i64 %v.1, ptr %78, align 8
  store i64 0, ptr %_19, align 8
  %79 = getelementptr inbounds i8, ptr %_19, i64 8
  %new_layout.0 = load i64, ptr %79, align 8
  %80 = getelementptr inbounds i8, ptr %79, i64 8
  %new_layout.1 = load i64, ptr %80, align 8
  %81 = load i64, ptr %elem_layout, align 8
  %82 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %83 = load i64, ptr %82, align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h55921b4bece4aa85E"(ptr sret([24 x i8]) align 8 %_26, ptr align 8 %self, i64 %81, i64 %83)
  %_28 = getelementptr inbounds i8, ptr %self, i64 16
; call alloc::raw_vec::finish_grow
  call void @_ZN5alloc7raw_vec11finish_grow17h20b6fbcdd5109fecE(ptr sret([24 x i8]) align 8 %self6, i64 %new_layout.0, i64 %new_layout.1, ptr align 8 %_26, ptr align 1 %_28)
  %_53 = load i64, ptr %self6, align 8
  %84 = trunc nuw i64 %_53 to i1
  br i1 %84, label %bb23, label %bb24

bb23:                                             ; preds = %bb22
  %85 = getelementptr inbounds i8, ptr %self6, i64 8
  %e.0 = load i64, ptr %85, align 8
  %86 = getelementptr inbounds i8, ptr %85, i64 8
  %e.1 = load i64, ptr %86, align 8
  store i64 %e.0, ptr %_56, align 8
  %87 = getelementptr inbounds i8, ptr %_56, i64 8
  store i64 %e.1, ptr %87, align 8
  %88 = load i64, ptr %_56, align 8
  %89 = getelementptr inbounds i8, ptr %_56, i64 8
  %90 = load i64, ptr %89, align 8
  %91 = getelementptr inbounds i8, ptr %_24, i64 8
  store i64 %88, ptr %91, align 8
  %92 = getelementptr inbounds i8, ptr %91, i64 8
  store i64 %90, ptr %92, align 8
  store i64 1, ptr %_24, align 8
  %93 = getelementptr inbounds i8, ptr %_24, i64 8
  %94 = load i64, ptr %93, align 8
  %95 = getelementptr inbounds i8, ptr %93, i64 8
  %96 = load i64, ptr %95, align 8
  store i64 %94, ptr %residual7, align 8
  %97 = getelementptr inbounds i8, ptr %residual7, i64 8
  store i64 %96, ptr %97, align 8
  %e.018 = load i64, ptr %residual7, align 8
  %98 = getelementptr inbounds i8, ptr %residual7, i64 8
  %e.119 = load i64, ptr %98, align 8
  store i64 %e.018, ptr %_0, align 8
  %99 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %e.119, ptr %99, align 8
  br label %bb6

bb24:                                             ; preds = %bb22
  %100 = getelementptr inbounds i8, ptr %self6, i64 8
  %v.016 = load ptr, ptr %100, align 8
  %101 = getelementptr inbounds i8, ptr %100, i64 8
  %v.117 = load i64, ptr %101, align 8
  %102 = getelementptr inbounds i8, ptr %_24, i64 8
  store ptr %v.016, ptr %102, align 8
  %103 = getelementptr inbounds i8, ptr %102, i64 8
  store i64 %v.117, ptr %103, align 8
  store i64 0, ptr %_24, align 8
  %104 = getelementptr inbounds i8, ptr %_24, i64 8
  %ptr.0 = load ptr, ptr %104, align 8
  %105 = getelementptr inbounds i8, ptr %104, i64 8
  %ptr.1 = load i64, ptr %105, align 8
  %106 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %ptr.0, ptr %106, align 8
  store i64 %cap13, ptr %self, align 8
  %107 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.3, align 8
  %108 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.3, i64 8), align 8
  store i64 %107, ptr %_0, align 8
  %109 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %108, ptr %109, align 8
  br label %bb8

bb6:                                              ; preds = %bb21, %bb23
  br label %bb7

bb7:                                              ; preds = %bb9, %bb6
  br label %bb8

bb3:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h3586fefd7f8cd3fdE"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h28e22ba7723574f9E(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.a8415ff6e4c3370c8d8c02329ada54cc.0, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h9c181c5c8eb82310E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7effe1e6d5a532e7E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17he0e056f9ac547be1E"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #1 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h3586fefd7f8cd3fdE"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #19
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hb22b9bc5f4d1c80fE(i1 zeroext %cond) #20
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::reserve::do_reserve_and_handle
; Function Attrs: cold uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hfca6de03fe45f7d4E"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #5 {
start:
  %_5 = alloca [16 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::grow_amortized
  %0 = call { i64, i64 } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14grow_amortized17heccdfb31ae3462ceE"(ptr align 8 %slf, i64 %len, i64 %additional, i64 %elem_layout.0, i64 %elem_layout.1)
  %1 = extractvalue { i64, i64 } %0, 0
  %2 = extractvalue { i64, i64 } %0, 1
  store i64 %1, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %2, ptr %3, align 8
  %4 = load i64, ptr %_5, align 8
  %5 = getelementptr inbounds i8, ptr %_5, i64 8
  %6 = load i64, ptr %5, align 8
  %7 = icmp eq i64 %4, -9223372036854775807
  %_6 = select i1 %7, i64 0, i64 1
  %8 = trunc nuw i64 %_6 to i1
  br i1 %8, label %bb2, label %bb3

bb2:                                              ; preds = %start
  %err.0 = load i64, ptr %_5, align 8
  %9 = getelementptr inbounds i8, ptr %_5, i64 8
  %err.1 = load i64, ptr %9, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 @alloc_3d99a694a639f36d512dfe286335fb89) #19
  unreachable

bb3:                                              ; preds = %start
  ret void

bb4:                                              ; No predecessors!
  unreachable
}

; <&i32 as core::ops::arith::Rem<i32>>::rem
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN60_$LT$$RF$i32$u20$as$u20$core..ops..arith..Rem$LT$i32$GT$$GT$3rem17h8007297518854509E"(ptr align 4 %self, i32 %other, ptr align 8 %0) unnamed_addr #1 {
start:
  %_3 = load i32, ptr %self, align 4
; call <i32 as core::ops::arith::Rem>::rem
  %_0 = call i32 @"_ZN45_$LT$i32$u20$as$u20$core..ops..arith..Rem$GT$3rem17hf7161259ce26fd9eE"(i32 %_3, i32 %other, ptr align 8 %0)
  ret i32 %_0
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hb0da550b5497749aE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h65fbcb9212356326E(ptr %_8, i64 1, i64 1, i64 %len) #20
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1fe8a13668ab23e2E"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h86c1fa940c30bf7dE"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd605f1e62b5d677eE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17he7ce5591eb8b05a3E"(i32 %self.0, i32 %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { i32, i32 } poison, i32 %self.0, 0
  %1 = insertvalue { i32, i32 } %0, i32 %self.1, 1
  ret { i32, i32 } %1
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17he1405e4abb0e902eE"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #1 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #20
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17h9c181c5c8eb82310E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h8c1cdf6b1feee63fE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::grow
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$4grow17h10ff61ca5d5b951bE"(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::grow_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global9grow_impl17h6289ab5973ad728dE(ptr align 1 %self, ptr %ptr, i64 %old_layout.0, i64 %old_layout.1, i64 %new_layout.0, i64 %new_layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17h7effe1e6d5a532e7E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #1 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h8c1cdf6b1feee63fE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN65_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17hbb1e5ce08ff9406aE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h65fbcb9212356326E(ptr %_6, i64 4, i64 4, i64 %len) #20
  br label %bb4

bb4:                                              ; preds = %bb2
; call <[T] as core::fmt::Debug>::fmt
  %_0 = call zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17hd696894a67eb77b3E"(ptr align 4 %_6, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum
; Function Attrs: uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum17h763041b5ffb0e734E"(ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h0f2b813df4a24c82E"(ptr %iter.0, ptr %iter.1, i32 0)
  ret i32 %_0
}

; <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17hca8b1420b044c1b5E"(ptr align 1 %_1, i32 %a, ptr align 4 %b) unnamed_addr #1 {
start:
  %other = load i32, ptr %b, align 4
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %a, i32 %other)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i32 %_5.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_2ff564f83739a041825038989c62f69d) #19
  unreachable
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h323804df6d042ddbE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h81ea4d490ac29f42E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h719c16e8d09d5d14E"(ptr align 8 %self) unnamed_addr #1 {
start:
; call alloc::vec::Vec<T,A>::as_slice
  %0 = call { ptr, i64 } @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$8as_slice17h7eea264a46657507E"(ptr align 8 %self)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <T as core::iter::adapters::step_by::SpecRangeSetup<T>>::setup
; Function Attrs: inlinehint uwtable
define internal void @"_ZN76_$LT$T$u20$as$u20$core..iter..adapters..step_by..SpecRangeSetup$LT$T$GT$$GT$5setup17h4c06d5e1a5efc43fE"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %inner, i64 %_step) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %inner, i64 12, i1 false)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h48810ed9ab43e1b5E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h568d7eaca3f3491dE"(ptr align 8 %self, i64 4, i64 4)
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17heebfbdf2d6585446E"(ptr align 8 %self) unnamed_addr #0 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h568d7eaca3f3491dE"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <alloc::vec::set_len_on_drop::SetLenOnDrop as core::ops::drop::Drop>::drop
; Function Attrs: inlinehint uwtable
define internal void @"_ZN83_$LT$alloc..vec..set_len_on_drop..SetLenOnDrop$u20$as$u20$core..ops..drop..Drop$GT$4drop17h39d2f05eb352c7f8E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_2 = load i64, ptr %0, align 8
  %_3 = load ptr, ptr %self, align 8
  store i64 %_2, ptr %_3, align 8
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h701b6de2cafd7a48E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #1 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17he0e056f9ac547be1E"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h833ed7e774297df7E(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #20
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17ha92c92d6d709d487E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_0 = alloca [8 x i8], align 4
  %_4 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_4, align 4
  %_0.i = icmp slt i32 %_3.i, %_4.i
  br i1 %_0.i, label %bb2, label %bb4

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb5

bb2:                                              ; preds = %start
  %old = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %_6 = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h32c4ff1e670f4fb3E"(i32 %old, i64 1)
  store i32 %_6, ptr %self, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %old, ptr %0, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5

bb5:                                              ; preds = %bb2, %bb4
  %1 = load i32, ptr %_0, align 4
  %2 = getelementptr inbounds i8, ptr %_0, i64 4
  %3 = load i32, ptr %2, align 4
  %4 = insertvalue { i32, i32 } poison, i32 %1, 0
  %5 = insertvalue { i32, i32 } %4, i32 %3, 1
  ret { i32, i32 } %5
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::find
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4find17h4a5ffe3a9411d794E"(ptr align 8 %self, ptr align 1 %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %x = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %predicate = alloca [8 x i8], align 8
  store ptr %0, ptr %predicate, align 8
  br label %bb1

bb1:                                              ; preds = %bb6, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %1 = invoke align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h21296b86c74b4eb6E"(ptr align 8 %self)
          to label %bb2 unwind label %funclet_bb9

bb9:                                              ; preds = %funclet_bb9
  cleanupret from %cleanuppad unwind to caller

funclet_bb9:                                      ; preds = %bb3, %bb1
  %cleanuppad = cleanuppad within none []
  br label %bb9

bb2:                                              ; preds = %bb1
  store ptr %1, ptr %_3, align 8
  %2 = load ptr, ptr %_3, align 8
  %3 = ptrtoint ptr %2 to i64
  %4 = icmp eq i64 %3, 0
  %_4 = select i1 %4, i64 0, i64 1
  %5 = trunc nuw i64 %_4 to i1
  br i1 %5, label %bb3, label %bb7

bb3:                                              ; preds = %bb2
  %6 = load ptr, ptr %_3, align 8
  store ptr %6, ptr %x, align 8
; invoke core::ops::function::impls::<impl core::ops::function::FnMut<A> for &mut F>::call_mut
  %_6 = invoke zeroext i1 @"_ZN4core3ops8function5impls79_$LT$impl$u20$core..ops..function..FnMut$LT$A$GT$$u20$for$u20$$RF$mut$u20$F$GT$8call_mut17h1e90f34a31640817E"(ptr align 8 %predicate, ptr align 8 %x)
          to label %bb4 unwind label %funclet_bb9

bb7:                                              ; preds = %bb2
  store ptr null, ptr %_0, align 8
  br label %bb8

bb4:                                              ; preds = %bb3
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  br label %bb1

bb5:                                              ; preds = %bb4
  %7 = load ptr, ptr %x, align 8
  store ptr %7, ptr %_0, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb5
  %8 = load ptr, ptr %_0, align 8
  ret ptr %8

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h0f2b813df4a24c82E"(ptr %0, ptr %1, i32 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [4 x i8], align 4
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [4 x i8], align 4
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store i32 %init, ptr %acc, align 4
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i32 %init, ptr %_0, align 4
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h25d4b84e7cb7566aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load i32, ptr %acc, align 4
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw i32, ptr %self1, i64 %count
; invoke <i32 as core::iter::traits::accum::Sum<&i32>>::sum::{{closure}}
  %_19 = invoke i32 @"_ZN69_$LT$i32$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$i32$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17hca8b1420b044c1b5E"(ptr align 1 %f, i32 %_22, ptr align 4 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store i32 %_19, ptr %acc, align 4
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17h77e3d0ea9445378dE"(i64 %self2, i64 1) #20
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load i32, ptr %acc, align 4
  store i32 %12, ptr %_0, align 4
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load i32, ptr %_0, align 4
  ret i32 %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 4 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h21296b86c74b4eb6E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i32, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::size_hint
; Function Attrs: inlinehint uwtable
define internal void @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$9size_hint17h48d5d8e6c7800f1cE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  %_9 = alloca [16 x i8], align 8
  %exact = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_6 = load ptr, ptr %0, align 8
  %_7 = load ptr, ptr %self, align 8
; call core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %1 = call i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h25d4b84e7cb7566aE"(ptr %_6, ptr %_7)
  store i64 %1, ptr %exact, align 8
  br label %bb4

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %exact, align 8
  %_10 = load i64, ptr %exact, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_10, ptr %2, align 8
  store i64 1, ptr %_9, align 8
  store i64 %_8, ptr %_0, align 8
  %3 = load i64, ptr %_9, align 8
  %4 = getelementptr inbounds i8, ptr %_9, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 %5, ptr %7, align 8
  ret void

bb1:                                              ; No predecessors!
  unreachable
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
; Function Attrs: inlinehint uwtable
define internal void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17h319393141526dfe3E"() unnamed_addr #1 {
start:
  ret void
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal void @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h0954e1ae90ab4e29E"() unnamed_addr #1 {
start:
  %_0 = alloca [0 x i8], align 1
  ret void
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17h2396369265a8c4edE"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iter, ptr align 8 %0) unnamed_addr #1 {
start:
  %_2 = alloca [12 x i8], align 4
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_2, ptr align 4 %iter)
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h8eae4223949490e0E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %_2, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as core::iter::traits::collect::FromIterator<T>>::from_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN95_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..collect..FromIterator$LT$T$GT$$GT$9from_iter17ha6343f793688bf36E"(ptr sret([24 x i8]) align 8 %_0, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #1 {
start:
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %1 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1fe8a13668ab23e2E"(ptr %iter.0, ptr %iter.1)
  %_2.0 = extractvalue { ptr, ptr } %1, 0
  %_2.1 = extractvalue { ptr, ptr } %1, 1
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
  call void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h3dd8b49f7a8cb8d5E"(ptr sret([24 x i8]) align 8 %_0, ptr %_2.0, ptr %_2.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h2d7125b8c0b6bcb3E"(ptr align 8 %self, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_trusted
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$14extend_trusted17hea4515eeb586951cE"(ptr align 8 %self, ptr align 4 %iterator, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T,A> as alloc::vec::spec_extend::SpecExtend<T,I>>::spec_extend
; Function Attrs: uwtable
define internal void @"_ZN97_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$alloc..vec..spec_extend..SpecExtend$LT$T$C$I$GT$$GT$11spec_extend17h3cbc4e7895ba4720E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call alloc::vec::Vec<T,A>::extend_desugared
  call void @"_ZN5alloc3vec16Vec$LT$T$C$A$GT$16extend_desugared17he412a12feb5f6797E"(ptr align 8 %self, ptr %iter.0, ptr %iter.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h3dd8b49f7a8cb8d5E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17hcd22e163a9162da4E"(ptr sret([24 x i8]) align 8 %_0, ptr %iterator.0, ptr %iterator.1, ptr align 8 %0)
  ret void
}

; <alloc::vec::Vec<T> as alloc::vec::spec_from_iter::SpecFromIter<T,I>>::from_iter
; Function Attrs: uwtable
define internal void @"_ZN98_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter..SpecFromIter$LT$T$C$I$GT$$GT$9from_iter17h8eae4223949490e0E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0) unnamed_addr #0 {
start:
; call <alloc::vec::Vec<T> as alloc::vec::spec_from_iter_nested::SpecFromIterNested<T,I>>::from_iter
  call void @"_ZN111_$LT$alloc..vec..Vec$LT$T$GT$$u20$as$u20$alloc..vec..spec_from_iter_nested..SpecFromIterNested$LT$T$C$I$GT$$GT$9from_iter17h173dacefec502728E"(ptr sret([24 x i8]) align 8 %_0, ptr align 4 %iterator, ptr align 8 %0)
  ret void
}

; _05_loops::main
; Function Attrs: uwtable
define internal void @_ZN9_05_loops4main17haf3f005522340343E() unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_482 = alloca [48 x i8], align 8
  %_479 = alloca [48 x i8], align 8
  %_476 = alloca [16 x i8], align 8
  %_475 = alloca [16 x i8], align 8
  %_472 = alloca [48 x i8], align 8
  %evens = alloca [24 x i8], align 8
  %_463 = alloca [16 x i8], align 8
  %_462 = alloca [16 x i8], align 8
  %_459 = alloca [48 x i8], align 8
  %sum17 = alloca [4 x i8], align 4
  %_453 = alloca [12 x i8], align 4
  %numbers = alloca [24 x i8], align 8
  %_450 = alloca [48 x i8], align 8
  %_447 = alloca [48 x i8], align 8
  %_444 = alloca [16 x i8], align 8
  %_443 = alloca [16 x i8], align 8
  %_440 = alloca [48 x i8], align 8
  %_437 = alloca [16 x i8], align 8
  %_435 = alloca [16 x i8], align 8
  %_434 = alloca [32 x i8], align 8
  %_431 = alloca [48 x i8], align 8
  %res = alloca [24 x i8], align 8
  %repeated = alloca [24 x i8], align 8
  %i16 = alloca [4 x i8], align 4
  %_425 = alloca [8 x i8], align 4
  %iter15 = alloca [12 x i8], align 4
  %_423 = alloca [12 x i8], align 4
  %_422 = alloca [12 x i8], align 4
  %base = alloca [16 x i8], align 8
  %_419 = alloca [48 x i8], align 8
  %_416 = alloca [48 x i8], align 8
  %_413 = alloca [16 x i8], align 8
  %_411 = alloca [16 x i8], align 8
  %_410 = alloca [32 x i8], align 8
  %_407 = alloca [48 x i8], align 8
  %_400 = alloca [4 x i8], align 4
  %_398 = alloca [16 x i8], align 8
  %_396 = alloca [16 x i8], align 8
  %_394 = alloca [16 x i8], align 8
  %_393 = alloca [48 x i8], align 8
  %_390 = alloca [48 x i8], align 8
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  %_382 = alloca [48 x i8], align 8
  %_379 = alloca [48 x i8], align 8
  %_376 = alloca [48 x i8], align 8
  %_373 = alloca [48 x i8], align 8
  %_368 = alloca [8 x i8], align 4
  %iter14 = alloca [12 x i8], align 4
  %_366 = alloca [12 x i8], align 4
  %_365 = alloca [12 x i8], align 4
  %_361 = alloca [8 x i8], align 4
  %iter13 = alloca [12 x i8], align 4
  %_359 = alloca [12 x i8], align 4
  %_358 = alloca [12 x i8], align 4
  %_356 = alloca [48 x i8], align 8
  %_353 = alloca [48 x i8], align 8
  %_350 = alloca [16 x i8], align 8
  %_349 = alloca [16 x i8], align 8
  %_346 = alloca [48 x i8], align 8
  %_343 = alloca [16 x i8], align 8
  %_341 = alloca [16 x i8], align 8
  %_340 = alloca [32 x i8], align 8
  %_337 = alloca [48 x i8], align 8
  %i12 = alloca [4 x i8], align 4
  %_331 = alloca [8 x i8], align 4
  %iter11 = alloca [12 x i8], align 4
  %_329 = alloca [12 x i8], align 4
  %_328 = alloca [12 x i8], align 4
  %factorial = alloca [4 x i8], align 4
  %_325 = alloca [48 x i8], align 8
  %_322 = alloca [48 x i8], align 8
  %_318 = alloca [16 x i8], align 8
  %_317 = alloca [16 x i8], align 8
  %_314 = alloca [48 x i8], align 8
  %_311 = alloca [16 x i8], align 8
  %_310 = alloca [16 x i8], align 8
  %_307 = alloca [48 x i8], align 8
  %found_at = alloca [4 x i8], align 4
  %search = alloca [4 x i8], align 4
  %_298 = alloca [48 x i8], align 8
  %_295 = alloca [48 x i8], align 8
  %_292 = alloca [16 x i8], align 8
  %_290 = alloca [16 x i8], align 8
  %_289 = alloca [32 x i8], align 8
  %_286 = alloca [48 x i8], align 8
  %_283 = alloca [16 x i8], align 8
  %_282 = alloca [16 x i8], align 8
  %_279 = alloca [48 x i8], align 8
  %j = alloca [4 x i8], align 4
  %_273 = alloca [8 x i8], align 4
  %iter10 = alloca [12 x i8], align 4
  %_271 = alloca [12 x i8], align 4
  %_270 = alloca [12 x i8], align 4
  %_268 = alloca [16 x i8], align 8
  %_267 = alloca [16 x i8], align 8
  %_264 = alloca [48 x i8], align 8
  %i9 = alloca [4 x i8], align 4
  %_259 = alloca [8 x i8], align 4
  %iter8 = alloca [12 x i8], align 4
  %_257 = alloca [12 x i8], align 4
  %_256 = alloca [12 x i8], align 4
  %_254 = alloca [48 x i8], align 8
  %_251 = alloca [48 x i8], align 8
  %_248 = alloca [16 x i8], align 8
  %_247 = alloca [16 x i8], align 8
  %_244 = alloca [48 x i8], align 8
  %_240 = alloca [16 x i8], align 8
  %_239 = alloca [16 x i8], align 8
  %_236 = alloca [48 x i8], align 8
  %guess = alloca [4 x i8], align 4
  %_229 = alloca [48 x i8], align 8
  %_226 = alloca [48 x i8], align 8
  %_223 = alloca [16 x i8], align 8
  %_221 = alloca [16 x i8], align 8
  %_220 = alloca [32 x i8], align 8
  %_217 = alloca [48 x i8], align 8
  %square = alloca [4 x i8], align 4
  %i7 = alloca [4 x i8], align 4
  %_210 = alloca [8 x i8], align 4
  %iter6 = alloca [12 x i8], align 4
  %_208 = alloca [12 x i8], align 4
  %_207 = alloca [12 x i8], align 4
  %_205 = alloca [48 x i8], align 8
  %_202 = alloca [48 x i8], align 8
  %_199 = alloca [16 x i8], align 8
  %_198 = alloca [16 x i8], align 8
  %_195 = alloca [48 x i8], align 8
  %_191 = alloca [16 x i8], align 8
  %_189 = alloca [16 x i8], align 8
  %_188 = alloca [32 x i8], align 8
  %_185 = alloca [48 x i8], align 8
  %sum = alloca [4 x i8], align 4
  %x = alloca [4 x i8], align 4
  %_174 = alloca [48 x i8], align 8
  %_171 = alloca [48 x i8], align 8
  %_168 = alloca [48 x i8], align 8
  %_165 = alloca [16 x i8], align 8
  %_163 = alloca [16 x i8], align 8
  %_162 = alloca [32 x i8], align 8
  %_159 = alloca [48 x i8], align 8
  %col = alloca [4 x i8], align 4
  %_154 = alloca [8 x i8], align 4
  %iter5 = alloca [12 x i8], align 4
  %_152 = alloca [12 x i8], align 4
  %_151 = alloca [12 x i8], align 4
  %row = alloca [4 x i8], align 4
  %_147 = alloca [8 x i8], align 4
  %iter4 = alloca [12 x i8], align 4
  %_145 = alloca [12 x i8], align 4
  %_144 = alloca [12 x i8], align 4
  %_142 = alloca [48 x i8], align 8
  %_139 = alloca [48 x i8], align 8
  %_134 = alloca [16 x i8], align 8
  %_132 = alloca [16 x i8], align 8
  %_130 = alloca [16 x i8], align 8
  %_129 = alloca [48 x i8], align 8
  %_126 = alloca [48 x i8], align 8
  %result = alloca [4 x i8], align 4
  %inner = alloca [4 x i8], align 4
  %_116 = alloca [16 x i8], align 8
  %_115 = alloca [16 x i8], align 8
  %_112 = alloca [48 x i8], align 8
  %outer = alloca [4 x i8], align 4
  %_106 = alloca [48 x i8], align 8
  %_103 = alloca [48 x i8], align 8
  %_100 = alloca [16 x i8], align 8
  %_99 = alloca [16 x i8], align 8
  %_96 = alloca [48 x i8], align 8
  %i3 = alloca [4 x i8], align 4
  %_91 = alloca [8 x i8], align 4
  %iter2 = alloca [24 x i8], align 8
  %_89 = alloca [12 x i8], align 4
  %_88 = alloca [24 x i8], align 8
  %_87 = alloca [24 x i8], align 8
  %_85 = alloca [48 x i8], align 8
  %_82 = alloca [48 x i8], align 8
  %_79 = alloca [16 x i8], align 8
  %_78 = alloca [16 x i8], align 8
  %_75 = alloca [48 x i8], align 8
  %num = alloca [4 x i8], align 4
  %_70 = alloca [8 x i8], align 4
  %iter1 = alloca [12 x i8], align 4
  %_68 = alloca [12 x i8], align 4
  %_67 = alloca [12 x i8], align 4
  %_65 = alloca [48 x i8], align 8
  %_62 = alloca [48 x i8], align 8
  %_59 = alloca [16 x i8], align 8
  %_58 = alloca [16 x i8], align 8
  %_55 = alloca [48 x i8], align 8
  %i = alloca [4 x i8], align 4
  %_50 = alloca [8 x i8], align 4
  %iter = alloca [8 x i8], align 4
  %_45 = alloca [48 x i8], align 8
  %_42 = alloca [48 x i8], align 8
  %_39 = alloca [48 x i8], align 8
  %_35 = alloca [16 x i8], align 8
  %_34 = alloca [16 x i8], align 8
  %_31 = alloca [48 x i8], align 8
  %countdown = alloca [4 x i8], align 4
  %_25 = alloca [48 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_18 = alloca [16 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_14 = alloca [48 x i8], align 8
  %counter = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_6e52b505a51406609023bcebfd1fca2b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_6760f45ebbe0fa7de952b2d501781225)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
  store i32 1, ptr %counter, align 4
  br label %bb7

bb7:                                              ; preds = %bb12, %start
  %_12 = load i32, ptr %counter, align 4
  %_11 = icmp sle i32 %_12, 5
  br i1 %_11, label %bb8, label %bb13

bb13:                                             ; preds = %bb7
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_22, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_22)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_25, ptr align 8 @alloc_9a8f9864ba7eb3002bddca72bb5ac004)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_25)
  store i32 10, ptr %countdown, align 4
  br label %bb18

bb8:                                              ; preds = %bb7
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_18, ptr align 4 %counter)
  %0 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_17, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 8 %_18, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_14, ptr align 8 @alloc_44ed4d9eee8b86b35581a8355e36756e, ptr align 8 %_17)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_14)
  %1 = load i32, ptr %counter, align 4
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %1, i32 1)
  %_20.0 = extractvalue { i32, i1 } %2, 0
  %_20.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_20.1, label %panic34, label %bb12

bb18:                                             ; preds = %bb23, %bb13
  %_29 = load i32, ptr %countdown, align 4
  %_28 = icmp sgt i32 %_29, 0
  br i1 %_28, label %bb19, label %bb24

bb24:                                             ; preds = %bb18
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_39, ptr align 8 @alloc_48f68c82c0a8f1e86b727a82ab78a744)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_39)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_42, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_42)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_45, ptr align 8 @alloc_f04dd6a3deeff2fa3d370bdeb8488bdd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_45)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %3 = call { i32, i32 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17he7ce5591eb8b05a3E"(i32 0, i32 5)
  %_47.0 = extractvalue { i32, i32 } %3, 0
  %_47.1 = extractvalue { i32, i32 } %3, 1
  store i32 %_47.0, ptr %iter, align 4
  %4 = getelementptr inbounds i8, ptr %iter, i64 4
  store i32 %_47.1, ptr %4, align 4
  br label %bb32

bb19:                                             ; preds = %bb18
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_35, ptr align 4 %countdown)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_34, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_35, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_31, ptr align 8 @alloc_10015caba44e6f8e925ee543e933f28f, ptr align 8 %_34)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_31)
  %6 = load i32, ptr %countdown, align 4
  %7 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %6, i32 1)
  %_37.0 = extractvalue { i32, i1 } %7, 0
  %_37.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_37.1, label %panic33, label %bb23

bb32:                                             ; preds = %bb35, %bb24
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
  %8 = call { i32, i32 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h316d8d4b8b19e6b0E"(ptr align 4 %iter)
  %9 = extractvalue { i32, i32 } %8, 0
  %10 = extractvalue { i32, i32 } %8, 1
  store i32 %9, ptr %_50, align 4
  %11 = getelementptr inbounds i8, ptr %_50, i64 4
  store i32 %10, ptr %11, align 4
  %12 = load i32, ptr %_50, align 4
  %13 = getelementptr inbounds i8, ptr %_50, i64 4
  %14 = load i32, ptr %13, align 4
  %_52 = zext i32 %12 to i64
  %15 = trunc nuw i64 %_52 to i1
  br i1 %15, label %bb35, label %bb36

bb35:                                             ; preds = %bb32
  %16 = getelementptr inbounds i8, ptr %_50, i64 4
  %17 = load i32, ptr %16, align 4
  store i32 %17, ptr %i, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_59, ptr align 4 %i)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_58, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_59, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_55, ptr align 8 @alloc_f93362d57036eb6e3e6272ae74459296, ptr align 8 %_58)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_55)
  br label %bb32

bb36:                                             ; preds = %bb32
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_62, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_62)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_65, ptr align 8 @alloc_d81bf84857e314d51972dcfbd89fdab8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_65)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_68, i32 1, i32 10)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_67, ptr align 4 %_68)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter1, ptr align 4 %_67, i64 12, i1 false)
  br label %bb45

bb45:                                             ; preds = %bb47, %bb36
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %19 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter1)
  %20 = extractvalue { i32, i32 } %19, 0
  %21 = extractvalue { i32, i32 } %19, 1
  store i32 %20, ptr %_70, align 4
  %22 = getelementptr inbounds i8, ptr %_70, i64 4
  store i32 %21, ptr %22, align 4
  %23 = load i32, ptr %_70, align 4
  %24 = getelementptr inbounds i8, ptr %_70, i64 4
  %25 = load i32, ptr %24, align 4
  %_72 = zext i32 %23 to i64
  %26 = trunc nuw i64 %_72 to i1
  br i1 %26, label %bb47, label %bb48

bb47:                                             ; preds = %bb45
  %27 = getelementptr inbounds i8, ptr %_70, i64 4
  %28 = load i32, ptr %27, align 4
  store i32 %28, ptr %num, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_79, ptr align 4 %num)
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_78, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_79, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_75, ptr align 8 @alloc_e63551567d31bfd937c248be61fc4a17, ptr align 8 %_78)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_75)
  br label %bb45

bb48:                                             ; preds = %bb45
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_82, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_82)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_85, ptr align 8 @alloc_4b644afd3b2d7bf138ebcff9a7522c01)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_85)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_89, i32 2, i32 10)
; call core::iter::traits::iterator::Iterator::step_by
  call void @_ZN4core4iter6traits8iterator8Iterator7step_by17h88860e30bc8949cdE(ptr sret([24 x i8]) align 8 %_88, ptr align 4 %_89, i64 2)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd605f1e62b5d677eE"(ptr sret([24 x i8]) align 8 %_87, ptr align 8 %_88)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter2, ptr align 8 %_87, i64 24, i1 false)
  br label %bb58

bb58:                                             ; preds = %bb60, %bb48
; call <core::iter::adapters::step_by::StepBy<I> as core::iter::traits::iterator::Iterator>::next
  %30 = call { i32, i32 } @"_ZN105_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h9f0b43dc2fb29dbbE"(ptr align 8 %iter2)
  %31 = extractvalue { i32, i32 } %30, 0
  %32 = extractvalue { i32, i32 } %30, 1
  store i32 %31, ptr %_91, align 4
  %33 = getelementptr inbounds i8, ptr %_91, i64 4
  store i32 %32, ptr %33, align 4
  %34 = load i32, ptr %_91, align 4
  %35 = getelementptr inbounds i8, ptr %_91, i64 4
  %36 = load i32, ptr %35, align 4
  %_93 = zext i32 %34 to i64
  %37 = trunc nuw i64 %_93 to i1
  br i1 %37, label %bb60, label %bb61

bb60:                                             ; preds = %bb58
  %38 = getelementptr inbounds i8, ptr %_91, i64 4
  %39 = load i32, ptr %38, align 4
  store i32 %39, ptr %i3, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_100, ptr align 4 %i3)
  %40 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %40, ptr align 8 %_100, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_96, ptr align 8 @alloc_a37200dd26e9374fe4498e5684023e1f, ptr align 8 %_99)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_96)
  br label %bb58

bb61:                                             ; preds = %bb58
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_103, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_103)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_106, ptr align 8 @alloc_8c21df12b0f05eafb2c669d6959c6be7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_106)
  store i32 1, ptr %outer, align 4
  br label %bb68

bb68:                                             ; preds = %bb83, %bb61
  %_110 = load i32, ptr %outer, align 4
  %_109 = icmp sle i32 %_110, 3
  br i1 %_109, label %bb69, label %bb85

bb85:                                             ; preds = %bb68
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_142, ptr align 8 @alloc_86706144e054f4abce370fdbc482cf4b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_142)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_145, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_144, ptr align 4 %_145)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter4, ptr align 4 %_144, i64 12, i1 false)
  br label %bb90

bb69:                                             ; preds = %bb68
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_116, ptr align 4 %outer)
  %41 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_115, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %41, ptr align 8 %_116, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_112, ptr align 8 @alloc_f82224d350f7b7e724a4242c07bd6e94, ptr align 8 %_115)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_112)
  store i32 1, ptr %inner, align 4
  br label %bb73

bb90:                                             ; preds = %bb99, %bb85
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %42 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter4)
  %43 = extractvalue { i32, i32 } %42, 0
  %44 = extractvalue { i32, i32 } %42, 1
  store i32 %43, ptr %_147, align 4
  %45 = getelementptr inbounds i8, ptr %_147, i64 4
  store i32 %44, ptr %45, align 4
  %46 = load i32, ptr %_147, align 4
  %47 = getelementptr inbounds i8, ptr %_147, i64 4
  %48 = load i32, ptr %47, align 4
  %_149 = zext i32 %46 to i64
  %49 = trunc nuw i64 %_149 to i1
  br i1 %49, label %bb92, label %bb93

bb92:                                             ; preds = %bb90
  %50 = getelementptr inbounds i8, ptr %_147, i64 4
  %51 = load i32, ptr %50, align 4
  store i32 %51, ptr %row, align 4
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_152, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_151, ptr align 4 %_152)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter5, ptr align 4 %_151, i64 12, i1 false)
  br label %bb96

bb93:                                             ; preds = %bb90
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_171, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_171)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_174, ptr align 8 @alloc_84704fa8740720bd5fd5fec22ce2b617)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_174)
  store i32 1, ptr %x, align 4
  store i32 0, ptr %sum, align 4
  br label %bb108

bb108:                                            ; preds = %bb116, %bb93
  %_179 = load i32, ptr %x, align 4
  %_178 = icmp sle i32 %_179, 10
  br i1 %_178, label %bb109, label %bb117

bb117:                                            ; preds = %bb109, %bb108
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_199, ptr align 4 %sum)
  %52 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_198, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %52, ptr align 8 %_199, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_195, ptr align 8 @alloc_a5213196877d3a44725d0f5190d7c0de, ptr align 8 %_198)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_195)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_202, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_202)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_205, ptr align 8 @alloc_4c334eb58b7b61dfa11577ff45d06830)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_205)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_208, i32 1, i32 5)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_207, ptr align 4 %_208)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter6, ptr align 4 %_207, i64 12, i1 false)
  br label %bb127

bb109:                                            ; preds = %bb108
  %_181 = load i32, ptr %sum, align 4
  %_180 = icmp slt i32 %_181, 30
  br i1 %_180, label %bb110, label %bb117

bb110:                                            ; preds = %bb109
  %_182 = load i32, ptr %x, align 4
  %53 = load i32, ptr %sum, align 4
  %54 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %53, i32 %_182)
  %_183.0 = extractvalue { i32, i1 } %54, 0
  %_183.1 = extractvalue { i32, i1 } %54, 1
  br i1 %_183.1, label %panic28, label %bb111

bb127:                                            ; preds = %bb131, %bb117
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %55 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter6)
  %56 = extractvalue { i32, i32 } %55, 0
  %57 = extractvalue { i32, i32 } %55, 1
  store i32 %56, ptr %_210, align 4
  %58 = getelementptr inbounds i8, ptr %_210, i64 4
  store i32 %57, ptr %58, align 4
  %59 = load i32, ptr %_210, align 4
  %60 = getelementptr inbounds i8, ptr %_210, i64 4
  %61 = load i32, ptr %60, align 4
  %_212 = zext i32 %59 to i64
  %62 = trunc nuw i64 %_212 to i1
  br i1 %62, label %bb129, label %bb130

bb129:                                            ; preds = %bb127
  %63 = getelementptr inbounds i8, ptr %_210, i64 4
  %64 = load i32, ptr %63, align 4
  store i32 %64, ptr %i7, align 4
  %65 = load i32, ptr %i7, align 4
  %66 = load i32, ptr %i7, align 4
  %67 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %65, i32 %66)
  %_215.0 = extractvalue { i32, i1 } %67, 0
  %_215.1 = extractvalue { i32, i1 } %67, 1
  br i1 %_215.1, label %panic27, label %bb131

bb130:                                            ; preds = %bb127
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_226, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_226)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_229, ptr align 8 @alloc_eeaaa0fbe8f7fa656378185f1b88656f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_229)
  store i32 1, ptr %guess, align 4
  br label %bb139

bb139:                                            ; preds = %bb144, %bb130
  %_233 = load i32, ptr %guess, align 4
  %_232 = icmp ne i32 %_233, 7
  br i1 %_232, label %bb140, label %bb145

bb145:                                            ; preds = %bb139
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_248, ptr align 4 %guess)
  %68 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_247, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %68, ptr align 8 %_248, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_244, ptr align 8 @alloc_e6a643577c5a7fc17b4d668be1720af4, ptr align 8 %_247)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_244)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_251, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_251)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_254, ptr align 8 @alloc_309fdb2dffcc123ef582b2c35b476cb7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_254)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_257, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_256, ptr align 4 %_257)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter8, ptr align 4 %_256, i64 12, i1 false)
  br label %bb155

bb140:                                            ; preds = %bb139
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_240, ptr align 4 %guess)
  %69 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_239, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %69, ptr align 8 %_240, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_236, ptr align 8 @alloc_53fc6a9fa23c657a081886c302e8c85b, ptr align 8 %_239)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_236)
  %70 = load i32, ptr %guess, align 4
  %71 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %70, i32 1)
  %_242.0 = extractvalue { i32, i1 } %71, 0
  %_242.1 = extractvalue { i32, i1 } %71, 1
  br i1 %_242.1, label %panic26, label %bb144

bb155:                                            ; preds = %bb164, %bb145
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %72 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter8)
  %73 = extractvalue { i32, i32 } %72, 0
  %74 = extractvalue { i32, i32 } %72, 1
  store i32 %73, ptr %_259, align 4
  %75 = getelementptr inbounds i8, ptr %_259, i64 4
  store i32 %74, ptr %75, align 4
  %76 = load i32, ptr %_259, align 4
  %77 = getelementptr inbounds i8, ptr %_259, i64 4
  %78 = load i32, ptr %77, align 4
  %_261 = zext i32 %76 to i64
  %79 = trunc nuw i64 %_261 to i1
  br i1 %79, label %bb157, label %bb158

bb157:                                            ; preds = %bb155
  %80 = getelementptr inbounds i8, ptr %_259, i64 4
  %81 = load i32, ptr %80, align 4
  store i32 %81, ptr %i9, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_268, ptr align 4 %i9)
  %82 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_267, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %82, ptr align 8 %_268, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_264, ptr align 8 @alloc_ff764d85c05e8fa0e78b421c9beddd21, ptr align 8 %_267)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_264)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_271, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_270, ptr align 4 %_271)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter10, ptr align 4 %_270, i64 12, i1 false)
  br label %bb164

bb158:                                            ; preds = %bb155
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_295, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_295)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_298, ptr align 8 @alloc_82dc11428f9c4f9b0d1ec1ba23d86ace)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_298)
  store i32 1, ptr %search, align 4
  store i32 0, ptr %found_at, align 4
  br label %bb178

bb178:                                            ; preds = %bb187, %bb158
  %_303 = load i32, ptr %search, align 4
  %_302 = icmp sle i32 %_303, 10
  br i1 %_302, label %bb179, label %bb188

bb188:                                            ; preds = %bb180, %bb178
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_322, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_322)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_325, ptr align 8 @alloc_33d0ecfa5a33cfb6212a5725dea1b18a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_325)
  store i32 1, ptr %factorial, align 4
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_329, i32 1, i32 5)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_328, ptr align 4 %_329)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter11, ptr align 4 %_328, i64 12, i1 false)
  br label %bb195

bb179:                                            ; preds = %bb178
  %_304 = load i32, ptr %search, align 4
  %83 = icmp eq i32 %_304, 6
  br i1 %83, label %bb180, label %bb183

bb180:                                            ; preds = %bb179
  %_305 = load i32, ptr %search, align 4
  store i32 %_305, ptr %found_at, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_311, ptr align 4 %found_at)
  %84 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_310, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %84, ptr align 8 %_311, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_307, ptr align 8 @alloc_555e600324efd390c30e2d72a0a9e702, ptr align 8 %_310)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_307)
  br label %bb188

bb183:                                            ; preds = %bb179
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_318, ptr align 4 %search)
  %85 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_317, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %85, ptr align 8 %_318, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_314, ptr align 8 @alloc_1396c82186ed518c11ba45476efd85ea, ptr align 8 %_317)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_314)
  %86 = load i32, ptr %search, align 4
  %87 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %86, i32 1)
  %_320.0 = extractvalue { i32, i1 } %87, 0
  %_320.1 = extractvalue { i32, i1 } %87, 1
  br i1 %_320.1, label %panic25, label %bb187

bb195:                                            ; preds = %bb199, %bb188
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %88 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter11)
  %89 = extractvalue { i32, i32 } %88, 0
  %90 = extractvalue { i32, i32 } %88, 1
  store i32 %89, ptr %_331, align 4
  %91 = getelementptr inbounds i8, ptr %_331, i64 4
  store i32 %90, ptr %91, align 4
  %92 = load i32, ptr %_331, align 4
  %93 = getelementptr inbounds i8, ptr %_331, i64 4
  %94 = load i32, ptr %93, align 4
  %_333 = zext i32 %92 to i64
  %95 = trunc nuw i64 %_333 to i1
  br i1 %95, label %bb197, label %bb198

bb197:                                            ; preds = %bb195
  %96 = getelementptr inbounds i8, ptr %_331, i64 4
  %97 = load i32, ptr %96, align 4
  store i32 %97, ptr %i12, align 4
  %98 = load i32, ptr %factorial, align 4
  %99 = load i32, ptr %i12, align 4
  %100 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %98, i32 %99)
  %_335.0 = extractvalue { i32, i1 } %100, 0
  %_335.1 = extractvalue { i32, i1 } %100, 1
  br i1 %_335.1, label %panic24, label %bb199

bb198:                                            ; preds = %bb195
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_350, ptr align 4 %factorial)
  %101 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_349, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %101, ptr align 8 %_350, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_346, ptr align 8 @alloc_71fa40ebab23c1c6653dacffb3f013e9, ptr align 8 %_349)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_346)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_353, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_353)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_356, ptr align 8 @alloc_3448d66ec8d3b571366ffcbfc5278139)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_356)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_359, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_358, ptr align 4 %_359)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter13, ptr align 4 %_358, i64 12, i1 false)
  br label %bb212

bb212:                                            ; preds = %bb221, %bb198
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %102 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter13)
  %103 = extractvalue { i32, i32 } %102, 0
  %104 = extractvalue { i32, i32 } %102, 1
  store i32 %103, ptr %_361, align 4
  %105 = getelementptr inbounds i8, ptr %_361, i64 4
  store i32 %104, ptr %105, align 4
  %106 = load i32, ptr %_361, align 4
  %107 = getelementptr inbounds i8, ptr %_361, i64 4
  %108 = load i32, ptr %107, align 4
  %_363 = zext i32 %106 to i64
  %109 = trunc nuw i64 %_363 to i1
  br i1 %109, label %bb214, label %bb215

bb214:                                            ; preds = %bb212
  %110 = getelementptr inbounds i8, ptr %_361, i64 4
  %i23 = load i32, ptr %110, align 4
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_366, i32 1, i32 %i23)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_365, ptr align 4 %_366)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter14, ptr align 4 %_365, i64 12, i1 false)
  br label %bb218

bb215:                                            ; preds = %bb212
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_379, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_379)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_382, ptr align 8 @alloc_26ac04c73b91fa604361572276cbceb9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_382)
  store i32 1, ptr %a, align 4
  store i32 10, ptr %b, align 4
  br label %bb228

bb228:                                            ; preds = %bb237, %bb215
  %_387 = load i32, ptr %a, align 4
  %_388 = load i32, ptr %b, align 4
  %_386 = icmp slt i32 %_387, %_388
  br i1 %_386, label %bb229, label %bb238

bb238:                                            ; preds = %bb228
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_411, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_413, ptr align 4 %b)
  %111 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_410, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %111, ptr align 8 %_411, i64 16, i1 false)
  %112 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_410, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %112, ptr align 8 %_413, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_407, ptr align 8 @alloc_3f78c67e50371bc27a576567dba254dd, ptr align 8 %_410)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_407)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_416, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_416)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_419, ptr align 8 @alloc_356194ee78976e0164244f86aaf7c5a4)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_419)
  store ptr @alloc_3edef0b68cfa9c8c95e6d4fe1a68842b, ptr %base, align 8
  %113 = getelementptr inbounds i8, ptr %base, i64 8
  store i64 5, ptr %113, align 8
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_423, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0a2f17cfc5e5ad3dE"(ptr sret([12 x i8]) align 4 %_422, ptr align 4 %_423)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter15, ptr align 4 %_422, i64 12, i1 false)
  br label %bb249

bb229:                                            ; preds = %bb228
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_394, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_396, ptr align 4 %b)
  %_401 = load i32, ptr %b, align 4
  %_402 = load i32, ptr %a, align 4
  %114 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_401, i32 %_402)
  %_403.0 = extractvalue { i32, i1 } %114, 0
  %_403.1 = extractvalue { i32, i1 } %114, 1
  br i1 %_403.1, label %panic, label %bb232

bb249:                                            ; preds = %bb260, %bb238
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %115 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter15)
  %116 = extractvalue { i32, i32 } %115, 0
  %117 = extractvalue { i32, i32 } %115, 1
  store i32 %116, ptr %_425, align 4
  %118 = getelementptr inbounds i8, ptr %_425, i64 4
  store i32 %117, ptr %118, align 4
  %119 = load i32, ptr %_425, align 4
  %120 = getelementptr inbounds i8, ptr %_425, i64 4
  %121 = load i32, ptr %120, align 4
  %_427 = zext i32 %119 to i64
  %122 = trunc nuw i64 %_427 to i1
  br i1 %122, label %bb251, label %bb252

bb251:                                            ; preds = %bb249
  %123 = getelementptr inbounds i8, ptr %_425, i64 4
  %124 = load i32, ptr %123, align 4
  store i32 %124, ptr %i16, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h0dc9b63ad3e13ac4E(ptr sret([16 x i8]) align 8 %_435, ptr align 8 %base)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_437, ptr align 4 %i16)
  %125 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_434, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %125, ptr align 8 %_435, i64 16, i1 false)
  %126 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_434, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %126, ptr align 8 %_437, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h2470d9f342650c59E(ptr sret([48 x i8]) align 8 %_431, ptr align 8 @alloc_a0434d70b15bcc9ff1a7b717be16ed72, ptr align 8 %_434)
; call alloc::fmt::format
  call void @_ZN5alloc3fmt6format17h96ae74123e798c1bE(ptr sret([24 x i8]) align 8 %res, ptr align 8 %_431)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %repeated, ptr align 8 %res, i64 24, i1 false)
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h94f78491c604fe02E(ptr sret([16 x i8]) align 8 %_444, ptr align 8 %repeated)
          to label %bb258 unwind label %funclet_bb289

bb252:                                            ; preds = %bb249
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_447, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_447)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_450, ptr align 8 @alloc_20604c4be05940d6c2566401e2c9f83d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_450)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h71ed88882a2cee66E"(ptr sret([12 x i8]) align 4 %_453, i32 1, i32 10)
; call core::iter::traits::iterator::Iterator::collect
  call void @_ZN4core4iter6traits8iterator8Iterator7collect17hc088abf1e5f7d622E(ptr sret([24 x i8]) align 8 %numbers, ptr align 4 %_453)
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %127 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h719c16e8d09d5d14E"(ptr align 8 %numbers)
          to label %bb267 unwind label %funclet_bb288

bb288:                                            ; preds = %funclet_bb288
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %numbers) #18 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind label %funclet_bb290

funclet_bb288:                                    ; preds = %bb287, %bb284, %bb276, %bb275, %bb274, %bb273, %bb272, %bb271, %bb270, %bb269, %bb268, %bb267, %bb252
  %cleanuppad = cleanuppad within none []
  br label %bb288

bb267:                                            ; preds = %bb252
  %_456.0 = extractvalue { ptr, i64 } %127, 0
  %_456.1 = extractvalue { ptr, i64 } %127, 1
; invoke core::slice::<impl [T]>::iter
  %128 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hc6a508531916984bE"(ptr align 4 %_456.0, i64 %_456.1)
          to label %bb268 unwind label %funclet_bb288

bb268:                                            ; preds = %bb267
  %_455.0 = extractvalue { ptr, ptr } %128, 0
  %_455.1 = extractvalue { ptr, ptr } %128, 1
; invoke core::iter::traits::iterator::Iterator::sum
  %129 = invoke i32 @_ZN4core4iter6traits8iterator8Iterator3sum17h84ea3891411f6273E(ptr %_455.0, ptr %_455.1)
          to label %bb269 unwind label %funclet_bb288

bb269:                                            ; preds = %bb268
  store i32 %129, ptr %sum17, align 4
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_463, ptr align 4 %sum17)
          to label %bb270 unwind label %funclet_bb288

bb270:                                            ; preds = %bb269
  %130 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_462, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %130, ptr align 8 %_463, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_459, ptr align 8 @alloc_0eb58bfbd806c8d45695a5721ce4b7d2, ptr align 8 %_462)
          to label %bb271 unwind label %funclet_bb288

bb271:                                            ; preds = %bb270
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_459)
          to label %bb272 unwind label %funclet_bb288

bb272:                                            ; preds = %bb271
; invoke <alloc::vec::Vec<T,A> as core::ops::deref::Deref>::deref
  %131 = invoke { ptr, i64 } @"_ZN72_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..deref..Deref$GT$5deref17h719c16e8d09d5d14E"(ptr align 8 %numbers)
          to label %bb273 unwind label %funclet_bb288

bb273:                                            ; preds = %bb272
  %_469.0 = extractvalue { ptr, i64 } %131, 0
  %_469.1 = extractvalue { ptr, i64 } %131, 1
; invoke core::slice::<impl [T]>::iter
  %132 = invoke { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17hc6a508531916984bE"(ptr align 4 %_469.0, i64 %_469.1)
          to label %bb274 unwind label %funclet_bb288

bb274:                                            ; preds = %bb273
  %_468.0 = extractvalue { ptr, ptr } %132, 0
  %_468.1 = extractvalue { ptr, ptr } %132, 1
; invoke core::iter::traits::iterator::Iterator::filter
  %133 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6filter17hbb76ff1c87dde395E(ptr %_468.0, ptr %_468.1)
          to label %bb275 unwind label %funclet_bb288

bb275:                                            ; preds = %bb274
  %_467.0 = extractvalue { ptr, ptr } %133, 0
  %_467.1 = extractvalue { ptr, ptr } %133, 1
; invoke core::iter::traits::iterator::Iterator::cloned
  %134 = invoke { ptr, ptr } @_ZN4core4iter6traits8iterator8Iterator6cloned17he72d9b0779c16f69E(ptr %_467.0, ptr %_467.1)
          to label %bb276 unwind label %funclet_bb288

bb276:                                            ; preds = %bb275
  %_466.0 = extractvalue { ptr, ptr } %134, 0
  %_466.1 = extractvalue { ptr, ptr } %134, 1
; invoke core::iter::traits::iterator::Iterator::collect
  invoke void @_ZN4core4iter6traits8iterator8Iterator7collect17h80346af0c5fb6b58E(ptr sret([24 x i8]) align 8 %evens, ptr %_466.0, ptr %_466.1)
          to label %bb277 unwind label %funclet_bb288

bb277:                                            ; preds = %bb276
; invoke core::fmt::rt::Argument::new_debug
  invoke void @_ZN4core3fmt2rt8Argument9new_debug17h95088346cbcb6dabE(ptr sret([16 x i8]) align 8 %_476, ptr align 8 %evens)
          to label %bb278 unwind label %funclet_bb287

bb287:                                            ; preds = %funclet_bb287
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %evens) #18 [ "funclet"(token %cleanuppad18) ]
  cleanupret from %cleanuppad18 unwind label %funclet_bb288

funclet_bb287:                                    ; preds = %bb283, %bb282, %bb281, %bb280, %bb279, %bb278, %bb277
  %cleanuppad18 = cleanuppad within none []
  br label %bb287

bb278:                                            ; preds = %bb277
  %135 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_475, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %135, ptr align 8 %_476, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_472, ptr align 8 @alloc_3dca2e8af32c5067fd0f53ddd35ea51d, ptr align 8 %_475)
          to label %bb279 unwind label %funclet_bb287

bb279:                                            ; preds = %bb278
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_472)
          to label %bb280 unwind label %funclet_bb287

bb280:                                            ; preds = %bb279
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_479, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
          to label %bb281 unwind label %funclet_bb287

bb281:                                            ; preds = %bb280
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_479)
          to label %bb282 unwind label %funclet_bb287

bb282:                                            ; preds = %bb281
; invoke core::fmt::Arguments::new_const
  invoke void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_482, ptr align 8 @alloc_b80cbda912480b37d5696acf4f3509e8)
          to label %bb283 unwind label %funclet_bb287

bb283:                                            ; preds = %bb282
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_482)
          to label %bb284 unwind label %funclet_bb287

bb284:                                            ; preds = %bb283
; invoke core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  invoke void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %evens)
          to label %bb285 unwind label %funclet_bb288

bb285:                                            ; preds = %bb284
; call core::ptr::drop_in_place<alloc::vec::Vec<i32>>
  call void @"_ZN4core3ptr47drop_in_place$LT$alloc..vec..Vec$LT$i32$GT$$GT$17hd03d7adc597ac0abE"(ptr align 8 %numbers)
  ret void

bb290:                                            ; preds = %funclet_bb290
  cleanupret from %cleanuppad19 unwind to caller

funclet_bb290:                                    ; preds = %bb289, %bb288
  %cleanuppad19 = cleanuppad within none []
  br label %bb290

bb289:                                            ; preds = %funclet_bb289
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h4a36589654e632c7E"(ptr align 8 %repeated) #18 [ "funclet"(token %cleanuppad20) ]
  cleanupret from %cleanuppad20 unwind label %funclet_bb290

funclet_bb289:                                    ; preds = %bb259, %bb258, %bb251
  %cleanuppad20 = cleanuppad within none []
  br label %bb289

bb258:                                            ; preds = %bb251
  %136 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_443, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %136, ptr align 8 %_444, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_440, ptr align 8 @alloc_9771be2481f51be410bd2ac520d18601, ptr align 8 %_443)
          to label %bb259 unwind label %funclet_bb289

bb259:                                            ; preds = %bb258
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_440)
          to label %bb260 unwind label %funclet_bb289

bb260:                                            ; preds = %bb259
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h4a36589654e632c7E"(ptr align 8 %repeated)
  br label %bb249

bb232:                                            ; preds = %bb229
  store i32 %_403.0, ptr %_400, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_398, ptr align 4 %_400)
  %137 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_393, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %137, ptr align 8 %_394, i64 16, i1 false)
  %138 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_393, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %138, ptr align 8 %_396, i64 16, i1 false)
  %139 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_393, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %139, ptr align 8 %_398, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3b8ecced06f7ffe2E(ptr sret([48 x i8]) align 8 %_390, ptr align 8 @alloc_fe98b5f9324d6badf224de6d68fc2a26, ptr align 8 %_393)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_390)
  %140 = load i32, ptr %a, align 4
  %141 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %140, i32 1)
  %_404.0 = extractvalue { i32, i1 } %141, 0
  %_404.1 = extractvalue { i32, i1 } %141, 1
  br i1 %_404.1, label %panic21, label %bb236

panic:                                            ; preds = %bb229
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_34705104e3cf75e7772685e41ac6f1f1) #19
  unreachable

bb236:                                            ; preds = %bb232
  store i32 %_404.0, ptr %a, align 4
  %142 = load i32, ptr %b, align 4
  %143 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %142, i32 1)
  %_405.0 = extractvalue { i32, i1 } %143, 0
  %_405.1 = extractvalue { i32, i1 } %143, 1
  br i1 %_405.1, label %panic22, label %bb237

panic21:                                          ; preds = %bb232
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f559d2581e904f6e48b8f1c2bf2b18ab) #19
  unreachable

bb237:                                            ; preds = %bb236
  store i32 %_405.0, ptr %b, align 4
  br label %bb228

panic22:                                          ; preds = %bb236
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_7bf6baa429897ec84b4126bfe3868d25) #19
  unreachable

bb218:                                            ; preds = %bb220, %bb214
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %144 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter14)
  %145 = extractvalue { i32, i32 } %144, 0
  %146 = extractvalue { i32, i32 } %144, 1
  store i32 %145, ptr %_368, align 4
  %147 = getelementptr inbounds i8, ptr %_368, i64 4
  store i32 %146, ptr %147, align 4
  %148 = load i32, ptr %_368, align 4
  %149 = getelementptr inbounds i8, ptr %_368, i64 4
  %150 = load i32, ptr %149, align 4
  %_370 = zext i32 %148 to i64
  %151 = trunc nuw i64 %_370 to i1
  br i1 %151, label %bb220, label %bb221

bb220:                                            ; preds = %bb218
  %152 = getelementptr inbounds i8, ptr %_368, i64 4
  %_j = load i32, ptr %152, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_373, ptr align 8 @alloc_56173ae41eedeb97a9658f94134753cd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_373)
  br label %bb218

bb221:                                            ; preds = %bb218
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_376, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_376)
  br label %bb212

bb199:                                            ; preds = %bb197
  store i32 %_335.0, ptr %factorial, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_341, ptr align 4 %i12)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_343, ptr align 4 %factorial)
  %153 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %153, ptr align 8 %_341, i64 16, i1 false)
  %154 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_340, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %154, ptr align 8 %_343, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_337, ptr align 8 @alloc_a916c854665ddb9c4d8c9c3cd1554989, ptr align 8 %_340)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_337)
  br label %bb195

panic24:                                          ; preds = %bb197
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_30edf13ca54f324c7427a6208ccfbf30) #19
  unreachable

bb187:                                            ; preds = %bb183
  store i32 %_320.0, ptr %search, align 4
  br label %bb178

panic25:                                          ; preds = %bb183
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_0c8119fda4534d9098b9bd5c51d37762) #19
  unreachable

bb164:                                            ; preds = %bb167, %bb170, %bb157
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %155 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter10)
  %156 = extractvalue { i32, i32 } %155, 0
  %157 = extractvalue { i32, i32 } %155, 1
  store i32 %156, ptr %_273, align 4
  %158 = getelementptr inbounds i8, ptr %_273, i64 4
  store i32 %157, ptr %158, align 4
  %159 = load i32, ptr %_273, align 4
  %160 = getelementptr inbounds i8, ptr %_273, i64 4
  %161 = load i32, ptr %160, align 4
  %_275 = zext i32 %159 to i64
  %162 = trunc nuw i64 %_275 to i1
  br i1 %162, label %bb166, label %bb155

bb166:                                            ; preds = %bb164
  %163 = getelementptr inbounds i8, ptr %_273, i64 4
  %164 = load i32, ptr %163, align 4
  store i32 %164, ptr %j, align 4
  %165 = load i32, ptr %i9, align 4
  %166 = load i32, ptr %j, align 4
  %_277 = icmp eq i32 %165, %166
  br i1 %_277, label %bb167, label %bb170

bb170:                                            ; preds = %bb166
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_290, ptr align 4 %i9)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_292, ptr align 4 %j)
  %167 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_289, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %167, ptr align 8 %_290, i64 16, i1 false)
  %168 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_289, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %168, ptr align 8 %_292, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_286, ptr align 8 @alloc_1abde274a31b2a907398fe0dc7a7643f, ptr align 8 %_289)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_286)
  br label %bb164

bb167:                                            ; preds = %bb166
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_283, ptr align 4 %i9)
  %169 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_282, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %169, ptr align 8 %_283, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hdc6c00120a48283bE(ptr sret([48 x i8]) align 8 %_279, ptr align 8 @alloc_e5b8a53e9f757f2b8fafb9122aa0467e, ptr align 8 %_282)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_279)
  br label %bb164

bb144:                                            ; preds = %bb140
  store i32 %_242.0, ptr %guess, align 4
  br label %bb139

panic26:                                          ; preds = %bb140
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_24887bce5ff1e7fdc2fc2f44a27180bc) #19
  unreachable

bb131:                                            ; preds = %bb129
  store i32 %_215.0, ptr %square, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_221, ptr align 4 %i7)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_223, ptr align 4 %square)
  %170 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_220, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %170, ptr align 8 %_221, i64 16, i1 false)
  %171 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_220, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %171, ptr align 8 %_223, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_217, ptr align 8 @alloc_0f2a4eb68444eb0bab98623b123a3365, ptr align 8 %_220)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_217)
  br label %bb127

panic27:                                          ; preds = %bb129
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_83a8188b77a5e612a8a06f7ee687af46) #19
  unreachable

bb111:                                            ; preds = %bb110
  store i32 %_183.0, ptr %sum, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_189, ptr align 4 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_191, ptr align 4 %sum)
  %172 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_188, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %172, ptr align 8 %_189, i64 16, i1 false)
  %173 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_188, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %173, ptr align 8 %_191, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_185, ptr align 8 @alloc_110e04e039bd3134eb101a41472378a9, ptr align 8 %_188)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_185)
  %174 = load i32, ptr %x, align 4
  %175 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %174, i32 1)
  %_193.0 = extractvalue { i32, i1 } %175, 0
  %_193.1 = extractvalue { i32, i1 } %175, 1
  br i1 %_193.1, label %panic29, label %bb116

panic28:                                          ; preds = %bb110
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_d2ddd6360ce42f02d4bf15160e82afd3) #19
  unreachable

bb116:                                            ; preds = %bb111
  store i32 %_193.0, ptr %x, align 4
  br label %bb108

panic29:                                          ; preds = %bb111
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_6b6f0d6ba334f3a47613d8f7c41013c6) #19
  unreachable

bb96:                                             ; preds = %bb98, %bb92
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %176 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h9760e3524728e9dcE"(ptr align 4 %iter5)
  %177 = extractvalue { i32, i32 } %176, 0
  %178 = extractvalue { i32, i32 } %176, 1
  store i32 %177, ptr %_154, align 4
  %179 = getelementptr inbounds i8, ptr %_154, i64 4
  store i32 %178, ptr %179, align 4
  %180 = load i32, ptr %_154, align 4
  %181 = getelementptr inbounds i8, ptr %_154, i64 4
  %182 = load i32, ptr %181, align 4
  %_156 = zext i32 %180 to i64
  %183 = trunc nuw i64 %_156 to i1
  br i1 %183, label %bb98, label %bb99

bb98:                                             ; preds = %bb96
  %184 = getelementptr inbounds i8, ptr %_154, i64 4
  %185 = load i32, ptr %184, align 4
  store i32 %185, ptr %col, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_163, ptr align 4 %row)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_165, ptr align 4 %col)
  %186 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_162, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %186, ptr align 8 %_163, i64 16, i1 false)
  %187 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_162, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %187, ptr align 8 %_165, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h61e31c0e980ff0f0E(ptr sret([48 x i8]) align 8 %_159, ptr align 8 @alloc_66e3d1a9098f9c73b7441dfc8f07b36d, ptr align 8 %_162)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_159)
  br label %bb96

bb99:                                             ; preds = %bb96
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_168, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_168)
  br label %bb90

bb73:                                             ; preds = %bb81, %bb69
  %_120 = load i32, ptr %inner, align 4
  %_119 = icmp sle i32 %_120, 5
  br i1 %_119, label %bb74, label %bb82

bb82:                                             ; preds = %bb73
  %188 = load i32, ptr %outer, align 4
  %189 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %188, i32 1)
  %_137.0 = extractvalue { i32, i1 } %189, 0
  %_137.1 = extractvalue { i32, i1 } %189, 1
  br i1 %_137.1, label %panic30, label %bb83

bb74:                                             ; preds = %bb73
  %_122 = load i32, ptr %outer, align 4
  %_123 = load i32, ptr %inner, align 4
  %190 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_122, i32 %_123)
  %_124.0 = extractvalue { i32, i1 } %190, 0
  %_124.1 = extractvalue { i32, i1 } %190, 1
  br i1 %_124.1, label %panic31, label %bb75

bb83:                                             ; preds = %bb82
  store i32 %_137.0, ptr %outer, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h8c526fac01ec4035E(ptr sret([48 x i8]) align 8 %_139, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_139)
  br label %bb68

panic30:                                          ; preds = %bb82
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_678427feec378691a572e2c78065244b) #19
  unreachable

bb75:                                             ; preds = %bb74
  store i32 %_124.0, ptr %result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_130, ptr align 4 %outer)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_132, ptr align 4 %inner)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h22cfcbad34abef53E(ptr sret([16 x i8]) align 8 %_134, ptr align 4 %result)
  %191 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_129, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %191, ptr align 8 %_130, i64 16, i1 false)
  %192 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_129, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %192, ptr align 8 %_132, i64 16, i1 false)
  %193 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_129, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %193, ptr align 8 %_134, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h3b8ecced06f7ffe2E(ptr sret([48 x i8]) align 8 %_126, ptr align 8 @alloc_7577c04eb2bd819bb55f3535d5d42496, ptr align 8 %_129)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_126)
  %194 = load i32, ptr %inner, align 4
  %195 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %194, i32 1)
  %_136.0 = extractvalue { i32, i1 } %195, 0
  %_136.1 = extractvalue { i32, i1 } %195, 1
  br i1 %_136.1, label %panic32, label %bb81

panic31:                                          ; preds = %bb74
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_828235f0e43732bae15495b87d3009f1) #19
  unreachable

bb81:                                             ; preds = %bb75
  store i32 %_136.0, ptr %inner, align 4
  br label %bb73

panic32:                                          ; preds = %bb75
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_be54450a55e2af8b7732e9c07bbf66af) #19
  unreachable

bb34:                                             ; No predecessors!
  unreachable

bb23:                                             ; preds = %bb19
  store i32 %_37.0, ptr %countdown, align 4
  br label %bb18

panic33:                                          ; preds = %bb19
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_38f405957f5b34ca827ae9c5cc4492fc) #19
  unreachable

bb12:                                             ; preds = %bb8
  store i32 %_20.0, ptr %counter, align 4
  br label %bb7

panic34:                                          ; preds = %bb8
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_309940334c56c09cb5e621e20eec2456) #19
  unreachable
}

; _05_loops::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN9_05_loops4main28_$u7b$$u7b$closure$u7d$$u7d$17hd87cced8fa2f6e0fE"(ptr align 1 %_1, ptr align 8 %_2) unnamed_addr #1 {
start:
  %x = load ptr, ptr %_2, align 8
; call <&i32 as core::ops::arith::Rem<i32>>::rem
  %_4 = call i32 @"_ZN60_$LT$$RF$i32$u20$as$u20$core..ops..arith..Rem$LT$i32$GT$$GT$3rem17h8007297518854509E"(ptr align 4 %x, i32 2, ptr align 8 @alloc_c43cc71148b1ec2ec05bff68521a8cdd)
  %_0 = icmp eq i32 %_4, 0
  ret i1 %_0
}

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #7

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.uadd.sat.i64(i64, i64) #9

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #0

; core::panicking::panic_const::panic_const_rem_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_rem_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_rem_overflow17h03da7aacd7efb4e3E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; core::fmt::Formatter::debug_list
; Function Attrs: uwtable
declare void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::finish
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #9

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #10

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #11

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i8 @llvm.scmp.i8.i32(i32, i32) #9

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::UpperHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17he333664202a3dc12E"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::num::<impl core::fmt::LowerHex for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17h4cb8b0b38b8081bbE"(ptr align 4, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::entry
; Function Attrs: uwtable
declare align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8, ptr align 1, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #9

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #0

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; alloc::fmt::format::format_inner
; Function Attrs: uwtable
declare void @_ZN5alloc3fmt6format12format_inner17hd01fa95a68d3feb6E(ptr sret([24 x i8]) align 8, ptr align 8) unnamed_addr #0

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #12

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #13

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #14

; __rustc::__rust_realloc
; Function Attrs: nounwind allockind("realloc,aligned") allocsize(3) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc14___rust_realloc(ptr allocptr, i64, i64 allocalign, i64) unnamed_addr #15

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: write)
declare void @llvm.memset.p0.i64(ptr nocapture writeonly, i8, i64, i1 immarg) #16

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #17

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #9

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17ha772066263c9998aE(ptr @_ZN9_05_loops4main17haf3f005522340343E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { cold uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #10 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #11 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind allockind("realloc,aligned") allocsize(3) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #16 = { nocallback nofree nounwind willreturn memory(argmem: write) }
attributes #17 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #18 = { cold }
attributes #19 = { noreturn }
attributes #20 = { nounwind }
attributes #21 = { noreturn nounwind }
attributes #22 = { cold noreturn nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 6123141601804500}
