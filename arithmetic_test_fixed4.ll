; Generated by <PERSON><PERSON> to LLVM IR Converter - Enhanced Version
; Source: <PERSON>let code with 88 statements
target triple = "x86_64-pc-windows-msvc"

; Function declarations
declare i32 @printf(i8*, ...)
declare i8* @fopen(i8*, i8*)
declare i32 @fprintf(i8*, i8*, ...)
declare i32 @fclose(i8*)
declare i32 @system(i8*)
declare i8* @malloc(i64)
declare void @free(i8*)
declare i8* @strcpy(i8*, i8*)
declare i8* @strcat(i8*, i8*)
declare i64 @strlen(i8*)
declare i8* @strstr(i8*, i8*)
declare i32 @strcmp(i8*, i8*)
declare void @llvm.memcpy.p0i8.p0i8.i64(i8*, i8*, i64, i1)
declare i32 @fseek(i8*, i64, i32)
declare i64 @ftell(i8*)
declare i64 @fread(i8*, i64, i64, i8*)
declare i64 @fwrite(i8*, i64, i64, i8*)
declare i32 @remove(i8*)
declare i32 @access(i8*, i32)
declare void @rewind(i8*)

; String constants
@.str_expr_0 = private unnamed_addr constant [5 x i8] c"a = \00", align 1
@.str_expr_1 = private unnamed_addr constant [7 x i8] c", b = \00", align 1
@.str_expr_2 = private unnamed_addr constant [11 x i8] c"Addition: \00", align 1
@.str_expr_3 = private unnamed_addr constant [4 x i8] c" + \00", align 1
@.str_expr_4 = private unnamed_addr constant [4 x i8] c" = \00", align 1
@.str_expr_5 = private unnamed_addr constant [14 x i8] c"Subtraction: \00", align 1
@.str_expr_6 = private unnamed_addr constant [4 x i8] c" - \00", align 1
@.str_expr_7 = private unnamed_addr constant [17 x i8] c"Multiplication: \00", align 1
@.str_expr_8 = private unnamed_addr constant [4 x i8] c" * \00", align 1
@.str_expr_9 = private unnamed_addr constant [11 x i8] c"Division: \00", align 1
@.str_expr_10 = private unnamed_addr constant [4 x i8] c" / \00", align 1
@.str_expr_11 = private unnamed_addr constant [9 x i8] c"Modulo: \00", align 1
@.str_expr_12 = private unnamed_addr constant [4 x i8] c" % \00", align 1
@.str_expr_13 = private unnamed_addr constant [5 x i8] c"x = \00", align 1
@.str_expr_14 = private unnamed_addr constant [7 x i8] c", y = \00", align 1
@.str_expr_15 = private unnamed_addr constant [15 x i8] c"(a + b) * 2 = \00", align 1
@.str_expr_16 = private unnamed_addr constant [15 x i8] c"a * (b + 5) = \00", align 1
@.str_expr_17 = private unnamed_addr constant [21 x i8] c"(a * 2) + (b * 3) = \00", align 1
@.str_expr_18 = private unnamed_addr constant [27 x i8] c"((a + b) * 2) - (a / b) = \00", align 1
@.str_expr_19 = private unnamed_addr constant [27 x i8] c"(a * b) + ((a - b) * 2) = \00", align 1
@.str_expr_20 = private unnamed_addr constant [27 x i8] c"((a + 5) * (b - 1)) / 2 = \00", align 1
@.str_expr_21 = private unnamed_addr constant [8 x i8] c"neg1 = \00", align 1
@.str_expr_22 = private unnamed_addr constant [10 x i8] c", neg2 = \00", align 1
@.str_expr_23 = private unnamed_addr constant [15 x i8] c"(-5) + (-3) = \00", align 1
@.str_expr_24 = private unnamed_addr constant [15 x i8] c"(-5) * (-3) = \00", align 1
@.str_expr_25 = private unnamed_addr constant [15 x i8] c"(-5) - (-3) = \00", align 1
@.str_expr_26 = private unnamed_addr constant [19 x i8] c"Initial counter = \00", align 1
@.str_expr_27 = private unnamed_addr constant [18 x i8] c"After increment: \00", align 1
@.str_expr_28 = private unnamed_addr constant [17 x i8] c"After doubling: \00", align 1
@.str_expr_29 = private unnamed_addr constant [22 x i8] c"After subtracting 5: \00", align 1
@.str_expr_30 = private unnamed_addr constant [21 x i8] c"1 + 2 + 3 + 4 + 5 = \00", align 1
@.str_expr_31 = private unnamed_addr constant [13 x i8] c"2 * 3 * 4 = \00", align 1
@.str_expr_32 = private unnamed_addr constant [8 x i8] c"base = \00", align 1
@.str_expr_33 = private unnamed_addr constant [7 x i8] c"2^2 = \00", align 1
@.str_expr_34 = private unnamed_addr constant [7 x i8] c"2^3 = \00", align 1
@.str_expr_35 = private unnamed_addr constant [7 x i8] c"2^4 = \00", align 1
@.str_expr_36 = private unnamed_addr constant [11 x i8] c"int_val = \00", align 1
@.str_expr_37 = private unnamed_addr constant [15 x i8] c", float_val = \00", align 1
@.str_expr_38 = private unnamed_addr constant [13 x i8] c"2 + 3 * 4 = \00", align 1
@.str_expr_39 = private unnamed_addr constant [15 x i8] c"(2 + 3) * 4 = \00", align 1
@.str_expr_40 = private unnamed_addr constant [13 x i8] c"2 * 3 + 4 = \00", align 1
@.str_expr_41 = private unnamed_addr constant [15 x i8] c"2 * (3 + 4) = \00", align 1
@.str_int = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@.str_int_fmt = private unnamed_addr constant [3 x i8] c"%d\00", align 1
@.str_fmt = private unnamed_addr constant [3 x i8] c"%s\00", align 1
@.str_float = private unnamed_addr constant [5 x i8] c"%.3f\00", align 1
@.str_char = private unnamed_addr constant [3 x i8] c"%c\00", align 1
@.str_true = private unnamed_addr constant [5 x i8] c"true\00", align 1
@.str_false = private unnamed_addr constant [6 x i8] c"false\00", align 1
@.str_newline = private unnamed_addr constant [2 x i8] c"\0A\00", align 1
@write_mode = private unnamed_addr constant [2 x i8] c"w\00", align 1
@read_mode = private unnamed_addr constant [2 x i8] c"r\00", align 1
@empty_string = private unnamed_addr constant [1 x i8] c"\00", align 1

; Global variables
@global_argc = global i32 0, align 4
@global_argv = global i8** null, align 8
@global_a = global i32 0, align 4
@global_b = global i32 0, align 4
@global_x = global float 0.0, align 4
@global_y = global float 0.0, align 4
@global_result1 = global i32 0, align 4
@global_result2 = global i32 0, align 4
@global_result3 = global i32 0, align 4
@global_complex1 = global i32 0, align 4
@global_complex2 = global i32 0, align 4
@global_complex3 = global i32 0, align 4
@global_neg1 = global i32 0, align 4
@global_neg2 = global i32 0, align 4
@global_counter = global i32 0, align 4
@global_chain_result = global i32 0, align 4
@global_chain_mult = global i32 0, align 4
@global_base = global i32 0, align 4
@global_power2 = global i32 0, align 4
@global_power3 = global i32 0, align 4
@global_power4 = global i32 0, align 4
@global_int_val = global i32 0, align 4
@global_float_val = global float 0.0, align 4
@global_order1 = global i32 0, align 4
@global_order2 = global i32 0, align 4
@global_order3 = global i32 0, align 4
@global_order4 = global i32 0, align 4

; Main function
define i32 @main(i32 %argc, i8** %argv) {
entry:
  ; Initialize stack variables for arguments
  %argc_ptr = alloca i32, align 4
  %argv_ptr = alloca i8**, align 8
  store i32 %argc, i32* %argc_ptr, align 4
  store i8** %argv, i8*** %argv_ptr, align 8

  ; print "=== Arithmetic Operations Demo ===" (inline)
  %tmp_1_str = alloca [35 x i8], align 1
  store [35 x i8] c"=== Arithmetic Operations Demo ===\00", [35 x i8]* %tmp_1_str, align 1
  %tmp_2 = bitcast [35 x i8]* %tmp_1_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_3_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_3_str, align 1
  %tmp_4 = bitcast [1 x i8]* %tmp_3_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_4)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Basic Integer Arithmetic:" (inline)
  %tmp_5_str = alloca [26 x i8], align 1
  store [26 x i8] c"Basic Integer Arithmetic:\00", [26 x i8]* %tmp_5_str, align 1
  %tmp_6 = bitcast [26 x i8]* %tmp_5_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_6)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int a = 15
  store i32 15, i32* @global_a, align 4
  ; int b = 4
  store i32 4, i32* @global_b, align 4
  ; print expression: "a = " + a + ", b = " + b
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_0, i32 0, i32 0))
  %tmp_7 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_7)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_1, i32 0, i32 0))
  %tmp_8 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_8)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Addition: " + a + " + " + b + " = " + (a + b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_9 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_9)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_10 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_10)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_11 = load i32, i32* @global_a, align 4
  %tmp_12 = load i32, i32* @global_b, align 4
  %tmp_13 = add i32 %tmp_11, %tmp_12
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_13)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Subtraction: " + a + " - " + b + " = " + (a - b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_14 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_14)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_15 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_15)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_16 = load i32, i32* @global_a, align 4
  %tmp_17 = load i32, i32* @global_b, align 4
  %tmp_18 = sub i32 %tmp_16, %tmp_17
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_18)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Multiplication: " + a + " * " + b + " = " + (a * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_19 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_19)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_20 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_20)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_21 = load i32, i32* @global_a, align 4
  %tmp_22 = load i32, i32* @global_b, align 4
  %tmp_23 = mul i32 %tmp_21, %tmp_22
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_23)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Division: " + a + " / " + b + " = " + (a / b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_24 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_24)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_25 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_25)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_26 = load i32, i32* @global_a, align 4
  %tmp_27 = load i32, i32* @global_b, align 4
  %tmp_28 = sdiv i32 %tmp_26, %tmp_27
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_28)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Modulo: " + a + " % " + b + " = " + (a % b)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([9 x i8], [9 x i8]* @.str_expr_11, i32 0, i32 0))
  %tmp_29 = load i32, i32* @global_a, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_29)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_12, i32 0, i32 0))
  %tmp_30 = load i32, i32* @global_b, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_30)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_31 = load i32, i32* @global_a, align 4
  %tmp_32 = load i32, i32* @global_b, align 4
  %tmp_33 = srem i32 %tmp_31, %tmp_32
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_33)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_34_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_34_str, align 1
  %tmp_35 = bitcast [1 x i8]* %tmp_34_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_35)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Float Arithmetic:" (inline)
  %tmp_36_str = alloca [18 x i8], align 1
  store [18 x i8] c"Float Arithmetic:\00", [18 x i8]* %tmp_36_str, align 1
  %tmp_37 = bitcast [18 x i8]* %tmp_36_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_37)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; float x = 10.5
  %tmp_38 = bitcast i32 1093140480 to float
  store float %tmp_38, float* @global_x, align 4
  ; float y = 3.2
  %tmp_39 = bitcast i32 1078774989 to float
  store float %tmp_39, float* @global_y, align 4
  ; print expression: "x = " + x + ", y = " + y
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([5 x i8], [5 x i8]* @.str_expr_13, i32 0, i32 0))
  %tmp_40 = load float, float* @global_x, align 4
  %tmp_41 = fpext float %tmp_40 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_41)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_14, i32 0, i32 0))
  %tmp_42 = load float, float* @global_y, align 4
  %tmp_43 = fpext float %tmp_42 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_43)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Addition: " + x + " + " + y + " = " + (x + y)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_2, i32 0, i32 0))
  %tmp_44 = load float, float* @global_x, align 4
  %tmp_45 = fpext float %tmp_44 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_45)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_3, i32 0, i32 0))
  %tmp_46 = load float, float* @global_y, align 4
  %tmp_47 = fpext float %tmp_46 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_47)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_48 = load float, float* @global_x, align 4
  %tmp_49 = load float, float* @global_y, align 4
  %tmp_50 = fadd float %tmp_48, %tmp_49
  %tmp_51 = fpext float %tmp_50 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_51)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Subtraction: " + x + " - " + y + " = " + (x - y)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([14 x i8], [14 x i8]* @.str_expr_5, i32 0, i32 0))
  %tmp_52 = load float, float* @global_x, align 4
  %tmp_53 = fpext float %tmp_52 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_53)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_6, i32 0, i32 0))
  %tmp_54 = load float, float* @global_y, align 4
  %tmp_55 = fpext float %tmp_54 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_55)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_56 = load float, float* @global_x, align 4
  %tmp_57 = load float, float* @global_y, align 4
  %tmp_58 = fsub float %tmp_56, %tmp_57
  %tmp_59 = fpext float %tmp_58 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_59)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Multiplication: " + x + " * " + y + " = " + (x * ...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_7, i32 0, i32 0))
  %tmp_60 = load float, float* @global_x, align 4
  %tmp_61 = fpext float %tmp_60 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_61)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_8, i32 0, i32 0))
  %tmp_62 = load float, float* @global_y, align 4
  %tmp_63 = fpext float %tmp_62 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_63)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_64 = load float, float* @global_x, align 4
  %tmp_65 = load float, float* @global_y, align 4
  %tmp_66 = fmul float %tmp_64, %tmp_65
  %tmp_67 = fpext float %tmp_66 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_67)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "Division: " + x + " / " + y + " = " + (x / y)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_9, i32 0, i32 0))
  %tmp_68 = load float, float* @global_x, align 4
  %tmp_69 = fpext float %tmp_68 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_69)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_10, i32 0, i32 0))
  %tmp_70 = load float, float* @global_y, align 4
  %tmp_71 = fpext float %tmp_70 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_71)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_expr_4, i32 0, i32 0))
  %tmp_72 = load float, float* @global_x, align 4
  %tmp_73 = load float, float* @global_y, align 4
  %tmp_74 = fdiv float %tmp_72, %tmp_73
  %tmp_75 = fpext float %tmp_74 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_75)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_76_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_76_str, align 1
  %tmp_77 = bitcast [1 x i8]* %tmp_76_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_77)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Operations with Parentheses:" (inline)
  %tmp_78_str = alloca [29 x i8], align 1
  store [29 x i8] c"Operations with Parentheses:\00", [29 x i8]* %tmp_78_str, align 1
  %tmp_79 = bitcast [29 x i8]* %tmp_78_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_79)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int result1 = (a + b) * 2
  store i32 0, i32* @global_result1, align 4
  ; int result2 = a * (b + 5)
  ; unknown function call assignment: result2 = a * (b + 5)
  ; int result3 = (a * 2) + (b * 3)
  ; unknown function call assignment: result3 = (a * 2) + (b * 3)
  ; print expression: "(a + b) * 2 = " + result1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_15, i32 0, i32 0))
  %tmp_80 = load i32, i32* @global_result1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_80)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "a * (b + 5) = " + result2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_16, i32 0, i32 0))
  %tmp_81 = load i32, i32* @global_result2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_81)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * 2) + (b * 3) = " + result3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_17, i32 0, i32 0))
  %tmp_82 = load i32, i32* @global_result3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_82)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_83_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_83_str, align 1
  %tmp_84 = bitcast [1 x i8]* %tmp_83_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_84)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Complex Nested Expressions:" (inline)
  %tmp_85_str = alloca [28 x i8], align 1
  store [28 x i8] c"Complex Nested Expressions:\00", [28 x i8]* %tmp_85_str, align 1
  %tmp_86 = bitcast [28 x i8]* %tmp_85_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_86)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int complex1 = ((a + b) * 2) - (a / b)
  ; unknown function call assignment: complex1 = ((a + b) * 2) - (a / b)
  ; int complex2 = (a * b) + ((a - b) * 2)
  ; unknown function call assignment: complex2 = (a * b) + ((a - b) * 2)
  ; int complex3 = ((a + 5) * (b - 1)) / 2
  store i32 0, i32* @global_complex3, align 4
  ; print expression: "((a + b) * 2) - (a / b) = " + complex1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_18, i32 0, i32 0))
  %tmp_87 = load i32, i32* @global_complex1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_87)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(a * b) + ((a - b) * 2) = " + complex2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_19, i32 0, i32 0))
  %tmp_88 = load i32, i32* @global_complex2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_88)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "((a + 5) * (b - 1)) / 2 = " + complex3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([27 x i8], [27 x i8]* @.str_expr_20, i32 0, i32 0))
  %tmp_89 = load i32, i32* @global_complex3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_89)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_90_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_90_str, align 1
  %tmp_91 = bitcast [1 x i8]* %tmp_90_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_91)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Negative Number Operations:" (inline)
  %tmp_92_str = alloca [28 x i8], align 1
  store [28 x i8] c"Negative Number Operations:\00", [28 x i8]* %tmp_92_str, align 1
  %tmp_93 = bitcast [28 x i8]* %tmp_92_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_93)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int neg1 = -5
  store i32 -5, i32* @global_neg1, align 4
  ; int neg2 = -3
  store i32 -3, i32* @global_neg2, align 4
  ; print expression: "neg1 = " + neg1 + ", neg2 = " + neg2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_21, i32 0, i32 0))
  %tmp_94 = load i32, i32* @global_neg1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_94)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([10 x i8], [10 x i8]* @.str_expr_22, i32 0, i32 0))
  %tmp_95 = load i32, i32* @global_neg2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_95)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(-5) + (-3) = " + (neg1 + neg2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_23, i32 0, i32 0))
  %tmp_96 = load i32, i32* @global_neg1, align 4
  %tmp_97 = load i32, i32* @global_neg2, align 4
  %tmp_98 = add i32 %tmp_96, %tmp_97
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_98)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(-5) * (-3) = " + (neg1 * neg2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_24, i32 0, i32 0))
  %tmp_99 = load i32, i32* @global_neg1, align 4
  %tmp_100 = load i32, i32* @global_neg2, align 4
  %tmp_101 = mul i32 %tmp_99, %tmp_100
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_101)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(-5) - (-3) = " + (neg1 - neg2)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_25, i32 0, i32 0))
  %tmp_102 = load i32, i32* @global_neg1, align 4
  %tmp_103 = load i32, i32* @global_neg2, align 4
  %tmp_104 = sub i32 %tmp_102, %tmp_103
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_104)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_105_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_105_str, align 1
  %tmp_106 = bitcast [1 x i8]* %tmp_105_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_106)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Assignment with Operations:" (inline)
  %tmp_107_str = alloca [28 x i8], align 1
  store [28 x i8] c"Assignment with Operations:\00", [28 x i8]* %tmp_107_str, align 1
  %tmp_108 = bitcast [28 x i8]* %tmp_107_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_108)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int counter = 0
  store i32 0, i32* @global_counter, align 4
  ; print expression: "Initial counter = " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([19 x i8], [19 x i8]* @.str_expr_26, i32 0, i32 0))
  %tmp_109 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_109)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter + 1
  %tmp_110 = load i32, i32* @global_counter, align 4
  %tmp_111 = add i32 %tmp_110, 1
  store i32 %tmp_111, i32* @global_counter, align 4
  ; print expression: "After increment: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([18 x i8], [18 x i8]* @.str_expr_27, i32 0, i32 0))
  %tmp_112 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_112)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter * 2
  %tmp_113 = load i32, i32* @global_counter, align 4
  %tmp_114 = mul i32 %tmp_113, 2
  store i32 %tmp_114, i32* @global_counter, align 4
  ; print expression: "After doubling: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([17 x i8], [17 x i8]* @.str_expr_28, i32 0, i32 0))
  %tmp_115 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_115)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; set counter = counter - 5
  %tmp_116 = load i32, i32* @global_counter, align 4
  %tmp_117 = sub i32 %tmp_116, 5
  store i32 %tmp_117, i32* @global_counter, align 4
  ; print expression: "After subtracting 5: " + counter
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([22 x i8], [22 x i8]* @.str_expr_29, i32 0, i32 0))
  %tmp_118 = load i32, i32* @global_counter, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_118)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_119_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_119_str, align 1
  %tmp_120 = bitcast [1 x i8]* %tmp_119_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_120)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Chain Operations:" (inline)
  %tmp_121_str = alloca [18 x i8], align 1
  store [18 x i8] c"Chain Operations:\00", [18 x i8]* %tmp_121_str, align 1
  %tmp_122 = bitcast [18 x i8]* %tmp_121_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_122)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int chain_result = 1 + 2 + 3 + 4 + 5
  store i32 0, i32* @global_chain_result, align 4
  ; print expression: "1 + 2 + 3 + 4 + 5 = " + chain_result
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([21 x i8], [21 x i8]* @.str_expr_30, i32 0, i32 0))
  %tmp_123 = load i32, i32* @global_chain_result, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_123)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int chain_mult = 2 * 3 * 4
  store i32 0, i32* @global_chain_mult, align 4
  ; print expression: "2 * 3 * 4 = " + chain_mult
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_31, i32 0, i32 0))
  %tmp_124 = load i32, i32* @global_chain_mult, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_124)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_125_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_125_str, align 1
  %tmp_126 = bitcast [1 x i8]* %tmp_125_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_126)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Power Operations (using multiplication):" (inline)
  %tmp_127_str = alloca [41 x i8], align 1
  store [41 x i8] c"Power Operations (using multiplication):\00", [41 x i8]* %tmp_127_str, align 1
  %tmp_128 = bitcast [41 x i8]* %tmp_127_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_128)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int base = 2
  store i32 2, i32* @global_base, align 4
  ; int power2 = base * base
  store i32 0, i32* @global_power2, align 4
  ; int power3 = base * base * base
  store i32 0, i32* @global_power3, align 4
  ; int power4 = base * base * base * base
  store i32 0, i32* @global_power4, align 4
  ; print expression: "base = " + base
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([8 x i8], [8 x i8]* @.str_expr_32, i32 0, i32 0))
  %tmp_129 = load i32, i32* @global_base, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_129)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2^2 = " + power2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_33, i32 0, i32 0))
  %tmp_130 = load i32, i32* @global_power2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_130)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2^3 = " + power3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_34, i32 0, i32 0))
  %tmp_131 = load i32, i32* @global_power3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_131)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2^4 = " + power4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([7 x i8], [7 x i8]* @.str_expr_35, i32 0, i32 0))
  %tmp_132 = load i32, i32* @global_power4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_132)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_133_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_133_str, align 1
  %tmp_134 = bitcast [1 x i8]* %tmp_133_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_134)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Mixed Type Operations:" (inline)
  %tmp_135_str = alloca [23 x i8], align 1
  store [23 x i8] c"Mixed Type Operations:\00", [23 x i8]* %tmp_135_str, align 1
  %tmp_136 = bitcast [23 x i8]* %tmp_135_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_136)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int int_val = 10
  store i32 10, i32* @global_int_val, align 4
  ; float float_val = 2.5
  %tmp_137 = bitcast i32 1075838976 to float
  store float %tmp_137, float* @global_float_val, align 4
  ; print expression: "int_val = " + int_val + ", float_val = " + float_...
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([11 x i8], [11 x i8]* @.str_expr_36, i32 0, i32 0))
  %tmp_138 = load i32, i32* @global_int_val, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_138)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_37, i32 0, i32 0))
  %tmp_139 = load float, float* @global_float_val, align 4
  %tmp_140 = fpext float %tmp_139 to double
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([4 x i8], [4 x i8]* @.str_float, i32 0, i32 0), double %tmp_140)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Mixed operations require careful handling" (inline)
  %tmp_141_str = alloca [42 x i8], align 1
  store [42 x i8] c"Mixed operations require careful handling\00", [42 x i8]* %tmp_141_str, align 1
  %tmp_142 = bitcast [42 x i8]* %tmp_141_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_142)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_143_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_143_str, align 1
  %tmp_144 = bitcast [1 x i8]* %tmp_143_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_144)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "Order of Operations:" (inline)
  %tmp_145_str = alloca [21 x i8], align 1
  store [21 x i8] c"Order of Operations:\00", [21 x i8]* %tmp_145_str, align 1
  %tmp_146 = bitcast [21 x i8]* %tmp_145_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_146)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; int order1 = 2 + 3 * 4
  store i32 0, i32* @global_order1, align 4
  ; int order2 = (2 + 3) * 4
  store i32 0, i32* @global_order2, align 4
  ; int order3 = 2 * 3 + 4
  store i32 0, i32* @global_order3, align 4
  ; int order4 = 2 * (3 + 4)
  ; unknown function call assignment: order4 = 2 * (3 + 4)
  ; print expression: "2 + 3 * 4 = " + order1
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_38, i32 0, i32 0))
  %tmp_147 = load i32, i32* @global_order1, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_147)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "(2 + 3) * 4 = " + order2
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_39, i32 0, i32 0))
  %tmp_148 = load i32, i32* @global_order2, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_148)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2 * 3 + 4 = " + order3
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([13 x i8], [13 x i8]* @.str_expr_40, i32 0, i32 0))
  %tmp_149 = load i32, i32* @global_order3, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_149)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print expression: "2 * (3 + 4) = " + order4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([15 x i8], [15 x i8]* @.str_expr_41, i32 0, i32 0))
  %tmp_150 = load i32, i32* @global_order4, align 4
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([3 x i8], [3 x i8]* @.str_int_fmt, i32 0, i32 0), i32 %tmp_150)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "" (inline)
  %tmp_151_str = alloca [1 x i8], align 1
  store [1 x i8] c"\00", [1 x i8]* %tmp_151_str, align 1
  %tmp_152 = bitcast [1 x i8]* %tmp_151_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_152)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ; print "=== End of Arithmetic Operations Demo ===" (inline)
  %tmp_153_str = alloca [42 x i8], align 1
  store [42 x i8] c"=== End of Arithmetic Operations Demo ===\00", [42 x i8]* %tmp_153_str, align 1
  %tmp_154 = bitcast [42 x i8]* %tmp_153_str to i8*
  call i32 (i8*, ...) @printf(i8* %tmp_154)
  call i32 (i8*, ...) @printf(i8* getelementptr inbounds ([2 x i8], [2 x i8]* @.str_newline, i32 0, i32 0))
  ret i32 0
}
