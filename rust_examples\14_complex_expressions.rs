// 14. Complex Expressions - nested operations, precedence (Rust version)
// This file demonstrates complex expressions in Rust for comparison with Dolet

fn main() {
    println!("=== Complex Expressions Demo (Rust) ===");
    println!();

    // Basic arithmetic precedence
    println!("Basic arithmetic precedence:");
    let result1 = 2 + 3 * 4;
    let result2 = (2 + 3) * 4;
    let result3 = 2 * 3 + 4;
    let result4 = 2 * (3 + 4);
    
    println!("2 + 3 * 4 = {}", result1);        // 14 (multiplication first)
    println!("(2 + 3) * 4 = {}", result2);      // 20 (parentheses first)
    println!("2 * 3 + 4 = {}", result3);        // 10 (multiplication first)
    println!("2 * (3 + 4) = {}", result4);      // 14 (parentheses first)
    println!();

    // Complex mathematical expressions
    println!("Complex mathematical expressions:");
    let a = 10;
    let b = 5;
    let c = 3;
    let d = 2;
    
    let complex1 = a + b * c - d;
    let complex2 = (a + b) * (c - d);
    let complex3 = a * b / c + d;
    let complex4 = a / (b - c) + d * c;
    
    println!("a={}, b={}, c={}, d={}", a, b, c, d);
    println!("a + b * c - d = {} + {} * {} - {} = {}", a, b, c, d, complex1);
    println!("(a + b) * (c - d) = ({} + {}) * ({} - {}) = {}", a, b, c, d, complex2);
    println!("a * b / c + d = {} * {} / {} + {} = {}", a, b, c, d, complex3);
    println!("a / (b - c) + d * c = {} / ({} - {}) + {} * {} = {}", a, b, c, d, d, complex4);
    println!();

    // Boolean expressions with precedence
    println!("Boolean expressions with precedence:");
    let x = true;
    let y = false;
    let z = true;
    
    let bool1 = x && y || z;
    let bool2 = x && (y || z);
    let bool3 = (x && y) || z;
    let bool4 = !x || y && z;
    let bool5 = !(x || y) && z;
    
    println!("x={}, y={}, z={}", x, y, z);
    println!("x && y || z = {} && {} || {} = {}", x, y, z, bool1);
    println!("x && (y || z) = {} && ({} || {}) = {}", x, y, z, bool2);
    println!("(x && y) || z = ({} && {}) || {} = {}", x, y, z, bool3);
    println!("!x || y && z = !{} || {} && {} = {}", x, y, z, bool4);
    println!("!(x || y) && z = !({} || {}) && {} = {}", x, y, z, bool5);
    println!();

    // Comparison expressions
    println!("Comparison expressions:");
    let num1 = 15;
    let num2 = 10;
    let num3 = 20;
    
    let comp1 = num1 > num2 && num1 < num3;
    let comp2 = num1 >= num2 || num1 <= num3;
    let comp3 = (num1 + num2) > num3;
    let comp4 = num1 * 2 == num3 + num2;
    
    println!("num1={}, num2={}, num3={}", num1, num2, num3);
    println!("num1 > num2 && num1 < num3 = {} > {} && {} < {} = {}", num1, num2, num1, num3, comp1);
    println!("num1 >= num2 || num1 <= num3 = {} >= {} || {} <= {} = {}", num1, num2, num1, num3, comp2);
    println!("(num1 + num2) > num3 = ({} + {}) > {} = {}", num1, num2, num3, comp3);
    println!("num1 * 2 == num3 + num2 = {} * 2 == {} + {} = {}", num1, num3, num2, comp4);
    println!();

    // Nested function calls
    println!("Nested function calls:");
    let nested1 = add(multiply(3, 4), subtract(10, 5));
    let nested2 = multiply(add(2, 3), subtract(8, 3));
    let nested3 = divide(add(multiply(4, 5), 10), subtract(15, 5));
    
    println!("add(multiply(3, 4), subtract(10, 5)) = add({}, {}) = {}", multiply(3, 4), subtract(10, 5), nested1);
    println!("multiply(add(2, 3), subtract(8, 3)) = multiply({}, {}) = {}", add(2, 3), subtract(8, 3), nested2);
    println!("divide(add(multiply(4, 5), 10), subtract(15, 5)) = divide({}, {}) = {}", add(multiply(4, 5), 10), subtract(15, 5), nested3);
    println!();

    // Array expressions
    println!("Array expressions:");
    let arr1 = [1, 2, 3, 4, 5];
    let arr2 = [10, 20, 30];
    
    let array_expr1 = arr1[0] + arr1[4];
    let array_expr2 = arr1[1] * arr2[0] / arr1[2];
    let array_expr3 = (arr1[0] + arr1[1]) * (arr2[1] - arr2[0]);
    
    println!("arr1[0] + arr1[4] = {} + {} = {}", arr1[0], arr1[4], array_expr1);
    println!("arr1[1] * arr2[0] / arr1[2] = {} * {} / {} = {}", arr1[1], arr2[0], arr1[2], array_expr2);
    println!("(arr1[0] + arr1[1]) * (arr2[1] - arr2[0]) = ({} + {}) * ({} - {}) = {}", 
             arr1[0], arr1[1], arr2[1], arr2[0], array_expr3);
    println!();

    // String expressions
    println!("String expressions:");
    let str1 = "Hello";
    let str2 = "World";
    let str3 = "!";
    
    let str_expr1 = format!("{} {}{}", str1, str2, str3);
    let str_expr2 = format!("{} {} {}", str1.to_uppercase(), str2.to_lowercase(), str3);
    
    println!("Concatenation: '{}' + ' ' + '{}' + '{}' = '{}'", str1, str2, str3, str_expr1);
    println!("With case changes: '{}' = '{}'", "str1.upper() + ' ' + str2.lower() + str3", str_expr2);
    println!();

    // Conditional expressions (ternary-like)
    println!("Conditional expressions:");
    let score = 85;
    let grade = if score >= 90 { "A" } else if score >= 80 { "B" } else { "C" };
    let status = if score >= 60 { "Pass" } else { "Fail" };
    let bonus = if score > 95 { 100 } else { 0 };
    
    println!("Score: {}", score);
    println!("Grade: {}", grade);
    println!("Status: {}", status);
    println!("Bonus: {}", bonus);
    println!();

    // Complex conditional expressions
    println!("Complex conditional expressions:");
    let age = 25;
    let income = 50000;
    let has_job = true;
    let credit_score = 750;
    
    let loan_eligible = age >= 18 && age <= 65 && income >= 30000 && has_job && credit_score >= 700;
    let loan_amount = if loan_eligible { 
        if income > 80000 { 500000 } else { 300000 }
    } else { 
        0 
    };
    
    println!("Age: {}, Income: {}, Has job: {}, Credit score: {}", age, income, has_job, credit_score);
    println!("Loan eligible: {}", loan_eligible);
    println!("Loan amount: {}", loan_amount);
    println!();

    // Mathematical formulas
    println!("Mathematical formulas:");
    let radius = 5.0;
    let height = 10.0;
    let pi = 3.14159;
    
    // Circle area: π * r²
    let circle_area = pi * radius * radius;
    
    // Cylinder volume: π * r² * h
    let cylinder_volume = pi * radius * radius * height;
    
    // Sphere volume: (4/3) * π * r³
    let sphere_volume = (4.0 / 3.0) * pi * radius * radius * radius;
    
    println!("Radius: {}, Height: {}", radius, height);
    println!("Circle area (π * r²): {:.2}", circle_area);
    println!("Cylinder volume (π * r² * h): {:.2}", cylinder_volume);
    println!("Sphere volume ((4/3) * π * r³): {:.2}", sphere_volume);
    println!();

    // Compound interest formula
    println!("Compound interest formula:");
    let principal = 1000.0_f64;
    let rate = 0.05_f64; // 5%
    let time = 3.0_f64;
    let n = 12.0_f64; // Monthly compounding

    // A = P(1 + r/n)^(nt)
    let amount = principal * (1.0 + rate / n).powf(n * time);
    let interest = amount - principal;
    
    println!("Principal: ${:.2}", principal);
    println!("Rate: {:.1}%", rate * 100.0);
    println!("Time: {:.0} years", time);
    println!("Compounding: {:.0} times per year", n);
    println!("Final amount: ${:.2}", amount);
    println!("Interest earned: ${:.2}", interest);
    println!();

    // Quadratic formula: (-b ± √(b² - 4ac)) / 2a
    println!("Quadratic formula:");
    let a_coeff = 1.0_f64;
    let b_coeff = -5.0_f64;
    let c_coeff = 6.0_f64;

    let discriminant = b_coeff * b_coeff - 4.0 * a_coeff * c_coeff;
    
    if discriminant >= 0.0 {
        let root1 = (-b_coeff + discriminant.sqrt()) / (2.0 * a_coeff);
        let root2 = (-b_coeff - discriminant.sqrt()) / (2.0 * a_coeff);
        
        println!("Equation: {}x² + {}x + {} = 0", a_coeff, b_coeff, c_coeff);
        println!("Discriminant: {:.2}", discriminant);
        println!("Root 1: {:.2}", root1);
        println!("Root 2: {:.2}", root2);
    } else {
        println!("No real roots (discriminant = {:.2})", discriminant);
    }
    println!();

    // Complex array operations
    println!("Complex array operations:");
    let numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    
    let sum: i32 = numbers.iter().sum();
    let product: i32 = numbers.iter().product();
    let average = sum as f64 / numbers.len() as f64;
    let max_val = *numbers.iter().max().unwrap();
    let min_val = *numbers.iter().min().unwrap();
    
    println!("Numbers: {:?}", numbers);
    println!("Sum: {}", sum);
    println!("Product: {}", product);
    println!("Average: {:.2}", average);
    println!("Maximum: {}", max_val);
    println!("Minimum: {}", min_val);
    println!("Range: {}", max_val - min_val);
    println!();

    println!("=== End of Complex Expressions Demo ===");
}

// Helper functions for nested calls
fn add(a: i32, b: i32) -> i32 {
    a + b
}

fn subtract(a: i32, b: i32) -> i32 {
    a - b
}

fn multiply(a: i32, b: i32) -> i32 {
    a * b
}

fn divide(a: i32, b: i32) -> i32 {
    a / b
}
