# 🧪 COMPREHENSIVE PROBLEM TEST FILE 🧪
# Testing ALL possible string issues and edge cases

print "=== COMPREHENSIVE PROBLEM TEST ==="
print ""

# ========================================
# 🎯 STRING QUOTE ISSUES (Main Problem)
# ========================================
print "=== STRING QUOTE ISSUES ==="

# Test 1: Basic quote escaping
string basic_quotes = "She said \"Hello\" to me"
print "Basic quotes: " + basic_quotes

# Test 2: Multiple quotes in one string
string multiple_quotes = "\"Start\" and \"End\" and \"Middle\""
print "Multiple quotes: " + multiple_quotes

# Test 3: Quotes with other special characters
string quotes_and_special = "Quote: \"Test\" Symbol: @ Percent: % Hash: #"
print "Quotes and special: " + quotes_and_special

# Test 4: The original problematic string
string original_problem = "   Multiple   Spaces   And   Special   Characters   !@#$%^&*()_+-=[]{}|;':\",./<>?   "
print "Original problem: " + original_problem

# Test 5: Quotes at beginning and end
string quotes_edges = "\"Beginning and end\""
print "Quotes at edges: " + quotes_edges

print ""

# ========================================
# 🎯 PERCENT CHARACTER ISSUES
# ========================================
print "=== PERCENT CHARACTER ISSUES ==="

# Test 6: Single percent
string single_percent = "Progress: 50%"
print "Single percent: " + single_percent

# Test 7: Multiple percents
string multiple_percents = "100% complete, 50% done, 25% remaining"
print "Multiple percents: " + multiple_percents

# Test 8: Percent with other specials
string percent_special = "%d %s %f %c %% formatting"
print "Percent special: " + percent_special

print ""

# ========================================
# 🎯 ESCAPE SEQUENCE ISSUES
# ========================================
print "=== ESCAPE SEQUENCE ISSUES ==="

# Test 9: Newlines and tabs
string newlines_tabs = "Line1\nLine2\tTabbed\nLine3"
print "Newlines and tabs: " + newlines_tabs

# Test 10: Backslashes
string backslashes = "Path: C:\\Users\\<USER>\\File.txt"
print "Backslashes: " + backslashes

# Test 11: Mixed escapes
string mixed_escapes = "Quote:\" Newline:\n Tab:\t Backslash:\\ Percent:%"
print "Mixed escapes: " + mixed_escapes

print ""

# ========================================
# 🎯 SPECIAL CHARACTER COMBINATIONS
# ========================================
print "=== SPECIAL CHARACTER COMBINATIONS ==="

# Test 12: All symbols
string all_symbols = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
print "All symbols: " + all_symbols

# Test 13: Numbers and symbols
string numbers_symbols = "123!@#456$%^789&*()"
print "Numbers and symbols: " + numbers_symbols

# Test 14: Unicode-like characters (ASCII extended)
string extended_chars = "Café naïve résumé"
print "Extended chars: " + extended_chars

print ""

# ========================================
# 🎯 WHITESPACE ISSUES
# ========================================
print "=== WHITESPACE ISSUES ==="

# Test 15: Leading/trailing spaces
string spaces_edges = "   Leading and trailing spaces   "
print "Spaces edges: '" + spaces_edges + "'"

# Test 16: Multiple internal spaces
string internal_spaces = "Word1     Word2     Word3"
print "Internal spaces: '" + internal_spaces + "'"

# Test 17: Mixed whitespace
string mixed_whitespace = " \t Mixed \n whitespace \t "
print "Mixed whitespace: '" + mixed_whitespace + "'"

print ""

# ========================================
# 🎯 EMPTY AND MINIMAL STRINGS
# ========================================
print "=== EMPTY AND MINIMAL STRINGS ==="

# Test 18: Empty string
string empty_str = ""
print "Empty string: '" + empty_str + "'"

# Test 19: Single character strings
string single_char = "A"
string single_quote = "\""
string single_percent = "%"
print "Single char: " + single_char
print "Single quote: " + single_quote
print "Single percent: " + single_percent

print ""

# ========================================
# 🎯 VERY LONG STRINGS
# ========================================
print "=== VERY LONG STRINGS ==="

# Test 20: Very long string with mixed content
string very_long = "This is a very long string that contains many different types of characters including quotes like \"hello\" and percents like 100% and symbols like !@#$%^&*() and escape sequences like \n newlines and \t tabs and backslashes like \\ and should test the complete string handling system of the Dolet compiler to make sure everything works correctly even with very long content that might cause buffer issues or parsing problems."
print "Very long: " + very_long

print ""

# ========================================
# 🎯 CONCATENATION WITH PROBLEMATIC STRINGS
# ========================================
print "=== CONCATENATION TESTS ==="

# Test 21: Concatenating problematic strings
string part1 = "Start \"quote\""
string part2 = " middle % percent "
string part3 = "end \n newline"
string combined = part1 + part2 + part3
print "Combined: " + combined

print ""

# ========================================
# 🎯 VARIABLE ASSIGNMENTS WITH PROBLEMS
# ========================================
print "=== VARIABLE ASSIGNMENT TESTS ==="

# Test 22: Reassigning problematic strings
set basic_quotes = "New \"quoted\" value"
set single_percent = "New 100% value"
set mixed_escapes = "New \n\t\\ value"

print "After reassignment:"
print "Basic quotes: " + basic_quotes
print "Single percent: " + single_percent  
print "Mixed escapes: " + mixed_escapes

print ""

# ========================================
# 🎯 FINAL STRESS TEST
# ========================================
print "=== FINAL STRESS TEST ==="

# Test 23: The ultimate problematic string
string ultimate_test = "\"Ultimate test: 100% complete with \n newlines, \t tabs, \\ backslashes, and all symbols !@#$%^&*()_+-=[]{}|;':\",./<>? in one string\""
print "Ultimate test: " + ultimate_test

print ""
print "=== ALL TESTS COMPLETED ==="
print "If you can see this message, all string problems are SOLVED! 🎉"
