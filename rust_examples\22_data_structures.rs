// 22. Data Structures - structs, enums, collections (Rust version)
// This file demonstrates data structures in Rust for comparison with Dolet

use std::collections::{HashMap, HashSet, VecDeque, BTreeMap};

fn main() {
    println!("=== Data Structures Demo (Rust) ===");
    println!();

    // Basic struct definition and usage
    println!("Basic struct usage:");
    let person1 = Person {
        name: "<PERSON>".to_string(),
        age: 25,
        city: "Cairo".to_string(),
        active: true,
    };
    
    let person2 = Person {
        name: "<PERSON>".to_string(),
        age: 30,
        city: "Alexandria".to_string(),
        active: false,
    };
    
    println!("Person 1: {} (age {}) from {} - Active: {}", 
             person1.name, person1.age, person1.city, person1.active);
    println!("Person 2: {} (age {}) from {} - Active: {}", 
             person2.name, person2.age, person2.city, person2.active);
    
    // Using struct methods
    person1.display_info();
    println!("Person 1 is adult: {}", person1.is_adult());
    println!();

    // Tuple structs
    println!("Tuple structs:");
    let point = Point(3.0, 4.0);
    let color = Color(255, 128, 0);
    
    println!("Point: ({}, {})", point.0, point.1);
    println!("Distance from origin: {:.2}", point.distance_from_origin());
    println!("Color RGB: ({}, {}, {})", color.0, color.1, color.2);
    println!();

    // Enums
    println!("Enums:");
    let status1 = Status::Active;
    let status2 = Status::Inactive;
    let status3 = Status::Pending;
    
    println!("Status 1: {:?}", status1);
    println!("Status 2: {:?}", status2);
    println!("Status 3: {:?}", status3);
    
    // Enum with associated data
    let message1 = Message::Text("Hello World".to_string());
    let message2 = Message::Number(42);
    let message3 = Message::Quit;
    
    process_message(&message1);
    process_message(&message2);
    process_message(&message3);
    println!();

    // Vectors (dynamic arrays)
    println!("Vectors (dynamic arrays):");
    let mut numbers = vec![1, 2, 3, 4, 5];
    println!("Initial vector: {:?}", numbers);
    
    numbers.push(6);
    numbers.push(7);
    println!("After pushing: {:?}", numbers);
    
    if let Some(last) = numbers.pop() {
        println!("Popped element: {}", last);
    }
    println!("After popping: {:?}", numbers);
    
    numbers.insert(2, 99);
    println!("After inserting 99 at index 2: {:?}", numbers);
    
    numbers.remove(2);
    println!("After removing element at index 2: {:?}", numbers);
    println!();

    // HashMap (key-value pairs)
    println!("HashMap (key-value pairs):");
    let mut scores = HashMap::new();
    scores.insert("Ahmed", 95);
    scores.insert("Sara", 87);
    scores.insert("Omar", 92);
    scores.insert("Fatima", 98);
    
    println!("Student scores:");
    for (name, score) in &scores {
        println!("  {}: {}", name, score);
    }
    
    // Accessing values
    if let Some(score) = scores.get("Ahmed") {
        println!("Ahmed's score: {}", score);
    }
    
    // Updating values
    scores.insert("Ahmed", 97); // Update existing
    scores.entry("Layla").or_insert(85); // Insert if not exists
    
    println!("Updated scores:");
    for (name, score) in &scores {
        println!("  {}: {}", name, score);
    }
    println!();

    // HashSet (unique values)
    println!("HashSet (unique values):");
    let mut unique_numbers = HashSet::new();
    let numbers_to_add = [1, 2, 3, 2, 4, 3, 5, 1, 6];
    
    for num in numbers_to_add {
        unique_numbers.insert(num);
        println!("Added {}, set now contains: {:?}", num, unique_numbers);
    }
    
    println!("Final unique numbers: {:?}", unique_numbers);
    println!("Contains 3: {}", unique_numbers.contains(&3));
    println!("Contains 10: {}", unique_numbers.contains(&10));
    println!();

    // VecDeque (double-ended queue)
    println!("VecDeque (double-ended queue):");
    let mut deque = VecDeque::new();
    
    // Add to both ends
    deque.push_back(1);
    deque.push_back(2);
    deque.push_front(0);
    deque.push_front(-1);
    
    println!("Deque after pushes: {:?}", deque);
    
    // Remove from both ends
    if let Some(front) = deque.pop_front() {
        println!("Popped from front: {}", front);
    }
    if let Some(back) = deque.pop_back() {
        println!("Popped from back: {}", back);
    }
    
    println!("Deque after pops: {:?}", deque);
    println!();

    // BTreeMap (sorted key-value pairs)
    println!("BTreeMap (sorted key-value pairs):");
    let mut sorted_map = BTreeMap::new();
    sorted_map.insert("zebra", 1);
    sorted_map.insert("apple", 2);
    sorted_map.insert("banana", 3);
    sorted_map.insert("cherry", 4);
    
    println!("BTreeMap (automatically sorted by key):");
    for (key, value) in &sorted_map {
        println!("  {}: {}", key, value);
    }
    println!();

    // Nested data structures
    println!("Nested data structures:");
    let mut students = HashMap::new();
    
    let student1 = Student {
        name: "Ahmed".to_string(),
        grades: vec![85, 92, 78, 96],
        subjects: vec!["Math".to_string(), "Physics".to_string(), "Chemistry".to_string()],
    };
    
    let student2 = Student {
        name: "Sara".to_string(),
        grades: vec![90, 88, 94, 87],
        subjects: vec!["Biology".to_string(), "Chemistry".to_string(), "English".to_string()],
    };
    
    students.insert(1, student1);
    students.insert(2, student2);
    
    for (id, student) in &students {
        println!("Student ID {}: {}", id, student.name);
        println!("  Subjects: {:?}", student.subjects);
        println!("  Grades: {:?}", student.grades);
        println!("  Average: {:.2}", student.calculate_average());
    }
    println!();

    // Option and Result types
    println!("Option and Result types:");
    let maybe_number = Some(42);
    let no_number: Option<i32> = None;
    
    match maybe_number {
        Some(value) => println!("Found number: {}", value),
        None => println!("No number found"),
    }
    
    match no_number {
        Some(value) => println!("Found number: {}", value),
        None => println!("No number found"),
    }
    
    // Result type for error handling
    let division_result = safe_divide(10, 2);
    match division_result {
        Ok(result) => println!("Division result: {}", result),
        Err(error) => println!("Division error: {}", error),
    }
    
    let error_result = safe_divide(10, 0);
    match error_result {
        Ok(result) => println!("Division result: {}", result),
        Err(error) => println!("Division error: {}", error),
    }
    println!();

    // Custom data structure with methods
    println!("Custom data structure with methods:");
    let mut library = Library::new();
    
    library.add_book(Book {
        title: "The Rust Programming Language".to_string(),
        author: "Steve Klabnik".to_string(),
        pages: 552,
        available: true,
    });
    
    library.add_book(Book {
        title: "Programming Rust".to_string(),
        author: "Jim Blandy".to_string(),
        pages: 624,
        available: true,
    });
    
    library.display_books();
    
    if library.borrow_book("The Rust Programming Language") {
        println!("Successfully borrowed 'The Rust Programming Language'");
    }
    
    library.display_books();
    println!();

    // Generic data structure
    println!("Generic data structure:");
    let mut int_stack = Stack::new();
    int_stack.push(1);
    int_stack.push(2);
    int_stack.push(3);
    
    println!("Integer stack:");
    while let Some(value) = int_stack.pop() {
        println!("  Popped: {}", value);
    }
    
    let mut string_stack = Stack::new();
    string_stack.push("Hello".to_string());
    string_stack.push("World".to_string());
    
    println!("String stack:");
    while let Some(value) = string_stack.pop() {
        println!("  Popped: {}", value);
    }
    println!();

    println!("=== End of Data Structures Demo ===");
}

// Struct definitions
#[derive(Debug)]
struct Person {
    name: String,
    age: u32,
    city: String,
    active: bool,
}

impl Person {
    fn display_info(&self) {
        println!("Person Info: {} is {} years old, lives in {}, active: {}", 
                 self.name, self.age, self.city, self.active);
    }
    
    fn is_adult(&self) -> bool {
        self.age >= 18
    }
}

// Tuple structs
struct Point(f64, f64);
struct Color(u8, u8, u8);

impl Point {
    fn distance_from_origin(&self) -> f64 {
        (self.0 * self.0 + self.1 * self.1).sqrt()
    }
}

// Enums
#[derive(Debug)]
enum Status {
    Active,
    Inactive,
    Pending,
}

#[derive(Debug)]
enum Message {
    Text(String),
    Number(i32),
    Quit,
}

fn process_message(msg: &Message) {
    match msg {
        Message::Text(text) => println!("Text message: {}", text),
        Message::Number(num) => println!("Number message: {}", num),
        Message::Quit => println!("Quit message received"),
    }
}

// Complex struct
struct Student {
    name: String,
    grades: Vec<i32>,
    subjects: Vec<String>,
}

impl Student {
    fn calculate_average(&self) -> f64 {
        if self.grades.is_empty() {
            0.0
        } else {
            self.grades.iter().sum::<i32>() as f64 / self.grades.len() as f64
        }
    }
}

// Result type function
fn safe_divide(a: i32, b: i32) -> Result<i32, String> {
    if b == 0 {
        Err("Division by zero".to_string())
    } else {
        Ok(a / b)
    }
}

// Custom data structure
struct Book {
    title: String,
    author: String,
    pages: u32,
    available: bool,
}

struct Library {
    books: Vec<Book>,
}

impl Library {
    fn new() -> Self {
        Library {
            books: Vec::new(),
        }
    }
    
    fn add_book(&mut self, book: Book) {
        self.books.push(book);
    }
    
    fn display_books(&self) {
        println!("Library books:");
        for (i, book) in self.books.iter().enumerate() {
            println!("  {}: '{}' by {} ({} pages) - Available: {}", 
                     i + 1, book.title, book.author, book.pages, book.available);
        }
    }
    
    fn borrow_book(&mut self, title: &str) -> bool {
        for book in &mut self.books {
            if book.title == title && book.available {
                book.available = false;
                return true;
            }
        }
        false
    }
}

// Generic data structure
struct Stack<T> {
    items: Vec<T>,
}

impl<T> Stack<T> {
    fn new() -> Self {
        Stack {
            items: Vec::new(),
        }
    }
    
    fn push(&mut self, item: T) {
        self.items.push(item);
    }
    
    fn pop(&mut self) -> Option<T> {
        self.items.pop()
    }
    
    fn is_empty(&self) -> bool {
        self.items.is_empty()
    }
}
