#!/usr/bin/env python3
"""
LLVM IR Difference Analyzer
Detailed analysis of differences between Dolet and Rust generated LLVM IR
"""

import os
import re
import difflib
from pathlib import Path

class LLVMAnalyzer:
    def __init__(self, llvm_file):
        self.file_path = llvm_file
        self.content = self._read_file()
        self.lines = self.content.split('\n') if self.content else []
        
    def _read_file(self):
        """Read LLVM IR file content"""
        try:
            with open(self.file_path, 'r') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {self.file_path}: {e}")
            return ""
    
    def get_functions(self):
        """Extract function definitions"""
        functions = []
        for line in self.lines:
            if line.strip().startswith('define '):
                # Extract function signature
                match = re.search(r'define\s+.*?@(\w+)\s*\([^)]*\)', line)
                if match:
                    functions.append({
                        'name': match.group(1),
                        'signature': line.strip(),
                        'line': line
                    })
        return functions
    
    def get_global_variables(self):
        """Extract global variable declarations"""
        globals_vars = []
        for line in self.lines:
            if line.strip().startswith('@') and '=' in line:
                globals_vars.append(line.strip())
        return globals_vars
    
    def get_string_literals(self):
        """Extract string literals"""
        strings = []
        for line in self.lines:
            # Find string literals in LLVM IR
            matches = re.findall(r'c"([^"]*)"', line)
            strings.extend(matches)
        return strings
    
    def get_instruction_types(self):
        """Count different instruction types"""
        instructions = {
            'alloca': 0, 'store': 0, 'load': 0, 'call': 0,
            'br': 0, 'ret': 0, 'add': 0, 'sub': 0, 'mul': 0,
            'div': 0, 'icmp': 0, 'fcmp': 0, 'phi': 0, 'select': 0
        }
        
        for line in self.lines:
            line = line.strip()
            for instr in instructions:
                if f' {instr} ' in line or line.startswith(f'{instr} '):
                    instructions[instr] += 1
        
        return instructions
    
    def get_optimization_level(self):
        """Detect optimization hints in LLVM IR"""
        optimizations = {
            'inlining': 0,
            'constant_folding': 0,
            'dead_code_elimination': 0,
            'loop_optimizations': 0
        }
        
        for line in self.lines:
            if 'inline' in line.lower():
                optimizations['inlining'] += 1
            if 'constant' in line.lower():
                optimizations['constant_folding'] += 1
            # Add more optimization detection logic
        
        return optimizations
    
    def get_metadata(self):
        """Extract metadata information"""
        metadata = []
        for line in self.lines:
            if line.strip().startswith('!') and '=' in line:
                metadata.append(line.strip())
        return metadata
    
    def analyze_complexity(self):
        """Analyze code complexity metrics"""
        return {
            'total_lines': len(self.lines),
            'non_empty_lines': len([l for l in self.lines if l.strip()]),
            'comment_lines': len([l for l in self.lines if l.strip().startswith(';')]),
            'basic_blocks': len([l for l in self.lines if l.strip().endswith(':') and not l.strip().startswith(';')]),
            'function_count': len(self.get_functions()),
            'global_vars': len(self.get_global_variables())
        }

def compare_llvm_files(dolet_file, rust_file, output_file=None):
    """Compare two LLVM IR files in detail"""
    print(f"🔍 Detailed comparison: {dolet_file} vs {rust_file}")
    
    dolet_analyzer = LLVMAnalyzer(dolet_file)
    rust_analyzer = LLVMAnalyzer(rust_file)
    
    if not dolet_analyzer.content or not rust_analyzer.content:
        print("❌ Could not read one or both files")
        return
    
    # Complexity comparison
    dolet_complexity = dolet_analyzer.analyze_complexity()
    rust_complexity = rust_analyzer.analyze_complexity()
    
    print(f"\n📊 COMPLEXITY COMPARISON:")
    print(f"{'Metric':<20} {'Dolet':<10} {'Rust':<10} {'Ratio':<10}")
    print("-" * 55)
    
    for metric in dolet_complexity:
        dolet_val = dolet_complexity[metric]
        rust_val = rust_complexity[metric]
        ratio = f"{dolet_val/rust_val:.2f}" if rust_val > 0 else "N/A"
        print(f"{metric:<20} {dolet_val:<10} {rust_val:<10} {ratio:<10}")
    
    # Function comparison
    dolet_functions = dolet_analyzer.get_functions()
    rust_functions = rust_analyzer.get_functions()
    
    print(f"\n🔧 FUNCTION COMPARISON:")
    print(f"Dolet functions: {len(dolet_functions)}")
    print(f"Rust functions: {len(rust_functions)}")
    
    # Show function names
    dolet_func_names = [f['name'] for f in dolet_functions]
    rust_func_names = [f['name'] for f in rust_functions]
    
    print(f"Dolet function names: {dolet_func_names}")
    print(f"Rust function names: {rust_func_names}")
    
    # Instruction comparison
    dolet_instructions = dolet_analyzer.get_instruction_types()
    rust_instructions = rust_analyzer.get_instruction_types()
    
    print(f"\n⚙️  INSTRUCTION COMPARISON:")
    print(f"{'Instruction':<15} {'Dolet':<8} {'Rust':<8} {'Diff':<8}")
    print("-" * 45)
    
    for instr in dolet_instructions:
        dolet_count = dolet_instructions[instr]
        rust_count = rust_instructions[instr]
        diff = dolet_count - rust_count
        diff_str = f"{diff:+d}" if diff != 0 else "0"
        print(f"{instr:<15} {dolet_count:<8} {rust_count:<8} {diff_str:<8}")
    
    # String literals comparison
    dolet_strings = dolet_analyzer.get_string_literals()
    rust_strings = rust_analyzer.get_string_literals()
    
    print(f"\n📝 STRING LITERALS:")
    print(f"Dolet strings: {len(dolet_strings)}")
    print(f"Rust strings: {len(rust_strings)}")
    
    # Show first few strings
    if dolet_strings:
        print(f"Dolet examples: {dolet_strings[:3]}")
    if rust_strings:
        print(f"Rust examples: {rust_strings[:3]}")
    
    # Generate side-by-side diff if requested
    if output_file:
        generate_diff_report(dolet_analyzer, rust_analyzer, output_file)

def generate_diff_report(dolet_analyzer, rust_analyzer, output_file):
    """Generate detailed diff report"""
    print(f"📄 Generating detailed diff report: {output_file}")
    
    with open(output_file, 'w') as f:
        f.write("DOLET vs RUST LLVM IR COMPARISON REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        # File info
        f.write(f"Dolet file: {dolet_analyzer.file_path}\n")
        f.write(f"Rust file: {rust_analyzer.file_path}\n\n")
        
        # Side-by-side diff
        f.write("SIDE-BY-SIDE DIFF:\n")
        f.write("-" * 30 + "\n")
        
        diff = difflib.unified_diff(
            dolet_analyzer.lines,
            rust_analyzer.lines,
            fromfile="Dolet",
            tofile="Rust",
            lineterm=""
        )
        
        for line in diff:
            f.write(line + "\n")
        
        # Detailed analysis
        f.write("\n\nDETAILED ANALYSIS:\n")
        f.write("-" * 20 + "\n")
        
        dolet_complexity = dolet_analyzer.analyze_complexity()
        rust_complexity = rust_analyzer.analyze_complexity()
        
        f.write("Complexity Metrics:\n")
        for metric in dolet_complexity:
            f.write(f"  {metric}: Dolet={dolet_complexity[metric]}, Rust={rust_complexity[metric]}\n")

def main():
    print("🔬 LLVM IR DETAILED ANALYZER")
    print("=" * 40)
    
    # Find LLVM files to compare
    output_dir = Path("output")
    if not output_dir.exists():
        print("❌ Output directory not found. Run 'make compare' first.")
        return
    
    # Get all dolet and rust LLVM files
    dolet_files = list(output_dir.glob("dolet_*.ll"))
    rust_files = list(output_dir.glob("rust_*.ll"))
    
    if not dolet_files or not rust_files:
        print("❌ No LLVM files found. Run 'make compare' first.")
        return
    
    print(f"Found {len(dolet_files)} Dolet files and {len(rust_files)} Rust files")
    
    # Create analysis directory
    analysis_dir = Path("analysis")
    analysis_dir.mkdir(exist_ok=True)
    
    # Compare each pair
    for dolet_file in dolet_files:
        # Find corresponding rust file
        example_num = dolet_file.name.replace("dolet_", "").replace(".ll", "")
        rust_file = output_dir / f"rust_{example_num}.ll"
        
        if rust_file.exists():
            print(f"\n{'='*60}")
            compare_llvm_files(
                str(dolet_file), 
                str(rust_file),
                str(analysis_dir / f"comparison_{example_num}.txt")
            )
        else:
            print(f"❌ No corresponding Rust file for {dolet_file}")
    
    print(f"\n🎉 Analysis complete! Check the 'analysis/' directory for detailed reports.")

if __name__ == "__main__":
    main()
