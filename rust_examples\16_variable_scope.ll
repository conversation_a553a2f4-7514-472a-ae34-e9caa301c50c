; ModuleID = '16_variable_scope.be32ff110df2fd6-cgu.0'
source_filename = "16_variable_scope.be32ff110df2fd6-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }

@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h7123a331ea2fb5e7E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hcf36bfaf12934797E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hcf36bfaf12934797E" }>, align 8
@anon.21b9fe1916ccdbd19ce77edcbad2c0ea.0 = private unnamed_addr constant <{ [4 x i8], [4 x i8] }> <{ [4 x i8] zeroinitializer, [4 x i8] undef }>, align 4
@alloc_fad0cd83b7d1858a846a172eb260e593 = private unnamed_addr constant [42 x i8] c"is_aligned_to: align is not a power-of-two", align 1
@alloc_e92e94d0ff530782b571cfd99ec66aef = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fad0cd83b7d1858a846a172eb260e593, [8 x i8] c"*\00\00\00\00\00\00\00" }>, align 8
@anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_72de19bf38e62b85db2357042b61256e = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\C3\05\00\00\0D\00\00\00" }>, align 8
@alloc_bd3468a7b96187f70c1ce98a3e7a63bf = private unnamed_addr constant [283 x i8] c"unsafe precondition(s) violated: ptr::copy_nonoverlapping requires that both pointer arguments are aligned and non-null and the specified memory ranges do not overlap\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_db07ae5a9ce650d9b7cc970d048e6f0c = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_mul cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_2dff866d8f4414dd3e87cf8872473df8 = private unnamed_addr constant [227 x i8] c"unsafe precondition(s) violated: ptr::read_volatile requires that the pointer argument is aligned and non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_560a59ed819b9d9a5841f6e731c4c8e5 = private unnamed_addr constant [210 x i8] c"unsafe precondition(s) violated: NonNull::new_unchecked requires that the pointer is non-null\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_64e308ef4babfeb8b6220184de794a17 = private unnamed_addr constant [221 x i8] c"unsafe precondition(s) violated: hint::assert_unchecked must never be called when the condition is false\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_75fb06c2453febd814e73f5f2e72ae38 = private unnamed_addr constant [199 x i8] c"unsafe precondition(s) violated: hint::unreachable_unchecked must never be reached\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_9cd20c3e415f4d39f0ceb012cb758628 = private unnamed_addr constant [40 x i8] c"there is no such thing as a release load", align 1
@alloc_b55f69a00b865c3d8c5f6dad0122f5bd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_9cd20c3e415f4d39f0ceb012cb758628, [8 x i8] c"(\00\00\00\00\00\00\00" }>, align 8
@alloc_89ae956d4ea3e0150b08e694da2dc758 = private unnamed_addr constant [79 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\sync\\atomic.rs", align 1
@alloc_4a55fd50178bfdd3a757ab28109fa708 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_89ae956d4ea3e0150b08e694da2dc758, [16 x i8] c"O\00\00\00\00\00\00\00\9A\0E\00\00\18\00\00\00" }>, align 8
@alloc_96ab912d0054b46da785b206a96c9a45 = private unnamed_addr constant [49 x i8] c"there is no such thing as an acquire-release load", align 1
@alloc_0175cc81e1f5c8f5b757d44420d81e68 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_96ab912d0054b46da785b206a96c9a45, [8 x i8] c"1\00\00\00\00\00\00\00" }>, align 8
@alloc_82a6eea5780750969a484935b6fa420d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_89ae956d4ea3e0150b08e694da2dc758, [16 x i8] c"O\00\00\00\00\00\00\00\9B\0E\00\00\17\00\00\00" }>, align 8
@alloc_1be5ea12ba708d9a11b6e93a7d387a75 = private unnamed_addr constant [281 x i8] c"unsafe precondition(s) violated: Layout::from_size_align_unchecked requires that align is a power of 2 and the rounded-up allocation size does not exceed isize::MAX\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_7e91ad820dea7325849fe21a3cdb619c = private unnamed_addr constant [77 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ub_checks.rs", align 1
@alloc_3e3d0b2e0fa792e2b848e5abc8783d27 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_7e91ad820dea7325849fe21a3cdb619c, [16 x i8] c"M\00\00\00\00\00\00\00\86\00\00\006\00\00\00" }>, align 8
@alloc_a28e8c8fd5088943a8b5d44af697ff83 = private unnamed_addr constant [279 x i8] c"unsafe precondition(s) violated: slice::from_raw_parts requires the pointer to be aligned and non-null, and the total size of the slice not to exceed `isize::MAX`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_763310d78c99c2c1ad3f8a9821e942f3 = private unnamed_addr constant [61 x i8] c"is_nonoverlapping: `size_of::<T>() * count` overflows a usize", align 1
@__rust_no_alloc_shim_is_unstable = external global i8
@alloc_132cf3476a2e9457baecc94a242b588a = private unnamed_addr constant [74 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\alloc\\src\\slice.rs", align 1
@alloc_0ab120d992479c4cb1e1767c901c8927 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_132cf3476a2e9457baecc94a242b588a, [16 x i8] c"J\00\00\00\00\00\00\00\BE\01\00\00\1D\00\00\00" }>, align 8
@_ZN18_16_variable_scope14GLOBAL_COUNTER17h7370b5782adfe545E = internal global [4 x i8] zeroinitializer, align 4
@alloc_401511a70e0537fde0ee5a8ffcf8c8cf = private unnamed_addr constant [35 x i8] c"=== Variable Scope Demo (Rust) ===\0A", align 1
@alloc_2e122ca7024385f19d6e450b7e6ce334 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_401511a70e0537fde0ee5a8ffcf8c8cf, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ae0855c23ababcfe077724bbd7eeb6aa = private unnamed_addr constant [21 x i8] c"Global scope access:\0A", align 1
@alloc_e49b15e535f1e4588b410e976ff209ac = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ae0855c23ababcfe077724bbd7eeb6aa, [8 x i8] c"\15\00\00\00\00\00\00\00" }>, align 8
@alloc_e1a03da7b84dfa1a62a9a9acc01c2c4e = private unnamed_addr constant [18 x i8] c"GLOBAL_CONSTANT = ", align 1
@alloc_d04ae1e4e47e2edaacf420d40182da21 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e1a03da7b84dfa1a62a9a9acc01c2c4e, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_af995923a0f2cfedcdb19bb6362b5829 = private unnamed_addr constant [4 x i8] c"d\00\00\00", align 4
@alloc_1a70497d0b7fab210a80e5075146656d = private unnamed_addr constant [11 x i8] c"APP_NAME = ", align 1
@alloc_421f615f12d1b4228495ec26aaaf3a3b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1a70497d0b7fab210a80e5075146656d, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ed9eddd326092b2a23ac560f8f3eec33 = private unnamed_addr constant [10 x i8] c"Scope Demo", align 1
@alloc_8e083c548d5e7235d59b358a9d384b33 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ed9eddd326092b2a23ac560f8f3eec33, [8 x i8] c"\0A\00\00\00\00\00\00\00" }>, align 8
@alloc_9560c295a76f15a696250e3cd6680ba2 = private unnamed_addr constant [17 x i8] c"GLOBAL_COUNTER = ", align 1
@alloc_643e2b97687f8a57515123a227479277 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9560c295a76f15a696250e3cd6680ba2, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_79a31439c785530e507fcbdb6fcd3443 = private unnamed_addr constant [26 x i8] c"Function scope variables:\0A", align 1
@alloc_26c90648c8b9d75a8a20faae9e01ca00 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_79a31439c785530e507fcbdb6fcd3443, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_e9b689b9b1d81f0cb1d1fc7d7058b063 = private unnamed_addr constant [13 x i8] c"Main Function", align 1
@alloc_384ea2afd28c01408011f843ce6c3b27 = private unnamed_addr constant [11 x i8] c"main_var = ", align 1
@alloc_92a429fe4e38f1b486dd85808f707e98 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_384ea2afd28c01408011f843ce6c3b27, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7172bb24e96db0a99c31a75b5f5402d7 = private unnamed_addr constant [12 x i8] c"main_name = ", align 1
@alloc_78fbcc61e1453380b829f0c5144017e6 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7172bb24e96db0a99c31a75b5f5402d7, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5070f5a1e389ea5ba9034da967087b5d = private unnamed_addr constant [27 x i8] c"Block scope demonstration:\0A", align 1
@alloc_b5cc11a185758184293e41f368ac7947 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5070f5a1e389ea5ba9034da967087b5d, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_82c2c2fd10d71ee406d3e1eaa633f115 = private unnamed_addr constant [27 x i8] c"outer_var (before block) = ", align 1
@alloc_e7625a77f09c9d0e667d6c6294f339b3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_82c2c2fd10d71ee406d3e1eaa633f115, [8 x i8] c"\1B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9b549daa0167c8da1db0597819f3bc7c = private unnamed_addr constant [29 x i8] c"  inner_var (inside block) = ", align 1
@alloc_044884425a8194d8a6b615b3ebd27f70 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9b549daa0167c8da1db0597819f3bc7c, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e19b42411ba7dfa399767dc04d6708e5 = private unnamed_addr constant [39 x i8] c"  outer_var (inside block, shadowed) = ", align 1
@alloc_11c05c4e6e8267671992c539c92cf6ee = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e19b42411ba7dfa399767dc04d6708e5, [8 x i8] c"'\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a572d26ba3166a4abf6635ae94f8f486 = private unnamed_addr constant [28 x i8] c"    deep_var (deep block) = ", align 1
@alloc_67c894db437aff573c27f8a3ddf8b1ed = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a572d26ba3166a4abf6635ae94f8f486, [8 x i8] c"\1C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2c9d04f306f43ae4295947fa49f8aa1d = private unnamed_addr constant [39 x i8] c"    inner_var (deep block, shadowed) = ", align 1
@alloc_6553075b7b0ba76ec311e19aa09bd046 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2c9d04f306f43ae4295947fa49f8aa1d, [8 x i8] c"'\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1ce6e8f31e1f6356a66efcf944f6f613 = private unnamed_addr constant [36 x i8] c"  inner_var (back to block level) = ", align 1
@alloc_a75039ce18484cd18938e750c1bde565 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1ce6e8f31e1f6356a66efcf944f6f613, [8 x i8] c"$\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c38664f12b300c3a9cd8f0eef2111088 = private unnamed_addr constant [26 x i8] c"outer_var (after block) = ", align 1
@alloc_104198c2ddf9d0915b6174eb0a52b822 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c38664f12b300c3a9cd8f0eef2111088, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e820f695a41d3a5a25f1c88f332040ea = private unnamed_addr constant [26 x i8] c"Function calls and scope:\0A", align 1
@alloc_fc3e0a4aa6481b0a327bfd8c7e8df86f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_e820f695a41d3a5a25f1c88f332040ea, [8 x i8] c"\1A\00\00\00\00\00\00\00" }>, align 8
@alloc_21dabbd2bf343190e576066ed195b812 = private unnamed_addr constant [30 x i8] c"function_with_local_vars(5) = ", align 1
@alloc_f64ab8344aaa5cfd4a9e2e5ea1fbc342 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_21dabbd2bf343190e576066ed195b812, [8 x i8] c"\1E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5104e20048134829ded8c47089a983f1 = private unnamed_addr constant [35 x i8] c"function_with_parameters(10, 15) = ", align 1
@alloc_2a6d1c634f33869256db46f662bba0d4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5104e20048134829ded8c47089a983f1, [8 x i8] c"#\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ee657066134f5cce2450f6e60b7e53ec = private unnamed_addr constant [20 x i8] c"Variable shadowing:\0A", align 1
@alloc_8017d36ac04f36868653653404ba0c00 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ee657066134f5cce2450f6e60b7e53ec, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_7c24293f9117b5d42b5ee1ac0bb5f621 = private unnamed_addr constant [14 x i8] c"x (initial) = ", align 1
@alloc_32f86cc4dc7437e85985d529699c7200 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7c24293f9117b5d42b5ee1ac0bb5f621, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2b9d07d06f338d2c41ddca4b9c5036f6 = private unnamed_addr constant [20 x i8] c"16_variable_scope.rs", align 1
@alloc_f2d52a20d5c7f2495a1964fa80bdf04f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00C\00\00\00\0D\00\00\00" }>, align 8
@alloc_72c57de1ed1a5dcd8358f7a7d05b833b = private unnamed_addr constant [33 x i8] c"x (after shadowing with x + 1) = ", align 1
@alloc_b50235274a3a1231833366a232f2b3ea = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_72c57de1ed1a5dcd8358f7a7d05b833b, [8 x i8] c"!\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4d4e7212953f55af617663444ecc4cf8 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00F\00\00\00\0D\00\00\00" }>, align 8
@alloc_623f02aa0fc385622a17a47b8d433009 = private unnamed_addr constant [33 x i8] c"x (after shadowing with x * 2) = ", align 1
@alloc_d231c6678e007c7c3d19c38a4b850aa6 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_623f02aa0fc385622a17a47b8d433009, [8 x i8] c"!\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3edef0b68cfa9c8c95e6d4fe1a68842b = private unnamed_addr constant [5 x i8] c"Hello", align 1
@alloc_b142ed0aee0c98a62e7e190f244753ae = private unnamed_addr constant [37 x i8] c"  x (inside block, different type) = ", align 1
@alloc_b924672bd40e7732a3f940ec48f7c8e0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b142ed0aee0c98a62e7e190f244753ae, [8 x i8] c"%\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c080f2da1577c674e491e383b9ffd81c = private unnamed_addr constant [26 x i8] c"x (back to outer scope) = ", align 1
@alloc_4b5c98f361b708459fc443066acc30bc = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c080f2da1577c674e491e383b9ffd81c, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_166451ac38e0827cbbd7c2402bb6fb75 = private unnamed_addr constant [12 x i8] c"Loop scope:\0A", align 1
@alloc_f58c61cfaa81d4f44a13c3399ecfa0c9 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_166451ac38e0827cbbd7c2402bb6fb75, [8 x i8] c"\0C\00\00\00\00\00\00\00" }>, align 8
@alloc_fa44e2825d27622e8a7efe86bd8aa49e = private unnamed_addr constant [18 x i8] c"While loop scope:\0A", align 1
@alloc_2b363f8247b644341e7dcd35f32ca446 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_fa44e2825d27622e8a7efe86bd8aa49e, [8 x i8] c"\12\00\00\00\00\00\00\00" }>, align 8
@alloc_70770b393d95994e2e90c93d2b59f966 = private unnamed_addr constant [29 x i8] c"counter (after while loop) = ", align 1
@alloc_bd2b581d13fa2f7e68794a0ffb6584e1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_70770b393d95994e2e90c93d2b59f966, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_b1a377bf0f744f90479be074f799808f = private unnamed_addr constant [20 x i8] c"If statement scope:\0A", align 1
@alloc_495687b9bdba10c7956e5ad791751577 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b1a377bf0f744f90479be074f799808f, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_564123154e32e47616d56430ec26b5b5 = private unnamed_addr constant [23 x i8] c"  if_var (inside if) = ", align 1
@alloc_48ae43122f80692181a21e07b4480978 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_564123154e32e47616d56430ec26b5b5, [8 x i8] c"\17\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e0a9f64f9f48192b52592886f3e77214 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00s\00\00\00!\00\00\00" }>, align 8
@alloc_d1701a5fb61cc53b0ba1be09d9fa0a74 = private unnamed_addr constant [20 x i8] c"    nested_if_var = ", align 1
@alloc_72ae7a3d4720b5560259dd0d0a2c98e4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d1701a5fb61cc53b0ba1be09d9fa0a74, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5d62156b9720e26af63134db29def476 = private unnamed_addr constant [13 x i8] c"Match scope:\0A", align 1
@alloc_5460c416d36a15b165ebebe939b46e1a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5d62156b9720e26af63134db29def476, [8 x i8] c"\0D\00\00\00\00\00\00\00" }>, align 8
@alloc_79b094f2859b927a299be2ca51031e58 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\8B\00\00\00\1D\00\00\00" }>, align 8
@alloc_73dd3d7e7bd703ca781b17ea076105b8 = private unnamed_addr constant [27 x i8] c"  Large value: large_var = ", align 1
@alloc_8650e67de762b18d7022da51805c5671 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_73dd3d7e7bd703ca781b17ea076105b8, [8 x i8] c"\1B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6be91fbe26d7970cc722c77869a3f1d2 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\87\00\00\00\1E\00\00\00" }>, align 8
@alloc_151c8aa471a226ddda0acbe32f2d4e49 = private unnamed_addr constant [29 x i8] c"  Medium value: medium_var = ", align 1
@alloc_6572ab024c22f78f49eba77b6aeb0a7b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_151c8aa471a226ddda0acbe32f2d4e49, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_12f88efad98258d7e5c9c8c86ff95e1c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\83\00\00\00\1D\00\00\00" }>, align 8
@alloc_2c7110f50ec332d6e8cc83eb4a6ed981 = private unnamed_addr constant [27 x i8] c"  Small value: small_var = ", align 1
@alloc_1d77c2437e7de12f8c6161720a6ac2f2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2c7110f50ec332d6e8cc83eb4a6ed981, [8 x i8] c"\1B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_815fc4f3487fa47ecb3494397e458289 = private unnamed_addr constant [29 x i8] c"Mutable variables and scope:\0A", align 1
@alloc_db123cd1c347d8d8c8cebb462a2a3bc7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_815fc4f3487fa47ecb3494397e458289, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_edfcd537e352fc2ae14d50ec77486823 = private unnamed_addr constant [24 x i8] c"mutable_var (initial) = ", align 1
@alloc_65264418fffcdcb55b3dac18118923ee = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_edfcd537e352fc2ae14d50ec77486823, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_62006209cf2f4f87384b2650f309bf2b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\98\00\00\00\09\00\00\00" }>, align 8
@alloc_c3ac27bf7e76abe91ac3e3b55119f9df = private unnamed_addr constant [36 x i8] c"  mutable_var (modified in block) = ", align 1
@alloc_a1296dda46b749b243acfda47e4a0f6a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c3ac27bf7e76abe91ac3e3b55119f9df, [8 x i8] c"$\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e8552f3c3663fcc54c3ac1ebcd9a2bca = private unnamed_addr constant [18 x i8] c"  local_mutable = ", align 1
@alloc_f444c128805b5b4f2959d5a8ceb6ccb7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e8552f3c3663fcc54c3ac1ebcd9a2bca, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d3fedfb92b46d646f06d31ac2caba070 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\9D\00\00\00\09\00\00\00" }>, align 8
@alloc_c95a3212f5fe865778dbdefb52cf3c80 = private unnamed_addr constant [39 x i8] c"  local_mutable (after modification) = ", align 1
@alloc_47fd14502feda2184f1c91a89b0b0573 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c95a3212f5fe865778dbdefb52cf3c80, [8 x i8] c"'\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f92efacb1214a6f0356d80a4506a04dd = private unnamed_addr constant [28 x i8] c"mutable_var (after block) = ", align 1
@alloc_64f40a2d459f3f3a399c67d559f58634 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f92efacb1214a6f0356d80a4506a04dd, [8 x i8] c"\1C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_55950e645f3c0b57d7dc22c415cabf9a = private unnamed_addr constant [28 x i8] c"Function scope interaction:\0A", align 1
@alloc_cf383e772beb995630a83ded4b7769ba = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_55950e645f3c0b57d7dc22c415cabf9a, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_8ef65a83c3e7a4b487210a7f65c07ad9 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\AA\00\00\000\00\00\00" }>, align 8
@alloc_f1d42e82718ac6aa6456cd9052a82204 = private unnamed_addr constant [28 x i8] c"access_global_and_parameter(", align 1
@alloc_12470cffb0ed9f3cc12c6066ef64d57b = private unnamed_addr constant [4 x i8] c") = ", align 1
@alloc_f7583f65710ab9b8dbd8c5a922bee132 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f1d42e82718ac6aa6456cd9052a82204, [8 x i8] c"\1C\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d3920e37781cec4fd0d354cb317c4e6a = private unnamed_addr constant [26 x i8] c"modify_global_counter() = ", align 1
@alloc_b3fbc5a35a6df3c84b7adcd1c6f3f0d8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d3920e37781cec4fd0d354cb317c4e6a, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ac16e988bd464ba4b0fa6ebac798af23 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\AE\00\00\006\00\00\00" }>, align 8
@alloc_82e4a21a967d30d61fc0e3564331f76d = private unnamed_addr constant [15 x i8] c"Closure scope:\0A", align 1
@alloc_9f5649251a01542903117518f075105f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_82e4a21a967d30d61fc0e3564331f76d, [8 x i8] c"\0F\00\00\00\00\00\00\00" }>, align 8
@alloc_f5f47db1f84398d15fe57eb04aaa6036 = private unnamed_addr constant [16 x i8] c"Closure result: ", align 1
@alloc_baa63513cf120e966e81823cafda7c05 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f5f47db1f84398d15fe57eb04aaa6036, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a5f0883a4a72c5d0ff6c9af0fb53d127 = private unnamed_addr constant [23 x i8] c"Nested function scope:\0A", align 1
@alloc_d75c11794d48fe796a645a4ca286c5ed = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_a5f0883a4a72c5d0ff6c9af0fb53d127, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_f2888e8649178d15ee24b2cd24846870 = private unnamed_addr constant [24 x i8] c"Nested function result: ", align 1
@alloc_7e8499fe8323416714e153409bccaa2f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f2888e8649178d15ee24b2cd24846870, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ea8c627f36ebfa4f164640b4c983a523 = private unnamed_addr constant [27 x i8] c"Scope with error handling:\0A", align 1
@alloc_dc054de15f2b15832c0c966fc763122d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_ea8c627f36ebfa4f164640b4c983a523, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_d503103f6a201686f8c3db070fc15723 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\DB\00\00\00\1F\00\00\00" }>, align 8
@alloc_8d47a391153e81e0c10069eb6c542c41 = private unnamed_addr constant [32 x i8] c"  Division successful: result = ", align 1
@alloc_e963467ca430b618ecc312a5cc228b4f = private unnamed_addr constant [16 x i8] c", success_var = ", align 1
@alloc_29dedfaa3cf0bd649f425feb3aff04f9 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8d47a391153e81e0c10069eb6c542c41, [8 x i8] c" \00\00\00\00\00\00\00", ptr @alloc_e963467ca430b618ecc312a5cc228b4f, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0db46ac119ababfa0e6ea23184ef2ad8 = private unnamed_addr constant [15 x i8] c"Division failed", align 1
@alloc_59c71af4bd379be59a91f62b76cb7814 = private unnamed_addr constant [18 x i8] c"  Division error: ", align 1
@alloc_3c66ad895aed05cd2e1e4f0ff4581592 = private unnamed_addr constant [14 x i8] c", error_var = ", align 1
@alloc_f9b4a68bc48823bd6b3fc0cf850db76f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_59c71af4bd379be59a91f62b76cb7814, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_3c66ad895aed05cd2e1e4f0ff4581592, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c62199e2c56be01dcb6bc22f70b78b4b = private unnamed_addr constant [35 x i8] c"=== End of Variable Scope Demo ===\0A", align 1
@alloc_231ceedba1372284a0eca5b18f7db3f0 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c62199e2c56be01dcb6bc22f70b78b4b, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_19a26a844a0ac08110f5d3f6e596a55c = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00c\00\00\00\19\00\00\00" }>, align 8
@alloc_c68e18e7e8a662ec15642e2150929169 = private unnamed_addr constant [18 x i8] c"  While iteration ", align 1
@alloc_71f40af41e09b6845b5a2f3fa2f9b9d5 = private unnamed_addr constant [14 x i8] c": while_var = ", align 1
@alloc_cec7cd3e67e359fe0f057aaf267a9c78 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c68e18e7e8a662ec15642e2150929169, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_71f40af41e09b6845b5a2f3fa2f9b9d5, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_47c169422acced6c014f57b063551b57 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00e\00\00\00\09\00\00\00" }>, align 8
@alloc_24e97311129e8ba12f56b71650076da3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00S\00\00\00\18\00\00\00" }>, align 8
@alloc_680801b01cce38ce0b44481e45e606eb = private unnamed_addr constant [17 x i8] c"  Loop iteration ", align 1
@alloc_3f44695b1c2dcc2812a20040419b0a3a = private unnamed_addr constant [13 x i8] c": loop_var = ", align 1
@alloc_f4a1b6961393d6a04bde3a9449e0d8e3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_680801b01cce38ce0b44481e45e606eb, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_3f44695b1c2dcc2812a20040419b0a3a, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a1e6bc2caffef4f8bd3b49a23710dd67 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00W\00\00\00#\00\00\00" }>, align 8
@alloc_0f1c3e161ace4fdefac22264a7ee2e49 = private unnamed_addr constant [31 x i8] c"    conditional_var (i == 2) = ", align 1
@alloc_6dfc576ad2b11adcf5128eb10b826c36 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0f1c3e161ace4fdefac22264a7ee2e49, [8 x i8] c"\1F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_69732dc399a8ac6848aefe7630630d77 = private unnamed_addr constant [22 x i8] c"  Inside closure: x = ", align 1
@alloc_813b42af252348b58182f306d331470c = private unnamed_addr constant [18 x i8] c", closure_inner = ", align 1
@alloc_99c5542979e1612957ac6d3f26c9c3f9 = private unnamed_addr constant [18 x i8] c", closure_outer = ", align 1
@alloc_7281ac873afddb5061cdf48d23c109b3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_69732dc399a8ac6848aefe7630630d77, [8 x i8] c"\16\00\00\00\00\00\00\00", ptr @alloc_813b42af252348b58182f306d331470c, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_99c5542979e1612957ac6d3f26c9c3f9, [8 x i8] c"\12\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4c14e15fa9df4b9db337f6ec8a01604b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\B9\00\00\00\09\00\00\00" }>, align 8
@alloc_5182fb3fa7fa294ffdd2f989c8f2ca3d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\C5\00\00\00\1B\00\00\00" }>, align 8
@alloc_4bf81123f7924ecca49167bbd8f03d34 = private unnamed_addr constant [26 x i8] c"  outer_function: param = ", align 1
@alloc_a871179b01fcab744823cbff2b7107d8 = private unnamed_addr constant [16 x i8] c", outer_local = ", align 1
@alloc_a1599ad4d3f94d40542ab150a9fbb80e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_4bf81123f7924ecca49167bbd8f03d34, [8 x i8] c"\1A\00\00\00\00\00\00\00", ptr @alloc_a871179b01fcab744823cbff2b7107d8, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f843b5cbe670262f421cc94369ab56f1 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\CF\00\00\00\09\00\00\00" }>, align 8
@alloc_ae18c16c08f4b3703a75cb29d805d1ae = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\C9\00\00\00\1F\00\00\00" }>, align 8
@alloc_63d8de23b3463eb1166f0d65dc29d968 = private unnamed_addr constant [34 x i8] c"    inner_function: inner_param = ", align 1
@alloc_9b816be076feab6aa0724d1277da29f8 = private unnamed_addr constant [16 x i8] c", inner_local = ", align 1
@alloc_127a87760a1ac6cdaf6995817661e020 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_63d8de23b3463eb1166f0d65dc29d968, [8 x i8] c"\22\00\00\00\00\00\00\00", ptr @alloc_9b816be076feab6aa0724d1277da29f8, [8 x i8] c"\10\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_16bc2a35c706e93ceba629b350c47248 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\EA\00\00\00\16\00\00\00" }>, align 8
@alloc_aa1cb33f8bd119ea10d1cd3d936f3463 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\EB\00\00\00\16\00\00\00" }>, align 8
@alloc_8aba21f5f9abb56434838c21ff0883ba = private unnamed_addr constant [35 x i8] c"  Inside function_with_local_vars:\0A", align 1
@alloc_d54a9f1f31c07bb640970b94963adb39 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_8aba21f5f9abb56434838c21ff0883ba, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_89aeac3da8c827a8d72595097985c7d9 = private unnamed_addr constant [12 x i8] c"    param = ", align 1
@alloc_6830c67c40fb122b82219f7e86722bd8 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_89aeac3da8c827a8d72595097985c7d9, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c4bd0c6d77a453a54be12908261e3a51 = private unnamed_addr constant [17 x i8] c"    local_var1 = ", align 1
@alloc_08ba367b298d1f91fc1a9cf26810d275 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c4bd0c6d77a453a54be12908261e3a51, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8a308a5f75404137d399eb657f0978ce = private unnamed_addr constant [17 x i8] c"    local_var2 = ", align 1
@alloc_6687d5239236c2b90fed0f91c77ad493 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8a308a5f75404137d399eb657f0978ce, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c99eaa54d447884d978e8d1badd8c11d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\F6\00\00\00\0F\00\00\00" }>, align 8
@alloc_563b4d6416806b8cfe50e256fe8438b3 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\F7\00\00\00\13\00\00\00" }>, align 8
@alloc_c1980518ef99684c05dfdab208279675 = private unnamed_addr constant [35 x i8] c"  Inside function_with_parameters:\0A", align 1
@alloc_125a75e3a3a9823341a5ac87b13afa33 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c1980518ef99684c05dfdab208279675, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_77c4d820bac666c927d98fe6ce54f42b = private unnamed_addr constant [8 x i8] c"    a = ", align 1
@alloc_b7f5616537b55296b7d87860d9533cd5 = private unnamed_addr constant [6 x i8] c", b = ", align 1
@alloc_b36563b4b0c9a7dd6f070a518d7edcd0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_77c4d820bac666c927d98fe6ce54f42b, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_b7f5616537b55296b7d87860d9533cd5, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_782c6e221b538e3c7090ce8fa73f649b = private unnamed_addr constant [10 x i8] c"    sum = ", align 1
@alloc_1c28ce256d94e8c7815ed97f8011b647 = private unnamed_addr constant [12 x i8] c", product = ", align 1
@alloc_22fab347294c4d4ebab351bd77ae584d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_782c6e221b538e3c7090ce8fa73f649b, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_1c28ce256d94e8c7815ed97f8011b647, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a763594125e62e97b582eb09f68d0df2 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\FD\00\00\00\05\00\00\00" }>, align 8
@alloc_90bf980578a1501c0a0dd96771734985 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\01\01\00\00\16\00\00\00" }>, align 8
@alloc_286d12d11df5b49c55495558dddde5ee = private unnamed_addr constant [39 x i8] c"  access_global_and_parameter: param = ", align 1
@alloc_03ea1f35fef0d9aaa5fd68ab0ca4a4c7 = private unnamed_addr constant [20 x i8] c", GLOBAL_CONSTANT = ", align 1
@alloc_7cb27b1bd7357c09cf936ddf64760f1c = private unnamed_addr constant [11 x i8] c", result = ", align 1
@alloc_baccd27eceea2c32120da11051221e55 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_286d12d11df5b49c55495558dddde5ee, [8 x i8] c"'\00\00\00\00\00\00\00", ptr @alloc_03ea1f35fef0d9aaa5fd68ab0ca4a4c7, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_7cb27b1bd7357c09cf936ddf64760f1c, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_063ad75f0f8763261f5a47eb05ae5f15 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\09\01\00\00\15\00\00\00" }>, align 8
@alloc_936cb0c6137d446cfeee7d55a5e9259e = private unnamed_addr constant [31 x i8] c"  modify_global_counter: old = ", align 1
@alloc_6e1f52e8c46988d285cdc381911a708e = private unnamed_addr constant [8 x i8] c", new = ", align 1
@alloc_66c7b562be227870196bdea9e506c470 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_936cb0c6137d446cfeee7d55a5e9259e, [8 x i8] c"\1F\00\00\00\00\00\00\00", ptr @alloc_6e1f52e8c46988d285cdc381911a708e, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_36712b18fa80a356639965f9856729fb = private unnamed_addr constant [16 x i8] c"Division by zero", align 1
@alloc_64869a62148fb34466b31a6fe6cb3cf2 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_2b9d07d06f338d2c41ddca4b9c5036f6, [16 x i8] c"\14\00\00\00\00\00\00\00\12\01\00\00\0C\00\00\00" }>, align 8

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h587acec47a285d28E"(ptr align 4 %self) unnamed_addr #0 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp sle i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp slt i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <i32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hac4c0d6b91a9e6f4E"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h862eb16398512ee4E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #1 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hcf36bfaf12934797E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h5f5414d8ee0298bcE(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7b51e1130b98dbe6E"()
  ret i32 %self
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17h5f5414d8ee0298bcE(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h0ab7166e4e47997aE(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17hec53723dc2ee1c32E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #1 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <T as alloc::string::ToString>::to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17h463765ffe6c6a0d9E"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
; call <str as alloc::string::SpecToString>::spec_to_string
  call void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17hdc0543481924cd8bE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1)
  ret void
}

; <i32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$i32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17hac4c0d6b91a9e6f4E"(i32 %start1, i64 %n) unnamed_addr #0 {
start:
  %self = alloca [8 x i8], align 4
  %rhs = trunc i64 %n to i32
  %0 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %start1, i32 %rhs)
  %_10.0 = extractvalue { i32, i1 } %0, 0
  %_10.1 = extractvalue { i32, i1 } %0, 1
  %_7 = icmp slt i32 %rhs, 0
  %b = xor i1 %_10.1, %_7
  br i1 %b, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %_10.0, ptr %1, align 4
  store i32 1, ptr %self, align 4
  %2 = getelementptr inbounds i8, ptr %self, i64 4
  %val = load i32, ptr %2, align 4
  ret i32 %val

bb1:                                              ; preds = %start
  %3 = load i32, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.0, align 4
  %4 = load i32, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.0, i64 4), align 4
  store i32 %3, ptr %self, align 4
  %5 = getelementptr inbounds i8, ptr %self, i64 4
  store i32 %4, ptr %5, align 4
; call core::hint::unreachable_unchecked::precondition_check
  call void @_ZN4core4hint21unreachable_unchecked18precondition_check17h3dafc371609b49bdE() #15
  unreachable
}

; core::intrinsics::copy_nonoverlapping::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h49da6cadd49f63aeE(ptr %src, ptr %dst, i64 %size, i64 %align, i64 %count) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_26 = alloca [48 x i8], align 8
  %_21 = alloca [4 x i8], align 4
  %_20 = alloca [8 x i8], align 8
  %_19 = alloca [8 x i8], align 8
  %_18 = alloca [8 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %is_zst = alloca [1 x i8], align 1
  %align1 = alloca [8 x i8], align 8
  %zero_size = alloca [1 x i8], align 1
  %1 = icmp eq i64 %count, 0
  br i1 %1, label %bb1, label %bb2

bb1:                                              ; preds = %start
  store i8 1, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %2 = load i8, ptr %zero_size, align 1
  %3 = trunc nuw i8 %2 to i1
  %4 = zext i1 %3 to i8
  store i8 %4, ptr %is_zst, align 1
  %5 = call i64 @llvm.ctpop.i64(i64 %align)
  %6 = trunc i64 %5 to i32
  store i32 %6, ptr %_21, align 4
  %7 = load i32, ptr %_21, align 4
  %8 = icmp eq i32 %7, 1
  br i1 %8, label %bb26, label %bb15

bb2:                                              ; preds = %start
  %9 = icmp eq i64 %size, 0
  %10 = zext i1 %9 to i8
  store i8 %10, ptr %zero_size, align 1
  store i64 %align, ptr %align1, align 8
  %11 = load i8, ptr %zero_size, align 1
  %12 = trunc nuw i8 %11 to i1
  %13 = zext i1 %12 to i8
  store i8 %13, ptr %is_zst, align 1
  %14 = call i64 @llvm.ctpop.i64(i64 %align)
  %15 = trunc i64 %14 to i32
  store i32 %15, ptr %_21, align 4
  %16 = load i32, ptr %_21, align 4
  %17 = icmp eq i32 %16, 1
  br i1 %17, label %bb14, label %bb15

bb26:                                             ; preds = %bb1
  %18 = ptrtoint ptr %src to i64
  store i64 %18, ptr %_19, align 8
  %19 = sub i64 %align, 1
  store i64 %19, ptr %_20, align 8
  %20 = load i64, ptr %_19, align 8
  %21 = load i64, ptr %_20, align 8
  %22 = and i64 %20, %21
  store i64 %22, ptr %_18, align 8
  %23 = load i64, ptr %_18, align 8
  %24 = icmp eq i64 %23, 0
  br i1 %24, label %bb27, label %bb11

bb15:                                             ; preds = %bb2, %bb1
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_17, align 8
  %25 = getelementptr inbounds i8, ptr %_17, i64 8
  store i64 1, ptr %25, align 8
  %26 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %27 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %28 = getelementptr inbounds i8, ptr %_17, i64 32
  store ptr %26, ptr %28, align 8
  %29 = getelementptr inbounds i8, ptr %28, i64 8
  store i64 %27, ptr %29, align 8
  %30 = getelementptr inbounds i8, ptr %_17, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %30, align 8
  %31 = getelementptr inbounds i8, ptr %30, i64 8
  store i64 0, ptr %31, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_17, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb27:                                             ; preds = %bb26
  br label %bb12

bb11:                                             ; preds = %bb14, %bb26
  br label %bb6

bb12:                                             ; preds = %bb10, %bb27
  br label %bb3

bb14:                                             ; preds = %bb2
  %32 = ptrtoint ptr %src to i64
  store i64 %32, ptr %_19, align 8
  %33 = sub i64 %align, 1
  store i64 %33, ptr %_20, align 8
  %34 = load i64, ptr %_19, align 8
  %35 = load i64, ptr %_20, align 8
  %36 = and i64 %34, %35
  store i64 %36, ptr %_18, align 8
  %37 = load i64, ptr %_18, align 8
  %38 = icmp eq i64 %37, 0
  br i1 %38, label %bb10, label %bb11

bb10:                                             ; preds = %bb14
  %39 = load i8, ptr %is_zst, align 1
  %40 = trunc nuw i8 %39 to i1
  br i1 %40, label %bb12, label %bb13

bb13:                                             ; preds = %bb10
  %41 = load i64, ptr %_19, align 8
  %_15 = icmp eq i64 %41, 0
  %_8 = xor i1 %_15, true
  br i1 %_8, label %bb3, label %bb6

bb6:                                              ; preds = %bb11, %bb13
  br label %bb7

bb3:                                              ; preds = %bb12, %bb13
  %42 = load i8, ptr %zero_size, align 1
  %is_zst2 = trunc nuw i8 %42 to i1
  %43 = call i64 @llvm.ctpop.i64(i64 %align)
  %44 = trunc i64 %43 to i32
  store i32 %44, ptr %0, align 4
  %_29 = load i32, ptr %0, align 4
  %45 = icmp eq i32 %_29, 1
  br i1 %45, label %bb21, label %bb22

bb21:                                             ; preds = %bb3
  %_28 = ptrtoint ptr %dst to i64
  %46 = load i64, ptr %_20, align 8
  %_27 = and i64 %_28, %46
  %47 = icmp eq i64 %_27, 0
  br i1 %47, label %bb17, label %bb18

bb22:                                             ; preds = %bb3
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_26, align 8
  %48 = getelementptr inbounds i8, ptr %_26, i64 8
  store i64 1, ptr %48, align 8
  %49 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %50 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %51 = getelementptr inbounds i8, ptr %_26, i64 32
  store ptr %49, ptr %51, align 8
  %52 = getelementptr inbounds i8, ptr %51, i64 8
  store i64 %50, ptr %52, align 8
  %53 = getelementptr inbounds i8, ptr %_26, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %53, align 8
  %54 = getelementptr inbounds i8, ptr %53, i64 8
  store i64 0, ptr %54, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_26, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb17:                                             ; preds = %bb21
  br i1 %is_zst2, label %bb19, label %bb20

bb18:                                             ; preds = %bb21
  br label %bb5

bb20:                                             ; preds = %bb17
  %_24 = icmp eq i64 %_28, 0
  %_11 = xor i1 %_24, true
  br i1 %_11, label %bb4, label %bb5

bb19:                                             ; preds = %bb17
  br label %bb4

bb5:                                              ; preds = %bb18, %bb20
  br label %bb7

bb4:                                              ; preds = %bb19, %bb20
; invoke core::ub_checks::maybe_is_nonoverlapping::runtime
  %_6 = invoke zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17hd39347e0fb243828E(ptr %src, ptr %dst, i64 %size, i64 %count)
          to label %bb24 unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb15, %bb22, %bb4
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

bb24:                                             ; preds = %bb4
  br i1 %_6, label %bb9, label %bb8

bb8:                                              ; preds = %bb7, %bb24
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_bd3468a7b96187f70c1ce98a3e7a63bf, i64 283) #18
  unreachable

bb9:                                              ; preds = %bb24
  ret void

bb7:                                              ; preds = %bb6, %bb5
  br label %bb8

unreachable:                                      ; preds = %bb15, %bb22
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17haa2dcb8c20a48bb6E() unnamed_addr #4 {
start:
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h14190f8e616dc727E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hd12e4145c23fdc5aE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_0, ptr align 4 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17hf3d6b28e3bfa00e3E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17hec53723dc2ee1c32E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h006a2f0a25b8566bE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #0 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl usize>::unchecked_mul::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h88b32a1badac6a96E"(i64 %lhs, i64 %rhs) unnamed_addr #3 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_db07ae5a9ce650d9b7cc970d048e6f0c, i64 186) #18
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17ha8939ddc1d597df1E"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #0 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h7123a331ea2fb5e7E"(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h661c82e39780763bE(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h0ab7166e4e47997aE(ptr %_1) unnamed_addr #0 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h661c82e39780763bE(ptr %0) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17hcf36bfaf12934797E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ptr::read_volatile::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core3ptr13read_volatile18precondition_check17hd9221164d7f70239E(ptr %addr, i64 %align, i1 zeroext %is_zst) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %_8 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_12 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_12, 1
  br i1 %3, label %bb7, label %bb8

bb7:                                              ; preds = %start
  %_10 = ptrtoint ptr %addr to i64
  %_11 = sub i64 %align, 1
  %_9 = and i64 %_10, %_11
  %4 = icmp eq i64 %_9, 0
  br i1 %4, label %bb3, label %bb4

bb8:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_8, align 8
  %5 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_8, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_8, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_8, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb3:                                              ; preds = %bb7
  br i1 %is_zst, label %bb5, label %bb6

bb4:                                              ; preds = %bb7
  br label %bb2

bb6:                                              ; preds = %bb3
  %_6 = icmp eq i64 %_10, 0
  %_4 = xor i1 %_6, true
  br i1 %_4, label %bb1, label %bb2

bb5:                                              ; preds = %bb3
  br label %bb1

bb2:                                              ; preds = %bb4, %bb6
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_2dff866d8f4414dd3e87cf8872473df8, i64 227) #18
  unreachable

bb1:                                              ; preds = %bb5, %bb6
  ret void

cs_terminate:                                     ; preds = %bb8
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb8
  unreachable
}

; core::ptr::drop_in_place<alloc::string::String>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hbd10d7b9ac0f779dE"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call core::ptr::drop_in_place<alloc::vec::Vec<u8>>
  call void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17he90fc6d8517d4d88E"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::vec::Vec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17he90fc6d8517d4d88E"(ptr align 8 %_1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
  invoke void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h858bdf03d665bb86E"(ptr align 8 %_1)
          to label %bb4 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h64a955024842a05aE"(ptr align 8 %_1) #19 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb4:                                              ; preds = %start
; call core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
  call void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h64a955024842a05aE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<alloc::raw_vec::RawVec<u8>>
; Function Attrs: uwtable
define internal void @"_ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h64a955024842a05aE"(ptr align 8 %_1) unnamed_addr #1 {
start:
; call <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
  call void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2db5da15482b7a0fE"(ptr align 8 %_1)
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17he0d5ec683acf5974E"(ptr align 8 %_1) unnamed_addr #0 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h443925849fa68ee6E"(ptr %ptr) unnamed_addr #3 {
start:
  %_3 = ptrtoint ptr %ptr to i64
  %0 = icmp eq i64 %_3, 0
  br i1 %0, label %bb1, label %bb2

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_560a59ed819b9d9a5841f6e731c4c8e5, i64 210) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::assert_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint16assert_unchecked18precondition_check17hebad5c52021b0021E(i1 zeroext %cond) unnamed_addr #3 {
start:
  br i1 %cond, label %bb2, label %bb1

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_64e308ef4babfeb8b6220184de794a17, i64 221) #18
  unreachable

bb2:                                              ; preds = %start
  ret void
}

; core::hint::unreachable_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core4hint21unreachable_unchecked18precondition_check17h3dafc371609b49bdE() unnamed_addr #3 {
start:
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_75fb06c2453febd814e73f5f2e72ae38, i64 199) #18
  unreachable
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hd96b0c94b4bdb72bE"(ptr align 4 %self) unnamed_addr #0 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h587acec47a285d28E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::sync::atomic::atomic_load
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core4sync6atomic11atomic_load17h2c13f6cd19c4e096E(ptr %dst, i8 %order) unnamed_addr #0 {
start:
  %_7 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_0 = alloca [4 x i8], align 4
  %_3 = zext i8 %order to i64
  switch i64 %_3, label %bb1 [
    i64 0, label %bb6
    i64 1, label %bb3
    i64 2, label %bb5
    i64 3, label %bb2
    i64 4, label %bb4
  ]

bb1:                                              ; preds = %start
  unreachable

bb6:                                              ; preds = %start
  %0 = load atomic i32, ptr %dst monotonic, align 4
  store i32 %0, ptr %_0, align 4
  br label %bb7

bb3:                                              ; preds = %start
  store ptr @alloc_b55f69a00b865c3d8c5f6dad0122f5bd, ptr %_5, align 8
  %1 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 1, ptr %1, align 8
  %2 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %3 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %4 = getelementptr inbounds i8, ptr %_5, i64 32
  store ptr %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  store i64 %3, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %_5, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %6, i64 8
  store i64 0, ptr %7, align 8
; call core::panicking::panic_fmt
  call void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_5, ptr align 8 @alloc_4a55fd50178bfdd3a757ab28109fa708) #16
  unreachable

bb5:                                              ; preds = %start
  %8 = load atomic i32, ptr %dst acquire, align 4
  store i32 %8, ptr %_0, align 4
  br label %bb7

bb2:                                              ; preds = %start
  store ptr @alloc_0175cc81e1f5c8f5b757d44420d81e68, ptr %_7, align 8
  %9 = getelementptr inbounds i8, ptr %_7, i64 8
  store i64 1, ptr %9, align 8
  %10 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %12 = getelementptr inbounds i8, ptr %_7, i64 32
  store ptr %10, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %12, i64 8
  store i64 %11, ptr %13, align 8
  %14 = getelementptr inbounds i8, ptr %_7, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %14, align 8
  %15 = getelementptr inbounds i8, ptr %14, i64 8
  store i64 0, ptr %15, align 8
; call core::panicking::panic_fmt
  call void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_7, ptr align 8 @alloc_82a6eea5780750969a484935b6fa420d) #16
  unreachable

bb4:                                              ; preds = %start
  %16 = load atomic i32, ptr %dst seq_cst, align 4
  store i32 %16, ptr %_0, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb5, %bb6
  %17 = load i32, ptr %_0, align 4
  ret i32 %17
}

; core::sync::atomic::AtomicI32::load
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core4sync6atomic9AtomicI324load17he315e1e975863d4eE(ptr align 4 %self, i8 %order) unnamed_addr #0 {
start:
; call core::sync::atomic::atomic_load
  %_0 = call i32 @_ZN4core4sync6atomic11atomic_load17h2c13f6cd19c4e096E(ptr %self, i8 %order)
  ret i32 %_0
}

; core::sync::atomic::AtomicI32::fetch_add
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core4sync6atomic9AtomicI329fetch_add17h5ede85310db6cadfE(ptr align 4 %self, i32 %val, i8 %order) unnamed_addr #0 {
start:
  %_0 = alloca [4 x i8], align 4
  %_7 = zext i8 %order to i64
  switch i64 %_7, label %bb2 [
    i64 0, label %bb7
    i64 1, label %bb5
    i64 2, label %bb6
    i64 3, label %bb4
    i64 4, label %bb3
  ]

bb2:                                              ; preds = %start
  unreachable

bb7:                                              ; preds = %start
  %0 = atomicrmw add ptr %self, i32 %val monotonic, align 4
  store i32 %0, ptr %_0, align 4
  br label %bb1

bb5:                                              ; preds = %start
  %1 = atomicrmw add ptr %self, i32 %val release, align 4
  store i32 %1, ptr %_0, align 4
  br label %bb1

bb6:                                              ; preds = %start
  %2 = atomicrmw add ptr %self, i32 %val acquire, align 4
  store i32 %2, ptr %_0, align 4
  br label %bb1

bb4:                                              ; preds = %start
  %3 = atomicrmw add ptr %self, i32 %val acq_rel, align 4
  store i32 %3, ptr %_0, align 4
  br label %bb1

bb3:                                              ; preds = %start
  %4 = atomicrmw add ptr %self, i32 %val seq_cst, align 4
  store i32 %4, ptr %_0, align 4
  br label %bb1

bb1:                                              ; preds = %bb3, %bb4, %bb6, %bb5, %bb7
  %5 = load i32, ptr %_0, align 4
  ret i32 %5
}

; core::alloc::layout::Layout::repeat_packed
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h62e97f61b35f0b2bE(ptr align 8 %self, i64 %n) unnamed_addr #0 {
start:
  %_3 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %self1 = load i64, ptr %0, align 8
  %1 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %self1, i64 %n)
  %_9.0 = extractvalue { i64, i1 } %1, 0
  %_9.1 = extractvalue { i64, i1 } %1, 1
  br i1 %_9.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_3, i64 8
  store i64 %_9.0, ptr %2, align 8
  store i64 1, ptr %_3, align 8
  %3 = getelementptr inbounds i8, ptr %_3, i64 8
  %size = load i64, ptr %3, align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_15 = sub nuw i64 -9223372036854775808, %align
  %_14 = icmp ugt i64 %size, %_15
  br i1 %_14, label %bb6, label %bb7

bb2:                                              ; preds = %start
  %4 = load i64, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %5 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  store i64 %4, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  br label %bb1

bb7:                                              ; preds = %bb4
  store i64 %align, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %7, align 8
  br label %bb5

bb6:                                              ; preds = %bb4
  %8 = load i64, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %9 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  store i64 %8, ptr %_0, align 8
  %10 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %9, ptr %10, align 8
  br label %bb5

bb5:                                              ; preds = %bb6, %bb7
  br label %bb1

bb1:                                              ; preds = %bb2, %bb5
  %11 = load i64, ptr %_0, align 8
  %12 = getelementptr inbounds i8, ptr %_0, i64 8
  %13 = load i64, ptr %12, align 8
  %14 = insertvalue { i64, i64 } poison, i64 %11, 0
  %15 = insertvalue { i64, i64 } %14, i64 %13, 1
  ret { i64, i64 } %15
}

; core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h98c1de9bb76094d4E(i64 %size, i64 %align) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
; invoke core::alloc::layout::Layout::is_size_align_valid
  %_3 = invoke zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64 %size, i64 %align)
          to label %bb1 unwind label %cs_terminate

cs_terminate:                                     ; preds = %start
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

bb1:                                              ; preds = %start
  br i1 %_3, label %bb2, label %bb3

bb3:                                              ; preds = %bb1
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_1be5ea12ba708d9a11b6e93a7d387a75, i64 281) #18
  unreachable

bb2:                                              ; preds = %bb1
  ret void
}

; core::alloc::layout::Layout::repeat
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core5alloc6layout6Layout6repeat17h4c7322e45e937295E(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %n) unnamed_addr #0 {
start:
  %_8 = alloca [24 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %padded = alloca [16 x i8], align 8
  %align = load i64, ptr %self, align 8
  %_20 = icmp uge i64 %align, 1
  %_21 = icmp ule i64 %align, -9223372036854775808
  %_22 = and i1 %_20, %_21
  %_13 = sub nuw i64 %align, 1
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_16 = load i64, ptr %0, align 8
  %_15 = add nuw i64 %_16, %_13
  %_17 = xor i64 %_13, -1
  %new_size = and i64 %_15, %_17
  %_23 = load i64, ptr %self, align 8
  %_26 = icmp uge i64 %_23, 1
  %_27 = icmp ule i64 %_23, -9223372036854775808
  %_28 = and i1 %_26, %_27
  br label %bb5

bb5:                                              ; preds = %start
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h98c1de9bb76094d4E(i64 %new_size, i64 %_23) #15
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = getelementptr inbounds i8, ptr %padded, i64 8
  store i64 %new_size, ptr %1, align 8
  store i64 %_23, ptr %padded, align 8
; call core::alloc::layout::Layout::repeat_packed
  %2 = call { i64, i64 } @_ZN4core5alloc6layout6Layout13repeat_packed17h62e97f61b35f0b2bE(ptr align 8 %padded, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %6 = load i64, ptr %_4, align 8
  %7 = getelementptr inbounds i8, ptr %_4, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = icmp eq i64 %6, 0
  %_6 = select i1 %9, i64 1, i64 0
  %10 = trunc nuw i64 %_6 to i1
  br i1 %10, label %bb3, label %bb2

bb3:                                              ; preds = %bb6
  store i64 0, ptr %_0, align 8
  br label %bb4

bb2:                                              ; preds = %bb6
  %repeated.0 = load i64, ptr %_4, align 8
  %11 = getelementptr inbounds i8, ptr %_4, i64 8
  %repeated.1 = load i64, ptr %11, align 8
  store i64 %repeated.0, ptr %_8, align 8
  %12 = getelementptr inbounds i8, ptr %_8, i64 8
  store i64 %repeated.1, ptr %12, align 8
  %13 = getelementptr inbounds i8, ptr %_8, i64 16
  store i64 %new_size, ptr %13, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_8, i64 24, i1 false)
  br label %bb4

bb4:                                              ; preds = %bb3, %bb2
  ret void

bb7:                                              ; No predecessors!
  unreachable
}

; core::slice::raw::from_raw_parts::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h57ff3385a146f7a1E(ptr %data, i64 %size, i64 %align, i64 %len) unnamed_addr #3 personality ptr @__CxxFrameHandler3 {
start:
  %0 = alloca [4 x i8], align 4
  %max_len = alloca [8 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %1 = call i64 @llvm.ctpop.i64(i64 %align)
  %2 = trunc i64 %1 to i32
  store i32 %2, ptr %0, align 4
  %_15 = load i32, ptr %0, align 4
  %3 = icmp eq i32 %_15, 1
  br i1 %3, label %bb8, label %bb9

bb8:                                              ; preds = %start
  %_13 = ptrtoint ptr %data to i64
  %_14 = sub i64 %align, 1
  %_12 = and i64 %_13, %_14
  %4 = icmp eq i64 %_12, 0
  br i1 %4, label %bb6, label %bb7

bb9:                                              ; preds = %start
  store ptr @alloc_e92e94d0ff530782b571cfd99ec66aef, ptr %_11, align 8
  %5 = getelementptr inbounds i8, ptr %_11, i64 8
  store i64 1, ptr %5, align 8
  %6 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %7 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %8 = getelementptr inbounds i8, ptr %_11, i64 32
  store ptr %6, ptr %8, align 8
  %9 = getelementptr inbounds i8, ptr %8, i64 8
  store i64 %7, ptr %9, align 8
  %10 = getelementptr inbounds i8, ptr %_11, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %10, align 8
  %11 = getelementptr inbounds i8, ptr %10, i64 8
  store i64 0, ptr %11, align 8
; invoke core::panicking::panic_fmt
  invoke void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8 %_11, ptr align 8 @alloc_72de19bf38e62b85db2357042b61256e) #16
          to label %unreachable unwind label %cs_terminate

bb6:                                              ; preds = %bb8
  %_9 = icmp eq i64 %_13, 0
  %_5 = xor i1 %_9, true
  br i1 %_5, label %bb1, label %bb4

bb7:                                              ; preds = %bb8
  br label %bb4

bb4:                                              ; preds = %bb7, %bb6
  br label %bb5

bb1:                                              ; preds = %bb6
  %_19 = icmp eq i64 %size, 0
  %12 = icmp eq i64 %size, 0
  br i1 %12, label %bb11, label %bb12

bb11:                                             ; preds = %bb1
  store i64 -1, ptr %max_len, align 8
  br label %bb14

bb12:                                             ; preds = %bb1
  br i1 %_19, label %panic, label %bb13

bb14:                                             ; preds = %bb13, %bb11
  %_20 = load i64, ptr %max_len, align 8
  %_7 = icmp ule i64 %len, %_20
  br i1 %_7, label %bb2, label %bb3

bb13:                                             ; preds = %bb12
  %13 = udiv i64 9223372036854775807, %size
  store i64 %13, ptr %max_len, align 8
  br label %bb14

panic:                                            ; preds = %bb12
; invoke core::panicking::panic_const::panic_const_div_by_zero
  invoke void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_3e3d0b2e0fa792e2b848e5abc8783d27) #16
          to label %unreachable unwind label %cs_terminate

cs_terminate:                                     ; preds = %bb9, %panic
  %catchswitch = catchswitch within none [label %cp_terminate] unwind to caller

cp_terminate:                                     ; preds = %cs_terminate
  %catchpad = catchpad within %catchswitch [ptr null, i32 64, ptr null]
; call core::panicking::panic_cannot_unwind
  call void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() #17 [ "funclet"(token %catchpad) ]
  unreachable

unreachable:                                      ; preds = %bb9, %panic
  unreachable

bb3:                                              ; preds = %bb14
  br label %bb5

bb2:                                              ; preds = %bb14
  ret void

bb5:                                              ; preds = %bb4, %bb3
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a28e8c8fd5088943a8b5d44af697ff83, i64 279) #18
  unreachable
}

; core::ub_checks::maybe_is_nonoverlapping::runtime
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @_ZN4core9ub_checks23maybe_is_nonoverlapping7runtime17hd39347e0fb243828E(ptr %src, ptr %dst, i64 %size, i64 %count) unnamed_addr #0 {
start:
  %diff = alloca [8 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %src_usize = ptrtoint ptr %src to i64
  %dst_usize = ptrtoint ptr %dst to i64
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %size, i64 %count)
  %_14.0 = extractvalue { i64, i1 } %0, 0
  %_14.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_14.1, label %bb1, label %bb3

bb3:                                              ; preds = %start
  %1 = getelementptr inbounds i8, ptr %_9, i64 8
  store i64 %_14.0, ptr %1, align 8
  store i64 1, ptr %_9, align 8
  %2 = getelementptr inbounds i8, ptr %_9, i64 8
  %size1 = load i64, ptr %2, align 8
  %_22 = icmp ult i64 %src_usize, %dst_usize
  br i1 %_22, label %bb4, label %bb5

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_763310d78c99c2c1ad3f8a9821e942f3, i64 61) #18
  unreachable

bb5:                                              ; preds = %bb3
  %3 = sub i64 %src_usize, %dst_usize
  store i64 %3, ptr %diff, align 8
  br label %bb6

bb4:                                              ; preds = %bb3
  %4 = sub i64 %dst_usize, %src_usize
  store i64 %4, ptr %diff, align 8
  br label %bb6

bb6:                                              ; preds = %bb4, %bb5
  %_11 = load i64, ptr %diff, align 8
  %_0 = icmp uge i64 %_11, %size1
  ret i1 %_0
}

; <str as alloc::string::SpecToString>::spec_to_string
; Function Attrs: inlinehint uwtable
define internal void @"_ZN51_$LT$str$u20$as$u20$alloc..string..SpecToString$GT$14spec_to_string17hdc0543481924cd8bE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %self.0, i64 %self.1) unnamed_addr #0 {
start:
  %bytes = alloca [24 x i8], align 8
; call <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
  call void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h04663d13f553edcaE"(ptr sret([24 x i8]) align 8 %bytes, ptr align 1 %self.0, i64 %self.1)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %bytes, i64 24, i1 false)
  ret void
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h7b51e1130b98dbe6E"() unnamed_addr #0 {
start:
  ret i32 0
}

; alloc::alloc::alloc_zeroed
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc12alloc_zeroed17hb6b53809ad720824E(i64 %0, i64 %1) unnamed_addr #0 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17hd9221164d7f70239E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #15
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc_zeroed
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64 %_3, i64 %_10) #15
  ret ptr %_0
}

; alloc::alloc::alloc
; Function Attrs: inlinehint uwtable
define internal ptr @_ZN5alloc5alloc5alloc17h888fa042a5eba1a8E(i64 %0, i64 %1) unnamed_addr #0 {
start:
  %2 = alloca [1 x i8], align 1
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %3, align 8
  br label %bb3

bb3:                                              ; preds = %start
; call core::ptr::read_volatile::precondition_check
  call void @_ZN4core3ptr13read_volatile18precondition_check17hd9221164d7f70239E(ptr @__rust_no_alloc_shim_is_unstable, i64 1, i1 zeroext false) #15
  br label %bb5

bb5:                                              ; preds = %bb3
  %4 = load volatile i8, ptr @__rust_no_alloc_shim_is_unstable, align 1
  store i8 %4, ptr %2, align 1
  %_2 = load i8, ptr %2, align 1
  %5 = getelementptr inbounds i8, ptr %layout, i64 8
  %_3 = load i64, ptr %5, align 8
  %_10 = load i64, ptr %layout, align 8
  %_13 = icmp uge i64 %_10, 1
  %_14 = icmp ule i64 %_10, -9223372036854775808
  %_15 = and i1 %_13, %_14
; call __rustc::__rust_alloc
  %_0 = call ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64 %_3, i64 %_10) #15
  ret ptr %_0
}

; alloc::alloc::Global::alloc_impl
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h1291daf3d3e1eaeeE(ptr align 1 %self, i64 %0, i64 %1, i1 zeroext %zeroed) unnamed_addr #0 {
start:
  %self2 = alloca [8 x i8], align 8
  %self1 = alloca [8 x i8], align 8
  %_10 = alloca [8 x i8], align 8
  %raw_ptr = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %size = load i64, ptr %3, align 8
  %4 = icmp eq i64 %size, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %start
  %_17 = load i64, ptr %layout, align 8
  %_18 = getelementptr i8, ptr null, i64 %_17
  %data = getelementptr i8, ptr null, i64 %_17
  br label %bb7

bb1:                                              ; preds = %start
  br i1 %zeroed, label %bb3, label %bb4

bb7:                                              ; preds = %bb2
  %_23 = getelementptr i8, ptr null, i64 %_17
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h443925849fa68ee6E"(ptr %_23) #15
  br label %bb9

bb9:                                              ; preds = %bb7
  store ptr %data, ptr %_0, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb6

bb6:                                              ; preds = %bb17, %bb10, %bb9
  %6 = load ptr, ptr %_0, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 8
  %8 = load i64, ptr %7, align 8
  %9 = insertvalue { ptr, i64 } poison, ptr %6, 0
  %10 = insertvalue { ptr, i64 } %9, i64 %8, 1
  ret { ptr, i64 } %10

bb4:                                              ; preds = %bb1
  %11 = load i64, ptr %layout, align 8
  %12 = getelementptr inbounds i8, ptr %layout, i64 8
  %13 = load i64, ptr %12, align 8
; call alloc::alloc::alloc
  %14 = call ptr @_ZN5alloc5alloc5alloc17h888fa042a5eba1a8E(i64 %11, i64 %13)
  store ptr %14, ptr %raw_ptr, align 8
  br label %bb5

bb3:                                              ; preds = %bb1
  %15 = load i64, ptr %layout, align 8
  %16 = getelementptr inbounds i8, ptr %layout, i64 8
  %17 = load i64, ptr %16, align 8
; call alloc::alloc::alloc_zeroed
  %18 = call ptr @_ZN5alloc5alloc12alloc_zeroed17hb6b53809ad720824E(i64 %15, i64 %17)
  store ptr %18, ptr %raw_ptr, align 8
  br label %bb5

bb5:                                              ; preds = %bb3, %bb4
  %ptr = load ptr, ptr %raw_ptr, align 8
  %_27 = ptrtoint ptr %ptr to i64
  %19 = icmp eq i64 %_27, 0
  br i1 %19, label %bb10, label %bb11

bb10:                                             ; preds = %bb5
  store ptr null, ptr %self2, align 8
  store ptr null, ptr %self1, align 8
  %20 = load ptr, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %21 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  store ptr %20, ptr %_0, align 8
  %22 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %21, ptr %22, align 8
  br label %bb6

bb11:                                             ; preds = %bb5
  br label %bb12

bb12:                                             ; preds = %bb11
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h443925849fa68ee6E"(ptr %ptr) #15
  br label %bb14

bb14:                                             ; preds = %bb12
  store ptr %ptr, ptr %self2, align 8
  %v = load ptr, ptr %self2, align 8
  store ptr %v, ptr %self1, align 8
  %v3 = load ptr, ptr %self1, align 8
  store ptr %v3, ptr %_10, align 8
  %ptr4 = load ptr, ptr %_10, align 8
  br label %bb15

bb15:                                             ; preds = %bb14
; call core::ptr::non_null::NonNull<T>::new_unchecked::precondition_check
  call void @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$13new_unchecked18precondition_check17h443925849fa68ee6E"(ptr %ptr4) #15
  br label %bb17

bb17:                                             ; preds = %bb15
  store ptr %ptr4, ptr %_0, align 8
  %23 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %size, ptr %23, align 8
  br label %bb6
}

; alloc::raw_vec::RawVecInner<A>::deallocate
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h2be48a44ec906d0eE"(ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::current_memory
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h508c311f61f97e14E"(ptr sret([24 x i8]) align 8 %_3, ptr align 8 %self, i64 %elem_layout.0, i64 %elem_layout.1)
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  %1 = load i64, ptr %0, align 8
  %2 = icmp eq i64 %1, 0
  %_5 = select i1 %2, i64 0, i64 1
  %3 = trunc nuw i64 %_5 to i1
  br i1 %3, label %bb2, label %bb4

bb2:                                              ; preds = %start
  %ptr = load ptr, ptr %_3, align 8
  %4 = getelementptr inbounds i8, ptr %_3, i64 8
  %layout.0 = load i64, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %4, i64 8
  %layout.1 = load i64, ptr %5, align 8
  %_9 = getelementptr inbounds i8, ptr %self, i64 16
; call <alloc::alloc::Global as core::alloc::Allocator>::deallocate
  call void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h13c0df5a8097d698E"(ptr align 1 %_9, ptr %ptr, i64 %layout.0, i64 %layout.1)
  br label %bb5

bb4:                                              ; preds = %start
  br label %bb5

bb5:                                              ; preds = %bb4, %bb2
  ret void

bb6:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::current_memory
; Function Attrs: inlinehint uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$14current_memory17h508c311f61f97e14E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %_15 = alloca [24 x i8], align 8
  %align = alloca [8 x i8], align 8
  %alloc_size = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %self1 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %self1, 0
  br i1 %4, label %bb3, label %bb1

bb3:                                              ; preds = %bb2, %start
  %5 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %5, align 8
  br label %bb5

bb1:                                              ; preds = %start
  %self2 = load i64, ptr %self, align 8
  %6 = icmp eq i64 %self2, 0
  br i1 %6, label %bb2, label %bb4

bb2:                                              ; preds = %bb1
  br label %bb3

bb4:                                              ; preds = %bb1
  %self3 = load i64, ptr %self, align 8
  br label %bb6

bb5:                                              ; preds = %bb9, %bb3
  ret void

bb6:                                              ; preds = %bb4
; call core::num::<impl usize>::unchecked_mul::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_mul18precondition_check17h88b32a1badac6a96E"(i64 %self1, i64 %self3) #15
  %7 = mul nuw i64 %self1, %self3
  store i64 %7, ptr %alloc_size, align 8
  %size = load i64, ptr %alloc_size, align 8
  %_18 = load i64, ptr %elem_layout, align 8
  %_21 = icmp uge i64 %_18, 1
  %_22 = icmp ule i64 %_18, -9223372036854775808
  %_23 = and i1 %_21, %_22
  store i64 %_18, ptr %align, align 8
  br label %bb8

bb8:                                              ; preds = %bb6
  %8 = load i64, ptr %alloc_size, align 8
  %9 = load i64, ptr %align, align 8
; call core::alloc::layout::Layout::from_size_align_unchecked::precondition_check
  call void @_ZN4core5alloc6layout6Layout25from_size_align_unchecked18precondition_check17h98c1de9bb76094d4E(i64 %8, i64 %9) #15
  br label %bb9

bb9:                                              ; preds = %bb8
  %_25 = load i64, ptr %align, align 8
  %layout.1 = load i64, ptr %alloc_size, align 8
  %10 = getelementptr inbounds i8, ptr %self, i64 8
  %self4 = load ptr, ptr %10, align 8
  store ptr %self4, ptr %_15, align 8
  %11 = getelementptr inbounds i8, ptr %_15, i64 8
  store i64 %_25, ptr %11, align 8
  %12 = getelementptr inbounds i8, ptr %11, i64 8
  store i64 %layout.1, ptr %12, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_15, i64 24, i1 false)
  br label %bb5

bb7:                                              ; No predecessors!
  unreachable
}

; alloc::raw_vec::RawVecInner<A>::try_allocate_in
; Function Attrs: uwtable
define internal void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h946bb1ce4a49bf5aE"(ptr sret([24 x i8]) align 8 %_0, i64 %capacity, i1 zeroext %init, i64 %0, i64 %1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %self3 = alloca [24 x i8], align 8
  %self2 = alloca [16 x i8], align 8
  %self = alloca [16 x i8], align 8
  %result = alloca [16 x i8], align 8
  %elem_layout1 = alloca [16 x i8], align 8
  %_6 = alloca [24 x i8], align 8
  %layout = alloca [16 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %alloc = alloca [0 x i8], align 1
  store i64 %0, ptr %elem_layout, align 8
  %2 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = load i64, ptr %elem_layout, align 8
  %4 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  %5 = load i64, ptr %4, align 8
  store i64 %3, ptr %elem_layout1, align 8
  %6 = getelementptr inbounds i8, ptr %elem_layout1, i64 8
  store i64 %5, ptr %6, align 8
; invoke core::alloc::layout::Layout::repeat
  invoke void @_ZN4core5alloc6layout6Layout6repeat17h4c7322e45e937295E(ptr sret([24 x i8]) align 8 %self3, ptr align 8 %elem_layout1, i64 %capacity)
          to label %bb16 unwind label %funclet_bb15

bb15:                                             ; preds = %funclet_bb15
  br label %bb14

funclet_bb15:                                     ; preds = %bb4, %bb5, %start
  %cleanuppad = cleanuppad within none []
  br label %bb15

bb16:                                             ; preds = %start
  %7 = load i64, ptr %self3, align 8
  %8 = icmp eq i64 %7, 0
  %_33 = select i1 %8, i64 1, i64 0
  %9 = trunc nuw i64 %_33 to i1
  br i1 %9, label %bb17, label %bb18

bb17:                                             ; preds = %bb16
  %10 = load i64, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %11 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  store i64 %10, ptr %self2, align 8
  %12 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %11, ptr %12, align 8
  %13 = load i64, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, align 8
  %14 = load i64, ptr getelementptr inbounds (i8, ptr @anon.21b9fe1916ccdbd19ce77edcbad2c0ea.1, i64 8), align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %13, ptr %15, align 8
  %16 = getelementptr inbounds i8, ptr %15, i64 8
  store i64 %14, ptr %16, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb18:                                             ; preds = %bb16
  %t.0 = load i64, ptr %self3, align 8
  %17 = getelementptr inbounds i8, ptr %self3, i64 8
  %t.1 = load i64, ptr %17, align 8
  %18 = getelementptr inbounds i8, ptr %self3, i64 16
  %t = load i64, ptr %18, align 8
  store i64 %t.0, ptr %self2, align 8
  %19 = getelementptr inbounds i8, ptr %self2, i64 8
  store i64 %t.1, ptr %19, align 8
  %t.04 = load i64, ptr %self2, align 8
  %20 = getelementptr inbounds i8, ptr %self2, i64 8
  %t.15 = load i64, ptr %20, align 8
  %21 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %t.04, ptr %21, align 8
  %22 = getelementptr inbounds i8, ptr %21, i64 8
  store i64 %t.15, ptr %22, align 8
  store i64 0, ptr %_6, align 8
  %23 = getelementptr inbounds i8, ptr %_6, i64 8
  %layout.0 = load i64, ptr %23, align 8
  %24 = getelementptr inbounds i8, ptr %23, i64 8
  %layout.1 = load i64, ptr %24, align 8
  store i64 %layout.0, ptr %layout, align 8
  %25 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %layout.1, ptr %25, align 8
  %26 = icmp eq i64 %layout.1, 0
  br i1 %26, label %bb2, label %bb3

bb2:                                              ; preds = %bb18
  %_37 = load i64, ptr %elem_layout, align 8
  %_40 = icmp uge i64 %_37, 1
  %_41 = icmp ule i64 %_37, -9223372036854775808
  %_42 = and i1 %_40, %_41
  %_43 = getelementptr i8, ptr null, i64 %_37
  %27 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 0, ptr %27, align 8
  %28 = getelementptr inbounds i8, ptr %27, i64 8
  store ptr %_43, ptr %28, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb3:                                              ; preds = %bb18
  %_17 = zext i1 %init to i64
  %29 = trunc nuw i64 %_17 to i1
  br i1 %29, label %bb4, label %bb5

bb11:                                             ; preds = %bb13, %bb10, %bb2
  ret void

bb4:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
  %30 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17hf5c971dba833b3a6E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb7 unwind label %funclet_bb15

bb5:                                              ; preds = %bb3
; invoke <alloc::alloc::Global as core::alloc::Allocator>::allocate
  %31 = invoke { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17ha10571ce46485e36E"(ptr align 1 %alloc, i64 %layout.0, i64 %layout.1)
          to label %bb6 unwind label %funclet_bb15

bb6:                                              ; preds = %bb5
  %32 = extractvalue { ptr, i64 } %31, 0
  %33 = extractvalue { ptr, i64 } %31, 1
  store ptr %32, ptr %result, align 8
  %34 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %33, ptr %34, align 8
  br label %bb8

bb8:                                              ; preds = %bb7, %bb6
  %35 = load ptr, ptr %result, align 8
  %36 = getelementptr inbounds i8, ptr %result, i64 8
  %37 = load i64, ptr %36, align 8
  %38 = ptrtoint ptr %35 to i64
  %39 = icmp eq i64 %38, 0
  %_20 = select i1 %39, i64 1, i64 0
  %40 = trunc nuw i64 %_20 to i1
  br i1 %40, label %bb9, label %bb10

bb7:                                              ; preds = %bb4
  %41 = extractvalue { ptr, i64 } %30, 0
  %42 = extractvalue { ptr, i64 } %30, 1
  store ptr %41, ptr %result, align 8
  %43 = getelementptr inbounds i8, ptr %result, i64 8
  store i64 %42, ptr %43, align 8
  br label %bb8

bb9:                                              ; preds = %bb8
  store i64 %layout.0, ptr %self, align 8
  %44 = getelementptr inbounds i8, ptr %self, i64 8
  store i64 %layout.1, ptr %44, align 8
  %_22.0 = load i64, ptr %self, align 8
  %45 = getelementptr inbounds i8, ptr %self, i64 8
  %_22.1 = load i64, ptr %45, align 8
  %46 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_22.0, ptr %46, align 8
  %47 = getelementptr inbounds i8, ptr %46, i64 8
  store i64 %_22.1, ptr %47, align 8
  store i64 1, ptr %_0, align 8
  br label %bb13

bb10:                                             ; preds = %bb8
  %ptr.0 = load ptr, ptr %result, align 8
  %48 = getelementptr inbounds i8, ptr %result, i64 8
  %ptr.1 = load i64, ptr %48, align 8
  %49 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %capacity, ptr %49, align 8
  %50 = getelementptr inbounds i8, ptr %49, i64 8
  store ptr %ptr.0, ptr %50, align 8
  store i64 0, ptr %_0, align 8
  br label %bb11

bb13:                                             ; preds = %bb17, %bb9
  br label %bb11

bb1:                                              ; No predecessors!
  unreachable

bb14:                                             ; preds = %bb15
  br label %bb12

bb12:                                             ; preds = %bb14
  cleanupret from %cleanuppad unwind to caller
}

; alloc::raw_vec::RawVecInner<A>::with_capacity_in
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h2bcc0557a4e65b76E"(i64 %capacity, i64 %elem_layout.0, i64 %elem_layout.1, ptr align 8 %0) unnamed_addr #0 {
start:
  %self = alloca [8 x i8], align 8
  %elem_layout = alloca [16 x i8], align 8
  %this = alloca [16 x i8], align 8
  %_4 = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::try_allocate_in
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$15try_allocate_in17h946bb1ce4a49bf5aE"(ptr sret([24 x i8]) align 8 %_4, i64 %capacity, i1 zeroext false, i64 %elem_layout.0, i64 %elem_layout.1)
  %_5 = load i64, ptr %_4, align 8
  %1 = trunc nuw i64 %_5 to i1
  br i1 %1, label %bb3, label %bb4

bb3:                                              ; preds = %start
  %2 = getelementptr inbounds i8, ptr %_4, i64 8
  %err.0 = load i64, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %2, i64 8
  %err.1 = load i64, ptr %3, align 8
; call alloc::raw_vec::handle_error
  call void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64 %err.0, i64 %err.1, ptr align 8 %0) #16
  unreachable

bb4:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %_4, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
  store i64 %5, ptr %this, align 8
  %8 = getelementptr inbounds i8, ptr %this, i64 8
  store ptr %7, ptr %8, align 8
  store i64 %elem_layout.0, ptr %elem_layout, align 8
  %9 = getelementptr inbounds i8, ptr %elem_layout, i64 8
  store i64 %elem_layout.1, ptr %9, align 8
  %10 = icmp eq i64 %elem_layout.1, 0
  br i1 %10, label %bb6, label %bb7

bb6:                                              ; preds = %bb4
  store i64 -1, ptr %self, align 8
  br label %bb5

bb7:                                              ; preds = %bb4
  %self1 = load i64, ptr %this, align 8
  store i64 %self1, ptr %self, align 8
  br label %bb5

bb5:                                              ; preds = %bb7, %bb6
  %11 = load i64, ptr %self, align 8
  %_13 = sub i64 %11, 0
  %_8 = icmp ugt i64 %capacity, %_13
  %cond = xor i1 %_8, true
  br label %bb8

bb8:                                              ; preds = %bb5
; call core::hint::assert_unchecked::precondition_check
  call void @_ZN4core4hint16assert_unchecked18precondition_check17hebad5c52021b0021E(i1 zeroext %cond) #15
  br label %bb9

bb9:                                              ; preds = %bb8
  %_0.0 = load i64, ptr %this, align 8
  %12 = getelementptr inbounds i8, ptr %this, i64 8
  %_0.1 = load ptr, ptr %12, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %_0.0, 0
  %14 = insertvalue { i64, ptr } %13, ptr %_0.1, 1
  ret { i64, ptr } %14

bb2:                                              ; No predecessors!
  unreachable
}

; <alloc::string::String as core::fmt::Display>::fmt
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17hd12e4145c23fdc5aE"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::slice::raw::from_raw_parts::precondition_check
  call void @_ZN4core5slice3raw14from_raw_parts18precondition_check17h57ff3385a146f7a1E(ptr %_8, i64 1, i64 1, i64 %len) #15
  br label %bb4

bb4:                                              ; preds = %bb2
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_8, i64 %len, ptr align 8 %f)
  ret i1 %_0
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1cbea0a0df6f15f7E"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #0 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <alloc::alloc::Global as core::alloc::Allocator>::deallocate
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h13c0df5a8097d698E"(ptr align 1 %self, ptr %ptr, i64 %0, i64 %1) unnamed_addr #0 {
start:
  %layout1 = alloca [16 x i8], align 8
  %layout = alloca [16 x i8], align 8
  store i64 %0, ptr %layout, align 8
  %2 = getelementptr inbounds i8, ptr %layout, i64 8
  store i64 %1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %layout, i64 8
  %_4 = load i64, ptr %3, align 8
  %4 = icmp eq i64 %_4, 0
  br i1 %4, label %bb2, label %bb1

bb2:                                              ; preds = %bb1, %start
  ret void

bb1:                                              ; preds = %start
  %5 = load i64, ptr %layout, align 8
  %6 = getelementptr inbounds i8, ptr %layout, i64 8
  %7 = load i64, ptr %6, align 8
  store i64 %5, ptr %layout1, align 8
  %8 = getelementptr inbounds i8, ptr %layout1, i64 8
  store i64 %7, ptr %8, align 8
  %_11 = load i64, ptr %layout, align 8
  %_14 = icmp uge i64 %_11, 1
  %_15 = icmp ule i64 %_11, -9223372036854775808
  %_16 = and i1 %_14, %_15
; call __rustc::__rust_dealloc
  call void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr %ptr, i64 %_4, i64 %_11) #15
  br label %bb2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate_zeroed
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$15allocate_zeroed17hf5c971dba833b3a6E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #0 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h1291daf3d3e1eaeeE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext true)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::alloc::Global as core::alloc::Allocator>::allocate
; Function Attrs: inlinehint uwtable
define internal { ptr, i64 } @"_ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$8allocate17ha10571ce46485e36E"(ptr align 1 %self, i64 %layout.0, i64 %layout.1) unnamed_addr #0 {
start:
; call alloc::alloc::Global::alloc_impl
  %0 = call { ptr, i64 } @_ZN5alloc5alloc6Global10alloc_impl17h1291daf3d3e1eaeeE(ptr align 1 %self, i64 %layout.0, i64 %layout.1, i1 zeroext false)
  %_0.0 = extractvalue { ptr, i64 } %0, 0
  %_0.1 = extractvalue { ptr, i64 } %0, 1
  %1 = insertvalue { ptr, i64 } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, i64 } %1, i64 %_0.1, 1
  ret { ptr, i64 } %2
}

; <alloc::vec::Vec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN70_$LT$alloc..vec..Vec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h858bdf03d665bb86E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %len = load i64, ptr %1, align 8
  ret void
}

; <alloc::raw_vec::RawVec<T,A> as core::ops::drop::Drop>::drop
; Function Attrs: uwtable
define internal void @"_ZN77_$LT$alloc..raw_vec..RawVec$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2db5da15482b7a0fE"(ptr align 8 %self) unnamed_addr #1 {
start:
; call alloc::raw_vec::RawVecInner<A>::deallocate
  call void @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$10deallocate17h2be48a44ec906d0eE"(ptr align 8 %self, i64 1, i64 1)
  ret void
}

; <T as alloc::slice::<impl [T]>::to_vec_in::ConvertVec>::to_vec
; Function Attrs: inlinehint uwtable
define internal void @"_ZN87_$LT$T$u20$as$u20$alloc..slice..$LT$impl$u20$$u5b$T$u5d$$GT$..to_vec_in..ConvertVec$GT$6to_vec17h04663d13f553edcaE"(ptr sret([24 x i8]) align 8 %_0, ptr align 1 %s.0, i64 %s.1) unnamed_addr #0 {
start:
  %v = alloca [24 x i8], align 8
; call alloc::raw_vec::RawVecInner<A>::with_capacity_in
  %0 = call { i64, ptr } @"_ZN5alloc7raw_vec20RawVecInner$LT$A$GT$16with_capacity_in17h2bcc0557a4e65b76E"(i64 %s.1, i64 1, i64 1, ptr align 8 @alloc_0ab120d992479c4cb1e1767c901c8927)
  %_10.0 = extractvalue { i64, ptr } %0, 0
  %_10.1 = extractvalue { i64, ptr } %0, 1
  store i64 %_10.0, ptr %v, align 8
  %1 = getelementptr inbounds i8, ptr %v, i64 8
  store ptr %_10.1, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 0, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %v, i64 8
  %_12 = load ptr, ptr %3, align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::intrinsics::copy_nonoverlapping::precondition_check
  call void @_ZN4core10intrinsics19copy_nonoverlapping18precondition_check17h49da6cadd49f63aeE(ptr %s.0, ptr %_12, i64 1, i64 1, i64 %s.1) #15
  br label %bb4

bb4:                                              ; preds = %bb2
  %4 = mul i64 %s.1, 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 1 %_12, ptr align 1 %s.0, i64 %4, i1 false)
  %5 = getelementptr inbounds i8, ptr %v, i64 16
  store i64 %s.1, ptr %5, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %v, i64 24, i1 false)
  ret void
}

; _16_variable_scope::main
; Function Attrs: uwtable
define internal void @_ZN18_16_variable_scope4main17h2d337f3a0177b5e8E() unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_470 = alloca [48 x i8], align 8
  %_467 = alloca [48 x i8], align 8
  %_464 = alloca [16 x i8], align 8
  %_462 = alloca [16 x i8], align 8
  %_461 = alloca [32 x i8], align 8
  %_458 = alloca [48 x i8], align 8
  %error_var = alloca [16 x i8], align 8
  %error = alloca [24 x i8], align 8
  %_453 = alloca [16 x i8], align 8
  %_451 = alloca [16 x i8], align 8
  %_450 = alloca [32 x i8], align 8
  %_447 = alloca [48 x i8], align 8
  %success_var = alloca [4 x i8], align 4
  %result = alloca [4 x i8], align 4
  %division_result = alloca [24 x i8], align 8
  %_439 = alloca [48 x i8], align 8
  %_436 = alloca [48 x i8], align 8
  %_433 = alloca [16 x i8], align 8
  %_432 = alloca [16 x i8], align 8
  %_429 = alloca [48 x i8], align 8
  %nested_result = alloca [4 x i8], align 4
  %_425 = alloca [48 x i8], align 8
  %_422 = alloca [48 x i8], align 8
  %_419 = alloca [16 x i8], align 8
  %_418 = alloca [16 x i8], align 8
  %_415 = alloca [48 x i8], align 8
  %closure_result = alloca [4 x i8], align 4
  %closure = alloca [8 x i8], align 8
  %closure_outer = alloca [4 x i8], align 4
  %_406 = alloca [48 x i8], align 8
  %_403 = alloca [48 x i8], align 8
  %_400 = alloca [16 x i8], align 8
  %_398 = alloca [4 x i8], align 4
  %_396 = alloca [16 x i8], align 8
  %_395 = alloca [32 x i8], align 8
  %_392 = alloca [48 x i8], align 8
  %_389 = alloca [16 x i8], align 8
  %_388 = alloca [16 x i8], align 8
  %_385 = alloca [48 x i8], align 8
  %_382 = alloca [16 x i8], align 8
  %_380 = alloca [16 x i8], align 8
  %_379 = alloca [32 x i8], align 8
  %_376 = alloca [48 x i8], align 8
  %result_c = alloca [4 x i8], align 4
  %result_b = alloca [4 x i8], align 4
  %result_a = alloca [4 x i8], align 4
  %shared_value = alloca [4 x i8], align 4
  %_367 = alloca [48 x i8], align 8
  %_364 = alloca [48 x i8], align 8
  %_361 = alloca [16 x i8], align 8
  %_360 = alloca [16 x i8], align 8
  %_357 = alloca [48 x i8], align 8
  %_354 = alloca [16 x i8], align 8
  %_353 = alloca [16 x i8], align 8
  %_350 = alloca [48 x i8], align 8
  %_346 = alloca [16 x i8], align 8
  %_345 = alloca [16 x i8], align 8
  %_342 = alloca [48 x i8], align 8
  %_339 = alloca [16 x i8], align 8
  %_338 = alloca [16 x i8], align 8
  %_335 = alloca [48 x i8], align 8
  %local_mutable = alloca [4 x i8], align 4
  %_330 = alloca [16 x i8], align 8
  %_329 = alloca [16 x i8], align 8
  %_326 = alloca [48 x i8], align 8
  %mutable_var = alloca [4 x i8], align 4
  %_322 = alloca [48 x i8], align 8
  %_319 = alloca [48 x i8], align 8
  %_316 = alloca [16 x i8], align 8
  %_315 = alloca [16 x i8], align 8
  %_312 = alloca [48 x i8], align 8
  %large_var = alloca [4 x i8], align 4
  %_307 = alloca [16 x i8], align 8
  %_306 = alloca [16 x i8], align 8
  %_303 = alloca [48 x i8], align 8
  %medium_var = alloca [4 x i8], align 4
  %_298 = alloca [16 x i8], align 8
  %_297 = alloca [16 x i8], align 8
  %_294 = alloca [48 x i8], align 8
  %small_var = alloca [4 x i8], align 4
  %_284 = alloca [48 x i8], align 8
  %_281 = alloca [48 x i8], align 8
  %_270 = alloca [16 x i8], align 8
  %_269 = alloca [16 x i8], align 8
  %_266 = alloca [48 x i8], align 8
  %nested_if_var = alloca [4 x i8], align 4
  %_260 = alloca [16 x i8], align 8
  %_259 = alloca [16 x i8], align 8
  %_256 = alloca [48 x i8], align 8
  %if_var = alloca [4 x i8], align 4
  %_251 = alloca [48 x i8], align 8
  %_248 = alloca [48 x i8], align 8
  %_245 = alloca [16 x i8], align 8
  %_244 = alloca [16 x i8], align 8
  %_241 = alloca [48 x i8], align 8
  %_237 = alloca [16 x i8], align 8
  %_235 = alloca [16 x i8], align 8
  %_234 = alloca [32 x i8], align 8
  %_231 = alloca [48 x i8], align 8
  %while_var = alloca [4 x i8], align 4
  %counter = alloca [4 x i8], align 4
  %_222 = alloca [48 x i8], align 8
  %_219 = alloca [48 x i8], align 8
  %_216 = alloca [16 x i8], align 8
  %_215 = alloca [16 x i8], align 8
  %_212 = alloca [48 x i8], align 8
  %conditional_var = alloca [4 x i8], align 4
  %_207 = alloca [16 x i8], align 8
  %_205 = alloca [16 x i8], align 8
  %_204 = alloca [32 x i8], align 8
  %_201 = alloca [48 x i8], align 8
  %loop_var = alloca [4 x i8], align 4
  %i = alloca [4 x i8], align 4
  %_194 = alloca [8 x i8], align 4
  %iter = alloca [12 x i8], align 4
  %_192 = alloca [12 x i8], align 4
  %_191 = alloca [12 x i8], align 4
  %_189 = alloca [48 x i8], align 8
  %_186 = alloca [48 x i8], align 8
  %_183 = alloca [16 x i8], align 8
  %_182 = alloca [16 x i8], align 8
  %_179 = alloca [48 x i8], align 8
  %_176 = alloca [16 x i8], align 8
  %_175 = alloca [16 x i8], align 8
  %_172 = alloca [48 x i8], align 8
  %x5 = alloca [16 x i8], align 8
  %_168 = alloca [16 x i8], align 8
  %_167 = alloca [16 x i8], align 8
  %_164 = alloca [48 x i8], align 8
  %x4 = alloca [4 x i8], align 4
  %_159 = alloca [16 x i8], align 8
  %_158 = alloca [16 x i8], align 8
  %_155 = alloca [48 x i8], align 8
  %x3 = alloca [4 x i8], align 4
  %_150 = alloca [16 x i8], align 8
  %_149 = alloca [16 x i8], align 8
  %_146 = alloca [48 x i8], align 8
  %x = alloca [4 x i8], align 4
  %_142 = alloca [48 x i8], align 8
  %_139 = alloca [48 x i8], align 8
  %_136 = alloca [16 x i8], align 8
  %_135 = alloca [16 x i8], align 8
  %_132 = alloca [48 x i8], align 8
  %_129 = alloca [16 x i8], align 8
  %_128 = alloca [16 x i8], align 8
  %_125 = alloca [48 x i8], align 8
  %result2 = alloca [4 x i8], align 4
  %result1 = alloca [4 x i8], align 4
  %_120 = alloca [48 x i8], align 8
  %_117 = alloca [48 x i8], align 8
  %_114 = alloca [16 x i8], align 8
  %_113 = alloca [16 x i8], align 8
  %_110 = alloca [48 x i8], align 8
  %_107 = alloca [16 x i8], align 8
  %_106 = alloca [16 x i8], align 8
  %_103 = alloca [48 x i8], align 8
  %_100 = alloca [16 x i8], align 8
  %_99 = alloca [16 x i8], align 8
  %_96 = alloca [48 x i8], align 8
  %_93 = alloca [16 x i8], align 8
  %_92 = alloca [16 x i8], align 8
  %_89 = alloca [48 x i8], align 8
  %inner_var2 = alloca [4 x i8], align 4
  %deep_var = alloca [4 x i8], align 4
  %_84 = alloca [16 x i8], align 8
  %_83 = alloca [16 x i8], align 8
  %_80 = alloca [48 x i8], align 8
  %_77 = alloca [16 x i8], align 8
  %_76 = alloca [16 x i8], align 8
  %_73 = alloca [48 x i8], align 8
  %outer_var1 = alloca [4 x i8], align 4
  %inner_var = alloca [4 x i8], align 4
  %_68 = alloca [16 x i8], align 8
  %_67 = alloca [16 x i8], align 8
  %_64 = alloca [48 x i8], align 8
  %outer_var = alloca [4 x i8], align 4
  %_60 = alloca [48 x i8], align 8
  %_57 = alloca [48 x i8], align 8
  %_54 = alloca [16 x i8], align 8
  %_53 = alloca [16 x i8], align 8
  %_50 = alloca [48 x i8], align 8
  %_47 = alloca [16 x i8], align 8
  %_46 = alloca [16 x i8], align 8
  %_43 = alloca [48 x i8], align 8
  %main_name = alloca [16 x i8], align 8
  %main_var = alloca [4 x i8], align 4
  %_38 = alloca [48 x i8], align 8
  %_35 = alloca [48 x i8], align 8
  %_33 = alloca [1 x i8], align 1
  %_31 = alloca [4 x i8], align 4
  %_29 = alloca [16 x i8], align 8
  %_28 = alloca [16 x i8], align 8
  %_25 = alloca [48 x i8], align 8
  %_22 = alloca [16 x i8], align 8
  %_21 = alloca [16 x i8], align 8
  %_18 = alloca [48 x i8], align 8
  %_15 = alloca [16 x i8], align 8
  %_14 = alloca [16 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_2e122ca7024385f19d6e450b7e6ce334)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_e49b15e535f1e4588b410e976ff209ac)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_15, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
  %0 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_14, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 8 %_15, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_d04ae1e4e47e2edaacf420d40182da21, ptr align 8 %_14)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf3d6b28e3bfa00e3E(ptr sret([16 x i8]) align 8 %_22, ptr align 8 @alloc_8e083c548d5e7235d59b358a9d384b33)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_21, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_22, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_18, ptr align 8 @alloc_421f615f12d1b4228495ec26aaaf3a3b, ptr align 8 %_21)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_18)
  store i8 0, ptr %_33, align 1
  %2 = load i8, ptr %_33, align 1
; call core::sync::atomic::AtomicI32::load
  %3 = call i32 @_ZN4core4sync6atomic9AtomicI324load17he315e1e975863d4eE(ptr align 4 @_ZN18_16_variable_scope14GLOBAL_COUNTER17h7370b5782adfe545E, i8 %2)
  store i32 %3, ptr %_31, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_29, ptr align 4 %_31)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_28, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_29, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_25, ptr align 8 @alloc_643e2b97687f8a57515123a227479277, ptr align 8 %_28)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_25)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_35, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_35)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_38, ptr align 8 @alloc_26c90648c8b9d75a8a20faae9e01ca00)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_38)
  store i32 42, ptr %main_var, align 4
  store ptr @alloc_e9b689b9b1d81f0cb1d1fc7d7058b063, ptr %main_name, align 8
  %5 = getelementptr inbounds i8, ptr %main_name, i64 8
  store i64 13, ptr %5, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_47, ptr align 4 %main_var)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_46, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_47, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_43, ptr align 8 @alloc_92a429fe4e38f1b486dd85808f707e98, ptr align 8 %_46)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_43)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf3d6b28e3bfa00e3E(ptr sret([16 x i8]) align 8 %_54, ptr align 8 %main_name)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_53, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_54, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_50, ptr align 8 @alloc_78fbcc61e1453380b829f0c5144017e6, ptr align 8 %_53)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_50)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_57, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_57)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_60, ptr align 8 @alloc_b5cc11a185758184293e41f368ac7947)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_60)
  store i32 10, ptr %outer_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_68, ptr align 4 %outer_var)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_67, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_68, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_64, ptr align 8 @alloc_e7625a77f09c9d0e667d6c6294f339b3, ptr align 8 %_67)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_64)
  store i32 20, ptr %inner_var, align 4
  store i32 30, ptr %outer_var1, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_77, ptr align 4 %inner_var)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_76, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_77, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_73, ptr align 8 @alloc_044884425a8194d8a6b615b3ebd27f70, ptr align 8 %_76)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_73)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_84, ptr align 4 %outer_var1)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_83, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_84, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_80, ptr align 8 @alloc_11c05c4e6e8267671992c539c92cf6ee, ptr align 8 %_83)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_80)
  store i32 40, ptr %deep_var, align 4
  store i32 50, ptr %inner_var2, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_93, ptr align 4 %deep_var)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_92, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_93, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_89, ptr align 8 @alloc_67c894db437aff573c27f8a3ddf8b1ed, ptr align 8 %_92)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_89)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_100, ptr align 4 %inner_var2)
  %12 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_99, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %12, ptr align 8 %_100, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_96, ptr align 8 @alloc_6553075b7b0ba76ec311e19aa09bd046, ptr align 8 %_99)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_96)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_107, ptr align 4 %inner_var)
  %13 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_106, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %13, ptr align 8 %_107, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_103, ptr align 8 @alloc_a75039ce18484cd18938e750c1bde565, ptr align 8 %_106)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_103)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_114, ptr align 4 %outer_var)
  %14 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_113, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %14, ptr align 8 %_114, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_110, ptr align 8 @alloc_104198c2ddf9d0915b6174eb0a52b822, ptr align 8 %_113)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_110)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_117, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_117)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_120, ptr align 8 @alloc_fc3e0a4aa6481b0a327bfd8c7e8df86f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_120)
; call _16_variable_scope::function_with_local_vars
  %15 = call i32 @_ZN18_16_variable_scope24function_with_local_vars17he0f71551c0a0c94aE(i32 5)
  store i32 %15, ptr %result1, align 4
; call _16_variable_scope::function_with_parameters
  %16 = call i32 @_ZN18_16_variable_scope24function_with_parameters17hd756b39657f88c57E(i32 10, i32 15)
  store i32 %16, ptr %result2, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_129, ptr align 4 %result1)
  %17 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_128, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %17, ptr align 8 %_129, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_125, ptr align 8 @alloc_f64ab8344aaa5cfd4a9e2e5ea1fbc342, ptr align 8 %_128)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_125)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_136, ptr align 4 %result2)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_135, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_136, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_132, ptr align 8 @alloc_2a6d1c634f33869256db46f662bba0d4, ptr align 8 %_135)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_132)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_139, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_139)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_142, ptr align 8 @alloc_8017d36ac04f36868653653404ba0c00)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_142)
  store i32 5, ptr %x, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_150, ptr align 4 %x)
  %19 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_149, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %19, ptr align 8 %_150, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_146, ptr align 8 @alloc_32f86cc4dc7437e85985d529699c7200, ptr align 8 %_149)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_146)
  %20 = load i32, ptr %x, align 4
  %21 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %20, i32 1)
  %_153.0 = extractvalue { i32, i1 } %21, 0
  %_153.1 = extractvalue { i32, i1 } %21, 1
  br i1 %_153.1, label %panic, label %bb71

bb71:                                             ; preds = %start
  store i32 %_153.0, ptr %x3, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_159, ptr align 4 %x3)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_158, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_159, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_155, ptr align 8 @alloc_b50235274a3a1231833366a232f2b3ea, ptr align 8 %_158)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_155)
  %23 = load i32, ptr %x3, align 4
  %24 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %23, i32 2)
  %_162.0 = extractvalue { i32, i1 } %24, 0
  %_162.1 = extractvalue { i32, i1 } %24, 1
  br i1 %_162.1, label %panic6, label %bb75

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f2d52a20d5c7f2495a1964fa80bdf04f) #16
  unreachable

bb75:                                             ; preds = %bb71
  store i32 %_162.0, ptr %x4, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_168, ptr align 4 %x4)
  %25 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_167, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %25, ptr align 8 %_168, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_164, ptr align 8 @alloc_d231c6678e007c7c3d19c38a4b850aa6, ptr align 8 %_167)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_164)
  store ptr @alloc_3edef0b68cfa9c8c95e6d4fe1a68842b, ptr %x5, align 8
  %26 = getelementptr inbounds i8, ptr %x5, i64 8
  store i64 5, ptr %26, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17hf3d6b28e3bfa00e3E(ptr sret([16 x i8]) align 8 %_176, ptr align 8 %x5)
  %27 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_175, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %27, ptr align 8 %_176, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_172, ptr align 8 @alloc_b924672bd40e7732a3f940ec48f7c8e0, ptr align 8 %_175)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_172)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_183, ptr align 4 %x4)
  %28 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_182, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %28, ptr align 8 %_183, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_179, ptr align 8 @alloc_4b5c98f361b708459fc443066acc30bc, ptr align 8 %_182)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_179)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_186, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_186)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_189, ptr align 8 @alloc_f58c61cfaa81d4f44a13c3399ecfa0c9)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_189)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17ha8939ddc1d597df1E"(ptr sret([12 x i8]) align 4 %_192, i32 1, i32 3)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h1cbea0a0df6f15f7E"(ptr sret([12 x i8]) align 4 %_191, ptr align 4 %_192)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter, ptr align 4 %_191, i64 12, i1 false)
  br label %bb91

panic6:                                           ; preds = %bb71
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_4d4e7212953f55af617663444ecc4cf8) #16
  unreachable

bb91:                                             ; preds = %bb102, %bb96, %bb75
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %29 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17hd96b0c94b4bdb72bE"(ptr align 4 %iter)
  %30 = extractvalue { i32, i32 } %29, 0
  %31 = extractvalue { i32, i32 } %29, 1
  store i32 %30, ptr %_194, align 4
  %32 = getelementptr inbounds i8, ptr %_194, i64 4
  store i32 %31, ptr %32, align 4
  %33 = load i32, ptr %_194, align 4
  %34 = getelementptr inbounds i8, ptr %_194, i64 4
  %35 = load i32, ptr %34, align 4
  %_196 = zext i32 %33 to i64
  %36 = trunc nuw i64 %_196 to i1
  br i1 %36, label %bb94, label %bb95

bb94:                                             ; preds = %bb91
  %37 = getelementptr inbounds i8, ptr %_194, i64 4
  %38 = load i32, ptr %37, align 4
  store i32 %38, ptr %i, align 4
  %39 = load i32, ptr %i, align 4
  %40 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %39, i32 10)
  %_199.0 = extractvalue { i32, i1 } %40, 0
  %_199.1 = extractvalue { i32, i1 } %40, 1
  br i1 %_199.1, label %panic18, label %bb96

bb95:                                             ; preds = %bb91
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_219, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_219)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_222, ptr align 8 @alloc_2b363f8247b644341e7dcd35f32ca446)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_222)
  store i32 1, ptr %counter, align 4
  br label %bb109

bb109:                                            ; preds = %bb116, %bb95
  %_226 = load i32, ptr %counter, align 4
  %_225 = icmp sle i32 %_226, 3
  br i1 %_225, label %bb110, label %bb117

bb117:                                            ; preds = %bb109
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_245, ptr align 4 %counter)
  %41 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_244, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %41, ptr align 8 %_245, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_241, ptr align 8 @alloc_bd2b581d13fa2f7e68794a0ffb6584e1, ptr align 8 %_244)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_241)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_248, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_248)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_251, ptr align 8 @alloc_495687b9bdba10c7956e5ad791751577)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_251)
  br label %bb125

bb110:                                            ; preds = %bb109
  %_228 = load i32, ptr %counter, align 4
  %42 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %_228, i32 5)
  %_229.0 = extractvalue { i32, i1 } %42, 0
  %_229.1 = extractvalue { i32, i1 } %42, 1
  br i1 %_229.1, label %panic16, label %bb111

bb125:                                            ; preds = %bb117
  store i32 100, ptr %if_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_260, ptr align 4 %if_var)
  %43 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_259, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %43, ptr align 8 %_260, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_256, ptr align 8 @alloc_48ae43122f80692181a21e07b4480978, ptr align 8 %_259)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_256)
  %44 = load i32, ptr %if_var, align 4
  %_262 = icmp sgt i32 %44, 50
  br i1 %_262, label %bb129, label %bb136

bb136:                                            ; preds = %bb130, %bb125
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_281, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_281)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_284, ptr align 8 @alloc_5460c416d36a15b165ebebe939b46e1a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_284)
  br label %bb146

bb129:                                            ; preds = %bb125
  %45 = load i32, ptr %if_var, align 4
  %46 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %45, i32 2)
  %_264.0 = extractvalue { i32, i1 } %46, 0
  %_264.1 = extractvalue { i32, i1 } %46, 1
  br i1 %_264.1, label %panic7, label %bb130

bb130:                                            ; preds = %bb129
  store i32 %_264.0, ptr %nested_if_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_270, ptr align 4 %nested_if_var)
  %47 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_269, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %47, ptr align 8 %_270, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_266, ptr align 8 @alloc_72ae7a3d4720b5560259dd0d0a2c98e4, ptr align 8 %_269)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_266)
  br label %bb136

panic7:                                           ; preds = %bb129
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_e0a9f64f9f48192b52592886f3e77214) #16
  unreachable

bb146:                                            ; preds = %bb136
  br label %bb143

bb143:                                            ; preds = %bb146
  br label %bb145

bb145:                                            ; preds = %bb143
  br label %bb144

bb144:                                            ; preds = %bb145
  %48 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 42, i32 3)
  %_301.0 = extractvalue { i32, i1 } %48, 0
  %_301.1 = extractvalue { i32, i1 } %48, 1
  br i1 %_301.1, label %panic9, label %bb150

bb141:                                            ; No predecessors!
  %49 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 42, i32 4)
  %_310.0 = extractvalue { i32, i1 } %49, 0
  %_310.1 = extractvalue { i32, i1 } %49, 1
  br i1 %_310.1, label %panic8, label %bb153

bb153:                                            ; preds = %bb141
  store i32 %_310.0, ptr %large_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_316, ptr align 4 %large_var)
  %50 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_315, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %50, ptr align 8 %_316, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_312, ptr align 8 @alloc_8650e67de762b18d7022da51805c5671, ptr align 8 %_315)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_312)
  br label %bb156

panic8:                                           ; preds = %bb141
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_79b094f2859b927a299be2ca51031e58) #16
  unreachable

bb156:                                            ; preds = %bb147, %bb150, %bb153
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_319, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_319)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_322, ptr align 8 @alloc_db123cd1c347d8d8c8cebb462a2a3bc7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_322)
  store i32 10, ptr %mutable_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_330, ptr align 4 %mutable_var)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_329, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_330, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_326, ptr align 8 @alloc_65264418fffcdcb55b3dac18118923ee, ptr align 8 %_329)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_326)
  %52 = load i32, ptr %mutable_var, align 4
  %53 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %52, i32 5)
  %_332.0 = extractvalue { i32, i1 } %53, 0
  %_332.1 = extractvalue { i32, i1 } %53, 1
  br i1 %_332.1, label %panic11, label %bb164

bb150:                                            ; preds = %bb144
  store i32 %_301.0, ptr %medium_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_307, ptr align 4 %medium_var)
  %54 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_306, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %54, ptr align 8 %_307, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_303, ptr align 8 @alloc_6572ab024c22f78f49eba77b6aeb0a7b, ptr align 8 %_306)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_303)
  br label %bb156

panic9:                                           ; preds = %bb144
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_6be91fbe26d7970cc722c77869a3f1d2) #16
  unreachable

bb142:                                            ; No predecessors!
  %55 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 42, i32 2)
  %_292.0 = extractvalue { i32, i1 } %55, 0
  %_292.1 = extractvalue { i32, i1 } %55, 1
  br i1 %_292.1, label %panic10, label %bb147

bb147:                                            ; preds = %bb142
  store i32 %_292.0, ptr %small_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_298, ptr align 4 %small_var)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_297, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_298, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_294, ptr align 8 @alloc_1d77c2437e7de12f8c6161720a6ac2f2, ptr align 8 %_297)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_294)
  br label %bb156

panic10:                                          ; preds = %bb142
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_12f88efad98258d7e5c9c8c86ff95e1c) #16
  unreachable

bb164:                                            ; preds = %bb156
  store i32 %_332.0, ptr %mutable_var, align 4
  store i32 20, ptr %local_mutable, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_339, ptr align 4 %mutable_var)
  %57 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_338, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %57, ptr align 8 %_339, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_335, ptr align 8 @alloc_a1296dda46b749b243acfda47e4a0f6a, ptr align 8 %_338)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_335)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_346, ptr align 4 %local_mutable)
  %58 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_345, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %58, ptr align 8 %_346, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_342, ptr align 8 @alloc_f444c128805b5b4f2959d5a8ceb6ccb7, ptr align 8 %_345)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_342)
  %59 = load i32, ptr %local_mutable, align 4
  %60 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %59, i32 2)
  %_348.0 = extractvalue { i32, i1 } %60, 0
  %_348.1 = extractvalue { i32, i1 } %60, 1
  br i1 %_348.1, label %panic12, label %bb171

panic11:                                          ; preds = %bb156
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_62006209cf2f4f87384b2650f309bf2b) #16
  unreachable

bb171:                                            ; preds = %bb164
  store i32 %_348.0, ptr %local_mutable, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_354, ptr align 4 %local_mutable)
  %61 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_353, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %61, ptr align 8 %_354, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_350, ptr align 8 @alloc_47fd14502feda2184f1c91a89b0b0573, ptr align 8 %_353)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_350)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_361, ptr align 4 %mutable_var)
  %62 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_360, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %62, ptr align 8 %_361, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_357, ptr align 8 @alloc_64f40a2d459f3f3a399c67d559f58634, ptr align 8 %_360)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_357)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_364, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_364)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_367, ptr align 8 @alloc_cf383e772beb995630a83ded4b7769ba)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_367)
  store i32 25, ptr %shared_value, align 4
  %63 = load i32, ptr %shared_value, align 4
; call _16_variable_scope::access_global_and_parameter
  %64 = call i32 @_ZN18_16_variable_scope27access_global_and_parameter17h5e91df10f6bbe711E(i32 %63)
  store i32 %64, ptr %result_a, align 4
; call _16_variable_scope::modify_global_counter
  %65 = call i32 @_ZN18_16_variable_scope21modify_global_counter17hd9d48bd7cb65b78dE()
  store i32 %65, ptr %result_b, align 4
  %66 = load i32, ptr %shared_value, align 4
  %67 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %66, i32 2)
  %_374.0 = extractvalue { i32, i1 } %67, 0
  %_374.1 = extractvalue { i32, i1 } %67, 1
  br i1 %_374.1, label %panic13, label %bb184

panic12:                                          ; preds = %bb164
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_d3fedfb92b46d646f06d31ac2caba070) #16
  unreachable

bb184:                                            ; preds = %bb171
; call _16_variable_scope::access_global_and_parameter
  %68 = call i32 @_ZN18_16_variable_scope27access_global_and_parameter17h5e91df10f6bbe711E(i32 %_374.0)
  store i32 %68, ptr %result_c, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_380, ptr align 4 %shared_value)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_382, ptr align 4 %result_a)
  %69 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_379, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %69, ptr align 8 %_380, i64 16, i1 false)
  %70 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_379, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %70, ptr align 8 %_382, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_376, ptr align 8 @alloc_f7583f65710ab9b8dbd8c5a922bee132, ptr align 8 %_379)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_376)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_389, ptr align 4 %result_b)
  %71 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_388, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %71, ptr align 8 %_389, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_385, ptr align 8 @alloc_b3fbc5a35a6df3c84b7adcd1c6f3f0d8, ptr align 8 %_388)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_385)
  %72 = load i32, ptr %shared_value, align 4
  %73 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %72, i32 2)
  %_399.0 = extractvalue { i32, i1 } %73, 0
  %_399.1 = extractvalue { i32, i1 } %73, 1
  br i1 %_399.1, label %panic14, label %bb193

panic13:                                          ; preds = %bb171
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_8ef65a83c3e7a4b487210a7f65c07ad9) #16
  unreachable

bb193:                                            ; preds = %bb184
  store i32 %_399.0, ptr %_398, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_396, ptr align 4 %_398)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_400, ptr align 4 %result_c)
  %74 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_395, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %74, ptr align 8 %_396, i64 16, i1 false)
  %75 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_395, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %75, ptr align 8 %_400, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_392, ptr align 8 @alloc_f7583f65710ab9b8dbd8c5a922bee132, ptr align 8 %_395)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_392)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_403, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_403)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_406, ptr align 8 @alloc_9f5649251a01542903117518f075105f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_406)
  store i32 100, ptr %closure_outer, align 4
  store ptr %closure_outer, ptr %closure, align 8
; call _16_variable_scope::main::{{closure}}
  %76 = call i32 @"_ZN18_16_variable_scope4main28_$u7b$$u7b$closure$u7d$$u7d$17hecf85955b90d868dE"(ptr align 8 %closure, i32 25)
  store i32 %76, ptr %closure_result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_419, ptr align 4 %closure_result)
  %77 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_418, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %77, ptr align 8 %_419, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_415, ptr align 8 @alloc_baa63513cf120e966e81823cafda7c05, ptr align 8 %_418)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_415)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_422, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_422)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_425, ptr align 8 @alloc_d75c11794d48fe796a645a4ca286c5ed)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_425)
; call _16_variable_scope::main::outer_function
  %78 = call i32 @_ZN18_16_variable_scope4main14outer_function17h378176e26ec90296E(i32 5)
  store i32 %78, ptr %nested_result, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_433, ptr align 4 %nested_result)
  %79 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_432, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %79, ptr align 8 %_433, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_429, ptr align 8 @alloc_7e8499fe8323416714e153409bccaa2f, ptr align 8 %_432)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_429)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_436, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_436)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_439, ptr align 8 @alloc_dc054de15f2b15832c0c966fc763122d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_439)
; call _16_variable_scope::safe_division
  call void @_ZN18_16_variable_scope13safe_division17h4f760fcda4a99a65E(ptr sret([24 x i8]) align 8 %division_result, i32 10, i32 2)
  %80 = load i64, ptr %division_result, align 8
  %81 = icmp eq i64 %80, -9223372036854775808
  %_442 = select i1 %81, i64 0, i64 1
  %82 = trunc nuw i64 %_442 to i1
  br i1 %82, label %bb219, label %bb220

panic14:                                          ; preds = %bb184
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_ac16e988bd464ba4b0fa6ebac798af23) #16
  unreachable

bb219:                                            ; preds = %bb193
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %error, ptr align 8 %division_result, i64 24, i1 false)
  store ptr @alloc_0db46ac119ababfa0e6ea23184ef2ad8, ptr %error_var, align 8
  %83 = getelementptr inbounds i8, ptr %error_var, i64 8
  store i64 15, ptr %83, align 8
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17h14190f8e616dc727E(ptr sret([16 x i8]) align 8 %_462, ptr align 8 %error)
          to label %bb225 unwind label %funclet_bb234

bb220:                                            ; preds = %bb193
  %84 = getelementptr inbounds i8, ptr %division_result, i64 8
  %85 = load i32, ptr %84, align 8
  store i32 %85, ptr %result, align 4
  %86 = load i32, ptr %result, align 4
  %87 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %86, i32 2)
  %_445.0 = extractvalue { i32, i1 } %87, 0
  %_445.1 = extractvalue { i32, i1 } %87, 1
  br i1 %_445.1, label %panic15, label %bb221

bb221:                                            ; preds = %bb220
  store i32 %_445.0, ptr %success_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_451, ptr align 4 %result)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_453, ptr align 4 %success_var)
  %88 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_450, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %88, ptr align 8 %_451, i64 16, i1 false)
  %89 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_450, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %89, ptr align 8 %_453, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_447, ptr align 8 @alloc_29dedfaa3cf0bd649f425feb3aff04f9, ptr align 8 %_450)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_447)
  br label %bb229

panic15:                                          ; preds = %bb220
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_d503103f6a201686f8c3db070fc15723) #16
  unreachable

bb229:                                            ; preds = %bb228, %bb221
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_467, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_467)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_470, ptr align 8 @alloc_231ceedba1372284a0eca5b18f7db3f0)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_470)
  ret void

bb234:                                            ; preds = %funclet_bb234
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hbd10d7b9ac0f779dE"(ptr align 8 %error) #19 [ "funclet"(token %cleanuppad) ]
  cleanupret from %cleanuppad unwind to caller

funclet_bb234:                                    ; preds = %bb227, %bb226, %bb225, %bb219
  %cleanuppad = cleanuppad within none []
  br label %bb234

bb225:                                            ; preds = %bb219
; invoke core::fmt::rt::Argument::new_display
  invoke void @_ZN4core3fmt2rt8Argument11new_display17hf3d6b28e3bfa00e3E(ptr sret([16 x i8]) align 8 %_464, ptr align 8 %error_var)
          to label %bb226 unwind label %funclet_bb234

bb226:                                            ; preds = %bb225
  %90 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_461, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %90, ptr align 8 %_462, i64 16, i1 false)
  %91 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_461, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %91, ptr align 8 %_464, i64 16, i1 false)
; invoke core::fmt::Arguments::new_v1
  invoke void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_458, ptr align 8 @alloc_f9b4a68bc48823bd6b3fc0cf850db76f, ptr align 8 %_461)
          to label %bb227 unwind label %funclet_bb234

bb227:                                            ; preds = %bb226
; invoke std::io::stdio::_print
  invoke void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_458)
          to label %bb228 unwind label %funclet_bb234

bb228:                                            ; preds = %bb227
; call core::ptr::drop_in_place<alloc::string::String>
  call void @"_ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hbd10d7b9ac0f779dE"(ptr align 8 %error)
  br label %bb229

bb111:                                            ; preds = %bb110
  store i32 %_229.0, ptr %while_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_235, ptr align 4 %counter)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_237, ptr align 4 %while_var)
  %92 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_234, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %92, ptr align 8 %_235, i64 16, i1 false)
  %93 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_234, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %93, ptr align 8 %_237, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_231, ptr align 8 @alloc_cec7cd3e67e359fe0f057aaf267a9c78, ptr align 8 %_234)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_231)
  %94 = load i32, ptr %counter, align 4
  %95 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %94, i32 1)
  %_239.0 = extractvalue { i32, i1 } %95, 0
  %_239.1 = extractvalue { i32, i1 } %95, 1
  br i1 %_239.1, label %panic17, label %bb116

panic16:                                          ; preds = %bb110
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_19a26a844a0ac08110f5d3f6e596a55c) #16
  unreachable

bb116:                                            ; preds = %bb111
  store i32 %_239.0, ptr %counter, align 4
  br label %bb109

panic17:                                          ; preds = %bb111
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_47c169422acced6c014f57b063551b57) #16
  unreachable

bb96:                                             ; preds = %bb94
  store i32 %_199.0, ptr %loop_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_205, ptr align 4 %i)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_207, ptr align 4 %loop_var)
  %96 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_204, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %96, ptr align 8 %_205, i64 16, i1 false)
  %97 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_204, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %97, ptr align 8 %_207, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_201, ptr align 8 @alloc_f4a1b6961393d6a04bde3a9449e0d8e3, ptr align 8 %_204)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_201)
  %98 = load i32, ptr %i, align 4
  %99 = icmp eq i32 %98, 2
  br i1 %99, label %bb101, label %bb91

panic18:                                          ; preds = %bb94
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_24e97311129e8ba12f56b71650076da3) #16
  unreachable

bb101:                                            ; preds = %bb96
  %100 = load i32, ptr %i, align 4
  %101 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %100, i32 100)
  %_210.0 = extractvalue { i32, i1 } %101, 0
  %_210.1 = extractvalue { i32, i1 } %101, 1
  br i1 %_210.1, label %panic19, label %bb102

bb102:                                            ; preds = %bb101
  store i32 %_210.0, ptr %conditional_var, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_216, ptr align 4 %conditional_var)
  %102 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_215, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %102, ptr align 8 %_216, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_212, ptr align 8 @alloc_6dfc576ad2b11adcf5128eb10b826c36, ptr align 8 %_215)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_212)
  br label %bb91

panic19:                                          ; preds = %bb101
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_a1e6bc2caffef4f8bd3b49a23710dd67) #16
  unreachable

bb93:                                             ; No predecessors!
  unreachable

bb133:                                            ; No predecessors!
  unreachable

bb134:                                            ; No predecessors!
  unreachable

bb135:                                            ; No predecessors!
  unreachable
}

; _16_variable_scope::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN18_16_variable_scope4main28_$u7b$$u7b$closure$u7d$$u7d$17hecf85955b90d868dE"(ptr align 8 %_1, i32 %0) unnamed_addr #0 {
start:
  %_13 = alloca [16 x i8], align 8
  %_11 = alloca [16 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %closure_inner = alloca [4 x i8], align 4
  %x = alloca [4 x i8], align 4
  store i32 %0, ptr %x, align 4
  store i32 50, ptr %closure_inner, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_9, ptr align 4 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_11, ptr align 4 %closure_inner)
  %_18 = load ptr, ptr %_1, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_13, ptr align 4 %_18)
  %1 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %1, ptr align 8 %_9, i64 16, i1 false)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_11, i64 16, i1 false)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_13, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h006a2f0a25b8566bE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_7281ac873afddb5061cdf48d23c109b3, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  %4 = load i32, ptr %x, align 4
  %5 = load i32, ptr %closure_inner, align 4
  %6 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %4, i32 %5)
  %_15.0 = extractvalue { i32, i1 } %6, 0
  %_15.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_15.1, label %panic, label %bb6

bb6:                                              ; preds = %start
  %_19 = load ptr, ptr %_1, align 8
  %_16 = load i32, ptr %_19, align 4
  %7 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %_15.0, i32 %_16)
  %_17.0 = extractvalue { i32, i1 } %7, 0
  %_17.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_17.1, label %panic1, label %bb7

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_4c14e15fa9df4b9db337f6ec8a01604b) #16
  unreachable

bb7:                                              ; preds = %bb6
  ret i32 %_17.0

panic1:                                           ; preds = %bb6
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_4c14e15fa9df4b9db337f6ec8a01604b) #16
  unreachable
}

; _16_variable_scope::main::outer_function
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope4main14outer_function17h378176e26ec90296E(i32 %0) unnamed_addr #1 {
start:
  %_11 = alloca [16 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [32 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %outer_local = alloca [4 x i8], align 4
  %param = alloca [4 x i8], align 4
  store i32 %0, ptr %param, align 4
  %1 = load i32, ptr %param, align 4
  %2 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %1, i32 2)
  %_3.0 = extractvalue { i32, i1 } %2, 0
  %_3.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  store i32 %_3.0, ptr %outer_local, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_9, ptr align 4 %param)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_11, ptr align 4 %outer_local)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_9, i64 16, i1 false)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_11, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_a1599ad4d3f94d40542ab150a9fbb80e, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  %5 = load i32, ptr %outer_local, align 4
; call _16_variable_scope::main::outer_function::inner_function
  %inner_result = call i32 @_ZN18_16_variable_scope4main14outer_function14inner_function17hdc8fc66f148e1113E(i32 %5)
  %6 = load i32, ptr %outer_local, align 4
  %7 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %6, i32 %inner_result)
  %_14.0 = extractvalue { i32, i1 } %7, 0
  %_14.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_14.1, label %panic1, label %bb7

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_5182fb3fa7fa294ffdd2f989c8f2ca3d) #16
  unreachable

bb7:                                              ; preds = %bb1
  ret i32 %_14.0

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_f843b5cbe670262f421cc94369ab56f1) #16
  unreachable
}

; _16_variable_scope::main::outer_function::inner_function
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope4main14outer_function14inner_function17hdc8fc66f148e1113E(i32 %0) unnamed_addr #1 {
start:
  %_11 = alloca [16 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [32 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %inner_local = alloca [4 x i8], align 4
  %inner_param = alloca [4 x i8], align 4
  store i32 %0, ptr %inner_param, align 4
  %1 = load i32, ptr %inner_param, align 4
  %2 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %1, i32 3)
  %_3.0 = extractvalue { i32, i1 } %2, 0
  %_3.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  store i32 %_3.0, ptr %inner_local, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_9, ptr align 4 %inner_param)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_11, ptr align 4 %inner_local)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_9, i64 16, i1 false)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_11, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_127a87760a1ac6cdaf6995817661e020, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  %_0 = load i32, ptr %inner_local, align 4
  ret i32 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_ae18c16c08f4b3703a75cb29d805d1ae) #16
  unreachable
}

; _16_variable_scope::function_with_local_vars
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope24function_with_local_vars17he0f71551c0a0c94aE(i32 %0) unnamed_addr #1 {
start:
  %_28 = alloca [16 x i8], align 8
  %_27 = alloca [16 x i8], align 8
  %_24 = alloca [48 x i8], align 8
  %_21 = alloca [16 x i8], align 8
  %_20 = alloca [16 x i8], align 8
  %_17 = alloca [48 x i8], align 8
  %_14 = alloca [16 x i8], align 8
  %_13 = alloca [16 x i8], align 8
  %_10 = alloca [48 x i8], align 8
  %_7 = alloca [48 x i8], align 8
  %local_var2 = alloca [4 x i8], align 4
  %local_var1 = alloca [4 x i8], align 4
  %param = alloca [4 x i8], align 4
  store i32 %0, ptr %param, align 4
  %1 = load i32, ptr %param, align 4
  %2 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %1, i32 2)
  %_3.0 = extractvalue { i32, i1 } %2, 0
  %_3.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  store i32 %_3.0, ptr %local_var1, align 4
  %3 = load i32, ptr %local_var1, align 4
  %4 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %3, i32 10)
  %_5.0 = extractvalue { i32, i1 } %4, 0
  %_5.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_5.1, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_16bc2a35c706e93ceba629b350c47248) #16
  unreachable

bb2:                                              ; preds = %bb1
  store i32 %_5.0, ptr %local_var2, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_7, ptr align 8 @alloc_d54a9f1f31c07bb640970b94963adb39)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_7)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_14, ptr align 4 %param)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_13, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_14, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_10, ptr align 8 @alloc_6830c67c40fb122b82219f7e86722bd8, ptr align 8 %_13)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_10)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_21, ptr align 4 %local_var1)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_20, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_21, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_17, ptr align 8 @alloc_08ba367b298d1f91fc1a9cf26810d275, ptr align 8 %_20)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_17)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_28, ptr align 4 %local_var2)
  %7 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_27, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %7, ptr align 8 %_28, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117haeaabc2581ded180E(ptr sret([48 x i8]) align 8 %_24, ptr align 8 @alloc_6687d5239236c2b90fed0f91c77ad493, ptr align 8 %_27)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_24)
  %_0 = load i32, ptr %local_var2, align 4
  ret i32 %_0

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_aa1cb33f8bd119ea10d1cd3d936f3463) #16
  unreachable
}

; _16_variable_scope::function_with_parameters
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope24function_with_parameters17hd756b39657f88c57E(i32 %0, i32 %1) unnamed_addr #1 {
start:
  %_26 = alloca [16 x i8], align 8
  %_24 = alloca [16 x i8], align 8
  %_23 = alloca [32 x i8], align 8
  %_20 = alloca [48 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_15 = alloca [16 x i8], align 8
  %_14 = alloca [32 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %product = alloca [4 x i8], align 4
  %sum = alloca [4 x i8], align 4
  %b = alloca [4 x i8], align 4
  %a = alloca [4 x i8], align 4
  store i32 %0, ptr %a, align 4
  store i32 %1, ptr %b, align 4
  %2 = load i32, ptr %a, align 4
  %3 = load i32, ptr %b, align 4
  %4 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %2, i32 %3)
  %_4.0 = extractvalue { i32, i1 } %4, 0
  %_4.1 = extractvalue { i32, i1 } %4, 1
  br i1 %_4.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  store i32 %_4.0, ptr %sum, align 4
  %5 = load i32, ptr %a, align 4
  %6 = load i32, ptr %b, align 4
  %7 = call { i32, i1 } @llvm.smul.with.overflow.i32(i32 %5, i32 %6)
  %_6.0 = extractvalue { i32, i1 } %7, 0
  %_6.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_6.1, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_c99eaa54d447884d978e8d1badd8c11d) #16
  unreachable

bb2:                                              ; preds = %bb1
  store i32 %_6.0, ptr %product, align 4
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h2cd27f84ceafaaa4E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_125a75e3a3a9823341a5ac87b13afa33)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_15, ptr align 4 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_17, ptr align 4 %b)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_14, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_15, i64 16, i1 false)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_14, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_17, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_b36563b4b0c9a7dd6f070a518d7edcd0, ptr align 8 %_14)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_24, ptr align 4 %sum)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_26, ptr align 4 %product)
  %10 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_23, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %10, ptr align 8 %_24, i64 16, i1 false)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_23, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_26, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_20, ptr align 8 @alloc_22fab347294c4d4ebab351bd77ae584d, ptr align 8 %_23)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_20)
  %12 = load i32, ptr %sum, align 4
  %13 = load i32, ptr %product, align 4
  %14 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %12, i32 %13)
  %_28.0 = extractvalue { i32, i1 } %14, 0
  %_28.1 = extractvalue { i32, i1 } %14, 1
  br i1 %_28.1, label %panic2, label %bb13

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_563b4d6416806b8cfe50e256fe8438b3) #16
  unreachable

bb13:                                             ; preds = %bb2
  ret i32 %_28.0

panic2:                                           ; preds = %bb2
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_a763594125e62e97b582eb09f68d0df2) #16
  unreachable
}

; _16_variable_scope::access_global_and_parameter
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope27access_global_and_parameter17h5e91df10f6bbe711E(i32 %0) unnamed_addr #1 {
start:
  %_13 = alloca [16 x i8], align 8
  %_11 = alloca [16 x i8], align 8
  %_9 = alloca [16 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %local_calc = alloca [4 x i8], align 4
  %param = alloca [4 x i8], align 4
  store i32 %0, ptr %param, align 4
  %1 = load i32, ptr %param, align 4
  %2 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %1, i32 100)
  %_3.0 = extractvalue { i32, i1 } %2, 0
  %_3.1 = extractvalue { i32, i1 } %2, 1
  br i1 %_3.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  store i32 %_3.0, ptr %local_calc, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_9, ptr align 4 %param)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_11, ptr align 4 @alloc_af995923a0f2cfedcdb19bb6362b5829)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_13, ptr align 4 %local_calc)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_9, i64 16, i1 false)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_11, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_8, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_13, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h006a2f0a25b8566bE(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_baccd27eceea2c32120da11051221e55, ptr align 8 %_8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
  %_0 = load i32, ptr %local_calc, align 4
  ret i32 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_90bf980578a1501c0a0dd96771734985) #16
  unreachable
}

; _16_variable_scope::modify_global_counter
; Function Attrs: uwtable
define internal i32 @_ZN18_16_variable_scope21modify_global_counter17hd9d48bd7cb65b78dE() unnamed_addr #1 {
start:
  %_13 = alloca [16 x i8], align 8
  %_11 = alloca [16 x i8], align 8
  %_10 = alloca [32 x i8], align 8
  %_7 = alloca [48 x i8], align 8
  %new_value = alloca [4 x i8], align 4
  %_3 = alloca [1 x i8], align 1
  %old_value = alloca [4 x i8], align 4
  store i8 0, ptr %_3, align 1
  %0 = load i8, ptr %_3, align 1
; call core::sync::atomic::AtomicI32::fetch_add
  %1 = call i32 @_ZN4core4sync6atomic9AtomicI329fetch_add17h5ede85310db6cadfE(ptr align 4 @_ZN18_16_variable_scope14GLOBAL_COUNTER17h7370b5782adfe545E, i32 1, i8 %0)
  store i32 %1, ptr %old_value, align 4
  %2 = load i32, ptr %old_value, align 4
  %3 = call { i32, i1 } @llvm.sadd.with.overflow.i32(i32 %2, i32 1)
  %_5.0 = extractvalue { i32, i1 } %3, 0
  %_5.1 = extractvalue { i32, i1 } %3, 1
  br i1 %_5.1, label %panic, label %bb2

bb2:                                              ; preds = %start
  store i32 %_5.0, ptr %new_value, align 4
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_11, ptr align 4 %old_value)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9e55462cc0d36240E(ptr sret([16 x i8]) align 8 %_13, ptr align 4 %new_value)
  %4 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_10, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %4, ptr align 8 %_11, i64 16, i1 false)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_10, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_13, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h960ad92a44e688aaE(ptr sret([48 x i8]) align 8 %_7, ptr align 8 @alloc_66c7b562be227870196bdea9e506c470, ptr align 8 %_10)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_7)
  %_0 = load i32, ptr %new_value, align 4
  ret i32 %_0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_063ad75f0f8763261f5a47eb05ae5f15) #16
  unreachable
}

; _16_variable_scope::safe_division
; Function Attrs: uwtable
define internal void @_ZN18_16_variable_scope13safe_division17h4f760fcda4a99a65E(ptr sret([24 x i8]) align 8 %_0, i32 %a, i32 %b) unnamed_addr #1 {
start:
  %_3 = alloca [24 x i8], align 8
  %0 = icmp eq i32 %b, 0
  br i1 %0, label %bb1, label %bb3

bb1:                                              ; preds = %start
; call <T as alloc::string::ToString>::to_string
  call void @"_ZN45_$LT$T$u20$as$u20$alloc..string..ToString$GT$9to_string17h463765ffe6c6a0d9E"(ptr sret([24 x i8]) align 8 %_3, ptr align 1 @alloc_36712b18fa80a356639965f9856729fb, i64 16)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 24, i1 false)
  br label %bb6

bb3:                                              ; preds = %start
  %_6 = icmp eq i32 %b, 0
  br i1 %_6, label %panic, label %bb4

bb6:                                              ; preds = %bb5, %bb1
  ret void

bb4:                                              ; preds = %bb3
  %_7 = icmp eq i32 %b, -1
  %_8 = icmp eq i32 %a, -2147483648
  %_9 = and i1 %_7, %_8
  br i1 %_9, label %panic1, label %bb5

panic:                                            ; preds = %bb3
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_64869a62148fb34466b31a6fe6cb3cf2) #16
  unreachable

bb5:                                              ; preds = %bb4
  %_5 = sdiv i32 %a, %b
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i32 %_5, ptr %1, align 8
  store i64 -9223372036854775808, ptr %_0, align 8
  br label %bb6

panic1:                                           ; preds = %bb4
; call core::panicking::panic_const::panic_const_div_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8 @alloc_64869a62148fb34466b31a6fe6cb3cf2) #16
  unreachable
}

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #1

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.sadd.with.overflow.i32(i32, i32) #5

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.ctpop.i64(i64) #5

; core::panicking::panic_cannot_unwind
; Function Attrs: cold minsize noinline noreturn nounwind optsize uwtable
declare void @_ZN4core9panicking19panic_cannot_unwind17hb41e99f0bfbd365aE() unnamed_addr #7

; core::panicking::panic_fmt
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking9panic_fmt17h708ee4fa04e08acdE(ptr align 8, ptr align 8) unnamed_addr #8

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #9

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #10

; core::fmt::num::imp::<impl core::fmt::Display for i32>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17h8bbf4e331d1b87efE"(ptr align 4, ptr align 8) unnamed_addr #1

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #5

; core::alloc::layout::Layout::is_size_align_valid
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core5alloc6layout6Layout19is_size_align_valid17h530c07035deafd90E(i64, i64) unnamed_addr #1

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; __rustc::__rust_alloc_zeroed
; Function Attrs: nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc19___rust_alloc_zeroed(i64, i64 allocalign) unnamed_addr #11

; __rustc::__rust_alloc
; Function Attrs: nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable
declare noalias ptr @_RNvCscSpY9Juk0HT_7___rustc12___rust_alloc(i64, i64 allocalign) unnamed_addr #12

; alloc::raw_vec::handle_error
; Function Attrs: cold minsize noreturn optsize uwtable
declare void @_ZN5alloc7raw_vec12handle_error17h101a059851141b5aE(i64, i64, ptr align 8) unnamed_addr #13

; __rustc::__rust_dealloc
; Function Attrs: nounwind allockind("free") uwtable
declare void @_RNvCscSpY9Juk0HT_7___rustc14___rust_dealloc(ptr allocptr, i64, i64) unnamed_addr #14

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #1

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.smul.with.overflow.i32(i32, i32) #5

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_div_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_div_overflow17hbc9e0db0e49d5a01E(ptr align 8) unnamed_addr #8

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h862eb16398512ee4E(ptr @_ZN18_16_variable_scope4main17h2d337f3a0177b5e8E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { cold minsize noinline noreturn nounwind optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #10 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #11 = { nounwind allockind("alloc,zeroed,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { nounwind allockind("alloc,uninitialized,aligned") allocsize(0) uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #13 = { cold minsize noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #14 = { nounwind allockind("free") uwtable "alloc-family"="__rust_alloc" "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #15 = { nounwind }
attributes #16 = { noreturn }
attributes #17 = { cold noreturn nounwind }
attributes #18 = { noreturn nounwind }
attributes #19 = { cold }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 13075542604411904}
