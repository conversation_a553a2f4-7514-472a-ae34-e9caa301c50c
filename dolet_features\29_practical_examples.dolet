# 29. Practical Examples - Real-World Applications
# This file demonstrates practical programming examples in Dolet

print "=== Practical Examples Demo ==="
print ""

# Calculator application
print "Calculator Application:"

fun calculator(operation, num1, num2) {
    print "Calculator: " + num1 + " " + operation + " " + num2
    
    if operation == "+"
        int result = num1 + num2
        print "  Result: " + result
        return result
    elif operation == "-"
        int result = num1 - num2
        print "  Result: " + result
        return result
    elif operation == "*"
        int result = num1 * num2
        print "  Result: " + result
        return result
    elif operation == "/"
        if num2 != 0
            int result = num1 / num2
            print "  Result: " + result
            return result
        else
            print "  Error: Division by zero"
            return 0
    else
        print "  Error: Unknown operation"
        return 0
}

print "Calculator Examples:"
int calc1 = calculator("+", 15, 7)
int calc2 = calculator("*", 8, 6)
int calc3 = calculator("/", 20, 4)
int calc4 = calculator("/", 10, 0)
print ""

# Grade management system
print "Grade Management System:"

fun calculate_letter_grade(score) {
    if score >= 90
        return "A"
    elif score >= 80
        return "B"
    elif score >= 70
        return "C"
    elif score >= 60
        return "D"
    else
        return "F"
}

fun process_student_grades(name, scores, count) {
    print "Processing grades for: " + name
    
    int total = 0
    for i = 0 to count - 1
        set total = total + scores[i]
        print "  Assignment " + (i + 1) + ": " + scores[i]
    
    float average = total / count
    string letter_grade = calculate_letter_grade(average)
    
    print "  Total: " + total
    print "  Average: " + average
    print "  Letter Grade: " + letter_grade
    
    return letter_grade
}

array int student1_scores = [85, 92, 78, 88, 90]
array int student2_scores = [65, 70, 68, 72, 75]

string grade1 = process_student_grades("Ahmed Ali", student1_scores, 5)
string grade2 = process_student_grades("Sara Hassan", student2_scores, 5)
print ""

# Banking system simulation
print "Banking System Simulation:"

fun create_account(account_number, initial_balance) {
    print "Creating account #" + account_number
    print "  Initial balance: $" + initial_balance
    return initial_balance
}

fun deposit(account_number, current_balance, amount) {
    print "Deposit to account #" + account_number
    print "  Amount: $" + amount
    
    if amount <= 0
        print "  Error: Invalid deposit amount"
        return current_balance
    
    int new_balance = current_balance + amount
    print "  Previous balance: $" + current_balance
    print "  New balance: $" + new_balance
    
    return new_balance
}

fun withdraw(account_number, current_balance, amount) {
    print "Withdrawal from account #" + account_number
    print "  Amount: $" + amount
    
    if amount <= 0
        print "  Error: Invalid withdrawal amount"
        return current_balance
    elif amount > current_balance
        print "  Error: Insufficient funds"
        print "  Available balance: $" + current_balance
        return current_balance
    
    int new_balance = current_balance - amount
    print "  Previous balance: $" + current_balance
    print "  New balance: $" + new_balance
    
    return new_balance
}

int account_123 = create_account(123, 1000)
set account_123 = deposit(123, account_123, 500)
set account_123 = withdraw(123, account_123, 200)
set account_123 = withdraw(123, account_123, 2000)  # Should fail
print ""

# Inventory management
print "Inventory Management System:"

fun add_item(item_name, quantity, price) {
    print "Adding item to inventory:"
    print "  Name: " + item_name
    print "  Quantity: " + quantity
    print "  Price: $" + price
    print "  Total value: $" + (quantity * price)
    
    return quantity
}

fun update_stock(item_name, current_stock, change) {
    print "Updating stock for: " + item_name
    print "  Current stock: " + current_stock
    print "  Change: " + change
    
    int new_stock = current_stock + change
    
    if new_stock < 0
        print "  Warning: Stock would go negative"
        print "  Setting stock to 0"
        set new_stock = 0
    
    print "  New stock: " + new_stock
    
    if new_stock < 5
        print "  Alert: Low stock warning!"
    
    return new_stock
}

fun calculate_inventory_value(items, quantities, prices, count) {
    print "Calculating total inventory value:"
    
    float total_value = 0.0
    
    for i = 0 to count - 1
        float item_value = quantities[i] * prices[i]
        print "  " + items[i] + ": " + quantities[i] + " × $" + prices[i] + " = $" + item_value
        set total_value = total_value + item_value
    
    print "  Total inventory value: $" + total_value
    return total_value
}

int laptop_stock = add_item("Laptop", 10, 1200)
int mouse_stock = add_item("Mouse", 50, 25)

set laptop_stock = update_stock("Laptop", laptop_stock, -3)  # Sold 3
set mouse_stock = update_stock("Mouse", mouse_stock, -48)    # Sold 48 (low stock)

array string inventory_items = ["Laptop", "Mouse", "Keyboard"]
array int inventory_quantities = [7, 2, 15]
array float inventory_prices = [1200.0, 25.0, 75.0]

float total_value = calculate_inventory_value(inventory_items, inventory_quantities, inventory_prices, 3)
print ""

# Text processing utility
print "Text Processing Utility:"

fun word_count(text) {
    print "Counting words in: '" + text + "'"
    
    # Simplified word counting (count spaces + 1)
    int spaces = 0
    int length = 50  # Simulated length
    
    # Simulate counting spaces
    for i = 1 to length
        if i % 6 == 0  # Simulate finding spaces
            set spaces = spaces + 1
    
    int words = spaces + 1
    print "  Word count: " + words
    
    return words
}

fun character_frequency(text, target_char) {
    print "Counting '" + target_char + "' in: '" + text + "'"
    
    int count = 0
    int length = 20  # Simulated length
    
    # Simulate character counting
    for i = 1 to length
        if i % 4 == 0  # Simulate finding target character
            set count = count + 1
    
    print "  Character '" + target_char + "' appears " + count + " times"
    return count
}

fun text_statistics(text) {
    print "Text statistics for: '" + text + "'"
    
    int words = word_count(text)
    int chars = 45  # Simulated character count
    int sentences = 3  # Simulated sentence count
    
    print "  Characters: " + chars
    print "  Words: " + words
    print "  Sentences: " + sentences
    print "  Average word length: " + (chars / words)
}

string sample_text = "This is a sample text for processing and analysis"
text_statistics(sample_text)
int e_count = character_frequency(sample_text, "e")
print ""

# Simple game: Number guessing
print "Number Guessing Game:"

fun number_guessing_game(secret_number, max_attempts) {
    print "Number Guessing Game Started!"
    print "  Secret number: " + secret_number + " (hidden from player)"
    print "  Maximum attempts: " + max_attempts
    
    # Simulate player guesses
    array int guesses = [25, 75, 50, 60, 55]
    
    for attempt = 1 to max_attempts
        int guess = guesses[attempt - 1]
        print "  Attempt " + attempt + ": Player guesses " + guess
        
        if guess == secret_number
            print "    Correct! You won in " + attempt + " attempts!"
            return true
        elif guess < secret_number
            print "    Too low! Try higher."
        else
            print "    Too high! Try lower."
    
    print "  Game over! The secret number was " + secret_number
    return false
}

bool game_result = number_guessing_game(57, 5)
print ""

# Data validation utility
print "Data Validation Utility:"

fun validate_email(email) {
    print "Validating email: '" + email + "'"
    
    if email == ""
        print "  Error: Email is empty"
        return false
    
    # Simplified validation
    bool has_at = (email == "<EMAIL>" or email == "<EMAIL>")
    bool has_dot = has_at  # Simplified
    
    if has_at and has_dot
        print "  Valid email format"
        return true
    else
        print "  Invalid email format"
        return false
}

fun validate_phone(phone) {
    print "Validating phone: '" + phone + "'"
    
    if phone == ""
        print "  Error: Phone is empty"
        return false
    
    # Simplified validation (check length)
    int length = 12  # Simulated length for "************"
    
    if length >= 10 and length <= 15
        print "  Valid phone format"
        return true
    else
        print "  Invalid phone format"
        return false
}

bool email1_valid = validate_email("<EMAIL>")
bool email2_valid = validate_email("invalid-email")
bool phone1_valid = validate_phone("************")
bool phone2_valid = validate_phone("123")
print ""

print "=== End of Practical Examples Demo ==="
