// 16. Variable Scope - local, global, function scope (Rust version)
// This file demonstrates variable scope in Rust for comparison with Dolet

// Global constants (similar to global variables)
static GLOBAL_COUNTER: std::sync::atomic::AtomicI32 = std::sync::atomic::AtomicI32::new(0);
const GLOBAL_CONSTANT: i32 = 100;
const APP_NAME: &str = "Scope Demo";

fn main() {
    println!("=== Variable Scope Demo (Rust) ===");
    println!();

    // Global scope access
    println!("Global scope access:");
    println!("GLOBAL_CONSTANT = {}", GLOBAL_CONSTANT);
    println!("APP_NAME = {}", APP_NAME);
    println!("GLOBAL_COUNTER = {}", GLOBAL_COUNTER.load(std::sync::atomic::Ordering::Relaxed));
    println!();

    // Function scope variables
    println!("Function scope variables:");
    let main_var = 42;
    let main_name = "Main Function";
    
    println!("main_var = {}", main_var);
    println!("main_name = {}", main_name);
    println!();

    // Block scope demonstration
    println!("Block scope demonstration:");
    let outer_var = 10;
    println!("outer_var (before block) = {}", outer_var);
    
    {
        let inner_var = 20;
        let outer_var = 30; // Shadows the outer variable
        println!("  inner_var (inside block) = {}", inner_var);
        println!("  outer_var (inside block, shadowed) = {}", outer_var);
        
        {
            let deep_var = 40;
            let inner_var = 50; // Shadows the block-level variable
            println!("    deep_var (deep block) = {}", deep_var);
            println!("    inner_var (deep block, shadowed) = {}", inner_var);
        }
        // deep_var is no longer accessible here
        println!("  inner_var (back to block level) = {}", inner_var);
    }
    // inner_var is no longer accessible here
    println!("outer_var (after block) = {}", outer_var);
    println!();

    // Function calls and scope
    println!("Function calls and scope:");
    let result1 = function_with_local_vars(5);
    let result2 = function_with_parameters(10, 15);
    
    println!("function_with_local_vars(5) = {}", result1);
    println!("function_with_parameters(10, 15) = {}", result2);
    println!();

    // Variable shadowing
    println!("Variable shadowing:");
    let x = 5;
    println!("x (initial) = {}", x);
    
    let x = x + 1; // Shadow with new value
    println!("x (after shadowing with x + 1) = {}", x);
    
    let x = x * 2; // Shadow again
    println!("x (after shadowing with x * 2) = {}", x);
    
    {
        let x = "Hello"; // Shadow with different type
        println!("  x (inside block, different type) = {}", x);
    }
    println!("x (back to outer scope) = {}", x);
    println!();

    // Loop scope
    println!("Loop scope:");
    for i in 1..=3 {
        let loop_var = i * 10;
        println!("  Loop iteration {}: loop_var = {}", i, loop_var);
        
        if i == 2 {
            let conditional_var = i * 100;
            println!("    conditional_var (i == 2) = {}", conditional_var);
        }
        // conditional_var not accessible here when i != 2
    }
    // i and loop_var not accessible here
    println!();

    // While loop scope
    println!("While loop scope:");
    let mut counter = 1;
    while counter <= 3 {
        let while_var = counter * 5;
        println!("  While iteration {}: while_var = {}", counter, while_var);
        counter += 1;
    }
    // while_var not accessible here
    println!("counter (after while loop) = {}", counter);
    println!();

    // If statement scope
    println!("If statement scope:");
    let condition = true;
    if condition {
        let if_var = 100;
        println!("  if_var (inside if) = {}", if_var);
        
        if if_var > 50 {
            let nested_if_var = if_var * 2;
            println!("    nested_if_var = {}", nested_if_var);
        }
        // nested_if_var not accessible here
    } else {
        let else_var = 200;
        println!("  else_var (inside else) = {}", else_var);
    }
    // if_var and else_var not accessible here
    println!();

    // Match scope
    println!("Match scope:");
    let value = 42;
    match value {
        1..=10 => {
            let small_var = value * 2;
            println!("  Small value: small_var = {}", small_var);
        }
        11..=50 => {
            let medium_var = value * 3;
            println!("  Medium value: medium_var = {}", medium_var);
        }
        _ => {
            let large_var = value * 4;
            println!("  Large value: large_var = {}", large_var);
        }
    }
    // Variables from match arms not accessible here
    println!();

    // Mutable variables and scope
    println!("Mutable variables and scope:");
    let mut mutable_var = 10;
    println!("mutable_var (initial) = {}", mutable_var);
    
    {
        mutable_var += 5; // Modify outer variable
        let mut local_mutable = 20;
        println!("  mutable_var (modified in block) = {}", mutable_var);
        println!("  local_mutable = {}", local_mutable);
        
        local_mutable *= 2;
        println!("  local_mutable (after modification) = {}", local_mutable);
    }
    println!("mutable_var (after block) = {}", mutable_var);
    // local_mutable not accessible here
    println!();

    // Function scope interaction
    println!("Function scope interaction:");
    let shared_value = 25;
    
    let result_a = access_global_and_parameter(shared_value);
    let result_b = modify_global_counter();
    let result_c = access_global_and_parameter(shared_value * 2);
    
    println!("access_global_and_parameter({}) = {}", shared_value, result_a);
    println!("modify_global_counter() = {}", result_b);
    println!("access_global_and_parameter({}) = {}", shared_value * 2, result_c);
    println!();

    // Closure scope
    println!("Closure scope:");
    let closure_outer = 100;
    
    let closure = |x: i32| {
        let closure_inner = 50;
        println!("  Inside closure: x = {}, closure_inner = {}, closure_outer = {}", 
                 x, closure_inner, closure_outer);
        x + closure_inner + closure_outer
    };
    
    let closure_result = closure(25);
    println!("Closure result: {}", closure_result);
    // closure_inner not accessible here
    println!();

    // Nested function scope
    println!("Nested function scope:");
    
    fn outer_function(param: i32) -> i32 {
        let outer_local = param * 2;
        println!("  outer_function: param = {}, outer_local = {}", param, outer_local);
        
        fn inner_function(inner_param: i32) -> i32 {
            let inner_local = inner_param * 3;
            println!("    inner_function: inner_param = {}, inner_local = {}", inner_param, inner_local);
            inner_local
        }
        
        let inner_result = inner_function(outer_local);
        outer_local + inner_result
    }
    
    let nested_result = outer_function(5);
    println!("Nested function result: {}", nested_result);
    println!();

    // Scope with error handling
    println!("Scope with error handling:");
    let division_result = safe_division(10, 2);
    match division_result {
        Ok(result) => {
            let success_var = result * 2;
            println!("  Division successful: result = {}, success_var = {}", result, success_var);
        }
        Err(error) => {
            let error_var = "Division failed";
            println!("  Division error: {}, error_var = {}", error, error_var);
        }
    }
    // success_var and error_var not accessible here
    println!();

    println!("=== End of Variable Scope Demo ===");
}

fn function_with_local_vars(param: i32) -> i32 {
    let local_var1 = param * 2;
    let local_var2 = local_var1 + 10;
    
    println!("  Inside function_with_local_vars:");
    println!("    param = {}", param);
    println!("    local_var1 = {}", local_var1);
    println!("    local_var2 = {}", local_var2);
    
    local_var2
}

fn function_with_parameters(a: i32, b: i32) -> i32 {
    let sum = a + b;
    let product = a * b;
    
    println!("  Inside function_with_parameters:");
    println!("    a = {}, b = {}", a, b);
    println!("    sum = {}, product = {}", sum, product);
    
    sum + product
}

fn access_global_and_parameter(param: i32) -> i32 {
    let local_calc = param + GLOBAL_CONSTANT;
    println!("  access_global_and_parameter: param = {}, GLOBAL_CONSTANT = {}, result = {}", 
             param, GLOBAL_CONSTANT, local_calc);
    local_calc
}

fn modify_global_counter() -> i32 {
    let old_value = GLOBAL_COUNTER.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
    let new_value = old_value + 1;
    println!("  modify_global_counter: old = {}, new = {}", old_value, new_value);
    new_value
}

fn safe_division(a: i32, b: i32) -> Result<i32, String> {
    if b == 0 {
        Err("Division by zero".to_string())
    } else {
        Ok(a / b)
    }
}
