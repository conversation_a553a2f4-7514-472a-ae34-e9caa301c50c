; ModuleID = '21_mathematical_functions.c6cc3f9a0a7b47c9-cgu.0'
source_filename = "21_mathematical_functions.c6cc3f9a0a7b47c9-cgu.0"
target datalayout = "e-m:w-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-pc-windows-msvc"

%"core::fmt::rt::Argument<'_>" = type { %"core::fmt::rt::ArgumentType<'_>" }
%"core::fmt::rt::ArgumentType<'_>" = type { ptr, [1 x i64] }
%"core::fmt::rt::Placeholder" = type { %"core::fmt::rt::Count", %"core::fmt::rt::Count", i64, i32, [1 x i32] }
%"core::fmt::rt::Count" = type { i16, [7 x i16] }

@alloc_f81c2c1bd0b99ce84e80a9469274ae18 = private unnamed_addr constant [91 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\adapters\\enumerate.rs", align 1
@alloc_c80567f23cbf089cfb1ed8514bd745da = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f81c2c1bd0b99ce84e80a9469274ae18, [16 x i8] c"[\00\00\00\00\00\00\00l\00\00\00\11\00\00\00" }>, align 8
@alloc_67987f2acd65c96a8c883ff2ecae97af = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f81c2c1bd0b99ce84e80a9469274ae18, [16 x i8] c"[\00\00\00\00\00\00\001\00\00\00\09\00\00\00" }>, align 8
@vtable.0 = private unnamed_addr constant <{ [24 x i8], ptr, ptr, ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h6cd627a71d5d36c3E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h3a75b735979dc699E", ptr @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h3a75b735979dc699E" }>, align 8
@anon.d686dafb26d9dcf23a3316afed0a3342.0 = private unnamed_addr constant <{ [8 x i8], [8 x i8] }> <{ [8 x i8] zeroinitializer, [8 x i8] undef }>, align 8
@alloc_d9f760e35047ce9b69375ccc90650d26 = private unnamed_addr constant [78 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\range.rs", align 1
@alloc_dda2737f33a4b1c1d39fa523d13a220a = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_d9f760e35047ce9b69375ccc90650d26, [16 x i8] c"N\00\00\00\00\00\00\00\AA\01\00\00\01\00\00\00" }>, align 8
@vtable.1 = private unnamed_addr constant <{ [24 x i8], ptr }> <{ [24 x i8] c"\00\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00\08\00\00\00\00\00\00\00", ptr @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17hd487d5f7e5c22672E" }>, align 8
@alloc_a6a0cc8156fe455996de64a9d05b1dfe = private unnamed_addr constant [184 x i8] c"unsafe precondition(s) violated: u32::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_fc445f6abf67cf4b683577fd3aaed336 = private unnamed_addr constant [184 x i8] c"unsafe precondition(s) violated: u64::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_3e1ebac14318b612ab4efabc52799932 = private unnamed_addr constant [186 x i8] c"unsafe precondition(s) violated: usize::unchecked_add cannot overflow\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_ec595fc0e82ef92fc59bd74f68296eae = private unnamed_addr constant [73 x i8] c"assertion failed: 0 < pointee_size && pointee_size <= isize::MAX as usize", align 1
@alloc_f53323283b17477c36048817d7782669 = private unnamed_addr constant [81 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\ptr\\const_ptr.rs", align 1
@alloc_a58506e252faa4cbf86c6501c99b19f4 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_f53323283b17477c36048817d7782669, [16 x i8] c"Q\00\00\00\00\00\00\00\1D\03\00\00\09\00\00\00" }>, align 8
@alloc_de4e626d456b04760e72bc785ed7e52a = private unnamed_addr constant [201 x i8] c"unsafe precondition(s) violated: ptr::offset_from_unsigned requires `self >= origin`\0A\0AThis indicates a bug in the program. This Undefined Behavior check is optional, and cannot be relied on for safety.", align 1
@alloc_4aead6e2018a46d0df208d5729447de7 = private unnamed_addr constant [27 x i8] c"assertion failed: step != 0", align 1
@alloc_bdb8a431bd51e87cc946baf5a9712b61 = private unnamed_addr constant [89 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\adapters\\step_by.rs", align 1
@alloc_1d19af47b700b7580386eededda18b7b = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_bdb8a431bd51e87cc946baf5a9712b61, [16 x i8] c"Y\00\00\00\00\00\00\00#\00\00\00\09\00\00\00" }>, align 8
@alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8 = private unnamed_addr constant [85 x i8] c"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359\\library\\core\\src\\iter\\traits\\accum.rs", align 1
@alloc_2ff564f83739a041825038989c62f69d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_01c5b12bb62f7fbaf8bfef25b1ee04d8, [16 x i8] c"U\00\00\00\00\00\00\00\95\00\00\00\01\00\00\00" }>, align 8
@alloc_29a39a17deb89fe38ee27280283b87ae = private unnamed_addr constant [43 x i8] c"=== Mathematical Functions Demo (Rust) ===\0A", align 1
@alloc_ac996aed3ca0fd91b77fc28dd06cf980 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_29a39a17deb89fe38ee27280283b87ae, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8
@alloc_49a1e817e911805af64bbc7efb390101 = private unnamed_addr constant [1 x i8] c"\0A", align 1
@alloc_d1a7a28b1b2f6258cb14ac97db016f09 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_d5a30b479e3e3ef387a80df2e2e95b62 = private unnamed_addr constant [24 x i8] c"Mathematical constants:\0A", align 1
@alloc_0e180ec3c8f453ded99202ba432176d8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d5a30b479e3e3ef387a80df2e2e95b62, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_9428dfbeea6535217ef7e81bdfcb9e71 = private unnamed_addr constant [10 x i8] c"\CF\80 (PI) = ", align 1
@alloc_3bf5c39e8f0101c26de409f7eff5cfc2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9428dfbeea6535217ef7e81bdfcb9e71, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8b225e0169c69df7305aca2f83716515 = private unnamed_addr constant [8 x i8] c"\18-DT\FB!\09@", align 8
@alloc_11115e3051de94f9949805ff4b747bff = private unnamed_addr constant [21 x i8] c"e (Euler's number) = ", align 1
@alloc_8833b243aff017acc5586bbceed83924 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_11115e3051de94f9949805ff4b747bff, [8 x i8] c"\15\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_97141a5f2da28e32746b2e87e7a7f02b = private unnamed_addr constant [8 x i8] c"iW\14\8B\0A\BF\05@", align 8
@alloc_80c12f784cce3d28ecb63c3c7d88c7a3 = private unnamed_addr constant [7 x i8] c"\E2\88\9A2 = ", align 1
@alloc_34ab63eff4a60651a28bb4e8ffca4db2 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_80c12f784cce3d28ecb63c3c7d88c7a3, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_097e2602a9650474e7c1161152ec752d = private unnamed_addr constant [8 x i8] c"\CD;\7Ff\9E\A0\F6?", align 8
@alloc_3b65460bf42ffcb795913fba77e80888 = private unnamed_addr constant [8 x i8] c"ln(2) = ", align 1
@alloc_8cf9ed33f9adcff0cf5c95113952c4ed = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3b65460bf42ffcb795913fba77e80888, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a6f9eb080cec2b391d6e8a8179f5772a = private unnamed_addr constant [8 x i8] c"\EF9\FA\FEB.\E6?", align 8
@alloc_dc62f2a0ccba17a894c4128a308f23d9 = private unnamed_addr constant [9 x i8] c"ln(10) = ", align 1
@alloc_35b30c57354b60f1fee559c306f28469 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_dc62f2a0ccba17a894c4128a308f23d9, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4de7c80f515ccd653a5d590e6d349f5f = private unnamed_addr constant [8 x i8] c"\16U\B5\BB\B1k\02@", align 8
@alloc_c5837dd0353d0f14c68e8e3704c7ed3c = private unnamed_addr constant [12 x i8] c"log\E2\82\82(e) = ", align 1
@alloc_935d3d49f31af6a25c4bb01bbd2f6523 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c5837dd0353d0f14c68e8e3704c7ed3c, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c733b3b39552f8613765209c1ff75055 = private unnamed_addr constant [8 x i8] c"\FE\82+eG\15\F7?", align 8
@alloc_076d702c34c4d4c6d1fea26d5c49c3cd = private unnamed_addr constant [15 x i8] c"log\E2\82\81\E2\82\80(e) = ", align 1
@alloc_a3da551cf8a9cb26018bb4733c951b00 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_076d702c34c4d4c6d1fea26d5c49c3cd, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4ea4b122a69e0f5b2ff2c903b0d94e78 = private unnamed_addr constant [8 x i8] c"\0E\E5&\15{\CB\DB?", align 8
@alloc_47add5e9e03d10bf54236397ce883a1d = private unnamed_addr constant [28 x i8] c"Basic arithmetic functions:\0A", align 1
@alloc_11c05ba5bb0a3ed3ac8acb68e0cc77ac = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_47add5e9e03d10bf54236397ce883a1d, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_33218010b5bab0a581ebf98f73b1842d = private unnamed_addr constant [13 x i8] c"Numbers: a = ", align 1
@alloc_b7f5616537b55296b7d87860d9533cd5 = private unnamed_addr constant [6 x i8] c", b = ", align 1
@alloc_2c0bb3029ce4b06fc6333e1f78e43619 = private unnamed_addr constant [6 x i8] c", c = ", align 1
@alloc_6d35e60c1ec09e00a20cd9f2ef9e4752 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_33218010b5bab0a581ebf98f73b1842d, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_b7f5616537b55296b7d87860d9533cd5, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_2c0bb3029ce4b06fc6333e1f78e43619, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c949b46598879f859b9ed07bcde6dbfd = private unnamed_addr constant [9 x i8] c"abs(c) = ", align 1
@alloc_9796d792556dda9195bb6a75c3a5deb1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c949b46598879f859b9ed07bcde6dbfd, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f5fb97b8b76d49db5104aa6e7ed321de = private unnamed_addr constant [10 x i8] c"sqrt(a) = ", align 1
@alloc_8087de6b25049f0ed2258b592c2da366 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f5fb97b8b76d49db5104aa6e7ed321de, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_cbac769b25421cbe54adf4ce9e15ad94 = private unnamed_addr constant [10 x i8] c"cbrt(a) = ", align 1
@alloc_11f874a23d9410d4fdda15f92c239678 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_cbac769b25421cbe54adf4ce9e15ad94, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_acbe4f1da1d1e4b1194341431c9615e4 = private unnamed_addr constant [12 x i8] c"pow(b, 3) = ", align 1
@alloc_f90103e782e5b21d70a4934ba3e0646d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_acbe4f1da1d1e4b1194341431c9615e4, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0645a41e2917d6235245561ca9ffcba4 = private unnamed_addr constant [14 x i8] c"pow(a, 0.5) = ", align 1
@alloc_cd83647424918092aa31a65a610a7224 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0645a41e2917d6235245561ca9ffcba4, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6a15f318736ec1fe146f71b4f10a4ab1 = private unnamed_addr constant [9 x i8] c"exp(1) = ", align 1
@alloc_9a32ae22cf6c7ce4e05c50331c4d5f44 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6a15f318736ec1fe146f71b4f10a4ab1, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_f26017e789e2d70b6acb89f661a0489e = private unnamed_addr constant [8 x i8] c"ln(a) = ", align 1
@alloc_47f31e6a1819b2f566b7bd4a202e21e7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_f26017e789e2d70b6acb89f661a0489e, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ef216b2d3811783f4820f302e0211b2e = private unnamed_addr constant [12 x i8] c"log\E2\82\82(a) = ", align 1
@alloc_b38121c3a762b83276d69425dbed648b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ef216b2d3811783f4820f302e0211b2e, [8 x i8] c"\0C\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1557fea55b0c4f40980b29ce614fc774 = private unnamed_addr constant [15 x i8] c"log\E2\82\81\E2\82\80(a) = ", align 1
@alloc_f93500571669ba6a5a593852bb90258f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_1557fea55b0c4f40980b29ce614fc774, [8 x i8] c"\0F\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_128bf17b713e8e5ca1fc66495c4d3ea7 = private unnamed_addr constant [35 x i8] c"Trigonometric functions (radians):\0A", align 1
@alloc_1ba4d175d10ff4662405533e5e0c44d7 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_128bf17b713e8e5ca1fc66495c4d3ea7, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_67c5654397ba57104684a629f406f10e = private unnamed_addr constant [4 x i8] c"\CF\80/6", align 1
@alloc_4db0be8db4592be42bc4c77d0ffe4304 = private unnamed_addr constant [4 x i8] c"\CF\80/4", align 1
@alloc_6d9f488f4fbdaf418990477dc6cf8a35 = private unnamed_addr constant [4 x i8] c"\CF\80/3", align 1
@alloc_ddc6e154c703a61a437da6f22f30605c = private unnamed_addr constant [4 x i8] c"\CF\80/2", align 1
@alloc_a52aa3f05a7e86d9a79d613359190858 = private unnamed_addr constant [2 x i8] c"\CF\80", align 1
@alloc_dda1ee2b88b89b9cdac753eef7988035 = private unnamed_addr constant [1 x i8] c"0", align 1
@alloc_d69e9a57ea29a22f8251b69857ce0372 = private unnamed_addr constant [35 x i8] c"Trigonometric functions (degrees):\0A", align 1
@alloc_a55670ff7bab2045c7ceba43f5ae59b4 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_d69e9a57ea29a22f8251b69857ce0372, [8 x i8] c"#\00\00\00\00\00\00\00" }>, align 8
@alloc_738cea720c78fcc223ea439d83905baf = private unnamed_addr constant [33 x i8] c"Inverse trigonometric functions:\0A", align 1
@alloc_36e985c447b00f4fb0f971b7a5fdd3d1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_738cea720c78fcc223ea439d83905baf, [8 x i8] c"!\00\00\00\00\00\00\00" }>, align 8
@alloc_f3eb4a50ab924264c57312372b57d4ff = private unnamed_addr constant [22 x i8] c"Hyperbolic functions:\0A", align 1
@alloc_f828f7367503d5788d5676504c301368 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f3eb4a50ab924264c57312372b57d4ff, [8 x i8] c"\16\00\00\00\00\00\00\00" }>, align 8
@alloc_1243cc653c817654c5117f9644386f48 = private unnamed_addr constant [38 x i8] c"Rounding and ceiling/floor functions:\0A", align 1
@alloc_472c1e306a952df758316b3bedb5463a = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_1243cc653c817654c5117f9644386f48, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_6b7eeea7250c1b42c7ec20d538deb200 = private unnamed_addr constant [23 x i8] c"Statistical functions:\0A", align 1
@alloc_7b27168522d9cb74d571dfc76c701777 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_6b7eeea7250c1b42c7ec20d538deb200, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_73c026bd31ee39709360bf833d61efed = private unnamed_addr constant [6 x i8] c"Data: ", align 1
@alloc_488ed7bc1b42104f71c4fea09ee789ef = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_73c026bd31ee39709360bf833d61efed, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5b0102dc258292fd5e83bca8293a8d53 = private unnamed_addr constant [5 x i8] c"Sum: ", align 1
@alloc_fae7b78f3403b48b2870d7d78fcc4226 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5b0102dc258292fd5e83bca8293a8d53, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3007c43f214ba982c891d771b801739f = private unnamed_addr constant [6 x i8] c"Mean: ", align 1
@alloc_26f684b680020ab97826303bd1a73f57 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3007c43f214ba982c891d771b801739f, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_aaa77e12df216f15cb570cdb17ece78d = private unnamed_addr constant [5 x i8] c"Min: ", align 1
@alloc_9049c6b777d9a90bcdeadb5e3fb17782 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_aaa77e12df216f15cb570cdb17ece78d, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_88e48e4235cf742c489e6b854730948e = private unnamed_addr constant [5 x i8] c"Max: ", align 1
@alloc_1b004682d34d6a19e00a486d7d5ded1a = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_88e48e4235cf742c489e6b854730948e, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_51ac2a8b4aa57ba133e6af4352ae8b59 = private unnamed_addr constant [7 x i8] c"Range: ", align 1
@alloc_7a517152eb3d8bddd47643ba929d2459 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_51ac2a8b4aa57ba133e6af4352ae8b59, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_8f254a8bc900ef2fd3cbf1b76402b25b = private unnamed_addr constant [10 x i8] c"Variance: ", align 1
@alloc_55c5f5d5c57369529be9c4c6f581027d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_8f254a8bc900ef2fd3cbf1b76402b25b, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_83e3cbfd19ec7daa73adef79bf8d1367 = private unnamed_addr constant [20 x i8] c"Standard deviation: ", align 1
@alloc_e5fe011c7b5db218d1e9bbca664aae47 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_83e3cbfd19ec7daa73adef79bf8d1367, [8 x i8] c"\14\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_4b9bcd60e16c7723ff8d777ca9091e2e = private unnamed_addr constant [24 x i8] c"Geometric calculations:\0A", align 1
@alloc_a818d64198879d3f38ef5915aa155740 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_4b9bcd60e16c7723ff8d777ca9091e2e, [8 x i8] c"\18\00\00\00\00\00\00\00" }>, align 8
@alloc_391772ef39a2914d8ddd94b796ed39be = private unnamed_addr constant [17 x i8] c"Circle (radius = ", align 1
@alloc_6033e786437337a329f3ee6d9865c487 = private unnamed_addr constant [3 x i8] c"):\0A", align 1
@alloc_07f84e4998fda08dd4ad4803ee463098 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_391772ef39a2914d8ddd94b796ed39be, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_6033e786437337a329f3ee6d9865c487, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_32222c38b7287ba1aefe6cc361a7c3ae = private unnamed_addr constant [21 x i8] c"  Area = \CF\80 \C3\97 r\C2\B2 = ", align 1
@alloc_d7008bd83291e8f3585358147a8a9eed = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_32222c38b7287ba1aefe6cc361a7c3ae, [8 x i8] c"\15\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5e165b85439987b8231499de75c0070b = private unnamed_addr constant [29 x i8] c"  Circumference = 2\CF\80 \C3\97 r = ", align 1
@alloc_6a795f2badd5da71c4f1649b751db989 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5e165b85439987b8231499de75c0070b, [8 x i8] c"\1D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_254acb5dc46350f96b4c3cd86bec70fc = private unnamed_addr constant [11 x i8] c"Rectangle (", align 1
@alloc_d61fae4762085aeb356ac874a9e6e646 = private unnamed_addr constant [2 x i8] c"\C3\97", align 1
@alloc_f57bafb46e0b9795704237b009ffa89e = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_254acb5dc46350f96b4c3cd86bec70fc, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_d61fae4762085aeb356ac874a9e6e646, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_6033e786437337a329f3ee6d9865c487, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_cfa10ea5b03eb64ed9ffb93fa270e2da = private unnamed_addr constant [9 x i8] c"  Area = ", align 1
@alloc_8902b4ffc6aeb681b264c7ffe8887a9f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_cfa10ea5b03eb64ed9ffb93fa270e2da, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_5f67e3d01034b59e19a2c07934e22423 = private unnamed_addr constant [14 x i8] c"  Perimeter = ", align 1
@alloc_b5f48ad89d5e7bd2384e22277cfa38ff = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5f67e3d01034b59e19a2c07934e22423, [8 x i8] c"\0E\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_20bcf84d3102328ef97a63f3569a2910 = private unnamed_addr constant [13 x i8] c"  Diagonal = ", align 1
@alloc_259b93caf139ec0560ee181a4678e6ec = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_20bcf84d3102328ef97a63f3569a2910, [8 x i8] c"\0D\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_7ffef1435a9b2c2365397e1b4ca53ba6 = private unnamed_addr constant [17 x i8] c"Triangle (sides: ", align 1
@alloc_94b00be069aafad82a2c6df764237b82 = private unnamed_addr constant [2 x i8] c", ", align 1
@alloc_f31191e570319b3e3a9f0087287784eb = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7ffef1435a9b2c2365397e1b4ca53ba6, [8 x i8] c"\11\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_6033e786437337a329f3ee6d9865c487, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_b82c6f3528634506f8cec238d5b7b4ea = private unnamed_addr constant [27 x i8] c"  Area (Heron's formula) = ", align 1
@alloc_a03e1dc817e436a38e6c80eb9e7baa76 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b82c6f3528634506f8cec238d5b7b4ea, [8 x i8] c"\1B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_186b94996ada159e20ae9dc2fd9b9c83 = private unnamed_addr constant [38 x i8] c"Distance and coordinate calculations:\0A", align 1
@alloc_ed6d0f770aa3e5114914ca3a7a24069d = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_186b94996ada159e20ae9dc2fd9b9c83, [8 x i8] c"&\00\00\00\00\00\00\00" }>, align 8
@alloc_85bb6fde4206d7afe79030bdfd4c21d6 = private unnamed_addr constant [10 x i8] c"Point 1: (", align 1
@alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b = private unnamed_addr constant [2 x i8] c")\0A", align 1
@alloc_61773b17ed338785b327ee2cd659e084 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_85bb6fde4206d7afe79030bdfd4c21d6, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_10629c9a4990308ca3e6d8d0e94cb0d4 = private unnamed_addr constant [10 x i8] c"Point 2: (", align 1
@alloc_b5a55ecf4705f79ab8fd7c42b5da46b7 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_10629c9a4990308ca3e6d8d0e94cb0d4, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_9f494c60c4ffe58d60a4eed44d6be9a4 = private unnamed_addr constant [10 x i8] c"Distance: ", align 1
@alloc_0e52f25d7a4988b0ae4075e691ad0996 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9f494c60c4ffe58d60a4eed44d6be9a4, [8 x i8] c"\0A\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_15af1a8dcdae6f25cb9b0a68660f948c = private unnamed_addr constant [11 x i8] c"Midpoint: (", align 1
@alloc_ee52372046e04080d4280aa300e84ffc = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_15af1a8dcdae6f25cb9b0a68660f948c, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_cba169e49d2f6a8c1ea9f5f92c42ec6b, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_b93ae923d32cdbaf8e60ea25232c4446 = private unnamed_addr constant [29 x i8] c"Factorial and combinatorics:\0A", align 1
@alloc_326fae74cff293395ec663e598416e8f = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_b93ae923d32cdbaf8e60ea25232c4446, [8 x i8] c"\1D\00\00\00\00\00\00\00" }>, align 8
@alloc_daaf3c172d872ee354fb5386d8d92317 = private unnamed_addr constant [31 x i8] c"Combinations and permutations:\0A", align 1
@alloc_ab08460099930b23cae469bd33629e3e = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_daaf3c172d872ee354fb5386d8d92317, [8 x i8] c"\1F\00\00\00\00\00\00\00" }>, align 8
@alloc_958fa8256726c0f3a54ab1a4fd436a9f = private unnamed_addr constant [28 x i8] c"21_mathematical_functions.rs", align 1
@alloc_fb3ab407f0aaaa7dd62f414a4334e103 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\C1\00\00\00A\00\00\00" }>, align 8
@alloc_69977decc73ded67df62219241e5b08f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\C1\00\00\00'\00\00\00" }>, align 8
@alloc_29bad07221f6bfd3ef53326045497816 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\C1\00\00\00\18\00\00\00" }>, align 8
@alloc_219032e40856f2d193ab98d93a0ac508 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\C2\00\00\001\00\00\00" }>, align 8
@alloc_76f4ee9951a69d675cfc23fbee9011eb = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\C2\00\00\00\18\00\00\00" }>, align 8
@alloc_64362f12071c4245646f8313b76d1d19 = private unnamed_addr constant [2 x i8] c"C(", align 1
@alloc_12470cffb0ed9f3cc12c6066ef64d57b = private unnamed_addr constant [4 x i8] c") = ", align 1
@alloc_fb42756cc3da9fb402ff3bca4b291e2f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_64362f12071c4245646f8313b76d1d19, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_e84691d0e20d24db715449fd6e1ef199 = private unnamed_addr constant [2 x i8] c"P(", align 1
@alloc_e2431136cec9b058bf4d4b5edaed9452 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_e84691d0e20d24db715449fd6e1ef199, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_86892b183312bf04d7349eedf2ac24e1 = private unnamed_addr constant [25 x i8] c"Number theory functions:\0A", align 1
@alloc_a3646448bb21253f7b44414db5c869c1 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_86892b183312bf04d7349eedf2ac24e1, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_9b49f1ceeedffede0f5335fc02b9ef54 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\CD\00\00\00\16\00\00\00" }>, align 8
@alloc_c4c48d0d25cf38792afc3125210f8d1f = private unnamed_addr constant [4 x i8] c"GCD(", align 1
@alloc_d6ae72f098712ee0a3bec19b9e88a961 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c4c48d0d25cf38792afc3125210f8d1f, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_528897b6adb2e019cc1bacd9c70ea3c0 = private unnamed_addr constant [4 x i8] c"LCM(", align 1
@alloc_be67a2b5cd02057df1a373eefa23f7c5 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_528897b6adb2e019cc1bacd9c70ea3c0, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_94b00be069aafad82a2c6df764237b82, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2cbdf5d5bb3948fe316c35b2f74c11e9 = private unnamed_addr constant [23 x i8] c"Prime number checking:\0A", align 1
@alloc_78248e70a48da0c2f27026bc4dcf8c96 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_2cbdf5d5bb3948fe316c35b2f74c11e9, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_bd17cf1ff40e35ed5aac0df56c47297a = private unnamed_addr constant [20 x i8] c"Fibonacci sequence:\0A", align 1
@alloc_69705734b886c9c3bc63f86cd18562a8 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_bd17cf1ff40e35ed5aac0df56c47297a, [8 x i8] c"\14\00\00\00\00\00\00\00" }>, align 8
@alloc_f2d5cf1693a46d1d2aeba639f9faa785 = private unnamed_addr constant [28 x i8] c"First 15 Fibonacci numbers: ", align 1
@alloc_72ad9bff2df52c056a77e4c19ee527b2 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f2d5cf1693a46d1d2aeba639f9faa785, [8 x i8] c"\1C\00\00\00\00\00\00\00" }>, align 8
@alloc_c4298f024a72dfeb53ac738b18aff0e2 = private unnamed_addr constant [27 x i8] c"Complex number operations:\0A", align 1
@alloc_36cfe2095ee3e8ae98b816dfcd9b5d10 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_c4298f024a72dfeb53ac738b18aff0e2, [8 x i8] c"\1B\00\00\00\00\00\00\00" }>, align 8
@alloc_2c98d656d110838972e392b94d27cdcc = private unnamed_addr constant [11 x i8] c"Complex 1: ", align 1
@alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf = private unnamed_addr constant [3 x i8] c" + ", align 1
@alloc_d039542b6328f996f7a3eba7b5b3f054 = private unnamed_addr constant [2 x i8] c"i\0A", align 1
@alloc_e1d007017aac0a4b560ce2e7e6f7966b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2c98d656d110838972e392b94d27cdcc, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_d039542b6328f996f7a3eba7b5b3f054, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_7047173d17c4aa199407747af8c444ff = private unnamed_addr constant [11 x i8] c"Complex 2: ", align 1
@alloc_526391f2c7df527c87d72c4609e5f8fc = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_7047173d17c4aa199407747af8c444ff, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_d039542b6328f996f7a3eba7b5b3f054, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_01088f651774a781dd335dde79938cbd = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_5b0102dc258292fd5e83bca8293a8d53, [8 x i8] c"\05\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_d039542b6328f996f7a3eba7b5b3f054, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_b1f2c95baf3fd0056321c45a348389c5 = private unnamed_addr constant [9 x i8] c"Product: ", align 1
@alloc_1c0a7d4c6841616366ce5f8f1ee10c60 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_b1f2c95baf3fd0056321c45a348389c5, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_7dfda8f4ef3bc65bd5aae61db4a0d2cf, [8 x i8] c"\03\00\00\00\00\00\00\00", ptr @alloc_d039542b6328f996f7a3eba7b5b3f054, [8 x i8] c"\02\00\00\00\00\00\00\00" }>, align 8
@alloc_d1f9899b45b722359ec775b7b9f6889f = private unnamed_addr constant [24 x i8] c"Magnitude of Complex 1: ", align 1
@alloc_8095077786c9cf69c43ccdeffc74a784 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_d1f9899b45b722359ec775b7b9f6889f, [8 x i8] c"\18\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_17f9ef4c87379d76a115172b89f7ffca = private unnamed_addr constant [23 x i8] c"Polynomial evaluation:\0A", align 1
@alloc_37263b8a59b070e89a51e380ade6894b = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_17f9ef4c87379d76a115172b89f7ffca, [8 x i8] c"\17\00\00\00\00\00\00\00" }>, align 8
@alloc_5a5c9164370b7c29e9b0ad77aedd993a = private unnamed_addr constant [25 x i8] c"Polynomial: x\C2\B2 - 3x + 2\0A", align 1
@alloc_ed875bab1bbe78d9454a68e6f93e95cd = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_5a5c9164370b7c29e9b0ad77aedd993a, [8 x i8] c"\19\00\00\00\00\00\00\00" }>, align 8
@alloc_f2952ca7787f88b044e14dac8cab06ec = private unnamed_addr constant [43 x i8] c"=== End of Mathematical Functions Demo ===\0A", align 1
@alloc_c08436ecb0f9274cd3b39b90008ba740 = private unnamed_addr constant <{ ptr, [8 x i8] }> <{ ptr @alloc_f2952ca7787f88b044e14dac8cab06ec, [8 x i8] c"+\00\00\00\00\00\00\00" }>, align 8
@alloc_c4a3ba48f628bda7f4c05843e20da155 = private unnamed_addr constant [2 x i8] c"f(", align 1
@alloc_ba7d6ac60039dabaf730f3ff2aa8203f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_c4a3ba48f628bda7f4c05843e20da155, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_0242e8ee118de705af76c627590b82cc = private unnamed_addr constant [1 x i8] c" ", align 1
@alloc_a0434d70b15bcc9ff1a7b717be16ed72 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_0242e8ee118de705af76c627590b82cc, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_c00089db8c6d93cd11fdcbd101fe006d = private unnamed_addr constant [11 x i8] c" is prime: ", align 1
@alloc_e9606bb5ce363cb1670370982c10dd70 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_c00089db8c6d93cd11fdcbd101fe006d, [8 x i8] c"\0B\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_1116ebb6fe6d9ab4b1291c69131ce050 = private unnamed_addr constant [4 x i8] c"! = ", align 1
@alloc_6f9cb349d3e8afcbce241773548339ce = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr inttoptr (i64 1 to ptr), [8 x i8] zeroinitializer, ptr @alloc_1116ebb6fe6d9ab4b1291c69131ce050, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_22f5e75d688a3d57d7df3384ddeff491 = private unnamed_addr constant [7 x i8] c"Value: ", align 1
@alloc_a37200dd26e9374fe4498e5684023e1f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_22f5e75d688a3d57d7df3384ddeff491, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3cd8b5c09612cc1751f85be4db7b3e04 = private unnamed_addr constant [8 x i8] c"  floor(", align 1
@alloc_8d95ed21c2e5a24bf1bc2ed1aca38be1 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_3cd8b5c09612cc1751f85be4db7b3e04, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_cdd3eda3ceace19fcd012ba7748623c9 = private unnamed_addr constant [7 x i8] c"  ceil(", align 1
@alloc_a7df6d09c48d93e9a2cf66f54b3f145c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_cdd3eda3ceace19fcd012ba7748623c9, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_6289007cdee3fdbf8cfe23336f883add = private unnamed_addr constant [8 x i8] c"  round(", align 1
@alloc_89f8d942b3edf2b06ba8245605994135 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_6289007cdee3fdbf8cfe23336f883add, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ae11c0478ba393b75169e07fbcff280f = private unnamed_addr constant [8 x i8] c"  trunc(", align 1
@alloc_6c5a248a3665501dbe8b4a5d71297fd4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ae11c0478ba393b75169e07fbcff280f, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ac11f0cbaa54bed501a414a4b90e0e57 = private unnamed_addr constant [8 x i8] c"  fract(", align 1
@alloc_06db5496cb6611ff56806ad402991f06 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ac11f0cbaa54bed501a414a4b90e0e57, [8 x i8] c"\08\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_9984af7ea3bd8a1b03e9a5d5c832324f = private unnamed_addr constant [7 x i8] c"  sinh(", align 1
@alloc_0b7e751c8c99a47859511e4cc929fb4c = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_9984af7ea3bd8a1b03e9a5d5c832324f, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_2a8c027dba956671f04ca59db05c4633 = private unnamed_addr constant [7 x i8] c"  cosh(", align 1
@alloc_77d2acbce3cc62e8304e72b64646c979 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_2a8c027dba956671f04ca59db05c4633, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_263ee0ed150dfbe2f7e2eb3ce9693f61 = private unnamed_addr constant [7 x i8] c"  tanh(", align 1
@alloc_33accba464183ee73ddf7cecb62c67c3 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_263ee0ed150dfbe2f7e2eb3ce9693f61, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ceac793e2ffde90d0b98bb75d17b13e9 = private unnamed_addr constant [9 x i8] c"  arcsin(", align 1
@alloc_0900b61137c562c398562b94960bb032 = private unnamed_addr constant [7 x i8] c" rad = ", align 1
@alloc_46ae1d2c94e21b1c3ae0ed408eaa0a91 = private unnamed_addr constant [3 x i8] c"\C2\B0\0A", align 1
@alloc_8374882af40e6553855e7c1b30923341 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ceac793e2ffde90d0b98bb75d17b13e9, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0900b61137c562c398562b94960bb032, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_46ae1d2c94e21b1c3ae0ed408eaa0a91, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_afae6b1ac73874c81b29f466972c5280 = private unnamed_addr constant [9 x i8] c"  arccos(", align 1
@alloc_1b9cf2d56092e546a08becdfdd798753 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_afae6b1ac73874c81b29f466972c5280, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0900b61137c562c398562b94960bb032, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_46ae1d2c94e21b1c3ae0ed408eaa0a91, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_45a389059197cab6a5ad467525adcce8 = private unnamed_addr constant [9 x i8] c"  arctan(", align 1
@alloc_90fea351adab84c834877784f9f58740 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_45a389059197cab6a5ad467525adcce8, [8 x i8] c"\09\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_0900b61137c562c398562b94960bb032, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_46ae1d2c94e21b1c3ae0ed408eaa0a91, [8 x i8] c"\03\00\00\00\00\00\00\00" }>, align 8
@alloc_0a05944ad58b4205f18bcadf34f0c0d5 = private unnamed_addr constant [7 x i8] c"Angle: ", align 1
@alloc_fe83eefb206863ffa53e7b0cbed5a00d = private unnamed_addr constant [4 x i8] c"\C2\B0 (", align 1
@alloc_7f611c855055e83ed2e8134986c70875 = private unnamed_addr constant [6 x i8] c" rad)\0A", align 1
@alloc_fc49891db259ef7e8fb2707b24e5c3be = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0a05944ad58b4205f18bcadf34f0c0d5, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_fe83eefb206863ffa53e7b0cbed5a00d, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_7f611c855055e83ed2e8134986c70875, [8 x i8] c"\06\00\00\00\00\00\00\00" }>, align 8
@alloc_ef4ab0ec5a985c9e5e6159d080f58740 = private unnamed_addr constant [6 x i8] c"  sin(", align 1
@alloc_220d4c06235ffd48770ad0986727a603 = private unnamed_addr constant [6 x i8] c"\C2\B0) = ", align 1
@alloc_29357bdf8eb5317387121192992cdaf4 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ef4ab0ec5a985c9e5e6159d080f58740, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_220d4c06235ffd48770ad0986727a603, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_00fe0f06901ca65f5517e46347ee6896 = private unnamed_addr constant [6 x i8] c"  cos(", align 1
@alloc_cde3d3bb48b3eb39c6dde7d111619b33 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_00fe0f06901ca65f5517e46347ee6896, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_220d4c06235ffd48770ad0986727a603, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_a2c07633baee718f79ac12718616c33b = private unnamed_addr constant [6 x i8] c"  tan(", align 1
@alloc_b7a26d15bead72b7de98f5171de2ef4c = private unnamed_addr constant [16 x i8] c"\C2\B0) = undefined\0A", align 1
@alloc_2920d4143b1f9aff8a75d9b5f67db967 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a2c07633baee718f79ac12718616c33b, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_b7a26d15bead72b7de98f5171de2ef4c, [8 x i8] c"\10\00\00\00\00\00\00\00" }>, align 8
@alloc_eb8225f4a87bb95452a1475f66fc914b = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a2c07633baee718f79ac12718616c33b, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_220d4c06235ffd48770ad0986727a603, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_91d16892003a5ae3285cccf7b8b371ec = private unnamed_addr constant [2 x i8] c" (", align 1
@alloc_e58e2a7074d474581ef62356c3d35f0f = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_0a05944ad58b4205f18bcadf34f0c0d5, [8 x i8] c"\07\00\00\00\00\00\00\00", ptr @alloc_91d16892003a5ae3285cccf7b8b371ec, [8 x i8] c"\02\00\00\00\00\00\00\00", ptr @alloc_7f611c855055e83ed2e8134986c70875, [8 x i8] c"\06\00\00\00\00\00\00\00" }>, align 8
@alloc_4a5a7ae8156b8db5f910ccf62516770d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00-\00\00\00+\00\00\00" }>, align 8
@alloc_dcff1936d1e80d8971cc63014ce5da0d = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_ef4ab0ec5a985c9e5e6159d080f58740, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_3c0bcb7feff3a60a684159dc6f0e7df8 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00.\00\00\00'\00\00\00" }>, align 8
@alloc_133596709236629331b4feafd9153a46 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_00fe0f06901ca65f5517e46347ee6896, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_ca49bfb6cc7c32b21d8d6deb6223873f = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00/\00\00\00'\00\00\00" }>, align 8
@alloc_60d40733446453038e537e5feaeecff0 = private unnamed_addr constant <{ ptr, [8 x i8], ptr, [8 x i8], ptr, [8 x i8] }> <{ ptr @alloc_a2c07633baee718f79ac12718616c33b, [8 x i8] c"\06\00\00\00\00\00\00\00", ptr @alloc_12470cffb0ed9f3cc12c6066ef64d57b, [8 x i8] c"\04\00\00\00\00\00\00\00", ptr @alloc_49a1e817e911805af64bbc7efb390101, [8 x i8] c"\01\00\00\00\00\00\00\00" }>, align 8
@alloc_307669aee7bfb7f22345b2feaaa96b9a = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\000\00\00\00'\00\00\00" }>, align 8
@alloc_697a2ff9785a41cecb9c68d5d3656e12 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00\10\01\00\00\0D\00\00\00" }>, align 8
@alloc_4c83b73bab296ead0b6c2f740bbed263 = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00#\01\00\00\0C\00\00\00" }>, align 8
@alloc_4e862ca84b7437a7a00f07232d819c3d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\002\01\00\00\1C\00\00\00" }>, align 8
@alloc_22678f473f24f7dea89642298fe67d0d = private unnamed_addr constant <{ ptr, [16 x i8] }> <{ ptr @alloc_958fa8256726c0f3a54ab1a4fd436a9f, [16 x i8] c"\1C\00\00\00\00\00\00\00>\01\00\00+\00\00\00" }>, align 8

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: uwtable
define internal double @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h62fdbae60cbb195eE"(ptr align 8 %self, double %init) unnamed_addr #0 {
start:
  %_4.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4.1 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %f = load ptr, ptr %1, align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17hdb99542e9fa61958E"(ptr %_4.0, ptr %_4.1, double %init, ptr align 8 %f)
  ret double %_0
}

; <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: uwtable
define internal double @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h7ab198c8c0e1541aE"(ptr align 8 %self, double %init) unnamed_addr #0 {
start:
  %_4 = alloca [24 x i8], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_4, ptr align 8 %self, i64 24, i1 false)
  %0 = getelementptr inbounds i8, ptr %self, i64 24
  %f.0 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  %f.1 = load ptr, ptr %1, align 8
; call <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h379aabf5fc4a1c68E"(ptr align 8 %_4, double %init, ptr align 8 %f.0, ptr align 8 %f.1)
  ret double %_0
}

; <core::iter::adapters::step_by::StepBy<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN105_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hb90f351d7a47c4c0E"(ptr align 8 %self) unnamed_addr #1 {
start:
; call <core::iter::adapters::step_by::StepBy<I> as core::iter::adapters::step_by::StepByImpl<I>>::spec_next
  %0 = call { i64, i64 } @"_ZN117_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..adapters..step_by..StepByImpl$LT$I$GT$$GT$9spec_next17hc2065dc4610acc9bE"(ptr align 8 %self)
  %_0.0 = extractvalue { i64, i64 } %0, 0
  %_0.1 = extractvalue { i64, i64 } %0, 1
  %1 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %2 = insertvalue { i64, i64 } %1, i64 %_0.1, 1
  ret { i64, i64 } %2
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h9b8e9d88e07bbee2E"(ptr align 8 %self, i64 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_36 = alloca [1 x i8], align 1
  %_35 = alloca [1 x i8], align 1
  %_25 = alloca [8 x i8], align 8
  %_14 = alloca [8 x i8], align 8
  %accum = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  store i8 1, ptr %_36, align 1
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %1 = load i8, ptr %0, align 8
  %_37 = trunc nuw i8 %1 to i1
  br i1 %_37, label %bb34, label %bb35

bb35:                                             ; preds = %start
  %_40 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_40, align 8
  %_0.i = icmp ule i64 %_3.i, %_4.i
  br label %bb36

bb34:                                             ; preds = %start
  br label %bb1

bb28:                                             ; preds = %funclet_bb28
  cleanupret from %cleanuppad unwind label %funclet_bb33

funclet_bb28:                                     ; preds = %bb1, %bb30, %bb31_cleanup_trampoline_bb28
  %cleanuppad = cleanuppad within none []
  br label %bb28

bb36:                                             ; preds = %bb35
  %_4 = xor i1 %_0.i, true
  br i1 %_4, label %bb1, label %bb3

bb3:                                              ; preds = %bb36
  store i8 0, ptr %_36, align 1
  store i8 1, ptr %_35, align 1
  store i64 %init, ptr %accum, align 8
  br label %bb4

bb1:                                              ; preds = %bb34, %bb36
  store i8 0, ptr %_36, align 1
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
  %2 = invoke i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17hb9e99d0d1674be58E"(i64 %init)
          to label %bb2 unwind label %funclet_bb28

bb4:                                              ; preds = %bb11, %bb3
  %_9 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i5 = load i64, ptr %self, align 8
  %_4.i6 = load i64, ptr %_9, align 8
  %_0.i7 = icmp ult i64 %_3.i5, %_4.i6
  br label %bb5

bb31:                                             ; preds = %funclet_bb31
  %3 = load i8, ptr %_35, align 1
  %4 = trunc nuw i8 %3 to i1
  br i1 %4, label %bb30, label %bb31_cleanup_trampoline_bb28

funclet_bb31:                                     ; preds = %bb12, %bb8, %bb7, %bb6, %bb20, %bb23, %bb17, %bb16, %bb14
  %cleanuppad1 = cleanuppad within none []
  br label %bb31

bb5:                                              ; preds = %bb4
  br i1 %_0.i7, label %bb6, label %bb14

bb14:                                             ; preds = %bb5
  %5 = getelementptr inbounds i8, ptr %self, i64 16
  store i8 1, ptr %5, align 8
  %_24 = getelementptr inbounds i8, ptr %self, i64 8
; invoke core::cmp::impls::<impl core::cmp::PartialEq for u64>::eq
  %_22 = invoke zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$u64$GT$2eq17hce62cbe9af302c13E"(ptr align 8 %self, ptr align 8 %_24)
          to label %bb15 unwind label %funclet_bb31

bb6:                                              ; preds = %bb5
  %_11 = load i64, ptr %self, align 8
; invoke <u64 as core::iter::range::Step>::forward_unchecked
  %n = invoke i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h529087958077e5c4E"(i64 %_11, i64 1)
          to label %bb7 unwind label %funclet_bb31

bb15:                                             ; preds = %bb14
  br i1 %_22, label %bb16, label %bb22

bb22:                                             ; preds = %bb15
  br label %bb23

bb16:                                             ; preds = %bb15
  store i8 0, ptr %_35, align 1
  %_29 = load i64, ptr %accum, align 8
  %_30 = load i64, ptr %self, align 8
; invoke core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
  %_26 = invoke i64 @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h143f3d2a553ac183E"(ptr align 1 %f, i64 %_29, i64 %_30)
          to label %bb17 unwind label %funclet_bb31

bb23:                                             ; preds = %bb19, %bb22
  store i8 0, ptr %_35, align 1
  %_34 = load i64, ptr %accum, align 8
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
  %6 = invoke i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17hb9e99d0d1674be58E"(i64 %_34)
          to label %bb24 unwind label %funclet_bb31

bb17:                                             ; preds = %bb16
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
  %7 = invoke i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h246dfbd3de104048E"(i64 %_26)
          to label %bb18 unwind label %funclet_bb31

bb18:                                             ; preds = %bb17
  store i64 %7, ptr %_25, align 8
  %8 = load i64, ptr %_25, align 8
  br label %bb19

bb19:                                             ; preds = %bb18
  %val = load i64, ptr %_25, align 8
  store i64 %val, ptr %accum, align 8
  br label %bb23

bb24:                                             ; preds = %bb23
  store i64 %6, ptr %_0, align 8
  br label %bb27

bb27:                                             ; preds = %bb26, %bb24
  %9 = load i64, ptr %_0, align 8
  ret i64 %9

bb20:                                             ; No predecessors!
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
  %10 = invoke i64 @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h77736f86045ab2e6E"()
          to label %bb21 unwind label %funclet_bb31

bb21:                                             ; preds = %bb20
  store i64 %10, ptr %_0, align 8
  br label %bb25

bb25:                                             ; preds = %bb13, %bb21
  br label %bb26

bb7:                                              ; preds = %bb6
  %n2 = load i64, ptr %self, align 8
  store i64 %n, ptr %self, align 8
  store i8 0, ptr %_35, align 1
  %_18 = load i64, ptr %accum, align 8
; invoke core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
  %_15 = invoke i64 @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h143f3d2a553ac183E"(ptr align 1 %f, i64 %_18, i64 %n2)
          to label %bb8 unwind label %funclet_bb31

bb8:                                              ; preds = %bb7
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
  %11 = invoke i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h246dfbd3de104048E"(i64 %_15)
          to label %bb9 unwind label %funclet_bb31

bb9:                                              ; preds = %bb8
  store i64 %11, ptr %_14, align 8
  %12 = load i64, ptr %_14, align 8
  br label %bb11

bb11:                                             ; preds = %bb9
  %val3 = load i64, ptr %_14, align 8
  store i8 1, ptr %_35, align 1
  store i64 %val3, ptr %accum, align 8
  br label %bb4

bb12:                                             ; No predecessors!
; invoke <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
  %13 = invoke i64 @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h77736f86045ab2e6E"()
          to label %bb13 unwind label %funclet_bb31

bb13:                                             ; preds = %bb12
  store i64 %13, ptr %_0, align 8
  br label %bb25

bb26:                                             ; preds = %bb2, %bb25
  br label %bb27

bb10:                                             ; No predecessors!
  unreachable

bb31_cleanup_trampoline_bb28:                     ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb30:                                             ; preds = %bb31
  cleanupret from %cleanuppad1 unwind label %funclet_bb28

bb2:                                              ; preds = %bb1
  store i64 %2, ptr %_0, align 8
  br label %bb26

bb33:                                             ; preds = %funclet_bb33
  %14 = load i8, ptr %_36, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb32, label %bb29

funclet_bb33:                                     ; preds = %bb28
  %cleanuppad4 = cleanuppad within none []
  br label %bb33

bb29:                                             ; preds = %bb32, %bb33
  cleanupret from %cleanuppad4 unwind to caller

bb32:                                             ; preds = %bb33
  br label %bb29
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h25d7c03d95adf79bE"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_6 = alloca [4 x i8], align 4
  %_0 = alloca [8 x i8], align 4
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %1 = load i8, ptr %0, align 4
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_13, align 4
  %_0.i = icmp ule i32 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i1 = load i32, ptr %self, align 4
  %_4.i2 = load i32, ptr %_5, align 4
  %_0.i3 = icmp ult i32 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i32 0, ptr %_0, align 4
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store i8 1, ptr %2, align 4
  %3 = load i32, ptr %self, align 4
  store i32 %3, ptr %_6, align 4
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i32, ptr %self, align 4
; call <u32 as core::iter::range::Step>::forward_unchecked
  %n = call i32 @"_ZN47_$LT$u32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h2e18c2700d97843cE"(i32 %_8, i64 1)
  %4 = load i32, ptr %self, align 4
  store i32 %4, ptr %_6, align 4
  store i32 %n, ptr %self, align 4
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i32, ptr %_6, align 4
  %6 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %5, ptr %6, align 4
  store i32 1, ptr %_0, align 4
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i32, ptr %_0, align 4
  %8 = getelementptr inbounds i8, ptr %_0, i64 4
  %9 = load i32, ptr %8, align 4
  %10 = insertvalue { i32, i32 } poison, i32 %7, 0
  %11 = insertvalue { i32, i32 } %10, i32 %9, 1
  ret { i32, i32 } %11
}

; <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h98fc3e94a56c0458E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_6 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %1 = load i8, ptr %0, align 8
  %_10 = trunc nuw i8 %1 to i1
  br i1 %_10, label %bb9, label %bb10

bb10:                                             ; preds = %start
  %_13 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_13, align 8
  %_0.i = icmp ule i64 %_3.i, %_4.i
  %_2 = xor i1 %_0.i, true
  br i1 %_2, label %bb1, label %bb2

bb9:                                              ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb10
  %_5 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i1 = load i64, ptr %self, align 8
  %_4.i2 = load i64, ptr %_5, align 8
  %_0.i3 = icmp ult i64 %_3.i1, %_4.i2
  br i1 %_0.i3, label %bb4, label %bb6

bb1:                                              ; preds = %bb9, %bb10
  store i64 0, ptr %_0, align 8
  br label %bb8

bb6:                                              ; preds = %bb2
  %2 = getelementptr inbounds i8, ptr %self, i64 16
  store i8 1, ptr %2, align 8
  %3 = load i64, ptr %self, align 8
  store i64 %3, ptr %_6, align 8
  br label %bb7

bb4:                                              ; preds = %bb2
  %_8 = load i64, ptr %self, align 8
; call <u64 as core::iter::range::Step>::forward_unchecked
  %n = call i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h529087958077e5c4E"(i64 %_8, i64 1)
  %4 = load i64, ptr %self, align 8
  store i64 %4, ptr %_6, align 8
  store i64 %n, ptr %self, align 8
  br label %bb7

bb7:                                              ; preds = %bb4, %bb6
  %5 = load i64, ptr %_6, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %5, ptr %6, align 8
  store i64 1, ptr %_0, align 8
  br label %bb8

bb8:                                              ; preds = %bb1, %bb7
  %7 = load i64, ptr %_0, align 8
  %8 = getelementptr inbounds i8, ptr %_0, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = insertvalue { i64, i64 } poison, i64 %7, 0
  %11 = insertvalue { i64, i64 } %10, i64 %9, 1
  ret { i64, i64 } %11
}

; <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h379aabf5fc4a1c68E"(ptr align 8 %self, double %init, ptr align 8 %fold.0, ptr align 8 %fold.1) unnamed_addr #1 {
start:
  %_5 = alloca [24 x i8], align 8
  %_4.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_4.1 = load ptr, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 16
  %count = load i64, ptr %1, align 8
  store ptr %fold.0, ptr %_5, align 8
  %2 = getelementptr inbounds i8, ptr %_5, i64 8
  store ptr %fold.1, ptr %2, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 16
  store i64 %count, ptr %3, align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h5b00ae5205c64e86E"(ptr %_4.0, ptr %_4.1, double %init, ptr align 8 %_5)
  ret double %_0
}

; <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::fold::enumerate::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold9enumerate28_$u7b$$u7b$closure$u7d$$u7d$17h40aeba57fb090764E"(ptr align 8 %_1, double %acc, ptr align 8 %item) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_6 = alloca [24 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %_1, i64 16
  %_8 = load i64, ptr %0, align 8
  store double %acc, ptr %_6, align 8
  %1 = getelementptr inbounds i8, ptr %_6, i64 8
  store i64 %_8, ptr %1, align 8
  %2 = getelementptr inbounds i8, ptr %1, i64 8
  store ptr %item, ptr %2, align 8
  %3 = load double, ptr %_6, align 8
  %4 = getelementptr inbounds i8, ptr %_6, i64 8
  %5 = load i64, ptr %4, align 8
  %6 = getelementptr inbounds i8, ptr %4, i64 8
  %7 = load ptr, ptr %6, align 8
; call core::iter::adapters::map::map_fold::{{closure}}
  %acc1 = call double @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h3e02cdff30ae8a47E"(ptr align 8 %_1, double %3, i64 %5, ptr align 8 %7)
  %8 = getelementptr inbounds i8, ptr %_1, i64 16
  %9 = load i64, ptr %8, align 8
  %10 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %9, i64 1)
  %_9.0 = extractvalue { i64, i1 } %10, 0
  %_9.1 = extractvalue { i64, i1 } %10, 1
  br i1 %_9.1, label %panic, label %bb2

bb2:                                              ; preds = %start
  %11 = getelementptr inbounds i8, ptr %_1, i64 16
  store i64 %_9.0, ptr %11, align 8
  ret double %acc1

panic:                                            ; preds = %start
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_c80567f23cbf089cfb1ed8514bd745da) #12
          to label %unreachable unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %panic
  %cleanuppad = cleanuppad within none []
  br label %bb3

unreachable:                                      ; preds = %panic
  unreachable
}

; <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha1fff5d202be5f55E"(ptr align 8 %self) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %self1 = alloca [8 x i8], align 8
  %_3 = alloca [8 x i8], align 8
  %_0 = alloca [16 x i8], align 8
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %0 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %self)
  store ptr %0, ptr %self1, align 8
  %1 = load ptr, ptr %self1, align 8
  %2 = ptrtoint ptr %1 to i64
  %3 = icmp eq i64 %2, 0
  %_11 = select i1 %3, i64 0, i64 1
  %4 = trunc nuw i64 %_11 to i1
  br i1 %4, label %bb8, label %bb7

bb8:                                              ; preds = %start
  %v = load ptr, ptr %self1, align 8
  store ptr %v, ptr %_3, align 8
  %val = load ptr, ptr %_3, align 8
  %5 = getelementptr inbounds i8, ptr %self, i64 16
  %i = load i64, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %self, i64 16
  %7 = load i64, ptr %6, align 8
  %8 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %7, i64 1)
  %_8.0 = extractvalue { i64, i1 } %8, 0
  %_8.1 = extractvalue { i64, i1 } %8, 1
  br i1 %_8.1, label %panic, label %bb3

bb7:                                              ; preds = %start
  %9 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr null, ptr %9, align 8
  br label %bb4

bb4:                                              ; preds = %bb3, %bb7
  %10 = load i64, ptr %_0, align 8
  %11 = getelementptr inbounds i8, ptr %_0, i64 8
  %12 = load ptr, ptr %11, align 8
  %13 = insertvalue { i64, ptr } poison, i64 %10, 0
  %14 = insertvalue { i64, ptr } %13, ptr %12, 1
  ret { i64, ptr } %14

bb3:                                              ; preds = %bb8
  %15 = getelementptr inbounds i8, ptr %self, i64 16
  store i64 %_8.0, ptr %15, align 8
  store i64 %i, ptr %_0, align 8
  %16 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %val, ptr %16, align 8
  br label %bb4

panic:                                            ; preds = %bb8
; invoke core::panicking::panic_const::panic_const_add_overflow
  invoke void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_67987f2acd65c96a8c883ff2ecae97af) #12
          to label %unreachable unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  cleanupret from %cleanuppad unwind to caller

funclet_bb5:                                      ; preds = %panic
  %cleanuppad = cleanuppad within none []
  br label %bb5

unreachable:                                      ; preds = %panic
  unreachable

bb2:                                              ; No predecessors!
  unreachable
}

; <core::iter::adapters::step_by::StepBy<I> as core::iter::adapters::step_by::StepByImpl<I>>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN117_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..adapters..step_by..StepByImpl$LT$I$GT$$GT$9spec_next17hc2065dc4610acc9bE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %step_size = alloca [8 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 32
  %1 = load i8, ptr %0, align 8
  %_3 = trunc nuw i8 %1 to i1
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %2 = load i64, ptr %self, align 8
  store i64 %2, ptr %step_size, align 8
  br label %bb3

bb1:                                              ; preds = %start
  store i64 0, ptr %step_size, align 8
  br label %bb3

bb3:                                              ; preds = %bb1, %bb2
  %3 = getelementptr inbounds i8, ptr %self, i64 32
  store i8 0, ptr %3, align 8
  %_4 = getelementptr inbounds i8, ptr %self, i64 8
  %_5 = load i64, ptr %step_size, align 8
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::nth
  %4 = call { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$3nth17h184750e88e62cea6E"(ptr align 8 %_4, i64 %_5)
  %_0.0 = extractvalue { i64, i64 } %4, 0
  %_0.1 = extractvalue { i64, i64 } %4, 1
  %5 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %6 = insertvalue { i64, i64 } %5, i64 %_0.1, 1
  ret { i64, i64 } %6
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::FromResidual<core::ops::try_trait::NeverShortCircuitResidual>>::from_residual
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN158_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..FromResidual$LT$core..ops..try_trait..NeverShortCircuitResidual$GT$$GT$13from_residual17h77736f86045ab2e6E"() unnamed_addr #1 {
start:
  unreachable
}

; std::rt::lang_start
; Function Attrs: uwtable
define hidden i64 @_ZN3std2rt10lang_start17h28167623e72390e5E(ptr %main, i64 %argc, ptr %argv, i8 %sigpipe) unnamed_addr #0 {
start:
  %_7 = alloca [8 x i8], align 8
  store ptr %main, ptr %_7, align 8
; call std::rt::lang_start_internal
  %_0 = call i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1 %_7, ptr align 8 @vtable.0, i64 %argc, ptr %argv, i8 %sigpipe)
  ret i64 %_0
}

; std::rt::lang_start::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h3a75b735979dc699E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  %_4 = load ptr, ptr %_1, align 8
; call std::sys::backtrace::__rust_begin_short_backtrace
  call void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hc0550a08597be6b6E(ptr %_4)
; call <() as std::process::Termination>::report
  %self = call i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h1da288c24c9cf0bcE"()
  ret i32 %self
}

; std::f64::<impl f64>::ln
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$2ln17hc764f3993a195a61E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.log.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::cos
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3cos17h441f1dbd8b285e4cE"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.cos.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::exp
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3exp17h5781cb802bc92ae1E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.exp.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::sin
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3sin17h2d8fa3ef473081aaE"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.sin.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::tan
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3tan17heb63c8d704691aceE"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @tan(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::acos
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4acos17h7296f6f8305c7dc2E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @acos(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::asin
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4asin17h3f95b6cd2537be19E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @asin(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::atan
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4atan17he788001a47cdf344E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @atan(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::cbrt
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4cbrt17h4b8333a563c1c3e8E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @cbrt(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::ceil
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4ceil17h4fdb41ce649351e0E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.ceil.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::cosh
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4cosh17h910508e6d4a6eee3E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @cosh(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::log2
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4log217h49c2bfb6926be7b8E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.log2.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::powf
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17hb46404ddf630b33dE"(double %self, double %n) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.pow.f64(double %self, double %n)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::powi
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %self, i32 %n) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.powi.f64.i32(double %self, i32 %n)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::sinh
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sinh17h7a30010c2d6bce05E"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @sinh(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::sqrt
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.sqrt.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::tanh
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4tanh17h2c3fff3e90ecc31dE"(double %self) unnamed_addr #1 {
start:
  %_0 = call double @tanh(double %self) #13
  ret double %_0
}

; std::f64::<impl f64>::floor
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5floor17h2012b6cab1f3b1c1E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.floor.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::fract
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5fract17h14bf28d3003e400dE"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.trunc.f64(double %self)
  store double %1, ptr %0, align 8
  %_2 = load double, ptr %0, align 8
  %_0 = fsub double %self, %_2
  ret double %_0
}

; std::f64::<impl f64>::log10
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5log1017hc58cfafa35dc96e5E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.log10.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::round
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5round17ha4a65347234b54f0E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.round.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::f64::<impl f64>::trunc
; Function Attrs: inlinehint uwtable
define internal double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5trunc17h25b6858ec28c1f15E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.trunc.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; std::sys::backtrace::__rust_begin_short_backtrace
; Function Attrs: noinline uwtable
define internal void @_ZN3std3sys9backtrace28__rust_begin_short_backtrace17hc0550a08597be6b6E(ptr %f) unnamed_addr #2 {
start:
; call core::ops::function::FnOnce::call_once
  call void @_ZN4core3ops8function6FnOnce9call_once17h9fda9c9e1f0d2ad0E(ptr %f)
  call void asm sideeffect "", "~{memory}"(), !srcloc !3
  ret void
}

; <&T as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17hd487d5f7e5c22672E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3 = load ptr, ptr %self, align 8
; call core::fmt::float::<impl core::fmt::Debug for f64>::fmt
  %_0 = call zeroext i1 @"_ZN4core3fmt5float50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$f64$GT$3fmt17h6d2ef5c8d1e67605E"(ptr align 8 %_3, ptr align 8 %f)
  ret i1 %_0
}

; <&T as core::fmt::Display>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h548936744bc5dfc0E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_3.0 = load ptr, ptr %self, align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.1 = load i64, ptr %0, align 8
; call <str as core::fmt::Display>::fmt
  %_0 = call zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1 %_3.0, i64 %_3.1, ptr align 8 %f)
  ret i1 %_0
}

; <u32 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN47_$LT$u32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h2e18c2700d97843cE"(i32 %start1, i64 %n) unnamed_addr #1 {
start:
  %rhs = trunc i64 %n to i32
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl u32>::unchecked_add::precondition_check
  call void @"_ZN4core3num21_$LT$impl$u20$u32$GT$13unchecked_add18precondition_check17hc4ac58224e3d721fE"(i32 %start1, i32 %rhs) #13
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = add nuw i32 %start1, %rhs
  ret i32 %_0
}

; <u64 as core::iter::range::Step>::forward_checked
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$15forward_checked17h0509ab406c51af14E"(i64 %start1, i64 %n) unnamed_addr #1 {
start:
  %_0 = alloca [16 x i8], align 8
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %start1, i64 %n)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb2, label %bb4

bb4:                                              ; preds = %start
  %_6 = add nuw i64 %start1, %n
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_6, ptr %1, align 8
  store i64 1, ptr %_0, align 8
  br label %bb1

bb2:                                              ; preds = %start
  %2 = load i64, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %3 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  store i64 %2, ptr %_0, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %3, ptr %4, align 8
  br label %bb1

bb1:                                              ; preds = %bb2, %bb4
  %5 = load i64, ptr %_0, align 8
  %6 = getelementptr inbounds i8, ptr %_0, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = insertvalue { i64, i64 } poison, i64 %5, 0
  %9 = insertvalue { i64, i64 } %8, i64 %7, 1
  ret { i64, i64 } %9
}

; <u64 as core::iter::range::Step>::forward_unchecked
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h529087958077e5c4E"(i64 %start1, i64 %n) unnamed_addr #1 {
start:
  br label %bb1

bb1:                                              ; preds = %start
; call core::num::<impl u64>::unchecked_add::precondition_check
  call void @"_ZN4core3num21_$LT$impl$u20$u64$GT$13unchecked_add18precondition_check17h98a8c505e43767bfE"(i64 %start1, i64 %n) #13
  br label %bb2

bb2:                                              ; preds = %bb1
  %_0 = add nuw i64 %start1, %n
  ret i64 %_0
}

; <u64 as core::iter::range::Step>::forward
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$7forward17h0b08ea434a471b31E"(i64 %start1, i64 %n) unnamed_addr #1 {
start:
  %_5 = alloca [16 x i8], align 8
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %start1, i64 %n)
  %_8.0 = extractvalue { i64, i1 } %0, 0
  %_8.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_8.1, label %bb5, label %bb7

bb7:                                              ; preds = %start
  %_9 = add nuw i64 %start1, %n
  %1 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %_9, ptr %1, align 8
  store i64 1, ptr %_5, align 8
  br label %bb4

bb5:                                              ; preds = %start
  %2 = load i64, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %3 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  store i64 %2, ptr %_5, align 8
  %4 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %3, ptr %4, align 8
  br label %bb4

bb4:                                              ; preds = %bb5, %bb7
  %_12 = load i64, ptr %_5, align 8
  %5 = getelementptr inbounds i8, ptr %_5, i64 8
  %6 = load i64, ptr %5, align 8
  %_11 = icmp eq i64 %_12, 1
  %_3 = xor i1 %_11, true
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %bb4
  br label %bb3

bb1:                                              ; preds = %bb4
  br i1 true, label %panic, label %bb3

bb3:                                              ; preds = %bb1, %bb2
  %_0 = add i64 %start1, %n
  ret i64 %_0

panic:                                            ; preds = %bb1
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_dda2737f33a4b1c1d39fa523d13a220a) #12
  unreachable
}

; <[T] as core::fmt::Debug>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h18d16c11083ea7fdE"(ptr align 8 %self.0, i64 %self.1, ptr align 8 %f) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  %_5 = alloca [16 x i8], align 8
; call core::fmt::Formatter::debug_list
  call void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8 %_5, ptr align 8 %f)
  br label %bb5

bb5:                                              ; preds = %start
  %_11 = getelementptr inbounds nuw double, ptr %self.0, i64 %self.1
  store ptr %_11, ptr %end_or_len, align 8
  br label %bb6

bb6:                                              ; preds = %bb5
  %_13 = load ptr, ptr %end_or_len, align 8
; call core::fmt::builders::DebugList::entries
  %_3 = call align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h8d6ca50ab3faced1E(ptr align 8 %_5, ptr %self.0, ptr %_13)
; call core::fmt::builders::DebugList::finish
  %_0 = call zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8 %_3)
  ret i1 %_0

bb4:                                              ; No predecessors!
  unreachable
}

; core::intrinsics::cold_path
; Function Attrs: cold nounwind uwtable
define internal void @_ZN4core10intrinsics9cold_path17hdeb2af380d8cc092E() unnamed_addr #3 {
start:
  ret void
}

; core::cmp::impls::<impl core::cmp::PartialEq for u64>::eq
; Function Attrs: inlinehint uwtable
define internal zeroext i1 @"_ZN4core3cmp5impls54_$LT$impl$u20$core..cmp..PartialEq$u20$for$u20$u64$GT$2eq17hce62cbe9af302c13E"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_3 = load i64, ptr %self, align 8
  %_4 = load i64, ptr %other, align 8
  %_0 = icmp eq i64 %_3, %_4
  ret i1 %_0
}

; core::cmp::impls::<impl core::cmp::PartialOrd for u64>::partial_cmp
; Function Attrs: inlinehint uwtable
define internal i8 @"_ZN4core3cmp5impls55_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$u64$GT$11partial_cmp17h3e779e57ec7c723eE"(ptr align 8 %self, ptr align 8 %other) unnamed_addr #1 {
start:
  %_0 = alloca [1 x i8], align 1
  %_4 = load i64, ptr %self, align 8
  %_5 = load i64, ptr %other, align 8
  %_3 = call i8 @llvm.ucmp.i8.i64(i64 %_4, i64 %_5)
  store i8 %_3, ptr %_0, align 1
  %0 = load i8, ptr %_0, align 1
  ret i8 %0
}

; core::f64::<impl f64>::to_degrees
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_degrees17hcbc2515506bf219aE"(double %self) unnamed_addr #1 {
start:
  %_0 = fmul double %self, 0x404CA5DC1A63C1F8
  ret double %_0
}

; core::f64::<impl f64>::to_radians
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_radians17h3ff76c3f42527209E"(double %self) unnamed_addr #1 {
start:
  %_0 = fmul double %self, 0x3F91DF46A2529D39
  ret double %_0
}

; core::f64::<impl f64>::abs
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3abs17hd697952636949800E"(double %self) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.fabs.f64(double %self)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; core::f64::<impl f64>::max
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3max17hcdfad5b3469c501fE"(double %self, double %other) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.maxnum.f64(double %self, double %other)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; core::f64::<impl f64>::min
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3min17h97a1b461355a299eE"(double %self, double %other) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  %1 = call double @llvm.minnum.f64(double %self, double %other)
  store double %1, ptr %0, align 8
  %_0 = load double, ptr %0, align 8
  ret double %_0
}

; core::fmt::rt::Placeholder::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_0, i64 %position, i32 %flags, ptr align 8 %precision, ptr align 8 %width) unnamed_addr #1 {
start:
  %0 = getelementptr inbounds i8, ptr %_0, i64 32
  store i64 %position, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 40
  store i32 %flags, ptr %1, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %precision, i64 16, i1 false)
  %2 = getelementptr inbounds i8, ptr %_0, i64 16
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %width, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h3cfab197877ea650E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h548936744bc5dfc0E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h2e45205524cc218aE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_display
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument11new_display17h9a6cd3bdaa32d1daE(ptr sret([16 x i8]) align 8 %_0, ptr align 1 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::Argument::new_debug
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt8Argument9new_debug17hd787b376edae8d0cE(ptr sret([16 x i8]) align 8 %_0, ptr align 8 %x) unnamed_addr #1 {
start:
  %_3 = alloca [16 x i8], align 8
  store ptr %x, ptr %_3, align 8
  %0 = getelementptr inbounds i8, ptr %_3, i64 8
  store ptr @"_ZN4core5array69_$LT$impl$u20$core..fmt..Debug$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$3fmt17h54043c2f95831c25E", ptr %0, align 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %_3, i64 16, i1 false)
  ret void
}

; core::fmt::rt::UnsafeArg::new
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE() unnamed_addr #1 {
start:
  ret void
}

; core::fmt::builders::DebugList::entries
; Function Attrs: uwtable
define internal align 8 ptr @_ZN4core3fmt8builders9DebugList7entries17h8d6ca50ab3faced1E(ptr align 8 %self, ptr %entries.0, ptr %entries.1) unnamed_addr #0 personality ptr @__CxxFrameHandler3 {
start:
  %entry = alloca [8 x i8], align 8
  %_5 = alloca [8 x i8], align 8
  %iter = alloca [16 x i8], align 8
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %0 = call { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0badb94d3d6e74f9E"(ptr %entries.0, ptr %entries.1)
  %_3.0 = extractvalue { ptr, ptr } %0, 0
  %_3.1 = extractvalue { ptr, ptr } %0, 1
  store ptr %_3.0, ptr %iter, align 8
  %1 = getelementptr inbounds i8, ptr %iter, i64 8
  store ptr %_3.1, ptr %1, align 8
  br label %bb2

bb2:                                              ; preds = %bb8, %start
; invoke <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %2 = invoke align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter)
          to label %bb3 unwind label %funclet_bb11

bb11:                                             ; preds = %funclet_bb11
  cleanupret from %cleanuppad unwind to caller

funclet_bb11:                                     ; preds = %bb10, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb11

bb3:                                              ; preds = %bb2
  store ptr %2, ptr %_5, align 8
  %3 = load ptr, ptr %_5, align 8
  %4 = ptrtoint ptr %3 to i64
  %5 = icmp eq i64 %4, 0
  %_7 = select i1 %5, i64 0, i64 1
  %6 = trunc nuw i64 %_7 to i1
  br i1 %6, label %bb5, label %bb6

bb5:                                              ; preds = %bb3
  %7 = load ptr, ptr %_5, align 8
  store ptr %7, ptr %entry, align 8
; invoke core::fmt::builders::DebugList::entry
  %_9 = invoke align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8 %self, ptr align 1 %entry, ptr align 8 @vtable.1)
          to label %bb7 unwind label %funclet_bb10

bb6:                                              ; preds = %bb3
  ret ptr %self

bb10:                                             ; preds = %funclet_bb10
  cleanupret from %cleanuppad1 unwind label %funclet_bb11

funclet_bb10:                                     ; preds = %bb5
  %cleanuppad1 = cleanuppad within none []
  br label %bb10

bb7:                                              ; preds = %bb5
  br label %bb8

bb8:                                              ; preds = %bb7
  br label %bb2

bb4:                                              ; No predecessors!
  unreachable
}

; core::fmt::Arguments::new_v1_formatted
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces.0, i64 %pieces.1, ptr align 8 %args.0, i64 %args.1, ptr align 8 %fmt.0, i64 %fmt.1) unnamed_addr #1 {
start:
  %_5 = alloca [16 x i8], align 8
  store ptr %fmt.0, ptr %_5, align 8
  %0 = getelementptr inbounds i8, ptr %_5, i64 8
  store i64 %fmt.1, ptr %0, align 8
  store ptr %pieces.0, ptr %_0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %pieces.1, ptr %1, align 8
  %2 = load ptr, ptr %_5, align 8
  %3 = getelementptr inbounds i8, ptr %_5, i64 8
  %4 = load i64, ptr %3, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %2, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 %4, ptr %6, align 8
  %7 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args.0, ptr %7, align 8
  %8 = getelementptr inbounds i8, ptr %7, i64 8
  store i64 %args.1, ptr %8, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 4, ptr %0, align 8
  %1 = load ptr, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 3, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 2, ptr %0, align 8
  %1 = load ptr, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 1, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_v1
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces, ptr align 8 %args) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 3, ptr %0, align 8
  %1 = load ptr, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %args, ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 2, ptr %6, align 8
  ret void
}

; core::fmt::Arguments::new_const
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_0, ptr align 8 %pieces) unnamed_addr #1 {
start:
  store ptr %pieces, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 1, ptr %0, align 8
  %1 = load ptr, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, align 8
  %2 = load i64, ptr getelementptr inbounds (i8, ptr @anon.d686dafb26d9dcf23a3316afed0a3342.0, i64 8), align 8
  %3 = getelementptr inbounds i8, ptr %_0, i64 32
  store ptr %1, ptr %3, align 8
  %4 = getelementptr inbounds i8, ptr %3, i64 8
  store i64 %2, ptr %4, align 8
  %5 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr inttoptr (i64 8 to ptr), ptr %5, align 8
  %6 = getelementptr inbounds i8, ptr %5, i64 8
  store i64 0, ptr %6, align 8
  ret void
}

; core::num::<impl u32>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num21_$LT$impl$u20$u32$GT$13unchecked_add18precondition_check17hc4ac58224e3d721fE"(i32 %lhs, i32 %rhs) unnamed_addr #4 {
start:
  %0 = call { i32, i1 } @llvm.uadd.with.overflow.i32(i32 %lhs, i32 %rhs)
  %_5.0 = extractvalue { i32, i1 } %0, 0
  %_5.1 = extractvalue { i32, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_a6a0cc8156fe455996de64a9d05b1dfe, i64 184) #14
  unreachable
}

; core::num::<impl u64>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num21_$LT$impl$u20$u64$GT$13unchecked_add18precondition_check17h98a8c505e43767bfE"(i64 %lhs, i64 %rhs) unnamed_addr #4 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_fc445f6abf67cf4b683577fd3aaed336, i64 184) #14
  unreachable
}

; core::num::<impl usize>::unchecked_add::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %lhs, i64 %rhs) unnamed_addr #4 {
start:
  %0 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %lhs, i64 %rhs)
  %_5.0 = extractvalue { i64, i1 } %0, 0
  %_5.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_5.1, label %bb1, label %bb2

bb2:                                              ; preds = %start
  ret void

bb1:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_3e1ebac14318b612ab4efabc52799932, i64 186) #14
  unreachable
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h8d984e9143256103E"(ptr sret([12 x i8]) align 4 %_0, i32 %start1, i32 %end) unnamed_addr #1 {
start:
  store i32 %start1, ptr %_0, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %end, ptr %0, align 4
  %1 = getelementptr inbounds i8, ptr %_0, i64 8
  store i8 0, ptr %1, align 4
  ret void
}

; core::ops::range::RangeInclusive<Idx>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf863846c96c7deb3E"(ptr sret([24 x i8]) align 8 %_0, i64 %start1, i64 %end) unnamed_addr #1 {
start:
  store i64 %start1, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %end, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i8 0, ptr %1, align 8
  ret void
}

; core::ops::function::FnOnce::call_once{{vtable.shim}}
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h6cd627a71d5d36c3E"(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  %0 = load ptr, ptr %_1, align 8
; call core::ops::function::FnOnce::call_once
  %_0 = call i32 @_ZN4core3ops8function6FnOnce9call_once17h1e18688bdc20dac9E(ptr %0)
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal i32 @_ZN4core3ops8function6FnOnce9call_once17h1e18688bdc20dac9E(ptr %0) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_2 = alloca [0 x i8], align 1
  %_1 = alloca [8 x i8], align 8
  store ptr %0, ptr %_1, align 8
; invoke std::rt::lang_start::{{closure}}
  %_0 = invoke i32 @"_ZN3std2rt10lang_start28_$u7b$$u7b$closure$u7d$$u7d$17h3a75b735979dc699E"(ptr align 8 %_1)
          to label %bb1 unwind label %funclet_bb3

bb3:                                              ; preds = %funclet_bb3
  cleanupret from %cleanuppad unwind to caller

funclet_bb3:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb3

bb1:                                              ; preds = %start
  ret i32 %_0
}

; core::ops::function::FnOnce::call_once
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core3ops8function6FnOnce9call_once17h9fda9c9e1f0d2ad0E(ptr %_1) unnamed_addr #1 {
start:
  %_2 = alloca [0 x i8], align 1
  call void %_1()
  ret void
}

; core::ops::try_trait::NeverShortCircuit<T>::wrap_mut_2::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ops9try_trait26NeverShortCircuit$LT$T$GT$10wrap_mut_228_$u7b$$u7b$closure$u7d$$u7d$17h143f3d2a553ac183E"(ptr align 1 %_1, i64 %a, i64 %b) unnamed_addr #1 {
start:
; call <u64 as core::iter::traits::accum::Product>::product::{{closure}}
  %_4 = call i64 @"_ZN58_$LT$u64$u20$as$u20$core..iter..traits..accum..Product$GT$7product28_$u7b$$u7b$closure$u7d$$u7d$17hf54c98ddb6ad069bE"(ptr align 1 %_1, i64 %a, i64 %b)
  ret i64 %_4
}

; core::ptr::drop_in_place<&f64>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr28drop_in_place$LT$$RF$f64$GT$17hd094eb516f52ec49E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::drop_in_place<std::rt::lang_start<()>::{{closure}}>
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core3ptr85drop_in_place$LT$std..rt..lang_start$LT$$LP$$RP$$GT$..$u7b$$u7b$closure$u7d$$u7d$$GT$17ha41680b8e601bc43E"(ptr align 8 %_1) unnamed_addr #1 {
start:
  ret void
}

; core::ptr::non_null::NonNull<T>::offset_from_unsigned
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %self, ptr %subtracted) unnamed_addr #1 {
start:
  %0 = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
; call core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
  call void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h7f804d4f47653101E"(ptr %self, ptr %subtracted) #13
  br label %bb4

bb4:                                              ; preds = %bb2
  br label %bb5

bb5:                                              ; preds = %bb4
  br label %bb6

bb6:                                              ; preds = %bb5
  %1 = ptrtoint ptr %self to i64
  %2 = ptrtoint ptr %subtracted to i64
  %3 = sub nuw i64 %1, %2
  %4 = udiv exact i64 %3, 8
  store i64 %4, ptr %0, align 8
  %_0 = load i64, ptr %0, align 8
  ret i64 %_0

bb7:                                              ; No predecessors!
; call core::panicking::panic
  call void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_ec595fc0e82ef92fc59bd74f68296eae, i64 73, ptr align 8 @alloc_a58506e252faa4cbf86c6501c99b19f4) #12
  unreachable
}

; core::ptr::const_ptr::<impl *const T>::offset_from_unsigned::precondition_check
; Function Attrs: inlinehint nounwind uwtable
define internal void @"_ZN4core3ptr9const_ptr33_$LT$impl$u20$$BP$const$u20$T$GT$20offset_from_unsigned18precondition_check17h7f804d4f47653101E"(ptr %this, ptr %origin) unnamed_addr #4 {
start:
  %_3 = icmp uge ptr %this, %origin
  br i1 %_3, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::panicking::panic_nounwind
  call void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1 @alloc_de4e626d456b04760e72bc785ed7e52a, i64 201) #14
  unreachable

bb1:                                              ; preds = %start
  ret void
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h12f9193f7870440cE"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17hfbf092153df5d194E"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::nth
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$3nth17h184750e88e62cea6E"(ptr align 8 %self, i64 %n) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_9 = alloca [1 x i8], align 1
  %plus_n = alloca [8 x i8], align 8
  %_4 = alloca [16 x i8], align 8
  %_0 = alloca [16 x i8], align 8
  %0 = getelementptr inbounds i8, ptr %self, i64 16
  %1 = load i8, ptr %0, align 8
  %_23 = trunc nuw i8 %1 to i1
  br i1 %_23, label %bb29, label %bb30

bb30:                                             ; preds = %start
  %_26 = getelementptr inbounds i8, ptr %self, i64 8
  %_3.i = load i64, ptr %self, align 8
  %_4.i = load i64, ptr %_26, align 8
  %_0.i = icmp ule i64 %_3.i, %_4.i
  %_3 = xor i1 %_0.i, true
  br i1 %_3, label %bb1, label %bb2

bb29:                                             ; preds = %start
  br label %bb1

bb2:                                              ; preds = %bb30
  %_0.i8 = load i64, ptr %self, align 8
; call <u64 as core::iter::range::Step>::forward_checked
  %2 = call { i64, i64 } @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$15forward_checked17h0509ab406c51af14E"(i64 %_0.i8, i64 %n)
  %3 = extractvalue { i64, i64 } %2, 0
  %4 = extractvalue { i64, i64 } %2, 1
  store i64 %3, ptr %_4, align 8
  %5 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 %4, ptr %5, align 8
  %_7 = load i64, ptr %_4, align 8
  %6 = getelementptr inbounds i8, ptr %_4, i64 8
  %7 = load i64, ptr %6, align 8
  %8 = trunc nuw i64 %_7 to i1
  br i1 %8, label %bb5, label %bb18

bb1:                                              ; preds = %bb29, %bb30
  store i64 0, ptr %_0, align 8
  br label %bb26

bb5:                                              ; preds = %bb2
  %9 = getelementptr inbounds i8, ptr %_4, i64 8
  %10 = load i64, ptr %9, align 8
  store i64 %10, ptr %plus_n, align 8
  %_11 = getelementptr inbounds i8, ptr %self, i64 8
; invoke core::cmp::impls::<impl core::cmp::PartialOrd for u64>::partial_cmp
  %11 = invoke i8 @"_ZN4core3cmp5impls55_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$u64$GT$11partial_cmp17h3e779e57ec7c723eE"(ptr align 8 %plus_n, ptr align 8 %_11)
          to label %bb6 unwind label %funclet_bb27

bb18:                                             ; preds = %bb2
  br label %bb21

bb27:                                             ; preds = %funclet_bb27
  cleanupret from %cleanuppad unwind label %funclet_bb28

funclet_bb27:                                     ; preds = %bb17, %bb14, %bb11, %bb5
  %cleanuppad = cleanuppad within none []
  br label %bb27

bb6:                                              ; preds = %bb5
  store i8 %11, ptr %_9, align 1
  %12 = load i8, ptr %_9, align 1
  %13 = icmp eq i8 %12, 2
  %_13 = select i1 %13, i64 0, i64 1
  %14 = trunc nuw i64 %_13 to i1
  br i1 %14, label %bb8, label %bb7

bb8:                                              ; preds = %bb6
  %_12 = load i8, ptr %_9, align 1
  switch i8 %_12, label %bb32 [
    i8 -1, label %bb10
    i8 0, label %bb9
    i8 1, label %bb7
  ]

bb7:                                              ; preds = %bb8, %bb6
  br label %bb21

bb32:                                             ; preds = %bb8
  unreachable

bb10:                                             ; preds = %bb8
  %_0.i7 = load i64, ptr %plus_n, align 8
  br label %bb11

bb9:                                              ; preds = %bb8
  %_0.i6 = load i64, ptr %plus_n, align 8
  br label %bb15

bb11:                                             ; preds = %bb10
; invoke <u64 as core::iter::range::Step>::forward
  %_14 = invoke i64 @"_ZN47_$LT$u64$u20$as$u20$core..iter..range..Step$GT$7forward17h0b08ea434a471b31E"(i64 %_0.i7, i64 1)
          to label %bb12 unwind label %funclet_bb27

bb12:                                             ; preds = %bb11
  br label %bb13

bb13:                                             ; preds = %bb12
  store i64 %_14, ptr %self, align 8
  %_17 = load i64, ptr %plus_n, align 8
  %15 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_17, ptr %15, align 8
  store i64 1, ptr %_0, align 8
  br label %bb25

bb25:                                             ; preds = %bb16, %bb13
  br label %bb26

bb14:                                             ; preds = %funclet_bb14
  store i64 %_14, ptr %self, align 8
  cleanupret from %cleanuppad1 unwind label %funclet_bb27

funclet_bb14:                                     ; No predecessors!
  %cleanuppad1 = cleanuppad within none []
  br label %bb14

bb15:                                             ; preds = %bb9
  br label %bb16

bb16:                                             ; preds = %bb15
  store i64 %_0.i6, ptr %self, align 8
  %16 = getelementptr inbounds i8, ptr %self, i64 16
  store i8 1, ptr %16, align 8
  %_20 = load i64, ptr %plus_n, align 8
  %17 = getelementptr inbounds i8, ptr %_0, i64 8
  store i64 %_20, ptr %17, align 8
  store i64 1, ptr %_0, align 8
  br label %bb25

bb26:                                             ; preds = %bb1, %bb23, %bb25
  %18 = load i64, ptr %_0, align 8
  %19 = getelementptr inbounds i8, ptr %_0, i64 8
  %20 = load i64, ptr %19, align 8
  %21 = insertvalue { i64, i64 } poison, i64 %18, 0
  %22 = insertvalue { i64, i64 } %21, i64 %20, 1
  ret { i64, i64 } %22

bb17:                                             ; preds = %funclet_bb17
  store i64 %_0.i6, ptr %self, align 8
  cleanupret from %cleanuppad2 unwind label %funclet_bb27

funclet_bb17:                                     ; No predecessors!
  %cleanuppad2 = cleanuppad within none []
  br label %bb17

bb21:                                             ; preds = %bb18, %bb7
  %_22 = getelementptr inbounds i8, ptr %self, i64 8
  %_0.i5 = load i64, ptr %_22, align 8
  br label %bb23

bb28:                                             ; preds = %funclet_bb28
  cleanupret from %cleanuppad3 unwind to caller

funclet_bb28:                                     ; preds = %bb24, %bb27
  %cleanuppad3 = cleanuppad within none []
  br label %bb28

bb23:                                             ; preds = %bb21
  store i64 %_0.i5, ptr %self, align 8
  %23 = getelementptr inbounds i8, ptr %self, i64 16
  store i8 1, ptr %23, align 8
  store i64 0, ptr %_0, align 8
  br label %bb26

bb24:                                             ; preds = %funclet_bb24
  store i64 %_0.i5, ptr %self, align 8
  cleanupret from %cleanuppad4 unwind label %funclet_bb28

funclet_bb24:                                     ; No predecessors!
  %cleanuppad4 = cleanuppad within none []
  br label %bb24
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::fold
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4fold17h2da35549c7ce007aE"(ptr align 8 %self, i64 %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
; invoke <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_try_fold
  %_4 = invoke i64 @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$13spec_try_fold17h9b8e9d88e07bbee2E"(ptr align 8 %self, i64 %init)
          to label %bb4 unwind label %funclet_bb2

bb2:                                              ; preds = %funclet_bb2
  cleanupret from %cleanuppad unwind to caller

funclet_bb2:                                      ; preds = %start
  %cleanuppad = cleanuppad within none []
  br label %bb2

bb4:                                              ; preds = %start
  ret i64 %_4
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h26a307ae4389603cE"(ptr align 8 %self) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i64, i64 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h98fc3e94a56c0458E"(ptr align 8 %self)
  %_0.0 = extractvalue { i64, i64 } %0, 0
  %_0.1 = extractvalue { i64, i64 } %0, 1
  %1 = insertvalue { i64, i64 } poison, i64 %_0.0, 0
  %2 = insertvalue { i64, i64 } %1, i64 %_0.1, 1
  ret { i64, i64 } %2
}

; core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17ha1446aa7340e9562E"(ptr align 4 %self) unnamed_addr #1 {
start:
; call <core::ops::range::RangeInclusive<T> as core::iter::range::RangeInclusiveIteratorImpl>::spec_next
  %0 = call { i32, i32 } @"_ZN107_$LT$core..ops..range..RangeInclusive$LT$T$GT$$u20$as$u20$core..iter..range..RangeInclusiveIteratorImpl$GT$9spec_next17h25d7c03d95adf79bE"(ptr align 4 %self)
  %_0.0 = extractvalue { i32, i32 } %0, 0
  %_0.1 = extractvalue { i32, i32 } %0, 1
  %1 = insertvalue { i32, i32 } poison, i32 %_0.0, 0
  %2 = insertvalue { i32, i32 } %1, i32 %_0.1, 1
  ret { i32, i32 } %2
}

; core::iter::traits::iterator::Iterator::map
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator3map17h38748cb352285587E(ptr sret([40 x i8]) align 8 %_0, ptr align 8 %self, ptr align 8 %f.0, ptr align 8 %f.1) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  %0 = getelementptr inbounds i8, ptr %_0, i64 24
  store ptr %f.0, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %0, i64 8
  store ptr %f.1, ptr %1, align 8
  ret void
}

; core::iter::traits::iterator::Iterator::map
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator3map17h7f4196daf92efe8dE(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1, ptr align 8 %f) unnamed_addr #1 {
start:
  store ptr %self.0, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.1, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store ptr %f, ptr %1, align 8
  ret void
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal double @_ZN4core4iter6traits8iterator8Iterator3sum17h322f90d27fb6b93fE(ptr align 8 %self) unnamed_addr #0 {
start:
; call <f64 as core::iter::traits::accum::Sum>::sum
  %_0 = call double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum17h43780fd59f1e4d80E"(ptr align 8 %self)
  ret double %_0
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal double @_ZN4core4iter6traits8iterator8Iterator3sum17h8fe0e692d02505a0E(ptr align 8 %self) unnamed_addr #0 {
start:
; call <f64 as core::iter::traits::accum::Sum>::sum
  %_0 = call double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum17h32971aae8128c4a8E"(ptr align 8 %self)
  ret double %_0
}

; core::iter::traits::iterator::Iterator::sum
; Function Attrs: uwtable
define internal double @_ZN4core4iter6traits8iterator8Iterator3sum17he8b0c48ddc11cdb3E(ptr %self.0, ptr %self.1) unnamed_addr #0 {
start:
; call <f64 as core::iter::traits::accum::Sum<&f64>>::sum
  %_0 = call double @"_ZN69_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$f64$GT$$GT$3sum17h86d2f68a8fb08432E"(ptr %self.0, ptr %self.1)
  ret double %_0
}

; core::iter::traits::iterator::Iterator::product
; Function Attrs: uwtable
define internal i64 @_ZN4core4iter6traits8iterator8Iterator7product17h2137ed72368063c3E(ptr align 8 %self) unnamed_addr #0 {
start:
; call <u64 as core::iter::traits::accum::Product>::product
  %_0 = call i64 @"_ZN58_$LT$u64$u20$as$u20$core..iter..traits..accum..Product$GT$7product17hc33fa9d6900dbfadE"(ptr align 8 %self)
  ret i64 %_0
}

; core::iter::traits::iterator::Iterator::step_by
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator7step_by17h59e53cc6f958c81fE(ptr sret([40 x i8]) align 8 %_0, ptr align 8 %self, i64 %step) unnamed_addr #1 {
start:
; call core::iter::adapters::step_by::StepBy<I>::new
  call void @"_ZN4core4iter8adapters7step_by15StepBy$LT$I$GT$3new17hf559471d78c6bd45E"(ptr sret([40 x i8]) align 8 %_0, ptr align 8 %self, i64 %step)
  ret void
}

; core::iter::traits::iterator::Iterator::enumerate
; Function Attrs: inlinehint uwtable
define internal void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h20a7b10fd82a0585E(ptr sret([24 x i8]) align 8 %_0, ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  store ptr %self.0, ptr %_0, align 8
  %0 = getelementptr inbounds i8, ptr %_0, i64 8
  store ptr %self.1, ptr %0, align 8
  %1 = getelementptr inbounds i8, ptr %_0, i64 16
  store i64 0, ptr %1, align 8
  ret void
}

; core::iter::adapters::map::map_fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h3e02cdff30ae8a47E"(ptr align 8 %_1, double %acc, i64 %elt.0, ptr align 8 %elt.1) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_4 = getelementptr inbounds i8, ptr %_1, i64 16
  store i8 1, ptr %_10, align 1
; invoke _21_mathematical_functions::evaluate_polynomial::{{closure}}
  %_7 = invoke double @"_ZN26_21_mathematical_functions19evaluate_polynomial28_$u7b$$u7b$closure$u7d$$u7d$17h8cf3980e243dd01eE"(ptr align 8 %_1, i64 %elt.0, ptr align 8 %elt.1)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  %0 = load i8, ptr %_10, align 1
  %1 = trunc nuw i8 %0 to i1
  br i1 %1, label %bb4, label %bb3

funclet_bb5:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke <f64 as core::iter::traits::accum::Sum>::sum::{{closure}}
  %_0 = invoke double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17ha74cb6024f4156c4E"(ptr align 1 %_4, double %acc, double %_7)
          to label %bb2 unwind label %funclet_bb5

bb2:                                              ; preds = %bb1
  ret double %_0

bb3:                                              ; preds = %bb4, %bb5
  cleanupret from %cleanuppad unwind to caller

bb4:                                              ; preds = %bb5
  br label %bb3
}

; core::iter::adapters::map::map_fold::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h42ab1b54771c6ce7E"(ptr align 8 %_1, double %acc, ptr align 8 %elt) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_10 = alloca [1 x i8], align 1
  %_4 = getelementptr inbounds i8, ptr %_1, i64 8
  store i8 1, ptr %_10, align 1
; invoke _21_mathematical_functions::main::{{closure}}
  %_7 = invoke double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h11b7d5b6a3699f6cE"(ptr align 8 %_1, ptr align 8 %elt)
          to label %bb1 unwind label %funclet_bb5

bb5:                                              ; preds = %funclet_bb5
  %0 = load i8, ptr %_10, align 1
  %1 = trunc nuw i8 %0 to i1
  br i1 %1, label %bb4, label %bb3

funclet_bb5:                                      ; preds = %bb1, %start
  %cleanuppad = cleanuppad within none []
  br label %bb5

bb1:                                              ; preds = %start
  store i8 0, ptr %_10, align 1
; invoke <f64 as core::iter::traits::accum::Sum>::sum::{{closure}}
  %_0 = invoke double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17h808c38d833917620E"(ptr align 1 %_4, double %acc, double %_7)
          to label %bb2 unwind label %funclet_bb5

bb2:                                              ; preds = %bb1
  ret double %_0

bb3:                                              ; preds = %bb4, %bb5
  cleanupret from %cleanuppad unwind to caller

bb4:                                              ; preds = %bb5
  br label %bb3
}

; core::iter::adapters::step_by::StepBy<I>::new
; Function Attrs: inlinehint uwtable
define internal void @"_ZN4core4iter8adapters7step_by15StepBy$LT$I$GT$3new17hf559471d78c6bd45E"(ptr sret([40 x i8]) align 8 %_0, ptr align 8 %iter, i64 %step) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_7 = alloca [1 x i8], align 1
  %_5 = alloca [24 x i8], align 8
  %iter1 = alloca [24 x i8], align 8
  store i8 1, ptr %_7, align 1
  %0 = icmp eq i64 %step, 0
  br i1 %0, label %bb2, label %bb1

bb2:                                              ; preds = %start
; invoke core::panicking::panic
  invoke void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1 @alloc_4aead6e2018a46d0df208d5729447de7, i64 27, ptr align 8 @alloc_1d19af47b700b7580386eededda18b7b) #12
          to label %unreachable unwind label %funclet_bb6

bb1:                                              ; preds = %start
  store i8 0, ptr %_7, align 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_5, ptr align 8 %iter, i64 24, i1 false)
; invoke <T as core::iter::adapters::step_by::SpecRangeSetup<T>>::setup
  invoke void @"_ZN76_$LT$T$u20$as$u20$core..iter..adapters..step_by..SpecRangeSetup$LT$T$GT$$GT$5setup17hf7a2f539fb535c9aE"(ptr sret([24 x i8]) align 8 %iter1, ptr align 8 %_5, i64 %step)
          to label %bb3 unwind label %funclet_bb6

bb6:                                              ; preds = %funclet_bb6
  %1 = load i8, ptr %_7, align 1
  %2 = trunc nuw i8 %1 to i1
  br i1 %2, label %bb5, label %bb4

funclet_bb6:                                      ; preds = %bb1, %bb2
  %cleanuppad = cleanuppad within none []
  br label %bb6

unreachable:                                      ; preds = %bb2
  unreachable

bb3:                                              ; preds = %bb1
  %_6 = sub i64 %step, 1
  %3 = getelementptr inbounds i8, ptr %_0, i64 8
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %iter1, i64 24, i1 false)
  store i64 %_6, ptr %_0, align 8
  %4 = getelementptr inbounds i8, ptr %_0, i64 32
  store i8 1, ptr %4, align 8
  ret void

bb4:                                              ; preds = %bb5, %bb6
  cleanupret from %cleanuppad unwind to caller

bb5:                                              ; preds = %bb6
  br label %bb4
}

; core::array::<impl core::fmt::Debug for [T; N]>::fmt
; Function Attrs: uwtable
define internal zeroext i1 @"_ZN4core5array69_$LT$impl$u20$core..fmt..Debug$u20$for$u20$$u5b$T$u3b$$u20$N$u5d$$GT$3fmt17h54043c2f95831c25E"(ptr align 8 %self, ptr align 8 %f) unnamed_addr #0 {
start:
  %_4 = alloca [16 x i8], align 8
  store ptr %self, ptr %_4, align 8
  %0 = getelementptr inbounds i8, ptr %_4, i64 8
  store i64 10, ptr %0, align 8
; call <[T] as core::fmt::Debug>::fmt
  %_0 = call zeroext i1 @"_ZN48_$LT$$u5b$T$u5d$$u20$as$u20$core..fmt..Debug$GT$3fmt17h18d16c11083ea7fdE"(ptr align 8 %self, i64 10, ptr align 8 %f)
  ret i1 %_0
}

; core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
; Function Attrs: uwtable
define internal { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h207ee6bd1130210fE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw double, ptr %self, i64 6
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %self, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
; Function Attrs: uwtable
define internal { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h3e692486c2a7a882E"(ptr align 8 %self) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw i64, ptr %self, i64 8
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %self, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
; Function Attrs: uwtable
define internal { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha3e16c7299db7d5bE"(ptr align 8 %self) unnamed_addr #0 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw double, ptr %self, i64 5
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %self, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; core::slice::<impl [T]>::iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %self.0, i64 %self.1) unnamed_addr #1 {
start:
; call core::slice::iter::Iter<T>::new
  %0 = call { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h1f52c7589af5e855E"(ptr align 8 %self.0, i64 %self.1)
  %_0.0 = extractvalue { ptr, ptr } %0, 0
  %_0.1 = extractvalue { ptr, ptr } %0, 1
  %1 = insertvalue { ptr, ptr } poison, ptr %_0.0, 0
  %2 = insertvalue { ptr, ptr } %1, ptr %_0.1, 1
  ret { ptr, ptr } %2
}

; core::slice::iter::Iter<T>::new
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN4core5slice4iter13Iter$LT$T$GT$3new17h1f52c7589af5e855E"(ptr align 8 %slice.0, i64 %slice.1) unnamed_addr #1 {
start:
  %end_or_len = alloca [8 x i8], align 8
  br label %bb2

bb2:                                              ; preds = %start
  %_6 = getelementptr inbounds nuw double, ptr %slice.0, i64 %slice.1
  store ptr %_6, ptr %end_or_len, align 8
  br label %bb3

bb3:                                              ; preds = %bb2
  %_8 = load ptr, ptr %end_or_len, align 8
  %0 = insertvalue { ptr, ptr } poison, ptr %slice.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %_8, 1
  ret { ptr, ptr } %1

bb1:                                              ; No predecessors!
  unreachable
}

; <() as std::process::Termination>::report
; Function Attrs: inlinehint uwtable
define internal i32 @"_ZN54_$LT$$LP$$RP$$u20$as$u20$std..process..Termination$GT$6report17h1da288c24c9cf0bcE"() unnamed_addr #1 {
start:
  ret i32 0
}

; <f64 as core::iter::traits::accum::Sum>::sum
; Function Attrs: uwtable
define internal double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum17h32971aae8128c4a8E"(ptr align 8 %iter) unnamed_addr #0 {
start:
; call <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h62fdbae60cbb195eE"(ptr align 8 %iter, double -0.000000e+00)
  ret double %_0
}

; <f64 as core::iter::traits::accum::Sum>::sum
; Function Attrs: uwtable
define internal double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum17h43780fd59f1e4d80E"(ptr align 8 %iter) unnamed_addr #0 {
start:
; call <core::iter::adapters::map::Map<I,F> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN102_$LT$core..iter..adapters..map..Map$LT$I$C$F$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h7ab198c8c0e1541aE"(ptr align 8 %iter, double -0.000000e+00)
  ret double %_0
}

; <f64 as core::iter::traits::accum::Sum>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17h808c38d833917620E"(ptr align 1 %_1, double %a, double %b) unnamed_addr #1 {
start:
  %_0 = fadd double %a, %b
  ret double %_0
}

; <f64 as core::iter::traits::accum::Sum>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN54_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17ha74cb6024f4156c4E"(ptr align 1 %_1, double %a, double %b) unnamed_addr #1 {
start:
  %_0 = fadd double %a, %b
  ret double %_0
}

; <u64 as core::iter::traits::accum::Product>::product
; Function Attrs: uwtable
define internal i64 @"_ZN58_$LT$u64$u20$as$u20$core..iter..traits..accum..Product$GT$7product17hc33fa9d6900dbfadE"(ptr align 8 %iter) unnamed_addr #0 {
start:
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::fold
  %_0 = call i64 @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4fold17h2da35549c7ce007aE"(ptr align 8 %iter, i64 1)
  ret i64 %_0
}

; <u64 as core::iter::traits::accum::Product>::product::{{closure}}
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN58_$LT$u64$u20$as$u20$core..iter..traits..accum..Product$GT$7product28_$u7b$$u7b$closure$u7d$$u7d$17hf54c98ddb6ad069bE"(ptr align 1 %_1, i64 %a, i64 %b) unnamed_addr #1 {
start:
  %0 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %a, i64 %b)
  %_4.0 = extractvalue { i64, i1 } %0, 0
  %_4.1 = extractvalue { i64, i1 } %0, 1
  br i1 %_4.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  ret i64 %_4.0

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_2ff564f83739a041825038989c62f69d) #12
  unreachable
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { ptr, ptr } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h0badb94d3d6e74f9E"(ptr %self.0, ptr %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { ptr, ptr } poison, ptr %self.0, 0
  %1 = insertvalue { ptr, ptr } %0, ptr %self.1, 1
  ret { ptr, ptr } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h86587e98c73fb24cE"(i32 %self.0, i32 %self.1) unnamed_addr #1 {
start:
  %0 = insertvalue { i32, i32 } poison, i32 %self.0, 0
  %1 = insertvalue { i32, i32 } %0, i32 %self.1, 1
  ret { i32, i32 } %1
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h99abcbff33964f45E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd32c3260bdaa8df8E"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 24, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17heccd56fdfae1a28bE"(ptr sret([40 x i8]) align 8 %_0, ptr align 8 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %self, i64 40, i1 false)
  ret void
}

; <I as core::iter::traits::collect::IntoIterator>::into_iter
; Function Attrs: inlinehint uwtable
define internal void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf35c3f156ac73698E"(ptr sret([12 x i8]) align 4 %_0, ptr align 4 %self) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %_0, ptr align 4 %self, i64 12, i1 false)
  ret void
}

; <f64 as core::iter::traits::accum::Sum<&f64>>::sum
; Function Attrs: uwtable
define internal double @"_ZN69_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$f64$GT$$GT$3sum17h86d2f68a8fb08432E"(ptr %iter.0, ptr %iter.1) unnamed_addr #0 {
start:
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %_0 = call double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h5552d07c7008b1e6E"(ptr %iter.0, ptr %iter.1, double -0.000000e+00)
  ret double %_0
}

; <f64 as core::iter::traits::accum::Sum<&f64>>::sum::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN69_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$f64$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17h23f97d42aaa752d5E"(ptr align 1 %_1, double %a, ptr align 8 %b) unnamed_addr #1 {
start:
  %_4 = load double, ptr %b, align 8
  %_0 = fadd double %a, %_4
  ret double %_0
}

; <T as core::iter::adapters::step_by::SpecRangeSetup<T>>::setup
; Function Attrs: inlinehint uwtable
define internal void @"_ZN76_$LT$T$u20$as$u20$core..iter..adapters..step_by..SpecRangeSetup$LT$T$GT$$GT$5setup17hf7a2f539fb535c9aE"(ptr sret([24 x i8]) align 8 %_0, ptr align 8 %inner, i64 %_step) unnamed_addr #1 {
start:
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %_0, ptr align 8 %inner, i64 24, i1 false)
  ret void
}

; <core::ops::range::Range<T> as core::iter::range::RangeIteratorImpl>::spec_next
; Function Attrs: inlinehint uwtable
define internal { i32, i32 } @"_ZN89_$LT$core..ops..range..Range$LT$T$GT$$u20$as$u20$core..iter..range..RangeIteratorImpl$GT$9spec_next17hfbf092153df5d194E"(ptr align 4 %self) unnamed_addr #1 {
start:
  %_0 = alloca [8 x i8], align 4
  %_4 = getelementptr inbounds i8, ptr %self, i64 4
  %_3.i = load i32, ptr %self, align 4
  %_4.i = load i32, ptr %_4, align 4
  %_0.i = icmp ult i32 %_3.i, %_4.i
  br i1 %_0.i, label %bb2, label %bb4

bb4:                                              ; preds = %start
  store i32 0, ptr %_0, align 4
  br label %bb5

bb2:                                              ; preds = %start
  %old = load i32, ptr %self, align 4
; call <u32 as core::iter::range::Step>::forward_unchecked
  %_6 = call i32 @"_ZN47_$LT$u32$u20$as$u20$core..iter..range..Step$GT$17forward_unchecked17h2e18c2700d97843cE"(i32 %old, i64 1)
  store i32 %_6, ptr %self, align 4
  %0 = getelementptr inbounds i8, ptr %_0, i64 4
  store i32 %old, ptr %0, align 4
  store i32 1, ptr %_0, align 4
  br label %bb5

bb5:                                              ; preds = %bb2, %bb4
  %1 = load i32, ptr %_0, align 4
  %2 = getelementptr inbounds i8, ptr %_0, i64 4
  %3 = load i32, ptr %2, align 4
  %4 = insertvalue { i32, i32 } poison, i32 %1, 0
  %5 = insertvalue { i32, i32 } %4, i32 %3, 1
  ret { i32, i32 } %5
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h1e41dc294c87f2c0E"(ptr %0, ptr %1, double %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store double %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store double %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load double, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw double, ptr %self1, i64 %count
; invoke _21_mathematical_functions::main::{{closure}}
  %_19 = invoke double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h65730c29a30aff68E"(ptr align 1 %f, double %_22, ptr align 8 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store double %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %self2, i64 1) #13
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load double, ptr %acc, align 8
  store double %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load double, ptr %_0, align 8
  ret double %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h5552d07c7008b1e6E"(ptr %0, ptr %1, double %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store double %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store double %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load double, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw double, ptr %self1, i64 %count
; invoke <f64 as core::iter::traits::accum::Sum<&f64>>::sum::{{closure}}
  %_19 = invoke double @"_ZN69_$LT$f64$u20$as$u20$core..iter..traits..accum..Sum$LT$$RF$f64$GT$$GT$3sum28_$u7b$$u7b$closure$u7d$$u7d$17h23f97d42aaa752d5E"(ptr align 1 %f, double %_22, ptr align 8 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store double %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %self2, i64 1) #13
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load double, ptr %acc, align 8
  store double %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load double, ptr %_0, align 8
  ret double %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h5b00ae5205c64e86E"(ptr %0, ptr %1, double %init, ptr align 8 %f) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store double %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store double %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load double, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw double, ptr %self1, i64 %count
; invoke <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::fold::enumerate::{{closure}}
  %_19 = invoke double @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold9enumerate28_$u7b$$u7b$closure$u7d$$u7d$17h40aeba57fb090764E"(ptr align 8 %f, double %_22, ptr align 8 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store double %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %self2, i64 1) #13
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load double, ptr %acc, align 8
  store double %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load double, ptr %_0, align 8
  ret double %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h932739ff6f99e483E"(ptr %0, ptr %1, double %init) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [0 x i8], align 1
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %2 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %2, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %3, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %4 = icmp eq ptr %_36, %_37
  %5 = zext i1 %4 to i8
  store i8 %5, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %6 = load i8, ptr %_4, align 1
  %7 = trunc nuw i8 %6 to i1
  br i1 %7, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store double %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store double %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %8 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %8, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %9 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %10 = load i8, ptr %_33, align 1
  %11 = trunc nuw i8 %10 to i1
  br i1 %11, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %9, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load double, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw double, ptr %self1, i64 %count
; invoke _21_mathematical_functions::main::{{closure}}
  %_19 = invoke double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h65e770d911767748E"(ptr align 1 %f, double %_22, ptr align 8 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store double %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %self2, i64 1) #13
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %12 = load double, ptr %acc, align 8
  store double %12, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %13 = load double, ptr %_0, align 8
  ret double %13

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %14 = load i8, ptr %_34, align 1
  %15 = trunc nuw i8 %14 to i1
  br i1 %15, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
; Function Attrs: inlinehint uwtable
define internal double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17hdb99542e9fa61958E"(ptr %0, ptr %1, double %init, ptr align 8 %2) unnamed_addr #1 personality ptr @__CxxFrameHandler3 {
start:
  %_34 = alloca [1 x i8], align 1
  %_33 = alloca [1 x i8], align 1
  %len = alloca [8 x i8], align 8
  %i = alloca [8 x i8], align 8
  %acc = alloca [8 x i8], align 8
  %end = alloca [8 x i8], align 8
  %_4 = alloca [1 x i8], align 1
  %_0 = alloca [8 x i8], align 8
  %f = alloca [8 x i8], align 8
  %self = alloca [16 x i8], align 8
  store ptr %0, ptr %self, align 8
  %3 = getelementptr inbounds i8, ptr %self, i64 8
  store ptr %1, ptr %3, align 8
  store ptr %2, ptr %f, align 8
  br label %bb2

bb2:                                              ; preds = %start
  %4 = getelementptr inbounds i8, ptr %self, i64 8
  %_8 = load ptr, ptr %4, align 8
  store ptr %_8, ptr %end, align 8
  %_36 = load ptr, ptr %self, align 8
  %_37 = load ptr, ptr %end, align 8
  %5 = icmp eq ptr %_36, %_37
  %6 = zext i1 %5 to i8
  store i8 %6, ptr %_4, align 1
  br label %bb3

bb3:                                              ; preds = %bb2
  %7 = load i8, ptr %_4, align 1
  %8 = trunc nuw i8 %7 to i1
  br i1 %8, label %bb4, label %bb5

bb5:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store i8 1, ptr %_33, align 1
  store double %init, ptr %acc, align 8
  store i64 0, ptr %i, align 8
  br label %bb7

bb4:                                              ; preds = %bb3
  store i8 0, ptr %_34, align 1
  store double %init, ptr %_0, align 8
  br label %bb14

bb7:                                              ; preds = %bb5
  %9 = getelementptr inbounds i8, ptr %self, i64 8
  %_17 = load ptr, ptr %9, align 8
  %_18 = load ptr, ptr %self, align 8
; invoke core::ptr::non_null::NonNull<T>::offset_from_unsigned
  %10 = invoke i64 @"_ZN4core3ptr8non_null16NonNull$LT$T$GT$20offset_from_unsigned17h2e0fe6fe272c501aE"(ptr %_17, ptr %_18)
          to label %bb8 unwind label %funclet_bb18

bb18:                                             ; preds = %funclet_bb18
  %11 = load i8, ptr %_33, align 1
  %12 = trunc nuw i8 %11 to i1
  br i1 %12, label %bb17, label %bb15

funclet_bb18:                                     ; preds = %bb10, %bb7
  %cleanuppad = cleanuppad within none []
  br label %bb18

bb8:                                              ; preds = %bb7
  store i64 %10, ptr %len, align 8
  br label %bb9

bb9:                                              ; preds = %bb8
  br label %bb10

bb10:                                             ; preds = %bb13, %bb9
  store i8 0, ptr %_33, align 1
  %_22 = load double, ptr %acc, align 8
  %self1 = load ptr, ptr %self, align 8
  %count = load i64, ptr %i, align 8
  %_38 = getelementptr inbounds nuw double, ptr %self1, i64 %count
; invoke core::iter::adapters::map::map_fold::{{closure}}
  %_19 = invoke double @"_ZN4core4iter8adapters3map8map_fold28_$u7b$$u7b$closure$u7d$$u7d$17h42ab1b54771c6ce7E"(ptr align 8 %f, double %_22, ptr align 8 %_38)
          to label %bb11 unwind label %funclet_bb18

bb11:                                             ; preds = %bb10
  store double %_19, ptr %acc, align 8
  %self2 = load i64, ptr %i, align 8
  br label %bb21

bb21:                                             ; preds = %bb11
; call core::num::<impl usize>::unchecked_add::precondition_check
  call void @"_ZN4core3num23_$LT$impl$u20$usize$GT$13unchecked_add18precondition_check17hdfad4428f5e640b8E"(i64 %self2, i64 1) #13
  br label %bb22

bb22:                                             ; preds = %bb21
  %_28 = add nuw i64 %self2, 1
  store i64 %_28, ptr %i, align 8
  %_31 = load i64, ptr %i, align 8
  %_32 = load i64, ptr %len, align 8
  %_30 = icmp eq i64 %_31, %_32
  br i1 %_30, label %bb12, label %bb13

bb13:                                             ; preds = %bb22
  br label %bb10

bb12:                                             ; preds = %bb22
  %13 = load double, ptr %acc, align 8
  store double %13, ptr %_0, align 8
  br label %bb14

bb14:                                             ; preds = %bb4, %bb12
  %14 = load double, ptr %_0, align 8
  ret double %14

bb15:                                             ; preds = %bb17, %bb18
  cleanupret from %cleanuppad unwind label %funclet_bb20

bb17:                                             ; preds = %bb18
  br label %bb15

bb20:                                             ; preds = %funclet_bb20
  %15 = load i8, ptr %_34, align 1
  %16 = trunc nuw i8 %15 to i1
  br i1 %16, label %bb19, label %bb16

funclet_bb20:                                     ; preds = %bb15
  %cleanuppad3 = cleanuppad within none []
  br label %bb20

bb16:                                             ; preds = %bb19, %bb20
  cleanupret from %cleanuppad3 unwind to caller

bb19:                                             ; preds = %bb20
  br label %bb16

bb1:                                              ; No predecessors!
  unreachable

bb6:                                              ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha22cb5e39ff255fdE"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw i64, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
; Function Attrs: inlinehint uwtable
define internal align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %self) unnamed_addr #1 {
start:
  %_13 = alloca [8 x i8], align 8
  %_9 = alloca [8 x i8], align 8
  %ptr = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %0 = load ptr, ptr %self, align 8
  store ptr %0, ptr %ptr, align 8
  %1 = getelementptr inbounds i8, ptr %self, i64 8
  %end_or_len = load ptr, ptr %1, align 8
  br label %bb4

bb4:                                              ; preds = %start
  store ptr %end_or_len, ptr %_9, align 8
  %_16 = load ptr, ptr %ptr, align 8
  %_17 = load ptr, ptr %_9, align 8
  %_6 = icmp eq ptr %_16, %_17
  br i1 %_6, label %bb5, label %bb6

bb6:                                              ; preds = %bb4
  %_19 = load ptr, ptr %ptr, align 8
  %_18 = getelementptr inbounds nuw double, ptr %_19, i64 1
  store ptr %_18, ptr %self, align 8
  br label %bb7

bb5:                                              ; preds = %bb4
  store ptr null, ptr %_0, align 8
  br label %bb8

bb7:                                              ; preds = %bb6
  %2 = load ptr, ptr %ptr, align 8
  store ptr %2, ptr %_13, align 8
  %_20 = load ptr, ptr %ptr, align 8
  store ptr %_20, ptr %_0, align 8
  br label %bb9

bb9:                                              ; preds = %bb8, %bb7
  %3 = load ptr, ptr %_0, align 8
  ret ptr %3

bb8:                                              ; preds = %bb5
  br label %bb9

bb1:                                              ; No predecessors!
  unreachable

bb2:                                              ; No predecessors!
  unreachable

bb3:                                              ; No predecessors!
  unreachable

bb10:                                             ; No predecessors!
  unreachable

bb11:                                             ; No predecessors!
  unreachable
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::from_output
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$11from_output17hb9e99d0d1674be58E"(i64 %x) unnamed_addr #1 {
start:
  ret i64 %x
}

; <core::ops::try_trait::NeverShortCircuit<T> as core::ops::try_trait::Try>::branch
; Function Attrs: inlinehint uwtable
define internal i64 @"_ZN94_$LT$core..ops..try_trait..NeverShortCircuit$LT$T$GT$$u20$as$u20$core..ops..try_trait..Try$GT$6branch17h246dfbd3de104048E"(i64 %self) unnamed_addr #1 {
start:
  %_0 = alloca [8 x i8], align 8
  store i64 %self, ptr %_0, align 8
  %0 = load i64, ptr %_0, align 8
  ret i64 %0
}

; _21_mathematical_functions::main
; Function Attrs: uwtable
define internal void @_ZN26_21_mathematical_functions4main17hd3edc837827e8a33E() unnamed_addr #0 {
start:
  %_1478 = alloca [48 x i8], align 8
  %_1475 = alloca [48 x i8], align 8
  %_1472 = alloca [16 x i8], align 8
  %_1471 = alloca [16 x i8], align 8
  %_1470 = alloca [48 x i8], align 8
  %_1469 = alloca [16 x i8], align 8
  %_1468 = alloca [16 x i8], align 8
  %_1467 = alloca [48 x i8], align 8
  %_1466 = alloca [96 x i8], align 8
  %_1462 = alloca [16 x i8], align 8
  %_1460 = alloca [16 x i8], align 8
  %_1459 = alloca [32 x i8], align 8
  %_1454 = alloca [48 x i8], align 8
  %result = alloca [8 x i8], align 8
  %x = alloca [8 x i8], align 8
  %_1446 = alloca [8 x i8], align 8
  %iter12 = alloca [16 x i8], align 8
  %_1441 = alloca [48 x i8], align 8
  %x_values = alloca [40 x i8], align 8
  %coefficients = alloca [24 x i8], align 8
  %_1436 = alloca [48 x i8], align 8
  %_1433 = alloca [48 x i8], align 8
  %_1430 = alloca [16 x i8], align 8
  %_1429 = alloca [16 x i8], align 8
  %_1428 = alloca [48 x i8], align 8
  %_1427 = alloca [48 x i8], align 8
  %_1423 = alloca [16 x i8], align 8
  %_1422 = alloca [16 x i8], align 8
  %_1417 = alloca [48 x i8], align 8
  %_1414 = alloca [16 x i8], align 8
  %_1413 = alloca [16 x i8], align 8
  %_1412 = alloca [48 x i8], align 8
  %_1411 = alloca [16 x i8], align 8
  %_1410 = alloca [16 x i8], align 8
  %_1409 = alloca [48 x i8], align 8
  %_1408 = alloca [96 x i8], align 8
  %_1404 = alloca [16 x i8], align 8
  %_1402 = alloca [16 x i8], align 8
  %_1401 = alloca [32 x i8], align 8
  %_1396 = alloca [48 x i8], align 8
  %_1393 = alloca [16 x i8], align 8
  %_1391 = alloca [16 x i8], align 8
  %_1390 = alloca [32 x i8], align 8
  %_1387 = alloca [48 x i8], align 8
  %_1384 = alloca [16 x i8], align 8
  %_1382 = alloca [16 x i8], align 8
  %_1381 = alloca [32 x i8], align 8
  %_1378 = alloca [48 x i8], align 8
  %_1375 = alloca [16 x i8], align 8
  %_1373 = alloca [16 x i8], align 8
  %_1372 = alloca [32 x i8], align 8
  %_1369 = alloca [48 x i8], align 8
  %magnitude1 = alloca [8 x i8], align 8
  %product = alloca [16 x i8], align 8
  %sum11 = alloca [16 x i8], align 8
  %complex2 = alloca [16 x i8], align 8
  %complex1 = alloca [16 x i8], align 8
  %_1334 = alloca [48 x i8], align 8
  %_1331 = alloca [48 x i8], align 8
  %_1328 = alloca [48 x i8], align 8
  %_1326 = alloca [8 x i8], align 8
  %_1324 = alloca [16 x i8], align 8
  %_1323 = alloca [16 x i8], align 8
  %_1320 = alloca [48 x i8], align 8
  %_1315 = alloca [8 x i8], align 4
  %iter10 = alloca [8 x i8], align 4
  %_1310 = alloca [48 x i8], align 8
  %_1307 = alloca [48 x i8], align 8
  %_1304 = alloca [48 x i8], align 8
  %_1302 = alloca [1 x i8], align 1
  %_1300 = alloca [16 x i8], align 8
  %_1298 = alloca [16 x i8], align 8
  %_1297 = alloca [32 x i8], align 8
  %_1294 = alloca [48 x i8], align 8
  %num = alloca [8 x i8], align 8
  %_1289 = alloca [8 x i8], align 8
  %iter9 = alloca [16 x i8], align 8
  %_1284 = alloca [48 x i8], align 8
  %test_numbers = alloca [64 x i8], align 8
  %_1280 = alloca [16 x i8], align 8
  %_1278 = alloca [16 x i8], align 8
  %_1276 = alloca [16 x i8], align 8
  %_1275 = alloca [48 x i8], align 8
  %_1272 = alloca [48 x i8], align 8
  %_1269 = alloca [16 x i8], align 8
  %_1267 = alloca [16 x i8], align 8
  %_1265 = alloca [16 x i8], align 8
  %_1264 = alloca [48 x i8], align 8
  %_1261 = alloca [48 x i8], align 8
  %lcm_result = alloca [8 x i8], align 8
  %gcd_result = alloca [8 x i8], align 8
  %num2 = alloca [8 x i8], align 8
  %num1 = alloca [8 x i8], align 8
  %_1251 = alloca [48 x i8], align 8
  %_1248 = alloca [48 x i8], align 8
  %_1245 = alloca [16 x i8], align 8
  %_1243 = alloca [16 x i8], align 8
  %_1241 = alloca [16 x i8], align 8
  %_1240 = alloca [48 x i8], align 8
  %_1237 = alloca [48 x i8], align 8
  %_1234 = alloca [16 x i8], align 8
  %_1232 = alloca [16 x i8], align 8
  %_1230 = alloca [16 x i8], align 8
  %_1229 = alloca [48 x i8], align 8
  %_1226 = alloca [48 x i8], align 8
  %permutations = alloca [8 x i8], align 8
  %combinations = alloca [8 x i8], align 8
  %r = alloca [8 x i8], align 8
  %n8 = alloca [8 x i8], align 8
  %_1206 = alloca [48 x i8], align 8
  %_1203 = alloca [16 x i8], align 8
  %_1201 = alloca [16 x i8], align 8
  %_1200 = alloca [32 x i8], align 8
  %_1197 = alloca [48 x i8], align 8
  %fact = alloca [8 x i8], align 8
  %n = alloca [8 x i8], align 8
  %_1191 = alloca [16 x i8], align 8
  %iter7 = alloca [24 x i8], align 8
  %_1189 = alloca [24 x i8], align 8
  %_1188 = alloca [24 x i8], align 8
  %_1186 = alloca [48 x i8], align 8
  %_1183 = alloca [48 x i8], align 8
  %_1180 = alloca [16 x i8], align 8
  %_1179 = alloca [16 x i8], align 8
  %_1178 = alloca [48 x i8], align 8
  %_1177 = alloca [16 x i8], align 8
  %_1176 = alloca [16 x i8], align 8
  %_1175 = alloca [48 x i8], align 8
  %_1174 = alloca [96 x i8], align 8
  %_1170 = alloca [16 x i8], align 8
  %_1168 = alloca [16 x i8], align 8
  %_1167 = alloca [32 x i8], align 8
  %_1162 = alloca [48 x i8], align 8
  %_1159 = alloca [16 x i8], align 8
  %_1158 = alloca [16 x i8], align 8
  %_1157 = alloca [48 x i8], align 8
  %_1156 = alloca [48 x i8], align 8
  %_1152 = alloca [16 x i8], align 8
  %_1151 = alloca [16 x i8], align 8
  %_1146 = alloca [48 x i8], align 8
  %_1143 = alloca [16 x i8], align 8
  %_1141 = alloca [16 x i8], align 8
  %_1140 = alloca [32 x i8], align 8
  %_1137 = alloca [48 x i8], align 8
  %_1134 = alloca [16 x i8], align 8
  %_1132 = alloca [16 x i8], align 8
  %_1131 = alloca [32 x i8], align 8
  %_1128 = alloca [48 x i8], align 8
  %midpoint = alloca [16 x i8], align 8
  %distance = alloca [8 x i8], align 8
  %point2 = alloca [16 x i8], align 8
  %point1 = alloca [16 x i8], align 8
  %_1104 = alloca [48 x i8], align 8
  %_1101 = alloca [48 x i8], align 8
  %_1098 = alloca [16 x i8], align 8
  %_1097 = alloca [16 x i8], align 8
  %_1096 = alloca [48 x i8], align 8
  %_1095 = alloca [48 x i8], align 8
  %_1091 = alloca [16 x i8], align 8
  %_1090 = alloca [16 x i8], align 8
  %_1085 = alloca [48 x i8], align 8
  %_1082 = alloca [16 x i8], align 8
  %_1080 = alloca [16 x i8], align 8
  %_1078 = alloca [16 x i8], align 8
  %_1077 = alloca [48 x i8], align 8
  %_1074 = alloca [48 x i8], align 8
  %triangle_area = alloca [8 x i8], align 8
  %side_c = alloca [8 x i8], align 8
  %side_b = alloca [8 x i8], align 8
  %side_a = alloca [8 x i8], align 8
  %_1058 = alloca [16 x i8], align 8
  %_1057 = alloca [16 x i8], align 8
  %_1056 = alloca [48 x i8], align 8
  %_1055 = alloca [48 x i8], align 8
  %_1051 = alloca [16 x i8], align 8
  %_1050 = alloca [16 x i8], align 8
  %_1045 = alloca [48 x i8], align 8
  %_1042 = alloca [16 x i8], align 8
  %_1041 = alloca [16 x i8], align 8
  %_1040 = alloca [48 x i8], align 8
  %_1039 = alloca [48 x i8], align 8
  %_1035 = alloca [16 x i8], align 8
  %_1034 = alloca [16 x i8], align 8
  %_1029 = alloca [48 x i8], align 8
  %_1026 = alloca [16 x i8], align 8
  %_1025 = alloca [16 x i8], align 8
  %_1024 = alloca [48 x i8], align 8
  %_1023 = alloca [48 x i8], align 8
  %_1019 = alloca [16 x i8], align 8
  %_1018 = alloca [16 x i8], align 8
  %_1013 = alloca [48 x i8], align 8
  %_1010 = alloca [16 x i8], align 8
  %_1008 = alloca [16 x i8], align 8
  %_1007 = alloca [32 x i8], align 8
  %_1004 = alloca [48 x i8], align 8
  %rect_diagonal = alloca [8 x i8], align 8
  %rect_perimeter = alloca [8 x i8], align 8
  %rect_area = alloca [8 x i8], align 8
  %width = alloca [8 x i8], align 8
  %length = alloca [8 x i8], align 8
  %_992 = alloca [16 x i8], align 8
  %_991 = alloca [16 x i8], align 8
  %_990 = alloca [48 x i8], align 8
  %_989 = alloca [48 x i8], align 8
  %_985 = alloca [16 x i8], align 8
  %_984 = alloca [16 x i8], align 8
  %_979 = alloca [48 x i8], align 8
  %_976 = alloca [16 x i8], align 8
  %_975 = alloca [16 x i8], align 8
  %_974 = alloca [48 x i8], align 8
  %_973 = alloca [48 x i8], align 8
  %_969 = alloca [16 x i8], align 8
  %_968 = alloca [16 x i8], align 8
  %_963 = alloca [48 x i8], align 8
  %_960 = alloca [16 x i8], align 8
  %_959 = alloca [16 x i8], align 8
  %_956 = alloca [48 x i8], align 8
  %circle_circumference = alloca [8 x i8], align 8
  %circle_area = alloca [8 x i8], align 8
  %radius = alloca [8 x i8], align 8
  %_948 = alloca [48 x i8], align 8
  %_945 = alloca [48 x i8], align 8
  %_942 = alloca [16 x i8], align 8
  %_941 = alloca [16 x i8], align 8
  %_940 = alloca [48 x i8], align 8
  %_939 = alloca [48 x i8], align 8
  %_935 = alloca [16 x i8], align 8
  %_934 = alloca [16 x i8], align 8
  %_929 = alloca [48 x i8], align 8
  %_926 = alloca [16 x i8], align 8
  %_925 = alloca [16 x i8], align 8
  %_924 = alloca [48 x i8], align 8
  %_923 = alloca [48 x i8], align 8
  %_919 = alloca [16 x i8], align 8
  %_918 = alloca [16 x i8], align 8
  %_913 = alloca [48 x i8], align 8
  %_910 = alloca [16 x i8], align 8
  %_909 = alloca [16 x i8], align 8
  %_908 = alloca [48 x i8], align 8
  %_907 = alloca [48 x i8], align 8
  %_904 = alloca [8 x i8], align 8
  %_902 = alloca [16 x i8], align 8
  %_901 = alloca [16 x i8], align 8
  %_896 = alloca [48 x i8], align 8
  %_893 = alloca [16 x i8], align 8
  %_892 = alloca [16 x i8], align 8
  %_891 = alloca [48 x i8], align 8
  %_890 = alloca [48 x i8], align 8
  %_886 = alloca [16 x i8], align 8
  %_885 = alloca [16 x i8], align 8
  %_880 = alloca [48 x i8], align 8
  %_877 = alloca [16 x i8], align 8
  %_876 = alloca [16 x i8], align 8
  %_875 = alloca [48 x i8], align 8
  %_874 = alloca [48 x i8], align 8
  %_870 = alloca [16 x i8], align 8
  %_869 = alloca [16 x i8], align 8
  %_864 = alloca [48 x i8], align 8
  %_861 = alloca [16 x i8], align 8
  %_860 = alloca [16 x i8], align 8
  %_859 = alloca [48 x i8], align 8
  %_858 = alloca [48 x i8], align 8
  %_854 = alloca [16 x i8], align 8
  %_853 = alloca [16 x i8], align 8
  %_848 = alloca [48 x i8], align 8
  %_845 = alloca [16 x i8], align 8
  %_844 = alloca [16 x i8], align 8
  %_843 = alloca [48 x i8], align 8
  %_842 = alloca [48 x i8], align 8
  %_838 = alloca [16 x i8], align 8
  %_837 = alloca [16 x i8], align 8
  %_832 = alloca [48 x i8], align 8
  %std_dev = alloca [8 x i8], align 8
  %_820 = alloca [24 x i8], align 8
  %variance = alloca [8 x i8], align 8
  %max_val = alloca [8 x i8], align 8
  %min_val = alloca [8 x i8], align 8
  %mean = alloca [8 x i8], align 8
  %sum = alloca [8 x i8], align 8
  %_799 = alloca [16 x i8], align 8
  %_798 = alloca [16 x i8], align 8
  %_795 = alloca [48 x i8], align 8
  %data = alloca [80 x i8], align 8
  %_791 = alloca [48 x i8], align 8
  %_788 = alloca [48 x i8], align 8
  %_785 = alloca [16 x i8], align 8
  %_784 = alloca [16 x i8], align 8
  %_783 = alloca [48 x i8], align 8
  %_782 = alloca [16 x i8], align 8
  %_781 = alloca [16 x i8], align 8
  %_780 = alloca [48 x i8], align 8
  %_779 = alloca [96 x i8], align 8
  %_776 = alloca [8 x i8], align 8
  %_774 = alloca [16 x i8], align 8
  %_772 = alloca [16 x i8], align 8
  %_771 = alloca [32 x i8], align 8
  %_766 = alloca [48 x i8], align 8
  %_764 = alloca [8 x i8], align 8
  %_762 = alloca [16 x i8], align 8
  %_760 = alloca [16 x i8], align 8
  %_759 = alloca [32 x i8], align 8
  %_756 = alloca [48 x i8], align 8
  %_754 = alloca [8 x i8], align 8
  %_752 = alloca [16 x i8], align 8
  %_750 = alloca [16 x i8], align 8
  %_749 = alloca [32 x i8], align 8
  %_746 = alloca [48 x i8], align 8
  %_744 = alloca [8 x i8], align 8
  %_742 = alloca [16 x i8], align 8
  %_740 = alloca [16 x i8], align 8
  %_739 = alloca [32 x i8], align 8
  %_736 = alloca [48 x i8], align 8
  %_734 = alloca [8 x i8], align 8
  %_732 = alloca [16 x i8], align 8
  %_730 = alloca [16 x i8], align 8
  %_729 = alloca [32 x i8], align 8
  %_726 = alloca [48 x i8], align 8
  %_723 = alloca [16 x i8], align 8
  %_722 = alloca [16 x i8], align 8
  %_719 = alloca [48 x i8], align 8
  %val6 = alloca [8 x i8], align 8
  %_714 = alloca [8 x i8], align 8
  %iter5 = alloca [16 x i8], align 8
  %decimal_values = alloca [48 x i8], align 8
  %_708 = alloca [48 x i8], align 8
  %_705 = alloca [48 x i8], align 8
  %_702 = alloca [16 x i8], align 8
  %_701 = alloca [16 x i8], align 8
  %_700 = alloca [48 x i8], align 8
  %_699 = alloca [16 x i8], align 8
  %_698 = alloca [16 x i8], align 8
  %_697 = alloca [48 x i8], align 8
  %_696 = alloca [96 x i8], align 8
  %_693 = alloca [8 x i8], align 8
  %_691 = alloca [16 x i8], align 8
  %_689 = alloca [16 x i8], align 8
  %_688 = alloca [32 x i8], align 8
  %_683 = alloca [48 x i8], align 8
  %_680 = alloca [16 x i8], align 8
  %_679 = alloca [16 x i8], align 8
  %_678 = alloca [48 x i8], align 8
  %_677 = alloca [16 x i8], align 8
  %_676 = alloca [16 x i8], align 8
  %_675 = alloca [48 x i8], align 8
  %_674 = alloca [96 x i8], align 8
  %_671 = alloca [8 x i8], align 8
  %_669 = alloca [16 x i8], align 8
  %_667 = alloca [16 x i8], align 8
  %_666 = alloca [32 x i8], align 8
  %_661 = alloca [48 x i8], align 8
  %_658 = alloca [16 x i8], align 8
  %_657 = alloca [16 x i8], align 8
  %_656 = alloca [48 x i8], align 8
  %_655 = alloca [16 x i8], align 8
  %_654 = alloca [16 x i8], align 8
  %_653 = alloca [48 x i8], align 8
  %_652 = alloca [96 x i8], align 8
  %_649 = alloca [8 x i8], align 8
  %_647 = alloca [16 x i8], align 8
  %_645 = alloca [16 x i8], align 8
  %_644 = alloca [32 x i8], align 8
  %_639 = alloca [48 x i8], align 8
  %_636 = alloca [16 x i8], align 8
  %_635 = alloca [16 x i8], align 8
  %_632 = alloca [48 x i8], align 8
  %val4 = alloca [8 x i8], align 8
  %_627 = alloca [8 x i8], align 8
  %iter3 = alloca [16 x i8], align 8
  %hyp_values = alloca [40 x i8], align 8
  %_621 = alloca [48 x i8], align 8
  %_618 = alloca [48 x i8], align 8
  %_615 = alloca [16 x i8], align 8
  %_614 = alloca [16 x i8], align 8
  %_613 = alloca [48 x i8], align 8
  %_612 = alloca [16 x i8], align 8
  %_611 = alloca [16 x i8], align 8
  %_610 = alloca [48 x i8], align 8
  %_609 = alloca [16 x i8], align 8
  %_608 = alloca [16 x i8], align 8
  %_607 = alloca [48 x i8], align 8
  %_606 = alloca [144 x i8], align 8
  %_602 = alloca [8 x i8], align 8
  %_600 = alloca [16 x i8], align 8
  %_599 = alloca [8 x i8], align 8
  %_597 = alloca [16 x i8], align 8
  %_595 = alloca [16 x i8], align 8
  %_594 = alloca [48 x i8], align 8
  %_589 = alloca [48 x i8], align 8
  %_586 = alloca [16 x i8], align 8
  %_585 = alloca [16 x i8], align 8
  %_584 = alloca [48 x i8], align 8
  %_583 = alloca [16 x i8], align 8
  %_582 = alloca [16 x i8], align 8
  %_581 = alloca [48 x i8], align 8
  %_580 = alloca [16 x i8], align 8
  %_579 = alloca [16 x i8], align 8
  %_578 = alloca [48 x i8], align 8
  %_577 = alloca [144 x i8], align 8
  %_573 = alloca [8 x i8], align 8
  %_571 = alloca [16 x i8], align 8
  %_570 = alloca [8 x i8], align 8
  %_568 = alloca [16 x i8], align 8
  %_566 = alloca [16 x i8], align 8
  %_565 = alloca [48 x i8], align 8
  %_560 = alloca [48 x i8], align 8
  %_557 = alloca [16 x i8], align 8
  %_556 = alloca [16 x i8], align 8
  %_555 = alloca [48 x i8], align 8
  %_554 = alloca [16 x i8], align 8
  %_553 = alloca [16 x i8], align 8
  %_552 = alloca [48 x i8], align 8
  %_551 = alloca [16 x i8], align 8
  %_550 = alloca [16 x i8], align 8
  %_549 = alloca [48 x i8], align 8
  %_548 = alloca [144 x i8], align 8
  %_544 = alloca [8 x i8], align 8
  %_542 = alloca [16 x i8], align 8
  %_541 = alloca [8 x i8], align 8
  %_539 = alloca [16 x i8], align 8
  %_537 = alloca [16 x i8], align 8
  %_536 = alloca [48 x i8], align 8
  %_531 = alloca [48 x i8], align 8
  %_528 = alloca [16 x i8], align 8
  %_527 = alloca [16 x i8], align 8
  %_524 = alloca [48 x i8], align 8
  %val = alloca [8 x i8], align 8
  %_517 = alloca [8 x i8], align 8
  %iter2 = alloca [16 x i8], align 8
  %values = alloca [40 x i8], align 8
  %_511 = alloca [48 x i8], align 8
  %_508 = alloca [48 x i8], align 8
  %_505 = alloca [16 x i8], align 8
  %_504 = alloca [16 x i8], align 8
  %_501 = alloca [48 x i8], align 8
  %_498 = alloca [16 x i8], align 8
  %_497 = alloca [16 x i8], align 8
  %_496 = alloca [48 x i8], align 8
  %_495 = alloca [16 x i8], align 8
  %_494 = alloca [16 x i8], align 8
  %_493 = alloca [48 x i8], align 8
  %_492 = alloca [96 x i8], align 8
  %_489 = alloca [8 x i8], align 8
  %_487 = alloca [16 x i8], align 8
  %_485 = alloca [16 x i8], align 8
  %_484 = alloca [32 x i8], align 8
  %_479 = alloca [48 x i8], align 8
  %_474 = alloca [16 x i8], align 8
  %_473 = alloca [16 x i8], align 8
  %_472 = alloca [48 x i8], align 8
  %_471 = alloca [16 x i8], align 8
  %_470 = alloca [16 x i8], align 8
  %_469 = alloca [48 x i8], align 8
  %_468 = alloca [96 x i8], align 8
  %_465 = alloca [8 x i8], align 8
  %_463 = alloca [16 x i8], align 8
  %_461 = alloca [16 x i8], align 8
  %_460 = alloca [32 x i8], align 8
  %_455 = alloca [48 x i8], align 8
  %_452 = alloca [16 x i8], align 8
  %_451 = alloca [16 x i8], align 8
  %_450 = alloca [48 x i8], align 8
  %_449 = alloca [16 x i8], align 8
  %_448 = alloca [16 x i8], align 8
  %_447 = alloca [48 x i8], align 8
  %_446 = alloca [96 x i8], align 8
  %_443 = alloca [8 x i8], align 8
  %_441 = alloca [16 x i8], align 8
  %_439 = alloca [16 x i8], align 8
  %_438 = alloca [32 x i8], align 8
  %_433 = alloca [48 x i8], align 8
  %_430 = alloca [16 x i8], align 8
  %_429 = alloca [16 x i8], align 8
  %_428 = alloca [48 x i8], align 8
  %_427 = alloca [16 x i8], align 8
  %_426 = alloca [16 x i8], align 8
  %_425 = alloca [48 x i8], align 8
  %_424 = alloca [96 x i8], align 8
  %_420 = alloca [16 x i8], align 8
  %_418 = alloca [16 x i8], align 8
  %_417 = alloca [32 x i8], align 8
  %_412 = alloca [48 x i8], align 8
  %angle_rad = alloca [8 x i8], align 8
  %angle_deg = alloca [8 x i8], align 8
  %_406 = alloca [8 x i8], align 8
  %iter1 = alloca [16 x i8], align 8
  %angles_deg = alloca [48 x i8], align 8
  %_400 = alloca [48 x i8], align 8
  %_397 = alloca [48 x i8], align 8
  %_394 = alloca [16 x i8], align 8
  %_393 = alloca [16 x i8], align 8
  %_392 = alloca [48 x i8], align 8
  %_391 = alloca [16 x i8], align 8
  %_390 = alloca [16 x i8], align 8
  %_389 = alloca [48 x i8], align 8
  %_388 = alloca [96 x i8], align 8
  %_385 = alloca [8 x i8], align 8
  %_383 = alloca [16 x i8], align 8
  %_380 = alloca [16 x i8], align 8
  %_379 = alloca [32 x i8], align 8
  %_374 = alloca [48 x i8], align 8
  %_371 = alloca [16 x i8], align 8
  %_370 = alloca [16 x i8], align 8
  %_369 = alloca [48 x i8], align 8
  %_368 = alloca [16 x i8], align 8
  %_367 = alloca [16 x i8], align 8
  %_366 = alloca [48 x i8], align 8
  %_365 = alloca [96 x i8], align 8
  %_362 = alloca [8 x i8], align 8
  %_360 = alloca [16 x i8], align 8
  %_357 = alloca [16 x i8], align 8
  %_356 = alloca [32 x i8], align 8
  %_351 = alloca [48 x i8], align 8
  %_348 = alloca [16 x i8], align 8
  %_347 = alloca [16 x i8], align 8
  %_346 = alloca [48 x i8], align 8
  %_345 = alloca [16 x i8], align 8
  %_344 = alloca [16 x i8], align 8
  %_343 = alloca [48 x i8], align 8
  %_342 = alloca [96 x i8], align 8
  %_339 = alloca [8 x i8], align 8
  %_337 = alloca [16 x i8], align 8
  %_334 = alloca [16 x i8], align 8
  %_333 = alloca [32 x i8], align 8
  %_328 = alloca [48 x i8], align 8
  %_325 = alloca [16 x i8], align 8
  %_324 = alloca [16 x i8], align 8
  %_323 = alloca [48 x i8], align 8
  %_322 = alloca [16 x i8], align 8
  %_321 = alloca [16 x i8], align 8
  %_320 = alloca [48 x i8], align 8
  %_319 = alloca [96 x i8], align 8
  %_315 = alloca [16 x i8], align 8
  %_312 = alloca [16 x i8], align 8
  %_311 = alloca [32 x i8], align 8
  %_306 = alloca [48 x i8], align 8
  %angle = alloca [8 x i8], align 8
  %_300 = alloca [16 x i8], align 8
  %iter = alloca [24 x i8], align 8
  %_295 = alloca [24 x i8], align 8
  %_294 = alloca [24 x i8], align 8
  %angle_names = alloca [96 x i8], align 8
  %angles_rad = alloca [48 x i8], align 8
  %_281 = alloca [48 x i8], align 8
  %_278 = alloca [48 x i8], align 8
  %_275 = alloca [16 x i8], align 8
  %_274 = alloca [16 x i8], align 8
  %_273 = alloca [48 x i8], align 8
  %_272 = alloca [48 x i8], align 8
  %_269 = alloca [8 x i8], align 8
  %_267 = alloca [16 x i8], align 8
  %_266 = alloca [16 x i8], align 8
  %_261 = alloca [48 x i8], align 8
  %_258 = alloca [16 x i8], align 8
  %_257 = alloca [16 x i8], align 8
  %_256 = alloca [48 x i8], align 8
  %_255 = alloca [48 x i8], align 8
  %_252 = alloca [8 x i8], align 8
  %_250 = alloca [16 x i8], align 8
  %_249 = alloca [16 x i8], align 8
  %_244 = alloca [48 x i8], align 8
  %_241 = alloca [16 x i8], align 8
  %_240 = alloca [16 x i8], align 8
  %_239 = alloca [48 x i8], align 8
  %_238 = alloca [48 x i8], align 8
  %_235 = alloca [8 x i8], align 8
  %_233 = alloca [16 x i8], align 8
  %_232 = alloca [16 x i8], align 8
  %_227 = alloca [48 x i8], align 8
  %_224 = alloca [16 x i8], align 8
  %_223 = alloca [16 x i8], align 8
  %_222 = alloca [48 x i8], align 8
  %_221 = alloca [48 x i8], align 8
  %_218 = alloca [8 x i8], align 8
  %_216 = alloca [16 x i8], align 8
  %_215 = alloca [16 x i8], align 8
  %_210 = alloca [48 x i8], align 8
  %_207 = alloca [16 x i8], align 8
  %_206 = alloca [16 x i8], align 8
  %_205 = alloca [48 x i8], align 8
  %_204 = alloca [48 x i8], align 8
  %_201 = alloca [8 x i8], align 8
  %_199 = alloca [16 x i8], align 8
  %_198 = alloca [16 x i8], align 8
  %_193 = alloca [48 x i8], align 8
  %_191 = alloca [8 x i8], align 8
  %_189 = alloca [16 x i8], align 8
  %_188 = alloca [16 x i8], align 8
  %_185 = alloca [48 x i8], align 8
  %_182 = alloca [16 x i8], align 8
  %_181 = alloca [16 x i8], align 8
  %_180 = alloca [48 x i8], align 8
  %_179 = alloca [48 x i8], align 8
  %_176 = alloca [8 x i8], align 8
  %_174 = alloca [16 x i8], align 8
  %_173 = alloca [16 x i8], align 8
  %_168 = alloca [48 x i8], align 8
  %_165 = alloca [16 x i8], align 8
  %_164 = alloca [16 x i8], align 8
  %_163 = alloca [48 x i8], align 8
  %_162 = alloca [48 x i8], align 8
  %_159 = alloca [8 x i8], align 8
  %_157 = alloca [16 x i8], align 8
  %_156 = alloca [16 x i8], align 8
  %_151 = alloca [48 x i8], align 8
  %_149 = alloca [8 x i8], align 8
  %_147 = alloca [16 x i8], align 8
  %_146 = alloca [16 x i8], align 8
  %_143 = alloca [48 x i8], align 8
  %_140 = alloca [16 x i8], align 8
  %_138 = alloca [16 x i8], align 8
  %_136 = alloca [16 x i8], align 8
  %_135 = alloca [48 x i8], align 8
  %_132 = alloca [48 x i8], align 8
  %c = alloca [8 x i8], align 8
  %b = alloca [8 x i8], align 8
  %a = alloca [8 x i8], align 8
  %_126 = alloca [48 x i8], align 8
  %_123 = alloca [48 x i8], align 8
  %_120 = alloca [16 x i8], align 8
  %_119 = alloca [16 x i8], align 8
  %_118 = alloca [48 x i8], align 8
  %_117 = alloca [48 x i8], align 8
  %_113 = alloca [16 x i8], align 8
  %_112 = alloca [16 x i8], align 8
  %_107 = alloca [48 x i8], align 8
  %_104 = alloca [16 x i8], align 8
  %_103 = alloca [16 x i8], align 8
  %_102 = alloca [48 x i8], align 8
  %_101 = alloca [48 x i8], align 8
  %_97 = alloca [16 x i8], align 8
  %_96 = alloca [16 x i8], align 8
  %_91 = alloca [48 x i8], align 8
  %_88 = alloca [16 x i8], align 8
  %_87 = alloca [16 x i8], align 8
  %_86 = alloca [48 x i8], align 8
  %_85 = alloca [48 x i8], align 8
  %_81 = alloca [16 x i8], align 8
  %_80 = alloca [16 x i8], align 8
  %_75 = alloca [48 x i8], align 8
  %_72 = alloca [16 x i8], align 8
  %_71 = alloca [16 x i8], align 8
  %_70 = alloca [48 x i8], align 8
  %_69 = alloca [48 x i8], align 8
  %_65 = alloca [16 x i8], align 8
  %_64 = alloca [16 x i8], align 8
  %_59 = alloca [48 x i8], align 8
  %_56 = alloca [16 x i8], align 8
  %_55 = alloca [16 x i8], align 8
  %_54 = alloca [48 x i8], align 8
  %_53 = alloca [48 x i8], align 8
  %_49 = alloca [16 x i8], align 8
  %_48 = alloca [16 x i8], align 8
  %_43 = alloca [48 x i8], align 8
  %_40 = alloca [16 x i8], align 8
  %_39 = alloca [16 x i8], align 8
  %_38 = alloca [48 x i8], align 8
  %_37 = alloca [48 x i8], align 8
  %_33 = alloca [16 x i8], align 8
  %_32 = alloca [16 x i8], align 8
  %_27 = alloca [48 x i8], align 8
  %_24 = alloca [16 x i8], align 8
  %_23 = alloca [16 x i8], align 8
  %_22 = alloca [48 x i8], align 8
  %_21 = alloca [48 x i8], align 8
  %_17 = alloca [16 x i8], align 8
  %_16 = alloca [16 x i8], align 8
  %_11 = alloca [48 x i8], align 8
  %_8 = alloca [48 x i8], align 8
  %_5 = alloca [48 x i8], align 8
  %_2 = alloca [48 x i8], align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_2, ptr align 8 @alloc_ac996aed3ca0fd91b77fc28dd06cf980)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_2)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_5, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_5)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_8, ptr align 8 @alloc_0e180ec3c8f453ded99202ba432176d8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_17, ptr align 8 @alloc_8b225e0169c69df7305aca2f83716515)
  %0 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_16, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %0, ptr align 8 %_17, i64 16, i1 false)
  %1 = getelementptr inbounds i8, ptr %_23, i64 2
  store i16 10, ptr %1, align 2
  store i16 0, ptr %_23, align 8
  store i16 2, ptr %_24, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_22, i64 0, i32 -268435424, ptr align 8 %_23, ptr align 8 %_24)
  %2 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_21, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %2, ptr align 8 %_22, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_11, ptr align 8 @alloc_3bf5c39e8f0101c26de409f7eff5cfc2, i64 2, ptr align 8 %_16, i64 1, ptr align 8 %_21, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_11)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_33, ptr align 8 @alloc_97141a5f2da28e32746b2e87e7a7f02b)
  %3 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_32, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %3, ptr align 8 %_33, i64 16, i1 false)
  %4 = getelementptr inbounds i8, ptr %_39, i64 2
  store i16 10, ptr %4, align 2
  store i16 0, ptr %_39, align 8
  store i16 2, ptr %_40, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_38, i64 0, i32 -268435424, ptr align 8 %_39, ptr align 8 %_40)
  %5 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_37, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %5, ptr align 8 %_38, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_27, ptr align 8 @alloc_8833b243aff017acc5586bbceed83924, i64 2, ptr align 8 %_32, i64 1, ptr align 8 %_37, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_27)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_49, ptr align 8 @alloc_097e2602a9650474e7c1161152ec752d)
  %6 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_48, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %6, ptr align 8 %_49, i64 16, i1 false)
  %7 = getelementptr inbounds i8, ptr %_55, i64 2
  store i16 10, ptr %7, align 2
  store i16 0, ptr %_55, align 8
  store i16 2, ptr %_56, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_54, i64 0, i32 -268435424, ptr align 8 %_55, ptr align 8 %_56)
  %8 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_53, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %8, ptr align 8 %_54, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_43, ptr align 8 @alloc_34ab63eff4a60651a28bb4e8ffca4db2, i64 2, ptr align 8 %_48, i64 1, ptr align 8 %_53, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_43)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_65, ptr align 8 @alloc_a6f9eb080cec2b391d6e8a8179f5772a)
  %9 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_64, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %9, ptr align 8 %_65, i64 16, i1 false)
  %10 = getelementptr inbounds i8, ptr %_71, i64 2
  store i16 10, ptr %10, align 2
  store i16 0, ptr %_71, align 8
  store i16 2, ptr %_72, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_70, i64 0, i32 -268435424, ptr align 8 %_71, ptr align 8 %_72)
  %11 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_69, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %11, ptr align 8 %_70, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_59, ptr align 8 @alloc_8cf9ed33f9adcff0cf5c95113952c4ed, i64 2, ptr align 8 %_64, i64 1, ptr align 8 %_69, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_59)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_81, ptr align 8 @alloc_4de7c80f515ccd653a5d590e6d349f5f)
  %12 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_80, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %12, ptr align 8 %_81, i64 16, i1 false)
  %13 = getelementptr inbounds i8, ptr %_87, i64 2
  store i16 10, ptr %13, align 2
  store i16 0, ptr %_87, align 8
  store i16 2, ptr %_88, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_86, i64 0, i32 -268435424, ptr align 8 %_87, ptr align 8 %_88)
  %14 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_85, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %14, ptr align 8 %_86, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_75, ptr align 8 @alloc_35b30c57354b60f1fee559c306f28469, i64 2, ptr align 8 %_80, i64 1, ptr align 8 %_85, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_75)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_97, ptr align 8 @alloc_c733b3b39552f8613765209c1ff75055)
  %15 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_96, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %15, ptr align 8 %_97, i64 16, i1 false)
  %16 = getelementptr inbounds i8, ptr %_103, i64 2
  store i16 10, ptr %16, align 2
  store i16 0, ptr %_103, align 8
  store i16 2, ptr %_104, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_102, i64 0, i32 -268435424, ptr align 8 %_103, ptr align 8 %_104)
  %17 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_101, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %17, ptr align 8 %_102, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_91, ptr align 8 @alloc_935d3d49f31af6a25c4bb01bbd2f6523, i64 2, ptr align 8 %_96, i64 1, ptr align 8 %_101, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_91)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_113, ptr align 8 @alloc_4ea4b122a69e0f5b2ff2c903b0d94e78)
  %18 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_112, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %18, ptr align 8 %_113, i64 16, i1 false)
  %19 = getelementptr inbounds i8, ptr %_119, i64 2
  store i16 10, ptr %19, align 2
  store i16 0, ptr %_119, align 8
  store i16 2, ptr %_120, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_118, i64 0, i32 -268435424, ptr align 8 %_119, ptr align 8 %_120)
  %20 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_117, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %20, ptr align 8 %_118, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_107, ptr align 8 @alloc_a3da551cf8a9cb26018bb4733c951b00, i64 2, ptr align 8 %_112, i64 1, ptr align 8 %_117, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_107)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_123, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_123)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_126, ptr align 8 @alloc_11c05ba5bb0a3ed3ac8acb68e0cc77ac)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_126)
  store double 1.600000e+01, ptr %a, align 8
  store double 4.000000e+00, ptr %b, align 8
  store double -7.500000e+00, ptr %c, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_136, ptr align 8 %a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_138, ptr align 8 %b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_140, ptr align 8 %c)
  %21 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_135, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %21, ptr align 8 %_136, i64 16, i1 false)
  %22 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_135, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %22, ptr align 8 %_138, i64 16, i1 false)
  %23 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_135, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %23, ptr align 8 %_140, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_132, ptr align 8 @alloc_6d35e60c1ec09e00a20cd9f2ef9e4752, ptr align 8 %_135)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_132)
  %24 = load double, ptr %c, align 8
; call core::f64::<impl f64>::abs
  %25 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3abs17hd697952636949800E"(double %24)
  store double %25, ptr %_149, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_147, ptr align 8 %_149)
  %26 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_146, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %26, ptr align 8 %_147, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_143, ptr align 8 @alloc_9796d792556dda9195bb6a75c3a5deb1, ptr align 8 %_146)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_143)
  %27 = load double, ptr %a, align 8
; call std::f64::<impl f64>::sqrt
  %28 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %27)
  store double %28, ptr %_159, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_157, ptr align 8 %_159)
  %29 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_156, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %29, ptr align 8 %_157, i64 16, i1 false)
  %30 = getelementptr inbounds i8, ptr %_164, i64 2
  store i16 6, ptr %30, align 2
  store i16 0, ptr %_164, align 8
  store i16 2, ptr %_165, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_163, i64 0, i32 -268435424, ptr align 8 %_164, ptr align 8 %_165)
  %31 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_162, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %31, ptr align 8 %_163, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_151, ptr align 8 @alloc_8087de6b25049f0ed2258b592c2da366, i64 2, ptr align 8 %_156, i64 1, ptr align 8 %_162, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_151)
  %32 = load double, ptr %a, align 8
; call std::f64::<impl f64>::cbrt
  %33 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4cbrt17h4b8333a563c1c3e8E"(double %32)
  store double %33, ptr %_176, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_174, ptr align 8 %_176)
  %34 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_173, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %34, ptr align 8 %_174, i64 16, i1 false)
  %35 = getelementptr inbounds i8, ptr %_181, i64 2
  store i16 6, ptr %35, align 2
  store i16 0, ptr %_181, align 8
  store i16 2, ptr %_182, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_180, i64 0, i32 -268435424, ptr align 8 %_181, ptr align 8 %_182)
  %36 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_179, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %36, ptr align 8 %_180, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_168, ptr align 8 @alloc_11f874a23d9410d4fdda15f92c239678, i64 2, ptr align 8 %_173, i64 1, ptr align 8 %_179, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_168)
  %37 = load double, ptr %b, align 8
; call std::f64::<impl f64>::powi
  %38 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %37, i32 3)
  store double %38, ptr %_191, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_189, ptr align 8 %_191)
  %39 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_188, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %39, ptr align 8 %_189, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_185, ptr align 8 @alloc_f90103e782e5b21d70a4934ba3e0646d, ptr align 8 %_188)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_185)
  %40 = load double, ptr %a, align 8
; call std::f64::<impl f64>::powf
  %41 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powf17hb46404ddf630b33dE"(double %40, double 5.000000e-01)
  store double %41, ptr %_201, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_199, ptr align 8 %_201)
  %42 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_198, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %42, ptr align 8 %_199, i64 16, i1 false)
  %43 = getelementptr inbounds i8, ptr %_206, i64 2
  store i16 6, ptr %43, align 2
  store i16 0, ptr %_206, align 8
  store i16 2, ptr %_207, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_205, i64 0, i32 -268435424, ptr align 8 %_206, ptr align 8 %_207)
  %44 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_204, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %44, ptr align 8 %_205, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_193, ptr align 8 @alloc_cd83647424918092aa31a65a610a7224, i64 2, ptr align 8 %_198, i64 1, ptr align 8 %_204, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_193)
; call std::f64::<impl f64>::exp
  %45 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3exp17h5781cb802bc92ae1E"(double 1.000000e+00)
  store double %45, ptr %_218, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_216, ptr align 8 %_218)
  %46 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_215, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %46, ptr align 8 %_216, i64 16, i1 false)
  %47 = getelementptr inbounds i8, ptr %_223, i64 2
  store i16 6, ptr %47, align 2
  store i16 0, ptr %_223, align 8
  store i16 2, ptr %_224, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_222, i64 0, i32 -268435424, ptr align 8 %_223, ptr align 8 %_224)
  %48 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_221, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %48, ptr align 8 %_222, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_210, ptr align 8 @alloc_9a32ae22cf6c7ce4e05c50331c4d5f44, i64 2, ptr align 8 %_215, i64 1, ptr align 8 %_221, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_210)
  %49 = load double, ptr %a, align 8
; call std::f64::<impl f64>::ln
  %50 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$2ln17hc764f3993a195a61E"(double %49)
  store double %50, ptr %_235, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_233, ptr align 8 %_235)
  %51 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_232, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %51, ptr align 8 %_233, i64 16, i1 false)
  %52 = getelementptr inbounds i8, ptr %_240, i64 2
  store i16 6, ptr %52, align 2
  store i16 0, ptr %_240, align 8
  store i16 2, ptr %_241, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_239, i64 0, i32 -268435424, ptr align 8 %_240, ptr align 8 %_241)
  %53 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_238, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %53, ptr align 8 %_239, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_227, ptr align 8 @alloc_47f31e6a1819b2f566b7bd4a202e21e7, i64 2, ptr align 8 %_232, i64 1, ptr align 8 %_238, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_227)
  %54 = load double, ptr %a, align 8
; call std::f64::<impl f64>::log2
  %55 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4log217h49c2bfb6926be7b8E"(double %54)
  store double %55, ptr %_252, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_250, ptr align 8 %_252)
  %56 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_249, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %56, ptr align 8 %_250, i64 16, i1 false)
  %57 = getelementptr inbounds i8, ptr %_257, i64 2
  store i16 6, ptr %57, align 2
  store i16 0, ptr %_257, align 8
  store i16 2, ptr %_258, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_256, i64 0, i32 -268435424, ptr align 8 %_257, ptr align 8 %_258)
  %58 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_255, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %58, ptr align 8 %_256, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_244, ptr align 8 @alloc_b38121c3a762b83276d69425dbed648b, i64 2, ptr align 8 %_249, i64 1, ptr align 8 %_255, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_244)
  %59 = load double, ptr %a, align 8
; call std::f64::<impl f64>::log10
  %60 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5log1017hc58cfafa35dc96e5E"(double %59)
  store double %60, ptr %_269, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_267, ptr align 8 %_269)
  %61 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_266, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %61, ptr align 8 %_267, i64 16, i1 false)
  %62 = getelementptr inbounds i8, ptr %_274, i64 2
  store i16 6, ptr %62, align 2
  store i16 0, ptr %_274, align 8
  store i16 2, ptr %_275, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_273, i64 0, i32 -268435424, ptr align 8 %_274, ptr align 8 %_275)
  %63 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_272, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %63, ptr align 8 %_273, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_261, ptr align 8 @alloc_f93500571669ba6a5a593852bb90258f, i64 2, ptr align 8 %_266, i64 1, ptr align 8 %_272, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_261)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_278, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_278)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_281, ptr align 8 @alloc_1ba4d175d10ff4662405533e5e0c44d7)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_281)
  %64 = getelementptr inbounds nuw double, ptr %angles_rad, i64 0
  store double 0.000000e+00, ptr %64, align 8
  %65 = getelementptr inbounds nuw double, ptr %angles_rad, i64 1
  store double 0x3FE0C152382D7365, ptr %65, align 8
  %66 = getelementptr inbounds nuw double, ptr %angles_rad, i64 2
  store double 0x3FE921FB54442D18, ptr %66, align 8
  %67 = getelementptr inbounds nuw double, ptr %angles_rad, i64 3
  store double 0x3FF0C152382D7365, ptr %67, align 8
  %68 = getelementptr inbounds nuw double, ptr %angles_rad, i64 4
  store double 0x3FF921FB54442D18, ptr %68, align 8
  %69 = getelementptr inbounds nuw double, ptr %angles_rad, i64 5
  store double 0x400921FB54442D18, ptr %69, align 8
  %70 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 0
  store ptr @alloc_dda1ee2b88b89b9cdac753eef7988035, ptr %70, align 8
  %71 = getelementptr inbounds i8, ptr %70, i64 8
  store i64 1, ptr %71, align 8
  %72 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 1
  store ptr @alloc_67c5654397ba57104684a629f406f10e, ptr %72, align 8
  %73 = getelementptr inbounds i8, ptr %72, i64 8
  store i64 4, ptr %73, align 8
  %74 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 2
  store ptr @alloc_4db0be8db4592be42bc4c77d0ffe4304, ptr %74, align 8
  %75 = getelementptr inbounds i8, ptr %74, i64 8
  store i64 4, ptr %75, align 8
  %76 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 3
  store ptr @alloc_6d9f488f4fbdaf418990477dc6cf8a35, ptr %76, align 8
  %77 = getelementptr inbounds i8, ptr %76, i64 8
  store i64 4, ptr %77, align 8
  %78 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 4
  store ptr @alloc_ddc6e154c703a61a437da6f22f30605c, ptr %78, align 8
  %79 = getelementptr inbounds i8, ptr %78, i64 8
  store i64 4, ptr %79, align 8
  %80 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 5
  store ptr @alloc_a52aa3f05a7e86d9a79d613359190858, ptr %80, align 8
  %81 = getelementptr inbounds i8, ptr %80, i64 8
  store i64 2, ptr %81, align 8
; call core::slice::<impl [T]>::iter
  %82 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %angles_rad, i64 6)
  %_296.0 = extractvalue { ptr, ptr } %82, 0
  %_296.1 = extractvalue { ptr, ptr } %82, 1
; call core::iter::traits::iterator::Iterator::enumerate
  call void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h20a7b10fd82a0585E(ptr sret([24 x i8]) align 8 %_295, ptr %_296.0, ptr %_296.1)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hd32c3260bdaa8df8E"(ptr sret([24 x i8]) align 8 %_294, ptr align 8 %_295)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %_294, i64 24, i1 false)
  br label %bb108

bb108:                                            ; preds = %bb139, %start
; call <core::iter::adapters::enumerate::Enumerate<I> as core::iter::traits::iterator::Iterator>::next
  %83 = call { i64, ptr } @"_ZN110_$LT$core..iter..adapters..enumerate..Enumerate$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha1fff5d202be5f55E"(ptr align 8 %iter)
  %84 = extractvalue { i64, ptr } %83, 0
  %85 = extractvalue { i64, ptr } %83, 1
  store i64 %84, ptr %_300, align 8
  %86 = getelementptr inbounds i8, ptr %_300, i64 8
  store ptr %85, ptr %86, align 8
  %87 = load i64, ptr %_300, align 8
  %88 = getelementptr inbounds i8, ptr %_300, i64 8
  %89 = load ptr, ptr %88, align 8
  %90 = ptrtoint ptr %89 to i64
  %91 = icmp eq i64 %90, 0
  %_302 = select i1 %91, i64 0, i64 1
  %92 = trunc nuw i64 %_302 to i1
  br i1 %92, label %bb111, label %bb112

bb111:                                            ; preds = %bb108
  %i19 = load i64, ptr %_300, align 8
  %93 = getelementptr inbounds i8, ptr %_300, i64 8
  %_1480 = load ptr, ptr %93, align 8
  %94 = load double, ptr %_1480, align 8
  store double %94, ptr %angle, align 8
  %_314 = icmp ult i64 %i19, 6
  br i1 %_314, label %bb113, label %panic20

bb112:                                            ; preds = %bb108
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_397, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_397)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_400, ptr align 8 @alloc_a55670ff7bab2045c7ceba43f5ae59b4)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_400)
  %95 = getelementptr inbounds nuw double, ptr %angles_deg, i64 0
  store double 0.000000e+00, ptr %95, align 8
  %96 = getelementptr inbounds nuw double, ptr %angles_deg, i64 1
  store double 3.000000e+01, ptr %96, align 8
  %97 = getelementptr inbounds nuw double, ptr %angles_deg, i64 2
  store double 4.500000e+01, ptr %97, align 8
  %98 = getelementptr inbounds nuw double, ptr %angles_deg, i64 3
  store double 6.000000e+01, ptr %98, align 8
  %99 = getelementptr inbounds nuw double, ptr %angles_deg, i64 4
  store double 9.000000e+01, ptr %99, align 8
  %100 = getelementptr inbounds nuw double, ptr %angles_deg, i64 5
  store double 1.800000e+02, ptr %100, align 8
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %101 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h207ee6bd1130210fE"(ptr align 8 %angles_deg)
  %_403.0 = extractvalue { ptr, ptr } %101, 0
  %_403.1 = extractvalue { ptr, ptr } %101, 1
  store ptr %_403.0, ptr %iter1, align 8
  %102 = getelementptr inbounds i8, ptr %iter1, i64 8
  store ptr %_403.1, ptr %102, align 8
  br label %bb152

bb152:                                            ; preds = %bb181, %bb189, %bb112
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %103 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter1)
  store ptr %103, ptr %_406, align 8
  %104 = load ptr, ptr %_406, align 8
  %105 = ptrtoint ptr %104 to i64
  %106 = icmp eq i64 %105, 0
  %_408 = select i1 %106, i64 0, i64 1
  %107 = trunc nuw i64 %_408 to i1
  br i1 %107, label %bb154, label %bb155

bb154:                                            ; preds = %bb152
  %_1481 = load ptr, ptr %_406, align 8
  %108 = load double, ptr %_1481, align 8
  store double %108, ptr %angle_deg, align 8
  %109 = load double, ptr %angle_deg, align 8
; call core::f64::<impl f64>::to_radians
  %110 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_radians17h3ff76c3f42527209E"(double %109)
  store double %110, ptr %angle_rad, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_418, ptr align 8 %angle_deg)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_420, ptr align 8 %angle_rad)
  %111 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_417, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %111, ptr align 8 %_418, i64 16, i1 false)
  %112 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_417, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %112, ptr align 8 %_420, i64 16, i1 false)
  store i16 2, ptr %_426, align 8
  store i16 2, ptr %_427, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_425, i64 0, i32 -536870880, ptr align 8 %_426, ptr align 8 %_427)
  %113 = getelementptr inbounds i8, ptr %_429, i64 2
  store i16 6, ptr %113, align 2
  store i16 0, ptr %_429, align 8
  store i16 2, ptr %_430, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_428, i64 1, i32 -268435424, ptr align 8 %_429, ptr align 8 %_430)
  %114 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_424, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %114, ptr align 8 %_425, i64 48, i1 false)
  %115 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_424, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %115, ptr align 8 %_428, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_412, ptr align 8 @alloc_fc49891db259ef7e8fb2707b24e5c3be, i64 3, ptr align 8 %_417, i64 2, ptr align 8 %_424, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_412)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_439, ptr align 8 %angle_deg)
  %116 = load double, ptr %angle_rad, align 8
; call std::f64::<impl f64>::sin
  %117 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3sin17h2d8fa3ef473081aaE"(double %116)
  store double %117, ptr %_443, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_441, ptr align 8 %_443)
  %118 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_438, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %118, ptr align 8 %_439, i64 16, i1 false)
  %119 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_438, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %119, ptr align 8 %_441, i64 16, i1 false)
  store i16 2, ptr %_448, align 8
  store i16 2, ptr %_449, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_447, i64 0, i32 -536870880, ptr align 8 %_448, ptr align 8 %_449)
  %120 = getelementptr inbounds i8, ptr %_451, i64 2
  store i16 6, ptr %120, align 2
  store i16 0, ptr %_451, align 8
  store i16 2, ptr %_452, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_450, i64 1, i32 -268435424, ptr align 8 %_451, ptr align 8 %_452)
  %121 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_446, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %121, ptr align 8 %_447, i64 48, i1 false)
  %122 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_446, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %122, ptr align 8 %_450, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_433, ptr align 8 @alloc_29357bdf8eb5317387121192992cdaf4, i64 3, ptr align 8 %_438, i64 2, ptr align 8 %_446, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_433)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_461, ptr align 8 %angle_deg)
  %123 = load double, ptr %angle_rad, align 8
; call std::f64::<impl f64>::cos
  %124 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3cos17h441f1dbd8b285e4cE"(double %123)
  store double %124, ptr %_465, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_463, ptr align 8 %_465)
  %125 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_460, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %125, ptr align 8 %_461, i64 16, i1 false)
  %126 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_460, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %126, ptr align 8 %_463, i64 16, i1 false)
  store i16 2, ptr %_470, align 8
  store i16 2, ptr %_471, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_469, i64 0, i32 -536870880, ptr align 8 %_470, ptr align 8 %_471)
  %127 = getelementptr inbounds i8, ptr %_473, i64 2
  store i16 6, ptr %127, align 2
  store i16 0, ptr %_473, align 8
  store i16 2, ptr %_474, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_472, i64 1, i32 -268435424, ptr align 8 %_473, ptr align 8 %_474)
  %128 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_468, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %128, ptr align 8 %_469, i64 48, i1 false)
  %129 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_468, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %129, ptr align 8 %_472, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_455, ptr align 8 @alloc_cde3d3bb48b3eb39c6dde7d111619b33, i64 3, ptr align 8 %_460, i64 2, ptr align 8 %_468, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_455)
  %130 = load double, ptr %angle_deg, align 8
  %_476 = fcmp une double %130, 9.000000e+01
  br i1 %_476, label %bb180, label %bb189

bb155:                                            ; preds = %bb152
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_508, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_508)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_511, ptr align 8 @alloc_36e985c447b00f4fb0f971b7a5fdd3d1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_511)
  %131 = getelementptr inbounds nuw double, ptr %values, i64 0
  store double 0.000000e+00, ptr %131, align 8
  %132 = getelementptr inbounds nuw double, ptr %values, i64 1
  store double 5.000000e-01, ptr %132, align 8
  %133 = getelementptr inbounds nuw double, ptr %values, i64 2
  store double 7.070000e-01, ptr %133, align 8
  %134 = getelementptr inbounds nuw double, ptr %values, i64 3
  store double 8.660000e-01, ptr %134, align 8
  %135 = getelementptr inbounds nuw double, ptr %values, i64 4
  store double 1.000000e+00, ptr %135, align 8
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %136 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha3e16c7299db7d5bE"(ptr align 8 %values)
  %_514.0 = extractvalue { ptr, ptr } %136, 0
  %_514.1 = extractvalue { ptr, ptr } %136, 1
  store ptr %_514.0, ptr %iter2, align 8
  %137 = getelementptr inbounds i8, ptr %iter2, i64 8
  store ptr %_514.1, ptr %137, align 8
  br label %bb197

bb197:                                            ; preds = %bb202, %bb201, %bb199, %bb155
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %138 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter2)
  store ptr %138, ptr %_517, align 8
  %139 = load ptr, ptr %_517, align 8
  %140 = ptrtoint ptr %139 to i64
  %141 = icmp eq i64 %140, 0
  %_519 = select i1 %141, i64 0, i64 1
  %142 = trunc nuw i64 %_519 to i1
  br i1 %142, label %bb199, label %bb200

bb199:                                            ; preds = %bb197
  %_1482 = load ptr, ptr %_517, align 8
  %143 = load double, ptr %_1482, align 8
  store double %143, ptr %val, align 8
  %144 = load double, ptr %val, align 8
  %_521 = fcmp oge double %144, -1.000000e+00
  br i1 %_521, label %bb201, label %bb197

bb200:                                            ; preds = %bb197
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_618, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_618)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_621, ptr align 8 @alloc_f828f7367503d5788d5676504c301368)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_621)
  %145 = getelementptr inbounds nuw double, ptr %hyp_values, i64 0
  store double 0.000000e+00, ptr %145, align 8
  %146 = getelementptr inbounds nuw double, ptr %hyp_values, i64 1
  store double 5.000000e-01, ptr %146, align 8
  %147 = getelementptr inbounds nuw double, ptr %hyp_values, i64 2
  store double 1.000000e+00, ptr %147, align 8
  %148 = getelementptr inbounds nuw double, ptr %hyp_values, i64 3
  store double 1.500000e+00, ptr %148, align 8
  %149 = getelementptr inbounds nuw double, ptr %hyp_values, i64 4
  store double 2.000000e+00, ptr %149, align 8
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %150 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha3e16c7299db7d5bE"(ptr align 8 %hyp_values)
  %_624.0 = extractvalue { ptr, ptr } %150, 0
  %_624.1 = extractvalue { ptr, ptr } %150, 1
  store ptr %_624.0, ptr %iter3, align 8
  %151 = getelementptr inbounds i8, ptr %iter3, i64 8
  store ptr %_624.1, ptr %151, align 8
  br label %bb246

bb246:                                            ; preds = %bb248, %bb200
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %152 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter3)
  store ptr %152, ptr %_627, align 8
  %153 = load ptr, ptr %_627, align 8
  %154 = ptrtoint ptr %153 to i64
  %155 = icmp eq i64 %154, 0
  %_629 = select i1 %155, i64 0, i64 1
  %156 = trunc nuw i64 %_629 to i1
  br i1 %156, label %bb248, label %bb249

bb248:                                            ; preds = %bb246
  %_1483 = load ptr, ptr %_627, align 8
  %157 = load double, ptr %_1483, align 8
  store double %157, ptr %val4, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_636, ptr align 8 %val4)
  %158 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_635, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %158, ptr align 8 %_636, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_632, ptr align 8 @alloc_a37200dd26e9374fe4498e5684023e1f, ptr align 8 %_635)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_632)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_645, ptr align 8 %val4)
  %159 = load double, ptr %val4, align 8
; call std::f64::<impl f64>::sinh
  %160 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sinh17h7a30010c2d6bce05E"(double %159)
  store double %160, ptr %_649, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_647, ptr align 8 %_649)
  %161 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_644, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %161, ptr align 8 %_645, i64 16, i1 false)
  %162 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_644, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %162, ptr align 8 %_647, i64 16, i1 false)
  store i16 2, ptr %_654, align 8
  store i16 2, ptr %_655, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_653, i64 0, i32 -536870880, ptr align 8 %_654, ptr align 8 %_655)
  %163 = getelementptr inbounds i8, ptr %_657, i64 2
  store i16 6, ptr %163, align 2
  store i16 0, ptr %_657, align 8
  store i16 2, ptr %_658, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_656, i64 1, i32 -268435424, ptr align 8 %_657, ptr align 8 %_658)
  %164 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_652, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %164, ptr align 8 %_653, i64 48, i1 false)
  %165 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_652, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %165, ptr align 8 %_656, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_639, ptr align 8 @alloc_0b7e751c8c99a47859511e4cc929fb4c, i64 3, ptr align 8 %_644, i64 2, ptr align 8 %_652, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_639)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_667, ptr align 8 %val4)
  %166 = load double, ptr %val4, align 8
; call std::f64::<impl f64>::cosh
  %167 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4cosh17h910508e6d4a6eee3E"(double %166)
  store double %167, ptr %_671, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_669, ptr align 8 %_671)
  %168 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_666, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %168, ptr align 8 %_667, i64 16, i1 false)
  %169 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_666, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %169, ptr align 8 %_669, i64 16, i1 false)
  store i16 2, ptr %_676, align 8
  store i16 2, ptr %_677, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_675, i64 0, i32 -536870880, ptr align 8 %_676, ptr align 8 %_677)
  %170 = getelementptr inbounds i8, ptr %_679, i64 2
  store i16 6, ptr %170, align 2
  store i16 0, ptr %_679, align 8
  store i16 2, ptr %_680, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_678, i64 1, i32 -268435424, ptr align 8 %_679, ptr align 8 %_680)
  %171 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_674, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %171, ptr align 8 %_675, i64 48, i1 false)
  %172 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_674, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %172, ptr align 8 %_678, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_661, ptr align 8 @alloc_77d2acbce3cc62e8304e72b64646c979, i64 3, ptr align 8 %_666, i64 2, ptr align 8 %_674, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_661)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_689, ptr align 8 %val4)
  %173 = load double, ptr %val4, align 8
; call std::f64::<impl f64>::tanh
  %174 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4tanh17h2c3fff3e90ecc31dE"(double %173)
  store double %174, ptr %_693, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_691, ptr align 8 %_693)
  %175 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_688, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %175, ptr align 8 %_689, i64 16, i1 false)
  %176 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_688, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %176, ptr align 8 %_691, i64 16, i1 false)
  store i16 2, ptr %_698, align 8
  store i16 2, ptr %_699, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_697, i64 0, i32 -536870880, ptr align 8 %_698, ptr align 8 %_699)
  %177 = getelementptr inbounds i8, ptr %_701, i64 2
  store i16 6, ptr %177, align 2
  store i16 0, ptr %_701, align 8
  store i16 2, ptr %_702, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_700, i64 1, i32 -268435424, ptr align 8 %_701, ptr align 8 %_702)
  %178 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_696, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %178, ptr align 8 %_697, i64 48, i1 false)
  %179 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_696, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %179, ptr align 8 %_700, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_683, ptr align 8 @alloc_33accba464183ee73ddf7cecb62c67c3, i64 3, ptr align 8 %_688, i64 2, ptr align 8 %_696, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_683)
  br label %bb246

bb249:                                            ; preds = %bb246
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_705, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_705)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_708, ptr align 8 @alloc_472c1e306a952df758316b3bedb5463a)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_708)
  %180 = getelementptr inbounds nuw double, ptr %decimal_values, i64 0
  store double 3.200000e+00, ptr %180, align 8
  %181 = getelementptr inbounds nuw double, ptr %decimal_values, i64 1
  store double 3.700000e+00, ptr %181, align 8
  %182 = getelementptr inbounds nuw double, ptr %decimal_values, i64 2
  store double -2.300000e+00, ptr %182, align 8
  %183 = getelementptr inbounds nuw double, ptr %decimal_values, i64 3
  store double -2.800000e+00, ptr %183, align 8
  %184 = getelementptr inbounds nuw double, ptr %decimal_values, i64 4
  store double 5.500000e+00, ptr %184, align 8
  %185 = getelementptr inbounds nuw double, ptr %decimal_values, i64 5
  store double -5.500000e+00, ptr %185, align 8
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %186 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h207ee6bd1130210fE"(ptr align 8 %decimal_values)
  %_711.0 = extractvalue { ptr, ptr } %186, 0
  %_711.1 = extractvalue { ptr, ptr } %186, 1
  store ptr %_711.0, ptr %iter5, align 8
  %187 = getelementptr inbounds i8, ptr %iter5, i64 8
  store ptr %_711.1, ptr %187, align 8
  br label %bb281

bb281:                                            ; preds = %bb283, %bb249
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %188 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter5)
  store ptr %188, ptr %_714, align 8
  %189 = load ptr, ptr %_714, align 8
  %190 = ptrtoint ptr %189 to i64
  %191 = icmp eq i64 %190, 0
  %_716 = select i1 %191, i64 0, i64 1
  %192 = trunc nuw i64 %_716 to i1
  br i1 %192, label %bb283, label %bb284

bb283:                                            ; preds = %bb281
  %_1484 = load ptr, ptr %_714, align 8
  %193 = load double, ptr %_1484, align 8
  store double %193, ptr %val6, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_723, ptr align 8 %val6)
  %194 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_722, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %194, ptr align 8 %_723, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_719, ptr align 8 @alloc_a37200dd26e9374fe4498e5684023e1f, ptr align 8 %_722)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_719)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_730, ptr align 8 %val6)
  %195 = load double, ptr %val6, align 8
; call std::f64::<impl f64>::floor
  %196 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5floor17h2012b6cab1f3b1c1E"(double %195)
  store double %196, ptr %_734, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_732, ptr align 8 %_734)
  %197 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_729, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %197, ptr align 8 %_730, i64 16, i1 false)
  %198 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_729, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %198, ptr align 8 %_732, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_726, ptr align 8 @alloc_8d95ed21c2e5a24bf1bc2ed1aca38be1, ptr align 8 %_729)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_726)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_740, ptr align 8 %val6)
  %199 = load double, ptr %val6, align 8
; call std::f64::<impl f64>::ceil
  %200 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4ceil17h4fdb41ce649351e0E"(double %199)
  store double %200, ptr %_744, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_742, ptr align 8 %_744)
  %201 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_739, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %201, ptr align 8 %_740, i64 16, i1 false)
  %202 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_739, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %202, ptr align 8 %_742, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_736, ptr align 8 @alloc_a7df6d09c48d93e9a2cf66f54b3f145c, ptr align 8 %_739)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_736)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_750, ptr align 8 %val6)
  %203 = load double, ptr %val6, align 8
; call std::f64::<impl f64>::round
  %204 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5round17ha4a65347234b54f0E"(double %203)
  store double %204, ptr %_754, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_752, ptr align 8 %_754)
  %205 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_749, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %205, ptr align 8 %_750, i64 16, i1 false)
  %206 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_749, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %206, ptr align 8 %_752, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_746, ptr align 8 @alloc_89f8d942b3edf2b06ba8245605994135, ptr align 8 %_749)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_746)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_760, ptr align 8 %val6)
  %207 = load double, ptr %val6, align 8
; call std::f64::<impl f64>::trunc
  %208 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5trunc17h25b6858ec28c1f15E"(double %207)
  store double %208, ptr %_764, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_762, ptr align 8 %_764)
  %209 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_759, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %209, ptr align 8 %_760, i64 16, i1 false)
  %210 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_759, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %210, ptr align 8 %_762, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_756, ptr align 8 @alloc_6c5a248a3665501dbe8b4a5d71297fd4, ptr align 8 %_759)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_756)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_772, ptr align 8 %val6)
  %211 = load double, ptr %val6, align 8
; call std::f64::<impl f64>::fract
  %212 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$5fract17h14bf28d3003e400dE"(double %211)
  store double %212, ptr %_776, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_774, ptr align 8 %_776)
  %213 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_771, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %213, ptr align 8 %_772, i64 16, i1 false)
  %214 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_771, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %214, ptr align 8 %_774, i64 16, i1 false)
  store i16 2, ptr %_781, align 8
  store i16 2, ptr %_782, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_780, i64 0, i32 -536870880, ptr align 8 %_781, ptr align 8 %_782)
  %215 = getelementptr inbounds i8, ptr %_784, i64 2
  store i16 6, ptr %215, align 2
  store i16 0, ptr %_784, align 8
  store i16 2, ptr %_785, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_783, i64 1, i32 -268435424, ptr align 8 %_784, ptr align 8 %_785)
  %216 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_779, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %216, ptr align 8 %_780, i64 48, i1 false)
  %217 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_779, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %217, ptr align 8 %_783, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_766, ptr align 8 @alloc_06db5496cb6611ff56806ad402991f06, i64 3, ptr align 8 %_771, i64 2, ptr align 8 %_779, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_766)
  br label %bb281

bb284:                                            ; preds = %bb281
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_788, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_788)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_791, ptr align 8 @alloc_7b27168522d9cb74d571dfc76c701777)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_791)
  %218 = getelementptr inbounds nuw double, ptr %data, i64 0
  store double 2.500000e+00, ptr %218, align 8
  %219 = getelementptr inbounds nuw double, ptr %data, i64 1
  store double 4.100000e+00, ptr %219, align 8
  %220 = getelementptr inbounds nuw double, ptr %data, i64 2
  store double 3.800000e+00, ptr %220, align 8
  %221 = getelementptr inbounds nuw double, ptr %data, i64 3
  store double 5.200000e+00, ptr %221, align 8
  %222 = getelementptr inbounds nuw double, ptr %data, i64 4
  store double 1.900000e+00, ptr %222, align 8
  %223 = getelementptr inbounds nuw double, ptr %data, i64 5
  store double 6.300000e+00, ptr %223, align 8
  %224 = getelementptr inbounds nuw double, ptr %data, i64 6
  store double 4.700000e+00, ptr %224, align 8
  %225 = getelementptr inbounds nuw double, ptr %data, i64 7
  store double 3.100000e+00, ptr %225, align 8
  %226 = getelementptr inbounds nuw double, ptr %data, i64 8
  store double 5.800000e+00, ptr %226, align 8
  %227 = getelementptr inbounds nuw double, ptr %data, i64 9
  store double 2.400000e+00, ptr %227, align 8
; call core::fmt::rt::Argument::new_debug
  call void @_ZN4core3fmt2rt8Argument9new_debug17hd787b376edae8d0cE(ptr sret([16 x i8]) align 8 %_799, ptr align 8 %data)
  %228 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_798, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %228, ptr align 8 %_799, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_795, ptr align 8 @alloc_488ed7bc1b42104f71c4fea09ee789ef, ptr align 8 %_798)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_795)
; call core::slice::<impl [T]>::iter
  %229 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %data, i64 10)
  %_802.0 = extractvalue { ptr, ptr } %229, 0
  %_802.1 = extractvalue { ptr, ptr } %229, 1
; call core::iter::traits::iterator::Iterator::sum
  %230 = call double @_ZN4core4iter6traits8iterator8Iterator3sum17he8b0c48ddc11cdb3E(ptr %_802.0, ptr %_802.1)
  store double %230, ptr %sum, align 8
  %231 = load double, ptr %sum, align 8
  %232 = fdiv double %231, 1.000000e+01
  store double %232, ptr %mean, align 8
; call core::slice::<impl [T]>::iter
  %233 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %data, i64 10)
  %_811.0 = extractvalue { ptr, ptr } %233, 0
  %_811.1 = extractvalue { ptr, ptr } %233, 1
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %234 = call double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h1e41dc294c87f2c0E"(ptr %_811.0, ptr %_811.1, double 0x7FF0000000000000)
  store double %234, ptr %min_val, align 8
; call core::slice::<impl [T]>::iter
  %235 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %data, i64 10)
  %_815.0 = extractvalue { ptr, ptr } %235, 0
  %_815.1 = extractvalue { ptr, ptr } %235, 1
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::fold
  %236 = call double @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4fold17h932739ff6f99e483E"(ptr %_815.0, ptr %_815.1, double 0xFFF0000000000000)
  store double %236, ptr %max_val, align 8
; call core::slice::<impl [T]>::iter
  %237 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %data, i64 10)
  %_821.0 = extractvalue { ptr, ptr } %237, 0
  %_821.1 = extractvalue { ptr, ptr } %237, 1
; call core::iter::traits::iterator::Iterator::map
  call void @_ZN4core4iter6traits8iterator8Iterator3map17h7f4196daf92efe8dE(ptr sret([24 x i8]) align 8 %_820, ptr %_821.0, ptr %_821.1, ptr align 8 %mean)
; call core::iter::traits::iterator::Iterator::sum
  %_819 = call double @_ZN4core4iter6traits8iterator8Iterator3sum17h8fe0e692d02505a0E(ptr align 8 %_820)
  %238 = fdiv double %_819, 1.000000e+01
  store double %238, ptr %variance, align 8
  %239 = load double, ptr %variance, align 8
; call std::f64::<impl f64>::sqrt
  %240 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %239)
  store double %240, ptr %std_dev, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_838, ptr align 8 %sum)
  %241 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_837, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %241, ptr align 8 %_838, i64 16, i1 false)
  %242 = getelementptr inbounds i8, ptr %_844, i64 2
  store i16 2, ptr %242, align 2
  store i16 0, ptr %_844, align 8
  store i16 2, ptr %_845, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_843, i64 0, i32 -268435424, ptr align 8 %_844, ptr align 8 %_845)
  %243 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_842, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %243, ptr align 8 %_843, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_832, ptr align 8 @alloc_fae7b78f3403b48b2870d7d78fcc4226, i64 2, ptr align 8 %_837, i64 1, ptr align 8 %_842, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_832)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_854, ptr align 8 %mean)
  %244 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_853, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %244, ptr align 8 %_854, i64 16, i1 false)
  %245 = getelementptr inbounds i8, ptr %_860, i64 2
  store i16 2, ptr %245, align 2
  store i16 0, ptr %_860, align 8
  store i16 2, ptr %_861, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_859, i64 0, i32 -268435424, ptr align 8 %_860, ptr align 8 %_861)
  %246 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_858, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %246, ptr align 8 %_859, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_848, ptr align 8 @alloc_26f684b680020ab97826303bd1a73f57, i64 2, ptr align 8 %_853, i64 1, ptr align 8 %_858, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_848)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_870, ptr align 8 %min_val)
  %247 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_869, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %247, ptr align 8 %_870, i64 16, i1 false)
  %248 = getelementptr inbounds i8, ptr %_876, i64 2
  store i16 2, ptr %248, align 2
  store i16 0, ptr %_876, align 8
  store i16 2, ptr %_877, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_875, i64 0, i32 -268435424, ptr align 8 %_876, ptr align 8 %_877)
  %249 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_874, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %249, ptr align 8 %_875, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_864, ptr align 8 @alloc_9049c6b777d9a90bcdeadb5e3fb17782, i64 2, ptr align 8 %_869, i64 1, ptr align 8 %_874, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_864)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_886, ptr align 8 %max_val)
  %250 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_885, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %250, ptr align 8 %_886, i64 16, i1 false)
  %251 = getelementptr inbounds i8, ptr %_892, i64 2
  store i16 2, ptr %251, align 2
  store i16 0, ptr %_892, align 8
  store i16 2, ptr %_893, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_891, i64 0, i32 -268435424, ptr align 8 %_892, ptr align 8 %_893)
  %252 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_890, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %252, ptr align 8 %_891, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_880, ptr align 8 @alloc_1b004682d34d6a19e00a486d7d5ded1a, i64 2, ptr align 8 %_885, i64 1, ptr align 8 %_890, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_880)
  %253 = load double, ptr %max_val, align 8
  %254 = load double, ptr %min_val, align 8
  %255 = fsub double %253, %254
  store double %255, ptr %_904, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_902, ptr align 8 %_904)
  %256 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_901, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %256, ptr align 8 %_902, i64 16, i1 false)
  %257 = getelementptr inbounds i8, ptr %_909, i64 2
  store i16 2, ptr %257, align 2
  store i16 0, ptr %_909, align 8
  store i16 2, ptr %_910, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_908, i64 0, i32 -268435424, ptr align 8 %_909, ptr align 8 %_910)
  %258 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_907, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %258, ptr align 8 %_908, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_896, ptr align 8 @alloc_7a517152eb3d8bddd47643ba929d2459, i64 2, ptr align 8 %_901, i64 1, ptr align 8 %_907, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_896)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_919, ptr align 8 %variance)
  %259 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_918, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %259, ptr align 8 %_919, i64 16, i1 false)
  %260 = getelementptr inbounds i8, ptr %_925, i64 2
  store i16 2, ptr %260, align 2
  store i16 0, ptr %_925, align 8
  store i16 2, ptr %_926, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_924, i64 0, i32 -268435424, ptr align 8 %_925, ptr align 8 %_926)
  %261 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_923, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %261, ptr align 8 %_924, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_913, ptr align 8 @alloc_55c5f5d5c57369529be9c4c6f581027d, i64 2, ptr align 8 %_918, i64 1, ptr align 8 %_923, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_913)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_935, ptr align 8 %std_dev)
  %262 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_934, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %262, ptr align 8 %_935, i64 16, i1 false)
  %263 = getelementptr inbounds i8, ptr %_941, i64 2
  store i16 2, ptr %263, align 2
  store i16 0, ptr %_941, align 8
  store i16 2, ptr %_942, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_940, i64 0, i32 -268435424, ptr align 8 %_941, ptr align 8 %_942)
  %264 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_939, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %264, ptr align 8 %_940, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_929, ptr align 8 @alloc_e5fe011c7b5db218d1e9bbca664aae47, i64 2, ptr align 8 %_934, i64 1, ptr align 8 %_939, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_929)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_945, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_945)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_948, ptr align 8 @alloc_a818d64198879d3f38ef5915aa155740)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_948)
  store double 5.000000e+00, ptr %radius, align 8
  %265 = load double, ptr %radius, align 8
  %_952 = fmul double 0x400921FB54442D18, %265
  %266 = load double, ptr %radius, align 8
  %267 = fmul double %_952, %266
  store double %267, ptr %circle_area, align 8
  %268 = load double, ptr %radius, align 8
  %269 = fmul double 0x401921FB54442D18, %268
  store double %269, ptr %circle_circumference, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_960, ptr align 8 %radius)
  %270 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_959, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %270, ptr align 8 %_960, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_956, ptr align 8 @alloc_07f84e4998fda08dd4ad4803ee463098, ptr align 8 %_959)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_956)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_969, ptr align 8 %circle_area)
  %271 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_968, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %271, ptr align 8 %_969, i64 16, i1 false)
  %272 = getelementptr inbounds i8, ptr %_975, i64 2
  store i16 2, ptr %272, align 2
  store i16 0, ptr %_975, align 8
  store i16 2, ptr %_976, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_974, i64 0, i32 -268435424, ptr align 8 %_975, ptr align 8 %_976)
  %273 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_973, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %273, ptr align 8 %_974, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_963, ptr align 8 @alloc_d7008bd83291e8f3585358147a8a9eed, i64 2, ptr align 8 %_968, i64 1, ptr align 8 %_973, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_963)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_985, ptr align 8 %circle_circumference)
  %274 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_984, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %274, ptr align 8 %_985, i64 16, i1 false)
  %275 = getelementptr inbounds i8, ptr %_991, i64 2
  store i16 2, ptr %275, align 2
  store i16 0, ptr %_991, align 8
  store i16 2, ptr %_992, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_990, i64 0, i32 -268435424, ptr align 8 %_991, ptr align 8 %_992)
  %276 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_989, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %276, ptr align 8 %_990, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_979, ptr align 8 @alloc_6a795f2badd5da71c4f1649b751db989, i64 2, ptr align 8 %_984, i64 1, ptr align 8 %_989, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_979)
  store double 8.000000e+00, ptr %length, align 8
  store double 6.000000e+00, ptr %width, align 8
  %277 = load double, ptr %length, align 8
  %278 = load double, ptr %width, align 8
  %279 = fmul double %277, %278
  store double %279, ptr %rect_area, align 8
  %280 = load double, ptr %length, align 8
  %281 = load double, ptr %width, align 8
  %_998 = fadd double %280, %281
  %282 = fmul double 2.000000e+00, %_998
  store double %282, ptr %rect_perimeter, align 8
  %283 = load double, ptr %length, align 8
  %284 = load double, ptr %length, align 8
  %_1001 = fmul double %283, %284
  %285 = load double, ptr %width, align 8
  %286 = load double, ptr %width, align 8
  %_1002 = fmul double %285, %286
  %_1000 = fadd double %_1001, %_1002
; call std::f64::<impl f64>::sqrt
  %287 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %_1000)
  store double %287, ptr %rect_diagonal, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1008, ptr align 8 %length)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1010, ptr align 8 %width)
  %288 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1007, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %288, ptr align 8 %_1008, i64 16, i1 false)
  %289 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1007, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %289, ptr align 8 %_1010, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1004, ptr align 8 @alloc_f57bafb46e0b9795704237b009ffa89e, ptr align 8 %_1007)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1004)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1019, ptr align 8 %rect_area)
  %290 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1018, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %290, ptr align 8 %_1019, i64 16, i1 false)
  %291 = getelementptr inbounds i8, ptr %_1025, i64 2
  store i16 2, ptr %291, align 2
  store i16 0, ptr %_1025, align 8
  store i16 2, ptr %_1026, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1024, i64 0, i32 -268435424, ptr align 8 %_1025, ptr align 8 %_1026)
  %292 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1023, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %292, ptr align 8 %_1024, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1013, ptr align 8 @alloc_8902b4ffc6aeb681b264c7ffe8887a9f, i64 2, ptr align 8 %_1018, i64 1, ptr align 8 %_1023, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1013)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1035, ptr align 8 %rect_perimeter)
  %293 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1034, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %293, ptr align 8 %_1035, i64 16, i1 false)
  %294 = getelementptr inbounds i8, ptr %_1041, i64 2
  store i16 2, ptr %294, align 2
  store i16 0, ptr %_1041, align 8
  store i16 2, ptr %_1042, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1040, i64 0, i32 -268435424, ptr align 8 %_1041, ptr align 8 %_1042)
  %295 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1039, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %295, ptr align 8 %_1040, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1029, ptr align 8 @alloc_b5f48ad89d5e7bd2384e22277cfa38ff, i64 2, ptr align 8 %_1034, i64 1, ptr align 8 %_1039, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1029)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1051, ptr align 8 %rect_diagonal)
  %296 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1050, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %296, ptr align 8 %_1051, i64 16, i1 false)
  %297 = getelementptr inbounds i8, ptr %_1057, i64 2
  store i16 2, ptr %297, align 2
  store i16 0, ptr %_1057, align 8
  store i16 2, ptr %_1058, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1056, i64 0, i32 -268435424, ptr align 8 %_1057, ptr align 8 %_1058)
  %298 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1055, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %298, ptr align 8 %_1056, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1045, ptr align 8 @alloc_259b93caf139ec0560ee181a4678e6ec, i64 2, ptr align 8 %_1050, i64 1, ptr align 8 %_1055, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1045)
  store double 3.000000e+00, ptr %side_a, align 8
  store double 4.000000e+00, ptr %side_b, align 8
  store double 5.000000e+00, ptr %side_c, align 8
  %299 = load double, ptr %side_a, align 8
  %300 = load double, ptr %side_b, align 8
  %_1065 = fadd double %299, %300
  %301 = load double, ptr %side_c, align 8
  %_1064 = fadd double %_1065, %301
  %semi_perimeter = fdiv double %_1064, 2.000000e+00
  %302 = load double, ptr %side_a, align 8
  %_1070 = fsub double %semi_perimeter, %302
  %_1069 = fmul double %semi_perimeter, %_1070
  %303 = load double, ptr %side_b, align 8
  %_1071 = fsub double %semi_perimeter, %303
  %_1068 = fmul double %_1069, %_1071
  %304 = load double, ptr %side_c, align 8
  %_1072 = fsub double %semi_perimeter, %304
  %_1067 = fmul double %_1068, %_1072
; call std::f64::<impl f64>::sqrt
  %305 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %_1067)
  store double %305, ptr %triangle_area, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1078, ptr align 8 %side_a)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1080, ptr align 8 %side_b)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1082, ptr align 8 %side_c)
  %306 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1077, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %306, ptr align 8 %_1078, i64 16, i1 false)
  %307 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1077, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %307, ptr align 8 %_1080, i64 16, i1 false)
  %308 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1077, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %308, ptr align 8 %_1082, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_1074, ptr align 8 @alloc_f31191e570319b3e3a9f0087287784eb, ptr align 8 %_1077)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1074)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1091, ptr align 8 %triangle_area)
  %309 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1090, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %309, ptr align 8 %_1091, i64 16, i1 false)
  %310 = getelementptr inbounds i8, ptr %_1097, i64 2
  store i16 2, ptr %310, align 2
  store i16 0, ptr %_1097, align 8
  store i16 2, ptr %_1098, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1096, i64 0, i32 -268435424, ptr align 8 %_1097, ptr align 8 %_1098)
  %311 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1095, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %311, ptr align 8 %_1096, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1085, ptr align 8 @alloc_a03e1dc817e436a38e6c80eb9e7baa76, i64 2, ptr align 8 %_1090, i64 1, ptr align 8 %_1095, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1085)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1101, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1101)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1104, ptr align 8 @alloc_ed6d0f770aa3e5114914ca3a7a24069d)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1104)
  store double 1.000000e+00, ptr %point1, align 8
  %312 = getelementptr inbounds i8, ptr %point1, i64 8
  store double 2.000000e+00, ptr %312, align 8
  store double 4.000000e+00, ptr %point2, align 8
  %313 = getelementptr inbounds i8, ptr %point2, i64 8
  store double 6.000000e+00, ptr %313, align 8
  %_1112 = load double, ptr %point2, align 8
  %_1113 = load double, ptr %point1, align 8
  %_1111 = fsub double %_1112, %_1113
; call std::f64::<impl f64>::powi
  %_1110 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %_1111, i32 2)
  %314 = getelementptr inbounds i8, ptr %point2, i64 8
  %_1116 = load double, ptr %314, align 8
  %315 = getelementptr inbounds i8, ptr %point1, i64 8
  %_1117 = load double, ptr %315, align 8
  %_1115 = fsub double %_1116, %_1117
; call std::f64::<impl f64>::powi
  %_1114 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %_1115, i32 2)
  %_1109 = fadd double %_1110, %_1114
; call std::f64::<impl f64>::sqrt
  %316 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %_1109)
  store double %316, ptr %distance, align 8
  %_1121 = load double, ptr %point1, align 8
  %_1122 = load double, ptr %point2, align 8
  %_1120 = fadd double %_1121, %_1122
  %_1119 = fdiv double %_1120, 2.000000e+00
  %317 = getelementptr inbounds i8, ptr %point1, i64 8
  %_1125 = load double, ptr %317, align 8
  %318 = getelementptr inbounds i8, ptr %point2, i64 8
  %_1126 = load double, ptr %318, align 8
  %_1124 = fadd double %_1125, %_1126
  %_1123 = fdiv double %_1124, 2.000000e+00
  store double %_1119, ptr %midpoint, align 8
  %319 = getelementptr inbounds i8, ptr %midpoint, i64 8
  store double %_1123, ptr %319, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1132, ptr align 8 %point1)
  %_1135 = getelementptr inbounds i8, ptr %point1, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1134, ptr align 8 %_1135)
  %320 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1131, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %320, ptr align 8 %_1132, i64 16, i1 false)
  %321 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1131, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %321, ptr align 8 %_1134, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1128, ptr align 8 @alloc_61773b17ed338785b327ee2cd659e084, ptr align 8 %_1131)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1128)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1141, ptr align 8 %point2)
  %_1144 = getelementptr inbounds i8, ptr %point2, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1143, ptr align 8 %_1144)
  %322 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1140, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %322, ptr align 8 %_1141, i64 16, i1 false)
  %323 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1140, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %323, ptr align 8 %_1143, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1137, ptr align 8 @alloc_b5a55ecf4705f79ab8fd7c42b5da46b7, ptr align 8 %_1140)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1137)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1152, ptr align 8 %distance)
  %324 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1151, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %324, ptr align 8 %_1152, i64 16, i1 false)
  %325 = getelementptr inbounds i8, ptr %_1158, i64 2
  store i16 2, ptr %325, align 2
  store i16 0, ptr %_1158, align 8
  store i16 2, ptr %_1159, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1157, i64 0, i32 -268435424, ptr align 8 %_1158, ptr align 8 %_1159)
  %326 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1156, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %326, ptr align 8 %_1157, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1146, ptr align 8 @alloc_0e52f25d7a4988b0ae4075e691ad0996, i64 2, ptr align 8 %_1151, i64 1, ptr align 8 %_1156, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1146)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1168, ptr align 8 %midpoint)
  %_1171 = getelementptr inbounds i8, ptr %midpoint, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1170, ptr align 8 %_1171)
  %327 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1167, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %327, ptr align 8 %_1168, i64 16, i1 false)
  %328 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1167, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %328, ptr align 8 %_1170, i64 16, i1 false)
  %329 = getelementptr inbounds i8, ptr %_1176, i64 2
  store i16 1, ptr %329, align 2
  store i16 0, ptr %_1176, align 8
  store i16 2, ptr %_1177, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1175, i64 0, i32 -268435424, ptr align 8 %_1176, ptr align 8 %_1177)
  %330 = getelementptr inbounds i8, ptr %_1179, i64 2
  store i16 1, ptr %330, align 2
  store i16 0, ptr %_1179, align 8
  store i16 2, ptr %_1180, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1178, i64 1, i32 -268435424, ptr align 8 %_1179, ptr align 8 %_1180)
  %331 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1174, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %331, ptr align 8 %_1175, i64 48, i1 false)
  %332 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1174, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %332, ptr align 8 %_1178, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1162, ptr align 8 @alloc_ee52372046e04080d4280aa300e84ffc, i64 3, ptr align 8 %_1167, i64 2, ptr align 8 %_1174, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1162)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1183, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1183)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1186, ptr align 8 @alloc_326fae74cff293395ec663e598416e8f)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1186)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf863846c96c7deb3E"(ptr sret([24 x i8]) align 8 %_1189, i64 0, i64 10)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h99abcbff33964f45E"(ptr sret([24 x i8]) align 8 %_1188, ptr align 8 %_1189)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter7, ptr align 8 %_1188, i64 24, i1 false)
  br label %bb448

bb448:                                            ; preds = %bb450, %bb284
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %333 = call { i64, i64 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17h26a307ae4389603cE"(ptr align 8 %iter7)
  %334 = extractvalue { i64, i64 } %333, 0
  %335 = extractvalue { i64, i64 } %333, 1
  store i64 %334, ptr %_1191, align 8
  %336 = getelementptr inbounds i8, ptr %_1191, i64 8
  store i64 %335, ptr %336, align 8
  %_1193 = load i64, ptr %_1191, align 8
  %337 = getelementptr inbounds i8, ptr %_1191, i64 8
  %338 = load i64, ptr %337, align 8
  %339 = trunc nuw i64 %_1193 to i1
  br i1 %339, label %bb450, label %bb451

bb450:                                            ; preds = %bb448
  %340 = getelementptr inbounds i8, ptr %_1191, i64 8
  %341 = load i64, ptr %340, align 8
  store i64 %341, ptr %n, align 8
  %342 = load i64, ptr %n, align 8
; call _21_mathematical_functions::factorial
  %343 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %342)
  store i64 %343, ptr %fact, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1201, ptr align 8 %n)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1203, ptr align 8 %fact)
  %344 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1200, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %344, ptr align 8 %_1201, i64 16, i1 false)
  %345 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1200, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %345, ptr align 8 %_1203, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1197, ptr align 8 @alloc_6f9cb349d3e8afcbce241773548339ce, ptr align 8 %_1200)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1197)
  br label %bb448

bb451:                                            ; preds = %bb448
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1206, ptr align 8 @alloc_ab08460099930b23cae469bd33629e3e)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1206)
  store i64 10, ptr %n8, align 8
  store i64 3, ptr %r, align 8
  %346 = load i64, ptr %n8, align 8
; call _21_mathematical_functions::factorial
  %_1211 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %346)
  %347 = load i64, ptr %r, align 8
; call _21_mathematical_functions::factorial
  %_1213 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %347)
  %348 = load i64, ptr %n8, align 8
  %349 = load i64, ptr %r, align 8
  %_1216.0 = sub i64 %348, %349
  %_1216.1 = icmp ult i64 %348, %349
  br i1 %_1216.1, label %panic, label %bb460

bb460:                                            ; preds = %bb451
; call _21_mathematical_functions::factorial
  %_1214 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %_1216.0)
  %350 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %_1213, i64 %_1214)
  %_1217.0 = extractvalue { i64, i1 } %350, 0
  %_1217.1 = extractvalue { i64, i1 } %350, 1
  br i1 %_1217.1, label %panic13, label %bb462

panic:                                            ; preds = %bb451
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_fb3ab407f0aaaa7dd62f414a4334e103) #12
  unreachable

bb462:                                            ; preds = %bb460
  %_1218 = icmp eq i64 %_1217.0, 0
  br i1 %_1218, label %panic14, label %bb463

panic13:                                          ; preds = %bb460
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_69977decc73ded67df62219241e5b08f) #12
  unreachable

bb463:                                            ; preds = %bb462
  %351 = udiv i64 %_1211, %_1217.0
  store i64 %351, ptr %combinations, align 8
  %352 = load i64, ptr %n8, align 8
; call _21_mathematical_functions::factorial
  %_1220 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %352)
  %353 = load i64, ptr %n8, align 8
  %354 = load i64, ptr %r, align 8
  %_1223.0 = sub i64 %353, %354
  %_1223.1 = icmp ult i64 %353, %354
  br i1 %_1223.1, label %panic15, label %bb465

panic14:                                          ; preds = %bb462
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_29bad07221f6bfd3ef53326045497816) #12
  unreachable

bb465:                                            ; preds = %bb463
; call _21_mathematical_functions::factorial
  %_1221 = call i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %_1223.0)
  %_1224 = icmp eq i64 %_1221, 0
  br i1 %_1224, label %panic16, label %bb467

panic15:                                          ; preds = %bb463
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_219032e40856f2d193ab98d93a0ac508) #12
  unreachable

bb467:                                            ; preds = %bb465
  %355 = udiv i64 %_1220, %_1221
  store i64 %355, ptr %permutations, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1230, ptr align 8 %n8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1232, ptr align 8 %r)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1234, ptr align 8 %combinations)
  %356 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1229, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %356, ptr align 8 %_1230, i64 16, i1 false)
  %357 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1229, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %357, ptr align 8 %_1232, i64 16, i1 false)
  %358 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1229, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %358, ptr align 8 %_1234, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_1226, ptr align 8 @alloc_fb42756cc3da9fb402ff3bca4b291e2f, ptr align 8 %_1229)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1226)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1241, ptr align 8 %n8)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1243, ptr align 8 %r)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1245, ptr align 8 %permutations)
  %359 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1240, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %359, ptr align 8 %_1241, i64 16, i1 false)
  %360 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1240, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %360, ptr align 8 %_1243, i64 16, i1 false)
  %361 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1240, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %361, ptr align 8 %_1245, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_1237, ptr align 8 @alloc_e2431136cec9b058bf4d4b5edaed9452, ptr align 8 %_1240)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1237)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1248, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1248)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1251, ptr align 8 @alloc_a3646448bb21253f7b44414db5c869c1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1251)
  store i64 48, ptr %num1, align 8
  store i64 18, ptr %num2, align 8
  %362 = load i64, ptr %num1, align 8
  %363 = load i64, ptr %num2, align 8
; call _21_mathematical_functions::gcd
  %364 = call i64 @_ZN26_21_mathematical_functions3gcd17h5c8f870afde3bbbeE(i64 %362, i64 %363)
  store i64 %364, ptr %gcd_result, align 8
  %365 = load i64, ptr %num1, align 8
  %366 = load i64, ptr %num2, align 8
  %367 = call { i64, i1 } @llvm.umul.with.overflow.i64(i64 %365, i64 %366)
  %_1258.0 = extractvalue { i64, i1 } %367, 0
  %_1258.1 = extractvalue { i64, i1 } %367, 1
  br i1 %_1258.1, label %panic17, label %bb483

panic16:                                          ; preds = %bb465
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_76f4ee9951a69d675cfc23fbee9011eb) #12
  unreachable

bb483:                                            ; preds = %bb467
  %368 = load i64, ptr %gcd_result, align 8
  %_1259 = icmp eq i64 %368, 0
  br i1 %_1259, label %panic18, label %bb484

panic17:                                          ; preds = %bb467
; call core::panicking::panic_const::panic_const_mul_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8 @alloc_9b49f1ceeedffede0f5335fc02b9ef54) #12
  unreachable

bb484:                                            ; preds = %bb483
  %369 = load i64, ptr %gcd_result, align 8
  %370 = udiv i64 %_1258.0, %369
  store i64 %370, ptr %lcm_result, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1265, ptr align 8 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1267, ptr align 8 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1269, ptr align 8 %gcd_result)
  %371 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1264, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %371, ptr align 8 %_1265, i64 16, i1 false)
  %372 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1264, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %372, ptr align 8 %_1267, i64 16, i1 false)
  %373 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1264, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %373, ptr align 8 %_1269, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_1261, ptr align 8 @alloc_d6ae72f098712ee0a3bec19b9e88a961, ptr align 8 %_1264)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1261)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1276, ptr align 8 %num1)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1278, ptr align 8 %num2)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1280, ptr align 8 %lcm_result)
  %374 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1275, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %374, ptr align 8 %_1276, i64 16, i1 false)
  %375 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1275, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %375, ptr align 8 %_1278, i64 16, i1 false)
  %376 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1275, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %376, ptr align 8 %_1280, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h07c99e3dc71a4c8dE(ptr sret([48 x i8]) align 8 %_1272, ptr align 8 @alloc_be67a2b5cd02057df1a373eefa23f7c5, ptr align 8 %_1275)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1272)
  %377 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 0
  store i64 2, ptr %377, align 8
  %378 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 1
  store i64 3, ptr %378, align 8
  %379 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 2
  store i64 4, ptr %379, align 8
  %380 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 3
  store i64 17, ptr %380, align 8
  %381 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 4
  store i64 25, ptr %381, align 8
  %382 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 5
  store i64 29, ptr %382, align 8
  %383 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 6
  store i64 100, ptr %383, align 8
  %384 = getelementptr inbounds nuw i64, ptr %test_numbers, i64 7
  store i64 101, ptr %384, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1284, ptr align 8 @alloc_78248e70a48da0c2f27026bc4dcf8c96)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1284)
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %385 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17h3e692486c2a7a882E"(ptr align 8 %test_numbers)
  %_1286.0 = extractvalue { ptr, ptr } %385, 0
  %_1286.1 = extractvalue { ptr, ptr } %385, 1
  store ptr %_1286.0, ptr %iter9, align 8
  %386 = getelementptr inbounds i8, ptr %iter9, i64 8
  store ptr %_1286.1, ptr %386, align 8
  br label %bb498

panic18:                                          ; preds = %bb483
; call core::panicking::panic_const::panic_const_div_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8 @alloc_9b49f1ceeedffede0f5335fc02b9ef54) #12
  unreachable

bb498:                                            ; preds = %bb500, %bb484
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %387 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17ha22cb5e39ff255fdE"(ptr align 8 %iter9)
  store ptr %387, ptr %_1289, align 8
  %388 = load ptr, ptr %_1289, align 8
  %389 = ptrtoint ptr %388 to i64
  %390 = icmp eq i64 %389, 0
  %_1291 = select i1 %390, i64 0, i64 1
  %391 = trunc nuw i64 %_1291 to i1
  br i1 %391, label %bb500, label %bb501

bb500:                                            ; preds = %bb498
  %_1485 = load ptr, ptr %_1289, align 8
  %392 = load i64, ptr %_1485, align 8
  store i64 %392, ptr %num, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1298, ptr align 8 %num)
  %393 = load i64, ptr %num, align 8
; call _21_mathematical_functions::is_prime
  %394 = call zeroext i1 @_ZN26_21_mathematical_functions8is_prime17hdbb3575ac479cb48E(i64 %393)
  %395 = zext i1 %394 to i8
  store i8 %395, ptr %_1302, align 1
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h9a6cd3bdaa32d1daE(ptr sret([16 x i8]) align 8 %_1300, ptr align 1 %_1302)
  %396 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1297, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %396, ptr align 8 %_1298, i64 16, i1 false)
  %397 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1297, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %397, ptr align 8 %_1300, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1294, ptr align 8 @alloc_e9606bb5ce363cb1670370982c10dd70, ptr align 8 %_1297)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1294)
  br label %bb498

bb501:                                            ; preds = %bb498
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1304, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1304)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1307, ptr align 8 @alloc_69705734b886c9c3bc63f86cd18562a8)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1307)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1310, ptr align 8 @alloc_72ad9bff2df52c056a77e4c19ee527b2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1310)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  %398 = call { i32, i32 } @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17h86587e98c73fb24cE"(i32 0, i32 15)
  %_1312.0 = extractvalue { i32, i32 } %398, 0
  %_1312.1 = extractvalue { i32, i32 } %398, 1
  store i32 %_1312.0, ptr %iter10, align 4
  %399 = getelementptr inbounds i8, ptr %iter10, i64 4
  store i32 %_1312.1, ptr %399, align 4
  br label %bb513

bb513:                                            ; preds = %bb515, %bb501
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::Range<A>>::next
  %400 = call { i32, i32 } @"_ZN4core4iter5range101_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..Range$LT$A$GT$$GT$4next17h12f9193f7870440cE"(ptr align 4 %iter10)
  %401 = extractvalue { i32, i32 } %400, 0
  %402 = extractvalue { i32, i32 } %400, 1
  store i32 %401, ptr %_1315, align 4
  %403 = getelementptr inbounds i8, ptr %_1315, i64 4
  store i32 %402, ptr %403, align 4
  %404 = load i32, ptr %_1315, align 4
  %405 = getelementptr inbounds i8, ptr %_1315, i64 4
  %406 = load i32, ptr %405, align 4
  %_1317 = zext i32 %404 to i64
  %407 = trunc nuw i64 %_1317 to i1
  br i1 %407, label %bb515, label %bb516

bb515:                                            ; preds = %bb513
  %408 = getelementptr inbounds i8, ptr %_1315, i64 4
  %i = load i32, ptr %408, align 4
; call _21_mathematical_functions::fibonacci
  %409 = call i64 @_ZN26_21_mathematical_functions9fibonacci17h8f0151299d5bb4ddE(i32 %i)
  store i64 %409, ptr %_1326, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h8c6bb27c04c565e3E(ptr sret([16 x i8]) align 8 %_1324, ptr align 8 %_1326)
  %410 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1323, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %410, ptr align 8 %_1324, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_1320, ptr align 8 @alloc_a0434d70b15bcc9ff1a7b717be16ed72, ptr align 8 %_1323)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1320)
  br label %bb513

bb516:                                            ; preds = %bb513
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1328, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1328)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1331, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1331)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1334, ptr align 8 @alloc_36cfe2095ee3e8ae98b816dfcd9b5d10)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1334)
  store double 3.000000e+00, ptr %complex1, align 8
  %411 = getelementptr inbounds i8, ptr %complex1, i64 8
  store double 4.000000e+00, ptr %411, align 8
  store double 1.000000e+00, ptr %complex2, align 8
  %412 = getelementptr inbounds i8, ptr %complex2, i64 8
  store double 2.000000e+00, ptr %412, align 8
  %_1340 = load double, ptr %complex1, align 8
  %_1341 = load double, ptr %complex2, align 8
  %_1339 = fadd double %_1340, %_1341
  %413 = getelementptr inbounds i8, ptr %complex1, i64 8
  %_1343 = load double, ptr %413, align 8
  %414 = getelementptr inbounds i8, ptr %complex2, i64 8
  %_1344 = load double, ptr %414, align 8
  %_1342 = fadd double %_1343, %_1344
  store double %_1339, ptr %sum11, align 8
  %415 = getelementptr inbounds i8, ptr %sum11, i64 8
  store double %_1342, ptr %415, align 8
  %_1348 = load double, ptr %complex1, align 8
  %_1349 = load double, ptr %complex2, align 8
  %_1347 = fmul double %_1348, %_1349
  %416 = getelementptr inbounds i8, ptr %complex1, i64 8
  %_1351 = load double, ptr %416, align 8
  %417 = getelementptr inbounds i8, ptr %complex2, i64 8
  %_1352 = load double, ptr %417, align 8
  %_1350 = fmul double %_1351, %_1352
  %_1346 = fsub double %_1347, %_1350
  %_1355 = load double, ptr %complex1, align 8
  %418 = getelementptr inbounds i8, ptr %complex2, i64 8
  %_1356 = load double, ptr %418, align 8
  %_1354 = fmul double %_1355, %_1356
  %419 = getelementptr inbounds i8, ptr %complex1, i64 8
  %_1358 = load double, ptr %419, align 8
  %_1359 = load double, ptr %complex2, align 8
  %_1357 = fmul double %_1358, %_1359
  %_1353 = fadd double %_1354, %_1357
  store double %_1346, ptr %product, align 8
  %420 = getelementptr inbounds i8, ptr %product, i64 8
  store double %_1353, ptr %420, align 8
  %_1363 = load double, ptr %complex1, align 8
  %_1364 = load double, ptr %complex1, align 8
  %_1362 = fmul double %_1363, %_1364
  %421 = getelementptr inbounds i8, ptr %complex1, i64 8
  %_1366 = load double, ptr %421, align 8
  %422 = getelementptr inbounds i8, ptr %complex1, i64 8
  %_1367 = load double, ptr %422, align 8
  %_1365 = fmul double %_1366, %_1367
  %_1361 = fadd double %_1362, %_1365
; call std::f64::<impl f64>::sqrt
  %423 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %_1361)
  store double %423, ptr %magnitude1, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1373, ptr align 8 %complex1)
  %_1376 = getelementptr inbounds i8, ptr %complex1, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1375, ptr align 8 %_1376)
  %424 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1372, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %424, ptr align 8 %_1373, i64 16, i1 false)
  %425 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1372, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %425, ptr align 8 %_1375, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1369, ptr align 8 @alloc_e1d007017aac0a4b560ce2e7e6f7966b, ptr align 8 %_1372)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1369)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1382, ptr align 8 %complex2)
  %_1385 = getelementptr inbounds i8, ptr %complex2, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1384, ptr align 8 %_1385)
  %426 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1381, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %426, ptr align 8 %_1382, i64 16, i1 false)
  %427 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1381, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %427, ptr align 8 %_1384, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1378, ptr align 8 @alloc_526391f2c7df527c87d72c4609e5f8fc, ptr align 8 %_1381)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1378)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1391, ptr align 8 %sum11)
  %_1394 = getelementptr inbounds i8, ptr %sum11, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1393, ptr align 8 %_1394)
  %428 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1390, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %428, ptr align 8 %_1391, i64 16, i1 false)
  %429 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1390, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %429, ptr align 8 %_1393, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117hfc1cf43c342350e5E(ptr sret([48 x i8]) align 8 %_1387, ptr align 8 @alloc_01088f651774a781dd335dde79938cbd, ptr align 8 %_1390)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1387)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1402, ptr align 8 %product)
  %_1405 = getelementptr inbounds i8, ptr %product, i64 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1404, ptr align 8 %_1405)
  %430 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1401, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %430, ptr align 8 %_1402, i64 16, i1 false)
  %431 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1401, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %431, ptr align 8 %_1404, i64 16, i1 false)
  %432 = getelementptr inbounds i8, ptr %_1410, i64 2
  store i16 2, ptr %432, align 2
  store i16 0, ptr %_1410, align 8
  store i16 2, ptr %_1411, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1409, i64 0, i32 -268435424, ptr align 8 %_1410, ptr align 8 %_1411)
  %433 = getelementptr inbounds i8, ptr %_1413, i64 2
  store i16 2, ptr %433, align 2
  store i16 0, ptr %_1413, align 8
  store i16 2, ptr %_1414, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1412, i64 1, i32 -268435424, ptr align 8 %_1413, ptr align 8 %_1414)
  %434 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1408, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %434, ptr align 8 %_1409, i64 48, i1 false)
  %435 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1408, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %435, ptr align 8 %_1412, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1396, ptr align 8 @alloc_1c0a7d4c6841616366ce5f8f1ee10c60, i64 3, ptr align 8 %_1401, i64 2, ptr align 8 %_1408, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1396)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1423, ptr align 8 %magnitude1)
  %436 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1422, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %436, ptr align 8 %_1423, i64 16, i1 false)
  %437 = getelementptr inbounds i8, ptr %_1429, i64 2
  store i16 2, ptr %437, align 2
  store i16 0, ptr %_1429, align 8
  store i16 2, ptr %_1430, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1428, i64 0, i32 -268435424, ptr align 8 %_1429, ptr align 8 %_1430)
  %438 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1427, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %438, ptr align 8 %_1428, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1417, ptr align 8 @alloc_8095077786c9cf69c43ccdeffc74a784, i64 2, ptr align 8 %_1422, i64 1, ptr align 8 %_1427, i64 1)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1417)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1433, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1433)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1436, ptr align 8 @alloc_37263b8a59b070e89a51e380ade6894b)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1436)
  %439 = getelementptr inbounds nuw double, ptr %coefficients, i64 0
  store double 1.000000e+00, ptr %439, align 8
  %440 = getelementptr inbounds nuw double, ptr %coefficients, i64 1
  store double -3.000000e+00, ptr %440, align 8
  %441 = getelementptr inbounds nuw double, ptr %coefficients, i64 2
  store double 2.000000e+00, ptr %441, align 8
  %442 = getelementptr inbounds nuw double, ptr %x_values, i64 0
  store double 0.000000e+00, ptr %442, align 8
  %443 = getelementptr inbounds nuw double, ptr %x_values, i64 1
  store double 1.000000e+00, ptr %443, align 8
  %444 = getelementptr inbounds nuw double, ptr %x_values, i64 2
  store double 2.000000e+00, ptr %444, align 8
  %445 = getelementptr inbounds nuw double, ptr %x_values, i64 3
  store double 3.000000e+00, ptr %445, align 8
  %446 = getelementptr inbounds nuw double, ptr %x_values, i64 4
  store double 4.000000e+00, ptr %446, align 8
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1441, ptr align 8 @alloc_ed875bab1bbe78d9454a68e6f93e95cd)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1441)
; call core::array::<impl core::iter::traits::collect::IntoIterator for &[T; N]>::into_iter
  %447 = call { ptr, ptr } @"_ZN4core5array98_$LT$impl$u20$core..iter..traits..collect..IntoIterator$u20$for$u20$$RF$$u5b$T$u3b$$u20$N$u5d$$GT$9into_iter17ha3e16c7299db7d5bE"(ptr align 8 %x_values)
  %_1443.0 = extractvalue { ptr, ptr } %447, 0
  %_1443.1 = extractvalue { ptr, ptr } %447, 1
  store ptr %_1443.0, ptr %iter12, align 8
  %448 = getelementptr inbounds i8, ptr %iter12, i64 8
  store ptr %_1443.1, ptr %448, align 8
  br label %bb558

bb558:                                            ; preds = %bb560, %bb516
; call <core::slice::iter::Iter<T> as core::iter::traits::iterator::Iterator>::next
  %449 = call align 8 ptr @"_ZN91_$LT$core..slice..iter..Iter$LT$T$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hd1b09a6a5db76340E"(ptr align 8 %iter12)
  store ptr %449, ptr %_1446, align 8
  %450 = load ptr, ptr %_1446, align 8
  %451 = ptrtoint ptr %450 to i64
  %452 = icmp eq i64 %451, 0
  %_1448 = select i1 %452, i64 0, i64 1
  %453 = trunc nuw i64 %_1448 to i1
  br i1 %453, label %bb560, label %bb561

bb560:                                            ; preds = %bb558
  %_1486 = load ptr, ptr %_1446, align 8
  %454 = load double, ptr %_1486, align 8
  store double %454, ptr %x, align 8
  %455 = load double, ptr %x, align 8
; call _21_mathematical_functions::evaluate_polynomial
  %456 = call double @_ZN26_21_mathematical_functions19evaluate_polynomial17h269a8d86eeb61e2fE(ptr align 8 %coefficients, i64 3, double %455)
  store double %456, ptr %result, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1460, ptr align 8 %x)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_1462, ptr align 8 %result)
  %457 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1459, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %457, ptr align 8 %_1460, i64 16, i1 false)
  %458 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_1459, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %458, ptr align 8 %_1462, i64 16, i1 false)
  store i16 2, ptr %_1468, align 8
  store i16 2, ptr %_1469, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1467, i64 0, i32 -536870880, ptr align 8 %_1468, ptr align 8 %_1469)
  %459 = getelementptr inbounds i8, ptr %_1471, i64 2
  store i16 2, ptr %459, align 2
  store i16 0, ptr %_1471, align 8
  store i16 2, ptr %_1472, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_1470, i64 1, i32 -268435424, ptr align 8 %_1471, ptr align 8 %_1472)
  %460 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1466, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %460, ptr align 8 %_1467, i64 48, i1 false)
  %461 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_1466, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %461, ptr align 8 %_1470, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_1454, ptr align 8 @alloc_ba7d6ac60039dabaf730f3ff2aa8203f, i64 3, ptr align 8 %_1459, i64 2, ptr align 8 %_1466, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1454)
  br label %bb558

bb561:                                            ; preds = %bb558
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1475, ptr align 8 @alloc_d1a7a28b1b2f6258cb14ac97db016f09)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1475)
; call core::fmt::Arguments::new_const
  call void @_ZN4core3fmt9Arguments9new_const17h05abd7bab384c4e5E(ptr sret([48 x i8]) align 8 %_1478, ptr align 8 @alloc_c08436ecb0f9274cd3b39b90008ba740)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_1478)
  ret void

bb201:                                            ; preds = %bb199
  %462 = load double, ptr %val, align 8
  %_522 = fcmp ole double %462, 1.000000e+00
  br i1 %_522, label %bb202, label %bb197

bb202:                                            ; preds = %bb201
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_528, ptr align 8 %val)
  %463 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_527, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %463, ptr align 8 %_528, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_524, ptr align 8 @alloc_a37200dd26e9374fe4498e5684023e1f, ptr align 8 %_527)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_524)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_537, ptr align 8 %val)
  %464 = load double, ptr %val, align 8
; call std::f64::<impl f64>::asin
  %465 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4asin17h3f95b6cd2537be19E"(double %464)
  store double %465, ptr %_541, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_539, ptr align 8 %_541)
  %466 = load double, ptr %val, align 8
; call std::f64::<impl f64>::asin
  %_545 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4asin17h3f95b6cd2537be19E"(double %466)
; call core::f64::<impl f64>::to_degrees
  %467 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_degrees17hcbc2515506bf219aE"(double %_545)
  store double %467, ptr %_544, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_542, ptr align 8 %_544)
  %468 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_536, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %468, ptr align 8 %_537, i64 16, i1 false)
  %469 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_536, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %469, ptr align 8 %_539, i64 16, i1 false)
  %470 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_536, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %470, ptr align 8 %_542, i64 16, i1 false)
  store i16 2, ptr %_550, align 8
  store i16 2, ptr %_551, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_549, i64 0, i32 -536870880, ptr align 8 %_550, ptr align 8 %_551)
  %471 = getelementptr inbounds i8, ptr %_553, i64 2
  store i16 6, ptr %471, align 2
  store i16 0, ptr %_553, align 8
  store i16 2, ptr %_554, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_552, i64 1, i32 -268435424, ptr align 8 %_553, ptr align 8 %_554)
  %472 = getelementptr inbounds i8, ptr %_556, i64 2
  store i16 2, ptr %472, align 2
  store i16 0, ptr %_556, align 8
  store i16 2, ptr %_557, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_555, i64 2, i32 -268435424, ptr align 8 %_556, ptr align 8 %_557)
  %473 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_548, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %473, ptr align 8 %_549, i64 48, i1 false)
  %474 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_548, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %474, ptr align 8 %_552, i64 48, i1 false)
  %475 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_548, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %475, ptr align 8 %_555, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_531, ptr align 8 @alloc_8374882af40e6553855e7c1b30923341, i64 4, ptr align 8 %_536, i64 3, ptr align 8 %_548, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_531)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_566, ptr align 8 %val)
  %476 = load double, ptr %val, align 8
; call std::f64::<impl f64>::acos
  %477 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4acos17h7296f6f8305c7dc2E"(double %476)
  store double %477, ptr %_570, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_568, ptr align 8 %_570)
  %478 = load double, ptr %val, align 8
; call std::f64::<impl f64>::acos
  %_574 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4acos17h7296f6f8305c7dc2E"(double %478)
; call core::f64::<impl f64>::to_degrees
  %479 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_degrees17hcbc2515506bf219aE"(double %_574)
  store double %479, ptr %_573, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_571, ptr align 8 %_573)
  %480 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_565, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %480, ptr align 8 %_566, i64 16, i1 false)
  %481 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_565, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %481, ptr align 8 %_568, i64 16, i1 false)
  %482 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_565, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %482, ptr align 8 %_571, i64 16, i1 false)
  store i16 2, ptr %_579, align 8
  store i16 2, ptr %_580, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_578, i64 0, i32 -536870880, ptr align 8 %_579, ptr align 8 %_580)
  %483 = getelementptr inbounds i8, ptr %_582, i64 2
  store i16 6, ptr %483, align 2
  store i16 0, ptr %_582, align 8
  store i16 2, ptr %_583, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_581, i64 1, i32 -268435424, ptr align 8 %_582, ptr align 8 %_583)
  %484 = getelementptr inbounds i8, ptr %_585, i64 2
  store i16 2, ptr %484, align 2
  store i16 0, ptr %_585, align 8
  store i16 2, ptr %_586, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_584, i64 2, i32 -268435424, ptr align 8 %_585, ptr align 8 %_586)
  %485 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_577, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %485, ptr align 8 %_578, i64 48, i1 false)
  %486 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_577, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %486, ptr align 8 %_581, i64 48, i1 false)
  %487 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_577, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %487, ptr align 8 %_584, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_560, ptr align 8 @alloc_1b9cf2d56092e546a08becdfdd798753, i64 4, ptr align 8 %_565, i64 3, ptr align 8 %_577, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_560)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_595, ptr align 8 %val)
  %488 = load double, ptr %val, align 8
; call std::f64::<impl f64>::atan
  %489 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4atan17he788001a47cdf344E"(double %488)
  store double %489, ptr %_599, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_597, ptr align 8 %_599)
  %490 = load double, ptr %val, align 8
; call std::f64::<impl f64>::atan
  %_603 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4atan17he788001a47cdf344E"(double %490)
; call core::f64::<impl f64>::to_degrees
  %491 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$10to_degrees17hcbc2515506bf219aE"(double %_603)
  store double %491, ptr %_602, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_600, ptr align 8 %_602)
  %492 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_594, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %492, ptr align 8 %_595, i64 16, i1 false)
  %493 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_594, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %493, ptr align 8 %_597, i64 16, i1 false)
  %494 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_594, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %494, ptr align 8 %_600, i64 16, i1 false)
  store i16 2, ptr %_608, align 8
  store i16 2, ptr %_609, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_607, i64 0, i32 -536870880, ptr align 8 %_608, ptr align 8 %_609)
  %495 = getelementptr inbounds i8, ptr %_611, i64 2
  store i16 6, ptr %495, align 2
  store i16 0, ptr %_611, align 8
  store i16 2, ptr %_612, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_610, i64 1, i32 -268435424, ptr align 8 %_611, ptr align 8 %_612)
  %496 = getelementptr inbounds i8, ptr %_614, i64 2
  store i16 2, ptr %496, align 2
  store i16 0, ptr %_614, align 8
  store i16 2, ptr %_615, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_613, i64 2, i32 -268435424, ptr align 8 %_614, ptr align 8 %_615)
  %497 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_606, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %497, ptr align 8 %_607, i64 48, i1 false)
  %498 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_606, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %498, ptr align 8 %_610, i64 48, i1 false)
  %499 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_606, i64 2
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %499, ptr align 8 %_613, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_589, ptr align 8 @alloc_90fea351adab84c834877784f9f58740, i64 4, ptr align 8 %_594, i64 3, ptr align 8 %_606, i64 3)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_589)
  br label %bb197

bb189:                                            ; preds = %bb180, %bb154
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_505, ptr align 8 %angle_deg)
  %500 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_504, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %500, ptr align 8 %_505, i64 16, i1 false)
; call core::fmt::Arguments::new_v1
  call void @_ZN4core3fmt9Arguments6new_v117h22d778f30abc942eE(ptr sret([48 x i8]) align 8 %_501, ptr align 8 @alloc_2920d4143b1f9aff8a75d9b5f67db967, ptr align 8 %_504)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_501)
  br label %bb152

bb180:                                            ; preds = %bb154
  %501 = load double, ptr %angle_deg, align 8
  %_477 = fcmp une double %501, 2.700000e+02
  br i1 %_477, label %bb181, label %bb189

bb181:                                            ; preds = %bb180
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_485, ptr align 8 %angle_deg)
  %502 = load double, ptr %angle_rad, align 8
; call std::f64::<impl f64>::tan
  %503 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3tan17heb63c8d704691aceE"(double %502)
  store double %503, ptr %_489, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_487, ptr align 8 %_489)
  %504 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_484, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %504, ptr align 8 %_485, i64 16, i1 false)
  %505 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_484, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %505, ptr align 8 %_487, i64 16, i1 false)
  store i16 2, ptr %_494, align 8
  store i16 2, ptr %_495, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_493, i64 0, i32 -536870880, ptr align 8 %_494, ptr align 8 %_495)
  %506 = getelementptr inbounds i8, ptr %_497, i64 2
  store i16 6, ptr %506, align 2
  store i16 0, ptr %_497, align 8
  store i16 2, ptr %_498, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_496, i64 1, i32 -268435424, ptr align 8 %_497, ptr align 8 %_498)
  %507 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_492, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %507, ptr align 8 %_493, i64 48, i1 false)
  %508 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_492, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %508, ptr align 8 %_496, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_479, ptr align 8 @alloc_eb8225f4a87bb95452a1475f66fc914b, i64 3, ptr align 8 %_484, i64 2, ptr align 8 %_492, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_479)
  br label %bb152

bb113:                                            ; preds = %bb111
  %_313 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 %i19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3cfab197877ea650E(ptr sret([16 x i8]) align 8 %_312, ptr align 8 %_313)
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_315, ptr align 8 %angle)
  %509 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_311, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %509, ptr align 8 %_312, i64 16, i1 false)
  %510 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_311, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %510, ptr align 8 %_315, i64 16, i1 false)
  store i16 2, ptr %_321, align 8
  store i16 2, ptr %_322, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_320, i64 0, i32 -536870880, ptr align 8 %_321, ptr align 8 %_322)
  %511 = getelementptr inbounds i8, ptr %_324, i64 2
  store i16 6, ptr %511, align 2
  store i16 0, ptr %_324, align 8
  store i16 2, ptr %_325, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_323, i64 1, i32 -268435424, ptr align 8 %_324, ptr align 8 %_325)
  %512 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_319, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %512, ptr align 8 %_320, i64 48, i1 false)
  %513 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_319, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %513, ptr align 8 %_323, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_306, ptr align 8 @alloc_e58e2a7074d474581ef62356c3d35f0f, i64 3, ptr align 8 %_311, i64 2, ptr align 8 %_319, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_306)
  %_336 = icmp ult i64 %i19, 6
  br i1 %_336, label %bb121, label %panic21

panic20:                                          ; preds = %bb111
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i19, i64 6, ptr align 8 @alloc_4a5a7ae8156b8db5f910ccf62516770d) #12
  unreachable

bb121:                                            ; preds = %bb113
  %_335 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 %i19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3cfab197877ea650E(ptr sret([16 x i8]) align 8 %_334, ptr align 8 %_335)
  %514 = load double, ptr %angle, align 8
; call std::f64::<impl f64>::sin
  %515 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3sin17h2d8fa3ef473081aaE"(double %514)
  store double %515, ptr %_339, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_337, ptr align 8 %_339)
  %516 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_333, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %516, ptr align 8 %_334, i64 16, i1 false)
  %517 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_333, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %517, ptr align 8 %_337, i64 16, i1 false)
  store i16 2, ptr %_344, align 8
  store i16 2, ptr %_345, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_343, i64 0, i32 -536870880, ptr align 8 %_344, ptr align 8 %_345)
  %518 = getelementptr inbounds i8, ptr %_347, i64 2
  store i16 6, ptr %518, align 2
  store i16 0, ptr %_347, align 8
  store i16 2, ptr %_348, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_346, i64 1, i32 -268435424, ptr align 8 %_347, ptr align 8 %_348)
  %519 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_342, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %519, ptr align 8 %_343, i64 48, i1 false)
  %520 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_342, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %520, ptr align 8 %_346, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_328, ptr align 8 @alloc_dcff1936d1e80d8971cc63014ce5da0d, i64 3, ptr align 8 %_333, i64 2, ptr align 8 %_342, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_328)
  %_359 = icmp ult i64 %i19, 6
  br i1 %_359, label %bb130, label %panic22

panic21:                                          ; preds = %bb113
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i19, i64 6, ptr align 8 @alloc_3c0bcb7feff3a60a684159dc6f0e7df8) #12
  unreachable

bb130:                                            ; preds = %bb121
  %_358 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 %i19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3cfab197877ea650E(ptr sret([16 x i8]) align 8 %_357, ptr align 8 %_358)
  %521 = load double, ptr %angle, align 8
; call std::f64::<impl f64>::cos
  %522 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3cos17h441f1dbd8b285e4cE"(double %521)
  store double %522, ptr %_362, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_360, ptr align 8 %_362)
  %523 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_356, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %523, ptr align 8 %_357, i64 16, i1 false)
  %524 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_356, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %524, ptr align 8 %_360, i64 16, i1 false)
  store i16 2, ptr %_367, align 8
  store i16 2, ptr %_368, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_366, i64 0, i32 -536870880, ptr align 8 %_367, ptr align 8 %_368)
  %525 = getelementptr inbounds i8, ptr %_370, i64 2
  store i16 6, ptr %525, align 2
  store i16 0, ptr %_370, align 8
  store i16 2, ptr %_371, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_369, i64 1, i32 -268435424, ptr align 8 %_370, ptr align 8 %_371)
  %526 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_365, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %526, ptr align 8 %_366, i64 48, i1 false)
  %527 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_365, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %527, ptr align 8 %_369, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_351, ptr align 8 @alloc_133596709236629331b4feafd9153a46, i64 3, ptr align 8 %_356, i64 2, ptr align 8 %_365, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_351)
  %_382 = icmp ult i64 %i19, 6
  br i1 %_382, label %bb139, label %panic23

panic22:                                          ; preds = %bb121
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i19, i64 6, ptr align 8 @alloc_ca49bfb6cc7c32b21d8d6deb6223873f) #12
  unreachable

bb139:                                            ; preds = %bb130
  %_381 = getelementptr inbounds nuw { ptr, i64 }, ptr %angle_names, i64 %i19
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h3cfab197877ea650E(ptr sret([16 x i8]) align 8 %_380, ptr align 8 %_381)
  %528 = load double, ptr %angle, align 8
; call std::f64::<impl f64>::tan
  %529 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$3tan17heb63c8d704691aceE"(double %528)
  store double %529, ptr %_385, align 8
; call core::fmt::rt::Argument::new_display
  call void @_ZN4core3fmt2rt8Argument11new_display17h5c4c9dca51cdc7f3E(ptr sret([16 x i8]) align 8 %_383, ptr align 8 %_385)
  %530 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_379, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %530, ptr align 8 %_380, i64 16, i1 false)
  %531 = getelementptr inbounds nuw %"core::fmt::rt::Argument<'_>", ptr %_379, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %531, ptr align 8 %_383, i64 16, i1 false)
  store i16 2, ptr %_390, align 8
  store i16 2, ptr %_391, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_389, i64 0, i32 -536870880, ptr align 8 %_390, ptr align 8 %_391)
  %532 = getelementptr inbounds i8, ptr %_393, i64 2
  store i16 6, ptr %532, align 2
  store i16 0, ptr %_393, align 8
  store i16 2, ptr %_394, align 8
; call core::fmt::rt::Placeholder::new
  call void @_ZN4core3fmt2rt11Placeholder3new17h8b785e63fc971b96E(ptr sret([48 x i8]) align 8 %_392, i64 1, i32 -268435424, ptr align 8 %_393, ptr align 8 %_394)
  %533 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_388, i64 0
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %533, ptr align 8 %_389, i64 48, i1 false)
  %534 = getelementptr inbounds nuw %"core::fmt::rt::Placeholder", ptr %_388, i64 1
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %534, ptr align 8 %_392, i64 48, i1 false)
; call core::fmt::rt::UnsafeArg::new
  call void @_ZN4core3fmt2rt9UnsafeArg3new17h33f005a1ed2d0c4cE()
; call core::fmt::Arguments::new_v1_formatted
  call void @_ZN4core3fmt9Arguments16new_v1_formatted17h5015795dba21f7a0E(ptr sret([48 x i8]) align 8 %_374, ptr align 8 @alloc_60d40733446453038e537e5feaeecff0, i64 3, ptr align 8 %_379, i64 2, ptr align 8 %_388, i64 2)
; call std::io::stdio::_print
  call void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8 %_374)
  br label %bb108

panic23:                                          ; preds = %bb130
; call core::panicking::panic_bounds_check
  call void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64 %i19, i64 6, ptr align 8 @alloc_307669aee7bfb7f22345b2feaaa96b9a) #12
  unreachable

bb110:                                            ; No predecessors!
  unreachable
}

; _21_mathematical_functions::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h65730c29a30aff68E"(ptr align 1 %_1, double %a, ptr align 8 %_3) unnamed_addr #1 {
start:
  %b = load double, ptr %_3, align 8
; call core::f64::<impl f64>::min
  %_0 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3min17h97a1b461355a299eE"(double %a, double %b)
  ret double %_0
}

; _21_mathematical_functions::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h65e770d911767748E"(ptr align 1 %_1, double %a, ptr align 8 %_3) unnamed_addr #1 {
start:
  %b = load double, ptr %_3, align 8
; call core::f64::<impl f64>::max
  %_0 = call double @"_ZN4core3f6421_$LT$impl$u20$f64$GT$3max17hcdfad5b3469c501fE"(double %a, double %b)
  ret double %_0
}

; _21_mathematical_functions::main::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN26_21_mathematical_functions4main28_$u7b$$u7b$closure$u7d$$u7d$17h11b7d5b6a3699f6cE"(ptr align 8 %_1, ptr align 8 %_2) unnamed_addr #1 {
start:
  %x = load double, ptr %_2, align 8
  %_6 = load ptr, ptr %_1, align 8
  %_5 = load double, ptr %_6, align 8
  %_4 = fsub double %x, %_5
; call std::f64::<impl f64>::powi
  %_0 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %_4, i32 2)
  ret double %_0
}

; _21_mathematical_functions::factorial
; Function Attrs: uwtable
define internal i64 @_ZN26_21_mathematical_functions9factorial17h9f6a23ac406999b2E(i64 %n) unnamed_addr #0 {
start:
  %_3 = alloca [24 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  %_2 = icmp ule i64 %n, 1
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf863846c96c7deb3E"(ptr sret([24 x i8]) align 8 %_3, i64 1, i64 %n)
; call core::iter::traits::iterator::Iterator::product
  %0 = call i64 @_ZN4core4iter6traits8iterator8Iterator7product17h2137ed72368063c3E(ptr align 8 %_3)
  store i64 %0, ptr %_0, align 8
  br label %bb4

bb1:                                              ; preds = %start
  store i64 1, ptr %_0, align 8
  br label %bb4

bb4:                                              ; preds = %bb1, %bb2
  %1 = load i64, ptr %_0, align 8
  ret i64 %1
}

; _21_mathematical_functions::gcd
; Function Attrs: uwtable
define internal i64 @_ZN26_21_mathematical_functions3gcd17h5c8f870afde3bbbeE(i64 %0, i64 %1) unnamed_addr #0 {
start:
  %b = alloca [8 x i8], align 8
  %a = alloca [8 x i8], align 8
  store i64 %0, ptr %a, align 8
  store i64 %1, ptr %b, align 8
  br label %bb1

bb1:                                              ; preds = %bb3, %start
  %_3 = load i64, ptr %b, align 8
  %2 = icmp eq i64 %_3, 0
  br i1 %2, label %bb4, label %bb2

bb4:                                              ; preds = %bb1
  %_0 = load i64, ptr %a, align 8
  ret i64 %_0

bb2:                                              ; preds = %bb1
  %temp = load i64, ptr %b, align 8
  %_5 = load i64, ptr %a, align 8
  %_6 = load i64, ptr %b, align 8
  %_7 = icmp eq i64 %_6, 0
  br i1 %_7, label %panic, label %bb3

bb3:                                              ; preds = %bb2
  %3 = urem i64 %_5, %_6
  store i64 %3, ptr %b, align 8
  store i64 %temp, ptr %a, align 8
  br label %bb1

panic:                                            ; preds = %bb2
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 @alloc_697a2ff9785a41cecb9c68d5d3656e12) #12
  unreachable
}

; _21_mathematical_functions::is_prime
; Function Attrs: uwtable
define internal zeroext i1 @_ZN26_21_mathematical_functions8is_prime17hdbb3575ac479cb48E(i64 %n) unnamed_addr #0 {
start:
  %_12 = alloca [16 x i8], align 8
  %iter = alloca [40 x i8], align 8
  %_10 = alloca [24 x i8], align 8
  %_9 = alloca [40 x i8], align 8
  %_8 = alloca [40 x i8], align 8
  %_0 = alloca [1 x i8], align 1
  %_2 = icmp ult i64 %n, 2
  br i1 %_2, label %bb1, label %bb2

bb2:                                              ; preds = %start
  %0 = icmp eq i64 %n, 2
  br i1 %0, label %bb3, label %bb4

bb1:                                              ; preds = %start
  store i8 0, ptr %_0, align 1
  br label %bb19

bb3:                                              ; preds = %bb2
  store i8 1, ptr %_0, align 1
  br label %bb19

bb4:                                              ; preds = %bb2
  %_3 = urem i64 %n, 2
  %1 = icmp eq i64 %_3, 0
  br i1 %1, label %bb6, label %bb7

bb19:                                             ; preds = %bb1, %bb18, %bb16, %bb6, %bb3
  %2 = load i8, ptr %_0, align 1
  %3 = trunc nuw i8 %2 to i1
  ret i1 %3

bb6:                                              ; preds = %bb4
  store i8 0, ptr %_0, align 1
  br label %bb19

bb7:                                              ; preds = %bb4
  %_7 = uitofp i64 %n to double
; call std::f64::<impl f64>::sqrt
  %_6 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4sqrt17hd60f6d5e075630e8E"(double %_7)
  %sqrt_n = call i64 @llvm.fptoui.sat.i64.f64(double %_6)
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17hf863846c96c7deb3E"(ptr sret([24 x i8]) align 8 %_10, i64 3, i64 %sqrt_n)
; call core::iter::traits::iterator::Iterator::step_by
  call void @_ZN4core4iter6traits8iterator8Iterator7step_by17h59e53cc6f958c81fE(ptr sret([40 x i8]) align 8 %_9, ptr align 8 %_10, i64 2)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17heccd56fdfae1a28bE"(ptr sret([40 x i8]) align 8 %_8, ptr align 8 %_9)
  call void @llvm.memcpy.p0.p0.i64(ptr align 8 %iter, ptr align 8 %_8, i64 40, i1 false)
  br label %bb12

bb12:                                             ; preds = %bb17, %bb7
; call <core::iter::adapters::step_by::StepBy<I> as core::iter::traits::iterator::Iterator>::next
  %4 = call { i64, i64 } @"_ZN105_$LT$core..iter..adapters..step_by..StepBy$LT$I$GT$$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hb90f351d7a47c4c0E"(ptr align 8 %iter)
  %5 = extractvalue { i64, i64 } %4, 0
  %6 = extractvalue { i64, i64 } %4, 1
  store i64 %5, ptr %_12, align 8
  %7 = getelementptr inbounds i8, ptr %_12, i64 8
  store i64 %6, ptr %7, align 8
  %_14 = load i64, ptr %_12, align 8
  %8 = getelementptr inbounds i8, ptr %_12, i64 8
  %9 = load i64, ptr %8, align 8
  %10 = trunc nuw i64 %_14 to i1
  br i1 %10, label %bb15, label %bb16

bb15:                                             ; preds = %bb12
  %11 = getelementptr inbounds i8, ptr %_12, i64 8
  %i = load i64, ptr %11, align 8
  %_17 = icmp eq i64 %i, 0
  br i1 %_17, label %panic, label %bb17

bb16:                                             ; preds = %bb12
  store i8 1, ptr %_0, align 1
  br label %bb19

bb17:                                             ; preds = %bb15
  %_16 = urem i64 %n, %i
  %12 = icmp eq i64 %_16, 0
  br i1 %12, label %bb18, label %bb12

panic:                                            ; preds = %bb15
; call core::panicking::panic_const::panic_const_rem_by_zero
  call void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8 @alloc_4c83b73bab296ead0b6c2f740bbed263) #12
  unreachable

bb18:                                             ; preds = %bb17
  store i8 0, ptr %_0, align 1
  br label %bb19

bb14:                                             ; No predecessors!
  unreachable
}

; _21_mathematical_functions::fibonacci
; Function Attrs: uwtable
define internal i64 @_ZN26_21_mathematical_functions9fibonacci17h8f0151299d5bb4ddE(i32 %n) unnamed_addr #0 {
start:
  %iter = alloca [12 x i8], align 4
  %_5 = alloca [12 x i8], align 4
  %_4 = alloca [12 x i8], align 4
  %b = alloca [8 x i8], align 8
  %a = alloca [8 x i8], align 8
  %_0 = alloca [8 x i8], align 8
  switch i32 %n, label %bb1 [
    i32 0, label %bb3
    i32 1, label %bb2
  ]

bb1:                                              ; preds = %start
  store i64 0, ptr %a, align 8
  store i64 1, ptr %b, align 8
; call core::ops::range::RangeInclusive<Idx>::new
  call void @"_ZN4core3ops5range25RangeInclusive$LT$Idx$GT$3new17h8d984e9143256103E"(ptr sret([12 x i8]) align 4 %_5, i32 2, i32 %n)
; call <I as core::iter::traits::collect::IntoIterator>::into_iter
  call void @"_ZN63_$LT$I$u20$as$u20$core..iter..traits..collect..IntoIterator$GT$9into_iter17hf35c3f156ac73698E"(ptr sret([12 x i8]) align 4 %_4, ptr align 4 %_5)
  call void @llvm.memcpy.p0.p0.i64(ptr align 4 %iter, ptr align 4 %_4, i64 12, i1 false)
  br label %bb6

bb3:                                              ; preds = %start
  store i64 0, ptr %_0, align 8
  br label %bb12

bb2:                                              ; preds = %start
  store i64 1, ptr %_0, align 8
  br label %bb12

bb12:                                             ; preds = %bb10, %bb2, %bb3
  %0 = load i64, ptr %_0, align 8
  ret i64 %0

bb6:                                              ; preds = %bb11, %bb1
; call core::iter::range::<impl core::iter::traits::iterator::Iterator for core::ops::range::RangeInclusive<A>>::next
  %1 = call { i32, i32 } @"_ZN4core4iter5range110_$LT$impl$u20$core..iter..traits..iterator..Iterator$u20$for$u20$core..ops..range..RangeInclusive$LT$A$GT$$GT$4next17ha1446aa7340e9562E"(ptr align 4 %iter)
  %_7.0 = extractvalue { i32, i32 } %1, 0
  %_7.1 = extractvalue { i32, i32 } %1, 1
  %_9 = zext i32 %_7.0 to i64
  %2 = trunc nuw i64 %_9 to i1
  br i1 %2, label %bb9, label %bb10

bb9:                                              ; preds = %bb6
  %_11 = load i64, ptr %a, align 8
  %_12 = load i64, ptr %b, align 8
  %3 = call { i64, i1 } @llvm.uadd.with.overflow.i64(i64 %_11, i64 %_12)
  %_13.0 = extractvalue { i64, i1 } %3, 0
  %_13.1 = extractvalue { i64, i1 } %3, 1
  br i1 %_13.1, label %panic, label %bb11

bb10:                                             ; preds = %bb6
  %4 = load i64, ptr %b, align 8
  store i64 %4, ptr %_0, align 8
  br label %bb12

bb11:                                             ; preds = %bb9
  %_14 = load i64, ptr %b, align 8
  store i64 %_14, ptr %a, align 8
  store i64 %_13.0, ptr %b, align 8
  br label %bb6

panic:                                            ; preds = %bb9
; call core::panicking::panic_const::panic_const_add_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8 @alloc_4e862ca84b7437a7a00f07232d819c3d) #12
  unreachable

bb8:                                              ; No predecessors!
  unreachable
}

; _21_mathematical_functions::evaluate_polynomial
; Function Attrs: uwtable
define internal double @_ZN26_21_mathematical_functions19evaluate_polynomial17h269a8d86eeb61e2fE(ptr align 8 %0, i64 %1, double %2) unnamed_addr #0 {
start:
  %_4 = alloca [24 x i8], align 8
  %_3 = alloca [40 x i8], align 8
  %x = alloca [8 x i8], align 8
  %coefficients = alloca [16 x i8], align 8
  store ptr %0, ptr %coefficients, align 8
  %3 = getelementptr inbounds i8, ptr %coefficients, i64 8
  store i64 %1, ptr %3, align 8
  store double %2, ptr %x, align 8
  %4 = load ptr, ptr %coefficients, align 8
  %5 = getelementptr inbounds i8, ptr %coefficients, i64 8
  %6 = load i64, ptr %5, align 8
; call core::slice::<impl [T]>::iter
  %7 = call { ptr, ptr } @"_ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$4iter17h9ace078414fecffcE"(ptr align 8 %4, i64 %6)
  %_5.0 = extractvalue { ptr, ptr } %7, 0
  %_5.1 = extractvalue { ptr, ptr } %7, 1
; call core::iter::traits::iterator::Iterator::enumerate
  call void @_ZN4core4iter6traits8iterator8Iterator9enumerate17h20a7b10fd82a0585E(ptr sret([24 x i8]) align 8 %_4, ptr %_5.0, ptr %_5.1)
; call core::iter::traits::iterator::Iterator::map
  call void @_ZN4core4iter6traits8iterator8Iterator3map17h38748cb352285587E(ptr sret([40 x i8]) align 8 %_3, ptr align 8 %_4, ptr align 8 %x, ptr align 8 %coefficients)
; call core::iter::traits::iterator::Iterator::sum
  %_0 = call double @_ZN4core4iter6traits8iterator8Iterator3sum17h322f90d27fb6b93fE(ptr align 8 %_3)
  ret double %_0
}

; _21_mathematical_functions::evaluate_polynomial::{{closure}}
; Function Attrs: inlinehint uwtable
define internal double @"_ZN26_21_mathematical_functions19evaluate_polynomial28_$u7b$$u7b$closure$u7d$$u7d$17h8cf3980e243dd01eE"(ptr align 8 %_1, i64 %0, ptr align 8 %1) unnamed_addr #1 {
start:
  %_2 = alloca [16 x i8], align 8
  store i64 %0, ptr %_2, align 8
  %2 = getelementptr inbounds i8, ptr %_2, i64 8
  store ptr %1, ptr %2, align 8
  %i = load i64, ptr %_2, align 8
  %3 = getelementptr inbounds i8, ptr %_2, i64 8
  %_14 = load ptr, ptr %3, align 8
  %coeff = load double, ptr %_14, align 8
  %_15 = load ptr, ptr %_1, align 8
  %_6 = load double, ptr %_15, align 8
  %4 = getelementptr inbounds i8, ptr %_1, i64 8
  %_16 = load ptr, ptr %4, align 8
  %_17.0 = load ptr, ptr %_16, align 8
  %5 = getelementptr inbounds i8, ptr %_16, i64 8
  %_17.1 = load i64, ptr %5, align 8
  %_9 = trunc i64 %_17.1 to i32
  %6 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_9, i32 1)
  %_11.0 = extractvalue { i32, i1 } %6, 0
  %_11.1 = extractvalue { i32, i1 } %6, 1
  br i1 %_11.1, label %panic, label %bb1

bb1:                                              ; preds = %start
  %_12 = trunc i64 %i to i32
  %7 = call { i32, i1 } @llvm.ssub.with.overflow.i32(i32 %_11.0, i32 %_12)
  %_13.0 = extractvalue { i32, i1 } %7, 0
  %_13.1 = extractvalue { i32, i1 } %7, 1
  br i1 %_13.1, label %panic1, label %bb2

panic:                                            ; preds = %start
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_22678f473f24f7dea89642298fe67d0d) #12
  unreachable

bb2:                                              ; preds = %bb1
; call std::f64::<impl f64>::powi
  %_5 = call double @"_ZN3std3f6421_$LT$impl$u20$f64$GT$4powi17h59c574b57865ecdeE"(double %_6, i32 %_13.0)
  %_0 = fmul double %coeff, %_5
  ret double %_0

panic1:                                           ; preds = %bb1
; call core::panicking::panic_const::panic_const_sub_overflow
  call void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8 @alloc_22678f473f24f7dea89642298fe67d0d) #12
  unreachable
}

; Function Attrs: nocallback nofree nounwind willreturn memory(argmem: readwrite)
declare void @llvm.memcpy.p0.p0.i64(ptr noalias nocapture writeonly, ptr noalias nocapture readonly, i64, i1 immarg) #5

declare i32 @__CxxFrameHandler3(...) unnamed_addr #6

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.uadd.with.overflow.i64(i64, i64) #7

; core::panicking::panic_const::panic_const_add_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_add_overflow17h019eb23b03f9a571E(ptr align 8) unnamed_addr #8

; std::rt::lang_start_internal
; Function Attrs: uwtable
declare i64 @_ZN3std2rt19lang_start_internal17h2abdc5cc45b16a8aE(ptr align 1, ptr align 8, i64, ptr, i8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.log.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.cos.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.exp.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.sin.f64(double) #7

; Function Attrs: nounwind uwtable
declare double @tan(double) unnamed_addr #9

; Function Attrs: nounwind uwtable
declare double @acos(double) unnamed_addr #9

; Function Attrs: nounwind uwtable
declare double @asin(double) unnamed_addr #9

; Function Attrs: nounwind uwtable
declare double @atan(double) unnamed_addr #9

; Function Attrs: nounwind uwtable
declare double @cbrt(double) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.ceil.f64(double) #7

; Function Attrs: nounwind uwtable
declare double @cosh(double) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.log2.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.pow.f64(double, double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.powi.f64.i32(double, i32) #7

; Function Attrs: nounwind uwtable
declare double @sinh(double) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.sqrt.f64(double) #7

; Function Attrs: nounwind uwtable
declare double @tanh(double) unnamed_addr #9

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.floor.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.trunc.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.log10.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.round.f64(double) #7

; core::fmt::float::<impl core::fmt::Debug for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$f64$GT$3fmt17h6d2ef5c8d1e67605E"(ptr align 8, ptr align 8) unnamed_addr #0

; <str as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17h9ed88eeff7842748E"(ptr align 1, i64, ptr align 8) unnamed_addr #0

; core::fmt::Formatter::debug_list
; Function Attrs: uwtable
declare void @_ZN4core3fmt9Formatter10debug_list17hc7d50b6d5d876c83E(ptr sret([16 x i8]) align 8, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::finish
; Function Attrs: uwtable
declare zeroext i1 @_ZN4core3fmt8builders9DebugList6finish17h4530e192f538e533E(ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i8 @llvm.ucmp.i8.i64(i64, i64) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.fabs.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.maxnum.f64(double, double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare double @llvm.minnum.f64(double, double) #7

; core::fmt::float::<impl core::fmt::Display for f64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt5float52_$LT$impl$u20$core..fmt..Display$u20$for$u20$f64$GT$3fmt17h63a2068999eae155E"(ptr align 8, ptr align 8) unnamed_addr #0

; core::fmt::num::imp::<impl core::fmt::Display for u64>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h2e45205524cc218aE"(ptr align 8, ptr align 8) unnamed_addr #0

; <bool as core::fmt::Display>::fmt
; Function Attrs: uwtable
declare zeroext i1 @"_ZN43_$LT$bool$u20$as$u20$core..fmt..Display$GT$3fmt17h4507d9a52e8449ffE"(ptr align 1, ptr align 8) unnamed_addr #0

; core::fmt::builders::DebugList::entry
; Function Attrs: uwtable
declare align 8 ptr @_ZN4core3fmt8builders9DebugList5entry17hf411dce3a090bc21E(ptr align 8, ptr align 1, ptr align 8) unnamed_addr #0

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.uadd.with.overflow.i32(i32, i32) #7

; core::panicking::panic_nounwind
; Function Attrs: cold noinline noreturn nounwind uwtable
declare void @_ZN4core9panicking14panic_nounwind17he705cbe34366ef57E(ptr align 1, i64) unnamed_addr #10

; core::panicking::panic
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking5panic17h934661cf41abf459E(ptr align 1, i64, ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i64, i1 } @llvm.umul.with.overflow.i64(i64, i64) #7

; core::panicking::panic_const::panic_const_mul_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_mul_overflow17hc5fa9d748c51347fE(ptr align 8) unnamed_addr #8

; std::io::stdio::_print
; Function Attrs: uwtable
declare void @_ZN3std2io5stdio6_print17hc35641ea0a8da346E(ptr align 8) unnamed_addr #0

; core::panicking::panic_const::panic_const_sub_overflow
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const24panic_const_sub_overflow17h4bc274ae8169ebdcE(ptr align 8) unnamed_addr #8

; core::panicking::panic_const::panic_const_div_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_div_by_zero17h7bc4f99963786481E(ptr align 8) unnamed_addr #8

; core::panicking::panic_bounds_check
; Function Attrs: cold minsize noinline noreturn optsize uwtable
declare void @_ZN4core9panicking18panic_bounds_check17h033c3b9d2ad687b4E(i64, i64, ptr align 8) unnamed_addr #11

; core::panicking::panic_const::panic_const_rem_by_zero
; Function Attrs: cold noinline noreturn uwtable
declare void @_ZN4core9panicking11panic_const23panic_const_rem_by_zero17habbd82649031145dE(ptr align 8) unnamed_addr #8

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare i64 @llvm.fptoui.sat.i64.f64(double) #7

; Function Attrs: nocallback nofree nosync nounwind speculatable willreturn memory(none)
declare { i32, i1 } @llvm.ssub.with.overflow.i32(i32, i32) #7

define i32 @main(i32 %0, ptr %1) unnamed_addr #6 {
top:
  %2 = sext i32 %0 to i64
; call std::rt::lang_start
  %3 = call i64 @_ZN3std2rt10lang_start17h28167623e72390e5E(ptr @_ZN26_21_mathematical_functions4main17hd3edc837827e8a33E, i64 %2, ptr %1, i8 0)
  %4 = trunc i64 %3 to i32
  ret i32 %4
}

attributes #0 = { uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #1 = { inlinehint uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #2 = { noinline uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #3 = { cold nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #4 = { inlinehint nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #5 = { nocallback nofree nounwind willreturn memory(argmem: readwrite) }
attributes #6 = { "target-cpu"="x86-64" }
attributes #7 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }
attributes #8 = { cold noinline noreturn uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #9 = { nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #10 = { cold noinline noreturn nounwind uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #11 = { cold minsize noinline noreturn optsize uwtable "target-cpu"="x86-64" "target-features"="+cx16,+sse3,+sahf" }
attributes #12 = { noreturn }
attributes #13 = { nounwind }
attributes #14 = { noreturn nounwind }

!llvm.module.flags = !{!0, !1}
!llvm.ident = !{!2}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{i32 7, !"PIE Level", i32 2}
!2 = !{!"rustc version 1.87.0 (17067e9ac 2025-05-09)"}
!3 = !{i64 15150381176050446}
