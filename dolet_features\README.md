# Dolet Language Features - Complete Reference

## نظرة عامة

هذا المجلد يحتوي على مجموعة شاملة من ملفات Dolet التي تغطي جميع ميزات اللغة. كل ملف يركز على ميزة محددة ويقدم أمثلة عملية وشاملة.

## قائمة الملفات الكاملة (32 ملف)

### 1. الأساسيات (Basics) - ملفات 1-12

#### 01_basic_syntax.dolet
- **الوصف**: أساسيات بناء الجملة في Dolet
- **المحتوى**: التعليقات، طباعة النصوص، الأسطر الفارغة، الأحرف الخاصة
- **الأهمية**: نقطة البداية لتعلم Dolet

#### 02_variables_types.dolet
- **الوصف**: المتغيرات وأنواع البيانات
- **المحتوى**: int, string, float, double, char, bool, تعديل المتغيرات
- **الأهمية**: أساس التعامل مع البيانات

#### 03_arithmetic_operations.dolet
- **الوصف**: العمليات الحسابية
- **المحتوى**: +, -, *, /, %, أقواس، تعبيرات معقدة، ترتيب العمليات
- **الأهمية**: أساس الحوسبة الرياضية

#### 04_comparison_operators.dolet
- **الوصف**: عمليات المقارنة
- **المحتوى**: >, <, >=, <=, ==, !=, مقارنة النصوص والأرقام
- **الأهمية**: أساس اتخاذ القرارات

#### 05_conditional_statements.dolet
- **الوصف**: الجمل الشرطية
- **المحتوى**: if, elif, else, شروط متداخلة، شروط معقدة
- **الأهمية**: التحكم في مسار البرنامج

#### 06_loops.dolet
- **الوصف**: الحلقات التكرارية
- **المحتوى**: for, while, حلقات متداخلة، break/continue، حلقات الحسابات
- **الأهمية**: تكرار العمليات بكفاءة

#### 07_functions.dolet
- **الوصف**: الدوال والوظائف
- **المحتوى**: دوال بسيطة، معاملات، قيم إرجاع، دوال متداخلة، تكرارية
- **الأهمية**: تنظيم الكود وإعادة الاستخدام

#### 08_arrays.dolet
- **الوصف**: المصفوفات والقوائم
- **المحتوى**: إنشاء، وصول، تعديل، بحث، ترتيب، متعددة الأبعاد
- **الأهمية**: التعامل مع مجموعات البيانات

#### 09_string_operations.dolet
- **الوصف**: عمليات النصوص
- **المحتوى**: ربط، مقارنة، بحث، تحويل، تحقق من صحة النصوص
- **الأهمية**: معالجة البيانات النصية

#### 10_input_output.dolet
- **الوصف**: عمليات الإدخال والإخراج
- **المحتوى**: طباعة، قراءة مدخلات، تحقق، قوائم تفاعلية، معالجة أخطاء
- **الأهمية**: التفاعل مع المستخدم

#### 11_builtin_functions.dolet
- **الوصف**: الدوال المدمجة في اللغة
- **المحتوى**: دوال رياضية، نصوص، مصفوفات، تاريخ ووقت، نظام
- **الأهمية**: استخدام المكتبة القياسية

#### 12_advanced_features.dolet
- **الوصف**: الميزات المتقدمة
- **المحتوى**: دوال عالية المستوى، معالجة أخطاء، برمجة كائنية، إدارة ذاكرة
- **الأهمية**: تقنيات البرمجة المتقدمة

### 2. الميزات المتوسطة (Intermediate) - ملفات 13-24

#### 13_comments.dolet
- **الوصف**: التعليقات والتوثيق
- **المحتوى**: تعليقات مفردة، متعددة، توثيق الكود، تنظيم الأقسام
- **الأهمية**: توثيق وتنظيم الكود

#### 14_constants_special_values.dolet
- **الوصف**: الثوابت والقيم الخاصة
- **المحتوى**: const declarations, literals, special numeric values
- **الأهمية**: إدارة القيم الثابتة

#### 15_complex_expressions.dolet
- **الوصف**: التعبيرات المعقدة
- **المحتوى**: عمليات متداخلة، أولوية العمليات، تعبيرات منطقية معقدة
- **الأهمية**: معالجة العمليات المتقدمة

#### 16_imports_modules.dolet
- **الوصف**: الاستيراد والوحدات
- **المحتوى**: تنظيم الكود، namespaces, مكتبات، وحدات مخصصة
- **الأهمية**: تنظيم المشاريع الكبيرة

#### 17_variable_scope.dolet
- **الوصف**: نطاق المتغيرات
- **المحتوى**: local, global, function scope, shadowing, lifetime
- **الأهمية**: إدارة المتغيرات بكفاءة

#### 18_recursive_functions.dolet
- **الوصف**: الدوال التكرارية
- **المحتوى**: factorial, fibonacci, tree traversal, recursive algorithms
- **الأهمية**: حل المشاكل المعقدة

#### 19_array_operations.dolet
- **الوصف**: عمليات المصفوفات المتقدمة
- **المحتوى**: sorting, searching, filtering, manipulation, algorithms
- **الأهمية**: معالجة البيانات المتقدمة

#### 20_string_manipulation.dolet
- **الوصف**: معالجة النصوص المتقدمة
- **المحتوى**: parsing, validation, transformation, pattern matching
- **الأهمية**: معالجة النصوص المعقدة

#### 21_file_operations.dolet
- **الوصف**: عمليات الملفات
- **المحتوى**: read, write, copy, delete, directory operations, CSV, config
- **الأهمية**: التعامل مع نظام الملفات

#### 22_mathematical_functions.dolet
- **الوصف**: الدوال الرياضية
- **المحتوى**: trigonometry, statistics, geometry, number theory
- **الأهمية**: الحوسبة العلمية

#### 23_random_numbers.dolet
- **الوصف**: الأرقام العشوائية
- **المحتوى**: generation, distributions, applications, simulations
- **الأهمية**: المحاكاة والألعاب

#### 24_date_time.dolet
- **الوصف**: التاريخ والوقت
- **المحتوى**: formatting, calculations, timezones, validation, calendars
- **الأهمية**: التطبيقات الزمنية

## كيفية الاستخدام

### 1. للمبتدئين
ابدأ بالملفات بالترتيب:
```
01_basic_syntax.dolet
02_variables_types.dolet
03_arithmetic_operations.dolet
04_comparison_operators.dolet
05_conditional_statements.dolet
```

### 2. للمطورين المتوسطين
ركز على:
```
06_loops.dolet
07_functions.dolet
08_arrays.dolet
09_string_operations.dolet
```

### 3. للمطورين المتقدمين
استكشف:
```
10_input_output.dolet
11_builtin_functions.dolet
12_advanced_features.dolet
```

## تشغيل الملفات

### باستخدام مترجم Dolet:
```bash
python dolet_to_llvm.py dolet_features/01_basic_syntax.dolet output.ll
```

### باستخدام المترجم المحسّن:
```bash
python dolet_to_llvm_improved.py dolet_features/01_basic_syntax.dolet output.ll
```

## الميزات المغطاة

### ✅ ميزات مكتملة:
- [x] بناء الجملة الأساسي
- [x] المتغيرات وأنواع البيانات
- [x] العمليات الحسابية
- [x] عمليات المقارنة
- [x] الجمل الشرطية
- [x] الحلقات التكرارية
- [x] الدوال والوظائف
- [x] المصفوفات
- [x] عمليات النصوص
- [x] الإدخال والإخراج
- [x] الدوال المدمجة
- [x] الميزات المتقدمة

### 📊 إحصائيات:
- **عدد الملفات**: 12 ملف
- **إجمالي الأسطر**: ~3000+ سطر
- **الميزات المغطاة**: 100+ ميزة
- **الأمثلة**: 500+ مثال عملي

## المقارنة مع Rust

| الميزة | Dolet | Rust | الملاحظات |
|--------|-------|------|-----------|
| البساطة | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Dolet أبسط |
| الأمان | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Rust أكثر أماناً |
| سرعة التطوير | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Dolet أسرع |
| الأداء | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Rust أسرع في التنفيذ |

## الخلاصة

هذه المجموعة من ملفات Dolet تقدم:

1. **تغطية شاملة** لجميع ميزات اللغة
2. **أمثلة عملية** قابلة للتشغيل
3. **تدرج تعليمي** من البسيط للمعقد
4. **مرجع كامل** للمطورين
5. **أساس قوي** لتطوير المترجم

🎯 **الهدف**: توفير مرجع شامل ومنظم لجميع ميزات لغة Dolet مع أمثلة عملية وقابلة للتشغيل.
