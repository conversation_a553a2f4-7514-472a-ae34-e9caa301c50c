// 25. Concurrency Basics - threads, synchronization, parallel processing (Rust version)
// This file demonstrates concurrency basics in Rust for comparison with Dolet

use std::sync::{Arc, Mutex, mpsc};
use std::thread;
use std::time::Duration;

fn main() {
    println!("=== Concurrency Basics Demo (Rust) ===");
    println!();

    // Basic thread creation and joining
    println!("Basic thread creation:");
    let handle = thread::spawn(|| {
        for i in 1..=5 {
            println!("  Thread: Count {}", i);
            thread::sleep(Duration::from_millis(100));
        }
        "Thread completed"
    });
    
    // Main thread work
    for i in 1..=3 {
        println!("Main: Count {}", i);
        thread::sleep(Duration::from_millis(150));
    }
    
    // Wait for thread to complete
    match handle.join() {
        Ok(result) => println!("Thread result: {}", result),
        Err(_) => println!("Thread panicked"),
    }
    println!();

    // Multiple threads
    println!("Multiple threads:");
    let mut handles = vec![];
    
    for i in 0..3 {
        let handle = thread::spawn(move || {
            println!("  Worker {} starting", i);
            thread::sleep(Duration::from_millis(200 * (i + 1) as u64));
            println!("  Worker {} finished", i);
            format!("Result from worker {}", i)
        });
        handles.push(handle);
    }
    
    // Collect results
    for (i, handle) in handles.into_iter().enumerate() {
        match handle.join() {
            Ok(result) => println!("Worker {} returned: {}", i, result),
            Err(_) => println!("Worker {} panicked", i),
        }
    }
    println!();

    // Shared state with Mutex
    println!("Shared state with Mutex:");
    let counter = Arc::new(Mutex::new(0));
    let mut handles = vec![];
    
    for i in 0..5 {
        let counter = Arc::clone(&counter);
        let handle = thread::spawn(move || {
            for _ in 0..10 {
                let mut num = counter.lock().unwrap();
                *num += 1;
                println!("  Thread {} incremented counter to {}", i, *num);
                thread::sleep(Duration::from_millis(10));
            }
        });
        handles.push(handle);
    }
    
    // Wait for all threads
    for handle in handles {
        handle.join().unwrap();
    }
    
    println!("Final counter value: {}", *counter.lock().unwrap());
    println!();

    // Message passing with channels
    println!("Message passing with channels:");
    let (tx, rx) = mpsc::channel();
    
    // Spawn producer threads
    for i in 0..3 {
        let tx = tx.clone();
        thread::spawn(move || {
            for j in 0..3 {
                let message = format!("Message {} from producer {}", j, i);
                tx.send(message).unwrap();
                thread::sleep(Duration::from_millis(100));
            }
        });
    }
    
    // Drop the original sender
    drop(tx);
    
    // Receive messages
    for received in rx {
        println!("  Received: {}", received);
    }
    println!();

    // Producer-Consumer pattern
    println!("Producer-Consumer pattern:");
    let (tx, rx) = mpsc::channel();
    
    // Producer thread
    let producer = thread::spawn(move || {
        for i in 1..=10 {
            let item = format!("Item {}", i);
            println!("  Producing: {}", item);
            tx.send(item).unwrap();
            thread::sleep(Duration::from_millis(50));
        }
        println!("  Producer finished");
    });
    
    // Consumer thread
    let consumer = thread::spawn(move || {
        while let Ok(item) = rx.recv() {
            println!("  Consuming: {}", item);
            thread::sleep(Duration::from_millis(100));
        }
        println!("  Consumer finished");
    });
    
    producer.join().unwrap();
    consumer.join().unwrap();
    println!();

    // Parallel computation
    println!("Parallel computation:");
    let data = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let chunk_size = 3;
    let chunks: Vec<_> = data.chunks(chunk_size).collect();
    
    println!("Processing data in parallel chunks:");
    let mut handles = vec![];
    
    for (i, chunk) in chunks.into_iter().enumerate() {
        let chunk = chunk.to_vec();
        let handle = thread::spawn(move || {
            println!("  Thread {} processing chunk: {:?}", i, chunk);
            let sum: i32 = chunk.iter().sum();
            thread::sleep(Duration::from_millis(200));
            println!("  Thread {} finished, sum: {}", i, sum);
            sum
        });
        handles.push(handle);
    }
    
    let mut total_sum = 0;
    for (i, handle) in handles.into_iter().enumerate() {
        let sum = handle.join().unwrap();
        total_sum += sum;
        println!("Chunk {} sum: {}", i, sum);
    }
    println!("Total sum: {}", total_sum);
    println!();

    // Thread-safe data sharing
    println!("Thread-safe data sharing:");
    let shared_data = Arc::new(Mutex::new(vec![1, 2, 3, 4, 5]));
    let mut handles = vec![];
    
    for i in 0..3 {
        let data = Arc::clone(&shared_data);
        let handle = thread::spawn(move || {
            let mut vec = data.lock().unwrap();
            vec.push(i + 10);
            println!("  Thread {} added {}, vector: {:?}", i, i + 10, *vec);
            thread::sleep(Duration::from_millis(100));
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
    
    println!("Final shared data: {:?}", *shared_data.lock().unwrap());
    println!();

    // Work distribution
    println!("Work distribution:");
    let (job_tx, job_rx) = mpsc::channel();
    let (result_tx, result_rx) = mpsc::channel();
    
    // Create worker threads
    let num_workers = 3;
    let job_rx = Arc::new(Mutex::new(job_rx));
    for worker_id in 0..num_workers {
        let job_rx = Arc::clone(&job_rx);
        let result_tx = result_tx.clone();
        
        thread::spawn(move || {
            while let Ok(job) = job_rx.lock().unwrap().recv() {
                println!("  Worker {} processing job: {}", worker_id, job);
                thread::sleep(Duration::from_millis(200));
                let result = format!("Job {} completed by worker {}", job, worker_id);
                result_tx.send(result).unwrap();
            }
        });
    }
    
    // Send jobs
    let jobs = vec!["Task A", "Task B", "Task C", "Task D", "Task E"];
    for job in jobs {
        job_tx.send(job).unwrap();
    }
    drop(job_tx); // Close the job channel
    drop(result_tx); // Close the result sender from main thread
    
    // Collect results
    for result in result_rx {
        println!("  Result: {}", result);
    }
    println!();

    // Synchronization barrier simulation
    println!("Synchronization barrier simulation:");
    let barrier = Arc::new(Mutex::new(0));
    let total_threads = 3;
    let mut handles = vec![];
    
    for i in 0..total_threads {
        let barrier = Arc::clone(&barrier);
        let handle = thread::spawn(move || {
            // Simulate some work
            println!("  Thread {} doing initial work", i);
            thread::sleep(Duration::from_millis(100 * (i + 1) as u64));
            
            // Reach barrier
            {
                let mut count = barrier.lock().unwrap();
                *count += 1;
                println!("  Thread {} reached barrier ({}/{})", i, *count, total_threads);
            }
            
            // Wait for all threads to reach barrier
            loop {
                let count = barrier.lock().unwrap();
                if *count >= total_threads {
                    break;
                }
                drop(count);
                thread::sleep(Duration::from_millis(10));
            }
            
            // Continue after barrier
            println!("  Thread {} continuing after barrier", i);
            thread::sleep(Duration::from_millis(50));
            println!("  Thread {} finished", i);
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
    println!();

    // Resource pool simulation
    println!("Resource pool simulation:");
    let resource_pool = Arc::new(Mutex::new(vec!["Resource1", "Resource2", "Resource3"]));
    let mut handles = vec![];
    
    for i in 0..5 {
        let pool = Arc::clone(&resource_pool);
        let handle = thread::spawn(move || {
            // Try to acquire a resource
            let resource = {
                let mut pool = pool.lock().unwrap();
                pool.pop()
            };
            
            match resource {
                Some(res) => {
                    println!("  Thread {} acquired {}", i, res);
                    thread::sleep(Duration::from_millis(200));
                    
                    // Return resource to pool
                    {
                        let mut pool = pool.lock().unwrap();
                        pool.push(res);
                        println!("  Thread {} returned {}", i, res);
                    }
                }
                None => {
                    println!("  Thread {} couldn't acquire resource", i);
                }
            }
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
    println!();

    // Simple thread pool simulation
    println!("Simple thread pool simulation:");
    let (task_tx, task_rx) = mpsc::channel();
    let task_rx = Arc::new(Mutex::new(task_rx));
    
    // Create thread pool
    let pool_size = 2;
    let mut pool_handles = vec![];
    
    for worker_id in 0..pool_size {
        let task_rx = Arc::clone(&task_rx);
        let handle = thread::spawn(move || {
            loop {
                let task = {
                    let rx = task_rx.lock().unwrap();
                    rx.recv()
                };
                
                match task {
                    Ok(task_name) => {
                        if task_name == "STOP" {
                            println!("  Worker {} stopping", worker_id);
                            break;
                        }
                        println!("  Worker {} executing {}", worker_id, task_name);
                        thread::sleep(Duration::from_millis(300));
                        println!("  Worker {} completed {}", worker_id, task_name);
                    }
                    Err(_) => {
                        println!("  Worker {} channel closed", worker_id);
                        break;
                    }
                }
            }
        });
        pool_handles.push(handle);
    }
    
    // Submit tasks
    let tasks = vec!["Task1", "Task2", "Task3", "Task4"];
    for task in tasks {
        task_tx.send(task).unwrap();
    }
    
    // Stop workers
    for _ in 0..pool_size {
        task_tx.send("STOP").unwrap();
    }
    
    // Wait for pool to finish
    for handle in pool_handles {
        handle.join().unwrap();
    }
    println!();

    // Performance comparison
    println!("Performance comparison:");
    let data: Vec<i32> = (1..=1000).collect();
    
    // Sequential processing
    let start = std::time::Instant::now();
    let sequential_sum: i32 = data.iter().map(|&x| x * x).sum();
    let sequential_time = start.elapsed();
    
    println!("Sequential processing:");
    println!("  Sum of squares: {}", sequential_sum);
    println!("  Time: {:?}", sequential_time);
    
    // Parallel processing (simulated)
    let start = std::time::Instant::now();
    let chunk_size = 250;
    let chunks: Vec<_> = data.chunks(chunk_size).collect();
    let mut handles = vec![];
    
    for chunk in chunks {
        let chunk = chunk.to_vec();
        let handle = thread::spawn(move || {
            chunk.iter().map(|&x| x * x).sum::<i32>()
        });
        handles.push(handle);
    }
    
    let parallel_sum: i32 = handles.into_iter()
        .map(|h| h.join().unwrap())
        .sum();
    let parallel_time = start.elapsed();
    
    println!("Parallel processing:");
    println!("  Sum of squares: {}", parallel_sum);
    println!("  Time: {:?}", parallel_time);
    println!("  Results match: {}", sequential_sum == parallel_sum);
    println!();

    println!("=== End of Concurrency Basics Demo ===");
}
